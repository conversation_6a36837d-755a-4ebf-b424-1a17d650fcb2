declare module 'material-colors' {
  interface MaterialColors {
    red: { [key: string]: string };
    pink: { [key: string]: string };
    purple: { [key: string]: string };
    deepPurple: { [key: string]: string };
    indigo: { [key: string]: string };
    blue: { [key: string]: string };
    lightBlue: { [key: string]: string };
    cyan: { [key: string]: string };
    teal: { [key: string]: string };
    green: { [key: string]: string };
    lightGreen: { [key: string]: string };
    lime: { [key: string]: string };
    yellow: { [key: string]: string };
    amber: { [key: string]: string };
    orange: { [key: string]: string };
    deepOrange: { [key: string]: string };
    brown: { [key: string]: string };
    grey: { [key: string]: string };
    blueGrey: { [key: string]: string };
  }

  const colors: MaterialColors;
  export = colors;
} 