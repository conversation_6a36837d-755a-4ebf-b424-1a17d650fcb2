{"name": "the-canvas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.39.1", "@hono/zod-validator": "^0.5.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.2", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.50.1", "@types/axios": "^0.9.36", "@types/lodash.debounce": "^4.0.9", "@types/react-color": "^3.0.13", "@types/uuid": "^10.0.0", "@uploadthing/react": "^6.8.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "fabric": "5.3.0-browser", "hono": "^4.4.12", "jsdom": "^24.1.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.399.0", "material-colors": "^1.2.6", "next": "14.2.4", "next-auth": "4.22.1", "next-themes": "^0.3.0", "react": "^18", "react-color": "^2.19.3", "react-dom": "^18", "react-icons": "^5.2.1", "react-use": "^17.6.0", "replicate": "^1.0.1", "sonner": "^1.4.0", "stripe": "^18.1.0", "svg-path-parser": "^1.1.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "unsplash-js": "^7.0.19", "uploadthing": "^6.13.3", "use-file-picker": "^2.1.2", "uuid": "^11.1.0", "zod": "^3.22.4", "zustand": "^5.0.4"}, "devDependencies": {"@types/fabric": "^5.3.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/svg-path-parser": "^1.1.6", "autoprefixer": "^10.0.1", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}