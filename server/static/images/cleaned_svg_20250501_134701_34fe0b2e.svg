<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet">
  <defs>
    <radialGradient id="bg-gradient" cx="0.5" cy="0.5" r="0.8">
      <stop offset="0%" stop-color="#0B1A3C"/>
      <stop offset="100%" stop-color="#07102A"/>
    </radialGradient>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Oswald:wght@700&family=Montserrat:wght@400&display=swap');
      .comingsoon {
        font-family: '<PERSON>', <PERSON><PERSON>, sans-serif;
        font-weight: 700;
        fill: #fff;
        font-size: 170px;
        letter-spacing: 2px;
        text-anchor: middle;
      }
      .subtitle {
        font-family: 'Montserrat', Aria<PERSON>, sans-serif;
        font-weight: 400;
        fill: #5eb9f7;
        font-size: 48px;
        text-anchor: middle;
      }
    </style>
  </defs>
  <rect width="1080" height="1080" fill="url(#bg-gradient)"/>
  <g>
    <polygon points="70,70 150,200 70,200" stroke="#263E72" stroke-width="4" fill="none"/>
    <rect x="920" y="70" width="24" height="24" stroke="#263E72" stroke-width="3" fill="none"/>
    <path d="M1040,50 Q1020,350 1040,1040" stroke="#263E72" stroke-width="2" fill="none"/>
    <path d="M50,1030 Q50,800 300,1030" stroke="#263E72" stroke-width="2" fill="none"/>
    <g opacity="0.10">
      <line x1="155" y1="165" x2="70" y2="250" stroke="#23345D" stroke-width="3"/>
      <line x1="85" y1="175" x2="175" y2="255" stroke="#23345D" stroke-width="3"/>
    </g>
  </g>
  <g stroke="#23345D" stroke-width="2" fill="none" opacity="0.20">
    <path d="M920,670 Q940,700 980,740 Q950,720 930,770 Q950,800 1010,820"/>
    <path d="M960,770 Q940,800 970,840"/>
    <path d="M1000,780 Q1020,805 1010,850"/>
    <path d="M960,850 Q960,900 1000,925"/>
  </g>
  <g stroke="#23345D" stroke-width="2" fill="none" opacity="0.18">
    <path d="M940,700 Q1040,880 1000,1040"/>
    <path d="M960,770 Q950,790 930,830"/>
    <path d="M1015,860 Q1045,890 1050,940"/>
    <path d="M960,850 Q980,900 950,1020"/>
  </g>
  <text x="540" y="370" class="comingsoon" dominant-baseline="middle">COMING</text>
  <text x="540" y="540" class="comingsoon" dominant-baseline="middle">SOON</text>
  <text x="540" y="700" class="subtitle" dominant-baseline="middle">The best is yot to come</text>
</svg>