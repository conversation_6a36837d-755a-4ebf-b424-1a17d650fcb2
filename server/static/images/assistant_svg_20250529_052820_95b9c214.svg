<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C5.414 10.569 10.441 21.253 15.166 32.147 C15.747 33.438 16.36 34.714 16.996 35.979 C17.997 37.994 18.874 40.026 19.727 42.109 C20.036 42.863 20.345 43.618 20.664 44.395 C20.981 45.172 21.298 45.949 21.625 46.75 C21.95 47.543 22.275 48.336 22.609 49.152 C23.408 51.101 24.204 53.05 25 55 C31.266 42.865 36.909 30.447 42.604 18.037 C44.471 13.976 46.356 9.924 48.25 5.875 C48.791 4.717 49.333 3.56 49.891 2.367 C50.257 1.586 50.623 0.805 51 0 C54.979 -0.029 58.958 -0.047 62.938 -0.062 C64.054 -0.071 65.171 -0.079 66.322 -0.088 C67.421 -0.091 68.52 -0.094 69.652 -0.098 C71.153 -0.106 71.153 -0.106 72.683 -0.114 C76.822 0.028 80.892 0.493 85 1 C84.955 2.403 84.955 2.403 84.909 3.835 C84.071 30.54 83.87 57.219 83.931 83.935 C83.942 89.046 83.946 94.157 83.951 99.268 C83.962 109.178 83.979 119.089 84 129 C82.036 128.74 80.072 128.481 78.108 128.221 C73.839 127.773 69.552 128.042 65.27 128.16 C62.035 128.002 59.897 127.391 57 126 C57.33 126.99 57.66 127.98 58 129 C57.34 129 56.68 129 56 129 C55.67 104.25 55.34 79.5 55 54 C44.34 76.934 44.34 76.934 34 100 C27.07 100 20.14 100 13 100 C7.72 88.12 2.44 76.24 -3 64 C-3.99 64.495 -3.99 64.495 -5 65 C-5.33 62.69 -5.66 60.38 -6 58 C-5.993 58.833 -5.987 59.665 -5.98 60.523 C-5.92 68.366 -5.874 76.209 -5.845 84.052 C-5.829 88.084 -5.808 92.116 -5.774 96.148 C-5.741 100.039 -5.723 103.929 -5.715 107.819 C-5.71 109.304 -5.699 110.79 -5.683 112.275 C-5.661 114.353 -5.658 116.431 -5.659 118.509 C-5.653 119.693 -5.646 120.876 -5.639 122.096 C-6 125 -6 125 -7.364 126.883 C-9.752 128.513 -11.605 128.356 -14.473 128.328 C-15.537 128.323 -16.601 128.318 -17.697 128.312 C-18.808 128.292 -19.918 128.271 -21.062 128.25 C-22.177 128.245 -23.291 128.24 -24.439 128.234 C-32.691 128.154 -32.691 128.154 -35 127 C-35 85.42 -35 43.84 -35 1 C-5 0 -5 0 0 0 Z " fill="#ED9335" transform="translate(475,119)"/>
<path d="M0 0 C13.013 9.804 22.518 24.955 25.191 41.121 C27.986 62.91 22.607 81.11 10.066 99.184 C0.154 110.363 -13.536 116.949 -27.973 119.578 C-30.744 119.976 -30.744 119.976 -32.809 121.121 C-34.669 121.159 -34.669 121.159 -36.914 121.035 C-38.143 120.969 -38.143 120.969 -39.396 120.902 C-40.254 120.851 -41.112 120.799 -41.996 120.746 C-43.254 120.68 -43.254 120.68 -44.537 120.613 C-48.321 120.401 -51.983 120.094 -55.734 119.52 C-58.841 118.89 -58.841 118.89 -61.809 120.121 C-64.809 118.121 -64.809 118.121 -66.809 116.121 C-68.648 115.194 -70.505 114.304 -72.371 113.434 C-88.365 105.504 -97.967 92.718 -103.91 76.027 C-105.404 70.945 -106.161 66.409 -105.809 61.121 C-106.469 60.791 -107.129 60.461 -107.809 60.121 C-107.365 43.432 -105.294 24.422 -93.316 11.723 C-91.791 10.16 -91.791 10.16 -90.551 8.457 C-83.488 -0.805 -72.808 -6.676 -61.809 -9.879 C-60.556 -10.324 -60.556 -10.324 -59.277 -10.777 C-39.318 -17.33 -16.955 -11.342 0 0 Z M-67.809 30.121 C-74.741 39.658 -76.092 50.701 -74.344 62.172 C-72.119 72.533 -67.635 79.22 -59.25 85.613 C-53.133 89.391 -48.043 90.651 -40.996 90.559 C-40.204 90.552 -39.413 90.546 -38.597 90.54 C-30.201 90.316 -22.879 88.191 -16.809 82.121 C-8.496 72.259 -5.393 63.85 -5.746 50.746 C-6.751 41.12 -11.103 32.239 -18.059 25.496 C-33.762 13.176 -54.714 15.526 -67.809 30.121 Z " fill="#E48E36" transform="translate(401.80859375,126.87890625)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C1.039 2.291 2.078 2.583 3.148 2.883 C11.535 5.318 18.71 8.057 26 13 C26.99 13.495 26.99 13.495 28 14 C29.507 15.61 30.889 17.306 32.297 19.004 C33.953 20.996 33.953 20.996 35.703 22.715 C48.039 35.412 51.235 53.007 51.062 70.062 C51.058 70.928 51.053 71.794 51.049 72.686 C51.037 74.79 51.019 76.895 51 79 C50.34 79 49.68 79 49 79 C49 80.155 49 81.31 49 82.5 C47.47 96.144 37.374 109.603 27.312 118.25 C11.973 129.86 -6.3 132.521 -25 131.688 C-25.838 131.653 -26.676 131.619 -27.54 131.583 C-28.331 131.539 -29.122 131.495 -29.938 131.449 C-31.003 131.393 -31.003 131.393 -32.09 131.335 C-34 131 -34 131 -37 129 C-38.966 128.137 -40.947 127.307 -42.938 126.5 C-46.361 125.108 -49.719 123.703 -53 122 C-53.66 121.67 -54.32 121.34 -55 121 C-56.189 119.845 -57.373 118.682 -58.523 117.488 C-59.986 115.978 -59.986 115.978 -61.602 114.605 C-68.757 108.153 -73.668 98.964 -77 90 C-77.66 90 -78.32 90 -79 90 C-79.99 85.05 -79.99 85.05 -81 80 C-80.67 80 -80.34 80 -80 80 C-80.023 79.007 -80.046 78.015 -80.07 76.992 C-80.425 55.334 -76.549 35.526 -61.387 19.217 C-56.814 14.541 -51.731 11.139 -46 8 C-45.348 7.603 -44.695 7.206 -44.023 6.797 C-31.208 -0.511 -14.361 -0.094 0 0 Z M-7 1 C-7 1.33 -7 1.66 -7 2 C-5.35 2 -3.7 2 -2 2 C-2 1.67 -2 1.34 -2 1 C-3.65 1 -5.3 1 -7 1 Z M-39.398 41.027 C-46.182 49.383 -48.427 58.325 -47.875 69 C-46.818 78.382 -43.752 87.202 -37 94 C-28.987 100.171 -20.146 102.927 -10 102 C-0.504 100.469 5.679 96.438 12.312 89.625 C19.133 79.016 20.863 67.136 18.352 54.82 C15.474 46.138 10.316 38.543 2.246 33.996 C-12.247 27.569 -28.307 29.101 -39.398 41.027 Z M-79 81 C-78 84 -78 84 -78 84 Z " fill="#DC8738" transform="translate(587,291)"/>
<path d="M0 0 C0.495 1.485 0.495 1.485 1 3 C2.237 2.938 3.475 2.876 4.75 2.812 C18.162 2.875 32.231 8.82 42 18 C47.088 23.505 51.61 28.83 54 36 C53.368 36.34 52.737 36.681 52.086 37.031 C44.322 41.238 36.654 45.598 29 50 C28.251 49.248 27.502 48.497 26.73 47.723 C18.022 39.039 11.223 33.897 -1.5 33.5 C-10.561 33.774 -17.812 36.032 -24.473 42.316 C-31.758 50.347 -34.628 60.348 -34.289 71.098 C-33.506 78.965 -32.286 86.888 -27 93 C-26.01 93.66 -25.02 94.32 -24 95 C-24 95.66 -24 96.32 -24 97 C-22.123 98.561 -22.123 98.561 -19.75 100.062 C-18.961 100.59 -18.172 101.117 -17.359 101.66 C-13.155 104.048 -9.673 104.116 -4.875 104.062 C-3.821 104.053 -2.766 104.044 -1.68 104.035 C-0.795 104.024 0.089 104.012 1 104 C2.453 104 3.905 104.054 5.355 104.141 C11.841 104.391 15.82 104.054 21 100 C22.216 96.794 22.103 93.648 22.062 90.25 C22.053 89.265 22.044 88.28 22.035 87.266 C22.024 86.518 22.012 85.77 22 85 C18.875 84.971 15.75 84.953 12.625 84.938 C11.736 84.929 10.846 84.921 9.93 84.912 C8.654 84.907 8.654 84.907 7.352 84.902 C6.566 84.897 5.781 84.892 4.971 84.886 C2.922 84.874 2.922 84.874 1 86 C0.67 86.66 0.34 87.32 0 88 C-2.678 85.322 -2.305 83.86 -2.414 80.117 C-2.45 79.066 -2.485 78.016 -2.522 76.933 C-2.556 75.594 -2.59 74.255 -2.625 72.875 C-2.749 68.626 -2.872 64.377 -3 60 C14.16 60 31.32 60 49 60 C49.33 59.01 49.66 58.02 50 57 C52.148 60.222 52.258 61.119 52.291 64.835 C52.303 65.817 52.316 66.798 52.329 67.809 C52.331 68.869 52.334 69.928 52.336 71.02 C52.346 72.657 52.346 72.657 52.356 74.327 C52.366 76.637 52.371 78.948 52.371 81.259 C52.375 84.788 52.411 88.317 52.449 91.846 C52.455 94.091 52.459 96.337 52.461 98.582 C52.475 99.635 52.49 100.689 52.504 101.774 C52.457 109.457 51.045 112.63 45.914 118.309 C32.217 131.047 15.461 135.98 -3 136 C-21.495 134.994 -37.968 129.019 -50.988 115.414 C-54.663 111.004 -57.44 106.12 -60 101 C-60.99 101.495 -60.99 101.495 -62 102 C-62.826 99.108 -63 97.113 -63 94 C-63.478 91.787 -63.981 89.579 -64.5 87.375 C-68.483 68.708 -66.41 48.545 -56.215 32.125 C-45.669 17.03 -32.844 7.369 -14.801 3.609 C-9.813 2.81 -5.032 2.616 0 3 C0 2.01 0 1.02 0 0 Z " fill="#E48D36" transform="translate(799,112)"/>
<path d="M0 0 C0.82 0.208 1.64 0.415 2.484 0.629 C19.945 5.374 32.913 15.461 42 31 C50.714 47.633 52.459 67.382 46.961 85.406 C41.463 100.588 32.427 114.177 17.734 121.664 C3.256 128.138 -10.803 131.07 -26.602 129.059 C-27.393 129.039 -28.185 129.02 -29 129 C-29.66 129.66 -30.32 130.32 -31 131 C-32.65 130.34 -34.3 129.68 -36 129 C-36 128.34 -36 127.68 -36 127 C-36.739 126.732 -37.477 126.464 -38.238 126.188 C-54.744 119.812 -66.323 108.938 -74 93 C-80.994 74.308 -81.702 53.411 -73.486 35.014 C-65.46 18.116 -52.559 7.27 -35 1 C-31.342 -0.006 -27.769 -0.592 -24 -1 C-22.864 -1.133 -21.729 -1.266 -20.559 -1.402 C-13.277 -2.101 -7.04 -1.97 0 0 Z M-40 38 C-48.229 48.44 -49.105 59.701 -47.723 72.586 C-45.847 82.768 -40.252 89.964 -32 96 C-24.979 100.116 -16.469 101.106 -8.547 99.504 C-2.893 97.969 2.48 95.825 7 92 C7 91.34 7 90.68 7 90 C7.66 90 8.32 90 9 90 C10.532 87.96 10.532 87.96 12 85.375 C12.516 84.516 13.031 83.658 13.562 82.773 C18.739 72.785 19.176 61.37 16 50.68 C12.675 41.771 6.089 34.071 -2.688 30.062 C-16.384 25.669 -29.341 28.186 -40 38 Z " fill="#D88438" transform="translate(452,293)"/>
<path d="M0 0 C1.693 -0.119 1.693 -0.119 3.42 -0.24 C11.475 -0.444 11.475 -0.444 14.515 2.393 C16.16 5.075 17.409 7.812 18.688 10.688 C19.665 12.563 20.677 14.418 21.691 16.275 C23.603 19.788 25.451 23.335 27.312 26.875 C28.108 28.383 28.903 29.89 29.699 31.398 C36.067 43.474 42.397 55.57 48.688 67.688 C49.018 67.688 49.347 67.688 49.688 67.688 C49.711 60.194 49.728 52.701 49.739 45.208 C49.745 41.728 49.752 38.249 49.763 34.77 C49.776 30.768 49.781 26.767 49.785 22.766 C49.79 21.518 49.795 20.27 49.801 18.984 C49.801 17.242 49.801 17.242 49.801 15.466 C49.803 14.444 49.805 13.423 49.808 12.37 C49.7 9.977 49.375 7.973 48.688 5.688 C49.347 5.688 50.007 5.688 50.688 5.688 C51.018 4.037 51.347 2.388 51.688 0.688 C55.833 0.659 59.979 0.641 64.125 0.625 C65.309 0.617 66.493 0.608 67.713 0.6 C68.838 0.596 69.962 0.593 71.121 0.59 C72.163 0.585 73.205 0.579 74.279 0.574 C76.688 0.688 76.688 0.688 77.688 1.688 C77.786 3.302 77.814 4.922 77.814 6.54 C77.819 8.115 77.819 8.115 77.823 9.722 C77.82 11.466 77.82 11.466 77.817 13.244 C77.82 15.068 77.82 15.068 77.822 16.929 C77.826 20.274 77.825 23.619 77.822 26.964 C77.819 30.459 77.822 33.953 77.823 37.448 C77.825 43.318 77.823 49.188 77.818 55.059 C77.812 61.852 77.814 68.645 77.82 75.438 C77.824 81.262 77.825 87.087 77.822 92.911 C77.821 96.393 77.82 99.875 77.824 103.357 C77.827 107.239 77.822 111.12 77.817 115.002 C77.819 116.162 77.821 117.322 77.823 118.517 C77.82 119.57 77.817 120.623 77.814 121.708 C77.814 123.086 77.814 123.086 77.813 124.492 C77.688 126.688 77.688 126.688 76.688 128.688 C73.313 128.775 69.938 128.828 66.562 128.875 C65.611 128.9 64.66 128.925 63.68 128.951 C58.306 129.007 54.201 128.959 49.688 125.688 C47.965 123.199 47.965 123.199 46.625 120.375 C44.926 116.982 43.345 114.02 40.812 111.188 C38.688 108.688 38.688 108.688 38.688 105.688 C37.898 103.913 37.031 102.171 36.125 100.453 C35.579 99.41 35.032 98.367 34.469 97.293 C33.881 96.186 33.293 95.078 32.688 93.938 C32.098 92.815 31.508 91.693 30.9 90.537 C29.169 87.25 27.43 83.968 25.688 80.688 C25.225 79.815 24.763 78.943 24.287 78.044 C22.099 73.919 19.901 69.799 17.688 65.688 C17.028 65.688 16.367 65.688 15.688 65.688 C15.688 64.037 15.688 62.387 15.688 60.688 C15.028 60.358 14.367 60.028 13.688 59.688 C13.357 59.028 13.028 58.368 12.688 57.688 C12.677 58.535 12.667 59.382 12.656 60.256 C12.558 68.231 12.456 76.207 12.348 84.182 C12.293 88.283 12.24 92.383 12.19 96.484 C12.143 100.439 12.09 104.394 12.035 108.349 C12.014 109.86 11.995 111.372 11.978 112.883 C11.954 114.995 11.924 117.106 11.893 119.218 C11.877 120.422 11.862 121.625 11.846 122.865 C11.688 125.688 11.688 125.688 10.688 127.688 C10.028 127.688 9.367 127.688 8.688 127.688 C8.688 128.347 8.688 129.007 8.688 129.688 C7.435 129.604 6.182 129.52 4.891 129.434 C3.24 129.33 1.589 129.228 -0.062 129.125 C-0.888 129.069 -1.712 129.013 -2.562 128.955 C-3.362 128.907 -4.161 128.858 -4.984 128.809 C-5.718 128.761 -6.451 128.714 -7.206 128.666 C-9.342 128.603 -9.342 128.603 -11.458 129.242 C-13.312 129.688 -13.312 129.688 -15.312 128.688 C-15.312 86.778 -15.312 44.868 -15.312 1.688 C-10.329 0.026 -5.219 0.168 0 0 Z M11.688 54.688 C12.688 56.688 12.688 56.688 12.688 56.688 Z M49.688 68.688 C50.688 70.688 50.688 70.688 50.688 70.688 Z M-13.312 126.688 C-12.653 127.347 -11.992 128.007 -11.312 128.688 C-11.312 128.028 -11.312 127.368 -11.312 126.688 C-11.972 126.688 -12.633 126.688 -13.312 126.688 Z " fill="#EC9235" transform="translate(642.3125,118.3125)"/>
<path d="M0 0 C1.252 0.047 2.503 0.094 3.793 0.143 C4.457 0.166 5.122 0.19 5.806 0.214 C7.936 0.291 10.066 0.375 12.195 0.459 C13.636 0.512 15.077 0.565 16.517 0.617 C20.056 0.747 23.594 0.883 27.133 1.022 C27.613 1.993 28.093 2.963 28.588 3.964 C38.841 24.652 49.612 45.01 61.133 65.022 C61.121 64.118 61.109 63.214 61.097 62.283 C61.088 61.104 61.079 59.924 61.07 58.709 C61.058 57.537 61.047 56.366 61.035 55.158 C61.133 52.022 61.133 52.022 62.133 49.022 C62.22 47.441 62.25 45.858 62.246 44.275 C62.246 43.314 62.246 42.353 62.246 41.363 C62.241 40.329 62.236 39.295 62.23 38.229 C62.229 37.168 62.227 36.106 62.226 35.013 C62.22 31.62 62.208 28.227 62.195 24.834 C62.19 22.535 62.185 20.237 62.181 17.938 C62.171 12.299 62.152 6.66 62.133 1.022 C70.713 1.022 79.293 1.022 88.133 1.022 C88.286 16.911 88.434 32.801 88.576 48.69 C88.641 56.068 88.709 63.445 88.781 70.822 C88.843 77.25 88.903 83.678 88.958 90.106 C88.987 93.511 89.018 96.917 89.053 100.322 C89.091 104.118 89.124 107.913 89.154 111.709 C89.167 112.846 89.18 113.983 89.194 115.155 C89.204 116.697 89.204 116.697 89.215 118.27 C89.223 119.169 89.231 120.067 89.24 120.993 C89.133 123.022 89.133 123.022 88.133 124.022 C86.06 124.109 83.984 124.129 81.91 124.119 C80.02 124.115 80.02 124.115 78.092 124.11 C76.751 124.101 75.411 124.093 74.07 124.084 C72.726 124.079 71.381 124.075 70.037 124.071 C66.735 124.059 63.434 124.042 60.133 124.022 C55.759 115.842 51.413 107.659 47.32 99.334 C43.519 91.622 39.405 84.088 35.256 76.558 C32.793 72.085 30.368 67.614 28.133 63.022 C27.473 63.022 26.813 63.022 26.133 63.022 C26.463 61.702 26.793 60.382 27.133 59.022 C25.648 59.517 25.648 59.517 24.133 60.022 C23.803 81.142 23.473 102.262 23.133 124.022 C15.213 124.352 7.293 124.682 -0.867 125.022 C-2.352 125.517 -2.352 125.517 -3.867 126.022 C-4.197 125.032 -4.527 124.042 -4.867 123.022 C-4.207 123.022 -3.547 123.022 -2.867 123.022 C-2.938 122.432 -3.009 121.843 -3.082 121.235 C-3.81 114.676 -4.01 108.217 -3.997 101.621 C-3.999 99.983 -3.999 99.983 -4.002 98.311 C-4.007 94.734 -4.004 91.157 -4 87.58 C-4.001 85.086 -4.002 82.592 -4.003 80.097 C-4.005 74.888 -4.002 69.68 -3.998 64.471 C-3.993 58.445 -3.994 52.419 -4 46.393 C-4.005 40.586 -4.004 34.779 -4.001 28.972 C-4.001 26.502 -4.001 24.033 -4.004 21.564 C-4.006 18.128 -4.002 14.693 -3.997 11.258 C-4 9.716 -4 9.716 -4.003 8.143 C-4 7.213 -3.997 6.284 -3.994 5.327 C-3.994 4.515 -3.994 3.703 -3.993 2.866 C-3.781 -0.239 -3.147 0.024 0 0 Z M24.133 55.022 C25.133 58.022 25.133 58.022 25.133 58.022 Z M61.133 66.022 C61.793 67.342 62.453 68.662 63.133 70.022 C62.803 68.702 62.473 67.382 62.133 66.022 C61.803 66.022 61.473 66.022 61.133 66.022 Z " fill="#E58C37" transform="translate(656.867431640625,294.978271484375)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C1.238 2.062 2.475 2.124 3.75 2.188 C16.332 3.629 26.531 11.11 36 19 C34.708 22.622 33.188 25.285 30.75 28.25 C27.632 32.116 24.94 36.107 22.359 40.348 C21.686 41.166 21.686 41.166 21 42 C20.01 42 19.02 42 18 42 C16.872 40.838 15.746 39.673 14.629 38.5 C7.293 31.745 -1.921 28.618 -11.688 27.625 C-16.679 28.19 -21.39 29.39 -25 33 C-25.5 36.416 -25.5 36.416 -25 40 C-22.332 43.29 -19.943 44.687 -15.938 45.938 C-14.882 46.27 -13.826 46.603 -12.738 46.945 C-9.99 47.721 -7.254 48.442 -4.48 49.117 C9.869 52.628 24.973 56.906 33.75 69.75 C39.49 79.771 40.796 91.053 37.875 102.25 C34.238 112.688 27.071 120.431 17.438 125.688 C10.925 128.756 3.975 130.326 -3 132 C-3.719 132.2 -4.439 132.4 -5.18 132.605 C-7.251 133.054 -9.133 133.098 -11.25 133.062 C-13.106 133.032 -13.106 133.032 -15 133 C-15 132.34 -15 131.68 -15 131 C-15.715 130.986 -16.431 130.972 -17.168 130.957 C-31.85 130.254 -45.872 123.812 -56 113 C-56 112.34 -56 111.68 -56 111 C-57.485 111.495 -57.485 111.495 -59 112 C-59.33 110.35 -59.66 108.7 -60 107 C-60.99 106.67 -61.98 106.34 -63 106 C-61.606 101.566 -59.31 99.089 -56 96 C-54.411 94.443 -52.828 92.88 -51.25 91.312 C-50.471 90.546 -49.693 89.779 -48.891 88.988 C-46.948 87.139 -46.948 87.139 -46 85 C-44.68 85 -43.36 85 -42 85 C-42 85.66 -42 86.32 -42 87 C-41.34 87 -40.68 87 -40 87 C-40 87.66 -40 88.32 -40 89 C-38.68 89.33 -37.36 89.66 -36 90 C-36 90.66 -36 91.32 -36 92 C-25.059 98.6 -15.431 102.642 -2.426 99.781 C0.939 98.698 3.263 97.219 6 95 C5.67 93.68 5.34 92.36 5 91 C5.66 91.33 6.32 91.66 7 92 C6.752 88.365 6.548 86.534 3.891 83.945 C0.613 81.74 -2.271 80.731 -6.062 79.625 C-6.748 79.419 -7.433 79.212 -8.139 79 C-12.114 77.809 -16.108 76.693 -20.109 75.594 C-33.639 71.816 -45.559 67.555 -53.211 55.016 C-57.427 46.823 -58.146 36.926 -55.734 28.062 C-53.95 23.051 -51.624 17.953 -48 14 C-47.34 14 -46.68 14 -46 14 C-46 13.34 -46 12.68 -46 12 C-33.367 1.083 -16.044 -0.6 0 0 Z " fill="#DA8539" transform="translate(331,291)"/>
<path d="M0 0 C0.495 1.485 0.495 1.485 1 3 C1.949 2.835 2.898 2.67 3.875 2.5 C12.893 1.237 21.978 2.208 31 3 C31 3.66 31 4.32 31 5 C32.361 5.062 32.361 5.062 33.75 5.125 C46.775 7.046 58.117 17.042 66 27 C64.382 29.173 62.757 31.338 61.125 33.5 C60.665 34.12 60.205 34.74 59.73 35.379 C58.534 36.956 57.271 38.483 56 40 C55.34 40 54.68 40 54 40 C53.745 40.572 53.49 41.145 53.227 41.734 C51.869 44.242 50.28 46.133 48.375 48.25 C47.428 49.31 47.428 49.31 46.461 50.391 C45.979 50.922 45.497 51.453 45 52 C44.484 51.319 43.969 50.639 43.438 49.938 C42.633 48.968 41.829 47.999 41 47 C40.549 46.446 40.098 45.891 39.633 45.32 C33.701 38.68 25.15 34.303 16.312 33.062 C14.876 32.994 13.437 32.974 12 33 C11.189 33.013 10.378 33.026 9.543 33.039 C0.095 33.591 -7.448 37.192 -14 44 C-20.956 52.502 -22.502 64.125 -21.82 74.82 C-20.525 84.096 -16.471 92.998 -8.922 98.793 C-3.677 102.087 2.1 104.185 8 106 C8 105.34 8 104.68 8 104 C8.906 104.046 9.812 104.093 10.746 104.141 C23.977 104.533 31.336 100.963 41 92 C42.366 90.36 43.709 88.699 45 87 C48.816 88.298 51.071 89.75 53.812 92.688 C57.155 96.176 60.635 99.354 64.336 102.457 C66 104 66 104 68 107 C58.179 120.192 46.26 129.068 30.227 133.324 C28.002 133.91 28.002 133.91 26.173 135.037 C23.579 136.187 21.827 136.191 19 136.082 C18.036 136.046 17.072 136.01 16.079 135.973 C15.063 135.92 14.047 135.867 13 135.812 C11.962 135.761 10.924 135.71 9.855 135.657 C-9.001 134.562 -26.783 127.49 -39.617 113.199 C-48.352 102.269 -53.26 88.921 -54 75 C-54.053 74.024 -54.106 73.048 -54.16 72.043 C-54.675 53.525 -48.689 37.051 -36.527 23.18 C-27.635 13.788 -18.165 8.178 -5.812 4.684 C-4.181 4.206 -2.585 3.612 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z M9 105 C9 105.33 9 105.66 9 106 C10.65 106 12.3 106 14 106 C14 105.67 14 105.34 14 105 C12.35 105 10.7 105 9 105 Z " fill="#E38D37" transform="translate(224,112)"/>
<path d="M0 0 C1.114 -0.001 2.227 -0.003 3.375 -0.004 C5.119 0.002 5.119 0.002 6.898 0.008 C8.654 0.002 8.654 0.002 10.445 -0.004 C11.556 -0.003 12.668 -0.001 13.812 0 C15.347 0.002 15.347 0.002 16.913 0.003 C19.273 0.133 19.273 0.133 20.273 1.133 C20.372 2.748 20.4 4.367 20.4 5.985 C20.403 7.035 20.406 8.086 20.409 9.168 C20.407 10.33 20.405 11.492 20.403 12.689 C20.405 13.905 20.406 15.121 20.408 16.374 C20.412 19.719 20.411 23.064 20.408 26.409 C20.405 29.904 20.408 33.398 20.409 36.893 C20.411 42.763 20.409 48.634 20.404 54.504 C20.398 61.297 20.4 68.09 20.406 74.884 C20.41 80.708 20.411 86.532 20.408 92.356 C20.407 95.838 20.406 99.32 20.41 102.802 C20.413 106.684 20.408 110.565 20.403 114.447 C20.405 115.607 20.407 116.767 20.409 117.962 C20.406 119.015 20.403 120.069 20.4 121.154 C20.4 122.072 20.4 122.991 20.399 123.937 C20.273 126.133 20.273 126.133 19.273 128.133 C16.898 128.883 16.898 128.883 14.273 129.133 C12.788 128.143 12.788 128.143 11.273 127.133 C10.696 127.483 10.118 127.834 9.523 128.195 C6.787 129.335 6.065 128.93 3.273 128.133 C0.94 128.094 -1.394 128.088 -3.727 128.133 C-3.727 127.473 -3.727 126.813 -3.727 126.133 C-7.829 125.117 -7.829 125.117 -11.727 126.133 C-11.397 124.483 -11.067 122.833 -10.727 121.133 C-10.397 121.133 -10.067 121.133 -9.727 121.133 C-9.232 61.733 -9.232 61.733 -8.727 1.133 C-5.569 0.08 -3.315 0.004 0 0 Z " fill="#EC9235" transform="translate(587.7265625,118.8671875)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5.672 11.025 6.116 20.948 6 31 C13.59 31 21.18 31 29 31 C29.99 27.7 30.98 24.4 32 21 C33.015 18.342 34.11 15.743 35.23 13.129 C36.17 10.907 36.17 10.907 36 8 C36.66 8 37.32 8 38 8 C38.309 6.866 38.619 5.731 38.938 4.562 C40 1 40 1 41 0 C42.666 -0.041 44.334 -0.043 46 0 C49.027 6.59 51.7 13.275 54.25 20.062 C54.614 21.023 54.977 21.984 55.352 22.975 C56.237 25.316 57.119 27.657 58 30 C58.66 29.67 59.32 29.34 60 29 C60 32.333 60 35.667 60 39 C59.67 38.34 59.34 37.68 59 37 C57.35 37 55.7 37 54 37 C53.01 35.02 52.02 33.04 51 31 C49.68 31 48.36 31 47 31 C47 30.34 47 29.68 47 29 C43.37 28.34 39.74 27.68 36 27 C35.505 29.475 35.505 29.475 35 32 C34.25 34.75 34.25 34.75 33 37 C29.191 38.481 25.928 37.822 22 37 C21.67 37.66 21.34 38.32 21 39 C20.34 38.67 19.68 38.34 19 38 C16.689 37.903 14.375 37.87 12.062 37.875 C10.193 37.871 10.193 37.871 8.285 37.867 C4.993 37.848 4.993 37.848 2 39 C0.521 37.947 0.521 37.947 -1 36 C-1.356 32.785 -1.356 32.785 -1.328 28.848 C-1.326 28.151 -1.324 27.455 -1.322 26.737 C-1.316 25.265 -1.302 23.793 -1.281 22.322 C-1.25 20.074 -1.24 17.828 -1.234 15.58 C-1.158 3.671 -1.158 3.671 0 0 Z M43 7 C41.35 11.95 39.7 16.9 38 22 C41.63 22 45.26 22 49 22 C47.35 17.05 45.7 12.1 44 7 C43.67 7 43.34 7 43 7 Z M50 28 C51 30 51 30 51 30 Z M27 32 C26.01 32.99 25.02 33.98 24 35 C25.32 34.67 26.64 34.34 28 34 C27.67 33.34 27.34 32.68 27 32 Z " fill="#9C978D" transform="translate(211,689)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5.66 3.31 6.32 5.62 7 8 C6.34 8 5.68 8 5 8 C5.03 8.681 5.061 9.362 5.092 10.064 C5.229 13.147 5.364 16.23 5.5 19.312 C5.548 20.384 5.595 21.456 5.645 22.561 C5.69 23.589 5.735 24.617 5.781 25.676 C5.823 26.624 5.865 27.572 5.908 28.548 C6.039 32.031 6 35.515 6 39 C4.02 39 2.04 39 0 39 C-0.495 26.625 -0.495 26.625 -1 14 C-3.429 19.541 -3.429 19.541 -5.815 25.099 C-6.128 25.787 -6.44 26.475 -6.762 27.184 C-7.225 28.244 -7.225 28.244 -7.698 29.326 C-9 31 -9 31 -14 32 C-16.31 27.38 -18.62 22.76 -21 18 C-21.33 18.99 -21.66 19.98 -22 21 C-21.67 21.66 -21.34 22.32 -21 23 C-21.66 23 -22.32 23 -23 23 C-23 27.95 -23 32.9 -23 38 C-24.32 38.66 -25.64 39.32 -27 40 C-27.66 39.67 -28.32 39.34 -29 39 C-29 38.34 -29 37.68 -29 37 C-29.99 37 -30.98 37 -32 37 C-31.67 36.01 -31.34 35.02 -31 34 C-30.34 34 -29.68 34 -29 34 C-29 23.44 -29 12.88 -29 2 C-26 1 -26 1 -24.043 1.504 C-21.052 3.694 -19.834 6.659 -18.312 9.938 C-18.005 10.571 -17.698 11.205 -17.381 11.857 C-16.091 14.541 -14.942 17.173 -14 20 C-13.34 20.33 -12.68 20.66 -12 21 C-12 21.66 -12 22.32 -12 23 C-11.469 21.842 -10.938 20.685 -10.391 19.492 C-9.677 17.953 -8.964 16.414 -8.25 14.875 C-7.902 14.114 -7.554 13.354 -7.195 12.57 C-5.111 8.092 -2.859 4.03 0 0 Z " fill="#A7A299" transform="translate(672,688)"/>
<path d="M0 0 C1.084 -0.001 2.168 -0.003 3.285 -0.004 C4.429 -0 5.572 0.004 6.75 0.008 C7.862 0.004 8.975 0 10.121 -0.004 C11.216 -0.003 12.31 -0.001 13.438 0 C14.431 0.001 15.425 0.002 16.449 0.003 C20.58 0.165 24.649 0.63 28.75 1.133 C28.705 2.536 28.705 2.536 28.659 3.968 C27.821 30.673 27.62 57.351 27.681 84.068 C27.692 89.179 27.696 94.29 27.701 99.4 C27.712 109.311 27.729 119.222 27.75 129.133 C25.786 128.873 23.822 128.614 21.858 128.354 C17.589 127.906 13.302 128.175 9.02 128.293 C5.785 128.135 3.647 127.524 0.75 126.133 C1.08 127.123 1.41 128.113 1.75 129.133 C1.09 129.133 0.43 129.133 -0.25 129.133 C-0.58 104.383 -0.91 79.633 -1.25 54.133 C-11.91 77.067 -11.91 77.067 -22.25 100.133 C-29.18 100.133 -36.11 100.133 -43.25 100.133 C-45.556 95.065 -47.856 89.994 -50.141 84.917 C-50.92 83.19 -51.703 81.464 -52.49 79.74 C-53.618 77.264 -54.734 74.783 -55.848 72.301 C-56.203 71.529 -56.558 70.757 -56.924 69.961 C-59.364 64.474 -59.364 64.474 -58.25 61.133 C-53.886 70.197 -49.669 79.305 -45.75 88.57 C-45.4 89.392 -45.05 90.215 -44.689 91.062 C-44.369 91.826 -44.049 92.591 -43.719 93.379 C-43.435 94.056 -43.15 94.734 -42.857 95.431 C-42.25 97.133 -42.25 97.133 -42.25 99.133 C-36.31 98.803 -30.37 98.473 -24.25 98.133 C-21.634 92.638 -19.02 87.143 -16.407 81.647 C-15.52 79.782 -14.633 77.917 -13.745 76.052 C-6.878 61.648 -6.878 61.648 -0.25 47.133 C0.08 47.133 0.41 47.133 0.75 47.133 C0.75 72.543 0.75 97.953 0.75 124.133 C9 124.133 17.25 124.133 25.75 124.133 C25.75 83.873 25.75 43.613 25.75 2.133 C15.85 2.463 5.95 2.793 -4.25 3.133 C-12.3 20.444 -20.317 37.768 -28.25 55.133 C-29.599 51.085 -28.079 48.906 -26.348 45.254 C-26.035 44.573 -25.721 43.893 -25.399 43.192 C-24.379 40.98 -23.346 38.775 -22.312 36.57 C-21.615 35.065 -20.919 33.559 -20.224 32.053 C-19.191 29.815 -18.157 27.577 -17.12 25.341 C-14.137 18.913 -11.254 12.443 -8.422 5.947 C-5.813 0.007 -5.813 0.007 0 0 Z " fill="#7F4F2B" transform="translate(531.25,118.8671875)"/>
<path d="M0 0 C3.448 0.86 5.512 1.42 7.836 4.18 C8.344 5.069 8.852 5.959 9.375 6.875 C9.951 7.848 10.527 8.821 11.121 9.824 C11.741 10.872 12.361 11.92 13 13 C14.451 15.249 15.943 17.466 17.438 19.688 C18.457 21.209 18.457 21.209 19.496 22.762 C19.992 23.5 20.489 24.239 21 25 C20.988 24.357 20.977 23.714 20.965 23.051 C20.891 15.654 21.344 8.366 22 1 C25.287 0.2 26.71 -0.097 30 1 C29.838 1.538 29.675 2.075 29.508 2.629 C28.903 5.451 28.87 8.051 28.867 10.938 C28.866 12.08 28.865 13.222 28.863 14.398 C28.869 16.181 28.869 16.181 28.875 18 C28.871 19.189 28.867 20.377 28.863 21.602 C28.865 22.744 28.866 23.886 28.867 25.062 C28.868 26.105 28.869 27.148 28.871 28.223 C28.985 30.668 29.365 32.655 30 35 C30 35.99 30 36.98 30 38 C26.777 38.324 26.777 38.324 23 38 C20.629 35.832 20.629 35.832 18.562 32.812 C17.812 31.731 17.062 30.649 16.289 29.535 C12.733 24.1 9.309 18.59 6 13 C5.409 21.011 4.859 28.962 5 37 C3.344 37.382 1.675 37.714 0 38 C-1.788 36.212 -1.128 33.804 -1.139 31.424 C-1.137 30.644 -1.135 29.865 -1.133 29.062 C-1.134 28.267 -1.135 27.472 -1.136 26.653 C-1.136 24.968 -1.135 23.283 -1.13 21.599 C-1.125 19.011 -1.13 16.424 -1.137 13.836 C-1.136 12.203 -1.135 10.57 -1.133 8.938 C-1.135 8.158 -1.137 7.379 -1.139 6.576 C-1.115 1.115 -1.115 1.115 0 0 Z M22 26 C23 28 23 28 23 28 Z " fill="#C8C2B8" transform="translate(449,689)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.114 2.091 2.114 2.091 3.25 2.184 C7.394 3.414 8.844 6.556 11 10.062 C11.619 11.028 11.619 11.028 12.25 12.014 C13.517 13.999 14.759 15.998 16 18 C17.65 20.64 19.3 23.28 21 26 C21.33 18.08 21.66 10.16 22 2 C23.98 2 25.96 2 28 2 C28.074 7.09 28.129 12.18 28.165 17.271 C28.18 19.003 28.2 20.736 28.226 22.468 C28.263 24.955 28.28 27.442 28.293 29.93 C28.308 30.707 28.324 31.484 28.34 32.285 C28.341 34.468 28.341 34.468 28 38 C27.01 38.66 26.02 39.32 25 40 C20.938 36.214 17.941 32.218 14.938 27.562 C14.306 26.599 14.306 26.599 13.662 25.615 C12.87 24.405 12.079 23.193 11.293 21.979 C9.547 19.307 7.77 16.656 6 14 C5.67 22.25 5.34 30.5 5 39 C2.525 39.495 2.525 39.495 0 40 C-1.262 37.475 -1.099 35.688 -1.062 32.875 C-1.053 31.965 -1.044 31.055 -1.035 30.117 C-1.024 29.419 -1.012 28.72 -1 28 C-1.66 27.67 -2.32 27.34 -3 27 C-2.34 27 -1.68 27 -1 27 C-1.012 25.94 -1.023 24.881 -1.035 23.789 C-1.045 22.401 -1.054 21.013 -1.062 19.625 C-1.071 18.926 -1.079 18.228 -1.088 17.508 C-1.113 12.227 -1.113 12.227 0 10 C-0.66 9.67 -1.32 9.34 -2 9 C-1.34 6.03 -0.68 3.06 0 0 Z " fill="#E9E2D8" transform="translate(316,688)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.732 2.05 2.464 2.101 3.219 2.152 C7.593 3.486 9.11 6.83 11.5 10.562 C12.19 11.6 12.19 11.6 12.895 12.658 C16.02 17.381 19.018 22.186 22 27 C21.992 25.88 21.992 25.88 21.984 24.737 C21.963 21.366 21.95 17.996 21.938 14.625 C21.929 13.449 21.921 12.274 21.912 11.062 C21.907 9.38 21.907 9.38 21.902 7.664 C21.894 6.109 21.894 6.109 21.886 4.522 C22 2 22 2 23 0 C25.475 0.495 25.475 0.495 28 1 C28.495 12.88 28.495 12.88 29 25 C29.33 25 29.66 25 30 25 C30 29.29 30 33.58 30 38 C26 40 26 40 23 40 C21.492 38.086 21.492 38.086 19.875 35.375 C19.273 34.386 18.671 33.398 18.051 32.379 C17.374 31.264 16.697 30.149 16 29 C14.369 26.428 12.716 23.87 11.062 21.312 C10.282 20.1 9.502 18.888 8.723 17.676 C8.154 16.793 7.586 15.91 7 15 C5.761 16.016 5.761 16.016 5.886 18.133 C5.892 19.049 5.897 19.966 5.902 20.91 C5.906 21.9 5.909 22.889 5.912 23.908 C5.92 24.949 5.929 25.99 5.938 27.062 C5.942 28.107 5.947 29.152 5.951 30.229 C5.963 32.819 5.979 35.41 6 38 C4.02 38.66 2.04 39.32 0 40 C-0.144 39.051 -0.289 38.102 -0.438 37.125 C-0.829 34.107 -0.829 34.107 -2 32 C-1.34 32 -0.68 32 0 32 C0 29.36 0 26.72 0 24 C-0.66 24 -1.32 24 -2 24 C-1.67 23.34 -1.34 22.68 -1 22 C-0.77 19.629 -0.589 17.253 -0.438 14.875 C-0.354 13.594 -0.27 12.312 -0.184 10.992 C-0.093 9.511 -0.093 9.511 0 8 C-0.66 7.67 -1.32 7.34 -2 7 C-1.34 7 -0.68 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#B8B3AA" transform="translate(583,688)"/>
<path d="M0 0 C2 2 2 2 2.312 4.5 C2.209 5.325 2.106 6.15 2 7 C1.01 7.66 0.02 8.32 -1 9 C-1 8.01 -1 7.02 -1 6 C-6.94 5.01 -6.94 5.01 -13 4 C-13 4.99 -13 5.98 -13 7 C-14.65 6.67 -16.3 6.34 -18 6 C-18 6.99 -18 7.98 -18 9 C-18.66 9 -19.32 9 -20 9 C-20.109 11.228 -20.186 13.458 -20.25 15.688 C-20.296 16.929 -20.343 18.17 -20.391 19.449 C-19.937 23.57 -18.897 25.108 -16 28 C-13.235 29.382 -10.954 29.095 -7.875 29.062 C-6.779 29.053 -5.684 29.044 -4.555 29.035 C-3.712 29.024 -2.869 29.012 -2 29 C-2.33 27.35 -2.66 25.7 -3 24 C-2.34 23.67 -1.68 23.34 -1 23 C-4.3 22.34 -7.6 21.68 -11 21 C-11 19.35 -11 17.7 -11 16 C-6.05 15.34 -1.1 14.68 4 14 C4.773 18.64 5.066 21.771 4.688 26.312 C4.57 27.885 4.57 27.885 4.449 29.488 C4.301 30.317 4.153 31.146 4 32 C3.01 32.495 3.01 32.495 2 33 C2 33.99 2 34.98 2 36 C0.68 36 -0.64 36 -2 36 C-2.709 36.012 -3.418 36.023 -4.148 36.035 C-8.578 36.09 -12.622 35.747 -17 35 C-17.99 35.495 -17.99 35.495 -19 36 C-19.248 35.423 -19.495 34.845 -19.75 34.25 C-20.917 31.893 -20.917 31.893 -23 30.062 C-26.539 26.413 -27.291 22.606 -27.312 17.625 C-27.161 10.977 -26.024 7.07 -21.25 2.188 C-14.114 -2.616 -8.032 -2.398 0 0 Z " fill="#F3EDE3" transform="translate(379,691)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2 2.98 2 4 2 C4.33 1.34 4.66 0.68 5 0 C5 1.32 5 2.64 5 4 C5.66 4.33 6.32 4.66 7 5 C7 8.63 7 12.26 7 16 C11.62 16 16.24 16 21 16 C20.67 15.34 20.34 14.68 20 14 C20.66 14 21.32 14 22 14 C21.814 12.907 21.629 11.814 21.438 10.688 C21 7 21 7 22 4 C21.34 3.67 20.68 3.34 20 3 C20.66 3 21.32 3 22 3 C22.33 2.01 22.66 1.02 23 0 C24.32 0.66 25.64 1.32 27 2 C27.02 2.989 27.04 3.978 27.06 4.998 C27.136 8.662 27.225 12.325 27.317 15.989 C27.356 17.576 27.391 19.163 27.422 20.75 C27.467 23.029 27.525 25.307 27.586 27.586 C27.597 28.297 27.609 29.009 27.621 29.742 C27.634 33.821 27.634 33.821 30 37 C25.82 39.786 23.853 38.796 19 38 C19.33 37.01 19.66 36.02 20 35 C20.245 33.317 20.451 31.629 20.625 29.938 C20.72 29.06 20.816 28.182 20.914 27.277 C21.184 24.967 21.184 24.967 20 23 C15.38 23 10.76 23 6 23 C6.165 25.062 6.33 27.125 6.5 29.25 C6.789 32.861 7 36.376 7 40 C4.69 40 2.38 40 0 40 C-1.164 35.88 -1.029 31.977 -0.879 27.734 C-0.855 26.941 -0.832 26.148 -0.807 25.33 C-0.731 22.803 -0.647 20.277 -0.562 17.75 C-0.509 16.034 -0.457 14.318 -0.404 12.602 C-0.275 8.401 -0.14 4.2 0 0 Z " fill="#9E988F" transform="translate(393,688)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C6.98 5.94 8.96 11.88 11 18 C12.888 14.224 13.999 10.803 15.125 6.75 C15.478 5.487 15.831 4.223 16.195 2.922 C16.461 1.958 16.726 0.993 17 0 C19.324 0.059 19.324 0.059 22 1 C23.221 3.44 24.045 5.487 24.812 8.062 C25.028 8.726 25.244 9.389 25.467 10.072 C25.997 11.709 26.501 13.354 27 15 C27.99 15 28.98 15 30 15 C31.32 10.05 32.64 5.1 34 0 C36.31 0 38.62 0 41 0 C37.7 9.9 34.4 19.8 31 30 C29.35 30 27.7 30 26 30 C24.239 25.359 22.535 20.722 21 16 C19.68 16 18.36 16 17 16 C16.876 17.011 16.752 18.021 16.625 19.062 C16.019 22.879 15.128 26.315 14 30 C12.35 30 10.7 30 9 30 C6.44 24.373 4.566 18.674 2.812 12.75 C2.54 11.853 2.267 10.956 1.986 10.031 C1.729 9.17 1.472 8.309 1.207 7.422 C0.975 6.644 0.742 5.867 0.503 5.065 C0 3 0 3 0 0 Z M19 10 C20 13 20 13 20 13 Z M27 17 C27.66 18.32 28.32 19.64 29 21 C28.67 19.68 28.34 18.36 28 17 C27.67 17 27.34 17 27 17 Z " fill="#B1ABA3" transform="translate(286,784)"/>
<path d="M0 0 C2.727 0.375 2.727 0.375 6 2 C7.951 5.146 9.134 8.524 10.375 12 C10.729 12.928 11.084 13.856 11.449 14.812 C14 21.588 14 21.588 14 24 C14.99 24.33 15.98 24.66 17 25 C16.34 25 15.68 25 15 25 C16.65 28.63 18.3 32.26 20 36 C20.66 36 21.32 36 22 36 C22 36.66 22 37.32 22 38 C18.671 39.11 16.284 39.347 13 38 C11.965 35.682 10.964 33.348 10 31 C7.984 29.867 7.984 29.867 6 30 C5.67 30.33 5.34 30.66 5 31 C-1.395 30.302 -1.395 30.302 -4 29 C-4.231 29.626 -4.461 30.253 -4.699 30.898 C-7.396 37.657 -7.396 37.657 -10.75 39.562 C-11.492 39.707 -12.235 39.851 -13 40 C-14.097 36.71 -13.8 35.287 -13 32 C-12.34 32 -11.68 32 -11 32 C-10.876 31.45 -10.753 30.899 -10.625 30.332 C-8.037 19.9 -3.913 9.99 0 0 Z M2 10 C-0.483 16.36 -0.483 16.36 -2 23 C0.64 23 3.28 23 6 23 C7.033 20.212 7.045 19.132 6.062 16.25 C5.712 15.508 5.361 14.765 5 14 C4.01 14.495 4.01 14.495 3 15 C2.67 14.34 2.34 13.68 2 13 C2.66 12.67 3.32 12.34 4 12 C3.34 11.34 2.68 10.68 2 10 Z " fill="#F2ECE2" transform="translate(698,688)"/>
<path d="M0 0 C3.527 4.887 5.561 10.304 4.84 16.352 C3.597 21.392 2.253 26.014 -1.812 29.438 C-6.653 31.867 -11.511 33.136 -16.895 32.137 C-21.974 30.285 -25.043 26.729 -27.75 22.188 C-29.488 16.055 -29.488 10.82 -27.75 4.688 C-25.037 0.137 -21.963 -3.435 -16.859 -5.262 C-10.213 -6.479 -4.909 -4.473 0 0 Z M-20.812 3.438 C-22.955 6.763 -23.082 9.528 -23.062 13.438 C-23.068 14.469 -23.073 15.5 -23.078 16.562 C-22.712 20.529 -21.75 22.588 -18.812 25.25 C-14.324 27.027 -10.423 26.79 -5.812 25.438 C-2.673 22.672 -1.954 20.667 -1.48 16.547 C-1.292 10.891 -1.366 7.116 -4.812 2.438 C-10.12 -1.101 -15.94 -0.575 -20.812 3.438 Z " fill="#E2DDD4" transform="translate(569.8125,694.5625)"/>
<path d="M0 0 C0.848 0.171 1.696 0.343 2.57 0.52 C4.875 1.188 4.875 1.188 7.875 3.188 C8.062 6.812 8.062 6.812 7.875 10.188 C7.545 9.528 7.215 8.867 6.875 8.188 C2.023 6.004 -2.881 5.386 -8.125 6.188 C-11.624 8.301 -12.831 9.305 -14.125 13.188 C-14.531 17.794 -14.669 21.883 -12.875 26.188 C-10.503 28.898 -8.442 30.055 -4.836 30.418 C-3.68 30.397 -3.68 30.397 -2.5 30.375 C-1.336 30.365 -1.336 30.365 -0.148 30.355 C1.944 30.308 1.944 30.308 3.875 29.188 C3.875 28.197 3.875 27.207 3.875 26.188 C4.205 24.867 4.535 23.548 4.875 22.188 C1.905 22.188 -1.065 22.188 -4.125 22.188 C-5.125 19.188 -5.125 19.188 -4.188 17 C-3.662 16.103 -3.662 16.103 -3.125 15.188 C-3.125 15.847 -3.125 16.508 -3.125 17.188 C-2.22 17.062 -2.22 17.062 -1.297 16.934 C-0.498 16.832 0.302 16.73 1.125 16.625 C1.914 16.521 2.703 16.416 3.516 16.309 C5.875 16.188 5.875 16.188 9.875 17.188 C9.875 22.467 9.875 27.747 9.875 33.188 C2.613 36.3 -2.602 38.197 -10.203 35.973 C-14.839 34.079 -17.502 31.246 -20 27 C-21.814 20.854 -21.774 15.62 -20.125 9.438 C-17.166 4.628 -13.18 1.083 -7.688 -0.527 C-4.892 -0.838 -2.746 -0.584 0 0 Z " fill="#ABA59C" transform="translate(507.125,689.8125)"/>
<path d="M0 0 C3.231 0.557 5.024 1.332 7 4 C7 5.32 7 6.64 7 8 C4.042 8.027 1.083 8.047 -1.875 8.062 C-3.138 8.075 -3.138 8.075 -4.426 8.088 C-5.231 8.091 -6.037 8.094 -6.867 8.098 C-7.983 8.106 -7.983 8.106 -9.121 8.114 C-11 8 -11 8 -13 7 C-13.167 9.416 -13.167 9.416 -13 12 C-10.49 14.51 -9.16 14.385 -5.688 14.812 C-2.012 15.454 -0.025 15.985 3.188 18 C5.795 22.315 5.9 26.081 5 31 C3.125 33.938 3.125 33.938 1 36 C0.34 36.99 -0.32 37.98 -1 39 C-7.836 39 -14.613 38.589 -21 36 C-20.722 35.072 -20.722 35.072 -20.438 34.125 C-19.763 31.958 -19.763 31.958 -21 30 C-18.237 30.523 -15.674 31.109 -13 32 C-11.502 32.092 -10.001 32.13 -8.5 32.125 C-7.727 32.128 -6.953 32.13 -6.156 32.133 C-3.816 32.081 -3.816 32.081 -1 31 C-1.103 29.721 -1.206 28.442 -1.312 27.125 C-1.371 26.406 -1.429 25.686 -1.488 24.945 C-1.874 22.605 -1.874 22.605 -5 21 C-7.159 20.9 -7.159 20.9 -9.438 21.25 C-10.199 21.338 -10.961 21.425 -11.746 21.516 C-14.047 21.903 -14.047 21.903 -16.098 23.109 C-18 24 -18 24 -21 23 C-21.33 21.02 -21.66 19.04 -22 17 C-21.34 17 -20.68 17 -20 17 C-20 14.03 -20 11.06 -20 8 C-20.33 8 -20.66 8 -21 8 C-21.043 6.334 -21.041 4.666 -21 3 C-18.139 0.139 -10.155 1.026 -6.27 0.938 C-4.18 0.938 -2.09 0.966 0 1 C0 0.67 0 0.34 0 0 Z M-14 14 C-13 16 -13 16 -13 16 Z " fill="#ADA8A0" transform="translate(817,688)"/>
<path d="M0 0 C1.114 -0.001 2.227 -0.003 3.375 -0.004 C5.119 0.002 5.119 0.002 6.898 0.008 C8.654 0.002 8.654 0.002 10.445 -0.004 C11.556 -0.003 12.668 -0.001 13.812 0 C15.347 0.002 15.347 0.002 16.913 0.003 C19.273 0.133 19.273 0.133 20.273 1.133 C20.372 2.748 20.4 4.367 20.4 5.985 C20.403 7.035 20.406 8.086 20.409 9.168 C20.407 10.33 20.405 11.492 20.403 12.689 C20.405 13.905 20.406 15.121 20.408 16.374 C20.412 19.719 20.411 23.064 20.408 26.409 C20.405 29.904 20.408 33.398 20.409 36.893 C20.411 42.763 20.409 48.634 20.404 54.504 C20.398 61.297 20.4 68.09 20.406 74.884 C20.41 80.708 20.411 86.532 20.408 92.356 C20.407 95.838 20.406 99.32 20.41 102.802 C20.413 106.684 20.408 110.565 20.403 114.447 C20.405 115.607 20.407 116.767 20.409 117.962 C20.406 119.015 20.403 120.069 20.4 121.154 C20.4 122.072 20.4 122.991 20.399 123.937 C20.273 126.133 20.273 126.133 19.273 128.133 C16.898 128.883 16.898 128.883 14.273 129.133 C12.788 128.143 12.788 128.143 11.273 127.133 C10.696 127.483 10.118 127.834 9.523 128.195 C6.787 129.335 6.065 128.93 3.273 128.133 C0.94 128.094 -1.394 128.088 -3.727 128.133 C-3.727 127.473 -3.727 126.813 -3.727 126.133 C-7.829 125.117 -7.829 125.117 -11.727 126.133 C-11.397 124.483 -11.067 122.833 -10.727 121.133 C-10.397 121.133 -10.067 121.133 -9.727 121.133 C-9.232 61.733 -9.232 61.733 -8.727 1.133 C-5.569 0.08 -3.315 0.004 0 0 Z M-6.727 2.133 C-6.727 42.393 -6.727 82.653 -6.727 124.133 C1.853 124.133 10.433 124.133 19.273 124.133 C19.273 83.873 19.273 43.613 19.273 2.133 C10.693 2.133 2.113 2.133 -6.727 2.133 Z " fill="#7B4B2A" transform="translate(587.7265625,118.8671875)"/>
<path d="M0 0 C4.146 -0.029 8.292 -0.047 12.438 -0.062 C13.622 -0.071 14.806 -0.079 16.025 -0.088 C17.15 -0.091 18.275 -0.094 19.434 -0.098 C20.476 -0.103 21.518 -0.108 22.592 -0.114 C25 0 25 0 26 1 C26.098 2.615 26.126 4.234 26.127 5.852 C26.131 7.428 26.131 7.428 26.136 9.035 C26.132 10.778 26.132 10.778 26.129 12.556 C26.132 14.381 26.132 14.381 26.135 16.242 C26.138 19.587 26.137 22.932 26.134 26.277 C26.132 29.771 26.134 33.266 26.136 36.76 C26.138 42.63 26.135 48.501 26.13 54.371 C26.125 61.164 26.127 67.958 26.132 74.751 C26.137 80.575 26.137 86.399 26.135 92.223 C26.133 95.705 26.133 99.188 26.136 102.67 C26.14 106.551 26.135 110.433 26.129 114.314 C26.131 115.474 26.133 116.634 26.136 117.83 C26.133 118.883 26.13 119.936 26.127 121.021 C26.126 122.399 26.126 122.399 26.126 123.805 C26 126 26 126 25 128 C21.625 128.088 18.251 128.141 14.875 128.188 C13.924 128.213 12.972 128.238 11.992 128.264 C6.615 128.32 2.518 128.268 -2 125 C-3.863 122.441 -3.863 122.441 -5.312 119.562 C-6.049 118.141 -6.049 118.141 -6.801 116.691 C-7.807 114.434 -8.515 112.412 -9 110 C-6.504 112.276 -4.934 114.607 -3.312 117.562 C-2.657 118.74 -2.657 118.74 -1.988 119.941 C-1 122 -1 122 -1 124 C7.58 124 16.16 124 25 124 C25 83.74 25 43.48 25 2 C16.75 2 8.5 2 0 2 C0.33 25.76 0.66 49.52 1 74 C-3.649 71.676 -4.737 69.36 -6.906 64.922 C-7.268 64.207 -7.63 63.492 -8.002 62.755 C-8.777 61.221 -9.546 59.685 -10.31 58.145 C-12.303 54.134 -14.332 50.141 -16.355 46.145 C-16.755 45.354 -17.154 44.563 -17.565 43.748 C-21.288 36.385 -25.201 29.14 -29.207 21.927 C-31.482 17.822 -33.721 13.699 -35.887 9.535 C-36.316 8.715 -36.744 7.896 -37.186 7.051 C-37.455 6.374 -37.723 5.697 -38 5 C-37.67 4.34 -37.34 3.68 -37 3 C-29.708 16.226 -22.566 29.528 -15.5 42.875 C-15.108 43.615 -14.716 44.355 -14.312 45.118 C-13.175 47.263 -12.04 49.409 -10.906 51.555 C-10.571 52.187 -10.236 52.82 -9.891 53.472 C-7.529 57.95 -5.252 62.466 -3 67 C-2.67 67 -2.34 67 -2 67 C-1.977 59.507 -1.959 52.014 -1.948 44.52 C-1.943 41.041 -1.936 37.562 -1.925 34.082 C-1.912 30.081 -1.907 26.079 -1.902 22.078 C-1.897 20.83 -1.892 19.582 -1.887 18.296 C-1.887 17.135 -1.887 15.974 -1.886 14.778 C-1.884 13.757 -1.882 12.735 -1.88 11.683 C-1.987 9.289 -2.313 7.285 -3 5 C-2.34 5 -1.68 5 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z M-2 68 C-1 70 -1 70 -1 70 Z " fill="#7B4D2B" transform="translate(694,119)"/>
<path d="M0 0 C1.473 5.583 1.019 11.244 0.873 16.963 C0.809 21.741 1.136 25.505 3 30 C3.66 30 4.32 30 5 30 C5 30.99 5 31.98 5 33 C6.461 32.744 7.919 32.47 9.375 32.188 C10.187 32.037 10.999 31.886 11.836 31.73 C14.358 31.125 14.358 31.125 16 28 C16.368 25.191 16.368 25.191 16.414 22.02 C16.453 20.856 16.491 19.693 16.531 18.494 C16.578 16.672 16.578 16.672 16.625 14.812 C16.664 13.586 16.702 12.359 16.742 11.096 C16.837 8.064 16.922 5.032 17 2 C18.65 1.67 20.3 1.34 22 1 C22.88 6.342 23.213 11.527 23.25 16.938 C23.271 17.697 23.291 18.457 23.312 19.24 C23.357 25.716 21.983 30.906 18.438 36.438 C14.545 38.933 10.564 39.188 6 39 C1.764 37.51 -1.155 35.477 -4 32 C-5.15 28.643 -5.115 25.448 -5.098 21.922 C-5.094 20.762 -5.091 19.602 -5.088 18.406 C-5.08 17.2 -5.071 15.993 -5.062 14.75 C-5.058 13.528 -5.053 12.306 -5.049 11.047 C-5.037 8.031 -5.021 5.016 -5 2 C-5.66 1.67 -6.32 1.34 -7 1 C-4.537 -0.231 -2.72 -0.072 0 0 Z " fill="#A7A299" transform="translate(282,688)"/>
<path d="M0 0 C1.273 2.52 2.544 5.041 3.812 7.562 C4.169 8.267 4.525 8.971 4.893 9.697 C5.944 11.791 6.978 13.892 8 16 C8.319 16.655 8.638 17.309 8.966 17.984 C11.119 22.642 11.119 22.642 10 26 C8.889 23.69 7.786 21.378 6.688 19.062 C6.379 18.422 6.07 17.782 5.752 17.123 C3.382 12.109 1.783 7.35 0 2 C-11.22 2 -22.44 2 -34 2 C-34 42.26 -34 82.52 -34 124 C-25.42 124 -16.84 124 -8 124 C-8 98.92 -8 73.84 -8 48 C-5.609 50.391 -5.403 51.696 -5 55 C-5.33 55.99 -5.66 56.98 -6 58 C-6.074 60.147 -6.087 62.296 -6.066 64.443 C-6.061 65.092 -6.057 65.742 -6.052 66.41 C-6.041 67.818 -6.029 69.226 -6.014 70.634 C-5.992 72.861 -5.978 75.088 -5.966 77.316 C-5.933 83.648 -5.891 89.981 -5.829 96.313 C-5.791 100.187 -5.768 104.062 -5.754 107.936 C-5.746 109.413 -5.733 110.89 -5.713 112.366 C-5.687 114.43 -5.679 116.493 -5.675 118.557 C-5.666 119.731 -5.656 120.906 -5.646 122.116 C-6 125 -6 125 -7.363 126.88 C-9.751 128.514 -11.603 128.356 -14.473 128.328 C-15.537 128.323 -16.601 128.318 -17.697 128.312 C-18.808 128.292 -19.918 128.271 -21.062 128.25 C-22.177 128.245 -23.291 128.24 -24.439 128.234 C-32.691 128.154 -32.691 128.154 -35 127 C-35 85.42 -35 43.84 -35 1 C-5 0 -5 0 0 0 Z " fill="#90582E" transform="translate(475,119)"/>
<path d="M0 0 C1.118 0.003 2.237 0.006 3.389 0.01 C4.556 0.018 5.723 0.027 6.926 0.035 C8.105 0.04 9.283 0.044 10.498 0.049 C13.412 0.061 16.325 0.077 19.238 0.098 C19.238 1.088 19.238 2.078 19.238 3.098 C10.328 2.768 1.418 2.438 -7.762 2.098 C-7.762 42.358 -7.762 82.618 -7.762 124.098 C0.158 124.098 8.078 124.098 16.238 124.098 C16.238 99.018 16.238 73.938 16.238 48.098 C19.035 50.894 20.593 53.175 22.488 56.598 C23.03 57.557 23.571 58.516 24.129 59.504 C25.238 62.098 25.238 62.098 24.238 65.098 C23.578 65.098 22.918 65.098 22.238 65.098 C22.238 63.448 22.238 61.798 22.238 60.098 C21.578 59.768 20.918 59.438 20.238 59.098 C19.908 58.438 19.578 57.778 19.238 57.098 C19.228 57.945 19.218 58.793 19.207 59.666 C19.109 67.641 19.007 75.617 18.899 83.592 C18.844 87.693 18.79 91.793 18.741 95.894 C18.693 99.849 18.641 103.804 18.585 107.76 C18.565 109.271 18.546 110.782 18.529 112.293 C18.505 114.405 18.475 116.517 18.444 118.628 C18.428 119.832 18.413 121.036 18.396 122.276 C18.238 125.098 18.238 125.098 17.238 127.098 C16.578 127.098 15.918 127.098 15.238 127.098 C15.238 127.758 15.238 128.418 15.238 129.098 C13.985 129.014 12.732 128.93 11.441 128.844 C9.79 128.741 8.139 128.638 6.488 128.535 C5.663 128.479 4.838 128.423 3.988 128.365 C3.189 128.317 2.39 128.269 1.566 128.219 C0.833 128.172 0.1 128.124 -0.655 128.076 C-2.791 128.014 -2.791 128.014 -4.907 128.653 C-6.762 129.098 -6.762 129.098 -8.762 128.098 C-8.762 86.188 -8.762 44.278 -8.762 1.098 C-5.584 0.038 -3.336 -0.017 0 0 Z M18.238 54.098 C19.238 56.098 19.238 56.098 19.238 56.098 Z M-6.762 126.098 C-6.102 126.758 -5.442 127.418 -4.762 128.098 C-4.762 127.438 -4.762 126.778 -4.762 126.098 C-5.422 126.098 -6.082 126.098 -6.762 126.098 Z " fill="#814F2B" transform="translate(635.76171875,118.90234375)"/>
<path d="M0 0 C3.019 -0.376 5.08 -0.629 8 0 C10.368 2.076 11.774 4.588 13.312 7.312 C14.139 8.685 14.969 10.056 15.801 11.426 C16.329 12.345 16.329 12.345 16.868 13.282 C18.147 15.348 18.147 15.348 21 18 C21 12.06 21 6.12 21 0 C23.31 0 25.62 0 28 0 C27.333 10 26.667 20 26 30 C23.074 29.668 21.445 29.447 19.331 27.326 C18.616 26.265 18.616 26.265 17.887 25.184 C17.361 24.42 16.836 23.656 16.295 22.869 C15.764 22.067 15.234 21.264 14.688 20.438 C13.863 19.228 13.863 19.228 13.021 17.994 C11.668 16.005 10.328 14.007 9 12 C8.951 13.192 8.902 14.385 8.852 15.613 C8.777 17.18 8.701 18.746 8.625 20.312 C8.579 21.491 8.579 21.491 8.531 22.693 C8.473 23.829 8.473 23.829 8.414 24.988 C8.383 25.685 8.351 26.381 8.319 27.099 C8 29 8 29 6 32 C5.01 32 4.02 32 3 32 C2.98 31.102 2.96 30.203 2.94 29.278 C2.862 25.949 2.775 22.621 2.683 19.292 C2.644 17.851 2.609 16.41 2.578 14.968 C2.533 12.898 2.475 10.828 2.414 8.758 C2.383 7.511 2.351 6.265 2.319 4.981 C2.303 1.885 2.303 1.885 0 0 Z " fill="#9B968F" transform="translate(690,784)"/>
<path d="M0 0 C7.26 0 14.52 0 22 0 C24.25 11.25 24.25 11.25 23.25 14.25 C21.975 16.035 20.565 17.469 19 19 C19.505 19.626 20.011 20.253 20.531 20.898 C21.181 21.716 21.831 22.533 22.5 23.375 C23.475 24.593 23.475 24.593 24.469 25.836 C26 28 26 28 26 30 C23.227 30.312 23.227 30.312 20 30 C18.023 27.812 18.023 27.812 16.375 25 C14.811 22.218 14.811 22.218 13 20 C11.68 20 10.36 20 9 20 C8.67 23.3 8.34 26.6 8 30 C6.35 30.66 4.7 31.32 3 32 C0.875 25.375 0.875 25.375 2 22 C2.226 19.607 2.408 17.211 2.562 14.812 C2.646 13.54 2.73 12.268 2.816 10.957 C2.877 9.981 2.938 9.005 3 8 C2.34 7.67 1.68 7.34 1 7 C1.206 6.216 1.413 5.433 1.625 4.625 C1.749 3.759 1.873 2.893 2 2 C1.34 1.34 0.68 0.68 0 0 Z M7 5 C7 7.97 7 10.94 7 14 C9.97 14 12.94 14 16 14 C16.144 13.237 16.289 12.474 16.438 11.688 C17 9 17 9 18 6 C14.571 4.285 10.783 4.946 7 5 Z M18 9 C17.505 10.98 17.505 10.98 17 13 C17.66 11.68 18.32 10.36 19 9 C18.67 9 18.34 9 18 9 Z " fill="#BDB8B0" transform="translate(417,784)"/>
<path d="M0 0 C5.871 1.174 6.316 2.11 9.625 6.812 C10.38 7.871 11.136 8.929 11.914 10.02 C12.602 11.003 13.291 11.987 14 13 C14.994 14.338 15.991 15.673 17 17 C17.754 12.021 17.754 12.021 18 7 C18 5 18 3 18 1 C19.65 0.67 21.3 0.34 23 0 C23.66 1.98 24.32 3.96 25 6 C24.34 6 23.68 6 23 6 C23.139 9.459 23.287 12.917 23.438 16.375 C23.477 17.36 23.516 18.345 23.557 19.359 C23.599 20.3 23.64 21.241 23.684 22.211 C23.72 23.08 23.757 23.95 23.795 24.845 C23.874 27.048 23.874 27.048 25 29 C23.02 29.33 21.04 29.66 19 30 C18.313 29.036 17.626 28.072 16.918 27.078 C16.008 25.802 15.098 24.526 14.188 23.25 C13.736 22.616 13.284 21.982 12.818 21.328 C10.58 18.191 8.332 15.068 6 12 C5.939 13.192 5.879 14.385 5.816 15.613 C5.732 17.18 5.647 18.746 5.562 20.312 C5.523 21.098 5.484 21.884 5.443 22.693 C5.401 23.451 5.36 24.208 5.316 24.988 C5.28 25.685 5.243 26.381 5.205 27.099 C5 29 5 29 4 32 C2.68 32 1.36 32 0 32 C0 21.44 0 10.88 0 0 Z M18 19 C19 21 19 21 19 21 Z " fill="#BAB5AD" transform="translate(557,784)"/>
<path d="M0 0 C0.749 0.005 1.498 0.01 2.269 0.016 C3.078 0.019 3.887 0.022 4.72 0.026 C5.571 0.034 6.421 0.042 7.297 0.051 C8.151 0.056 9.005 0.06 9.885 0.065 C12.001 0.077 14.118 0.094 16.234 0.114 C16.234 0.774 16.234 1.434 16.234 2.114 C17.039 2.897 17.843 3.681 18.672 4.489 C21.152 7.029 21.645 7.816 22.234 11.114 C22.894 11.444 23.554 11.774 24.234 12.114 C23.337 18.885 22.264 23.335 17.234 28.114 C16.574 28.114 15.914 28.114 15.234 28.114 C14.904 28.774 14.574 29.434 14.234 30.114 C9.284 30.114 4.334 30.114 -0.766 30.114 C-1.891 23.364 -1.891 23.364 -1.645 20.028 C-1.596 19.325 -1.548 18.623 -1.498 17.899 C-1.442 17.186 -1.386 16.473 -1.328 15.739 C-1.276 15.002 -1.224 14.266 -1.17 13.508 C-1.042 11.709 -0.905 9.911 -0.766 8.114 C-1.426 8.114 -2.086 8.114 -2.766 8.114 C-2.26 5.945 -1.766 4.114 -0.766 2.114 C-1.426 1.784 -2.086 1.454 -2.766 1.114 C-1.766 0.114 -1.766 0.114 0 0 Z M4.234 5.114 C4.234 11.714 4.234 18.314 4.234 25.114 C11.612 25.74 11.612 25.74 15.172 23.551 C17.798 20.447 18.003 18.113 18.234 14.114 C17.244 14.444 16.254 14.774 15.234 15.114 C15.894 14.454 16.554 13.794 17.234 13.114 C17.075 10.009 16.659 8.554 14.484 6.301 C11.812 4.891 10.216 4.721 7.234 5.114 C6.574 5.774 5.914 6.434 5.234 7.114 C4.904 6.454 4.574 5.794 4.234 5.114 Z " fill="#E9E3DA" transform="translate(450.765869140625,783.886474609375)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C26.153 15.89 26.302 31.779 26.443 47.669 C26.509 55.046 26.576 62.423 26.648 69.801 C26.711 76.228 26.77 82.656 26.825 89.084 C26.854 92.489 26.885 95.895 26.921 99.3 C26.959 103.096 26.991 106.892 27.022 110.687 C27.035 111.824 27.048 112.962 27.061 114.133 C27.068 115.161 27.075 116.189 27.082 117.248 C27.091 118.147 27.099 119.046 27.107 119.972 C27 122 27 122 26 123 C23.927 123.088 21.852 123.107 19.777 123.098 C18.517 123.094 17.257 123.091 15.959 123.088 C14.618 123.08 13.278 123.071 11.938 123.062 C10.593 123.057 9.249 123.053 7.904 123.049 C4.603 123.037 1.301 123.021 -2 123 C-3.49 120.277 -4.965 117.545 -6.438 114.812 C-6.858 114.044 -7.279 113.275 -7.713 112.482 C-9.75 108.685 -11.52 105.354 -12 101 C-9.843 103.014 -8.342 104.902 -6.957 107.504 C-6.606 108.162 -6.254 108.82 -5.893 109.498 C-5.536 110.179 -5.18 110.861 -4.812 111.562 C-4.26 112.59 -4.26 112.59 -3.697 113.639 C-1 118.738 -1 118.738 -1 121 C7.58 121 16.16 121 25 121 C25 81.4 25 41.8 25 1 C17.41 1 9.82 1 2 1 C2 24.76 2 48.52 2 73 C-2.233 68.767 -4.545 63.618 -7.263 58.348 C-8.251 56.436 -9.254 54.531 -10.258 52.627 C-10.886 51.417 -11.514 50.207 -12.141 48.996 C-12.713 47.895 -13.286 46.793 -13.876 45.658 C-14.247 44.781 -14.618 43.904 -15 43 C-14.67 42.34 -14.34 41.68 -14 41 C-9.71 48.59 -5.42 56.18 -1 64 C-1 59.71 -1 55.42 -1 51 C-0.67 50.01 -0.34 49.02 0 48 C0.087 46.42 0.118 44.836 0.114 43.253 C0.113 42.292 0.113 41.332 0.113 40.342 C0.108 39.307 0.103 38.273 0.098 37.207 C0.096 35.615 0.096 35.615 0.093 33.991 C0.088 30.598 0.075 27.205 0.062 23.812 C0.057 21.514 0.053 19.215 0.049 16.916 C0.039 11.277 0.019 5.639 0 0 Z M-1 65 C-0.34 66.32 0.32 67.64 1 69 C0.67 67.68 0.34 66.36 0 65 C-0.33 65 -0.66 65 -1 65 Z " fill="#714B32" transform="translate(719,296)"/>
<path d="M0 0 C0.688 1.812 0.688 1.812 1 4 C-0.438 5.75 -0.438 5.75 -2 7 C-2.516 6.67 -3.031 6.34 -3.562 6 C-6.78 4.68 -9.557 4.868 -13 5 C-12.67 6.32 -12.34 7.64 -12 9 C-13.32 9 -14.64 9 -16 9 C-15.886 10.959 -15.759 12.917 -15.625 14.875 C-15.555 15.966 -15.486 17.056 -15.414 18.18 C-15.267 21.13 -15.267 21.13 -13 23 C-9.417 23.167 -9.417 23.167 -6 23 C-6 22.34 -6 21.68 -6 21 C-5.34 20.67 -4.68 20.34 -4 20 C-3.67 19.01 -3.34 18.02 -3 17 C-3.928 17.227 -4.856 17.454 -5.812 17.688 C-9 18 -9 18 -10.875 16.688 C-12 15 -12 15 -12 13 C-5.25 10 -5.25 10 -3 10 C-3 10.66 -3 11.32 -3 12 C-0.525 12.495 -0.525 12.495 2 13 C2.626 23.093 2.626 23.093 0.188 26.625 C-3.895 29.191 -7.261 29.316 -12 29 C-16.064 27.814 -18.583 26.625 -21 23 C-21.598 21.062 -21.598 21.062 -22.062 19 C-22.614 16.595 -23.217 14.349 -24 12 C-22.812 9.688 -22.812 9.688 -21 7 C-20.381 5.948 -19.763 4.896 -19.125 3.812 C-14.59 -2.189 -6.872 -2.291 0 0 Z M-15 6 C-14 8 -14 8 -14 8 Z " fill="#CBC6BD" transform="translate(683,785)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5.672 11.025 6.116 20.948 6 31 C13.59 31 21.18 31 29 31 C29.99 27.7 30.98 24.4 32 21 C32.961 18.65 33.946 16.309 35 14 C36.157 17.471 35.83 18.147 34.566 21.449 C34.247 22.297 33.928 23.144 33.6 24.018 C33.257 24.899 32.915 25.78 32.562 26.688 C32.224 27.58 31.886 28.473 31.537 29.393 C30.7 31.598 29.854 33.8 29 36 C29.99 36 30.98 36 32 36 C32.495 35.01 32.495 35.01 33 34 C33 34.99 33 35.98 33 37 C29.018 38.054 26.018 37.841 22 37 C21.67 37.66 21.34 38.32 21 39 C20.34 38.67 19.68 38.34 19 38 C16.689 37.903 14.375 37.87 12.062 37.875 C10.193 37.871 10.193 37.871 8.285 37.867 C4.993 37.848 4.993 37.848 2 39 C0.521 37.947 0.521 37.947 -1 36 C-1.356 32.785 -1.356 32.785 -1.328 28.848 C-1.326 28.151 -1.324 27.455 -1.322 26.737 C-1.316 25.265 -1.302 23.793 -1.281 22.322 C-1.25 20.074 -1.24 17.828 -1.234 15.58 C-1.158 3.671 -1.158 3.671 0 0 Z M27 32 C26.01 32.99 25.02 33.98 24 35 C25.32 34.67 26.64 34.34 28 34 C27.67 33.34 27.34 32.68 27 32 Z " fill="#908C85" transform="translate(211,689)"/>
<path d="M0 0 C4.54 0.284 7.954 0.697 11.625 3.562 C15.087 8.186 16.241 12.796 15.945 18.523 C15.267 22.842 13.268 24.998 10.062 27.812 C5.762 30.9 3.263 30.654 -1.895 30.066 C-5.307 29.373 -7.072 28.153 -9.375 25.562 C-11.151 22.179 -11.375 20.473 -11.375 16.562 C-12.035 16.562 -12.695 16.562 -13.375 16.562 C-12.23 13.129 -10.891 9.844 -9.375 6.562 C-8.632 4.861 -8.632 4.861 -7.875 3.125 C-4.922 0.098 -4.09 0.122 0 0 Z M-4.062 7.438 C-6.889 11.257 -7.115 13.528 -6.852 18.18 C-6.472 20.828 -6.472 20.828 -4.375 23.688 C-0.25 26.266 1.815 26.183 6.625 25.562 C8.83 23.955 9.523 22.937 10.242 20.293 C10.799 14.805 10.781 11.36 7.625 6.562 C5.975 6.893 4.325 7.222 2.625 7.562 C2.295 6.903 1.965 6.242 1.625 5.562 C-1.511 5.276 -1.511 5.276 -4.062 7.438 Z " fill="#C9C3BB" transform="translate(381.375,783.4375)"/>
<path d="M0 0 C7.218 -0.47 12.155 -0.506 18 4 C21.936 9.39 21.778 14.552 21 21 C19.231 24.878 17.736 26.47 14.312 28.938 C9.319 30.539 4.764 30.035 -0.129 28.203 C-3.733 25.886 -4.893 21.997 -6 18 C-6.299 12.563 -5.181 8.453 -2 4 C-1.319 2.674 -0.65 1.342 0 0 Z M0 10 C-1.247 14.654 -1.356 17.758 1 22 C2.02 23.937 2.02 23.937 4 25 C7.658 25.559 10.852 25.815 13.938 23.625 C16.752 19.321 16.551 15.015 16 10 C14.67 6.772 14.67 6.772 12 5 C6.14 3.934 3.461 5.216 0 10 Z " fill="#A6A199" transform="translate(528,784)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C7.573 3.788 9.14 7.578 10.7 11.371 C11.231 12.661 11.765 13.95 12.3 15.238 C13.07 17.09 13.832 18.945 14.594 20.801 C15.055 21.916 15.515 23.032 15.99 24.181 C17 27 17 27 17 30 C15.35 30 13.7 30 12 30 C11.34 28.02 10.68 26.04 10 24 C5.71 23.34 1.42 22.68 -3 22 C-3.66 24.64 -4.32 27.28 -5 30 C-6.65 30 -8.3 30 -10 30 C-11 28 -11 28 -10.104 25.203 C-9.405 23.459 -9.405 23.459 -8.691 21.68 C-8.446 21.061 -8.2 20.442 -7.947 19.805 C-7.16 17.825 -6.361 15.85 -5.562 13.875 C-5.027 12.535 -4.493 11.196 -3.959 9.855 C-2.647 6.567 -1.326 3.283 0 0 Z M2 8 C1.01 10.97 0.02 13.94 -1 17 C-0.01 16.67 0.98 16.34 2 16 C4.688 16.438 4.688 16.438 7 17 C6.01 14.03 5.02 11.06 4 8 C3.34 8 2.68 8 2 8 Z " fill="#B0ABA3" transform="translate(638,784)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C13.438 25.276 13.438 25.276 13.438 30.062 C10.562 29.875 10.562 29.875 7.438 29.062 C6.062 26.5 6.062 26.5 5.438 24.062 C4.778 24.062 4.117 24.062 3.438 24.062 C3.438 23.403 3.438 22.742 3.438 22.062 C0.138 22.062 -3.163 22.062 -6.562 22.062 C-6.893 25.033 -7.222 28.003 -7.562 31.062 C-12.562 31.062 -12.562 31.062 -15.562 29.062 C-14.562 26.062 -14.562 26.062 -12.562 24.062 C-11.8 22.355 -11.099 20.619 -10.434 18.871 C-10.038 17.844 -9.643 16.818 -9.236 15.76 C-8.828 14.684 -8.42 13.608 -8 12.5 C-7.188 10.373 -6.375 8.246 -5.559 6.121 C-5.2 5.175 -4.84 4.23 -4.47 3.256 C-3.154 0.077 -3.154 0.077 0 0 Z M-0.562 7.062 C-1.883 10.362 -3.202 13.663 -4.562 17.062 C-3.139 17.032 -3.139 17.032 -1.688 17 C1.438 17.062 1.438 17.062 3.438 18.062 C2.886 14.113 2.048 10.713 0.438 7.062 C0.107 7.062 -0.222 7.062 -0.562 7.062 Z " fill="#B5AFA6" transform="translate(743.5625,783.9375)"/>
<path d="M0 0 C-0.364 0.643 -0.727 1.286 -1.102 1.949 C-2.77 4.948 -4.389 7.97 -6 11 C-6.572 12.045 -7.145 13.091 -7.734 14.168 C-11.745 22.008 -12.775 28.236 -12 37 C-15.465 37.495 -15.465 37.495 -19 38 C-19.495 36.515 -19.495 36.515 -20 35 C-19.34 35 -18.68 35 -18 35 C-18.66 33.02 -19.32 31.04 -20 29 C-19.34 29 -18.68 29 -18 29 C-19.05 22.493 -20.938 17.981 -24.469 12.434 C-30 3.643 -30 3.643 -30 0 C-28.02 0 -26.04 0 -24 0 C-21.36 5.28 -18.72 10.56 -16 16 C-15.06 14.285 -15.06 14.285 -14.102 12.535 C-13.276 11.044 -12.451 9.553 -11.625 8.062 C-11.212 7.308 -10.8 6.553 -10.375 5.775 C-9.776 4.697 -9.776 4.697 -9.164 3.598 C-8.797 2.933 -8.431 2.268 -8.053 1.582 C-5.798 -1.806 -3.544 -0.839 0 0 Z " fill="#B4AEA5" transform="translate(748,690)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.99 0.585 5.979 1.17 5.968 1.772 C5.927 4.432 5.901 7.091 5.875 9.75 C5.858 10.67 5.841 11.591 5.824 12.539 C5.818 13.429 5.811 14.318 5.805 15.234 C5.794 16.051 5.784 16.868 5.773 17.71 C6.022 20.219 6.743 21.834 8 24 C12.141 24.21 12.141 24.21 16 23 C17.683 15.46 17.334 8.295 15 1 C16 0 16 0 19.562 -0.062 C20.697 -0.042 21.831 -0.021 23 0 C23.058 3.208 23.094 6.416 23.125 9.625 C23.142 10.529 23.159 11.432 23.176 12.363 C23.212 17.283 23.113 21.448 21 26 C17.76 28.863 15.739 29.909 11.438 30.438 C7.781 29.972 5.899 29.236 3 27 C0.775 23.662 0.71 22.372 0.586 18.457 C0.547 17.384 0.509 16.311 0.469 15.205 C0.438 14.086 0.407 12.966 0.375 11.812 C0.317 10.116 0.317 10.116 0.258 8.385 C0.163 5.59 0.078 2.795 0 0 Z " fill="#DAD3CA" transform="translate(257,784)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.356 4.012 3.356 4.012 3.328 6.473 C3.323 7.373 3.318 8.273 3.312 9.201 C3.292 10.145 3.271 11.09 3.25 12.062 C3.245 12.98 3.24 13.897 3.234 14.842 C3.169 20.625 2.728 26.263 2 32 C4.64 32 7.28 32 10 32 C10.625 33.875 10.625 33.875 11 36 C9 38 9 38 5.918 38.266 C4.687 38.26 3.456 38.255 2.188 38.25 C0.964 38.255 -0.259 38.26 -1.52 38.266 C-4.808 38.015 -7.051 37.416 -10 36 C-10 34.68 -10 33.36 -10 32 C-6.25 30.875 -6.25 30.875 -4 32 C-4.072 31.022 -4.144 30.043 -4.219 29.035 C-4.716 22.011 -5.124 15.046 -5 8 C-6.65 8.33 -8.3 8.66 -10 9 C-10.495 6.525 -10.495 6.525 -11 4 C-9.236 3.275 -7.463 2.571 -5.688 1.875 C-4.701 1.481 -3.715 1.086 -2.699 0.68 C-1.808 0.455 -0.918 0.231 0 0 Z " fill="#D6D1C7" transform="translate(781,689)"/>
<path d="M0 0 C1.252 0.047 2.503 0.094 3.793 0.143 C4.457 0.166 5.122 0.19 5.806 0.214 C7.936 0.291 10.066 0.375 12.195 0.459 C13.636 0.512 15.077 0.565 16.517 0.617 C20.056 0.747 23.594 0.883 27.133 1.022 C27.624 2.015 28.116 3.008 28.622 4.031 C31.798 10.44 35.011 16.82 38.371 23.135 C38.92 24.171 39.47 25.208 40.036 26.275 C41.137 28.346 42.244 30.413 43.358 32.477 C43.862 33.428 44.367 34.378 44.886 35.358 C45.572 36.635 45.572 36.635 46.271 37.937 C46.698 38.969 46.698 38.969 47.133 40.022 C46.638 41.012 46.638 41.012 46.133 42.022 C44.862 39.702 43.593 37.382 42.328 35.06 C41.231 33.06 40.121 31.068 38.998 29.083 C35.985 23.702 33.371 18.57 31.65 12.634 C30.226 9.19 30.226 9.19 25.133 3.022 C16.223 2.692 7.313 2.362 -1.867 2.022 C-1.867 41.952 -1.867 81.882 -1.867 123.022 C6.053 123.022 13.973 123.022 22.133 123.022 C22.133 98.272 22.133 73.522 22.133 48.022 C24.303 52.363 24.351 54.318 24.268 59.085 C24.258 59.788 24.249 60.491 24.239 61.215 C24.206 63.531 24.157 65.847 24.109 68.162 C24.083 69.771 24.058 71.379 24.033 72.988 C23.967 77.216 23.888 81.445 23.806 85.673 C23.725 89.99 23.656 94.306 23.586 98.623 C23.446 107.09 23.293 115.556 23.133 124.022 C22.421 124.047 21.71 124.072 20.977 124.098 C17.758 124.214 14.539 124.336 11.32 124.459 C10.201 124.499 9.081 124.538 7.927 124.578 C6.854 124.62 5.781 124.662 4.676 124.705 C3.686 124.742 2.696 124.779 1.676 124.816 C-0.989 124.93 -0.989 124.93 -3.867 126.022 C-4.197 125.032 -4.527 124.042 -4.867 123.022 C-4.207 123.022 -3.547 123.022 -2.867 123.022 C-2.938 122.432 -3.009 121.843 -3.082 121.235 C-3.81 114.676 -4.01 108.217 -3.997 101.621 C-3.999 99.983 -3.999 99.983 -4.002 98.311 C-4.007 94.734 -4.004 91.157 -4 87.58 C-4.001 85.086 -4.002 82.592 -4.003 80.097 C-4.005 74.888 -4.002 69.68 -3.998 64.471 C-3.993 58.445 -3.994 52.419 -4 46.393 C-4.005 40.586 -4.004 34.779 -4.001 28.972 C-4.001 26.502 -4.001 24.033 -4.004 21.564 C-4.006 18.128 -4.002 14.693 -3.997 11.258 C-4 9.716 -4 9.716 -4.003 8.143 C-4 7.213 -3.997 6.284 -3.994 5.327 C-3.994 4.515 -3.994 3.703 -3.993 2.866 C-3.781 -0.239 -3.147 0.024 0 0 Z " fill="#71482C" transform="translate(656.867431640625,294.978271484375)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 0.66 4.66 1.32 5 2 C5.66 2.33 6.32 2.66 7 3 C6.67 4.65 6.34 6.3 6 8 C2.125 7.125 2.125 7.125 1 6 C-0.666 5.959 -2.334 5.957 -4 6 C-4.128 8.385 -4.128 8.385 -3 11 C-1.051 12.211 0.961 13.101 3.051 14.047 C5.652 15.319 6.851 16.32 8 19 C7.935 22.692 7.835 25.037 5.375 27.875 C0.292 30.283 -3.685 30.305 -8.938 28.562 C-10.297 28.056 -11.654 27.54 -13 27 C-12.67 25.02 -12.34 23.04 -12 21 C-10 22 -10 22 -7 24 C-4.943 24.241 -4.943 24.241 -2.812 24.125 C-1.554 24.084 -0.296 24.043 1 24 C1.33 22.68 1.66 21.36 2 20 C1.336 19.769 0.672 19.539 -0.012 19.301 C-9.853 15.666 -9.853 15.666 -13 11 C-13.15 6.725 -12.694 4.796 -9.875 1.562 C-6.315 -0.372 -4.004 -0.291 0 0 Z M-8 7 C-7.67 8.32 -7.34 9.64 -7 11 C-6.34 10.67 -5.68 10.34 -5 10 C-5.33 9.01 -5.66 8.02 -6 7 C-6.66 7 -7.32 7 -8 7 Z " fill="#D3CDC5" transform="translate(243,784)"/>
<path d="M0 0 C4.292 0.048 7.167 0.85 10.812 3.25 C11.803 4.735 11.803 4.735 12.812 6.25 C12.482 6.91 12.153 7.57 11.812 8.25 C8.973 8.25 6.575 7.847 3.812 7.25 C3.812 6.92 3.812 6.59 3.812 6.25 C1.503 6.25 -0.808 6.25 -3.188 6.25 C-3.518 7.57 -3.847 8.89 -4.188 10.25 C-3.356 10.504 -2.525 10.758 -1.668 11.02 C-0.581 11.364 0.505 11.708 1.625 12.062 C3.243 12.567 3.243 12.567 4.895 13.082 C7.673 14.194 9.1 14.834 10.812 17.25 C11.586 24.21 11.586 24.21 9 27.688 C4.961 30.572 0.639 30.95 -4.188 30.25 C-6.496 29.137 -8.167 27.866 -10.188 26.25 C-9.528 24.93 -8.867 23.61 -8.188 22.25 C-7.625 22.577 -7.063 22.905 -6.484 23.242 C-3.919 24.368 -2.219 24.457 0.562 24.375 C1.761 24.348 1.761 24.348 2.984 24.32 C3.588 24.297 4.191 24.274 4.812 24.25 C5.143 22.6 5.472 20.95 5.812 19.25 C5.065 19.08 4.317 18.91 3.547 18.734 C2.562 18.492 1.577 18.25 0.562 18 C-0.412 17.768 -1.387 17.536 -2.391 17.297 C-5.553 16.113 -7.055 14.841 -9.188 12.25 C-10.25 9.312 -10.25 9.312 -10.188 6.25 C-7.812 3.438 -7.812 3.438 -5.188 1.25 C-3.637 -0.301 -2.151 0.043 0 0 Z " fill="#9F9B93" transform="translate(771.1875,783.75)"/>
<path d="M0 0 C2.148 3.222 2.258 4.119 2.291 7.835 C2.303 8.817 2.316 9.798 2.329 10.809 C2.331 11.869 2.334 12.928 2.336 14.02 C2.343 15.111 2.349 16.202 2.356 17.327 C2.366 19.637 2.371 21.948 2.371 24.259 C2.375 27.788 2.411 31.317 2.449 34.846 C2.455 37.091 2.459 39.337 2.461 41.582 C2.475 42.635 2.49 43.689 2.504 44.774 C2.459 52.174 1.224 55.406 -3.703 60.898 C-4.469 61.629 -4.469 61.629 -5.25 62.375 C-6.158 63.241 -7.065 64.108 -8 65 C-8.99 64.505 -8.99 64.505 -10 64 C-9.434 63.446 -8.868 62.891 -8.285 62.32 C-7.552 61.596 -6.818 60.871 -6.062 60.125 C-5.332 59.406 -4.601 58.686 -3.848 57.945 C-1.021 54.97 -0.865 53.186 -0.795 49.183 C-0.774 48.168 -0.754 47.153 -0.734 46.108 C-0.717 45.038 -0.701 43.969 -0.684 42.867 C-0.665 41.886 -0.646 40.905 -0.627 39.895 C-0.559 36.222 -0.499 32.548 -0.438 28.875 C-0.293 20.666 -0.149 12.458 0 4 C-16.83 4 -33.66 4 -51 4 C-51 11.26 -51 18.52 -51 26 C-42.75 26 -34.5 26 -26 26 C-25.876 28.702 -25.753 31.404 -25.625 34.188 C-25.57 35.029 -25.516 35.871 -25.459 36.739 C-25.362 41.166 -25.575 42.539 -28.707 45.938 C-38.258 51.92 -50.16 52.48 -61 50 C-61 49.67 -61 49.34 -61 49 C-56.38 48.67 -51.76 48.34 -47 48 C-47 47.67 -47 47.34 -47 47 C-46.287 47.046 -45.574 47.093 -44.84 47.141 C-38.286 47.388 -34.235 47.097 -29 43 C-27.784 39.794 -27.897 36.648 -27.938 33.25 C-27.947 32.265 -27.956 31.28 -27.965 30.266 C-27.976 29.518 -27.988 28.77 -28 28 C-31.125 27.971 -34.25 27.953 -37.375 27.938 C-38.709 27.925 -38.709 27.925 -40.07 27.912 C-40.921 27.909 -41.772 27.906 -42.648 27.902 C-43.434 27.897 -44.219 27.892 -45.029 27.886 C-47.078 27.874 -47.078 27.874 -49 29 C-49.33 29.66 -49.66 30.32 -50 31 C-52.678 28.322 -52.305 26.86 -52.414 23.117 C-52.45 22.066 -52.485 21.016 -52.522 19.933 C-52.556 18.594 -52.59 17.255 -52.625 15.875 C-52.749 11.626 -52.872 7.377 -53 3 C-35.84 3 -18.68 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#7F512D" transform="translate(849,169)"/>
<path d="M0 0 C1.084 -0.001 2.168 -0.003 3.285 -0.004 C4.429 -0 5.572 0.004 6.75 0.008 C7.862 0.004 8.975 0 10.121 -0.004 C16.415 0.004 22.51 0.3 28.75 1.133 C28.72 1.948 28.689 2.763 28.658 3.602 C27.789 28.259 27.661 52.902 27.688 77.57 C27.688 78.374 27.689 79.178 27.69 80.006 C27.702 90.048 27.722 100.09 27.75 110.133 C27.42 110.133 27.09 110.133 26.75 110.133 C25.965 96.092 25.611 82.112 25.652 68.051 C25.654 66.13 25.656 64.21 25.657 62.289 C25.66 57.313 25.67 52.337 25.681 47.361 C25.692 42.252 25.696 37.142 25.701 32.033 C25.712 22.066 25.729 12.1 25.75 2.133 C15.85 2.463 5.95 2.793 -4.25 3.133 C-12.3 20.444 -20.317 37.768 -28.25 55.133 C-29.599 51.085 -28.079 48.906 -26.348 45.254 C-26.035 44.573 -25.721 43.893 -25.399 43.192 C-24.379 40.98 -23.346 38.775 -22.312 36.57 C-21.615 35.065 -20.919 33.559 -20.224 32.053 C-19.191 29.815 -18.157 27.577 -17.12 25.341 C-14.137 18.913 -11.254 12.443 -8.422 5.947 C-5.813 0.007 -5.813 0.007 0 0 Z " fill="#AA6B31" transform="translate(531.25,118.8671875)"/>
<path d="M0 0 C8.25 0 16.5 0 25 0 C25 40.26 25 80.52 25 122 C16.75 122 8.5 122 0 122 C0 121.67 0 121.34 0 121 C7.92 121 15.84 121 24 121 C23.756 117.785 23.756 117.785 23.506 114.506 C22.758 103.315 22.875 92.116 22.902 80.906 C22.904 78.62 22.906 76.333 22.907 74.047 C22.91 68.083 22.92 62.119 22.931 56.154 C22.942 50.047 22.946 43.94 22.951 37.832 C22.962 25.888 22.979 13.944 23 2 C15.41 2 7.82 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#F69D37" transform="translate(694,121)"/>
<path d="M0 0 C3.505 0.865 5.528 1.432 7.871 4.25 C8.388 5.158 8.905 6.065 9.438 7 C12.711 12.531 16.267 17.77 20 23 C19.67 23.66 19.34 24.32 19 25 C17.828 23.546 16.662 22.087 15.5 20.625 C14.85 19.813 14.201 19.001 13.531 18.164 C12 16 12 16 12 14 C11.34 14 10.68 14 10 14 C10.33 15.65 10.66 17.3 11 19 C7.125 15.25 7.125 15.25 6 13 C5.409 21.011 4.859 28.962 5 37 C3.344 37.382 1.675 37.714 0 38 C-1.788 36.212 -1.128 33.804 -1.139 31.424 C-1.137 30.644 -1.135 29.865 -1.133 29.062 C-1.134 28.267 -1.135 27.472 -1.136 26.653 C-1.136 24.968 -1.135 23.283 -1.13 21.599 C-1.125 19.011 -1.13 16.424 -1.137 13.836 C-1.136 12.203 -1.135 10.57 -1.133 8.938 C-1.135 8.158 -1.137 7.379 -1.139 6.576 C-1.115 1.115 -1.115 1.115 0 0 Z " fill="#A39E95" transform="translate(449,689)"/>
<path d="M0 0 C5.622 0.572 10.744 1.607 16.125 3.312 C17.14 3.613 17.14 3.613 18.176 3.92 C22.661 5.321 26.165 7.209 30 10 C30.619 10.413 31.238 10.825 31.875 11.25 C33.852 14.325 33.681 17.544 33 21 C30.239 25.574 27.458 27.93 22.352 29.527 C11.061 31.797 -0.353 29.254 -10 23.062 C-13.337 20.648 -16.227 18.041 -19 15 C-23.253 16.676 -26.121 19.718 -29.312 22.875 C-29.875 23.405 -30.438 23.935 -31.018 24.48 C-32.828 26.208 -32.828 26.208 -35 29 C-34.021 36.055 -28.248 41.08 -22.875 45.188 C-18.042 48.552 -13.121 50.901 -7.629 52.992 C-5.33 53.874 -3.178 54.857 -1 56 C-12.314 55.774 -22.444 49.176 -30.523 41.699 C-32 40 -32 40 -32 38 C-33.485 38.495 -33.485 38.495 -35 39 C-35.495 36.525 -35.495 36.525 -36 34 C-36.99 33.67 -37.98 33.34 -39 33 C-37.606 28.566 -35.31 26.089 -32 23 C-30.411 21.443 -28.828 19.88 -27.25 18.312 C-26.471 17.546 -25.693 16.779 -24.891 15.988 C-22.948 14.139 -22.948 14.139 -22 12 C-20.68 12 -19.36 12 -18 12 C-18 12.66 -18 13.32 -18 14 C-17.34 14 -16.68 14 -16 14 C-16 14.66 -16 15.32 -16 16 C-14.68 16.33 -13.36 16.66 -12 17 C-12 17.66 -12 18.32 -12 19 C-1.059 25.6 8.569 29.642 21.574 26.781 C24.939 25.698 27.263 24.219 30 22 C29.505 20.02 29.505 20.02 29 18 C29.66 18.33 30.32 18.66 31 19 C30.754 15.399 30.546 13.54 27.934 10.957 C24.53 8.687 21.145 7.534 17.25 6.438 C15.828 6.016 14.406 5.594 12.984 5.172 C12.302 4.973 11.619 4.774 10.916 4.568 C7.244 3.479 3.627 2.23 0 1 C0 0.67 0 0.34 0 0 Z " fill="#905B35" transform="translate(307,364)"/>
<path d="M0 0 C2.812 0.438 2.812 0.438 6 2 C7.485 4.871 8.21 7.878 9 11 C9.392 12.176 9.784 13.351 10.188 14.562 C10.456 15.367 10.724 16.171 11 17 C10.34 17.33 9.68 17.66 9 18 C8.01 17.67 7.02 17.34 6 17 C6.33 18.65 6.66 20.3 7 22 C6.34 22 5.68 22 5 22 C5 26.95 5 31.9 5 37 C3.68 37.66 2.36 38.32 1 39 C0.34 38.67 -0.32 38.34 -1 38 C-1 37.34 -1 36.68 -1 36 C-1.99 36 -2.98 36 -4 36 C-3.67 35.01 -3.34 34.02 -3 33 C-2.34 33 -1.68 33 -1 33 C-1.005 32.036 -1.01 31.072 -1.016 30.078 C-1.034 26.513 -1.045 22.948 -1.055 19.383 C-1.06 17.838 -1.067 16.293 -1.075 14.747 C-1.088 12.531 -1.093 10.314 -1.098 8.098 C-1.103 7.403 -1.108 6.708 -1.113 5.992 C-1.113 4.328 -1.062 2.663 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F4EFE6" transform="translate(644,689)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C25.67 21.12 25.34 42.24 25 64 C24.67 64 24.34 64 24 64 C24 43.54 24 23.08 24 2 C16.41 2 8.82 2 1 2 C1 41.27 1 80.54 1 121 C9.25 121 17.5 121 26 121 C26 121.33 26 121.66 26 122 C17.42 122 8.84 122 0 122 C0 81.74 0 41.48 0 0 Z " fill="#F89F37" transform="translate(581,121)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 4.95 3 9.9 3 15 C9.27 15 15.54 15 22 15 C22 10.05 22 5.1 22 0 C22.99 0 23.98 0 25 0 C25 11.22 25 22.44 25 34 C24.01 34 23.02 34 22 34 C22 28.72 22 23.44 22 18 C15.73 18 9.46 18 3 18 C3 23.28 3 28.56 3 34 C2.01 34 1.02 34 0 34 C0 22.78 0 11.56 0 0 Z " fill="#F4EEE5" transform="translate(394,691)"/>
<path d="M0 0 C7.59 0 15.18 0 23 0 C22.67 1.65 22.34 3.3 22 5 C21.01 5 20.02 5 19 5 C18.67 5.99 18.34 6.98 18 8 C16.68 7.01 15.36 6.02 14 5 C14 12.92 14 20.84 14 29 C12.35 29.33 10.7 29.66 9 30 C9 22.41 9 14.82 9 7 C6.03 6.67 3.06 6.34 0 6 C-1 3 -1 3 0 0 Z " fill="#9C9890" transform="translate(343,784)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.469 1.436 0.938 1.871 0.391 2.32 C-8.859 10.119 -12.48 17.99 -13.812 29.938 C-14.473 41.16 -11.86 51.243 -4.367 59.883 C1.351 65.175 8.658 67.741 16 70 C16 69.34 16 68.68 16 68 C16.906 68.046 17.812 68.093 18.746 68.141 C31.977 68.533 39.336 64.963 49 56 C50.366 54.36 51.709 52.699 53 51 C56.881 52.312 59.148 53.83 61.938 56.812 C65.134 60.142 68.398 63.118 72 66 C68.534 66 68.142 65.574 65.645 63.375 C65.023 62.834 64.401 62.292 63.76 61.734 C63.117 61.162 62.475 60.59 61.812 60 C61.158 59.428 60.504 58.855 59.83 58.266 C58.214 56.85 56.606 55.426 55 54 C54.544 54.391 54.088 54.782 53.618 55.185 C52.353 56.269 51.088 57.353 49.822 58.437 C48.454 59.61 47.093 60.792 45.734 61.977 C35.446 70.523 25 72.087 12 71 C2.677 69.493 -4.33 63.597 -9.875 56.188 C-16.602 45.793 -17.593 34.58 -15.344 22.465 C-12.779 13.014 -8.108 5.651 0 0 Z M17 69 C17 69.33 17 69.66 17 70 C18.65 70 20.3 70 22 70 C22 69.67 22 69.34 22 69 C20.35 69 18.7 69 17 69 Z " fill="#945E31" transform="translate(216,148)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C3.7 9.9 0.4 19.8 -3 30 C-4.65 30 -6.3 30 -8 30 C-9.761 25.359 -11.465 20.722 -13 16 C-14.32 16 -15.64 16 -17 16 C-16.125 9.25 -16.125 9.25 -15 7 C-14.34 7 -13.68 7 -13 7 C-12.01 10.63 -11.02 14.26 -10 18 C-10.144 16.989 -10.289 15.979 -10.438 14.938 C-10.958 11.292 -11.479 7.646 -12 4 C-10.332 6.502 -9.376 8.411 -8.375 11.188 C-8.115 11.903 -7.854 12.618 -7.586 13.355 C-7.393 13.898 -7.199 14.441 -7 15 C-6.01 15 -5.02 15 -4 15 C-2.68 10.05 -1.36 5.1 0 0 Z M-15 10 C-14 13 -14 13 -14 13 Z M-7 17 C-6.34 18.32 -5.68 19.64 -5 21 C-5.33 19.68 -5.66 18.36 -6 17 C-6.33 17 -6.66 17 -7 17 Z " fill="#97928B" transform="translate(320,784)"/>
<path d="M0 0 C3.771 -0.116 7.541 -0.187 11.312 -0.25 C12.384 -0.284 13.456 -0.317 14.561 -0.352 C15.589 -0.364 16.617 -0.377 17.676 -0.391 C18.624 -0.412 19.572 -0.433 20.548 -0.454 C23 0 23 0 26 4 C22.37 4.66 18.74 5.32 15 6 C15.021 7.732 15.041 9.465 15.062 11.25 C15.07 17.543 14.621 23.741 14 30 C12.68 30 11.36 30 10 30 C8.728 23.32 6.923 13.154 10 7 C6.7 6.34 3.4 5.68 0 5 C0 3.35 0 1.7 0 0 Z " fill="#9C9890" transform="translate(494,784)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 8.25 5 16.5 5 25 C9.62 24.67 14.24 24.34 19 24 C19.33 25.65 19.66 27.3 20 29 C19 30 19 30 16.934 30.098 C16.11 30.086 15.286 30.074 14.438 30.062 C13.611 30.053 12.785 30.044 11.934 30.035 C10.976 30.018 10.976 30.018 10 30 C9.67 30.66 9.34 31.32 9 32 C9 31.34 9 30.68 9 30 C6.03 30 3.06 30 0 30 C-0.038 29.032 -0.038 29.032 -0.076 28.044 C-0.192 25.112 -0.315 22.181 -0.438 19.25 C-0.477 18.234 -0.516 17.218 -0.557 16.172 C-0.599 15.192 -0.64 14.212 -0.684 13.203 C-0.72 12.302 -0.757 11.402 -0.795 10.474 C-0.925 8.025 -0.925 8.025 -1.578 5.815 C-2.159 3.317 -1.295 2.133 0 0 Z " fill="#B6B1A9" transform="translate(604,784)"/>
<path d="M0 0 C-3 1 -3 1 -6.812 0.875 C-11.683 0.812 -15.058 2.393 -18.812 5.5 C-21.32 8.667 -21.108 11.832 -21.062 15.688 C-21.053 16.681 -21.044 17.675 -21.035 18.699 C-21.024 19.458 -21.012 20.218 -21 21 C-20.807 20.073 -20.613 19.146 -20.414 18.191 C-20.023 16.395 -20.023 16.395 -19.625 14.562 C-19.37 13.368 -19.115 12.173 -18.852 10.941 C-18.571 9.971 -18.29 9 -18 8 C-17.34 7.67 -16.68 7.34 -16 7 C-16.162 7.536 -16.325 8.072 -16.492 8.625 C-17.1 11.467 -17.132 14.095 -17.125 17 C-17.128 18.031 -17.13 19.062 -17.133 20.125 C-17.018 22.62 -16.842 24.657 -16 27 C-12.067 29.664 -7.841 29.164 -3.316 28.625 C-0.696 28.12 -0.696 28.12 1.625 25.438 C2.079 24.633 2.533 23.829 3 23 C2.65 26.242 2.438 27.615 -0.062 29.812 C-0.702 30.204 -1.341 30.596 -2 31 C-1.34 31.99 -0.68 32.98 0 34 C-4.791 36.036 -9.019 36.635 -14 35 C-19.297 31.855 -22.31 27.966 -24 22 C-24.72 15.715 -24.301 10.537 -21 5 C-17.858 1.326 -14.786 -1.201 -9.938 -2 C-6.103 -2 -3.507 -1.49 0 0 Z " fill="#B4AEA5" transform="translate(565,691)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.015 0.696 2.029 1.391 2.044 2.108 C2.119 5.281 2.216 8.453 2.312 11.625 C2.335 12.719 2.358 13.814 2.381 14.941 C2.574 20.726 2.794 25.101 6 30 C7.582 30.109 9.166 30.186 10.75 30.25 C11.632 30.296 12.513 30.343 13.422 30.391 C16.879 29.867 17.839 28.712 20 26 C20.638 23.111 20.638 23.111 20.609 19.922 C20.642 18.762 20.674 17.602 20.707 16.406 C20.728 14.596 20.728 14.596 20.75 12.75 C20.794 10.917 20.794 10.917 20.84 9.047 C20.911 6.031 20.963 3.016 21 0 C21.99 0 22.98 0 24 0 C24.087 4.229 24.14 8.458 24.188 12.688 C24.213 13.886 24.238 15.084 24.264 16.318 C24.273 17.475 24.283 18.632 24.293 19.824 C24.309 20.887 24.324 21.95 24.341 23.046 C23.84 27.386 22.168 30.003 18.875 32.812 C14.854 34.473 11.293 34.584 7 34 C3.365 32.376 2.226 31.339 0 28 C-0.227 25.089 -0.227 25.089 -0.195 21.582 C-0.189 20.328 -0.182 19.075 -0.176 17.783 C-0.159 16.473 -0.142 15.163 -0.125 13.812 C-0.115 12.477 -0.106 11.142 -0.098 9.807 C-0.074 6.538 -0.041 3.269 0 0 Z " fill="#EFE9DE" transform="translate(279,691)"/>
<path d="M0 0 C1.675 0.286 3.344 0.618 5 1 C6.08 4.448 6.038 7.482 5.879 11.078 C5.831 12.238 5.782 13.398 5.732 14.594 C5.676 15.8 5.62 17.007 5.562 18.25 C5.51 19.472 5.458 20.694 5.404 21.953 C5.275 24.969 5.14 27.985 5 31 C5.66 31 6.32 31 7 31 C7.33 32.98 7.66 34.96 8 37 C7.01 37 6.02 37 5 37 C4.67 37.66 4.34 38.32 4 39 C2.68 38.67 1.36 38.34 0 38 C-0.167 32.749 -0.328 27.498 -0.482 22.247 C-0.536 20.459 -0.591 18.671 -0.648 16.884 C-0.73 14.32 -0.805 11.756 -0.879 9.191 C-0.906 8.387 -0.933 7.583 -0.961 6.755 C-1.013 4.837 -1.012 2.918 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M5 33 C6 35 6 35 6 35 Z " fill="#F6F0E8" transform="translate(432,689)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.025 4.236 7.043 8.472 7.055 12.708 C7.06 14.149 7.067 15.59 7.075 17.032 C7.088 19.102 7.093 21.172 7.098 23.242 C7.103 24.489 7.108 25.735 7.114 27.019 C7 30 7 30 6 32 C4.68 32 3.36 32 2 32 C0.143 28.051 -0.237 24.952 -0.195 20.605 C-0.189 19.403 -0.182 18.201 -0.176 16.963 C-0.159 15.717 -0.142 14.471 -0.125 13.188 C-0.111 11.29 -0.111 11.29 -0.098 9.354 C-0.074 6.235 -0.041 3.118 0 0 Z M1 14 C2 17 2 17 2 17 Z M1 23 C2 25 2 25 2 25 Z " fill="#A29D96" transform="translate(403,784)"/>
<path d="M0 0 C7.59 0 15.18 0 23 0 C23 36.96 23 73.92 23 112 C22.67 112 22.34 112 22 112 C21.867 102.808 21.736 93.617 21.609 84.425 C21.55 80.153 21.489 75.881 21.427 71.608 C21.095 48.737 20.933 25.874 21 3 C14.73 2.67 8.46 2.34 2 2 C1.67 3.65 1.34 5.3 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#F79F38" transform="translate(721,297)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 0.33 19 0.66 19 1 C13.06 1 7.12 1 1 1 C1.33 1.66 1.66 2.32 2 3 C2.308 5.331 2.509 7.662 2.719 10.004 C2.795 11.996 2.795 11.996 4 13 C5.918 12.915 7.834 12.807 9.75 12.688 C15.389 12.641 15.389 12.641 17.906 14.121 C20.081 16.555 20.967 18.825 21.562 22 C20.851 25.792 19.39 27.996 17 31 C12.75 33.834 8.988 33.413 4 33 C0.875 32 0.875 32 -1 31 C-0.67 30.01 -0.34 29.02 0 28 C1.237 28.526 1.237 28.526 2.5 29.062 C6.834 30.223 9.609 29.757 14 29 C15.767 24.37 15.767 24.37 15.25 19.625 C14.078 17.857 14.078 17.857 12 17 C12 16.34 12 15.68 12 15 C8.04 15.66 4.08 16.32 0 17 C-1.215 10.926 -0.609 6.153 0 0 Z " fill="#EEE8DE" transform="translate(800,692)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 3 -0.32 3 -1 3 C-1.109 5.228 -1.186 7.458 -1.25 9.688 C-1.296 10.929 -1.343 12.17 -1.391 13.449 C-0.937 17.57 0.103 19.108 3 22 C5.765 23.382 8.046 23.095 11.125 23.062 C12.221 23.053 13.316 23.044 14.445 23.035 C15.288 23.024 16.131 23.012 17 23 C16.67 21.35 16.34 19.7 16 18 C16.66 17.67 17.32 17.34 18 17 C14.7 16.34 11.4 15.68 8 15 C8 13.35 8 11.7 8 10 C12.95 9.34 17.9 8.68 23 8 C23.773 12.64 24.066 15.771 23.688 20.312 C23.57 21.885 23.57 21.885 23.449 23.488 C23.227 24.732 23.227 24.732 23 26 C22.34 26.33 21.68 26.66 21 27 C21 27.99 21 28.98 21 30 C19.68 30 18.36 30 17 30 C15.937 30.017 15.937 30.017 14.852 30.035 C10.422 30.09 6.378 29.747 2 29 C1.34 29.33 0.68 29.66 0 30 C-0.248 29.423 -0.495 28.845 -0.75 28.25 C-2.167 25.7 -3.876 23.982 -6 22 C-5.67 21.01 -5.34 20.02 -5 19 C-4.752 19.784 -4.505 20.567 -4.25 21.375 C-2.819 24.38 -2.053 24.89 1 26 C7.98 28.327 15.101 27.044 22 25 C22 20.38 22 15.76 22 11 C16.06 11.495 16.06 11.495 10 12 C10 12.33 10 12.66 10 13 C12.97 13.33 15.94 13.66 19 14 C19 16.97 19 19.94 19 23 C14.518 25.873 11.315 26.446 6.184 25.633 C2.366 24.527 0.667 22.992 -2 20 C-4.372 14.636 -4.369 9.627 -3 4 C-1.438 1.438 -1.438 1.438 0 0 Z " fill="#A49D94" transform="translate(360,697)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C5.67 2.66 5.34 3.32 5 4 C2.525 3.505 2.525 3.505 0 3 C-0.235 4.226 -0.469 5.452 -0.711 6.715 C-2.082 12.885 -4.354 18.676 -6.625 24.562 C-7.045 25.667 -7.465 26.771 -7.898 27.908 C-8.927 30.608 -9.961 33.305 -11 36 C-10.34 36 -9.68 36 -9 36 C-8.01 33.03 -7.02 30.06 -6 27 C-0.39 27 5.22 27 11 27 C14 34 14 34 14 37 C15.485 36.505 15.485 36.505 17 36 C16.627 35.096 16.255 34.193 15.871 33.262 C15.398 32.082 14.925 30.903 14.438 29.688 C13.962 28.516 13.486 27.344 12.996 26.137 C12 23 12 23 13 20 C13.103 20.639 13.206 21.279 13.312 21.938 C13.539 22.618 13.766 23.299 14 24 C14.99 24.33 15.98 24.66 17 25 C16.34 25 15.68 25 15 25 C16.65 28.63 18.3 32.26 20 36 C20.66 36 21.32 36 22 36 C22 36.66 22 37.32 22 38 C18.671 39.11 16.284 39.347 13 38 C11.965 35.682 10.964 33.348 10 31 C7.984 29.867 7.984 29.867 6 30 C5.67 30.33 5.34 30.66 5 31 C-1.395 30.302 -1.395 30.302 -4 29 C-4.231 29.626 -4.461 30.253 -4.699 30.898 C-7.396 37.657 -7.396 37.657 -10.75 39.562 C-11.492 39.707 -12.235 39.851 -13 40 C-14.097 36.71 -13.8 35.287 -13 32 C-12.34 32 -11.68 32 -11 32 C-10.876 31.45 -10.753 30.899 -10.625 30.332 C-8.037 19.9 -3.913 9.99 0 0 Z " fill="#A59F96" transform="translate(698,688)"/>
<path d="M0 0 C2.318 1.891 3.669 3.338 5 6 C5.045 7.687 5.039 9.375 5 11.062 C4.962 12.708 4.954 14.355 5 16 C5.33 16.33 5.66 16.66 6 17 C6.027 15.083 6.046 13.167 6.062 11.25 C6.074 10.183 6.086 9.115 6.098 8.016 C6.015 5.466 5.746 3.421 5 1 C7.71 3.474 8.838 5.336 9.203 9.086 C9.302 13.913 9.206 17.13 6 21 C0.274 26.028 0.274 26.028 -3.348 25.949 C-4.244 25.863 -5.139 25.776 -6.062 25.688 C-7.41 25.57 -7.41 25.57 -8.785 25.449 C-9.516 25.301 -10.247 25.153 -11 25 C-11.33 24.34 -11.66 23.68 -12 23 C-11.01 23 -10.02 23 -9 23 C-9.825 22.258 -10.65 21.515 -11.5 20.75 C-14.825 16.781 -14.641 12.787 -14.43 7.789 C-13.874 4.184 -12.808 2.327 -10 0 C-6.194 -1.269 -3.894 -0.888 0 0 Z M-10.688 2.875 C-13.514 6.694 -13.74 8.965 -13.477 13.617 C-13.097 16.265 -13.097 16.265 -11 19.125 C-6.875 21.703 -4.81 21.621 0 21 C2.205 19.392 2.898 18.375 3.617 15.73 C4.174 10.243 4.156 6.797 1 2 C-0.65 2.33 -2.3 2.66 -4 3 C-4.33 2.34 -4.66 1.68 -5 1 C-8.136 0.714 -8.136 0.714 -10.688 2.875 Z " fill="#ADA8A1" transform="translate(388,788)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.34 1 2.68 1 2 1 C2 24.76 2 48.52 2 73 C-2.233 68.767 -4.545 63.618 -7.263 58.348 C-8.251 56.436 -9.254 54.531 -10.258 52.627 C-10.886 51.417 -11.514 50.207 -12.141 48.996 C-12.713 47.895 -13.286 46.793 -13.876 45.658 C-14.247 44.781 -14.618 43.904 -15 43 C-14.67 42.34 -14.34 41.68 -14 41 C-9.71 48.59 -5.42 56.18 -1 64 C-1 59.71 -1 55.42 -1 51 C-0.67 50.01 -0.34 49.02 0 48 C0.087 46.42 0.118 44.836 0.114 43.253 C0.113 42.292 0.113 41.332 0.113 40.342 C0.108 39.307 0.103 38.273 0.098 37.207 C0.096 35.615 0.096 35.615 0.093 33.991 C0.088 30.598 0.075 27.205 0.062 23.812 C0.057 21.514 0.053 19.215 0.049 16.916 C0.038 11.277 0.021 5.639 0 0 Z M-1 65 C-0.34 66.32 0.32 67.64 1 69 C0.67 67.68 0.34 66.36 0 65 C-0.33 65 -0.66 65 -1 65 Z " fill="#9D6132" transform="translate(719,296)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C1.039 2.291 2.078 2.583 3.148 2.883 C11.535 5.318 18.71 8.057 26 13 C26.99 13.495 26.99 13.495 28 14 C28.989 15.077 29.944 16.186 30.875 17.312 C33.138 20.008 35.46 22.558 38 25 C37.67 25.66 37.34 26.32 37 27 C36.053 25.948 36.053 25.948 35.086 24.875 C26.553 15.826 16.882 7.88 4.086 6.352 C2 6 2 6 0.205 4.999 C-4.16 3.022 -9.159 3.618 -13.875 3.625 C-15.001 3.627 -16.127 3.628 -17.287 3.63 C-29.187 3.805 -38.454 6.524 -49 12 C-49.66 11.67 -50.32 11.34 -51 11 C-34.895 1.235 -18.503 -0.121 0 0 Z M-7 1 C-7 1.33 -7 1.66 -7 2 C-5.35 2 -3.7 2 -2 2 C-2 1.67 -2 1.34 -2 1 C-3.65 1 -5.3 1 -7 1 Z " fill="#A46730" transform="translate(587,291)"/>
<path d="M0 0 C0.71 0.545 0.71 0.545 1.434 1.102 C3.608 2.755 5.799 4.382 8 6 C8.697 6.514 9.395 7.029 10.113 7.559 C26.692 19.227 45.612 21.125 65.361 18.711 C74.362 17.07 82.179 12.523 90 8 C91.327 7.321 92.658 6.649 94 6 C86.728 14.526 74.904 19.385 64.113 21.57 C62.028 21.967 62.028 21.967 60.126 22.528 C43.414 26.24 24.439 19.261 10.441 10.652 C6.69 8.113 3.079 5.337 0 2 C0 1.34 0 0.68 0 0 Z " fill="#805331" transform="translate(186,225)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C2.107 0.722 1.778 1.383 1.438 2.062 C1.438 5.799 2.657 8.799 3.938 12.25 C4.176 12.904 4.414 13.558 4.66 14.232 C5.248 15.844 5.842 17.453 6.438 19.062 C2.148 19.062 -2.143 19.062 -6.562 19.062 C-6.562 17.742 -6.562 16.423 -6.562 15.062 C-8.212 19.352 -9.862 23.643 -11.562 28.062 C-10.903 28.062 -10.242 28.062 -9.562 28.062 C-8.903 26.082 -8.242 24.102 -7.562 22.062 C-6.403 25.541 -6.854 27.521 -7.562 31.062 C-12.562 31.062 -12.562 31.062 -15.562 29.062 C-14.562 26.062 -14.562 26.062 -12.562 24.062 C-11.8 22.355 -11.099 20.619 -10.434 18.871 C-10.038 17.844 -9.643 16.818 -9.236 15.76 C-8.828 14.684 -8.42 13.608 -8 12.5 C-7.188 10.373 -6.375 8.246 -5.559 6.121 C-5.2 5.175 -4.84 4.23 -4.47 3.256 C-3.154 0.077 -3.154 0.077 0 0 Z M-0.562 7.062 C-1.883 10.362 -3.202 13.663 -4.562 17.062 C-3.139 17.032 -3.139 17.032 -1.688 17 C1.438 17.062 1.438 17.062 3.438 18.062 C2.886 14.113 2.048 10.713 0.438 7.062 C0.107 7.062 -0.222 7.062 -0.562 7.062 Z " fill="#B6B1A8" transform="translate(743.5625,783.9375)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 10.23 3 20.46 3 31 C8.94 31 14.88 31 21 31 C21 31.99 21 32.98 21 34 C14.07 34 7.14 34 0 34 C0 22.78 0 11.56 0 0 Z " fill="#EFEAE1" transform="translate(212,691)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C12.108 2.103 12.186 4.208 12.25 6.312 C12.296 7.484 12.343 8.656 12.391 9.863 C12 13 12 13 10.172 14.855 C5.202 17.474 0.495 17.471 -5 17 C-9.704 15.345 -12.346 13.226 -15 9 C-15.75 5.688 -15.75 5.688 -16 3 C-11.943 4.352 -11.366 6.475 -9.379 10.09 C-7.768 12.322 -6.563 13.05 -4 14 C0.567 14.332 3.871 14.002 8 12 C8.33 9.03 8.66 6.06 9 3 C6.03 3 3.06 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#F4EFE5" transform="translate(504,708)"/>
<path d="M0 0 C4.609 0.72 8.672 2.313 13 4 C13.66 3.34 14.32 2.68 15 2 C17 5 17 5 16.875 8.5 C16 12 16 12 14.375 13.875 C9.292 16.283 5.315 16.305 0.062 14.562 C-1.297 14.056 -2.654 13.54 -4 13 C-3.67 11.02 -3.34 9.04 -3 7 C-1 8 -1 8 2 10 C4.057 10.241 4.057 10.241 6.188 10.125 C7.446 10.084 8.704 10.043 10 10 C10.33 8.68 10.66 7.36 11 6 C10.29 5.734 9.579 5.469 8.848 5.195 C7.469 4.666 7.469 4.666 6.062 4.125 C5.146 3.777 4.229 3.429 3.285 3.07 C1 2 1 2 0 0 Z " fill="#A7A19A" transform="translate(234,798)"/>
<path d="M0 0 C0 3.239 -0.677 4.924 -1.938 7.875 C-6.558 18.705 -5.982 30 -2 41 C1.949 49.022 7.673 53.551 15.312 57.875 C19.493 59.15 22.93 59.171 27.266 59.039 C30.931 58.987 34.38 59.454 38 60 C32.457 63.305 25.703 62.697 19.531 61.535 C8.671 58.304 1.15 51.404 -4.32 41.645 C-8.528 32.156 -8.693 20.11 -5.941 10.18 C-4.36 6.284 -2.989 2.989 0 0 Z " fill="#945D30" transform="translate(333,157)"/>
<path d="M0 0 C0.749 0.005 1.498 0.01 2.269 0.016 C3.078 0.019 3.887 0.022 4.72 0.026 C5.571 0.034 6.421 0.042 7.297 0.051 C8.151 0.056 9.005 0.06 9.885 0.065 C12.001 0.077 14.118 0.094 16.234 0.114 C16.234 0.774 16.234 1.434 16.234 2.114 C17.039 2.897 17.843 3.681 18.672 4.489 C21.152 7.029 21.645 7.816 22.234 11.114 C22.894 11.444 23.554 11.774 24.234 12.114 C23.337 18.885 22.264 23.335 17.234 28.114 C16.574 28.114 15.914 28.114 15.234 28.114 C14.904 28.774 14.574 29.434 14.234 30.114 C9.284 30.114 4.334 30.114 -0.766 30.114 C-1.891 23.364 -1.891 23.364 -1.645 20.028 C-1.596 19.325 -1.548 18.623 -1.498 17.899 C-1.442 17.186 -1.386 16.473 -1.328 15.739 C-1.276 15.002 -1.224 14.266 -1.17 13.508 C-1.042 11.709 -0.905 9.911 -0.766 8.114 C-1.426 8.114 -2.086 8.114 -2.766 8.114 C-2.26 5.945 -1.766 4.114 -0.766 2.114 C-1.426 1.784 -2.086 1.454 -2.766 1.114 C-1.766 0.114 -1.766 0.114 0 0 Z M0.234 1.114 C0.234 10.024 0.234 18.934 0.234 28.114 C2.441 28.196 4.648 28.279 6.922 28.364 C8.163 28.41 9.404 28.456 10.683 28.504 C14.832 28.048 16.322 27.031 19.234 24.114 C22.351 19.438 21.819 14.587 21.234 9.114 C19.335 5.392 17.703 4.385 14.109 2.301 C9.316 0.625 9.316 0.625 0.234 1.114 Z " fill="#98948D" transform="translate(450.765869140625,783.886474609375)"/>
<path d="M0 0 C2.148 3.222 2.258 4.119 2.291 7.835 C2.31 9.307 2.31 9.307 2.329 10.809 C2.331 11.869 2.334 12.928 2.336 14.02 C2.343 15.111 2.349 16.202 2.356 17.327 C2.366 19.637 2.371 21.948 2.371 24.259 C2.375 27.788 2.411 31.317 2.449 34.846 C2.455 37.091 2.459 39.337 2.461 41.582 C2.475 42.635 2.49 43.689 2.504 44.774 C2.459 52.174 1.224 55.406 -3.703 60.898 C-4.469 61.629 -4.469 61.629 -5.25 62.375 C-6.158 63.241 -7.065 64.108 -8 65 C-8.66 64.67 -9.32 64.34 -10 64 C-9.151 63.169 -9.151 63.169 -8.285 62.32 C-7.552 61.596 -6.818 60.871 -6.062 60.125 C-5.332 59.406 -4.601 58.686 -3.848 57.945 C-1.021 54.97 -0.865 53.186 -0.795 49.183 C-0.775 48.182 -0.755 47.181 -0.734 46.15 C-0.718 45.067 -0.701 43.983 -0.684 42.867 C-0.663 41.76 -0.642 40.653 -0.621 39.513 C-0.555 35.967 -0.496 32.421 -0.438 28.875 C-0.394 26.475 -0.351 24.076 -0.307 21.676 C-0.199 15.784 -0.098 9.892 0 4 C-2.64 4 -5.28 4 -8 4 C-8 3.67 -8 3.34 -8 3 C-5.69 3 -3.38 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#8E5E35" transform="translate(849,169)"/>
<path d="M0 0 C6.465 0.61 6.465 0.61 8.73 2.262 C10.82 5.122 10.148 7.544 9.629 10.902 C9 13 9 13 6.938 15.062 C2.545 16.464 -1.623 17.161 -5.848 15.098 C-7.5 14 -7.5 14 -10 12 C-9.34 10.68 -8.68 9.36 -8 8 C-7.438 8.327 -6.876 8.655 -6.297 8.992 C-3.731 10.118 -2.032 10.207 0.75 10.125 C1.949 10.098 1.949 10.098 3.172 10.07 C4.077 10.036 4.077 10.036 5 10 C5.33 8.35 5.66 6.7 6 5 C5.34 5 4.68 5 4 5 C4 4.34 4 3.68 4 3 C3.34 3 2.68 3 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#B0ACA4" transform="translate(771,798)"/>
<path d="M0 0 C0.913 0.242 1.825 0.485 2.766 0.734 C9.667 2.667 9.667 2.667 12 5 C11.022 4.801 11.022 4.801 10.023 4.598 C9.149 4.421 8.275 4.244 7.375 4.062 C6.516 3.888 5.658 3.714 4.773 3.535 C3.858 3.359 2.943 3.182 2 3 C1.138 2.82 0.275 2.639 -0.613 2.453 C-13.989 0.664 -28.522 0.462 -41 6 C-41.873 6.351 -42.745 6.701 -43.645 7.062 C-49.619 9.706 -54.066 13.421 -58.773 17.914 C-60.764 19.779 -62.786 21.413 -65 23 C-60.064 11.931 -48.793 6.142 -38 2 C-25.777 -1.778 -12.433 -3.494 0 0 Z " fill="#A26531" transform="translate(452,293)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.039 1.555 1.039 1.555 1.078 3.141 C1.134 4.51 1.192 5.88 1.25 7.25 C1.264 7.932 1.278 8.614 1.293 9.316 C1.454 12.629 1.803 14.715 3.703 17.465 C6.536 19.358 8.12 19.447 11.5 19.312 C12.438 19.288 13.377 19.264 14.344 19.238 C17.253 19.046 17.253 19.046 21 18 C21 14.37 21 10.74 21 7 C17.7 7 14.4 7 11 7 C11 7.66 11 8.32 11 9 C13.64 9 16.28 9 19 9 C19 11.64 19 14.28 19 17 C17.68 16.67 16.36 16.34 15 16 C15 15.34 15 14.68 15 14 C15.66 13.67 16.32 13.34 17 13 C17.33 12.01 17.66 11.02 18 10 C17.072 10.227 16.144 10.454 15.188 10.688 C12 11 12 11 10.125 9.688 C9 8 9 8 9 6 C15.75 3 15.75 3 18 3 C18 3.66 18 4.32 18 5 C20.475 5.495 20.475 5.495 23 6 C23.626 16.093 23.626 16.093 21.188 19.625 C17.105 22.191 13.739 22.316 9 22 C4.936 20.814 2.417 19.625 0 16 C-0.598 14.062 -0.598 14.062 -1.062 12 C-1.614 9.595 -2.217 7.349 -3 5 C-1.625 2.312 -1.625 2.312 0 0 Z " fill="#9C9890" transform="translate(662,792)"/>
<path d="M0 0 C8.996 8.003 12.069 21.417 13 33 C13.107 36.356 13.096 39.705 13.062 43.062 C13.058 43.928 13.053 44.794 13.049 45.686 C13.037 47.79 13.019 49.895 13 52 C12.34 52 11.68 52 11 52 C10.979 53.196 10.959 54.392 10.938 55.625 C10.209 62.739 6.366 68.822 3 75 C2.67 74.34 2.34 73.68 2 73 C2.71 71.409 3.496 69.852 4.312 68.312 C7.642 61.358 8.312 54.609 9 47 C9.66 47 10.32 47 11 47 C10.526 31.366 8.861 18.749 1.426 4.836 C0 2 0 2 0 0 Z " fill="#925C2F" transform="translate(625,318)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 40.92 1 81.84 1 124 C-0.98 124.495 -0.98 124.495 -3 125 C-2.67 123.35 -2.34 121.7 -2 120 C-1.67 120 -1.34 120 -1 120 C-0.505 60.6 -0.505 60.6 0 0 Z " fill="#D28436" transform="translate(579,120)"/>
<path d="M0 0 C2.658 2.374 4.107 4.821 5.715 7.988 C6.23 8.994 6.746 10 7.277 11.036 C7.825 12.117 8.373 13.198 8.938 14.312 C13.307 22.835 17.753 31.3 22.375 39.688 C26.783 47.701 31.011 55.77 35 64 C35.99 64 36.98 64 38 64 C38.495 63.01 38.495 63.01 39 62 C39 64.97 39 67.94 39 71 C34.351 68.676 33.263 66.36 31.094 61.922 C30.732 61.207 30.37 60.492 29.998 59.755 C29.223 58.221 28.454 56.685 27.69 55.145 C25.697 51.134 23.668 47.141 21.645 43.145 C21.245 42.354 20.846 41.563 20.435 40.748 C16.712 33.385 12.799 26.14 8.793 18.927 C0 3.061 0 3.061 0 0 Z M36 65 C37 67 37 67 37 67 Z " fill="#9A612D" transform="translate(656,122)"/>
<path d="M0 0 C2.363 2.363 3.038 4.312 4.188 7.438 C4.552 8.406 4.917 9.374 5.293 10.371 C6 13 6 13 5 16 C5 15.34 5 14.68 5 14 C4.34 14 3.68 14 3 14 C3 15.98 3 17.96 3 20 C-1.29 20 -5.58 20 -10 20 C-7.366 10.72 -7.366 10.72 -5.25 6.562 C-3.619 3.977 -3.619 3.977 -5 1 C-2.625 0.375 -2.625 0.375 0 0 Z M-4 6 C-6.483 12.36 -6.483 12.36 -8 19 C-5.36 19 -2.72 19 0 19 C1.033 16.212 1.045 15.132 0.062 12.25 C-0.288 11.508 -0.639 10.765 -1 10 C-1.99 10.495 -1.99 10.495 -3 11 C-3.33 10.34 -3.66 9.68 -4 9 C-3.34 8.67 -2.68 8.34 -2 8 C-2.66 7.34 -3.32 6.68 -4 6 Z " fill="#BDB7AE" transform="translate(704,692)"/>
<path d="M0 0 C-0.99 0.495 -0.99 0.495 -2 1 C-1.649 1.763 -1.299 2.526 -0.938 3.312 C0 6 0 6 -1 9 C-1.268 8.196 -1.536 7.391 -1.812 6.562 C-2.204 5.717 -2.596 4.871 -3 4 C-7.93 2.357 -12.314 2.65 -17.312 3.688 C-21.303 7.121 -21.488 11.105 -22.359 16.164 C-22.571 17.1 -22.782 18.036 -23 19 C-23.66 19.33 -24.32 19.66 -25 20 C-25.082 18.105 -25.139 16.209 -25.188 14.312 C-25.222 13.257 -25.257 12.201 -25.293 11.113 C-24.847 6.375 -22.98 3.882 -19.5 0.75 C-13.401 -3.519 -6.597 -2.639 0 0 Z " fill="#D8D1C7" transform="translate(568,693)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5.33 8.92 5.66 16.84 6 25 C6.33 25 6.66 25 7 25 C7 29.29 7 33.58 7 38 C3 40 3 40 0 40 C-1.754 37.77 -1.754 37.77 -3.562 34.812 C-4.162 33.85 -4.761 32.887 -5.379 31.895 C-6.83 29.304 -7.978 26.782 -9 24 C-6.5 26.5 -6.5 26.5 -4 29 C-3.237 29.681 -2.474 30.361 -1.688 31.062 C0 33 0 33 0 37 C1.32 37 2.64 37 4 37 C4 25.78 4 14.56 4 3 C3.01 3 2.02 3 1 3 C1 12.24 1 21.48 1 31 C-1 29 -1 29 -1.243 26.326 C-1.239 25.218 -1.235 24.11 -1.23 22.969 C-1.229 21.766 -1.227 20.563 -1.225 19.324 C-1.212 18.062 -1.2 16.8 -1.188 15.5 C-1.187 14.238 -1.186 12.976 -1.186 11.676 C-1.14 2.28 -1.14 2.28 0 0 Z " fill="#87837A" transform="translate(606,688)"/>
<path d="M0 0 C2.339 0.287 4.674 0.619 7 1 C6.838 1.538 6.675 2.075 6.508 2.629 C5.903 5.451 5.87 8.051 5.867 10.938 C5.866 12.08 5.865 13.222 5.863 14.398 C5.867 15.587 5.871 16.775 5.875 18 C5.871 19.189 5.867 20.377 5.863 21.602 C5.865 23.315 5.865 23.315 5.867 25.062 C5.869 26.627 5.869 26.627 5.871 28.223 C5.985 30.668 6.365 32.655 7 35 C7 35.99 7 36.98 7 38 C4.69 38 2.38 38 0 38 C-0.66 36.68 -1.32 35.36 -2 34 C-0.02 34.66 1.96 35.32 4 36 C4 24.78 4 13.56 4 2 C3.01 2 2.02 2 1 2 C1 10.91 1 19.82 1 29 C0.34 28.67 -0.32 28.34 -1 28 C-2.631 21.872 -1.98 15.202 -1.625 8.938 C-1.582 8.175 -1.539 7.413 -1.495 6.627 C-1.131 1.131 -1.131 1.131 0 0 Z " fill="#A29E97" transform="translate(472,689)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C5.67 2.99 5.34 3.98 5 5 C4.67 4.34 4.34 3.68 4 3 C2.68 2.67 1.36 2.34 0 2 C0 13.22 0 24.44 0 36 C0.99 36 1.98 36 3 36 C3 27.09 3 18.18 3 9 C7.5 13.5 7.5 13.5 8 17 C7.34 17 6.68 17 6 17 C6.33 18.65 6.66 20.3 7 22 C6.34 22 5.68 22 5 22 C5 26.95 5 31.9 5 37 C3.68 37.66 2.36 38.32 1 39 C0.34 38.67 -0.32 38.34 -1 38 C-1 37.34 -1 36.68 -1 36 C-1.99 36 -2.98 36 -4 36 C-3.67 35.01 -3.34 34.02 -3 33 C-2.34 33 -1.68 33 -1 33 C-1.005 32.036 -1.01 31.072 -1.016 30.078 C-1.034 26.513 -1.045 22.948 -1.055 19.383 C-1.06 17.838 -1.067 16.293 -1.075 14.747 C-1.088 12.531 -1.093 10.314 -1.098 8.098 C-1.103 7.403 -1.108 6.708 -1.113 5.992 C-1.113 4.328 -1.062 2.663 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9B968D" transform="translate(644,689)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C3.527 2.656 3.527 2.656 6 3 C5.67 3.66 5.34 4.32 5 5 C3.68 4.34 2.36 3.68 1 3 C1 14.22 1 25.44 1 37 C1.99 37 2.98 37 4 37 C3.67 27.43 3.34 17.86 3 8 C3.99 8.66 4.98 9.32 6 10 C6.249 12.889 6.249 12.889 6.074 16.539 C6.019 17.841 5.965 19.143 5.908 20.484 C5.835 21.865 5.761 23.245 5.688 24.625 C5.624 26.013 5.562 27.401 5.502 28.789 C5.348 32.194 5.18 35.597 5 39 C2.525 39.495 2.525 39.495 0 40 C-1.262 37.475 -1.099 35.688 -1.062 32.875 C-1.053 31.965 -1.044 31.055 -1.035 30.117 C-1.024 29.419 -1.012 28.72 -1 28 C-1.66 27.67 -2.32 27.34 -3 27 C-2.34 27 -1.68 27 -1 27 C-1.012 25.94 -1.023 24.881 -1.035 23.789 C-1.045 22.401 -1.054 21.013 -1.062 19.625 C-1.071 18.926 -1.079 18.228 -1.088 17.508 C-1.113 12.227 -1.113 12.227 0 10 C-0.66 9.67 -1.32 9.34 -2 9 C-1.34 6.03 -0.68 3.06 0 0 Z " fill="#A7A299" transform="translate(316,688)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.074 5.09 6.129 10.18 6.165 15.271 C6.18 17.003 6.2 18.736 6.226 20.468 C6.263 22.955 6.28 25.442 6.293 27.93 C6.308 28.707 6.324 29.484 6.34 30.285 C6.341 32.468 6.341 32.468 6 36 C5.01 36.66 4.02 37.32 3 38 C0.5 35.688 0.5 35.688 -2 33 C-2 32.01 -2 31.02 -2 30 C-1.567 30.639 -1.134 31.279 -0.688 31.938 C0.912 34.267 0.912 34.267 4 35 C4 23.78 4 12.56 4 1 C3.01 1 2.02 1 1 1 C1 9.91 1 18.82 1 28 C-1 26 -1 26 -1.135 23.386 C-1.083 22.307 -1.031 21.229 -0.977 20.117 C-0.925 18.951 -0.873 17.784 -0.82 16.582 C-0.756 15.359 -0.691 14.135 -0.625 12.875 C-0.568 11.644 -0.512 10.413 -0.453 9.145 C-0.311 6.096 -0.16 3.048 0 0 Z " fill="#9D988E" transform="translate(338,690)"/>
<path d="M0 0 C2.496 2.276 4.066 4.607 5.688 7.562 C6.124 8.348 6.561 9.133 7.012 9.941 C8 12 8 12 8 14 C8.66 14.33 9.32 14.66 10 15 C10 15.66 10 16.32 10 17 C10.99 15.02 11.98 13.04 13 11 C13.66 11 14.32 11 15 11 C15 10.34 15 9.68 15 9 C15.99 9.33 16.98 9.66 18 10 C16.02 14.62 14.04 19.24 12 24 C10.68 24 9.36 24 8 24 C5 18.25 5 18.25 5 16 C4.34 16 3.68 16 3 16 C2.67 14.68 2.34 13.36 2 12 C2.66 12 3.32 12 4 12 C3.567 10.701 3.567 10.701 3.125 9.375 C2.083 6.25 1.042 3.125 0 0 Z " fill="#DFD9CF" transform="translate(650,694)"/>
<path d="M0 0 C10.681 -0.704 10.681 -0.704 13.91 1.02 C16.61 3.877 17.323 6.784 17.5 10.562 C17.245 14.447 16.692 17.048 14 20 C8.757 22.924 6.291 23 0 23 C0 15.41 0 7.82 0 0 Z M2 1 C2 7.6 2 14.2 2 21 C9.377 21.626 9.377 21.626 12.938 19.438 C15.564 16.334 15.769 13.999 16 10 C15.01 10.33 14.02 10.66 13 11 C13.66 10.34 14.32 9.68 15 9 C14.841 5.895 14.425 4.44 12.25 2.188 C9.578 0.777 7.982 0.608 5 1 C4.34 1.66 3.68 2.32 3 3 C2.67 2.34 2.34 1.68 2 1 Z " fill="#9B968F" transform="translate(453,788)"/>
<path d="M0 0 C7.26 0 14.52 0 22 0 C22 3 22 3 21 5 C18.525 3.515 18.525 3.515 16 2 C12.04 1.67 8.08 1.34 4 1 C4 10.24 4 19.48 4 29 C4.66 29 5.32 29 6 29 C6 25.37 6 21.74 6 18 C11.373 17.648 11.373 17.648 14 18 C16 20 16 20 17 22 C16.67 22.66 16.34 23.32 16 24 C15.381 23.01 15.381 23.01 14.75 22 C14.173 21.34 13.595 20.68 13 20 C11.68 20 10.36 20 9 20 C8.67 23.3 8.34 26.6 8 30 C6.35 30.66 4.7 31.32 3 32 C0.875 25.375 0.875 25.375 2 22 C2.226 19.607 2.408 17.211 2.562 14.812 C2.646 13.54 2.73 12.268 2.816 10.957 C2.877 9.981 2.938 9.005 3 8 C2.34 7.67 1.68 7.34 1 7 C1.206 6.216 1.413 5.433 1.625 4.625 C1.749 3.759 1.873 2.893 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#9B9690" transform="translate(417,784)"/>
<path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.062 C3.496 3.271 3.531 6.479 3.562 9.688 C3.579 10.591 3.596 11.495 3.613 12.426 C3.649 17.344 3.541 21.505 1.438 26.062 C-0.688 27.438 -0.688 27.438 -2.562 28.062 C-2.082 27.471 -1.601 26.879 -1.105 26.27 C1.176 21.528 1.009 16.95 1.125 11.75 C1.159 10.723 1.193 9.697 1.229 8.639 C1.311 6.113 1.38 3.588 1.438 1.062 C0.777 1.062 0.118 1.062 -0.562 1.062 C-0.536 1.672 -0.51 2.282 -0.483 2.911 C-0.379 5.69 -0.314 8.469 -0.25 11.25 C-0.187 12.69 -0.187 12.69 -0.123 14.158 C-0.041 18.914 -0.009 21.289 -2.844 25.25 C-5.562 27.062 -5.562 27.062 -9.375 26.875 C-10.427 26.607 -11.479 26.339 -12.562 26.062 C-12.562 25.072 -12.562 24.082 -12.562 23.062 C-12.233 23.393 -11.902 23.722 -11.562 24.062 C-7.42 24.31 -7.42 24.31 -3.562 23.062 C-1.88 15.523 -2.228 8.357 -4.562 1.062 C-3.562 0.062 -3.562 0.062 0 0 Z " fill="#9B9790" transform="translate(276.5625,783.9375)"/>
<path d="M0 0 C2.438 2.185 3.649 4.08 4.824 7.125 C5.13 7.914 5.437 8.703 5.752 9.516 C6.215 10.745 6.215 10.745 6.688 12 C7.008 12.82 7.328 13.64 7.658 14.484 C10 20.596 10 20.596 10 24 C9.01 24 8.02 24 7 24 C4 17.375 4 17.375 4 14 C-1.28 14 -6.56 14 -12 14 C-12 13.01 -12 12.02 -12 11 C-7.38 11 -2.76 11 2 11 C1.01 7.7 0.02 4.4 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#EFE9E0" transform="translate(259,701)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-6.65 7.407 -15.42 10.794 -26.004 12.605 C-28.062 12.921 -28.062 12.921 -30 14 C-31.86 14.038 -31.86 14.038 -34.105 13.914 C-34.925 13.87 -35.744 13.826 -36.588 13.781 C-37.446 13.73 -38.304 13.678 -39.188 13.625 C-40.026 13.581 -40.865 13.537 -41.729 13.492 C-45.512 13.28 -49.175 12.973 -52.926 12.398 C-56.033 11.769 -56.033 11.769 -59 13 C-59.99 12.34 -60.98 11.68 -62 11 C-60 9 -60 9 -57.18 8.805 C-56.089 8.828 -54.999 8.851 -53.875 8.875 C-52.779 8.893 -51.684 8.911 -50.555 8.93 C-49.712 8.953 -48.869 8.976 -48 9 C-48 9.33 -48 9.66 -48 10 C-30.899 10.424 -17.645 10.194 -2.617 1.328 C-1.322 0.671 -1.322 0.671 0 0 Z " fill="#986033" transform="translate(399,234)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8.66 1.65 9.32 3.3 10 5 C9.01 4.34 8.02 3.68 7 3 C7 2.34 7 1.68 7 1 C6.01 1 5.02 1 4 1 C4 10.24 4 19.48 4 29 C4.66 29 5.32 29 6 29 C6 21.41 6 13.82 6 6 C9.914 7.305 10.578 8.987 12.75 12.438 C13.384 13.426 14.018 14.415 14.672 15.434 C15.11 16.281 15.548 17.127 16 18 C15.67 18.66 15.34 19.32 15 20 C13.02 17.36 11.04 14.72 9 12 C8.951 13.192 8.902 14.385 8.852 15.613 C8.777 17.18 8.701 18.746 8.625 20.312 C8.579 21.491 8.579 21.491 8.531 22.693 C8.473 23.829 8.473 23.829 8.414 24.988 C8.383 25.685 8.351 26.381 8.319 27.099 C8 29 8 29 6 32 C5.01 32 4.02 32 3 32 C2.98 31.102 2.96 30.203 2.94 29.278 C2.862 25.949 2.775 22.621 2.683 19.292 C2.644 17.851 2.609 16.41 2.578 14.968 C2.533 12.898 2.475 10.828 2.414 8.758 C2.383 7.511 2.351 6.265 2.319 4.981 C2.303 1.885 2.303 1.885 0 0 Z " fill="#99948D" transform="translate(690,784)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.922 1.13 -0.922 1.13 -1.863 1.262 C-6.99 2.118 -6.99 2.118 -11.125 5.062 C-12.341 9.145 -12.211 12.908 -11 17 C-9.857 18.851 -9.857 18.851 -8 20 C-5.487 20.362 -5.487 20.362 -2.812 20.188 C-1.911 20.16 -1.01 20.133 -0.082 20.105 C0.605 20.071 1.292 20.036 2 20 C1.67 20.66 1.34 21.32 1 22 C-0.973 22.296 -2.95 22.581 -4.938 22.766 C-7.143 22.956 -7.143 22.956 -9.75 23.812 C-12 24 -12 24 -14.188 22.438 C-18.074 17.21 -18.171 12.974 -17.289 6.758 C-17.194 6.178 -17.098 5.598 -17 5 C-16.67 5 -16.34 5 -16 5 C-15.67 7.97 -15.34 10.94 -15 14 C-14.925 13.336 -14.85 12.672 -14.773 11.988 C-13.564 3.864 -13.564 3.864 -10.312 0.812 C-6.301 -1.383 -4.354 -1.132 0 0 Z " fill="#A5A098" transform="translate(540,788)"/>
<path d="M0 0 C3 0 3 0 5.438 2.125 C9.933 7.169 12.441 11.506 14 18 C13.34 18.33 12.68 18.66 12 19 C11.446 18.073 10.891 17.146 10.32 16.191 C9.589 14.982 8.857 13.772 8.125 12.562 C7.76 11.951 7.395 11.339 7.02 10.709 C5.078 7.285 5.078 7.285 2 5 C2 12.59 2 20.18 2 28 C1.34 28 0.68 28 0 28 C0 18.76 0 9.52 0 0 Z " fill="#DAD4CB" transform="translate(694,785)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.215 1.666 0.43 2.333 -0.379 3.02 C-13.6 14.504 -21.652 28.411 -24 46 C-24.218 50.38 -24.191 54.741 -24.125 59.125 C-24.116 60.272 -24.107 61.42 -24.098 62.602 C-24.074 65.401 -24.042 68.201 -24 71 C-26.866 68.134 -26.55 65.109 -26.75 61.25 C-26.79 60.488 -26.83 59.726 -26.872 58.941 C-27.615 40.233 -22.083 22.369 -9.375 8.5 C-6.464 5.357 -3.531 2.453 0 0 Z " fill="#89562F" transform="translate(534,303)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C5.062 1.812 5.062 1.812 6 4 C5.67 4.99 5.34 5.98 5 7 C4.67 6.67 4.34 6.34 4 6 C3.67 15.24 3.34 24.48 3 34 C2.01 34 1.02 34 0 34 C0 22.78 0 11.56 0 0 Z " fill="#F7F3EA" transform="translate(584,691)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C5.312 3.375 5.312 3.375 6 6 C5.67 6.99 5.34 7.98 5 9 C4.34 8.34 3.68 7.68 3 7 C3 15.91 3 24.82 3 34 C2.01 34 1.02 34 0 34 C0 22.78 0 11.56 0 0 Z " fill="#F1EAE0" transform="translate(317,691)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.356 4.012 3.356 4.012 3.328 6.473 C3.323 7.373 3.318 8.273 3.312 9.201 C3.292 10.145 3.271 11.09 3.25 12.062 C3.245 12.98 3.24 13.897 3.234 14.842 C3.169 20.625 2.728 26.263 2 32 C3.98 32 5.96 32 8 32 C8 32.33 8 32.66 8 33 C5.36 33 2.72 33 0 33 C0 22.77 0 12.54 0 2 C-4.455 3.485 -4.455 3.485 -9 5 C-9 5.66 -9 6.32 -9 7 C-7.02 6.67 -5.04 6.34 -3 6 C-3 8.97 -3 11.94 -3 15 C-3.33 15 -3.66 15 -4 15 C-4.33 12.69 -4.66 10.38 -5 8 C-6.65 8.33 -8.3 8.66 -10 9 C-10.495 6.525 -10.495 6.525 -11 4 C-9.236 3.275 -7.463 2.571 -5.688 1.875 C-4.701 1.481 -3.715 1.086 -2.699 0.68 C-1.808 0.455 -0.918 0.231 0 0 Z " fill="#A59F97" transform="translate(781,689)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.654 0.984 -1.307 0.969 -1.981 0.952 C-27.418 0.422 -43.751 4.441 -64 20 C-60.612 11.803 -50.915 6.899 -43.25 3.32 C-29.013 -2.464 -14.885 -4.158 0 0 Z " fill="#9C632D" transform="translate(376,116)"/>
<path d="M0 0 C1.435 -0.021 1.435 -0.021 2.898 -0.043 C5.655 0.19 7.274 0.778 9.625 2.188 C9.625 3.178 9.625 4.168 9.625 5.188 C8.491 4.816 7.356 4.445 6.188 4.062 C1.307 2.857 -2.722 3.306 -7.375 5.188 C-11.393 8.278 -11.743 11.647 -12.5 16.438 C-12.626 17.495 -12.753 18.552 -12.883 19.641 C-13.126 20.901 -13.126 20.901 -13.375 22.188 C-14.035 22.518 -14.695 22.847 -15.375 23.188 C-17.58 18.778 -16.626 14.009 -15.25 9.5 C-11.7 3.229 -7.236 0.048 0 0 Z " fill="#F4EDE3" transform="translate(369.375,690.8125)"/>
<path d="M0 0 C2 2 2 2 2.312 4.5 C2.209 5.325 2.106 6.15 2 7 C1.01 7.66 0.02 8.32 -1 9 C-1 8.01 -1 7.02 -1 6 C-6.94 5.01 -6.94 5.01 -13 4 C-13 4.99 -13 5.98 -13 7 C-14.65 6.67 -16.3 6.34 -18 6 C-16.093 3.42 -14.94 2.986 -11.75 2.25 C-7.943 1.996 -4.724 2.181 -1 3 C-0.67 3.66 -0.34 4.32 0 5 C0 4.01 0 3.02 0 2 C-6.191 0.212 -12.654 -0.131 -18.5 2.688 C-21.887 5.821 -23.551 8.652 -25 13 C-25.069 15.061 -25.085 17.125 -25.062 19.188 C-25.053 20.274 -25.044 21.361 -25.035 22.48 C-25.024 23.312 -25.012 24.143 -25 25 C-27.563 22.437 -27.298 21.369 -27.312 17.812 C-27.163 11.074 -26.092 7.139 -21.25 2.188 C-14.114 -2.616 -8.032 -2.398 0 0 Z " fill="#A29C93" transform="translate(379,691)"/>
<path d="M0 0 C7.287 -0.474 11.972 -0.287 18 4 C17.67 5.32 17.34 6.64 17 8 C15.989 7.319 14.979 6.639 13.938 5.938 C10.912 4.207 9.931 4.001 6.25 4.062 C2.951 5.014 2.027 5.381 0 8 C-0.715 10.464 -0.715 10.464 -1.062 13.125 C-1.473 15.82 -1.473 15.82 -2 18 C-2.66 18.33 -3.32 18.66 -4 19 C-4.109 16.939 -4.186 14.876 -4.25 12.812 C-4.296 11.664 -4.343 10.515 -4.391 9.332 C-3.928 5.386 -2.661 3.865 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BEB8AF" transform="translate(528,784)"/>
<path d="M0 0 C6.6 0 13.2 0 20 0 C20 0.33 20 0.66 20 1 C17.36 1.33 14.72 1.66 12 2 C12 10.25 12 18.5 12 27 C11.01 27 10.02 27 9 27 C9 18.75 9 10.5 9 2 C6.03 2 3.06 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DFD9D0" transform="translate(344,786)"/>
<path d="M0 0 C2.899 0.322 4.824 0.89 7.312 2.438 C9.942 6.43 9.768 10.36 9 15 C7.125 17.938 7.125 17.938 5 20 C4.34 20.99 3.68 21.98 3 23 C-3.836 23 -10.613 22.589 -17 20 C-16.814 19.381 -16.629 18.763 -16.438 18.125 C-15.763 15.958 -15.763 15.958 -17 14 C-14.237 14.523 -11.674 15.109 -9 16 C-7.501 16.068 -6 16.085 -4.5 16.062 C-3.727 16.053 -2.953 16.044 -2.156 16.035 C-1.445 16.024 -0.733 16.012 0 16 C0.99 16 1.98 16 3 16 C0.721 17.781 -1.054 18.994 -4.008 19.012 C-7.128 18.626 -10.019 17.994 -13 17 C-13.33 17.66 -13.66 18.32 -14 19 C-9.127 20.3 -3.992 20.754 1 20 C3.824 17.81 5.415 16.17 7 13 C7.424 9.188 7.326 7.494 5.188 4.25 C3.016 1.859 3.016 1.859 0 0 Z " fill="#95918A" transform="translate(813,704)"/>
<path d="M0 0 C0.797 3.5 1.119 6.714 1.098 10.301 C1.094 11.277 1.091 12.254 1.088 13.26 C1.08 14.267 1.071 15.275 1.062 16.312 C1.058 17.339 1.053 18.366 1.049 19.424 C1.037 21.949 1.021 24.475 1 27 C0.34 27 -0.32 27 -1 27 C-1 19.08 -1 11.16 -1 3 C-1.66 3 -2.32 3 -3 3 C-2.97 3.832 -2.939 4.665 -2.908 5.523 C-2.796 8.614 -2.684 11.706 -2.573 14.798 C-2.501 16.793 -2.428 18.788 -2.355 20.783 C-2.31 22.037 -2.265 23.29 -2.219 24.582 C-2.156 26.318 -2.156 26.318 -2.092 28.089 C-2.03 30.059 -2 32.029 -2 34 C-4.819 31.181 -4.956 29.091 -5.114 25.159 C-5.106 23.557 -5.106 23.557 -5.098 21.922 C-5.094 20.762 -5.091 19.602 -5.088 18.406 C-5.08 17.2 -5.071 15.993 -5.062 14.75 C-5.058 13.528 -5.053 12.306 -5.049 11.047 C-5.037 8.031 -5.021 5.016 -5 2 C-5.66 1.67 -6.32 1.34 -7 1 C-4.537 -0.231 -2.72 -0.072 0 0 Z " fill="#ABA59D" transform="translate(282,688)"/>
<path d="M0 0 C1.675 0.286 3.344 0.618 5 1 C6.08 4.448 6.038 7.482 5.879 11.078 C5.831 12.238 5.782 13.398 5.732 14.594 C5.676 15.8 5.62 17.007 5.562 18.25 C5.51 19.472 5.458 20.694 5.404 21.953 C5.275 24.969 5.14 27.985 5 31 C5.66 31 6.32 31 7 31 C7.33 32.98 7.66 34.96 8 37 C7.01 37 6.02 37 5 37 C4.67 37.66 4.34 38.32 4 39 C2.68 38.67 1.36 38.34 0 38 C-0.167 32.749 -0.328 27.498 -0.482 22.247 C-0.536 20.459 -0.591 18.671 -0.648 16.884 C-0.73 14.32 -0.805 11.756 -0.879 9.191 C-0.906 8.387 -0.933 7.583 -0.961 6.755 C-1.013 4.837 -1.012 2.918 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M1 2 C1 13.22 1 24.44 1 36 C1.99 36 2.98 36 4 36 C4 24.78 4 13.56 4 2 C3.01 2 2.02 2 1 2 Z M5 33 C6 35 6 35 6 35 Z " fill="#979289" transform="translate(432,689)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 11.22 4 22.44 4 34 C3.01 34 2.02 34 1 34 C0.975 29.764 0.957 25.528 0.945 21.292 C0.94 19.851 0.933 18.41 0.925 16.968 C0.912 14.898 0.907 12.828 0.902 10.758 C0.897 9.511 0.892 8.265 0.886 6.981 C1 4 1 4 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F8F4EC" transform="translate(672,691)"/>
<path d="M0 0 C4.764 4.19 5.732 10.078 6.293 16.195 C6.57 27.375 4.183 36.306 -3 45 C-9.908 52.095 -9.908 52.095 -15 53 C-12.645 50.55 -10.212 48.367 -7.562 46.25 C-1.136 40.789 2.464 33.726 3.414 25.352 C3.484 24.122 3.553 22.892 3.625 21.625 C3.7 20.38 3.775 19.135 3.852 17.852 C3.901 16.911 3.95 15.97 4 15 C3.34 15 2.68 15 2 15 C2.041 13.824 2.082 12.649 2.125 11.438 C2.14 7.276 1.312 3.935 0 0 Z " fill="#7D5233" transform="translate(364,362)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.99 5 1.98 5 3 C3.68 2.67 2.36 2.34 1 2 C0.845 3.423 0.845 3.423 0.688 4.875 C-0.003 9.689 -1.191 14.483 -3 19 C-3.66 19.33 -4.32 19.66 -5 20 C-5.879 22.738 -5.879 22.738 -6.562 25.938 C-7.921 31.841 -7.921 31.841 -9 34 C-9.99 34 -10.98 34 -12 34 C-10.392 26.532 -7.66 19.6 -4.875 12.5 C-4.407 11.296 -3.939 10.092 -3.457 8.852 C-2.309 5.899 -1.157 2.949 0 0 Z " fill="#EEE7DE" transform="translate(252,691)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C6.312 3.062 6.312 3.062 7 5 C5.02 3.68 3.04 2.36 1 1 C1 9.91 1 18.82 1 28 C1.66 28 2.32 28 3 28 C2.99 27.405 2.979 26.809 2.968 26.196 C2.927 23.506 2.901 20.815 2.875 18.125 C2.85 16.719 2.85 16.719 2.824 15.285 C2.815 13.941 2.815 13.941 2.805 12.57 C2.794 11.743 2.784 10.915 2.773 10.063 C3 8 3 8 5 6 C5.861 8.935 6.078 11.393 5.914 14.441 C5.87 15.272 5.826 16.103 5.781 16.959 C5.73 17.818 5.678 18.677 5.625 19.562 C5.581 20.426 5.537 21.289 5.492 22.178 C5.295 25.702 5.123 28.63 4 32 C2.68 32 1.36 32 0 32 C0 21.44 0 10.88 0 0 Z " fill="#A29C95" transform="translate(557,784)"/>
<path d="M0 0 C9.9 0 19.8 0 30 0 C30 4.29 30 8.58 30 13 C29.34 13.66 28.68 14.32 28 15 C28 10.71 28 6.42 28 2 C23.58 1.979 19.169 2.006 14.75 2.062 C13.496 2.052 12.241 2.041 10.949 2.029 C4.603 1.85 4.603 1.85 -0.752 4.621 C-2.042 6.73 -2.042 6.73 -3 10 C-3.33 9.01 -3.66 8.02 -4 7 C-2.68 4.69 -1.36 2.38 0 0 Z " fill="#F6A034" transform="translate(527,121)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.818 0.632 2.636 1.263 2.449 1.914 C0.578 8.572 -0.958 15.258 -2.352 22.031 C-3 25 -3 25 -4 28 C-5.666 28.043 -7.334 28.041 -9 28 C-10 27 -10 27 -10.098 25.152 C-10.065 23.102 -10.033 21.051 -10 19 C-8.68 19.33 -7.36 19.66 -6 20 C-5.184 17.605 -4.372 15.209 -3.562 12.812 C-3.332 12.139 -3.102 11.465 -2.865 10.771 C-1.665 7.205 -0.627 3.713 0 0 Z " fill="#C2BEB5" transform="translate(321,786)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C5.464 6.653 8.137 13.338 10.688 20.125 C11.051 21.086 11.415 22.047 11.789 23.037 C12.674 25.378 13.557 27.72 14.438 30.062 C15.098 29.732 15.757 29.403 16.438 29.062 C16.438 32.396 16.438 35.729 16.438 39.062 C16.108 38.403 15.777 37.742 15.438 37.062 C13.788 37.062 12.137 37.062 10.438 37.062 C10.108 35.413 9.777 33.763 9.438 32.062 C10.098 33.383 10.757 34.702 11.438 36.062 C12.428 36.062 13.418 36.062 14.438 36.062 C14.074 35.139 13.71 34.216 13.336 33.264 C11.989 29.839 10.649 26.411 9.311 22.983 C8.73 21.499 8.148 20.016 7.564 18.533 C6.726 16.403 5.893 14.27 5.062 12.137 C4.8 11.474 4.538 10.812 4.267 10.129 C3.163 7.277 2.438 5.151 2.438 2.062 C0.787 2.062 -0.863 2.062 -2.562 2.062 C-2.756 2.761 -2.949 3.46 -3.148 4.18 C-3.539 5.545 -3.539 5.545 -3.938 6.938 C-4.193 7.842 -4.448 8.747 -4.711 9.68 C-5.562 12.062 -5.562 12.062 -7.562 14.062 C-7.562 12.082 -7.562 10.102 -7.562 8.062 C-6.903 8.062 -6.243 8.062 -5.562 8.062 C-5.253 6.928 -4.944 5.794 -4.625 4.625 C-3.269 0.08 -3.269 0.08 0 0 Z " fill="#97928A" transform="translate(254.5625,688.9375)"/>
<path d="M0 0 C4 1 4 1 5.484 3.066 C5.902 3.89 6.32 4.714 6.75 5.562 C7.178 6.389 7.606 7.215 8.047 8.066 C8.361 8.704 8.676 9.343 9 10 C4.125 8.125 4.125 8.125 3 7 C2.67 15.91 2.34 24.82 2 34 C1.34 34 0.68 34 0 34 C0 22.78 0 11.56 0 0 Z " fill="#F6F2E9" transform="translate(450,691)"/>
<path d="M0 0 C0 3 0 3 -1.566 4.684 C-2.897 5.853 -4.231 7.02 -5.566 8.184 C-7.232 9.876 -7.232 9.876 -6.863 11.988 C-2.422 22.337 7.385 28.689 17.438 32.875 C18.406 33.244 19.374 33.612 20.371 33.992 C22.67 34.874 24.822 35.857 27 37 C15.686 36.774 5.556 30.176 -2.523 22.699 C-4 21 -4 21 -4 19 C-4.99 19.33 -5.98 19.66 -7 20 C-7.495 17.525 -7.495 17.525 -8 15 C-8.99 14.67 -9.98 14.34 -11 14 C-9.595 9.529 -7.407 7.163 -4 4 C-3.237 3.237 -2.474 2.474 -1.688 1.688 C-1.131 1.131 -0.574 0.574 0 0 Z " fill="#7D5437" transform="translate(279,383)"/>
<path d="M0 0 C1.675 0.286 3.344 0.618 5 1 C5.33 2.65 5.66 4.3 6 6 C5.34 6 4.68 6 4 6 C4.139 9.459 4.287 12.917 4.438 16.375 C4.477 17.36 4.516 18.345 4.557 19.359 C4.619 20.771 4.619 20.771 4.684 22.211 C4.72 23.08 4.757 23.95 4.795 24.845 C4.874 27.048 4.874 27.048 6 29 C4.02 29.33 2.04 29.66 0 30 C-0.66 28.68 -1.32 27.36 -2 26 C-0.35 26.99 1.3 27.98 3 29 C3 20.09 3 11.18 3 2 C2.34 2 1.68 2 1 2 C1 8.6 1 15.2 1 22 C0.34 21.67 -0.32 21.34 -1 21 C-2.006 16.975 -1.973 14.001 -1 10 C-1 7.958 -1.022 5.916 -1.062 3.875 C-1 1 -1 1 0 0 Z " fill="#949089" transform="translate(576,784)"/>
<path d="M0 0 C6.93 0 13.86 0 21 0 C21 0.66 21 1.32 21 2 C18.03 2 15.06 2 12 2 C12 10.25 12 18.5 12 27 C11.34 27 10.68 27 10 27 C10 18.75 10 10.5 10 2 C6.7 2 3.4 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E8E1D9" transform="translate(495,786)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.015 0.696 2.029 1.391 2.044 2.108 C2.119 5.281 2.216 8.453 2.312 11.625 C2.335 12.719 2.358 13.814 2.381 14.941 C2.55 22.941 2.55 22.941 6 30 C8.212 31.335 8.212 31.335 10.688 31.875 C11.496 32.099 12.304 32.324 13.137 32.555 C13.752 32.702 14.366 32.849 15 33 C15 33.33 15 33.66 15 34 C6.333 34.441 6.333 34.441 2 31 C-0.391 27.413 -0.234 25.836 -0.195 21.582 C-0.189 20.328 -0.182 19.075 -0.176 17.783 C-0.159 16.473 -0.142 15.163 -0.125 13.812 C-0.115 12.477 -0.106 11.142 -0.098 9.807 C-0.074 6.538 -0.041 3.269 0 0 Z " fill="#FAF6ED" transform="translate(279,691)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C5.67 2.66 5.34 3.32 5 4 C2.525 3.505 2.525 3.505 0 3 C-0.235 4.226 -0.469 5.452 -0.711 6.715 C-2.082 12.885 -4.354 18.676 -6.625 24.562 C-7.045 25.667 -7.465 26.771 -7.898 27.908 C-8.927 30.608 -9.961 33.305 -11 36 C-10.34 36 -9.68 36 -9 36 C-8.01 33.03 -7.02 30.06 -6 27 C-5.34 27.66 -4.68 28.32 -4 29 C-7.133 37.508 -7.133 37.508 -10.75 39.562 C-11.492 39.707 -12.235 39.851 -13 40 C-14.097 36.71 -13.8 35.287 -13 32 C-12.34 32 -11.68 32 -11 32 C-10.876 31.45 -10.753 30.899 -10.625 30.332 C-8.037 19.9 -3.913 9.99 0 0 Z " fill="#99948A" transform="translate(698,688)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.168 3.771 1.334 7.542 1.5 11.312 C1.548 12.384 1.595 13.456 1.645 14.561 C1.69 15.589 1.735 16.617 1.781 17.676 C1.844 19.098 1.844 19.098 1.908 20.548 C2.039 24.031 2 27.515 2 31 C0.02 31 -1.96 31 -4 31 C-4.33 22.75 -4.66 14.5 -5 6 C-5.66 6.66 -6.32 7.32 -7 8 C-6.25 3.25 -6.25 3.25 -4 1 C-3 2 -3 2 -2.886 4.592 C-2.892 5.718 -2.897 6.844 -2.902 8.004 C-2.906 9.219 -2.909 10.434 -2.912 11.686 C-2.92 12.965 -2.929 14.244 -2.938 15.562 C-2.942 16.846 -2.947 18.129 -2.951 19.451 C-2.963 22.634 -2.979 25.817 -3 29 C-2.01 29 -1.02 29 0 29 C0 19.43 0 9.86 0 0 Z " fill="#8D877D" transform="translate(676,696)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2 2.98 2 4 2 C4.33 1.34 4.66 0.68 5 0 C4.67 1.32 4.34 2.64 4 4 C3.01 3.67 2.02 3.34 1 3 C1 14.22 1 25.44 1 37 C1.99 37 2.98 37 4 37 C4.33 32.71 4.66 28.42 5 24 C5.33 24 5.66 24 6 24 C6.169 25.916 6.335 27.833 6.5 29.75 C6.593 30.817 6.686 31.885 6.781 32.984 C6.951 35.318 7 37.66 7 40 C4.69 40 2.38 40 0 40 C-1.164 35.88 -1.029 31.977 -0.879 27.734 C-0.855 26.941 -0.832 26.148 -0.807 25.33 C-0.731 22.803 -0.647 20.277 -0.562 17.75 C-0.509 16.034 -0.457 14.318 -0.404 12.602 C-0.275 8.401 -0.14 4.2 0 0 Z " fill="#9F998F" transform="translate(393,688)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.225 5.362 -1.503 7.602 -2.875 9.875 C-6.887 16.859 -8.22 22.863 -8 31 C-8.99 31 -9.98 31 -11 31 C-11.054 28.917 -11.093 26.834 -11.125 24.75 C-11.148 23.59 -11.171 22.43 -11.195 21.234 C-11 18.005 -10.638 16.699 -9 14 C-8.453 12.944 -7.907 11.889 -7.344 10.801 C-6.735 9.65 -6.127 8.499 -5.5 7.312 C-4.892 6.154 -4.283 4.995 -3.656 3.801 C-2 1 -2 1 0 0 Z " fill="#F4EEE3" transform="translate(742,694)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 9.24 3 18.48 3 28 C2.01 28 1.02 28 0 28 C0 18.76 0 9.52 0 0 Z " fill="#E5DFD7" transform="translate(406,785)"/>
<path d="M0 0 C4.122 3.768 7.631 7.889 10 13 C9.67 13.66 9.34 14.32 9 15 C8.01 14.67 7.02 14.34 6 14 C6.517 14.771 7.034 15.542 7.566 16.336 C8.225 17.339 8.884 18.342 9.562 19.375 C10.554 20.872 10.554 20.872 11.566 22.398 C12.039 23.257 12.513 24.115 13 25 C12.67 25.66 12.34 26.32 12 27 C10.369 24.608 8.745 22.212 7.125 19.812 C6.435 18.802 6.435 18.802 5.73 17.771 C1.647 11.705 -0.294 7.497 0 0 Z " fill="#B7B1A8" transform="translate(322,693)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 8.91 2 17.82 2 27 C-2 25 -2 25 -5 23 C-5.688 20.375 -5.688 20.375 -6 18 C-3 18 -3 18 0 20 C0 13.4 0 6.8 0 0 Z " fill="#F0EBE2" transform="translate(577,786)"/>
<path d="M0 0 C1.825 0.031 1.825 0.031 3.688 0.062 C5.007 0.062 6.327 0.062 7.688 0.062 C8.017 0.722 8.347 1.383 8.688 2.062 C9.347 2.393 10.007 2.722 10.688 3.062 C10.358 4.712 10.028 6.362 9.688 8.062 C5.812 7.188 5.812 7.188 4.688 6.062 C2.229 5.812 2.229 5.812 -0.312 6.062 C-0.972 6.722 -1.632 7.383 -2.312 8.062 C-2.972 7.732 -3.632 7.403 -4.312 7.062 C-4.643 8.053 -4.972 9.043 -5.312 10.062 C-5.312 8.413 -5.312 6.763 -5.312 5.062 C-0.268 2.848 2.547 3.212 7.688 5.062 C4.027 2.622 0.946 2.326 -3.312 3.062 C-5.818 4.896 -5.818 4.896 -7.312 7.062 C-6.653 9.372 -5.993 11.683 -5.312 14.062 C-6.303 13.732 -7.292 13.403 -8.312 13.062 C-9.125 9.875 -9.125 9.875 -9.312 6.062 C-6.938 3.188 -6.938 3.188 -4.312 1.062 C-3.312 0.062 -3.312 0.062 0 0 Z " fill="#9A9690" transform="translate(239.3125,783.9375)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C1.66 8 2.32 8 3 8 C3.392 8.495 3.784 8.99 4.188 9.5 C6.998 11.826 9.468 11.248 13 11 C15.31 10.571 15.31 10.571 17 10 C17 7.36 17 4.72 17 2 C14.36 2 11.72 2 9 2 C9 1.34 9 0.68 9 0 C12.3 0 15.6 0 19 0 C19 3.63 19 7.26 19 11 C13.864 13.568 9.658 13.812 4 13 C0.812 10.875 0.812 10.875 -1 8 C-1.377 4.796 -1.444 2.889 0 0 Z " fill="#E6E0D7" transform="translate(664,799)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.03 3.292 3.047 6.583 3.062 9.875 C3.071 10.793 3.079 11.711 3.088 12.656 C3.11 18.834 2.739 24.867 2 31 C0 29 0 29 -0.227 26.111 C-0.217 24.906 -0.206 23.702 -0.195 22.461 C-0.186 20.508 -0.186 20.508 -0.176 18.516 C-0.159 17.135 -0.142 15.755 -0.125 14.375 C-0.115 12.987 -0.106 11.599 -0.098 10.211 C-0.074 6.807 -0.041 3.404 0 0 Z " fill="#F6F1E9" transform="translate(473,691)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 8.91 2 17.82 2 27 C-2.155 24.923 -3.117 24.064 -5 20 C-4.67 19.01 -4.34 18.02 -4 17 C-2.68 17.99 -1.36 18.98 0 20 C0 13.4 0 6.8 0 0 Z " fill="#EFE9DF" transform="translate(713,786)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 7.59 6 15.18 6 23 C5.34 22.67 4.68 22.34 4 22 C4 15.4 4 8.8 4 2 C3.34 2 2.68 2 2 2 C2.33 9.59 2.66 17.18 3 25 C2.34 24.67 1.68 24.34 1 24 C0.67 16.08 0.34 8.16 0 0 Z " fill="#9B968F" transform="translate(257,784)"/>
<path d="M0 0 C1.157 3.471 0.83 4.147 -0.434 7.449 C-0.753 8.297 -1.072 9.144 -1.4 10.018 C-1.743 10.899 -2.085 11.78 -2.438 12.688 C-2.776 13.58 -3.114 14.473 -3.463 15.393 C-4.3 17.598 -5.146 19.8 -6 22 C-5.01 22 -4.02 22 -3 22 C-2.67 21.34 -2.34 20.68 -2 20 C-2 20.99 -2 21.98 -2 23 C-5.982 24.054 -8.982 23.841 -13 23 C-13.33 23.66 -13.66 24.32 -14 25 C-14 22.333 -14 19.667 -14 17 C-11.36 17 -8.72 17 -6 17 C-5.818 16.359 -5.636 15.719 -5.449 15.059 C-3.937 9.856 -2.25 4.929 0 0 Z M-8 18 C-8.99 18.99 -9.98 19.98 -11 21 C-9.68 20.67 -8.36 20.34 -7 20 C-7.33 19.34 -7.66 18.68 -8 18 Z " fill="#A6A29A" transform="translate(246,703)"/>
<path d="M0 0 C2.062 0.438 2.062 0.438 4 1 C3.361 1.186 2.721 1.371 2.062 1.562 C-0.373 2.904 -0.373 2.904 -0.625 6.25 C-0.749 7.487 -0.872 8.725 -1 10 C-1.623 12.014 -2.276 14.02 -3 16 C0.3 16.33 3.6 16.66 7 17 C7 17.33 7 17.66 7 18 C3.04 18 -0.92 18 -5 18 C-5.66 20.97 -6.32 23.94 -7 27 C-7.99 27 -8.98 27 -10 27 C-8.272 20.693 -6.163 14.559 -3.875 8.438 C-3.609 7.723 -3.344 7.008 -3.07 6.271 C-1.126 1.126 -1.126 1.126 0 0 Z " fill="#EAE4DB" transform="translate(639,786)"/>
<path d="M0 0 C0 9.9 0 19.8 0 30 C-0.66 30 -1.32 30 -2 30 C-2 21.09 -2 12.18 -2 3 C-3.98 3.99 -3.98 3.99 -6 5 C-6.99 5 -7.98 5 -9 5 C-9 4.34 -9 3.68 -9 3 C-2.25 0 -2.25 0 0 0 Z " fill="#F8F3EA" transform="translate(781,691)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.32 3.33 -2.64 3.66 -4 4 C-4.33 3.01 -4.66 2.02 -5 1 C-8.653 2.25 -9.781 2.671 -12 6 C-12.649 10.519 -12.792 14.614 -10.5 18.625 C-6.762 20.681 -4.249 20.405 0 20 C0.99 19.34 1.98 18.68 3 18 C2.312 19.938 2.312 19.938 1 22 C-3.317 23.727 -6.781 22.641 -11 21 C-14.094 17.856 -14.432 15.399 -14.438 11.062 C-14.341 7.103 -14.174 5.306 -12.188 1.812 C-8.055 -1.611 -5.049 -0.857 0 0 Z " fill="#938E86" transform="translate(388,788)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C26 0.66 26 1.32 26 2 C25.34 2 24.68 2 24 2 C24 2.66 24 3.32 24 4 C22.747 3.916 21.494 3.832 20.203 3.746 C18.552 3.643 16.901 3.54 15.25 3.438 C14.425 3.381 13.6 3.325 12.75 3.268 C11.951 3.219 11.152 3.171 10.328 3.121 C9.228 3.05 9.228 3.05 8.106 2.978 C5.971 2.916 5.971 2.916 3.854 3.555 C2 4 2 4 0 3 C0 2.01 0 1.02 0 0 Z M2 1 C2.66 1.66 3.32 2.32 4 3 C4 2.34 4 1.68 4 1 C3.34 1 2.68 1 2 1 Z " fill="#D9893F" transform="translate(627,244)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 11.22 3 22.44 3 34 C1.68 34 0.36 34 -1 34 C-1 33.34 -1 32.68 -1 32 C-0.01 32 0.98 32 2 32 C1.505 29.525 1.505 29.525 1 27 C0.67 27 0.34 27 0 27 C0 18.09 0 9.18 0 0 Z " fill="#FBF9F3" transform="translate(607,691)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-4.802 4.384 -11.61 6.321 -19 8 C-19.69 8.191 -20.379 8.382 -21.09 8.578 C-23.2 9.044 -25.092 9.098 -27.25 9.062 C-29.106 9.032 -29.106 9.032 -31 9 C-31 8.34 -31 7.68 -31 7 C-33.97 6.67 -36.94 6.34 -40 6 C-40 5.67 -40 5.34 -40 5 C-39.171 5.031 -39.171 5.031 -38.326 5.063 C-24.585 5.513 -13.08 4.36 0 0 Z " fill="#7A5236" transform="translate(347,415)"/>
<path d="M0 0 C11.796 -0.856 21.591 1.56 31 9 C32.193 10.266 33.38 11.537 34.562 12.812 C36.239 14.642 36.239 14.642 38 16 C40.869 16.132 40.869 16.132 43 15 C41.63 18.161 41.011 18.993 38 21 C37.649 20.319 37.299 19.639 36.938 18.938 C31.212 10.258 23.224 4.798 12.943 2.55 C8.649 1.807 4.344 1.348 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A1652F" transform="translate(230,143)"/>
<path d="M0 0 C8.91 0 17.82 0 27 0 C27.33 1.32 27.66 2.64 28 4 C26.969 3.835 25.938 3.67 24.875 3.5 C19.672 2.821 14.504 3.016 9.27 3.16 C6.035 3.002 3.897 2.391 1 1 C1.33 1.99 1.66 2.98 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#D8893F" transform="translate(531,244)"/>
<path d="M0 0 C4.5 2.25 4.5 2.25 6 6 C5.01 6.33 4.02 6.66 3 7 C2.67 13.6 2.34 20.2 2 27 C1.34 27 0.68 27 0 27 C0 18.09 0 9.18 0 0 Z " fill="#E6E0D6" transform="translate(558,785)"/>
<path d="M0 0 C0.901 0.027 1.802 0.054 2.73 0.082 C3.761 0.134 3.761 0.134 4.812 0.188 C5.143 0.847 5.472 1.508 5.812 2.188 C2.513 2.188 -0.788 2.188 -4.188 2.188 C-3.857 3.508 -3.528 4.827 -3.188 6.188 C-4.508 6.188 -5.827 6.188 -7.188 6.188 C-7.074 8.147 -6.946 10.105 -6.812 12.062 C-6.743 13.153 -6.673 14.244 -6.602 15.367 C-6.455 18.318 -6.455 18.318 -4.188 20.188 C-1.985 20.648 -1.985 20.648 0.438 20.812 C1.255 20.887 2.072 20.962 2.914 21.039 C3.541 21.088 4.167 21.137 4.812 21.188 C2.812 23.188 2.812 23.188 -0.562 23.562 C-4.188 23.188 -4.188 23.188 -6.387 21.668 C-8.868 18.25 -8.738 15.185 -8.625 11.125 C-8.636 10.045 -8.636 10.045 -8.646 8.943 C-8.611 6.014 -8.523 3.742 -6.984 1.203 C-4.547 -0.174 -2.781 -0.108 0 0 Z M-6.188 3.188 C-5.188 5.188 -5.188 5.188 -5.188 5.188 Z " fill="#939088" transform="translate(674.1875,787.8125)"/>
<path d="M0 0 C1.875 0.312 1.875 0.312 4 1 C6.319 4.479 6.184 5.519 5.812 9.562 C5.736 10.533 5.66 11.504 5.582 12.504 C5 15 5 15 2 17 C-3.228 17.603 -8.279 17.518 -13 15 C-12.67 14.01 -12.34 13.02 -12 12 C-11.175 12.351 -10.35 12.701 -9.5 13.062 C-5.166 14.223 -2.391 13.757 2 13 C3.769 8.399 3.769 8.399 3.25 3.688 C1.967 1.869 1.967 1.869 0 0 Z " fill="#C2BCB2" transform="translate(812,708)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.242 3.631 -0.583 5.23 -1.438 6.812 C-4.533 13.261 -4.503 18.97 -4 26 C-6.31 26.33 -8.62 26.66 -11 27 C-11.33 26.01 -11.66 25.02 -12 24 C-11.34 24 -10.68 24 -10 24 C-10.66 22.02 -11.32 20.04 -12 18 C-11.34 18 -10.68 18 -10 18 C-10 15.69 -10 13.38 -10 11 C-9.67 11 -9.34 11 -9 11 C-9 15.29 -9 19.58 -9 24 C-8.01 24 -7.02 24 -6 24 C-6.07 23.189 -6.139 22.378 -6.211 21.543 C-6.641 12.738 -4.906 7.286 0 0 Z " fill="#9C978E" transform="translate(740,701)"/>
<path d="M0 0 C-1.98 3.63 -3.96 7.26 -6 11 C-7.245 8.509 -6.777 7.589 -6 5 C-8.86 7.516 -10.415 10.339 -12.188 13.688 C-12.982 15.178 -12.982 15.178 -13.793 16.699 C-14.39 17.838 -14.39 17.838 -15 19 C-15.66 18.67 -16.32 18.34 -17 18 C-15.294 14.588 -13.414 11.272 -11.562 7.938 C-11.155 7.192 -10.747 6.446 -10.326 5.678 C-9.931 4.968 -9.536 4.258 -9.129 3.527 C-8.769 2.875 -8.408 2.223 -8.037 1.551 C-5.681 -1.972 -4.299 -0.86 0 0 Z " fill="#AFAAA2" transform="translate(748,690)"/>
<path d="M0 0 C9.24 0 18.48 0 28 0 C27.67 0.99 27.34 1.98 27 3 C24.688 3.625 24.688 3.625 22 4 C21.01 3.34 20.02 2.68 19 2 C18.134 2.526 18.134 2.526 17.25 3.062 C14.514 4.203 13.791 3.797 11 3 C8.667 2.961 6.333 2.955 4 3 C4 2.34 4 1.68 4 1 C2.68 0.67 1.36 0.34 0 0 Z " fill="#DA893F" transform="translate(580,244)"/>
<path d="M0 0 C9.24 0 18.48 0 28 0 C28 0.66 28 1.32 28 2 C24.605 2.763 21.465 3.128 17.988 3.133 C17.066 3.134 16.144 3.135 15.193 3.137 C14.243 3.133 13.292 3.129 12.312 3.125 C11.35 3.129 10.388 3.133 9.396 3.137 C8.478 3.135 7.56 3.134 6.613 3.133 C5.776 3.132 4.938 3.131 4.075 3.129 C2 3 2 3 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D7873F" transform="translate(440,244)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C2.68 2 1.36 2 0 2 C0.026 2.761 0.052 3.523 0.079 4.307 C0.183 7.767 0.248 11.227 0.312 14.688 C0.354 15.886 0.396 17.084 0.439 18.318 C0.563 27.157 0.563 27.157 -2.199 31.297 C-5.16 33.097 -6.693 33.652 -10.125 33.562 C-11.397 33.564 -11.397 33.564 -12.695 33.566 C-15 33 -15 33 -16.867 31.152 C-17.241 30.442 -17.615 29.732 -18 29 C-17.67 28.01 -17.34 27.02 -17 26 C-14.752 28.052 -13.998 29.007 -13 32 C-11.539 31.744 -10.081 31.47 -8.625 31.188 C-7.813 31.037 -7.001 30.886 -6.164 30.73 C-3.642 30.125 -3.642 30.125 -2 27 C-1.648 24.207 -1.648 24.207 -1.621 21.055 C-1.587 19.896 -1.553 18.737 -1.518 17.543 C-1.491 16.333 -1.465 15.122 -1.438 13.875 C-1.378 11.476 -1.314 9.078 -1.246 6.68 C-1.222 5.615 -1.198 4.55 -1.174 3.453 C-1 1 -1 1 0 0 Z " fill="#938E86" transform="translate(300,689)"/>
<path d="M0 0 C5.94 0 11.88 0 18 0 C18 0.33 18 0.66 18 1 C16.844 1.091 16.844 1.091 15.664 1.184 C14.16 1.309 14.16 1.309 12.625 1.438 C11.128 1.559 11.128 1.559 9.602 1.684 C7.021 1.85 7.021 1.85 5 3 C4.34 2.67 3.68 2.34 3 2 C2.833 4.416 2.833 4.416 3 7 C5.762 9.762 8.207 9.579 12 10 C12 10.66 12 11.32 12 12 C8.7 12.33 5.4 12.66 2 13 C0.057 9.115 0.347 4.257 0 0 Z M2 9 C3 11 3 11 3 11 Z " fill="#AEA9A0" transform="translate(801,693)"/>
<path d="M0 0 C2.285 2.285 2.971 4.125 4.125 7.125 C4.478 8.035 4.831 8.945 5.195 9.883 C5.461 10.581 5.726 11.28 6 12 C1.25 13.125 1.25 13.125 -1 12 C-1.722 10.356 -2.394 8.689 -3 7 C-3.66 7 -4.32 7 -5 7 C-5 6.34 -5 5.68 -5 5 C-8.3 5 -11.6 5 -15 5 C-15 4.34 -15 3.68 -15 3 C-10.71 3 -6.42 3 -2 3 C0 7 0 7 0 11 C0.99 11 1.98 11 3 11 C2.783 10.457 2.567 9.915 2.344 9.355 C2.065 8.64 1.787 7.925 1.5 7.188 C1.222 6.48 0.943 5.772 0.656 5.043 C0 3 0 3 0 0 Z " fill="#99948D" transform="translate(752,801)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C25.67 0.99 25.34 1.98 25 3 C21.583 3.087 18.167 3.14 14.75 3.188 C13.782 3.213 12.814 3.238 11.816 3.264 C10.882 3.273 9.947 3.283 8.984 3.293 C8.126 3.309 7.267 3.324 6.382 3.341 C4 3 4 3 0 0 Z " fill="#D7883E" transform="translate(694,244)"/>
<path d="M0 0 C12.298 0.246 23.125 8.127 32 16 C30.25 20.875 30.25 20.875 28 22 C28.33 20.35 28.66 18.7 29 17 C21.017 8.844 10.97 3.865 0 1 C0 0.67 0 0.34 0 0 Z " fill="#995F2F" transform="translate(335,294)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.123 1.219 -2.246 1.438 -3.402 1.664 C-17.226 4.522 -24.95 8.903 -34 20 C-34.33 19.34 -34.66 18.68 -35 18 C-25.845 6.728 -15.243 -1.173 0 0 Z " fill="#F49B38" transform="translate(315,294)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.701 1.577 4.386 3.162 5.062 4.75 C5.445 5.632 5.828 6.513 6.223 7.422 C7 10 7 10 6 14 C2.7 14 -0.6 14 -4 14 C-2.68 9.38 -1.36 4.76 0 0 Z M1 3 C0.01 5.97 -0.98 8.94 -2 12 C-1.01 11.67 -0.02 11.34 1 11 C3.688 11.438 3.688 11.438 6 12 C5.01 9.03 4.02 6.06 3 3 C2.34 3 1.68 3 1 3 Z " fill="#A39E96" transform="translate(639,789)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C2.67 2.66 2.34 3.32 2 4 C1.34 3.67 0.68 3.34 0 3 C-1.498 2.905 -2.999 2.87 -4.5 2.875 C-5.294 2.872 -6.088 2.87 -6.906 2.867 C-9.059 2.867 -9.059 2.867 -11 4 C-11.658 7.029 -11.658 7.029 -12 10 C-12.99 10 -13.98 10 -15 10 C-15.33 8.02 -15.66 6.04 -16 4 C-10.955 -0.249 -6.535 -0.967 0 0 Z " fill="#D7D1C8" transform="translate(777,786)"/>
<path d="M0 0 C1.422 4.265 -0.346 7.2 -1.938 11.188 C-2.228 11.937 -2.519 12.687 -2.818 13.459 C-3.537 15.309 -4.268 17.155 -5 19 C-4.34 19 -3.68 19 -3 19 C-2.34 17.02 -1.68 15.04 -1 13 C0.159 16.478 -0.292 18.459 -1 22 C-6 22 -6 22 -9 20 C-8 17 -8 17 -6 15 C-4.902 12.468 -3.867 9.947 -2.875 7.375 C-2.459 6.315 -2.459 6.315 -2.035 5.234 C-1.352 3.491 -0.675 1.746 0 0 Z " fill="#9B9690" transform="translate(737,793)"/>
<path d="M0 0 C13.466 -0.796 13.466 -0.796 19 4 C20.118 7.683 20.451 9.827 19.062 13.438 C18.537 14.211 18.537 14.211 18 15 C17.34 14.67 16.68 14.34 16 14 C16.66 11.36 17.32 8.72 18 6 C17.01 6 16.02 6 15 6 C14.67 5.34 14.34 4.68 14 4 C10.04 4 6.08 4 2 4 C2 3.34 2 2.68 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#C4BFB6" transform="translate(421,785)"/>
<path d="M0 0 C1.141 3.422 1.098 6.087 1.062 9.688 C1.053 10.867 1.044 12.046 1.035 13.262 C1.024 14.165 1.012 15.069 1 16 C-1.42 15.032 -2.767 14.335 -4.273 12.172 C-5.133 9.603 -5.017 7.918 -4.625 5.25 C-4.514 4.451 -4.403 3.652 -4.289 2.828 C-4.194 2.225 -4.098 1.622 -4 1 C-2.68 0.67 -1.36 0.34 0 0 Z " fill="#C9C4BA" transform="translate(492,700)"/>
<path d="M0 0 C5.064 1.349 7.108 4.413 9.812 8.625 C10.219 9.236 10.626 9.847 11.045 10.477 C12.041 11.977 13.022 13.487 14 15 C13.67 15.66 13.34 16.32 13 17 C11.68 15.02 10.36 13.04 9 11 C8.01 11.33 7.02 11.66 6 12 C4.992 10.568 3.994 9.129 3 7.688 C2.165 6.487 2.165 6.487 1.312 5.262 C0 3 0 3 0 0 Z " fill="#CDC8BF" transform="translate(590,698)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5.66 3.31 6.32 5.62 7 8 C6.01 8 5.02 8 4 8 C4 6.35 4 4.7 4 3 C2.68 3.33 1.36 3.66 0 4 C0.66 4.33 1.32 4.66 2 5 C1.524 9.288 0.341 11.266 -3 14 C-3.66 14 -4.32 14 -5 14 C-4.587 8.007 -3.681 4.75 0 0 Z " fill="#ACA79E" transform="translate(672,688)"/>
<path d="M0 0 C3.231 0.557 5.024 1.332 7 4 C7 5.32 7 6.64 7 8 C1.72 7.67 -3.56 7.34 -9 7 C-9 6.67 -9 6.34 -9 6 C-5.37 6 -1.74 6 2 6 C2 5.01 2 4.02 2 3 C-4.27 3 -10.54 3 -17 3 C-17 2.67 -17 2.34 -17 2 C-11.28 1.168 -5.778 0.88 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8E8C85" transform="translate(817,688)"/>
<path d="M0 0 C1.57 -0.006 1.57 -0.006 3.172 -0.012 C10.368 -0.003 17.464 0.421 24.625 1.125 C24.625 1.455 24.625 1.785 24.625 2.125 C9.115 2.125 -6.395 2.125 -22.375 2.125 C-22.375 1.795 -22.375 1.465 -22.375 1.125 C-14.898 0.21 -7.528 -0.028 0 0 Z " fill="#7E7975" transform="translate(367.375,564.875)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.941 1.037 -1.882 1.075 -2.852 1.113 C-4.719 1.212 -4.719 1.212 -6.625 1.312 C-8.47 1.4 -8.47 1.4 -10.352 1.488 C-14.988 2.139 -18.405 3.611 -21.938 6.688 C-23.602 10.31 -22.681 13.101 -22 17 C-22 17.99 -22 18.98 -22 20 C-24.676 17.324 -25.008 15.916 -25.375 12.062 C-25.207 8.293 -24.511 5.929 -22 3 C-14.706 -1.538 -8.187 -1.052 0 0 Z " fill="#F59C39" transform="translate(327,316)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.025 0.827 1.05 1.655 1.076 2.507 C1.192 6.255 1.315 10.002 1.438 13.75 C1.477 15.052 1.516 16.354 1.557 17.695 C1.619 19.571 1.619 19.571 1.684 21.484 C1.72 22.636 1.757 23.789 1.795 24.976 C1.966 27.493 2.348 29.584 3 32 C3 32.99 3 33.98 3 35 C0.69 35 -1.62 35 -4 35 C-4.66 33.68 -5.32 32.36 -6 31 C-4.02 31.66 -2.04 32.32 0 33 C0 22.11 0 11.22 0 0 Z " fill="#908B83" transform="translate(476,692)"/>
<path d="M0 0 C7.59 0 15.18 0 23 0 C23 0.66 23 1.32 23 2 C16.07 2 9.14 2 2 2 C1.67 3.65 1.34 5.3 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#ED9A35" transform="translate(721,297)"/>
<path d="M0 0 C0.708 -0.008 1.415 -0.015 2.145 -0.023 C7.22 -0.005 11.618 0.864 16.5 2.25 C16.5 2.58 16.5 2.91 16.5 3.25 C15.464 3.242 15.464 3.242 14.408 3.234 C11.251 3.213 8.094 3.2 4.938 3.188 C3.851 3.179 2.765 3.171 1.646 3.162 C-5.032 3.123 -5.032 3.123 -11.652 3.879 C-13.062 4.063 -13.062 4.063 -14.5 4.25 C-15.16 3.59 -15.82 2.93 -16.5 2.25 C-10.856 0.648 -5.854 -0.064 0 0 Z " fill="#AD6F30" transform="translate(359.5,113.75)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C-0.65 10.33 -2.3 10.66 -4 11 C-4 11.66 -4 12.32 -4 13 C1.61 13 7.22 13 13 13 C13 13.66 13 14.32 13 15 C10.771 15.054 8.542 15.093 6.312 15.125 C4.45 15.16 4.45 15.16 2.551 15.195 C-0.812 15.01 -2.999 14.458 -6 13 C-6 11.68 -6 10.36 -6 9 C-2.25 7.875 -2.25 7.875 0 9 C0 6.03 0 3.06 0 0 Z " fill="#918D86" transform="translate(777,712)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.66 1.65 3.32 3.3 4 5 C3.34 5 2.68 5 2 5 C1.979 6.196 1.959 7.392 1.938 8.625 C1.209 15.739 -2.634 21.822 -6 28 C-6.33 27.34 -6.66 26.68 -7 26 C-6.29 24.409 -5.504 22.852 -4.688 21.312 C-1.358 14.358 -0.688 7.609 0 0 Z " fill="#A56834" transform="translate(634,365)"/>
<path d="M0 0 C8.203 1.129 15.627 6.886 21 13 C19.731 16.914 18.488 18.791 15 21 C15.66 18.03 16.32 15.06 17 12 C11.582 7.536 6.399 3.909 0 1 C0 0.67 0 0.34 0 0 Z " fill="#F29A38" transform="translate(344,298)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C5.67 2.66 5.34 3.32 5 4 C3.68 3.34 2.36 2.68 1 2 C1 11.57 1 21.14 1 31 C0.67 31 0.34 31 0 31 C-1.488 10.542 -1.488 10.542 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B5AEA6" transform="translate(449,689)"/>
<path d="M0 0 C0.885 -0.013 1.77 -0.026 2.682 -0.039 C3.924 -0.041 3.924 -0.041 5.191 -0.043 C5.917 -0.047 6.642 -0.051 7.389 -0.055 C9.438 0.188 9.438 0.188 13.438 2.188 C-1.742 2.188 -16.923 2.188 -32.562 2.188 C-32.562 1.857 -32.562 1.528 -32.562 1.188 C-21.712 0.572 -10.869 0.077 0 0 Z " fill="#7E7975" transform="translate(674.5625,564.8125)"/>
<path d="M0 0 C4.041 3.554 5.026 7.83 6 13 C6.23 17.348 6.184 21.651 6 26 C4 24 4 24 3.805 21.836 C3.828 21.024 3.851 20.212 3.875 19.375 C3.902 18.149 3.902 18.149 3.93 16.898 C3.964 15.959 3.964 15.959 4 15 C3.34 15 2.68 15 2 15 C2.041 13.824 2.082 12.649 2.125 11.438 C2.14 7.276 1.312 3.935 0 0 Z " fill="#9E6537" transform="translate(364,362)"/>
<path d="M0 0 C3.184 1.592 3.548 4.861 4.625 8.062 C4.854 8.726 5.084 9.389 5.32 10.072 C5.887 11.713 6.444 13.356 7 15 C2.71 15 -1.58 15 -6 15 C-5.34 14.01 -4.68 13.02 -4 12 C-3.34 12.33 -2.68 12.66 -2 13 C-1.67 12.34 -1.34 11.68 -1 11 C-1 11.66 -1 12.32 -1 13 C0.32 12.34 1.64 11.68 3 11 C2.526 9.907 2.051 8.814 1.562 7.688 C0.598 5.465 -0.232 3.305 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#AAA39B" transform="translate(743,788)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C1.65 2.33 3.3 2.66 5 3 C5 3.33 5 3.66 5 4 C1.04 4 -2.92 4 -7 4 C-7 4.66 -7 5.32 -7 6 C-4.36 6 -1.72 6 1 6 C1 8.64 1 11.28 1 14 C-0.32 13.67 -1.64 13.34 -3 13 C-3 12.34 -3 11.68 -3 11 C-2.34 10.67 -1.68 10.34 -1 10 C-0.67 9.01 -0.34 8.02 0 7 C-1.392 7.34 -1.392 7.34 -2.812 7.688 C-6 8 -6 8 -7.875 6.688 C-9 5 -9 5 -9 3 C-3.375 0 -3.375 0 0 0 Z " fill="#A5A098" transform="translate(680,795)"/>
<path d="M0 0 C2 1 2 1 2 1 Z M-1 1 C-0.375 2.812 -0.375 2.812 0 5 C-0.99 6.485 -0.99 6.485 -2 8 C-2.603 10.856 -3.013 13.714 -3.438 16.602 C-3.623 17.393 -3.809 18.185 -4 19 C-4.66 19.33 -5.32 19.66 -6 20 C-6.082 18.105 -6.139 16.209 -6.188 14.312 C-6.222 13.257 -6.257 12.201 -6.293 11.113 C-5.874 6.659 -4.115 4.115 -1 1 Z " fill="#F5F0E6" transform="translate(549,693)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5.66 3.31 6.32 5.62 7 8 C6.01 8.33 5.02 8.66 4 9 C4 8.01 4 7.02 4 6 C3.01 6 2.02 6 1 6 C-0.32 9.63 -1.64 13.26 -3 17 C-4.15 12.401 -3.522 10.674 -2.062 6.25 C-1.682 5.08 -1.302 3.909 -0.91 2.703 C-0.61 1.811 -0.309 0.919 0 0 Z " fill="#BEB8AF" transform="translate(253,693)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.293 4.25 -0.293 4.25 -2.188 7 C-7.605 15.278 -10.425 23.263 -12 33 C-12.33 33 -12.66 33 -13 33 C-13.729 21.826 -8.756 12.328 -3 3 C-1.281 1.121 -1.281 1.121 0 0 Z " fill="#F6A237" transform="translate(311,138)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.842 2.727 4.674 5.456 5.5 8.188 C5.738 8.956 5.977 9.725 6.223 10.518 C7.402 14.444 8.333 17.85 8 22 C5.877 19.877 5.413 18.731 4.492 15.938 C4.228 15.141 3.964 14.344 3.691 13.523 C3.422 12.691 3.153 11.858 2.875 11 C2.598 10.167 2.321 9.335 2.035 8.477 C0 2.281 0 2.281 0 0 Z " fill="#E9E2D9" transform="translate(287,785)"/>
<path d="M0 0 C3.97 3.012 5.378 6.527 6.66 11.191 C7.249 16.06 6.069 20.586 4 25 C3.01 25.66 2.02 26.32 1 27 C1.324 26.156 1.647 25.311 1.98 24.441 C3.066 20.779 3.3 17.75 3.312 13.938 C3.329 12.75 3.346 11.563 3.363 10.34 C2.973 6.749 1.995 4.953 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9B968C" transform="translate(568,694)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C4.63 5.33 8.26 5.66 12 6 C12 6.33 12 6.66 12 7 C8.04 7 4.08 7 0 7 C-0.309 7.949 -0.619 8.898 -0.938 9.875 C-2 13 -2 13 -3 15 C-3.66 15 -4.32 15 -5 15 C-3.786 9.678 -2.274 4.961 0 0 Z " fill="#E6E0D7" transform="translate(737,797)"/>
<path d="M0 0 C6.93 0 13.86 0 21 0 C21 0.66 21 1.32 21 2 C13.812 2.888 7.178 3.012 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DFDCD3" transform="translate(495,786)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-14.19 1 -28.38 1 -43 1 C-43 0.67 -43 0.34 -43 0 C-12.667 -1.4 -12.667 -1.4 0 0 Z " fill="#7D7975" transform="translate(737,566)"/>
<path d="M0 0 C1.549 -0.004 1.549 -0.004 3.129 -0.008 C4.747 0.004 4.747 0.004 6.398 0.016 C7.454 0.008 8.51 0 9.598 -0.008 C15.968 0.008 21.612 0.638 27.773 2.266 C27.113 2.926 26.453 3.586 25.773 4.266 C24.783 4.266 23.793 4.266 22.773 4.266 C22.773 3.606 22.773 2.946 22.773 2.266 C12.543 2.266 2.313 2.266 -8.227 2.266 C-4.914 0.058 -3.857 0.009 0 0 Z " fill="#F69E38" transform="translate(429.2265625,293.734375)"/>
<path d="M0 0 C1.828 0.191 1.828 0.191 4 1 C5.484 3.215 5.484 3.215 6.75 5.938 C7.178 6.833 7.606 7.729 8.047 8.652 C9 11 9 11 9 13 C8.01 13.33 7.02 13.66 6 14 C4.995 12.044 3.996 10.085 3 8.125 C2.165 6.489 2.165 6.489 1.312 4.82 C0 2 0 2 0 0 Z " fill="#F2EDE3" transform="translate(720,691)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.859 11.801 1.859 11.801 -1.062 15.688 C-4.479 18.377 -6.701 18.831 -11 19 C-11 18.34 -11 17.68 -11 17 C-9.886 16.587 -8.772 16.175 -7.625 15.75 C-5.007 14.645 -4.165 14.251 -2.562 11.812 C-2.284 10.915 -2.284 10.915 -2 10 C-1.34 10 -0.68 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#DDD6CC" transform="translate(471,793)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.227 1.072 1.454 2.145 1.688 3.25 C3.145 7.413 4.494 8.509 8 11 C8 11.66 8 12.32 8 13 C7.01 13.33 6.02 13.66 5 14 C1.675 10.937 -0.963 8.494 -2 4 C-1.125 1.688 -1.125 1.688 0 0 Z " fill="#EDE7DB" transform="translate(356,710)"/>
<path d="M0 0 C2.496 2.276 4.066 4.607 5.688 7.562 C6.124 8.348 6.561 9.133 7.012 9.941 C8 12 8 12 8 14 C8.66 14.33 9.32 14.66 10 15 C10.625 17.562 10.625 17.562 11 20 C10.34 20 9.68 20 9 20 C8.01 18.02 7.02 16.04 6 14 C5.01 14.66 4.02 15.32 3 16 C2.67 14.68 2.34 13.36 2 12 C2.66 12 3.32 12 4 12 C3.567 10.701 3.567 10.701 3.125 9.375 C2.083 6.25 1.042 3.125 0 0 Z " fill="#9E998F" transform="translate(650,694)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C2.67 2.66 2.34 3.32 2 4 C1.77 6.204 1.589 8.414 1.438 10.625 C1.354 11.814 1.27 13.002 1.184 14.227 C1.123 15.142 1.062 16.057 1 17 C0.01 17 -0.98 17 -2 17 C-2.054 14.917 -2.093 12.834 -2.125 10.75 C-2.148 9.59 -2.171 8.43 -2.195 7.234 C-2.01 4.172 -1.608 2.558 0 0 Z " fill="#F6F0E7" transform="translate(733,708)"/>
<path d="M0 0 C4.29 0 8.58 0 13 0 C13.33 0.99 13.66 1.98 14 3 C9.38 3 4.76 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#F7F2E9" transform="translate(247,712)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.438 5.062 -0.438 5.062 -3 7 C-7.647 7.243 -11.124 6.584 -15 4 C-14.67 4.99 -14.34 5.98 -14 7 C-14.66 7 -15.32 7 -16 7 C-15.67 5.02 -15.34 3.04 -15 1 C-13 2 -13 2 -10 4 C-7.943 4.241 -7.943 4.241 -5.812 4.125 C-4.554 4.084 -3.296 4.043 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#8C8883" transform="translate(246,804)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.331 3.638 2.488 5.377 0.188 8.312 C-3.048 10.809 -4.599 11.048 -8.688 10.562 C-9.781 10.377 -10.874 10.191 -12 10 C-12 9.34 -12 8.68 -12 8 C-8.7 7.67 -5.4 7.34 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z " fill="#EDE8DE" transform="translate(437,791)"/>
<path d="M0 0 C5.28 0 10.56 0 16 0 C15.67 1.32 15.34 2.64 15 4 C14.361 3.814 13.721 3.629 13.062 3.438 C12.382 3.293 11.701 3.149 11 3 C10.67 3.33 10.34 3.66 10 4 C5.17 3.642 3.517 3.517 0 0 Z " fill="#B3ADA3" transform="translate(693,715)"/>
<path d="M0 0 C4.324 3.267 7.065 5.764 9 11 C8.67 11.99 8.34 12.98 8 14 C6.104 12.199 4.504 10.373 2.875 8.312 C2.256 7.549 1.638 6.786 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#F6F0E6" transform="translate(646,693)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.192 1.713 0.384 2.426 -0.449 3.16 C-5.961 8.08 -11.064 12.674 -15 19 C-15.66 18.67 -16.32 18.34 -17 18 C-11.9 11.218 -6.536 5.417 0 0 Z " fill="#9B612F" transform="translate(534,303)"/>
<path d="M0 0 C1.887 0.031 1.887 0.031 3.812 0.062 C3.812 0.393 3.812 0.722 3.812 1.062 C3.198 1.16 2.583 1.258 1.949 1.359 C-2.978 2.187 -2.978 2.187 -7.125 4.75 C-8.516 7.777 -9.381 10.836 -10.188 14.062 C-10.517 14.062 -10.848 14.062 -11.188 14.062 C-11.642 8.381 -10.854 5.462 -7.188 1.062 C-4.437 0.146 -2.828 -0.046 0 0 Z " fill="#EDE7DE" transform="translate(383.1875,785.9375)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C4.33 3.66 4.66 4.32 5 5 C7.113 5.907 7.113 5.907 9.562 6.625 C10.389 6.885 11.215 7.146 12.066 7.414 C13.024 7.704 13.024 7.704 14 8 C14 8.33 14 8.66 14 9 C5.051 9.455 5.051 9.455 1 6 C-0.312 3.312 -0.312 3.312 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F2ECE2" transform="translate(280,716)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.63 1 7.26 1 11 C1.66 11 2.32 11 3 11 C3.33 12.98 3.66 14.96 4 17 C3.01 17 2.02 17 1 17 C0.67 17.66 0.34 18.32 0 19 C-1.32 18.67 -2.64 18.34 -4 18 C-4 17.34 -4 16.68 -4 16 C-2.68 16 -1.36 16 0 16 C0 10.72 0 5.44 0 0 Z M1 13 C2 15 2 15 2 15 Z " fill="#A39E95" transform="translate(436,709)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.727 2.082 3.727 2.082 5.625 4.812 C6.257 5.706 6.888 6.599 7.539 7.52 C9 10 9 10 9 13 C5.623 11.607 4.129 10.177 2.25 7.062 C1.822 6.373 1.394 5.683 0.953 4.973 C0 3 0 3 0 0 Z " fill="#F3EDE4" transform="translate(459,703)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 1.66 2.34 2.32 2 3 C2.933 5.714 3.627 6.78 6.125 8.25 C6.744 8.498 7.363 8.745 8 9 C6.68 9.33 5.36 9.66 4 10 C0.389 6.124 0.389 6.124 -1 4 C-0.812 1.625 -0.812 1.625 0 0 Z " fill="#D78140" transform="translate(272,392)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.32 3.33 -2.64 3.66 -4 4 C-4.33 3.01 -4.66 2.02 -5 1 C-8.937 2.312 -11.227 3.919 -14 7 C-13.077 3.897 -12.295 2.253 -9.812 0.125 C-6.187 -1.325 -3.763 -0.812 0 0 Z " fill="#A49F98" transform="translate(388,788)"/>
<path d="M0 0 C11.522 -0.554 11.522 -0.554 17 3 C16.67 3.66 16.34 4.32 16 5 C15.432 4.675 14.863 4.35 14.277 4.016 C9.694 1.972 4.62 2.471 -0.062 4 C-1.022 4.495 -1.022 4.495 -2 5 C-1.34 3.35 -0.68 1.7 0 0 Z " fill="#A19B94" transform="translate(376,784)"/>
<path d="M0 0 C1.667 1.667 3.333 3.333 5 5 C5.763 5.681 6.526 6.361 7.312 7.062 C9 9 9 9 9 13 C10.65 13.33 12.3 13.66 14 14 C13.34 14.66 12.68 15.32 12 16 C11.01 16 10.02 16 9 16 C7.246 13.77 7.246 13.77 5.438 10.812 C4.838 9.85 4.239 8.887 3.621 7.895 C2.17 5.304 1.022 2.782 0 0 Z " fill="#AEA89E" transform="translate(597,712)"/>
<path d="M0 0 C1.504 0.014 1.504 0.014 3.039 0.027 C3.81 0.039 4.581 0.051 5.375 0.062 C5.375 1.053 5.375 2.043 5.375 3.062 C1.085 3.062 -3.205 3.062 -7.625 3.062 C-7.625 2.403 -7.625 1.742 -7.625 1.062 C-4.98 -0.26 -2.947 -0.034 0 0 Z " fill="#F8F3E9" transform="translate(701.625,711.9375)"/>
<path d="M0 0 C2.041 3.062 3.056 5.808 4.219 9.258 C4.868 11.198 4.868 11.198 7 12 C6.34 13.65 5.68 15.3 5 17 C4.34 17 3.68 17 3 17 C3 18.65 3 20.3 3 22 C2.67 22 2.34 22 2 22 C2.023 21.203 2.046 20.407 2.07 19.586 C2.167 13.925 2.13 9.325 0 4 C0 2.68 0 1.36 0 0 Z " fill="#A09A92" transform="translate(308,788)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.169 1.916 1.335 3.833 1.5 5.75 C1.593 6.817 1.686 7.885 1.781 8.984 C1.951 11.318 2 13.66 2 16 C-0.31 16 -2.62 16 -5 16 C-5.33 15.34 -5.66 14.68 -6 14 C-4.35 13.67 -2.7 13.34 -1 13 C-0.67 8.71 -0.34 4.42 0 0 Z " fill="#C0BAB3" transform="translate(398,712)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-1.133 10.508 -1.133 10.508 -4.75 12.562 C-5.492 12.707 -6.235 12.851 -7 13 C-8.097 9.71 -7.8 8.287 -7 5 C-6.34 5 -5.68 5 -5 5 C-5 4.34 -5 3.68 -5 3 C-4.34 3.33 -3.68 3.66 -3 4 C-3.66 5.65 -4.32 7.3 -5 9 C-4.34 9 -3.68 9 -3 9 C-2.01 6.03 -1.02 3.06 0 0 Z " fill="#A7A197" transform="translate(692,715)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C1.41 6.234 -0.276 12.163 -3 18 C-4.631 13.937 -3.3 10.799 -2.062 6.75 C-1.682 5.487 -1.302 4.223 -0.91 2.922 C-0.61 1.958 -0.309 0.993 0 0 Z " fill="#EDE8DE" transform="translate(321,786)"/>
<path d="M0 0 C1.943 0.077 1.943 0.077 3.926 0.156 C5.289 0.221 6.652 0.286 8.016 0.352 C9.394 0.41 10.773 0.467 12.152 0.523 C15.524 0.665 18.895 0.816 22.266 0.977 C22.266 1.307 22.266 1.637 22.266 1.977 C11.376 1.977 0.486 1.977 -10.734 1.977 C-6.426 -0.178 -4.698 -0.206 0 0 Z " fill="#7D7975" transform="translate(313.734375,565.0234375)"/>
<path d="M0 0 C-0.33 1.98 -0.66 3.96 -1 6 C-5.95 5.67 -10.9 5.34 -16 5 C-16 4.67 -16 4.34 -16 4 C-15.325 3.963 -14.649 3.925 -13.953 3.887 C-13.061 3.821 -12.169 3.755 -11.25 3.688 C-10.368 3.629 -9.487 3.571 -8.578 3.512 C-5.174 2.836 -3.329 0 0 0 Z " fill="#DDD6CC" transform="translate(569,718)"/>
<path d="M0 0 C1.908 3.175 2.161 5.106 1.625 8.75 C1.514 9.549 1.403 10.348 1.289 11.172 C1.194 11.775 1.098 12.378 1 13 C2.98 13 4.96 13 7 13 C7 13.33 7 13.66 7 14 C4.36 14 1.72 14 -1 14 C-1.027 11.854 -1.046 9.708 -1.062 7.562 C-1.074 6.368 -1.086 5.173 -1.098 3.941 C-1 1 -1 1 0 0 Z " fill="#9C9890" transform="translate(782,708)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-10.56 1 -21.12 1 -32 1 C-32 0.67 -32 0.34 -32 0 C-27.98 -0.198 -23.959 -0.382 -19.938 -0.562 C-18.804 -0.619 -17.67 -0.675 -16.502 -0.732 C-10.844 -0.98 -5.567 -1.091 0 0 Z " fill="#7F7A75" transform="translate(440,566)"/>
<path d="M0 0 C1.018 0.005 2.035 0.009 3.084 0.014 C5.577 0.025 8.07 0.042 10.562 0.062 C10.892 0.722 11.222 1.383 11.562 2.062 C1.662 2.062 -8.238 2.062 -18.438 2.062 C-18.438 1.732 -18.438 1.403 -18.438 1.062 C-12.27 -0.036 -6.253 -0.053 0 0 Z " fill="#757370" transform="translate(244.4375,564.9375)"/>
<path d="M0 0 C3.465 1.98 3.465 1.98 7 4 C7 4.99 7 5.98 7 7 C8.764 8.688 8.764 8.688 11 10.188 C12.114 10.982 12.114 10.982 13.25 11.793 C13.827 12.191 14.405 12.59 15 13 C14.01 13.495 14.01 13.495 13 14 C7.928 10.451 3.656 7.027 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9B6230" transform="translate(768,202)"/>
<path d="M0 0 C2.5 1.375 2.5 1.375 5 3 C5 3.66 5 4.32 5 5 C5.66 5 6.32 5 7 5 C7 6.65 7 8.3 7 10 C3.174 8.549 1.777 6.642 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D9D3C9" transform="translate(708,803)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.01 4.99 0.02 5.98 -1 7 C-1.66 6.01 -2.32 5.02 -3 4 C-4.964 3.423 -4.964 3.423 -7.125 3.312 C-8.404 3.209 -9.683 3.106 -11 3 C-11 2.67 -11 2.34 -11 2 C-5.555 1.01 -5.555 1.01 0 0 Z " fill="#C6C1B8" transform="translate(682,785)"/>
<path d="M0 0 C10.23 0 20.46 0 31 0 C31 0.33 31 0.66 31 1 C20.77 1 10.54 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7F7975" transform="translate(473,566)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.33 1.32 6.66 2.64 7 4 C5.125 5.062 5.125 5.062 3 6 C2.34 5.67 1.68 5.34 1 5 C0.375 2.438 0.375 2.438 0 0 Z " fill="#B8B1A9" transform="translate(477,808)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 5.62 2 10.24 2 15 C1.34 14.67 0.68 14.34 0 14 C0 11.03 0 8.06 0 5 C-0.66 5.66 -1.32 6.32 -2 7 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#98938B" transform="translate(671,697)"/>
<path d="M0 0 C3.008 1.504 3.307 3.86 4.402 6.922 C5.101 9.351 5.236 11.486 5 14 C4.34 13.67 3.68 13.34 3 13 C2.109 10.185 1.462 7.341 0.781 4.469 C0.523 3.654 0.266 2.839 0 2 C-0.66 1.67 -1.32 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#EAE2D8" transform="translate(744,785)"/>
<path d="M0 0 C2.119 2.044 3.99 4.14 5.75 6.5 C6.199 7.088 6.647 7.676 7.109 8.281 C7.403 8.848 7.697 9.416 8 10 C7.67 10.99 7.34 11.98 7 13 C5.661 11.338 4.329 9.67 3 8 C2.423 7.319 1.845 6.639 1.25 5.938 C0 4 0 4 0 0 Z " fill="#E7E0D6" transform="translate(700,791)"/>
<path d="M0 0 C2.523 1.974 2.993 2.967 3.688 6.188 C3.791 7.116 3.894 8.044 4 9 C4.66 9 5.32 9 6 9 C6 9.66 6 10.32 6 11 C5.01 11 4.02 11 3 11 C2.3 9.736 1.616 8.463 0.938 7.188 C0.555 6.48 0.172 5.772 -0.223 5.043 C-1 3 -1 3 0 0 Z " fill="#F4EFE5" transform="translate(655,707)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C11.34 0.66 10.68 1.32 10 2 C9.34 1.67 8.68 1.34 8 1 C8 1.66 8 2.32 8 3 C5.36 3 2.72 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EEE7DF" transform="translate(504,708)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 5.94 2 11.88 2 18 C1.01 17.67 0.02 17.34 -1 17 C-0.835 16.051 -0.67 15.102 -0.5 14.125 C0.185 9.424 0.086 4.74 0 0 Z " fill="#AFA99F" transform="translate(777,694)"/>
<path d="M0 0 C1.31 -0.005 2.619 -0.01 3.969 -0.016 C7.5 0.25 7.5 0.25 11.5 2.25 C3.91 2.25 -3.68 2.25 -11.5 2.25 C-7.131 0.065 -4.819 -0.019 0 0 Z " fill="#7B7773" transform="translate(290.5,564.75)"/>
<path d="M0 0 C2.249 -0.081 4.5 -0.139 6.75 -0.188 C8.003 -0.222 9.256 -0.257 10.547 -0.293 C14.141 0.012 15.447 0.544 18 3 C11.717 3.233 6.12 2.405 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A66938" transform="translate(230,245)"/>
<path d="M0 0 C-1.75 3.875 -1.75 3.875 -4 5 C-5.874 5.07 -7.75 5.084 -9.625 5.062 C-10.628 5.053 -11.631 5.044 -12.664 5.035 C-13.435 5.024 -14.206 5.012 -15 5 C-15.33 4.34 -15.66 3.68 -16 3 C-15.112 3.052 -15.112 3.052 -14.207 3.105 C-8.662 3.295 -4.754 0 0 0 Z " fill="#ECE5DC" transform="translate(545,807)"/>
<path d="M0 0 C0.866 0.33 1.732 0.66 2.625 1 C8.376 2.704 12.437 2.119 18 0 C16.562 2 16.562 2 14 4 C9.034 4.797 4.501 4.323 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E9E3D9" transform="translate(231,808)"/>
<path d="M0 0 C6.401 -0.512 9.98 -0.119 15 4 C14.67 4.66 14.34 5.32 14 6 C13.01 5.319 12.02 4.639 11 3.938 C6.68 1.42 3.79 1.992 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#EDE8DE" transform="translate(531,786)"/>
<path d="M0 0 C0.773 2.982 1.412 5.976 2 9 C1.01 9.495 1.01 9.495 0 10 C0 9.34 0 8.68 0 8 C-1.65 8 -3.3 8 -5 8 C-5.495 5.525 -5.495 5.525 -6 3 C-5.34 4.32 -4.68 5.64 -4 7 C-3.01 7 -2.02 7 -1 7 C-1.66 5.02 -2.32 3.04 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#A29D94" transform="translate(270,718)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 4.96 2.34 8.92 2 13 C1.34 13 0.68 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#ACA69C" transform="translate(397,692)"/>
<path d="M0 0 C1.98 0.495 1.98 0.495 4 1 C4.934 4.01 5.044 4.867 4 8 C2.68 7.67 1.36 7.34 0 7 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#D6D0C6" transform="translate(295,805)"/>
<path d="M0 0 C4.29 0 8.58 0 13 0 C12.67 1.32 12.34 2.64 12 4 C11.34 4 10.68 4 10 4 C10 3.34 10 2.68 10 2 C6.7 2 3.4 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A49F96" transform="translate(737,804)"/>
<path d="M0 0 C4.29 0.99 8.58 1.98 13 3 C8.443 5.278 6.85 4.988 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#F1EBE2" transform="translate(799,720)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.33 0.66 5.66 1.32 6 2 C5.34 2 4.68 2 4 2 C4.351 2.763 4.701 3.526 5.062 4.312 C6 7 6 7 5 10 C4.732 9.196 4.464 8.391 4.188 7.562 C3.796 6.717 3.404 5.871 3 5 C2.01 4.67 1.02 4.34 0 4 C0 2.68 0 1.36 0 0 Z " fill="#BAB4AB" transform="translate(562,692)"/>
<path d="M0 0 C4.869 0.596 9.328 1.57 14 3 C12.438 4.125 12.438 4.125 10 5 C6.25 4.125 6.25 4.125 3 3 C3 2.34 3 1.68 3 1 C2.01 0.67 1.02 0.34 0 0 Z " fill="#DB853E" transform="translate(213,240)"/>
<path d="M0 0 C0.843 0.476 0.843 0.476 1.703 0.961 C4.152 2.069 6.084 2.39 8.75 2.625 C9.549 2.7 10.348 2.775 11.172 2.852 C11.775 2.901 12.378 2.95 13 3 C11 5 11 5 7.625 5.375 C4 5 4 5 1.562 3.062 C0 1 0 1 0 0 Z " fill="#A09A93" transform="translate(666,806)"/>
<path d="M0 0 C2.5 1.25 2.5 1.25 5 3 C5 4.32 5 5.64 5 7 C3.35 6.67 1.7 6.34 0 6 C-0.382 4.344 -0.714 2.675 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CEC8BF" transform="translate(572,803)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.67 2.97 0.34 5.94 0 9 C-0.99 9.33 -1.98 9.66 -3 10 C-3.125 4.25 -3.125 4.25 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CAC5BC" transform="translate(318,802)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-8.25 1 -16.5 1 -25 1 C-25 0.67 -25 0.34 -25 0 C-22.146 -0.196 -19.292 -0.382 -16.438 -0.562 C-15.638 -0.619 -14.838 -0.675 -14.014 -0.732 C-9.158 -1.03 -4.789 -0.868 0 0 Z " fill="#7F7975" transform="translate(584,566)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.76 2.322 -0.493 3.632 -1.75 4.938 C-2.446 5.668 -3.142 6.399 -3.859 7.152 C-6.006 9.005 -7.235 9.608 -10 10 C-3.85 2.517 -3.85 2.517 0 0 Z " fill="#F39B37" transform="translate(534,305)"/>
<path d="M0 0 C1.675 0.286 3.344 0.618 5 1 C5.33 2.65 5.66 4.3 6 6 C5.34 6 4.68 6 4 6 C3.67 6.66 3.34 7.32 3 8 C3 6.02 3 4.04 3 2 C0.669 2.959 0.669 2.959 -1 6 C-1.043 4.334 -1.041 2.666 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A39E97" transform="translate(576,784)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.97 1 5.94 1 9 C-0.32 8.34 -1.64 7.68 -3 7 C-3.33 6.01 -3.66 5.02 -4 4 C-2.68 3.34 -1.36 2.68 0 2 C0 1.34 0 0.68 0 0 Z M-2 3 C-1 5 -1 5 -1 5 Z " fill="#BE7533" transform="translate(694,184)"/>
<path d="M0 0 C3.288 3.11 5.837 6.016 8 10 C6.25 9.875 6.25 9.875 4 9 C0 3.889 0 3.889 0 0 Z " fill="#EBE4DA" transform="translate(433,803)"/>
<path d="M0 0 C6.75 0.75 6.75 0.75 9 3 C9.125 6.125 9.125 6.125 9 9 C8.67 9 8.34 9 8 9 C8 7.35 8 5.7 8 4 C5.03 3.505 5.03 3.505 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#E2DCD2" transform="translate(771,798)"/>
<path d="M0 0 C-0.33 1.65 -0.66 3.3 -1 5 C-1.33 4.34 -1.66 3.68 -2 3 C-2.598 3.33 -3.196 3.66 -3.812 4 C-6 5 -6 5 -9 5 C-9 4.34 -9 3.68 -9 3 C-2.25 0 -2.25 0 0 0 Z " fill="#F2EDE4" transform="translate(781,691)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-7.59 1 -15.18 1 -23 1 C-23 0.67 -23 0.34 -23 0 C-15.197 -1.211 -7.844 -0.725 0 0 Z " fill="#6E6D6B" transform="translate(831,566)"/>
<path d="M0 0 C-6.93 0 -13.86 0 -21 0 C-21 -0.33 -21 -0.66 -21 -1 C-4.62 -3.08 -4.62 -3.08 0 0 Z " fill="#7B7774" transform="translate(759,567)"/>
<path d="M0 0 C2 1 2 1 2.781 2.777 C3.018 3.49 3.256 4.203 3.5 4.938 C4.424 7.705 4.891 8.891 7 11 C6.67 11.66 6.34 12.32 6 13 C5.34 13 4.68 13 4 13 C3.327 11.399 2.662 9.794 2 8.188 C1.629 7.294 1.257 6.401 0.875 5.48 C0 3 0 3 0 0 Z " fill="#A3642E" transform="translate(495,165)"/>
<path d="M0 0 C2.679 1.339 2.75 2.517 3.688 5.312 C3.959 6.092 4.231 6.872 4.512 7.676 C5.038 10.183 4.818 11.601 4 14 C3.353 12.396 2.708 10.792 2.062 9.188 C1.523 7.848 1.523 7.848 0.973 6.48 C0.264 4.673 -0.386 2.842 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#928E87" transform="translate(743,788)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.66 1.98 3.32 3.96 4 6 C3.34 6 2.68 6 2 6 C2 6.99 2 7.98 2 9 C1.34 9 0.68 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#EBE3D7" transform="translate(707,706)"/>
<path d="M0 0 C2.978 0.331 4.814 0.874 7.312 2.562 C9.787 6.137 9.286 8.711 9 13 C8.67 13 8.34 13 8 13 C7.732 11.928 7.464 10.855 7.188 9.75 C5.642 4.87 4.23 2.82 0 0 Z " fill="#8A8780" transform="translate(813,704)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.28 1 10.56 1 16 C-2 12 -2 12 -1.855 8.984 C-1.635 7.917 -1.415 6.85 -1.188 5.75 C-0.868 4.134 -0.868 4.134 -0.543 2.484 C-0.364 1.665 -0.185 0.845 0 0 Z " fill="#938E87" transform="translate(420,797)"/>
<path d="M0 0 C3.394 3.126 5.606 5.565 7 10 C4.625 8.938 4.625 8.938 2 7 C0.688 3.312 0.688 3.312 0 0 Z " fill="#A59F97" transform="translate(564,789)"/>
<path d="M0 0 C0.598 0.639 1.196 1.279 1.812 1.938 C3.908 4.203 3.908 4.203 7 5 C7.33 4.34 7.66 3.68 8 3 C8 4.32 8 5.64 8 7 C7.01 7.495 7.01 7.495 6 8 C1.125 3.375 1.125 3.375 0 0 Z " fill="#A69F96" transform="translate(335,720)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C-3.75 11 -3.75 11 -6 11 C-6 10.34 -6 9.68 -6 9 C-2.25 7.875 -2.25 7.875 0 9 C0 6.03 0 3.06 0 0 Z " fill="#9E9A93" transform="translate(777,712)"/>
<path d="M0 0 C3.465 1.485 3.465 1.485 7 3 C6.67 3.99 6.34 4.98 6 6 C3.03 5.01 0.06 4.02 -3 3 C-2.01 2.67 -1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EBE5DC" transform="translate(507,691)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C4.062 1.812 4.062 1.812 5 4 C4.67 4.99 4.34 5.98 4 7 C3.01 6.67 2.02 6.34 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#F1EBE1" transform="translate(585,691)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-1.3 3.64 -4.6 6.28 -8 9 C-8.66 8.67 -9.32 8.34 -10 8 C-8.711 6.852 -7.418 5.706 -6.125 4.562 C-5.046 3.605 -5.046 3.605 -3.945 2.629 C-2 1 -2 1 0 0 Z " fill="#F6A235" transform="translate(764,125)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C2.32 4.67 3.64 4.34 5 4 C4.67 5.32 4.34 6.64 4 8 C2.68 8 1.36 8 0 8 C-1.125 5.688 -1.125 5.688 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#A19D97" transform="translate(405,808)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C6.125 6.625 6.125 6.625 5 10 C4.34 7.69 3.68 5.38 3 3 C2.34 3 1.68 3 1 3 C0.67 3.66 0.34 4.32 0 5 C0 3.35 0 1.7 0 0 Z " fill="#97918A" transform="translate(639,789)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.99 3 2.98 3 4 C3.66 4.33 4.32 4.66 5 5 C3.35 5.33 1.7 5.66 0 6 C-0.66 4.68 -1.32 3.36 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D7D1C8" transform="translate(468,714)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.625 4.062 2.625 4.062 3 7 C1.68 7.33 0.36 7.66 -1 8 C-0.67 6.68 -0.34 5.36 0 4 C-0.66 4 -1.32 4 -2 4 C-2 3.01 -2 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#A9A39A" transform="translate(701,696)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C5.67 2.66 5.34 3.32 5 4 C3.68 3.34 2.36 2.68 1 2 C1 3.65 1 5.3 1 7 C0.34 7 -0.32 7 -1 7 C-1.043 5 -1.041 3 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9C978E" transform="translate(449,689)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.274 4.713 -1.614 6.377 -3 8 C-3.709 9.311 -4.395 10.638 -5 12 C-5.33 11.01 -5.66 10.02 -6 9 C-4.785 6.738 -4.785 6.738 -3.062 4.312 C-2.497 3.504 -1.931 2.696 -1.348 1.863 C-0.903 1.248 -0.458 0.634 0 0 Z " fill="#F6A336" transform="translate(747,141)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.625 5 -0.625 5 -3 7 C-6.25 7.25 -6.25 7.25 -9 7 C-5.375 4 -5.375 4 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#9D9890" transform="translate(246,804)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C5.67 2.66 5.34 3.32 5 4 C3.35 3.67 1.7 3.34 0 3 C-0.33 4.32 -0.66 5.64 -1 7 C-1.33 6.34 -1.66 5.68 -2 5 C-1.062 2.375 -1.062 2.375 0 0 Z " fill="#9F9991" transform="translate(698,688)"/>
<path d="M0 0 C2.97 1.98 2.97 1.98 6 4 C4.25 8.875 4.25 8.875 2 10 C2.34 8.855 2.34 8.855 2.688 7.688 C2.791 6.801 2.894 5.914 3 5 C1.625 3.291 1.625 3.291 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A86932" transform="translate(361,306)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 2.98 1.68 4.96 1 7 C0.01 7 -0.98 7 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z " fill="#DCD5CB" transform="translate(631,806)"/>
<path d="M0 0 C1.114 0.557 1.114 0.557 2.25 1.125 C1.92 2.115 1.59 3.105 1.25 4.125 C-1.39 3.135 -4.03 2.145 -6.75 1.125 C-4.021 -0.694 -3.214 -1.023 0 0 Z " fill="#DCD5CB" transform="translate(245.75,798.875)"/>
<path d="M0 0 C0.99 1.32 1.98 2.64 3 4 C2.34 5.32 1.68 6.64 1 8 C0.01 7.34 -0.98 6.68 -2 6 C-1.34 4.02 -0.68 2.04 0 0 Z " fill="#EFE8DD" transform="translate(569,711)"/>
<path d="M0 0 C1 3 1 3 0.062 5.188 C-0.463 6.085 -0.463 6.085 -1 7 C-1.66 7 -2.32 7 -3 7 C-3.33 6.01 -3.66 5.02 -4 4 C-2.68 2.68 -1.36 1.36 0 0 Z " fill="#EDE6DA" transform="translate(742,694)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.188 3.438 1.188 3.438 0 7 C-0.99 7.33 -1.98 7.66 -3 8 C-1.125 1.125 -1.125 1.125 0 0 Z " fill="#F5F0E6" transform="translate(670,694)"/>
<path d="M0 0 C2.688 0.312 2.688 0.312 5 1 C4.34 2.32 3.68 3.64 3 5 C2.67 4.34 2.34 3.68 2 3 C1.01 2.67 0.02 2.34 -1 2 C-1.33 2.99 -1.66 3.98 -2 5 C-2.33 4.34 -2.66 3.68 -3 3 C-1.812 1.438 -1.812 1.438 0 0 Z " fill="#9C9790" transform="translate(743,689)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-4.62 2 -9.24 2 -14 2 C-14 1.67 -14 1.34 -14 1 C-9.237 0.047 -4.833 -0.083 0 0 Z " fill="#7F7975" transform="translate(472,565)"/>
<path d="M0 0 C0 3.589 -0.664 4.109 -3 6.688 C-3.557 7.31 -4.114 7.933 -4.688 8.574 C-5.121 9.045 -5.554 9.515 -6 10 C-6 6.411 -5.336 5.891 -3 3.312 C-2.443 2.69 -1.886 2.067 -1.312 1.426 C-0.879 0.955 -0.446 0.485 0 0 Z " fill="#F09E35" transform="translate(279,147)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 3.31 2 5.62 2 8 C0.68 7.34 -0.64 6.68 -2 6 C-1.34 5.67 -0.68 5.34 0 5 C0 3.35 0 1.7 0 0 Z " fill="#E5DFD6" transform="translate(577,805)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.461 2.647 4 3.894 4 7 C3.01 7 2.02 7 1 7 C0.67 4.69 0.34 2.38 0 0 Z " fill="#E4DED5" transform="translate(751,805)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C1.66 6.33 2.32 6.66 3 7 C2.34 8.32 1.68 9.64 1 11 C0.34 10.67 -0.32 10.34 -1 10 C-0.67 6.7 -0.34 3.4 0 0 Z " fill="#D5CEC4" transform="translate(545,797)"/>
<path d="M0 0 C1.32 1.65 2.64 3.3 4 5 C1 7 1 7 -1.188 6.625 C-1.786 6.419 -2.384 6.212 -3 6 C-2.01 5.67 -1.02 5.34 0 5 C0 3.35 0 1.7 0 0 Z " fill="#AEA8A0" transform="translate(419,720)"/>
<path d="M0 0 C1.65 1.65 3.3 3.3 5 5 C4.67 5.99 4.34 6.98 4 8 C2.68 7.34 1.36 6.68 0 6 C0 4.02 0 2.04 0 0 Z " fill="#E5DED3" transform="translate(646,693)"/>
<path d="M0 0 C3.829 1.453 5.121 3.429 7 7 C3.634 5.557 1.49 3.667 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B57233" transform="translate(341,323)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C2.767 1.383 3.098 2.702 3.438 4.062 C2.118 4.062 0.798 4.062 -0.562 4.062 C-0.562 3.403 -0.562 2.742 -0.562 2.062 C-1.553 1.732 -2.543 1.403 -3.562 1.062 C-2.562 0.062 -2.562 0.062 0 0 Z " fill="#CAC6C0" transform="translate(258.5625,715.9375)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4.33 2.32 4.66 3.64 5 5 C4.01 5 3.02 5 2 5 C1.34 3.35 0.68 1.7 0 0 Z " fill="#F5EFE5" transform="translate(328,707)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10.33 0.66 10.66 1.32 11 2 C7.04 2 3.08 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#7F7974" transform="translate(515,565)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C-0.756 9.765 -1.206 5.227 0 0 Z " fill="#E9963B" transform="translate(691,170)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.66 9 1.32 9 2 C5.7 2.33 2.4 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#A96A32" transform="translate(526,119)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#BAB5AE" transform="translate(555,788)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.96 1 7.92 1 12 C0.67 12 0.34 12 0 12 C0 9.03 0 6.06 0 3 C-0.99 2.67 -1.98 2.34 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C1BCB7" transform="translate(275,691)"/>
<path d="M0 0 C1.625 -0.054 3.25 -0.093 4.875 -0.125 C5.78 -0.148 6.685 -0.171 7.617 -0.195 C10 0 10 0 12 2 C7.71 2 3.42 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#7F7974" transform="translate(546,565)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-4.62 1 -9.24 1 -14 1 C-14 0.67 -14 0.34 -14 0 C-8.977 -1.13 -5.021 -0.779 0 0 Z " fill="#7F7A76" transform="translate(407,566)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C15 0.33 15 0.66 15 1 C10.05 1 5.1 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#716F6C" transform="translate(209,566)"/>
<path d="M0 0 C4.62 0 9.24 0 14 0 C14 0.33 14 0.66 14 1 C9.38 1 4.76 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#73716E" transform="translate(793,566)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C0.67 10 0.34 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#B8B4AE" transform="translate(691,792)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.33 10 0.66 10 1 C6.7 1 3.4 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#797672" transform="translate(268,566)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C7 1.33 7 1.66 7 2 C4.69 2 2.38 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7E7874" transform="translate(634,565)"/>
<path d="M0 0 C2.97 0.33 5.94 0.66 9 1 C9 1.33 9 1.66 9 2 C6.03 2 3.06 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DC8F3A" transform="translate(789,112)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.97 1 5.94 1 9 C0.67 9 0.34 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#C8C3BC" transform="translate(641,696)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#757370" transform="translate(783,566)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7E7974" transform="translate(599,566)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7E7974" transform="translate(527,566)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#787572" transform="translate(257,566)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.67 1.66 4.34 2.32 4 3 C3.34 3 2.68 3 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#E08E3E" transform="translate(816,197)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#B1ADA7" transform="translate(256,792)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.33 0.66 5.66 1.32 6 2 C3.03 1.505 3.03 1.505 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BCB7B0" transform="translate(535,790)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.34 0.66 4.68 1.32 4 2 C2.68 2 1.36 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CCC7C0" transform="translate(248,717)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 1.32 2.66 2.64 3 4 C2.01 3.67 1.02 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C8C2BB" transform="translate(255,706)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#CCC7C0" transform="translate(359,704)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#C7C2BC" transform="translate(297,696)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.33 8 0.66 8 1 C5.36 1 2.72 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7A7774" transform="translate(760,566)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.33 8 0.66 8 1 C5.36 1 2.72 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#807A74" transform="translate(449,566)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C9C4BD" transform="translate(484,705)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.98 2 -2.96 2 -5 2 C-5 1.67 -5 1.34 -5 1 C-3.35 0.67 -1.7 0.34 0 0 Z " fill="#787572" transform="translate(775,565)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.33 7 0.66 7 1 C4.69 1 2.38 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#6E6C6A" transform="translate(201,566)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C5.01 1.33 4.02 1.66 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#CF8242" transform="translate(305,366)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.67 -0.32 4.34 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#E18B3F" transform="translate(625,239)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#E8933E" transform="translate(625,217)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.01 0.66 4.02 1.32 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#E18F3D" transform="translate(808,197)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.99 2.34 1.98 2 3 C1.34 2.67 0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E29138" transform="translate(752,128)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.67 -0.32 4.34 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#B3AFA8" transform="translate(555,808)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#B4AFA8" transform="translate(602,802)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C7C3BD" transform="translate(581,700)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.33 6 0.66 6 1 C4.02 1 2.04 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7F7974" transform="translate(592,566)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.68 1.33 1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7F7974" transform="translate(505,565)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#D38542" transform="translate(699,386)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.01 4.495 0.01 4.495 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#E18D3E" transform="translate(507,342)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.33 6 0.66 6 1 C4.02 1 2.04 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DF8B3F" transform="translate(439,323)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.99 1.34 2.98 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#CF8440" transform="translate(176,214)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#DF8D3D" transform="translate(204,192)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.01 4.495 0.01 4.495 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#EA973C" transform="translate(731,176)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#B9B4AE" transform="translate(602,795)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B7B3AE" transform="translate(492,785)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 2.485 1.01 2.485 0 4 C0 2.68 0 1.36 0 0 Z " fill="#CCC6C0" transform="translate(421,711)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C7C2BD" transform="translate(612,699)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7F7975" transform="translate(619,566)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BD7946" transform="translate(427,423)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C2.68 1.67 1.36 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BE7B4A" transform="translate(542,418)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#DD8B3F" transform="translate(689,368)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-0.66 2.34 -1.32 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#E08B3C" transform="translate(604,349)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.67 0.66 3.34 1.32 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#DD8B3D" transform="translate(332,340)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E28D3D" transform="translate(576,323)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#E9943D" transform="translate(652,315)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 2.485 1.01 2.485 0 4 C0 2.68 0 1.36 0 0 Z " fill="#D58A41" transform="translate(275,313)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E1903D" transform="translate(332,291)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.32 1.67 -1.64 1.34 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#E4903E" transform="translate(426,290)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#CF853E" transform="translate(168,179)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#B0ABA6" transform="translate(719,811)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B3AFA8" transform="translate(742,807)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B7B1AC" transform="translate(428,806)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#B5B0A8" transform="translate(761,797)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C0B9B2" transform="translate(563,797)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BEB9B3" transform="translate(632,793)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BAB5AD" transform="translate(512,790)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#BDB6B1" transform="translate(689,789)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BBB7B0" transform="translate(367,784)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D0CAC4" transform="translate(589,719)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CDC7C1" transform="translate(641,712)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CAC5BF" transform="translate(612,708)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CBC4BE" transform="translate(493,708)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#C9C3BD" transform="translate(253,708)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CBC6BF" transform="translate(493,704)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CAC5BF" transform="translate(604,700)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#777472" transform="translate(778,566)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#807A75" transform="translate(444,566)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#7E7A75" transform="translate(337,566)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#C07A46" transform="translate(436,423)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B77848" transform="translate(730,420)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CC8247" transform="translate(652,412)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#DA8942" transform="translate(652,387)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#DE8B3F" transform="translate(690,370)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D88840" transform="translate(405,353)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#E8933D" transform="translate(652,339)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#DF8C3E" transform="translate(592,329)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#E28F3E" transform="translate(316,319)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#DF8E3C" transform="translate(436,290)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CB8243" transform="translate(812,247)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D68941" transform="translate(655,240)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#E08C3F" transform="translate(529,239)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D58640" transform="translate(322,237)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#DE8C3F" transform="translate(655,226)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DB8A41" transform="translate(305,221)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CB8240" transform="translate(178,218)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#E9953E" transform="translate(732,193)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#E4913C" transform="translate(766,187)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D4893F" transform="translate(169,189)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E9963B" transform="translate(500,168)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#ED9739" transform="translate(576,155)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#E9933A" transform="translate(786,147)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#EA9639" transform="translate(577,141)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#E69439" transform="translate(744,138)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#D98D3B" transform="translate(802,113)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#AEAAA5" transform="translate(604,815)"/>
<path d="" fill="#B3AEA9" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#ADA9A4" transform="translate(758,811)"/>
<path d="" fill="#B8B4AE" transform="translate(0,0)"/>
<path d="" fill="#B6B1AA" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B7B1AD" transform="translate(427,804)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B6B0AB" transform="translate(307,804)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B3AEA7" transform="translate(242,803)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B9B4AF" transform="translate(272,801)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BBB8AF" transform="translate(669,798)"/>
<path d="" fill="#BDBAB0" transform="translate(0,0)"/>
<path d="" fill="#BFBAB4" transform="translate(0,0)"/>
<path d="" fill="#B9B4AF" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B3B0A8" transform="translate(771,790)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BBB7AF" transform="translate(671,790)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C0BAB3" transform="translate(430,790)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BEB8B1" transform="translate(380,790)"/>
<path d="" fill="#BCB7B1" transform="translate(0,0)"/>
<path d="" fill="#BFB9B2" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BEBAB4" transform="translate(411,784)"/>
<path d="" fill="#C7C1BB" transform="translate(0,0)"/>
<path d="" fill="#CCC6BF" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CDC7BF" transform="translate(330,719)"/>
<path d="" fill="#D3CEC7" transform="translate(0,0)"/>
<path d="" fill="#CAC7C0" transform="translate(0,0)"/>
<path d="" fill="#C2BEB7" transform="translate(0,0)"/>
<path d="" fill="#CCC6BF" transform="translate(0,0)"/>
<path d="" fill="#D1CAC3" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CEC7BF" transform="translate(456,706)"/>
<path d="" fill="#C4C1BA" transform="translate(0,0)"/>
<path d="" fill="#C6C1BC" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CCC6BF" transform="translate(379,704)"/>
<path d="" fill="#C6C0BA" transform="translate(0,0)"/>
<path d="" fill="#CAC7BE" transform="translate(0,0)"/>
<path d="" fill="#C6C0B9" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C2BDB8" transform="translate(550,688)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#696865" transform="translate(834,566)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7D7A75" transform="translate(690,566)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7F7974" transform="translate(538,566)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6A6967" transform="translate(187,566)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B87646" transform="translate(443,423)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BA7949" transform="translate(311,423)"/>
<path d="" fill="#D18445" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C97E45" transform="translate(707,404)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C87F47" transform="translate(521,402)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D08444" transform="translate(512,391)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CD8145" transform="translate(378,389)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C78047" transform="translate(275,384)"/>
<path d="" fill="#DB8840" transform="translate(0,0)"/>
<path d="" fill="#D88740" transform="translate(0,0)"/>
<path d="" fill="#D48742" transform="translate(0,0)"/>
<path d="" fill="#DA8840" transform="translate(0,0)"/>
<path d="" fill="#DB8A3E" transform="translate(0,0)"/>
<path d="" fill="#DF8A3D" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E08B3D" transform="translate(347,344)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DD8C3F" transform="translate(328,339)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E5903C" transform="translate(636,336)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DE8A3E" transform="translate(445,324)"/>
<path d="" fill="#E9953C" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E4923D" transform="translate(652,300)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#E2903C" transform="translate(448,291)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DF903F" transform="translate(304,291)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E08F3D" transform="translate(434,289)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D18644" transform="translate(780,247)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DA8A41" transform="translate(534,246)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D68941" transform="translate(379,246)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DF8D43" transform="translate(469,240)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DC8B41" transform="translate(749,231)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DC8D41" transform="translate(744,229)"/>
<path d="" fill="#E7923F" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DB8A40" transform="translate(504,221)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DD8C41" transform="translate(492,220)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#E08D3E" transform="translate(483,212)"/>
<path d="" fill="#CA8442" transform="translate(0,0)"/>
<path d="" fill="#E08E3E" transform="translate(0,0)"/>
<path d="" fill="#E9943B" transform="translate(0,0)"/>
<path d="" fill="#E5923E" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E3913B" transform="translate(794,168)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#EA9438" transform="translate(335,158)"/>
<path d="" fill="#EE983A" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E5933B" transform="translate(297,154)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E7943C" transform="translate(256,152)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E9943A" transform="translate(343,150)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#E6923C" transform="translate(249,149)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#EA953B" transform="translate(368,147)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#E29139" transform="translate(406,129)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#E5933B" transform="translate(576,123)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D68A3A" transform="translate(199,122)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DC8E39" transform="translate(256,115)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D98D3A" transform="translate(216,115)"/>
<path d="" fill="#ABA8A2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2ADA7" transform="translate(628,815)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ADAAA4" transform="translate(621,815)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1ADA8" transform="translate(528,815)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B0ABA6" transform="translate(388,814)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1ACA9" transform="translate(528,813)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2AEA9" transform="translate(690,811)"/>
<path d="" fill="#B4B0A9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEA9A4" transform="translate(757,809)"/>
<path d="" fill="#B4B0AA" transform="translate(0,0)"/>
<path d="" fill="#B7B3AB" transform="translate(0,0)"/>
<path d="" fill="#B7B1AB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3AEA7" transform="translate(302,807)"/>
<path d="" fill="#B8B3AD" transform="translate(0,0)"/>
<path d="" fill="#B9B3AC" transform="translate(0,0)"/>
<path d="" fill="#BEB7B2" transform="translate(0,0)"/>
<path d="" fill="#C0B9B1" transform="translate(0,0)"/>
<path d="" fill="#BBB6B0" transform="translate(0,0)"/>
<path d="" fill="#BEB9B2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9A59E" transform="translate(232,799)"/>
<path d="" fill="#B9B4AD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BAB7B0" transform="translate(428,796)"/>
<path d="" fill="#B3AFA8" transform="translate(0,0)"/>
<path d="" fill="#BEB7B2" transform="translate(0,0)"/>
<path d="" fill="#B7B2AC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDB8B2" transform="translate(705,793)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BCB8B0" transform="translate(659,793)"/>
<path d="" fill="#BCB8B3" transform="translate(0,0)"/>
<path d="" fill="#C1BBB6" transform="translate(0,0)"/>
<path d="" fill="#BCB6AE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFBAB2" transform="translate(677,791)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2BCB5" transform="translate(378,791)"/>
<path d="" fill="#BEB8B1" transform="translate(0,0)"/>
<path d="" fill="#B8B4AC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8B3AC" transform="translate(768,790)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6B3AC" transform="translate(496,790)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9B5AE" transform="translate(346,790)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5B0A8" transform="translate(240,790)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDB7B3" transform="translate(411,788)"/>
<path d="" fill="#BDB8B0" transform="translate(0,0)"/>
<path d="" fill="#BCB8B1" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEABA4" transform="translate(780,785)"/>
<path d="" fill="#BBB6AF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDB9B3" transform="translate(526,785)"/>
<path d="" fill="#BBB6AF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3BDB6" transform="translate(542,784)"/>
<path d="" fill="#CBC4BD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDC7C1" transform="translate(378,727)"/>
<path d="" fill="#C9C4BD" transform="translate(0,0)"/>
<path d="" fill="#C5C1BB" transform="translate(0,0)"/>
<path d="" fill="#C7C2BA" transform="translate(0,0)"/>
<path d="" fill="#C9C3BC" transform="translate(0,0)"/>
<path d="" fill="#CBC5BE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6BFBA" transform="translate(245,724)"/>
<path d="" fill="#CAC4BC" transform="translate(0,0)"/>
<path d="" fill="#CCC6C0" transform="translate(0,0)"/>
<path d="" fill="#CEC8C1" transform="translate(0,0)"/>
<path d="" fill="#CCC8C1" transform="translate(0,0)"/>
<path d="" fill="#C9C4BD" transform="translate(0,0)"/>
<path d="" fill="#B7B4AE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBC5BF" transform="translate(488,721)"/>
<path d="" fill="#C4C1BA" transform="translate(0,0)"/>
<path d="" fill="#CFC8C1" transform="translate(0,0)"/>
<path d="" fill="#CBC7BF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFCAC4" transform="translate(563,717)"/>
<path d="" fill="#CBC6BF" transform="translate(0,0)"/>
<path d="" fill="#CAC3BD" transform="translate(0,0)"/>
<path d="" fill="#CFCAC3" transform="translate(0,0)"/>
<path d="" fill="#CBC5BD" transform="translate(0,0)"/>
<path d="" fill="#C9C5BF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8C3BC" transform="translate(539,713)"/>
<path d="" fill="#CAC5BF" transform="translate(0,0)"/>
<path d="" fill="#CEC9C1" transform="translate(0,0)"/>
<path d="" fill="#CDC7BF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8C3BE" transform="translate(538,709)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D0CAC4" transform="translate(335,709)"/>
<path d="" fill="#B1ADA7" transform="translate(0,0)"/>
<path d="" fill="#CFC9C0" transform="translate(0,0)"/>
<path d="" fill="#C8C3BC" transform="translate(0,0)"/>
<path d="" fill="#CEC8C1" transform="translate(0,0)"/>
<path d="" fill="#CDC9C2" transform="translate(0,0)"/>
<path d="" fill="#C7C2BB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBC5BF" transform="translate(514,705)"/>
<path d="" fill="#C7C3BD" transform="translate(0,0)"/>
<path d="" fill="#C9C3BE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4BEB8" transform="translate(732,701)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBC6BF" transform="translate(691,701)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CAC4BE" transform="translate(596,700)"/>
<path d="" fill="#CAC5BD" transform="translate(0,0)"/>
<path d="" fill="#C8C4BD" transform="translate(0,0)"/>
<path d="" fill="#C9C3BB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7C1BB" transform="translate(640,693)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C5C0B8" transform="translate(312,693)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDB9B4" transform="translate(748,692)"/>
<path d="" fill="#C4BFB9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEBAB5" transform="translate(272,690)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ADABA8" transform="translate(805,688)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1AEAB" transform="translate(802,688)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6B3AF" transform="translate(799,688)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0BEB8" transform="translate(585,688)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BAB7B2" transform="translate(273,688)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7D7875" transform="translate(311,569)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7F7974" transform="translate(587,566)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7F7B74" transform="translate(542,566)"/>
<path d="" fill="#7F7874" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7E7A76" transform="translate(342,566)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BE7948" transform="translate(580,423)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C57D49" transform="translate(574,423)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BA7A4A" transform="translate(308,423)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BF7B46" transform="translate(448,422)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B97A4B" transform="translate(304,422)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B57748" transform="translate(739,420)"/>
<path d="" fill="#C17C49" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C77E47" transform="translate(484,404)"/>
<path d="" fill="#D78845" transform="translate(0,0)"/>
<path d="" fill="#CF8242" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CB8145" transform="translate(427,390)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D38542" transform="translate(586,388)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CD8045" transform="translate(304,387)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D58541" transform="translate(697,385)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CF8141" transform="translate(298,384)"/>
<path d="" fill="#D38543" transform="translate(0,0)"/>
<path d="" fill="#D38544" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D38643" transform="translate(371,375)"/>
<path d="" fill="#DA8840" transform="translate(0,0)"/>
<path d="" fill="#D48542" transform="translate(0,0)"/>
<path d="" fill="#D78741" transform="translate(0,0)"/>
<path d="" fill="#DD8A3E" transform="translate(0,0)"/>
<path d="" fill="#DA8A43" transform="translate(0,0)"/>
<path d="" fill="#DB8B42" transform="translate(0,0)"/>
<path d="" fill="#D7873F" transform="translate(0,0)"/>
<path d="" fill="#D88741" transform="translate(0,0)"/>
<path d="" fill="#E8933E" transform="translate(0,0)"/>
<path d="" fill="#DE8940" transform="translate(0,0)"/>
<path d="" fill="#E08D3B" transform="translate(0,0)"/>
<path d="" fill="#DE8B3E" transform="translate(0,0)"/>
<path d="" fill="#E38E3F" transform="translate(0,0)"/>
<path d="" fill="#D8883E" transform="translate(0,0)"/>
<path d="" fill="#E08B40" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DF8A3E" transform="translate(405,351)"/>
<path d="" fill="#DD8D41" transform="translate(0,0)"/>
<path d="" fill="#D7863E" transform="translate(0,0)"/>
<path d="" fill="#E7933E" transform="translate(0,0)"/>
<path d="" fill="#E5933E" transform="translate(0,0)"/>
<path d="" fill="#DB8A40" transform="translate(0,0)"/>
<path d="" fill="#D18643" transform="translate(0,0)"/>
<path d="" fill="#DE8E3F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DC883D" transform="translate(317,336)"/>
<path d="" fill="#E08A3C" transform="translate(0,0)"/>
<path d="" fill="#DA8A3D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DE8B3F" transform="translate(588,327)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DE8A3D" transform="translate(420,326)"/>
<path d="" fill="#E48E3E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E08C3C" transform="translate(560,325)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E38E3F" transform="translate(449,325)"/>
<path d="" fill="#EA933C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E28C3D" transform="translate(562,324)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E28E3D" transform="translate(566,323)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DD893F" transform="translate(430,323)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E18F39" transform="translate(630,322)"/>
<path d="" fill="#E8913A" transform="translate(0,0)"/>
<path d="" fill="#E6943C" transform="translate(0,0)"/>
<path d="" fill="#E1903C" transform="translate(0,0)"/>
<path d="" fill="#DA8B3D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E08D3B" transform="translate(393,304)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E08E3E" transform="translate(404,297)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E08F3E" transform="translate(454,292)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E1913C" transform="translate(445,291)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D98941" transform="translate(348,247)"/>
<path d="" fill="#D18641" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DD8D41" transform="translate(624,245)"/>
<path d="" fill="#DB8C3F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D08344" transform="translate(260,244)"/>
<path d="" fill="#DB8841" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D78642" transform="translate(388,243)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D58741" transform="translate(275,236)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DD8C3F" transform="translate(752,233)"/>
<path d="" fill="#DE8D41" transform="translate(0,0)"/>
<path d="" fill="#E39040" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DD8C40" transform="translate(746,227)"/>
<path d="" fill="#CA8140" transform="translate(0,0)"/>
<path d="" fill="#E99541" transform="translate(0,0)"/>
<path d="" fill="#E18E40" transform="translate(0,0)"/>
<path d="" fill="#DA8740" transform="translate(0,0)"/>
<path d="" fill="#E1903F" transform="translate(0,0)"/>
<path d="" fill="#E4913C" transform="translate(0,0)"/>
<path d="" fill="#EC973F" transform="translate(0,0)"/>
<path d="" fill="#E9953F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E38E3B" transform="translate(780,212)"/>
<path d="" fill="#E48E3D" transform="translate(0,0)"/>
<path d="" fill="#E08C3E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D8873D" transform="translate(219,212)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DE8D3D" transform="translate(818,211)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DE8C3D" transform="translate(342,210)"/>
<path d="" fill="#E6913D" transform="translate(0,0)"/>
<path d="" fill="#EA953C" transform="translate(0,0)"/>
<path d="" fill="#E4913C" transform="translate(0,0)"/>
<path d="" fill="#EA943F" transform="translate(0,0)"/>
<path d="" fill="#E9933D" transform="translate(0,0)"/>
<path d="" fill="#CC8540" transform="translate(0,0)"/>
<path d="" fill="#E8963E" transform="translate(0,0)"/>
<path d="" fill="#E4903C" transform="translate(0,0)"/>
<path d="" fill="#EB963D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E8963D" transform="translate(730,187)"/>
<path d="" fill="#E08E3D" transform="translate(0,0)"/>
<path d="" fill="#E9963C" transform="translate(0,0)"/>
<path d="" fill="#EB993E" transform="translate(0,0)"/>
<path d="" fill="#E7963C" transform="translate(0,0)"/>
<path d="" fill="#EA973D" transform="translate(0,0)"/>
<path d="" fill="#E4923B" transform="translate(0,0)"/>
<path d="" fill="#ED953B" transform="translate(0,0)"/>
<path d="" fill="#EB943B" transform="translate(0,0)"/>
<path d="" fill="#E5913A" transform="translate(0,0)"/>
<path d="" fill="#E8913A" transform="translate(0,0)"/>
<path d="" fill="#E1913A" transform="translate(0,0)"/>
<path d="" fill="#EE993C" transform="translate(0,0)"/>
<path d="" fill="#E6943A" transform="translate(0,0)"/>
<path d="" fill="#D88B3E" transform="translate(0,0)"/>
<path d="" fill="#ED9A3B" transform="translate(0,0)"/>
<path d="" fill="#EA973C" transform="translate(0,0)"/>
<path d="" fill="#E4903A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DF903F" transform="translate(265,165)"/>
<path d="" fill="#D3883E" transform="translate(0,0)"/>
<path d="" fill="#E59039" transform="translate(0,0)"/>
<path d="" fill="#E1903C" transform="translate(0,0)"/>
<path d="" fill="#D4883B" transform="translate(0,0)"/>
<path d="" fill="#EB9739" transform="translate(0,0)"/>
<path d="" fill="#E3913C" transform="translate(0,0)"/>
<path d="" fill="#E5923D" transform="translate(0,0)"/>
<path d="" fill="#D48B3E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EC963C" transform="translate(385,157)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E2903B" transform="translate(820,156)"/>
<path d="" fill="#EC9B3C" transform="translate(0,0)"/>
<path d="" fill="#E4933C" transform="translate(0,0)"/>
<path d="" fill="#E6933C" transform="translate(0,0)"/>
<path d="" fill="#EF983A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E8933B" transform="translate(345,149)"/>
<path d="" fill="#E4933D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EB953A" transform="translate(347,148)"/>
<path d="" fill="#EA9539" transform="translate(0,0)"/>
<path d="" fill="#EF9B3B" transform="translate(0,0)"/>
<path d="" fill="#E6943A" transform="translate(0,0)"/>
<path d="" fill="#E4933A" transform="translate(0,0)"/>
<path d="" fill="#DF933C" transform="translate(0,0)"/>
<path d="" fill="#E2923B" transform="translate(0,0)"/>
<path d="" fill="#E49539" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E2933B" transform="translate(322,123)"/>
<path d="" fill="#D68839" transform="translate(0,0)"/>
<path d="" fill="#DD8F3A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E29239" transform="translate(327,120)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DD8F3A" transform="translate(340,115)"/>
<path d="" fill="#D58C3A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DB8C3A" transform="translate(244,113)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DB8D3A" transform="translate(365,112)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DD903A" transform="translate(359,112)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DA8C3A" transform="translate(238,112)"/>
<path d="" fill="#4E4E4E" transform="translate(0,0)"/>
<path d="" fill="#464747" transform="translate(0,0)"/>
<path d="" fill="#3F4040" transform="translate(0,0)"/>
<path d="" fill="#444645" transform="translate(0,0)"/>
<path d="" fill="#474847" transform="translate(0,0)"/>
<path d="" fill="#565654" transform="translate(0,0)"/>
<path d="" fill="#545555" transform="translate(0,0)"/>
<path d="" fill="#5B5C5B" transform="translate(0,0)"/>
<path d="" fill="#444544" transform="translate(0,0)"/>
<path d="" fill="#454645" transform="translate(0,0)"/>
<path d="" fill="#4C4B4B" transform="translate(0,0)"/>
<path d="" fill="#5F5F5F" transform="translate(0,0)"/>
<path d="" fill="#4A4B49" transform="translate(0,0)"/>
<path d="" fill="#A7A49F" transform="translate(0,0)"/>
<path d="" fill="#ADAAA4" transform="translate(0,0)"/>
<path d="" fill="#ACA9A3" transform="translate(0,0)"/>
<path d="" fill="#B4ADA9" transform="translate(0,0)"/>
<path d="" fill="#AEA9A5" transform="translate(0,0)"/>
<path d="" fill="#B0AAA4" transform="translate(0,0)"/>
<path d="" fill="#B0ACA6" transform="translate(0,0)"/>
<path d="" fill="#AEA8A3" transform="translate(0,0)"/>
<path d="" fill="#ABA79F" transform="translate(0,0)"/>
<path d="" fill="#AEABA6" transform="translate(0,0)"/>
<path d="" fill="#A9A6A1" transform="translate(0,0)"/>
<path d="" fill="#B1ADA9" transform="translate(0,0)"/>
<path d="" fill="#AFABA7" transform="translate(0,0)"/>
<path d="" fill="#B1ABA4" transform="translate(0,0)"/>
<path d="" fill="#B3AFA7" transform="translate(0,0)"/>
<path d="" fill="#AEACA7" transform="translate(0,0)"/>
<path d="" fill="#AFACA7" transform="translate(0,0)"/>
<path d="" fill="#ACA9A1" transform="translate(0,0)"/>
<path d="" fill="#ABA7A1" transform="translate(0,0)"/>
<path d="" fill="#ACA8A3" transform="translate(0,0)"/>
<path d="" fill="#ABA6A3" transform="translate(0,0)"/>
<path d="" fill="#A9A6A3" transform="translate(0,0)"/>
<path d="" fill="#ABA9A2" transform="translate(0,0)"/>
<path d="" fill="#A9A59F" transform="translate(0,0)"/>
<path d="" fill="#ADA9A4" transform="translate(0,0)"/>
<path d="" fill="#B2ADA6" transform="translate(0,0)"/>
<path d="" fill="#AEACA6" transform="translate(0,0)"/>
<path d="" fill="#AEACA7" transform="translate(0,0)"/>
<path d="" fill="#B4AFA8" transform="translate(0,0)"/>
<path d="" fill="#AFAEA6" transform="translate(0,0)"/>
<path d="" fill="#B2B0A9" transform="translate(0,0)"/>
<path d="" fill="#ADACA5" transform="translate(0,0)"/>
<path d="" fill="#A7A59F" transform="translate(0,0)"/>
<path d="" fill="#A5A4A0" transform="translate(0,0)"/>
<path d="" fill="#AAA7A0" transform="translate(0,0)"/>
<path d="" fill="#A8A59F" transform="translate(0,0)"/>
<path d="" fill="#B4AFA9" transform="translate(0,0)"/>
<path d="" fill="#B6B0AC" transform="translate(0,0)"/>
<path d="" fill="#B1AEA8" transform="translate(0,0)"/>
<path d="" fill="#B2ADA9" transform="translate(0,0)"/>
<path d="" fill="#A6A2A0" transform="translate(0,0)"/>
<path d="" fill="#B6B2AC" transform="translate(0,0)"/>
<path d="" fill="#AFA9A3" transform="translate(0,0)"/>
<path d="" fill="#B2AEA9" transform="translate(0,0)"/>
<path d="" fill="#B3ADAA" transform="translate(0,0)"/>
<path d="" fill="#B0ACA6" transform="translate(0,0)"/>
<path d="" fill="#AFABA4" transform="translate(0,0)"/>
<path d="" fill="#B2ADA5" transform="translate(0,0)"/>
<path d="" fill="#ACA9A2" transform="translate(0,0)"/>
<path d="" fill="#B3AEA9" transform="translate(0,0)"/>
<path d="" fill="#BAB5B1" transform="translate(0,0)"/>
<path d="" fill="#B1AEA8" transform="translate(0,0)"/>
<path d="" fill="#B4B3A9" transform="translate(0,0)"/>
<path d="" fill="#B0AAA7" transform="translate(0,0)"/>
<path d="" fill="#B0ABA5" transform="translate(0,0)"/>
<path d="" fill="#B3AFA9" transform="translate(0,0)"/>
<path d="" fill="#B0ADA6" transform="translate(0,0)"/>
<path d="" fill="#B4AEA8" transform="translate(0,0)"/>
<path d="" fill="#B2AEA7" transform="translate(0,0)"/>
<path d="" fill="#B6B1AA" transform="translate(0,0)"/>
<path d="" fill="#B6B4AC" transform="translate(0,0)"/>
<path d="" fill="#B1ACA6" transform="translate(0,0)"/>
<path d="" fill="#ABA7A2" transform="translate(0,0)"/>
<path d="" fill="#BCB7B0" transform="translate(0,0)"/>
<path d="" fill="#C5BFBA" transform="translate(0,0)"/>
<path d="" fill="#BCB5B1" transform="translate(0,0)"/>
<path d="" fill="#B5B0AB" transform="translate(0,0)"/>
<path d="" fill="#B6B3AF" transform="translate(0,0)"/>
<path d="" fill="#B0ACA6" transform="translate(0,0)"/>
<path d="" fill="#B1ADA8" transform="translate(0,0)"/>
<path d="" fill="#BAB6AF" transform="translate(0,0)"/>
<path d="" fill="#BDB8B2" transform="translate(0,0)"/>
<path d="" fill="#B6B1AA" transform="translate(0,0)"/>
<path d="" fill="#B6B0A8" transform="translate(0,0)"/>
<path d="" fill="#B5B3AD" transform="translate(0,0)"/>
<path d="" fill="#BEBBB4" transform="translate(0,0)"/>
<path d="" fill="#B9B3AE" transform="translate(0,0)"/>
<path d="" fill="#BAB5AF" transform="translate(0,0)"/>
<path d="" fill="#B9B3AF" transform="translate(0,0)"/>
<path d="" fill="#B9B6AF" transform="translate(0,0)"/>
<path d="" fill="#B8B2AC" transform="translate(0,0)"/>
<path d="" fill="#BBB6AF" transform="translate(0,0)"/>
<path d="" fill="#B6B4AD" transform="translate(0,0)"/>
<path d="" fill="#B3B0A8" transform="translate(0,0)"/>
<path d="" fill="#BBB5AD" transform="translate(0,0)"/>
<path d="" fill="#BBB5AF" transform="translate(0,0)"/>
<path d="" fill="#BAB5B0" transform="translate(0,0)"/>
<path d="" fill="#B3ADA7" transform="translate(0,0)"/>
<path d="" fill="#B7B2AB" transform="translate(0,0)"/>
<path d="" fill="#B9B4AF" transform="translate(0,0)"/>
<path d="" fill="#B7B4AE" transform="translate(0,0)"/>
<path d="" fill="#B4AFA9" transform="translate(0,0)"/>
<path d="" fill="#B3AFA8" transform="translate(0,0)"/>
<path d="" fill="#B6B2AB" transform="translate(0,0)"/>
<path d="" fill="#BCB7AF" transform="translate(0,0)"/>
<path d="" fill="#C0BBB5" transform="translate(0,0)"/>
<path d="" fill="#BDBAB1" transform="translate(0,0)"/>
<path d="" fill="#B9B4AC" transform="translate(0,0)"/>
<path d="" fill="#B6B1AB" transform="translate(0,0)"/>
<path d="" fill="#B6B1AA" transform="translate(0,0)"/>
<path d="" fill="#B8B0AA" transform="translate(0,0)"/>
<path d="" fill="#AFAAA6" transform="translate(0,0)"/>
<path d="" fill="#BDB7AF" transform="translate(0,0)"/>
<path d="" fill="#BCB9B1" transform="translate(0,0)"/>
<path d="" fill="#BDB8B2" transform="translate(0,0)"/>
<path d="" fill="#B9B7AF" transform="translate(0,0)"/>
<path d="" fill="#BBB7B0" transform="translate(0,0)"/>
<path d="" fill="#BBB7B1" transform="translate(0,0)"/>
<path d="" fill="#BAB7AF" transform="translate(0,0)"/>
<path d="" fill="#B9B4AE" transform="translate(0,0)"/>
<path d="" fill="#B8B4B0" transform="translate(0,0)"/>
<path d="" fill="#BBB7B1" transform="translate(0,0)"/>
<path d="" fill="#C1B9B2" transform="translate(0,0)"/>
<path d="" fill="#B8B2AB" transform="translate(0,0)"/>
<path d="" fill="#C4BEB8" transform="translate(0,0)"/>
<path d="" fill="#BBB7B0" transform="translate(0,0)"/>
<path d="" fill="#BFBAB4" transform="translate(0,0)"/>
<path d="" fill="#B8B3AC" transform="translate(0,0)"/>
<path d="" fill="#BCB8B3" transform="translate(0,0)"/>
<path d="" fill="#BEBBB4" transform="translate(0,0)"/>
<path d="" fill="#BAB3AD" transform="translate(0,0)"/>
<path d="" fill="#ABA7A2" transform="translate(0,0)"/>
<path d="" fill="#B8B3AD" transform="translate(0,0)"/>
<path d="" fill="#B9B6AD" transform="translate(0,0)"/>
<path d="" fill="#BDB8AF" transform="translate(0,0)"/>
<path d="" fill="#BAB7B0" transform="translate(0,0)"/>
<path d="" fill="#C2BDB7" transform="translate(0,0)"/>
<path d="" fill="#BBB6B0" transform="translate(0,0)"/>
<path d="" fill="#BDB7B2" transform="translate(0,0)"/>
<path d="" fill="#B8B3AE" transform="translate(0,0)"/>
<path d="" fill="#B7B1AD" transform="translate(0,0)"/>
<path d="" fill="#BAB6B0" transform="translate(0,0)"/>
<path d="" fill="#BEB8B1" transform="translate(0,0)"/>
<path d="" fill="#B9B2AC" transform="translate(0,0)"/>
<path d="" fill="#BAB4AF" transform="translate(0,0)"/>
<path d="" fill="#BAB6AD" transform="translate(0,0)"/>
<path d="" fill="#BFBAB3" transform="translate(0,0)"/>
<path d="" fill="#BBB7B0" transform="translate(0,0)"/>
<path d="" fill="#BCB7B2" transform="translate(0,0)"/>
<path d="" fill="#B8B5B0" transform="translate(0,0)"/>
<path d="" fill="#BEB8B1" transform="translate(0,0)"/>
<path d="" fill="#BFBBB3" transform="translate(0,0)"/>
<path d="" fill="#BDB9B1" transform="translate(0,0)"/>
<path d="" fill="#B5B1AC" transform="translate(0,0)"/>
<path d="" fill="#BCBAB0" transform="translate(0,0)"/>
<path d="" fill="#BEB7AF" transform="translate(0,0)"/>
<path d="" fill="#BCB4AC" transform="translate(0,0)"/>
<path d="" fill="#BDB7B0" transform="translate(0,0)"/>
<path d="" fill="#BCB8B0" transform="translate(0,0)"/>
<path d="" fill="#BBB7AF" transform="translate(0,0)"/>
<path d="" fill="#BCB7B2" transform="translate(0,0)"/>
<path d="" fill="#B7B2AA" transform="translate(0,0)"/>
<path d="" fill="#BFBAB2" transform="translate(0,0)"/>
<path d="" fill="#C2BBB4" transform="translate(0,0)"/>
<path d="" fill="#BBB6B0" transform="translate(0,0)"/>
<path d="" fill="#BCBAB3" transform="translate(0,0)"/>
<path d="" fill="#B8B3AB" transform="translate(0,0)"/>
<path d="" fill="#AEAAA5" transform="translate(0,0)"/>
<path d="" fill="#BBB5AF" transform="translate(0,0)"/>
<path d="" fill="#BFBBB3" transform="translate(0,0)"/>
<path d="" fill="#BDB8B2" transform="translate(0,0)"/>
<path d="" fill="#BBB7B1" transform="translate(0,0)"/>
<path d="" fill="#B6B2AC" transform="translate(0,0)"/>
<path d="" fill="#BDB8B3" transform="translate(0,0)"/>
<path d="" fill="#BCB6B0" transform="translate(0,0)"/>
<path d="" fill="#B9B5AE" transform="translate(0,0)"/>
<path d="" fill="#BCB7AF" transform="translate(0,0)"/>
<path d="" fill="#BDB9B3" transform="translate(0,0)"/>
<path d="" fill="#B9B4B1" transform="translate(0,0)"/>
<path d="" fill="#BFBAB2" transform="translate(0,0)"/>
<path d="" fill="#BDB9B1" transform="translate(0,0)"/>
<path d="" fill="#BBB4AF" transform="translate(0,0)"/>
<path d="" fill="#B6B1AB" transform="translate(0,0)"/>
<path d="" fill="#BDB5B0" transform="translate(0,0)"/>
<path d="" fill="#BAB5B0" transform="translate(0,0)"/>
<path d="" fill="#BDBBB5" transform="translate(0,0)"/>
<path d="" fill="#BBB7B1" transform="translate(0,0)"/>
<path d="" fill="#B1AFA8" transform="translate(0,0)"/>
<path d="" fill="#B8B5B2" transform="translate(0,0)"/>
<path d="" fill="#C1BAB4" transform="translate(0,0)"/>
<path d="" fill="#BAB6B0" transform="translate(0,0)"/>
<path d="" fill="#BEBAB3" transform="translate(0,0)"/>
<path d="" fill="#BDB9B1" transform="translate(0,0)"/>
<path d="" fill="#BDB9B4" transform="translate(0,0)"/>
<path d="" fill="#BEBAB4" transform="translate(0,0)"/>
<path d="" fill="#BCB7B2" transform="translate(0,0)"/>
<path d="" fill="#C9C1B9" transform="translate(0,0)"/>
<path d="" fill="#C8C3BD" transform="translate(0,0)"/>
<path d="" fill="#C9C3BD" transform="translate(0,0)"/>
<path d="" fill="#CCC6BE" transform="translate(0,0)"/>
<path d="" fill="#CCC8C1" transform="translate(0,0)"/>
<path d="" fill="#CBC5BF" transform="translate(0,0)"/>
<path d="" fill="#C8C3BC" transform="translate(0,0)"/>
<path d="" fill="#CAC5BE" transform="translate(0,0)"/>
<path d="" fill="#C9C4BD" transform="translate(0,0)"/>
<path d="" fill="#C7C1BA" transform="translate(0,0)"/>
<path d="" fill="#C6C2BB" transform="translate(0,0)"/>
<path d="" fill="#D3CAC4" transform="translate(0,0)"/>
<path d="" fill="#C9C3BD" transform="translate(0,0)"/>
<path d="" fill="#C4BFB8" transform="translate(0,0)"/>
<path d="" fill="#C8C3BD" transform="translate(0,0)"/>
<path d="" fill="#C4C1BB" transform="translate(0,0)"/>
<path d="" fill="#C2BCB5" transform="translate(0,0)"/>
<path d="" fill="#BBB7AF" transform="translate(0,0)"/>
<path d="" fill="#C7C4BE" transform="translate(0,0)"/>
<path d="" fill="#C9C4BD" transform="translate(0,0)"/>
<path d="" fill="#CAC6BE" transform="translate(0,0)"/>
<path d="" fill="#C9C3C0" transform="translate(0,0)"/>
<path d="" fill="#C9C4BC" transform="translate(0,0)"/>
<path d="" fill="#CAC5C0" transform="translate(0,0)"/>
<path d="" fill="#C9C5C0" transform="translate(0,0)"/>
<path d="" fill="#C8C3BD" transform="translate(0,0)"/>
<path d="" fill="#C7C2BA" transform="translate(0,0)"/>
<path d="" fill="#B3AFAB" transform="translate(0,0)"/>
<path d="" fill="#B3B0A9" transform="translate(0,0)"/>
<path d="" fill="#BBB8B2" transform="translate(0,0)"/>
<path d="" fill="#CCC5BD" transform="translate(0,0)"/>
<path d="" fill="#C9C5BF" transform="translate(0,0)"/>
<path d="" fill="#D1C6C1" transform="translate(0,0)"/>
<path d="" fill="#C5C0B8" transform="translate(0,0)"/>
<path d="" fill="#BEB9B5" transform="translate(0,0)"/>
<path d="" fill="#D0C8C2" transform="translate(0,0)"/>
<path d="" fill="#CAC6BF" transform="translate(0,0)"/>
<path d="" fill="#D1CCC4" transform="translate(0,0)"/>
<path d="" fill="#CBC4BE" transform="translate(0,0)"/>
<path d="" fill="#CBC4BE" transform="translate(0,0)"/>
<path d="" fill="#C8C4BD" transform="translate(0,0)"/>
<path d="" fill="#BAB6B1" transform="translate(0,0)"/>
<path d="" fill="#CCC4BE" transform="translate(0,0)"/>
<path d="" fill="#CCC7BF" transform="translate(0,0)"/>
<path d="" fill="#C8C2BA" transform="translate(0,0)"/>
<path d="" fill="#CBC4BD" transform="translate(0,0)"/>
<path d="" fill="#CBC6BE" transform="translate(0,0)"/>
<path d="" fill="#CBC5BE" transform="translate(0,0)"/>
<path d="" fill="#C5C0B8" transform="translate(0,0)"/>
<path d="" fill="#CDC6BF" transform="translate(0,0)"/>
<path d="" fill="#C9C5BF" transform="translate(0,0)"/>
<path d="" fill="#B0AEA8" transform="translate(0,0)"/>
<path d="" fill="#CCC8C0" transform="translate(0,0)"/>
<path d="" fill="#BAB4B1" transform="translate(0,0)"/>
<path d="" fill="#CAC6BF" transform="translate(0,0)"/>
<path d="" fill="#CBC6C1" transform="translate(0,0)"/>
<path d="" fill="#CEC7BF" transform="translate(0,0)"/>
<path d="" fill="#C7C4BE" transform="translate(0,0)"/>
<path d="" fill="#CCC7C4" transform="translate(0,0)"/>
<path d="" fill="#CEC9C3" transform="translate(0,0)"/>
<path d="" fill="#CCC5BD" transform="translate(0,0)"/>
<path d="" fill="#CAC5BE" transform="translate(0,0)"/>
<path d="" fill="#CCC6C0" transform="translate(0,0)"/>
<path d="" fill="#CEC7C2" transform="translate(0,0)"/>
<path d="" fill="#CBC7C0" transform="translate(0,0)"/>
<path d="" fill="#CDCAC3" transform="translate(0,0)"/>
<path d="" fill="#CAC5BE" transform="translate(0,0)"/>
<path d="" fill="#C9C5BE" transform="translate(0,0)"/>
<path d="" fill="#CBC5C0" transform="translate(0,0)"/>
<path d="" fill="#C8C0BC" transform="translate(0,0)"/>
<path d="" fill="#CCC5BC" transform="translate(0,0)"/>
<path d="" fill="#CAC5BE" transform="translate(0,0)"/>
<path d="" fill="#C9C4BC" transform="translate(0,0)"/>
<path d="" fill="#CBC7BF" transform="translate(0,0)"/>
<path d="" fill="#CAC4BE" transform="translate(0,0)"/>
<path d="" fill="#CCC7C0" transform="translate(0,0)"/>
<path d="" fill="#CAC6BF" transform="translate(0,0)"/>
<path d="" fill="#C9C4BE" transform="translate(0,0)"/>
<path d="" fill="#CDC7C0" transform="translate(0,0)"/>
<path d="" fill="#C8C4BC" transform="translate(0,0)"/>
<path d="" fill="#CDC8C1" transform="translate(0,0)"/>
<path d="" fill="#CDC7C0" transform="translate(0,0)"/>
<path d="" fill="#CAC8C2" transform="translate(0,0)"/>
<path d="" fill="#CBC6BF" transform="translate(0,0)"/>
<path d="" fill="#CBC6BD" transform="translate(0,0)"/>
<path d="" fill="#CEC8C1" transform="translate(0,0)"/>
<path d="" fill="#C6BFB9" transform="translate(0,0)"/>
<path d="" fill="#D0C8C4" transform="translate(0,0)"/>
<path d="" fill="#CAC6C0" transform="translate(0,0)"/>
<path d="" fill="#CAC7BF" transform="translate(0,0)"/>
<path d="" fill="#CBC8C2" transform="translate(0,0)"/>
<path d="" fill="#CAC6BF" transform="translate(0,0)"/>
<path d="" fill="#CDC7C0" transform="translate(0,0)"/>
<path d="" fill="#CCC4BE" transform="translate(0,0)"/>
<path d="" fill="#CBC6BF" transform="translate(0,0)"/>
<path d="" fill="#CDC6BE" transform="translate(0,0)"/>
<path d="" fill="#C9C5BE" transform="translate(0,0)"/>
<path d="" fill="#CDC7C0" transform="translate(0,0)"/>
<path d="" fill="#BCB9B2" transform="translate(0,0)"/>
<path d="" fill="#BFB9B2" transform="translate(0,0)"/>
<path d="" fill="#C4BEBA" transform="translate(0,0)"/>
<path d="" fill="#C8C3BA" transform="translate(0,0)"/>
<path d="" fill="#CDC8C0" transform="translate(0,0)"/>
<path d="" fill="#CCC7C0" transform="translate(0,0)"/>
<path d="" fill="#D0CAC2" transform="translate(0,0)"/>
<path d="" fill="#C7C3BE" transform="translate(0,0)"/>
<path d="" fill="#CCC7C3" transform="translate(0,0)"/>
<path d="" fill="#C8C4BF" transform="translate(0,0)"/>
<path d="" fill="#D1CDC5" transform="translate(0,0)"/>
<path d="" fill="#D1CCC8" transform="translate(0,0)"/>
<path d="" fill="#CAC6BF" transform="translate(0,0)"/>
<path d="" fill="#BBB8B1" transform="translate(0,0)"/>
<path d="" fill="#C8C4BD" transform="translate(0,0)"/>
<path d="" fill="#D4CDC4" transform="translate(0,0)"/>
<path d="" fill="#CDC7C1" transform="translate(0,0)"/>
<path d="" fill="#CAC4BD" transform="translate(0,0)"/>
<path d="" fill="#D0CAC7" transform="translate(0,0)"/>
<path d="" fill="#CFC9C2" transform="translate(0,0)"/>
<path d="" fill="#D0CBC3" transform="translate(0,0)"/>
<path d="" fill="#C2BEB9" transform="translate(0,0)"/>
<path d="" fill="#C6C2BA" transform="translate(0,0)"/>
<path d="" fill="#CDC6BF" transform="translate(0,0)"/>
<path d="" fill="#CCC5BF" transform="translate(0,0)"/>
<path d="" fill="#C5C3BB" transform="translate(0,0)"/>
<path d="" fill="#CEC6BF" transform="translate(0,0)"/>
<path d="" fill="#C8C2BD" transform="translate(0,0)"/>
<path d="" fill="#CDC8C0" transform="translate(0,0)"/>
<path d="" fill="#C9C4BE" transform="translate(0,0)"/>
<path d="" fill="#CAC6BE" transform="translate(0,0)"/>
<path d="" fill="#CAC4BE" transform="translate(0,0)"/>
<path d="" fill="#CDC7C1" transform="translate(0,0)"/>
<path d="" fill="#CBC6BE" transform="translate(0,0)"/>
<path d="" fill="#CCC6BF" transform="translate(0,0)"/>
<path d="" fill="#CBC6BF" transform="translate(0,0)"/>
<path d="" fill="#CDC7BF" transform="translate(0,0)"/>
<path d="" fill="#CBC4C0" transform="translate(0,0)"/>
<path d="" fill="#CDC9C1" transform="translate(0,0)"/>
<path d="" fill="#CBC3BE" transform="translate(0,0)"/>
<path d="" fill="#CAC5BC" transform="translate(0,0)"/>
<path d="" fill="#B1ACA9" transform="translate(0,0)"/>
<path d="" fill="#C5C0BA" transform="translate(0,0)"/>
<path d="" fill="#CDC7C0" transform="translate(0,0)"/>
<path d="" fill="#C9C4BF" transform="translate(0,0)"/>
<path d="" fill="#CCC6C2" transform="translate(0,0)"/>
<path d="" fill="#CCC5C0" transform="translate(0,0)"/>
<path d="" fill="#CDC5BF" transform="translate(0,0)"/>
<path d="" fill="#CAC4BE" transform="translate(0,0)"/>
<path d="" fill="#B3B0AB" transform="translate(0,0)"/>
<path d="" fill="#C9C4BD" transform="translate(0,0)"/>
<path d="" fill="#CAC6BF" transform="translate(0,0)"/>
<path d="" fill="#CCC8C1" transform="translate(0,0)"/>
<path d="" fill="#D5CFCB" transform="translate(0,0)"/>
<path d="" fill="#CAC6BE" transform="translate(0,0)"/>
<path d="" fill="#D0CAC3" transform="translate(0,0)"/>
<path d="" fill="#CDC7C3" transform="translate(0,0)"/>
<path d="" fill="#C4BFBA" transform="translate(0,0)"/>
<path d="" fill="#CDC7C3" transform="translate(0,0)"/>
<path d="" fill="#CEC8C2" transform="translate(0,0)"/>
<path d="" fill="#CBC6BE" transform="translate(0,0)"/>
<path d="" fill="#CAC6BF" transform="translate(0,0)"/>
<path d="" fill="#C6C0BB" transform="translate(0,0)"/>
<path d="" fill="#D9D2CB" transform="translate(0,0)"/>
<path d="" fill="#BBB9B3" transform="translate(0,0)"/>
<path d="" fill="#C7C3BC" transform="translate(0,0)"/>
<path d="" fill="#D2CCC6" transform="translate(0,0)"/>
<path d="" fill="#CCC6BE" transform="translate(0,0)"/>
<path d="" fill="#C8C4BD" transform="translate(0,0)"/>
<path d="" fill="#C8C4BD" transform="translate(0,0)"/>
<path d="" fill="#C8C3BD" transform="translate(0,0)"/>
<path d="" fill="#C9C4BD" transform="translate(0,0)"/>
<path d="" fill="#C8C5BD" transform="translate(0,0)"/>
<path d="" fill="#CEC8C4" transform="translate(0,0)"/>
<path d="" fill="#CBC4BE" transform="translate(0,0)"/>
<path d="" fill="#C8C3BC" transform="translate(0,0)"/>
<path d="" fill="#CAC3BD" transform="translate(0,0)"/>
<path d="" fill="#C9C4BE" transform="translate(0,0)"/>
<path d="" fill="#C7C3BE" transform="translate(0,0)"/>
<path d="" fill="#BEBAB1" transform="translate(0,0)"/>
<path d="" fill="#BDB9B3" transform="translate(0,0)"/>
<path d="" fill="#C3BEB8" transform="translate(0,0)"/>
<path d="" fill="#C8C2BC" transform="translate(0,0)"/>
<path d="" fill="#CAC5BE" transform="translate(0,0)"/>
<path d="" fill="#C8C4BA" transform="translate(0,0)"/>
<path d="" fill="#C9C5BD" transform="translate(0,0)"/>
<path d="" fill="#C6C1BB" transform="translate(0,0)"/>
<path d="" fill="#BBB7B1" transform="translate(0,0)"/>
<path d="" fill="#C6C0BA" transform="translate(0,0)"/>
<path d="" fill="#CEC9C0" transform="translate(0,0)"/>
<path d="" fill="#CEC7C0" transform="translate(0,0)"/>
<path d="" fill="#C8C4BE" transform="translate(0,0)"/>
<path d="" fill="#CAC5BE" transform="translate(0,0)"/>
<path d="" fill="#C6C2BC" transform="translate(0,0)"/>
<path d="" fill="#CCC6C0" transform="translate(0,0)"/>
<path d="" fill="#C8C1BD" transform="translate(0,0)"/>
<path d="" fill="#BEBAB4" transform="translate(0,0)"/>
<path d="" fill="#C9C2BB" transform="translate(0,0)"/>
<path d="" fill="#CCC8C0" transform="translate(0,0)"/>
<path d="" fill="#CAC5BE" transform="translate(0,0)"/>
<path d="" fill="#C6C1BB" transform="translate(0,0)"/>
<path d="" fill="#C7C4BC" transform="translate(0,0)"/>
<path d="" fill="#C4C1BC" transform="translate(0,0)"/>
<path d="" fill="#B0ACA5" transform="translate(0,0)"/>
<path d="" fill="#C0BDB8" transform="translate(0,0)"/>
<path d="" fill="#C6C0BB" transform="translate(0,0)"/>
<path d="" fill="#C3BEB7" transform="translate(0,0)"/>
<path d="" fill="#C5BFBA" transform="translate(0,0)"/>
<path d="" fill="#BEBAB3" transform="translate(0,0)"/>
<path d="" fill="#C1BFB7" transform="translate(0,0)"/>
<path d="" fill="#C6C2BB" transform="translate(0,0)"/>
<path d="" fill="#C3BEB8" transform="translate(0,0)"/>
<path d="" fill="#C3C0B9" transform="translate(0,0)"/>
<path d="" fill="#C9C4C0" transform="translate(0,0)"/>
<path d="" fill="#C4BFBB" transform="translate(0,0)"/>
<path d="" fill="#BAB7B1" transform="translate(0,0)"/>
<path d="" fill="#BCB9B4" transform="translate(0,0)"/>
<path d="" fill="#C6C1BB" transform="translate(0,0)"/>
<path d="" fill="#C5BFBA" transform="translate(0,0)"/>
<path d="" fill="#C5C2B9" transform="translate(0,0)"/>
<path d="" fill="#C8C2BC" transform="translate(0,0)"/>
<path d="" fill="#C6C1BB" transform="translate(0,0)"/>
<path d="" fill="#C8C2C0" transform="translate(0,0)"/>
<path d="" fill="#C4C0BA" transform="translate(0,0)"/>
<path d="" fill="#C9C4BF" transform="translate(0,0)"/>
<path d="" fill="#C6C0B9" transform="translate(0,0)"/>
<path d="" fill="#BFB9B4" transform="translate(0,0)"/>
<path d="" fill="#C1BDB8" transform="translate(0,0)"/>
<path d="" fill="#C0B9B5" transform="translate(0,0)"/>
<path d="" fill="#AAA7A3" transform="translate(0,0)"/>
<path d="" fill="#C4BEB7" transform="translate(0,0)"/>
<path d="" fill="#C0BDB9" transform="translate(0,0)"/>
<path d="" fill="#C6C0BA" transform="translate(0,0)"/>
<path d="" fill="#C0BBB5" transform="translate(0,0)"/>
<path d="" fill="#BFBDB7" transform="translate(0,0)"/>
<path d="" fill="#BFBAB5" transform="translate(0,0)"/>
<path d="" fill="#B9B4AD" transform="translate(0,0)"/>
<path d="" fill="#BBB6B0" transform="translate(0,0)"/>
<path d="" fill="#C2BDB7" transform="translate(0,0)"/>
<path d="" fill="#C6C3BC" transform="translate(0,0)"/>
<path d="" fill="#C2C0BB" transform="translate(0,0)"/>
<path d="" fill="#C6C2BD" transform="translate(0,0)"/>
<path d="" fill="#C4C0BA" transform="translate(0,0)"/>
<path d="" fill="#C2C0B8" transform="translate(0,0)"/>
<path d="" fill="#BBB7B2" transform="translate(0,0)"/>
<path d="" fill="#656564" transform="translate(0,0)"/>
<path d="" fill="#4F4F50" transform="translate(0,0)"/>
<path d="" fill="#4E4F4D" transform="translate(0,0)"/>
<path d="" fill="#6C6B6B" transform="translate(0,0)"/>
<path d="" fill="#797776" transform="translate(0,0)"/>
<path d="" fill="#817D78" transform="translate(0,0)"/>
<path d="" fill="#777572" transform="translate(0,0)"/>
<path d="" fill="#6C6B69" transform="translate(0,0)"/>
<path d="" fill="#7A7676" transform="translate(0,0)"/>
<path d="" fill="#7E7877" transform="translate(0,0)"/>
<path d="" fill="#7D7A76" transform="translate(0,0)"/>
<path d="" fill="#7E7B76" transform="translate(0,0)"/>
<path d="" fill="#7E7B75" transform="translate(0,0)"/>
<path d="" fill="#807B76" transform="translate(0,0)"/>
<path d="" fill="#7E7975" transform="translate(0,0)"/>
<path d="" fill="#7E7A76" transform="translate(0,0)"/>
<path d="" fill="#7F7975" transform="translate(0,0)"/>
<path d="" fill="#807D76" transform="translate(0,0)"/>
<path d="" fill="#7D7977" transform="translate(0,0)"/>
<path d="" fill="#7C7876" transform="translate(0,0)"/>
<path d="" fill="#797875" transform="translate(0,0)"/>
<path d="" fill="#7A7876" transform="translate(0,0)"/>
<path d="" fill="#797875" transform="translate(0,0)"/>
<path d="" fill="#7E7974" transform="translate(0,0)"/>
<path d="" fill="#5F615E" transform="translate(0,0)"/>
<path d="" fill="#5F5E5E" transform="translate(0,0)"/>
<path d="" fill="#656662" transform="translate(0,0)"/>
<path d="" fill="#807A76" transform="translate(0,0)"/>
<path d="" fill="#807874" transform="translate(0,0)"/>
<path d="" fill="#807B77" transform="translate(0,0)"/>
<path d="" fill="#6D6B6A" transform="translate(0,0)"/>
<path d="" fill="#6B6969" transform="translate(0,0)"/>
<path d="" fill="#6A6765" transform="translate(0,0)"/>
<path d="" fill="#686663" transform="translate(0,0)"/>
<path d="" fill="#646362" transform="translate(0,0)"/>
<path d="" fill="#63615F" transform="translate(0,0)"/>
<path d="" fill="#5D5C5B" transform="translate(0,0)"/>
<path d="" fill="#7F7875" transform="translate(0,0)"/>
<path d="" fill="#6D6968" transform="translate(0,0)"/>
<path d="" fill="#7E7874" transform="translate(0,0)"/>
<path d="" fill="#7E7972" transform="translate(0,0)"/>
<path d="" fill="#7E7975" transform="translate(0,0)"/>
<path d="" fill="#7F7671" transform="translate(0,0)"/>
<path d="" fill="#7E7771" transform="translate(0,0)"/>
<path d="" fill="#7E7268" transform="translate(0,0)"/>
<path d="" fill="#5A514A" transform="translate(0,0)"/>
<path d="" fill="#8E715B" transform="translate(0,0)"/>
<path d="" fill="#484340" transform="translate(0,0)"/>
<path d="" fill="#C27D48" transform="translate(0,0)"/>
<path d="" fill="#C67D49" transform="translate(0,0)"/>
<path d="" fill="#C07A44" transform="translate(0,0)"/>
<path d="" fill="#BC7B49" transform="translate(0,0)"/>
<path d="" fill="#3F3D3B" transform="translate(0,0)"/>
<path d="" fill="#C07C4A" transform="translate(0,0)"/>
<path d="" fill="#BC7A48" transform="translate(0,0)"/>
<path d="" fill="#BA7A49" transform="translate(0,0)"/>
<path d="" fill="#BA794A" transform="translate(0,0)"/>
<path d="" fill="#BD7B4B" transform="translate(0,0)"/>
<path d="" fill="#BD794B" transform="translate(0,0)"/>
<path d="" fill="#BA7B4B" transform="translate(0,0)"/>
<path d="" fill="#BE7947" transform="translate(0,0)"/>
<path d="" fill="#B97948" transform="translate(0,0)"/>
<path d="" fill="#BE7A49" transform="translate(0,0)"/>
<path d="" fill="#B87949" transform="translate(0,0)"/>
<path d="" fill="#B7794C" transform="translate(0,0)"/>
<path d="" fill="#BE7B49" transform="translate(0,0)"/>
<path d="" fill="#B97848" transform="translate(0,0)"/>
<path d="" fill="#B8784B" transform="translate(0,0)"/>
<path d="" fill="#BD7B4A" transform="translate(0,0)"/>
<path d="" fill="#BA7A49" transform="translate(0,0)"/>
<path d="" fill="#B7794A" transform="translate(0,0)"/>
<path d="" fill="#B37A4C" transform="translate(0,0)"/>
<path d="" fill="#C47D47" transform="translate(0,0)"/>
<path d="" fill="#C67F47" transform="translate(0,0)"/>
<path d="" fill="#C57A42" transform="translate(0,0)"/>
<path d="" fill="#C17945" transform="translate(0,0)"/>
<path d="" fill="#C97F46" transform="translate(0,0)"/>
<path d="" fill="#C97F42" transform="translate(0,0)"/>
<path d="" fill="#BF7746" transform="translate(0,0)"/>
<path d="" fill="#C17D46" transform="translate(0,0)"/>
<path d="" fill="#BD7D48" transform="translate(0,0)"/>
<path d="" fill="#C17A47" transform="translate(0,0)"/>
<path d="" fill="#BF7A46" transform="translate(0,0)"/>
<path d="" fill="#C27C47" transform="translate(0,0)"/>
<path d="" fill="#BE7C48" transform="translate(0,0)"/>
<path d="" fill="#C07B46" transform="translate(0,0)"/>
<path d="" fill="#C57E46" transform="translate(0,0)"/>
<path d="" fill="#C57D47" transform="translate(0,0)"/>
<path d="" fill="#C77E48" transform="translate(0,0)"/>
<path d="" fill="#CC8245" transform="translate(0,0)"/>
<path d="" fill="#C87E46" transform="translate(0,0)"/>
<path d="" fill="#C77D43" transform="translate(0,0)"/>
<path d="" fill="#C27D45" transform="translate(0,0)"/>
<path d="" fill="#C47B45" transform="translate(0,0)"/>
<path d="" fill="#C88148" transform="translate(0,0)"/>
<path d="" fill="#B97C4A" transform="translate(0,0)"/>
<path d="" fill="#C87E46" transform="translate(0,0)"/>
<path d="" fill="#C67D4A" transform="translate(0,0)"/>
<path d="" fill="#CF8046" transform="translate(0,0)"/>
<path d="" fill="#CB8045" transform="translate(0,0)"/>
<path d="" fill="#C57E48" transform="translate(0,0)"/>
<path d="" fill="#C98149" transform="translate(0,0)"/>
<path d="" fill="#CA7F46" transform="translate(0,0)"/>
<path d="" fill="#C67C47" transform="translate(0,0)"/>
<path d="" fill="#CA7E44" transform="translate(0,0)"/>
<path d="" fill="#C97D43" transform="translate(0,0)"/>
<path d="" fill="#CC8044" transform="translate(0,0)"/>
<path d="" fill="#B27548" transform="translate(0,0)"/>
<path d="" fill="#C57C44" transform="translate(0,0)"/>
<path d="" fill="#CE8344" transform="translate(0,0)"/>
<path d="" fill="#B4784C" transform="translate(0,0)"/>
<path d="" fill="#CC8247" transform="translate(0,0)"/>
<path d="" fill="#CB8244" transform="translate(0,0)"/>
<path d="" fill="#B5794A" transform="translate(0,0)"/>
<path d="" fill="#D28744" transform="translate(0,0)"/>
<path d="" fill="#CA803E" transform="translate(0,0)"/>
<path d="" fill="#CC7F42" transform="translate(0,0)"/>
<path d="" fill="#D68442" transform="translate(0,0)"/>
<path d="" fill="#CE8245" transform="translate(0,0)"/>
<path d="" fill="#D48340" transform="translate(0,0)"/>
<path d="" fill="#CF8243" transform="translate(0,0)"/>
<path d="" fill="#CD7F41" transform="translate(0,0)"/>
<path d="" fill="#D28441" transform="translate(0,0)"/>
<path d="" fill="#CD8146" transform="translate(0,0)"/>
<path d="" fill="#D08244" transform="translate(0,0)"/>
<path d="" fill="#D58642" transform="translate(0,0)"/>
<path d="" fill="#D28543" transform="translate(0,0)"/>
<path d="" fill="#D08343" transform="translate(0,0)"/>
<path d="" fill="#CE8044" transform="translate(0,0)"/>
<path d="" fill="#D18341" transform="translate(0,0)"/>
<path d="" fill="#CE8244" transform="translate(0,0)"/>
<path d="" fill="#D48641" transform="translate(0,0)"/>
<path d="" fill="#D48642" transform="translate(0,0)"/>
<path d="" fill="#CE8344" transform="translate(0,0)"/>
<path d="" fill="#CF8142" transform="translate(0,0)"/>
<path d="" fill="#D08545" transform="translate(0,0)"/>
<path d="" fill="#D08242" transform="translate(0,0)"/>
<path d="" fill="#DC8A43" transform="translate(0,0)"/>
<path d="" fill="#D08444" transform="translate(0,0)"/>
<path d="" fill="#DB8940" transform="translate(0,0)"/>
<path d="" fill="#D78841" transform="translate(0,0)"/>
<path d="" fill="#D68943" transform="translate(0,0)"/>
<path d="" fill="#DE893F" transform="translate(0,0)"/>
<path d="" fill="#D08545" transform="translate(0,0)"/>
<path d="" fill="#D18241" transform="translate(0,0)"/>
<path d="" fill="#D88641" transform="translate(0,0)"/>
<path d="" fill="#E28D43" transform="translate(0,0)"/>
<path d="" fill="#DB8842" transform="translate(0,0)"/>
<path d="" fill="#D88943" transform="translate(0,0)"/>
<path d="" fill="#D28343" transform="translate(0,0)"/>
<path d="" fill="#D38243" transform="translate(0,0)"/>
<path d="" fill="#D78741" transform="translate(0,0)"/>
<path d="" fill="#CA8143" transform="translate(0,0)"/>
<path d="" fill="#DA873E" transform="translate(0,0)"/>
<path d="" fill="#D88943" transform="translate(0,0)"/>
<path d="" fill="#D98B3F" transform="translate(0,0)"/>
<path d="" fill="#D28344" transform="translate(0,0)"/>
<path d="" fill="#D48A41" transform="translate(0,0)"/>
<path d="" fill="#D4833F" transform="translate(0,0)"/>
<path d="" fill="#D58642" transform="translate(0,0)"/>
<path d="" fill="#E08D42" transform="translate(0,0)"/>
<path d="" fill="#D88941" transform="translate(0,0)"/>
<path d="" fill="#D18544" transform="translate(0,0)"/>
<path d="" fill="#D4833F" transform="translate(0,0)"/>
<path d="" fill="#D98740" transform="translate(0,0)"/>
<path d="" fill="#E18F3F" transform="translate(0,0)"/>
<path d="" fill="#D9883F" transform="translate(0,0)"/>
<path d="" fill="#D28441" transform="translate(0,0)"/>
<path d="" fill="#D48341" transform="translate(0,0)"/>
<path d="" fill="#E28F3F" transform="translate(0,0)"/>
<path d="" fill="#D18442" transform="translate(0,0)"/>
<path d="" fill="#DC8D40" transform="translate(0,0)"/>
<path d="" fill="#DC8A3E" transform="translate(0,0)"/>
<path d="" fill="#DB893F" transform="translate(0,0)"/>
<path d="" fill="#DC893D" transform="translate(0,0)"/>
<path d="" fill="#D78842" transform="translate(0,0)"/>
<path d="" fill="#D28646" transform="translate(0,0)"/>
<path d="" fill="#E6913F" transform="translate(0,0)"/>
<path d="" fill="#DE8B3E" transform="translate(0,0)"/>
<path d="" fill="#D98A41" transform="translate(0,0)"/>
<path d="" fill="#CC8446" transform="translate(0,0)"/>
<path d="" fill="#D88941" transform="translate(0,0)"/>
<path d="" fill="#CC8143" transform="translate(0,0)"/>
<path d="" fill="#E6923F" transform="translate(0,0)"/>
<path d="" fill="#DE893E" transform="translate(0,0)"/>
<path d="" fill="#DF8B3F" transform="translate(0,0)"/>
<path d="" fill="#CB8244" transform="translate(0,0)"/>
<path d="" fill="#E39340" transform="translate(0,0)"/>
<path d="" fill="#E79640" transform="translate(0,0)"/>
<path d="" fill="#D98842" transform="translate(0,0)"/>
<path d="" fill="#CB8145" transform="translate(0,0)"/>
<path d="" fill="#DC8A3F" transform="translate(0,0)"/>
<path d="" fill="#E18C3D" transform="translate(0,0)"/>
<path d="" fill="#DD8C40" transform="translate(0,0)"/>
<path d="" fill="#E7913A" transform="translate(0,0)"/>
<path d="" fill="#DC8B3D" transform="translate(0,0)"/>
<path d="" fill="#DB8A40" transform="translate(0,0)"/>
<path d="" fill="#CD8341" transform="translate(0,0)"/>
<path d="" fill="#E08841" transform="translate(0,0)"/>
<path d="" fill="#423E37" transform="translate(0,0)"/>
<path d="" fill="#675441" transform="translate(0,0)"/>
<path d="" fill="#E7943F" transform="translate(0,0)"/>
<path d="" fill="#D9873F" transform="translate(0,0)"/>
<path d="" fill="#E9933E" transform="translate(0,0)"/>
<path d="" fill="#DF8C3D" transform="translate(0,0)"/>
<path d="" fill="#D08346" transform="translate(0,0)"/>
<path d="" fill="#E18C41" transform="translate(0,0)"/>
<path d="" fill="#DC8B41" transform="translate(0,0)"/>
<path d="" fill="#DE8A3C" transform="translate(0,0)"/>
<path d="" fill="#E4923D" transform="translate(0,0)"/>
<path d="" fill="#E28F3E" transform="translate(0,0)"/>
<path d="" fill="#DC8B3F" transform="translate(0,0)"/>
<path d="" fill="#E28B3C" transform="translate(0,0)"/>
<path d="" fill="#E58D3D" transform="translate(0,0)"/>
<path d="" fill="#DC8D3F" transform="translate(0,0)"/>
<path d="" fill="#E6973F" transform="translate(0,0)"/>
<path d="" fill="#DE8B3C" transform="translate(0,0)"/>
<path d="" fill="#DE8C40" transform="translate(0,0)"/>
<path d="" fill="#DD8A3F" transform="translate(0,0)"/>
<path d="" fill="#DD893E" transform="translate(0,0)"/>
<path d="" fill="#CF8646" transform="translate(0,0)"/>
<path d="" fill="#DF8B3F" transform="translate(0,0)"/>
<path d="" fill="#E7953E" transform="translate(0,0)"/>
<path d="" fill="#DB8A3F" transform="translate(0,0)"/>
<path d="" fill="#EA963C" transform="translate(0,0)"/>
<path d="" fill="#E58F3D" transform="translate(0,0)"/>
<path d="" fill="#DE8C3F" transform="translate(0,0)"/>
<path d="" fill="#E8943C" transform="translate(0,0)"/>
<path d="" fill="#DB8B3F" transform="translate(0,0)"/>
<path d="" fill="#DD8A3E" transform="translate(0,0)"/>
<path d="" fill="#DF8A3E" transform="translate(0,0)"/>
<path d="" fill="#CC8441" transform="translate(0,0)"/>
<path d="" fill="#EB943B" transform="translate(0,0)"/>
<path d="" fill="#DF8D3C" transform="translate(0,0)"/>
<path d="" fill="#E28D3F" transform="translate(0,0)"/>
<path d="" fill="#DC8C3C" transform="translate(0,0)"/>
<path d="" fill="#D48843" transform="translate(0,0)"/>
<path d="" fill="#D8883F" transform="translate(0,0)"/>
<path d="" fill="#DF883D" transform="translate(0,0)"/>
<path d="" fill="#E08E40" transform="translate(0,0)"/>
<path d="" fill="#E08B3E" transform="translate(0,0)"/>
<path d="" fill="#DF8A3D" transform="translate(0,0)"/>
<path d="" fill="#E9933D" transform="translate(0,0)"/>
<path d="" fill="#DE8C40" transform="translate(0,0)"/>
<path d="" fill="#E29141" transform="translate(0,0)"/>
<path d="" fill="#DD8C41" transform="translate(0,0)"/>
<path d="" fill="#E68D3D" transform="translate(0,0)"/>
<path d="" fill="#DF903F" transform="translate(0,0)"/>
<path d="" fill="#E28E3E" transform="translate(0,0)"/>
<path d="" fill="#E18F3E" transform="translate(0,0)"/>
<path d="" fill="#E7953A" transform="translate(0,0)"/>
<path d="" fill="#E08C3B" transform="translate(0,0)"/>
<path d="" fill="#DC8D3B" transform="translate(0,0)"/>
<path d="" fill="#DE8D3E" transform="translate(0,0)"/>
<path d="" fill="#DB8940" transform="translate(0,0)"/>
<path d="" fill="#DE8D3D" transform="translate(0,0)"/>
<path d="" fill="#CE8642" transform="translate(0,0)"/>
<path d="" fill="#E8943A" transform="translate(0,0)"/>
<path d="" fill="#E18C3C" transform="translate(0,0)"/>
<path d="" fill="#DB8A3E" transform="translate(0,0)"/>
<path d="" fill="#E18F3D" transform="translate(0,0)"/>
<path d="" fill="#E28D3D" transform="translate(0,0)"/>
<path d="" fill="#DC8B3A" transform="translate(0,0)"/>
<path d="" fill="#DE8B3E" transform="translate(0,0)"/>
<path d="" fill="#DE8D3F" transform="translate(0,0)"/>
<path d="" fill="#DF8E3E" transform="translate(0,0)"/>
<path d="" fill="#E6903F" transform="translate(0,0)"/>
<path d="" fill="#D48840" transform="translate(0,0)"/>
<path d="" fill="#E58F3D" transform="translate(0,0)"/>
<path d="" fill="#E18E3F" transform="translate(0,0)"/>
<path d="" fill="#E28E3E" transform="translate(0,0)"/>
<path d="" fill="#DD8F3E" transform="translate(0,0)"/>
<path d="" fill="#E2903F" transform="translate(0,0)"/>
<path d="" fill="#E6933A" transform="translate(0,0)"/>
<path d="" fill="#DF8E3E" transform="translate(0,0)"/>
<path d="" fill="#E58F3D" transform="translate(0,0)"/>
<path d="" fill="#E58B3B" transform="translate(0,0)"/>
<path d="" fill="#E2903B" transform="translate(0,0)"/>
<path d="" fill="#E6933C" transform="translate(0,0)"/>
<path d="" fill="#DA8F3F" transform="translate(0,0)"/>
<path d="" fill="#DE8D3D" transform="translate(0,0)"/>
<path d="" fill="#E4903A" transform="translate(0,0)"/>
<path d="" fill="#DA8C3E" transform="translate(0,0)"/>
<path d="" fill="#E08F3E" transform="translate(0,0)"/>
<path d="" fill="#DE8E3D" transform="translate(0,0)"/>
<path d="" fill="#E08D3D" transform="translate(0,0)"/>
<path d="" fill="#E08D3E" transform="translate(0,0)"/>
<path d="" fill="#E3913B" transform="translate(0,0)"/>
<path d="" fill="#E4923E" transform="translate(0,0)"/>
<path d="" fill="#E7933A" transform="translate(0,0)"/>
<path d="" fill="#E89037" transform="translate(0,0)"/>
<path d="" fill="#E08F3C" transform="translate(0,0)"/>
<path d="" fill="#E08D3D" transform="translate(0,0)"/>
<path d="" fill="#E1903C" transform="translate(0,0)"/>
<path d="" fill="#E48F3B" transform="translate(0,0)"/>
<path d="" fill="#E18E3A" transform="translate(0,0)"/>
<path d="" fill="#E4913B" transform="translate(0,0)"/>
<path d="" fill="#E0903C" transform="translate(0,0)"/>
<path d="" fill="#E2913B" transform="translate(0,0)"/>
<path d="" fill="#E08D3E" transform="translate(0,0)"/>
<path d="" fill="#E09240" transform="translate(0,0)"/>
<path d="" fill="#E18F40" transform="translate(0,0)"/>
<path d="" fill="#E2903E" transform="translate(0,0)"/>
<path d="" fill="#E4903D" transform="translate(0,0)"/>
<path d="" fill="#E4903D" transform="translate(0,0)"/>
<path d="" fill="#E7953E" transform="translate(0,0)"/>
<path d="" fill="#E0903D" transform="translate(0,0)"/>
<path d="" fill="#E3913D" transform="translate(0,0)"/>
<path d="" fill="#DF8E3D" transform="translate(0,0)"/>
<path d="" fill="#E3903E" transform="translate(0,0)"/>
<path d="" fill="#E18F3F" transform="translate(0,0)"/>
<path d="" fill="#E08E3D" transform="translate(0,0)"/>
<path d="" fill="#E18F3F" transform="translate(0,0)"/>
<path d="" fill="#E1913D" transform="translate(0,0)"/>
<path d="" fill="#E4913C" transform="translate(0,0)"/>
<path d="" fill="#E0923F" transform="translate(0,0)"/>
<path d="" fill="#DF8F3E" transform="translate(0,0)"/>
<path d="" fill="#E0903E" transform="translate(0,0)"/>
<path d="" fill="#DF8F3F" transform="translate(0,0)"/>
<path d="" fill="#D68B43" transform="translate(0,0)"/>
<path d="" fill="#DF8E43" transform="translate(0,0)"/>
<path d="" fill="#DE8C41" transform="translate(0,0)"/>
<path d="" fill="#DA8B42" transform="translate(0,0)"/>
<path d="" fill="#D88841" transform="translate(0,0)"/>
<path d="" fill="#D88B43" transform="translate(0,0)"/>
<path d="" fill="#C77F45" transform="translate(0,0)"/>
<path d="" fill="#C88144" transform="translate(0,0)"/>
<path d="" fill="#D78943" transform="translate(0,0)"/>
<path d="" fill="#DC8A41" transform="translate(0,0)"/>
<path d="" fill="#D08645" transform="translate(0,0)"/>
<path d="" fill="#CD8242" transform="translate(0,0)"/>
<path d="" fill="#C88142" transform="translate(0,0)"/>
<path d="" fill="#CF8542" transform="translate(0,0)"/>
<path d="" fill="#C37D40" transform="translate(0,0)"/>
<path d="" fill="#C88244" transform="translate(0,0)"/>
<path d="" fill="#CB8240" transform="translate(0,0)"/>
<path d="" fill="#C98042" transform="translate(0,0)"/>
<path d="" fill="#CA8444" transform="translate(0,0)"/>
<path d="" fill="#DB9044" transform="translate(0,0)"/>
<path d="" fill="#DE8941" transform="translate(0,0)"/>
<path d="" fill="#D48543" transform="translate(0,0)"/>
<path d="" fill="#E79342" transform="translate(0,0)"/>
<path d="" fill="#D68A42" transform="translate(0,0)"/>
<path d="" fill="#DA8D44" transform="translate(0,0)"/>
<path d="" fill="#D6883F" transform="translate(0,0)"/>
<path d="" fill="#D78841" transform="translate(0,0)"/>
<path d="" fill="#D58440" transform="translate(0,0)"/>
<path d="" fill="#C98242" transform="translate(0,0)"/>
<path d="" fill="#D68841" transform="translate(0,0)"/>
<path d="" fill="#C78043" transform="translate(0,0)"/>
<path d="" fill="#DB8B44" transform="translate(0,0)"/>
<path d="" fill="#E49242" transform="translate(0,0)"/>
<path d="" fill="#C87E40" transform="translate(0,0)"/>
<path d="" fill="#C88141" transform="translate(0,0)"/>
<path d="" fill="#D88941" transform="translate(0,0)"/>
<path d="" fill="#D28343" transform="translate(0,0)"/>
<path d="" fill="#E3903E" transform="translate(0,0)"/>
<path d="" fill="#D68944" transform="translate(0,0)"/>
<path d="" fill="#E49040" transform="translate(0,0)"/>
<path d="" fill="#D68941" transform="translate(0,0)"/>
<path d="" fill="#CA8040" transform="translate(0,0)"/>
<path d="" fill="#DE8D42" transform="translate(0,0)"/>
<path d="" fill="#D88943" transform="translate(0,0)"/>
<path d="" fill="#DE8B3F" transform="translate(0,0)"/>
<path d="" fill="#D68640" transform="translate(0,0)"/>
<path d="" fill="#CE8441" transform="translate(0,0)"/>
<path d="" fill="#DE8D41" transform="translate(0,0)"/>
<path d="" fill="#C98142" transform="translate(0,0)"/>
<path d="" fill="#DC8A3F" transform="translate(0,0)"/>
<path d="" fill="#DE8C40" transform="translate(0,0)"/>
<path d="" fill="#E99541" transform="translate(0,0)"/>
<path d="" fill="#E09143" transform="translate(0,0)"/>
<path d="" fill="#DF8D41" transform="translate(0,0)"/>
<path d="" fill="#DB8B41" transform="translate(0,0)"/>
<path d="" fill="#E08E3F" transform="translate(0,0)"/>
<path d="" fill="#E18F42" transform="translate(0,0)"/>
<path d="" fill="#DD8B40" transform="translate(0,0)"/>
<path d="" fill="#E5903D" transform="translate(0,0)"/>
<path d="" fill="#CC8341" transform="translate(0,0)"/>
<path d="" fill="#E1903F" transform="translate(0,0)"/>
<path d="" fill="#E08D40" transform="translate(0,0)"/>
<path d="" fill="#DE8D41" transform="translate(0,0)"/>
<path d="" fill="#E48E3E" transform="translate(0,0)"/>
<path d="" fill="#C58143" transform="translate(0,0)"/>
<path d="" fill="#DF8D3D" transform="translate(0,0)"/>
<path d="" fill="#E9973F" transform="translate(0,0)"/>
<path d="" fill="#DF8B3D" transform="translate(0,0)"/>
<path d="" fill="#DD8B3D" transform="translate(0,0)"/>
<path d="" fill="#E28F3F" transform="translate(0,0)"/>
<path d="" fill="#DF8E3F" transform="translate(0,0)"/>
<path d="" fill="#E29040" transform="translate(0,0)"/>
<path d="" fill="#DE8D40" transform="translate(0,0)"/>
<path d="" fill="#DB8C41" transform="translate(0,0)"/>
<path d="" fill="#C98141" transform="translate(0,0)"/>
<path d="" fill="#E58C3F" transform="translate(0,0)"/>
<path d="" fill="#E7923C" transform="translate(0,0)"/>
<path d="" fill="#DD8C3E" transform="translate(0,0)"/>
<path d="" fill="#E18E40" transform="translate(0,0)"/>
<path d="" fill="#DA893D" transform="translate(0,0)"/>
<path d="" fill="#D88B41" transform="translate(0,0)"/>
<path d="" fill="#DD8E43" transform="translate(0,0)"/>
<path d="" fill="#D98840" transform="translate(0,0)"/>
<path d="" fill="#E7913C" transform="translate(0,0)"/>
<path d="" fill="#E28F3E" transform="translate(0,0)"/>
<path d="" fill="#DD8D40" transform="translate(0,0)"/>
<path d="" fill="#DE8D3E" transform="translate(0,0)"/>
<path d="" fill="#DC8A40" transform="translate(0,0)"/>
<path d="" fill="#DC8E43" transform="translate(0,0)"/>
<path d="" fill="#E28C3D" transform="translate(0,0)"/>
<path d="" fill="#E48D3F" transform="translate(0,0)"/>
<path d="" fill="#DF8E40" transform="translate(0,0)"/>
<path d="" fill="#DD8B3D" transform="translate(0,0)"/>
<path d="" fill="#DB8D40" transform="translate(0,0)"/>
<path d="" fill="#DA8B3E" transform="translate(0,0)"/>
<path d="" fill="#E68E3E" transform="translate(0,0)"/>
<path d="" fill="#DE8B3F" transform="translate(0,0)"/>
<path d="" fill="#E08E40" transform="translate(0,0)"/>
<path d="" fill="#E08C3D" transform="translate(0,0)"/>
<path d="" fill="#E08B3D" transform="translate(0,0)"/>
<path d="" fill="#CD8542" transform="translate(0,0)"/>
<path d="" fill="#E19040" transform="translate(0,0)"/>
<path d="" fill="#EC9740" transform="translate(0,0)"/>
<path d="" fill="#DE8C41" transform="translate(0,0)"/>
<path d="" fill="#D8893D" transform="translate(0,0)"/>
<path d="" fill="#E18F3E" transform="translate(0,0)"/>
<path d="" fill="#E38F3D" transform="translate(0,0)"/>
<path d="" fill="#E28F3F" transform="translate(0,0)"/>
<path d="" fill="#E08D3E" transform="translate(0,0)"/>
<path d="" fill="#D08641" transform="translate(0,0)"/>
<path d="" fill="#CB8441" transform="translate(0,0)"/>
<path d="" fill="#DC8C42" transform="translate(0,0)"/>
<path d="" fill="#DD8D3E" transform="translate(0,0)"/>
<path d="" fill="#DE8B3C" transform="translate(0,0)"/>
<path d="" fill="#DA8D3F" transform="translate(0,0)"/>
<path d="" fill="#CE8541" transform="translate(0,0)"/>
<path d="" fill="#E59240" transform="translate(0,0)"/>
<path d="" fill="#E18D3E" transform="translate(0,0)"/>
<path d="" fill="#E38F3F" transform="translate(0,0)"/>
<path d="" fill="#DF8C3E" transform="translate(0,0)"/>
<path d="" fill="#DF8E3F" transform="translate(0,0)"/>
<path d="" fill="#E58F3B" transform="translate(0,0)"/>
<path d="" fill="#E8913B" transform="translate(0,0)"/>
<path d="" fill="#E18F3D" transform="translate(0,0)"/>
<path d="" fill="#E5903F" transform="translate(0,0)"/>
<path d="" fill="#E5913C" transform="translate(0,0)"/>
<path d="" fill="#E9953F" transform="translate(0,0)"/>
<path d="" fill="#EC983D" transform="translate(0,0)"/>
<path d="" fill="#E5943D" transform="translate(0,0)"/>
<path d="" fill="#E49441" transform="translate(0,0)"/>
<path d="" fill="#E2913F" transform="translate(0,0)"/>
<path d="" fill="#DE8A3B" transform="translate(0,0)"/>
<path d="" fill="#DF8F3D" transform="translate(0,0)"/>
<path d="" fill="#E5933C" transform="translate(0,0)"/>
<path d="" fill="#E48F3A" transform="translate(0,0)"/>
<path d="" fill="#DA8E41" transform="translate(0,0)"/>
<path d="" fill="#CF8742" transform="translate(0,0)"/>
<path d="" fill="#E8933B" transform="translate(0,0)"/>
<path d="" fill="#DB8D3E" transform="translate(0,0)"/>
<path d="" fill="#EB973E" transform="translate(0,0)"/>
<path d="" fill="#EA953E" transform="translate(0,0)"/>
<path d="" fill="#E6923D" transform="translate(0,0)"/>
<path d="" fill="#E7943F" transform="translate(0,0)"/>
<path d="" fill="#E5903B" transform="translate(0,0)"/>
<path d="" fill="#EE993D" transform="translate(0,0)"/>
<path d="" fill="#E6953D" transform="translate(0,0)"/>
<path d="" fill="#E7923F" transform="translate(0,0)"/>
<path d="" fill="#E38F3B" transform="translate(0,0)"/>
<path d="" fill="#E8943E" transform="translate(0,0)"/>
<path d="" fill="#E9953D" transform="translate(0,0)"/>
<path d="" fill="#E9953A" transform="translate(0,0)"/>
<path d="" fill="#E3913B" transform="translate(0,0)"/>
<path d="" fill="#EE9A3C" transform="translate(0,0)"/>
<path d="" fill="#E7953E" transform="translate(0,0)"/>
<path d="" fill="#E0903F" transform="translate(0,0)"/>
<path d="" fill="#EA953D" transform="translate(0,0)"/>
<path d="" fill="#E29040" transform="translate(0,0)"/>
<path d="" fill="#E6933F" transform="translate(0,0)"/>
<path d="" fill="#E6923D" transform="translate(0,0)"/>
<path d="" fill="#CD8441" transform="translate(0,0)"/>
<path d="" fill="#E8963F" transform="translate(0,0)"/>
<path d="" fill="#EB9439" transform="translate(0,0)"/>
<path d="" fill="#E7943F" transform="translate(0,0)"/>
<path d="" fill="#E3923B" transform="translate(0,0)"/>
<path d="" fill="#E7943D" transform="translate(0,0)"/>
<path d="" fill="#EC9A3D" transform="translate(0,0)"/>
<path d="" fill="#E8933C" transform="translate(0,0)"/>
<path d="" fill="#E79540" transform="translate(0,0)"/>
<path d="" fill="#E9933D" transform="translate(0,0)"/>
<path d="" fill="#EC953D" transform="translate(0,0)"/>
<path d="" fill="#E7923C" transform="translate(0,0)"/>
<path d="" fill="#EC9439" transform="translate(0,0)"/>
<path d="" fill="#E7943E" transform="translate(0,0)"/>
<path d="" fill="#EB9539" transform="translate(0,0)"/>
<path d="" fill="#E8933B" transform="translate(0,0)"/>
<path d="" fill="#F09D3A" transform="translate(0,0)"/>
<path d="" fill="#E89840" transform="translate(0,0)"/>
<path d="" fill="#CC8340" transform="translate(0,0)"/>
<path d="" fill="#E8943C" transform="translate(0,0)"/>
<path d="" fill="#EC963E" transform="translate(0,0)"/>
<path d="" fill="#E48F3D" transform="translate(0,0)"/>
<path d="" fill="#EA953B" transform="translate(0,0)"/>
<path d="" fill="#ED9A39" transform="translate(0,0)"/>
<path d="" fill="#DA8C3B" transform="translate(0,0)"/>
<path d="" fill="#E38F38" transform="translate(0,0)"/>
<path d="" fill="#E8963C" transform="translate(0,0)"/>
<path d="" fill="#E28F3A" transform="translate(0,0)"/>
<path d="" fill="#EA9A3B" transform="translate(0,0)"/>
<path d="" fill="#E7933B" transform="translate(0,0)"/>
<path d="" fill="#D58A3E" transform="translate(0,0)"/>
<path d="" fill="#EB9239" transform="translate(0,0)"/>
<path d="" fill="#EC973A" transform="translate(0,0)"/>
<path d="" fill="#EA973F" transform="translate(0,0)"/>
<path d="" fill="#EC993E" transform="translate(0,0)"/>
<path d="" fill="#DE903E" transform="translate(0,0)"/>
<path d="" fill="#DF923B" transform="translate(0,0)"/>
<path d="" fill="#EC963A" transform="translate(0,0)"/>
<path d="" fill="#E7973D" transform="translate(0,0)"/>
<path d="" fill="#DC8E3E" transform="translate(0,0)"/>
<path d="" fill="#E1903E" transform="translate(0,0)"/>
<path d="" fill="#E6923A" transform="translate(0,0)"/>
<path d="" fill="#E0903D" transform="translate(0,0)"/>
<path d="" fill="#EE993C" transform="translate(0,0)"/>
<path d="" fill="#E1933E" transform="translate(0,0)"/>
<path d="" fill="#E0923E" transform="translate(0,0)"/>
<path d="" fill="#EB973C" transform="translate(0,0)"/>
<path d="" fill="#E7953C" transform="translate(0,0)"/>
<path d="" fill="#ED983E" transform="translate(0,0)"/>
<path d="" fill="#E6943B" transform="translate(0,0)"/>
<path d="" fill="#EA983D" transform="translate(0,0)"/>
<path d="" fill="#EF9B3C" transform="translate(0,0)"/>
<path d="" fill="#E9983E" transform="translate(0,0)"/>
<path d="" fill="#E3923E" transform="translate(0,0)"/>
<path d="" fill="#E3913C" transform="translate(0,0)"/>
<path d="" fill="#E18E3B" transform="translate(0,0)"/>
<path d="" fill="#DE8E3C" transform="translate(0,0)"/>
<path d="" fill="#E2923E" transform="translate(0,0)"/>
<path d="" fill="#E1903D" transform="translate(0,0)"/>
<path d="" fill="#E3933B" transform="translate(0,0)"/>
<path d="" fill="#E49238" transform="translate(0,0)"/>
<path d="" fill="#E6913B" transform="translate(0,0)"/>
<path d="" fill="#E5943D" transform="translate(0,0)"/>
<path d="" fill="#ED9A38" transform="translate(0,0)"/>
<path d="" fill="#F09C3C" transform="translate(0,0)"/>
<path d="" fill="#E2903C" transform="translate(0,0)"/>
<path d="" fill="#E4923C" transform="translate(0,0)"/>
<path d="" fill="#E3913C" transform="translate(0,0)"/>
<path d="" fill="#CB8640" transform="translate(0,0)"/>
<path d="" fill="#D28A3E" transform="translate(0,0)"/>
<path d="" fill="#E3913A" transform="translate(0,0)"/>
<path d="" fill="#D1893F" transform="translate(0,0)"/>
<path d="" fill="#E69239" transform="translate(0,0)"/>
<path d="" fill="#EB983C" transform="translate(0,0)"/>
<path d="" fill="#E8953B" transform="translate(0,0)"/>
<path d="" fill="#EA993A" transform="translate(0,0)"/>
<path d="" fill="#EB9A3B" transform="translate(0,0)"/>
<path d="" fill="#D48A3E" transform="translate(0,0)"/>
<path d="" fill="#D68B37" transform="translate(0,0)"/>
<path d="" fill="#E39239" transform="translate(0,0)"/>
<path d="" fill="#E6943C" transform="translate(0,0)"/>
<path d="" fill="#E79439" transform="translate(0,0)"/>
<path d="" fill="#E6973B" transform="translate(0,0)"/>
<path d="" fill="#ED973A" transform="translate(0,0)"/>
<path d="" fill="#E69640" transform="translate(0,0)"/>
<path d="" fill="#E6933D" transform="translate(0,0)"/>
<path d="" fill="#E7933A" transform="translate(0,0)"/>
<path d="" fill="#E8943D" transform="translate(0,0)"/>
<path d="" fill="#CE843C" transform="translate(0,0)"/>
<path d="" fill="#E7953A" transform="translate(0,0)"/>
<path d="" fill="#E4963D" transform="translate(0,0)"/>
<path d="" fill="#EB973A" transform="translate(0,0)"/>
<path d="" fill="#E3903A" transform="translate(0,0)"/>
<path d="" fill="#D4883C" transform="translate(0,0)"/>
<path d="" fill="#EB963A" transform="translate(0,0)"/>
<path d="" fill="#E7963A" transform="translate(0,0)"/>
<path d="" fill="#E99439" transform="translate(0,0)"/>
<path d="" fill="#EC9539" transform="translate(0,0)"/>
<path d="" fill="#EC963B" transform="translate(0,0)"/>
<path d="" fill="#E7933A" transform="translate(0,0)"/>
<path d="" fill="#E9933B" transform="translate(0,0)"/>
<path d="" fill="#E5913B" transform="translate(0,0)"/>
<path d="" fill="#E9943C" transform="translate(0,0)"/>
<path d="" fill="#E48F3C" transform="translate(0,0)"/>
<path d="" fill="#E5943C" transform="translate(0,0)"/>
<path d="" fill="#E6943C" transform="translate(0,0)"/>
<path d="" fill="#E69339" transform="translate(0,0)"/>
<path d="" fill="#E9953C" transform="translate(0,0)"/>
<path d="" fill="#E8933B" transform="translate(0,0)"/>
<path d="" fill="#EA983B" transform="translate(0,0)"/>
<path d="" fill="#E9963B" transform="translate(0,0)"/>
<path d="" fill="#EB993D" transform="translate(0,0)"/>
<path d="" fill="#E6933A" transform="translate(0,0)"/>
<path d="" fill="#D78A3B" transform="translate(0,0)"/>
<path d="" fill="#EC9739" transform="translate(0,0)"/>
<path d="" fill="#E7983E" transform="translate(0,0)"/>
<path d="" fill="#DA8938" transform="translate(0,0)"/>
<path d="" fill="#E4953A" transform="translate(0,0)"/>
<path d="" fill="#E5943D" transform="translate(0,0)"/>
<path d="" fill="#D3893C" transform="translate(0,0)"/>
<path d="" fill="#E59339" transform="translate(0,0)"/>
<path d="" fill="#D2883E" transform="translate(0,0)"/>
<path d="" fill="#E7953A" transform="translate(0,0)"/>
<path d="" fill="#D38539" transform="translate(0,0)"/>
<path d="" fill="#E79539" transform="translate(0,0)"/>
<path d="" fill="#EB963C" transform="translate(0,0)"/>
<path d="" fill="#EE9A3C" transform="translate(0,0)"/>
<path d="" fill="#E9973A" transform="translate(0,0)"/>
<path d="" fill="#DD8E3A" transform="translate(0,0)"/>
<path d="" fill="#DD8F3B" transform="translate(0,0)"/>
<path d="" fill="#CE883C" transform="translate(0,0)"/>
<path d="" fill="#E2943D" transform="translate(0,0)"/>
<path d="" fill="#DC8C3B" transform="translate(0,0)"/>
<path d="" fill="#E29139" transform="translate(0,0)"/>
<path d="" fill="#E69439" transform="translate(0,0)"/>
<path d="" fill="#E09340" transform="translate(0,0)"/>
<path d="" fill="#E3953B" transform="translate(0,0)"/>
<path d="" fill="#DF923E" transform="translate(0,0)"/>
<path d="" fill="#DD903A" transform="translate(0,0)"/>
<path d="" fill="#E9973B" transform="translate(0,0)"/>
<path d="" fill="#DC8F3A" transform="translate(0,0)"/>
<path d="" fill="#E6933A" transform="translate(0,0)"/>
<path d="" fill="#E2933A" transform="translate(0,0)"/>
<path d="" fill="#E19439" transform="translate(0,0)"/>
<path d="" fill="#E39338" transform="translate(0,0)"/>
<path d="" fill="#E0923A" transform="translate(0,0)"/>
<path d="" fill="#E59237" transform="translate(0,0)"/>
<path d="" fill="#E0903C" transform="translate(0,0)"/>
<path d="" fill="#D98B39" transform="translate(0,0)"/>
<path d="" fill="#E08F3B" transform="translate(0,0)"/>
<path d="" fill="#DF8B38" transform="translate(0,0)"/>
<path d="" fill="#DB8B38" transform="translate(0,0)"/>
<path d="" fill="#D38737" transform="translate(0,0)"/>
<path d="" fill="#E08F38" transform="translate(0,0)"/>
<path d="" fill="#E7963D" transform="translate(0,0)"/>
<path d="" fill="#E3933C" transform="translate(0,0)"/>
<path d="" fill="#E2923A" transform="translate(0,0)"/>
<path d="" fill="#DF8E3A" transform="translate(0,0)"/>
<path d="" fill="#DD943E" transform="translate(0,0)"/>
<path d="" fill="#E0923F" transform="translate(0,0)"/>
<path d="" fill="#D88D3B" transform="translate(0,0)"/>
<path d="" fill="#D68B3C" transform="translate(0,0)"/>
<path d="" fill="#DE903A" transform="translate(0,0)"/>
<path d="" fill="#DC8E39" transform="translate(0,0)"/>
<path d="" fill="#D98E3B" transform="translate(0,0)"/>
<path d="" fill="#DC903C" transform="translate(0,0)"/>
<path d="" fill="#DB903C" transform="translate(0,0)"/>
<path d="" fill="#E09039" transform="translate(0,0)"/>
<path d="" fill="#E59538" transform="translate(0,0)"/>
<path d="" fill="#DC913A" transform="translate(0,0)"/>
<path d="" fill="#DF8F3C" transform="translate(0,0)"/>
<path d="" fill="#DD913A" transform="translate(0,0)"/>
<path d="" fill="#E19039" transform="translate(0,0)"/>
<path d="" fill="#DD9039" transform="translate(0,0)"/>
<path d="" fill="#DC903B" transform="translate(0,0)"/>
<path d="" fill="#DA8F39" transform="translate(0,0)"/>
<path d="" fill="#D88C3B" transform="translate(0,0)"/>
<path d="" fill="#DF8F39" transform="translate(0,0)"/>
<path d="" fill="#DC8E39" transform="translate(0,0)"/>
<path d="" fill="#DB8F38" transform="translate(0,0)"/>
<path d="" fill="#D78E3A" transform="translate(0,0)"/>
<path d="" fill="#DB8D3A" transform="translate(0,0)"/>
<path d="" fill="#DD923C" transform="translate(0,0)"/>
<path d="" fill="#DE8F39" transform="translate(0,0)"/>
<path d="" fill="#DD8E3A" transform="translate(0,0)"/>
<path d="" fill="#D98D3A" transform="translate(0,0)"/>
<path d="" fill="#D78C3B" transform="translate(0,0)"/>
</svg>
