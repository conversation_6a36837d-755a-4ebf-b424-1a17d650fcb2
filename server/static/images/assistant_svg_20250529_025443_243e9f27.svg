<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C1.518 0.042 3.036 0.09 4.554 0.143 C5.751 0.178 5.751 0.178 6.972 0.214 C9.529 0.291 12.087 0.375 14.644 0.459 C16.374 0.512 18.104 0.565 19.834 0.617 C24.083 0.747 28.333 0.883 32.582 1.022 C36.389 8.352 39.692 15.822 42.832 23.459 C43.501 25.071 43.501 25.071 44.183 26.715 C47.421 34.54 47.421 34.54 48.582 38.022 C49.572 38.517 49.572 38.517 50.582 39.022 C50.087 40.012 50.087 40.012 49.582 41.022 C52.959 40.284 52.959 40.284 54.375 36.543 C55.041 35.001 55.695 33.453 56.34 31.901 C56.862 30.678 56.862 30.678 57.396 29.431 C58.51 26.819 59.608 24.202 60.707 21.584 C61.454 19.823 62.203 18.062 62.953 16.301 C64.324 13.08 65.688 9.856 67.047 6.629 C67.85 4.742 68.664 2.856 69.582 1.022 C76.724 -0.561 84.436 -0.101 91.707 -0.041 C93.285 -0.034 93.285 -0.034 94.894 -0.027 C97.457 -0.015 100.019 0.001 102.582 0.022 C103.961 2.78 103.708 4.994 103.709 8.082 C103.713 10.021 103.713 10.021 103.717 11.999 C103.715 13.44 103.713 14.881 103.711 16.322 C103.713 17.833 103.714 19.344 103.716 20.855 C103.721 24.967 103.719 29.078 103.716 33.19 C103.714 37.488 103.716 41.786 103.717 46.085 C103.719 53.306 103.717 60.527 103.712 67.749 C103.707 76.1 103.708 84.451 103.714 92.803 C103.719 99.967 103.719 107.131 103.717 114.296 C103.715 118.577 103.715 122.858 103.718 127.14 C103.721 131.167 103.719 135.193 103.713 139.22 C103.712 140.699 103.712 142.177 103.714 143.656 C103.717 145.673 103.713 147.69 103.709 149.708 C103.708 151.402 103.708 151.402 103.708 153.13 C103.6 155.61 103.306 157.66 102.582 160.022 C103.242 160.352 103.902 160.682 104.582 161.022 C95.037 165.794 83.254 162.022 72.582 162.022 C72.252 129.022 71.922 96.022 71.582 62.022 C67.622 71.427 67.622 71.427 63.582 81.022 C62.262 83.992 60.942 86.962 59.582 90.022 C53.832 91.605 49.105 91.562 43.582 89.022 C41.707 85.959 41.707 85.959 40.582 83.022 C40.252 82.692 39.922 82.362 39.582 82.022 C38.592 82.517 38.592 82.517 37.582 83.022 C37.623 82.135 37.664 81.248 37.707 80.334 C37.583 77.049 36.968 74.967 35.582 72.022 C34.922 72.022 34.262 72.022 33.582 72.022 C33.437 70.887 33.293 69.753 33.144 68.584 C32.97 65.214 32.97 65.214 31.582 64.022 C31.558 71.187 31.541 78.352 31.53 85.517 C31.525 88.847 31.517 92.177 31.506 95.506 C31.461 109.558 31.452 123.572 32.317 137.602 C32.595 142.517 32.493 147.139 31.816 152.02 C31.504 154.348 31.504 154.348 31.582 158.022 C31.582 159.012 31.582 160.002 31.582 161.022 C29.577 163.027 25.582 162.278 22.828 162.338 C21.665 162.369 21.665 162.369 20.478 162.401 C17.992 162.467 15.506 162.526 13.019 162.584 C11.338 162.627 9.656 162.671 7.974 162.715 C3.844 162.822 -0.287 162.924 -4.418 163.022 C-5.492 156.914 -5.56 150.958 -5.548 144.767 C-5.55 143.094 -5.55 143.094 -5.553 141.387 C-5.557 137.698 -5.554 134.008 -5.551 130.319 C-5.552 127.722 -5.553 125.125 -5.554 122.529 C-5.556 117.068 -5.553 111.607 -5.549 106.147 C-5.544 100.614 -5.544 95.08 -5.549 89.547 C-5.594 36.849 -5.594 36.849 -4.973 14.725 C-4.953 13.996 -4.934 13.267 -4.914 12.517 C-4.567 0.029 -4.567 0.029 0 0 Z " fill="#DC673F" transform="translate(442.418212890625,76.978271484375)"/>
<path d="M0 0 C12.21 0 24.42 0 37 0 C38.258 3.692 39.516 7.384 40.812 11.188 C45.493 24.799 50.587 38.231 55.734 51.673 C58.557 59.09 61.273 66.547 64 74 C64.33 64.76 64.66 55.52 65 46 C63.515 45.505 63.515 45.505 62 45 C62.66 44.34 63.32 43.68 64 43 C64.242 41.019 64.242 41.019 64.125 38.875 C64.084 37.596 64.043 36.317 64 35 C63.67 35 63.34 35 63 35 C63 33.02 63 31.04 63 29 C63.33 29 63.66 29 64 29 C64 25.7 64 22.4 64 19 C63.67 19 63.34 19 63 19 C62.505 16.03 62.505 16.03 62 13 C62.66 12.67 63.32 12.34 64 12 C63.959 10.948 63.918 9.896 63.875 8.812 C64 5 64 5 66 0 C76.89 0 87.78 0 99 0 C100.62 40.501 100.62 40.501 99 49 C99.66 49.33 100.32 49.66 101 50 C100.34 50 99.68 50 99 50 C99.077 51.142 99.155 52.283 99.234 53.46 C99.924 64.42 100.012 75.244 99.648 86.216 C99.499 90.751 99.374 95.285 99.254 99.82 C99.229 100.743 99.205 101.666 99.179 102.617 C98.895 114.079 98.947 125.541 99.037 137.005 C99.042 137.762 99.046 138.52 99.05 139.3 C99.062 141.328 99.079 143.356 99.098 145.384 C98.999 149.029 98.588 152.407 98 156 C94.454 156.808 91.177 157.128 87.543 157.133 C86.512 157.134 85.482 157.135 84.42 157.137 C83.353 157.133 82.287 157.129 81.188 157.125 C80.132 157.129 79.077 157.133 77.99 157.137 C72.231 157.13 66.695 156.866 61 156 C60.622 154.916 60.245 153.832 59.855 152.715 C59.3 151.122 58.744 149.53 58.188 147.938 C57.887 147.077 57.587 146.216 57.277 145.329 C55.235 139.491 53.169 133.663 51.086 127.84 C50.582 126.428 50.078 125.016 49.573 123.604 C48.818 121.489 48.062 119.375 47.303 117.261 C46.586 115.266 45.873 113.27 45.16 111.273 C44.744 110.112 44.328 108.952 43.899 107.755 C43 105 43 105 43 103 C42.01 102.505 42.01 102.505 41 102 C40.105 98.923 39.461 95.819 38.781 92.688 C38.395 91.357 38.395 91.357 38 90 C37.01 89.505 37.01 89.505 36 89 C35.148 86.496 35.148 86.496 34.375 83.438 C34.115 82.426 33.854 81.414 33.586 80.371 C33.393 79.589 33.199 78.806 33 78 C32.67 78 32.34 78 32 78 C32 103.74 32 129.48 32 156 C29.446 157.277 27.622 157.129 24.766 157.133 C23.192 157.135 23.192 157.135 21.586 157.137 C20.485 157.133 19.384 157.129 18.25 157.125 C17.165 157.129 16.079 157.133 14.961 157.137 C13.379 157.135 13.379 157.135 11.766 157.133 C10.804 157.132 9.843 157.131 8.853 157.129 C6 157 6 157 0 156 C0 104.52 0 53.04 0 0 Z " fill="#D66644" transform="translate(626,270)"/>
<path d="M0 0 C11.88 0 23.76 0 36 0 C39 8 39 8 39 11 C39.66 11.33 40.32 11.66 41 12 C41.719 13.921 41.719 13.921 42.39 16.416 C43.926 21.834 45.785 27.075 47.758 32.348 C48.106 33.286 48.455 34.223 48.814 35.19 C49.544 37.151 50.275 39.111 51.007 41.071 C52.133 44.084 53.252 47.1 54.371 50.115 C55.083 52.026 55.795 53.937 56.508 55.848 C56.843 56.752 57.179 57.656 57.524 58.587 C57.835 59.416 58.145 60.244 58.465 61.098 C58.738 61.829 59.011 62.561 59.293 63.315 C59.876 65.002 59.876 65.002 61 66 C61.33 44.22 61.66 22.44 62 0 C72.89 0 83.78 0 95 0 C96.405 2.811 96.123 5.132 96.12 8.277 C96.121 8.928 96.122 9.579 96.123 10.249 C96.124 12.441 96.119 14.632 96.114 16.823 C96.113 18.39 96.113 19.956 96.114 21.523 C96.114 25.785 96.108 30.047 96.101 34.31 C96.095 38.76 96.095 43.211 96.093 47.662 C96.09 56.095 96.082 64.527 96.072 72.96 C96.061 82.558 96.055 92.157 96.05 101.755 C96.04 121.503 96.022 141.252 96 161 C85.11 161 74.22 161 63 161 C61.144 156.072 59.291 151.143 57.446 146.211 C56.817 144.533 56.187 142.856 55.554 141.18 C54.646 138.771 53.744 136.361 52.844 133.949 C52.559 133.199 52.275 132.449 51.982 131.676 C50.858 128.65 50 126.256 50 123 C49.01 122.505 49.01 122.505 48 122 C47.21 120.257 47.21 120.257 46.449 117.984 C46.165 117.151 45.881 116.317 45.588 115.458 C45.291 114.565 44.994 113.671 44.688 112.75 C44.065 110.925 43.442 109.101 42.816 107.277 C42.51 106.38 42.203 105.483 41.887 104.558 C39.448 95.638 39.448 95.638 35 88 C33.903 91.291 33.886 93.688 33.902 97.152 C33.906 98.42 33.909 99.688 33.912 100.995 C33.919 102.011 33.919 102.011 33.925 103.047 C33.938 105.17 33.944 107.293 33.949 109.415 C33.962 114.688 33.987 119.96 34.01 125.232 C34.03 129.687 34.046 134.142 34.056 138.598 C34.062 140.687 34.075 142.777 34.088 144.866 C34.091 146.137 34.094 147.409 34.098 148.719 C34.103 149.838 34.108 150.957 34.114 152.109 C34.016 154.595 33.727 156.635 33 159 C33.99 159.33 34.98 159.66 36 160 C32.16 161.785 29.151 162.247 24.93 162.23 C23.786 162.229 22.643 162.227 21.465 162.225 C20.28 162.212 19.096 162.2 17.875 162.188 C16.683 162.187 15.49 162.186 14.262 162.186 C5.422 162.141 5.422 162.141 2 161 C2 111.17 2 61.34 2 10 C1.34 10 0.68 10 0 10 C0.293 7.982 0.586 5.964 0.879 3.945 C1.168 1.919 1.168 1.919 0 0 Z M34 84 C35 87 35 87 35 87 Z " fill="#DC663F" transform="translate(616,78)"/>
<path d="M0 0 C12.549 13.856 11.944 32.054 11.855 49.633 C11.854 51.236 11.854 52.838 11.854 54.441 C11.853 57.77 11.844 61.1 11.831 64.429 C11.814 68.656 11.81 72.882 11.811 77.109 C11.81 80.409 11.805 83.708 11.798 87.008 C11.795 88.565 11.793 90.122 11.792 91.679 C11.754 129.199 11.754 129.199 -0.227 142.418 C-4.748 146.642 -9.546 149.349 -15.375 151.375 C-16.399 151.736 -17.422 152.097 -18.477 152.469 C-23.323 153.818 -28.194 153.627 -33.188 153.562 C-34.588 153.562 -34.588 153.562 -36.018 153.561 C-49.254 153.478 -49.254 153.478 -54.375 152.375 C-54.87 151.385 -54.87 151.385 -55.375 150.375 C-56.779 149.486 -58.224 148.661 -59.688 147.875 C-60.467 147.452 -61.247 147.029 -62.051 146.594 C-64.147 145.495 -66.253 144.424 -68.375 143.375 C-68.375 142.715 -68.375 142.055 -68.375 141.375 C-69.365 141.705 -70.355 142.035 -71.375 142.375 C-72.375 139.375 -72.375 139.375 -72.375 136.375 C-73.152 134.382 -73.971 132.404 -74.812 130.438 C-77.358 124.038 -78.239 118.229 -78.375 111.375 C-79.035 111.375 -79.695 111.375 -80.375 111.375 C-80.082 109.357 -79.789 107.339 -79.496 105.32 C-79.207 103.294 -79.207 103.294 -80.375 101.375 C-79.715 101.375 -79.055 101.375 -78.375 101.375 C-78.516 99.083 -78.663 96.791 -78.812 94.5 C-78.934 92.586 -78.934 92.586 -79.059 90.633 C-79.167 87.492 -79.167 87.492 -80.375 85.375 C-79.715 85.375 -79.055 85.375 -78.375 85.375 C-78.375 84.385 -78.375 83.395 -78.375 82.375 C-79.035 82.375 -79.695 82.375 -80.375 82.375 C-80.375 80.725 -80.375 79.075 -80.375 77.375 C-80.045 77.375 -79.715 77.375 -79.375 77.375 C-79.375 75.725 -79.375 74.075 -79.375 72.375 C-79.705 72.375 -80.035 72.375 -80.375 72.375 C-80.375 70.395 -80.375 68.415 -80.375 66.375 C-80.045 66.375 -79.715 66.375 -79.375 66.375 C-79.045 63.735 -78.715 61.095 -78.375 58.375 C-79.035 58.375 -79.695 58.375 -80.375 58.375 C-80.131 57.341 -80.131 57.341 -79.883 56.285 C-79.323 53.075 -79.242 50.07 -79.25 46.812 C-79.247 45.697 -79.245 44.582 -79.242 43.434 C-79.353 40.883 -79.628 38.799 -80.375 36.375 C-79.385 35.88 -79.385 35.88 -78.375 35.375 C-79.035 34.385 -79.695 33.395 -80.375 32.375 C-80.5 29.688 -80.5 29.688 -80.375 27.375 C-79.715 27.375 -79.055 27.375 -78.375 27.375 C-78.137 26.016 -78.137 26.016 -77.895 24.629 C-76.441 17.042 -74.777 10.76 -70.375 4.375 C-69.653 3.323 -68.931 2.271 -68.188 1.188 C-50.239 -16.761 -18.649 -16.925 0 0 Z M-40.964 23.656 C-46.589 30.508 -44.665 43.18 -44.641 51.598 C-44.642 53.27 -44.644 54.941 -44.646 56.613 C-44.649 60.104 -44.645 63.595 -44.636 67.085 C-44.624 71.542 -44.631 75.999 -44.643 80.457 C-44.65 83.905 -44.648 87.353 -44.643 90.801 C-44.641 92.444 -44.643 94.086 -44.648 95.729 C-44.653 98.031 -44.645 100.332 -44.634 102.633 C-44.632 103.938 -44.631 105.243 -44.629 106.588 C-44.306 111.408 -43.881 116.521 -40.75 120.375 C-40.296 120.705 -39.842 121.035 -39.375 121.375 C-37.89 120.88 -37.89 120.88 -36.375 120.375 C-36.045 120.705 -35.715 121.035 -35.375 121.375 C-32.37 122.377 -31.511 122.42 -28.375 121.375 C-24.643 118.887 -24.414 117.676 -23.375 113.375 C-23.129 110.488 -23.141 107.626 -23.18 104.73 C-23.184 103.515 -23.184 103.515 -23.188 102.275 C-23.199 99.704 -23.224 97.133 -23.25 94.562 C-23.26 92.816 -23.269 91.069 -23.277 89.322 C-23.3 85.006 -23.335 80.691 -23.375 76.375 C-23.375 74.623 -23.356 72.87 -23.343 71.118 C-23.307 66.052 -23.284 60.987 -23.265 55.921 C-23.255 53.731 -23.241 51.541 -23.224 49.351 C-23.2 46.197 -23.189 43.044 -23.18 39.891 C-23.169 38.913 -23.159 37.936 -23.148 36.929 C-23.042 30.38 -23.042 30.38 -25.375 24.375 C-28.288 23.404 -30.832 22.969 -33.875 22.625 C-34.834 22.506 -35.793 22.388 -36.781 22.266 C-39.378 22.118 -39.378 22.118 -40.964 23.656 Z " fill="#D66540" transform="translate(410.375,87.625)"/>
<path d="M0 0 C0.822 0.005 1.645 0.009 2.492 0.014 C4.495 0.025 6.497 0.043 8.5 0.062 C8.5 0.723 8.5 1.382 8.5 2.062 C9.387 2.186 10.274 2.31 11.188 2.438 C14.225 3.011 16.657 3.896 19.5 5.062 C21.159 5.431 22.825 5.775 24.5 6.062 C24.5 6.723 24.5 7.382 24.5 8.062 C25.366 8.743 26.233 9.424 27.125 10.125 C35.89 17.191 39.178 27.265 40.5 38.062 C40.642 40.916 40.657 43.765 40.661 46.621 C40.669 47.862 40.669 47.862 40.676 49.128 C40.691 51.847 40.698 54.566 40.703 57.285 C40.707 58.689 40.707 58.689 40.712 60.121 C40.727 65.078 40.736 70.035 40.74 74.992 C40.746 80.076 40.77 85.161 40.798 90.245 C40.817 94.186 40.822 98.126 40.824 102.067 C40.827 103.939 40.835 105.812 40.848 107.684 C40.952 123.51 40.598 140.186 29.562 152.75 C17.294 164.203 2.292 164.727 -13.54 164.268 C-18.237 163.942 -22.133 162.82 -26.461 160.938 C-28.59 159.865 -28.59 159.865 -31.5 160.062 C-35.562 156.44 -39.008 152.226 -42.5 148.062 C-43.82 148.393 -45.14 148.722 -46.5 149.062 C-47.434 146.052 -47.544 145.196 -46.5 142.062 C-47.139 140.054 -47.81 138.055 -48.5 136.062 C-49.473 130.623 -49.659 125.349 -49.677 119.845 C-49.684 118.948 -49.69 118.051 -49.697 117.127 C-49.717 114.176 -49.728 111.225 -49.738 108.273 C-49.742 107.259 -49.746 106.245 -49.751 105.199 C-49.771 99.829 -49.786 94.458 -49.795 89.087 C-49.806 83.57 -49.841 78.053 -49.88 72.536 C-49.907 68.267 -49.915 63.998 -49.918 59.728 C-49.923 57.696 -49.935 55.664 -49.953 53.631 C-50.184 26.852 -50.184 26.852 -41.5 16.062 C-40.799 15.072 -40.098 14.082 -39.375 13.062 C-29.587 1.624 -14.051 -0.149 0 0 Z M-11.5 35.062 C-16.253 39.558 -16.391 44.228 -16.634 50.532 C-16.638 51.748 -16.641 52.964 -16.645 54.216 C-16.649 54.894 -16.652 55.571 -16.655 56.269 C-16.665 58.488 -16.667 60.707 -16.668 62.926 C-16.671 64.484 -16.675 66.042 -16.678 67.599 C-16.684 70.858 -16.686 74.117 -16.685 77.376 C-16.685 81.528 -16.699 85.68 -16.716 89.832 C-16.727 93.049 -16.729 96.265 -16.729 99.481 C-16.73 101.011 -16.734 102.541 -16.742 104.07 C-17.288 117.921 -17.288 117.921 -12.812 130.625 C-9.609 132.616 -7.267 132.998 -3.5 133.062 C-0.135 132.38 -0.135 132.38 2.5 130.062 C5.183 125.078 6.059 120.588 4.5 115.062 C4.83 115.062 5.16 115.062 5.5 115.062 C5.525 110.987 5.543 106.912 5.555 102.837 C5.56 101.451 5.567 100.065 5.575 98.679 C5.588 96.686 5.593 94.692 5.598 92.699 C5.603 91.5 5.608 90.301 5.614 89.065 C5.638 86.011 5.638 86.011 4.5 83.062 C4.938 80.375 4.938 80.375 5.5 78.062 C5.17 78.722 4.84 79.382 4.5 80.062 C4.5 79.072 4.5 78.082 4.5 77.062 C4.83 77.062 5.16 77.062 5.5 77.062 C5.55 73.116 5.586 69.169 5.61 65.222 C5.625 63.231 5.65 61.241 5.676 59.25 C5.713 51.931 5.356 45.16 3.5 38.062 C2.51 37.733 1.52 37.403 0.5 37.062 C0.5 36.403 0.5 35.743 0.5 35.062 C-4.244 33.029 -6.832 32.728 -11.5 35.062 Z " fill="#CF6446" transform="translate(463.5,265.9375)"/>
<path d="M0 0 C0.949 0.041 1.898 0.082 2.875 0.125 C3.37 1.115 3.37 1.115 3.875 2.125 C5.889 2.671 5.889 2.671 8.312 3 C19.859 5.077 27.558 9.479 34.5 18.812 C43.701 32.394 42.031 49.717 42.008 65.477 C42.008 67.109 42.009 68.742 42.011 70.375 C42.012 73.772 42.01 77.169 42.005 80.565 C42 84.886 42.003 89.207 42.009 93.528 C42.013 96.892 42.011 100.255 42.009 103.618 C42.008 105.21 42.009 106.802 42.011 108.394 C42.034 127.968 41.124 142.784 27.875 158.125 C24.92 161.08 21.601 161.916 17.625 163.062 C16.717 163.413 15.81 163.764 14.875 164.125 C14.545 165.115 14.215 166.105 13.875 167.125 C13.091 166.939 12.308 166.754 11.5 166.562 C8.904 165.865 8.904 165.865 6.875 167.125 C-6.364 167.733 -21.43 166.681 -32.125 158.125 C-33.362 157.321 -33.362 157.321 -34.625 156.5 C-42.621 148.904 -46.105 138.554 -46.529 127.752 C-46.636 123.479 -46.678 119.208 -46.707 114.934 C-46.729 113.06 -46.752 111.187 -46.776 109.314 C-46.823 105.397 -46.859 101.479 -46.888 97.561 C-46.926 92.588 -46.986 87.615 -47.054 82.642 C-47.104 78.774 -47.143 74.905 -47.179 71.036 C-47.198 69.205 -47.219 67.374 -47.243 65.544 C-47.655 32.699 -47.655 32.699 -41.125 22.125 C-40.715 21.443 -40.305 20.761 -39.883 20.059 C-34.457 11.982 -26.348 6.876 -17.125 4.125 C-16.135 4.125 -15.145 4.125 -14.125 4.125 C-13.795 3.465 -13.465 2.805 -13.125 2.125 C-11.846 2.166 -10.567 2.207 -9.25 2.25 C-7.106 2.367 -7.106 2.367 -5.125 2.125 C-3.125 0.125 -3.125 0.125 0 0 Z M-6.125 36.125 C-6.125 37.445 -6.125 38.765 -6.125 40.125 C-7.445 39.795 -8.765 39.465 -10.125 39.125 C-14.361 47.88 -13.288 58.574 -13.293 68.055 C-13.296 69.495 -13.3 70.936 -13.303 72.376 C-13.309 75.383 -13.311 78.391 -13.31 81.398 C-13.31 85.223 -13.324 89.047 -13.341 92.872 C-13.352 95.844 -13.354 98.816 -13.354 101.788 C-13.355 103.197 -13.359 104.605 -13.367 106.014 C-13.97 120.127 -13.97 120.127 -9.062 132.812 C-5.013 134.622 -1.489 134.957 2.875 134.125 C5.377 132.249 6.494 130.886 7.875 128.125 C8.245 124.346 8.308 120.543 8.438 116.75 C8.479 115.557 8.521 114.363 8.564 113.134 C8.937 100.903 8.985 88.673 9 76.438 C9.003 75.439 9.005 74.44 9.008 73.411 C9.014 70.562 9.012 67.713 9.008 64.863 C9.01 63.995 9.012 63.126 9.014 62.231 C9.38 58.367 9.38 58.367 7.875 55.125 C7.843 52.147 7.943 49.18 8.035 46.203 C7.869 43.008 7.374 40.941 5.875 38.125 C1.255 35.334 -1.044 34.77 -6.125 36.125 Z " fill="#CF6444" transform="translate(566.125,263.875)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.583 1.856 2.165 1.711 2.766 1.562 C9.602 0.015 16.011 -0.267 23 0 C23.749 -0.005 24.498 -0.01 25.27 -0.016 C33.716 0.298 42.355 4.692 48.535 10.301 C56.863 20.061 58.45 29.825 58.674 42.271 C58.7 43.531 58.7 43.531 58.727 44.816 C58.733 45.574 58.738 46.331 58.745 47.112 C58.839 49.243 58.839 49.243 61 51 C60.01 51.99 60.01 51.99 59 53 C58.352 55.571 58.352 55.571 58 58 C57.01 58 56.02 58 55 58 C54.01 58.99 54.01 58.99 53 60 C50.69 59.34 48.38 58.68 46 58 C45.01 58.99 45.01 58.99 44 60 C42.328 59.692 40.662 59.356 39 59 C36.946 59.409 36.946 59.409 35 60 C31 60 31 60 28.25 58.625 C25.268 55.147 25.722 53.099 25.945 48.676 C26.027 44.689 25.497 40.715 24 37 C23.01 36.34 22.02 35.68 21 35 C20.01 35.33 19.02 35.66 18 36 C15.667 36.04 13.333 36.044 11 36 C8.679 41.497 7.562 46.048 7.628 52.049 C7.624 52.81 7.62 53.57 7.617 54.353 C7.609 56.841 7.622 59.329 7.637 61.816 C7.637 63.559 7.637 65.302 7.635 67.045 C7.635 70.687 7.646 74.329 7.664 77.971 C7.687 82.627 7.687 87.283 7.68 91.939 C7.677 95.534 7.684 99.13 7.693 102.725 C7.697 104.441 7.698 106.158 7.697 107.874 C7.696 110.276 7.709 112.678 7.725 115.08 C7.723 115.781 7.72 116.482 7.718 117.204 C7.778 122.874 9.224 127.671 11 133 C12.217 132.835 13.434 132.67 14.688 132.5 C17.816 132.105 20.842 131.901 24 132 C24.904 128.163 25.106 124.625 25.062 120.688 C25.053 119.619 25.044 118.55 25.035 117.449 C25.024 116.641 25.012 115.833 25 115 C24.01 114.505 24.01 114.505 23 114 C21.961 109.608 21.883 105.349 21.902 100.852 C21.904 100.194 21.905 99.537 21.907 98.859 C21.912 96.781 21.925 94.703 21.938 92.625 C21.943 91.207 21.947 89.789 21.951 88.371 C21.962 84.914 21.979 81.457 22 78 C26.455 77.505 26.455 77.505 31 77 C31 76.67 31 76.34 31 76 C40.57 76 50.14 76 60 76 C59.67 79.63 59.34 83.26 59 87 C59.66 88.65 60.32 90.3 61 92 C60.75 94.25 60.75 94.25 60 96 C59.01 96.495 59.01 96.495 58 97 C58.108 97.99 58.108 97.99 58.219 99 C59.311 110.649 59.093 122.311 59 134 C58.34 134 57.68 134 57 134 C56.918 134.99 56.835 135.98 56.75 137 C55.562 144.978 50.934 151.691 45 157 C35.736 163.335 26.64 165.391 15.562 165.312 C14.729 165.307 13.896 165.301 13.037 165.295 C1.828 165.074 -7.554 161.731 -16.043 154.184 C-23.228 146.601 -26.141 137.318 -26.098 127.047 C-26.096 126.387 -26.095 125.726 -26.093 125.046 C-26.088 122.947 -26.075 120.849 -26.062 118.75 C-26.057 117.323 -26.053 115.896 -26.049 114.469 C-26.038 110.979 -26.021 107.49 -26 104 C-26.66 103.67 -27.32 103.34 -28 103 C-27.505 102.505 -27.505 102.505 -27 102 C-27.33 101.34 -27.66 100.68 -28 100 C-27.66 98.055 -27.66 98.055 -27.062 95.875 C-26.771 94.788 -26.771 94.788 -26.473 93.68 C-26.317 93.125 -26.161 92.571 -26 92 C-26.66 91.67 -27.32 91.34 -28 91 C-27.836 90.174 -27.673 89.347 -27.504 88.496 C-26.951 84.658 -26.892 80.931 -26.938 77.066 C-26.941 76.311 -26.945 75.556 -26.949 74.779 C-26.965 71.58 -26.993 68.381 -27.021 65.183 C-27.235 37.939 -27.235 37.939 -24 25 C-24 24.01 -24 23.02 -24 22 C-22.691 20.643 -21.351 19.315 -20 18 C-19.259 16.883 -18.531 15.757 -17.812 14.625 C-14.111 9.285 -8.506 6.462 -3 3.25 C-0.86 2.154 -0.86 2.154 0 0 Z M24 111 C25.485 111.99 25.485 111.99 27 113 C27 112.34 27 111.68 27 111 C26.01 111 25.02 111 24 111 Z " fill="#D76541" transform="translate(753,76)"/>
<path d="M0 0 C10.135 10.576 10.963 24.09 11.465 37.902 C11.521 39.273 11.521 39.273 11.578 40.672 C11.608 41.503 11.639 42.334 11.67 43.19 C11.739 45.313 11.739 45.313 12.859 47.25 C8.885 48.217 5.326 48.297 1.246 48.129 C-0.001 48.081 -1.248 48.032 -2.533 47.982 C-3.827 47.926 -5.12 47.87 -6.453 47.812 C-7.766 47.76 -9.079 47.708 -10.432 47.654 C-13.668 47.525 -16.904 47.39 -20.141 47.25 C-20.239 46.081 -20.337 44.912 -20.438 43.707 C-20.588 42.159 -20.739 40.611 -20.891 39.062 C-20.952 38.294 -21.014 37.525 -21.078 36.732 C-21.585 31.809 -22.388 28.783 -26.141 25.25 C-30.001 24.127 -32.179 23.887 -35.953 25.312 C-39.751 28.676 -40.033 32.158 -40.516 37.062 C-40.332 42.71 -38.356 47.831 -34.527 52.012 C-32.793 53.538 -31.02 54.908 -29.141 56.25 C-28.089 57.034 -27.037 57.817 -25.953 58.625 C-23.105 60.584 -23.105 60.584 -19.141 60.25 C-18.811 61.24 -18.481 62.23 -18.141 63.25 C-17.151 63.25 -16.161 63.25 -15.141 63.25 C-13.452 64.894 -11.788 66.564 -10.141 68.25 C-9.212 68.807 -8.284 69.364 -7.328 69.938 C-6.606 70.371 -5.884 70.804 -5.141 71.25 C-5.141 71.91 -5.141 72.57 -5.141 73.25 C-4.481 73.25 -3.821 73.25 -3.141 73.25 C-3.141 73.91 -3.141 74.57 -3.141 75.25 C-2.554 75.485 -1.968 75.719 -1.363 75.961 C5.806 80.119 9.215 89.608 11.419 97.171 C12.306 101.358 12.044 105.675 12.047 109.938 C12.059 110.893 12.071 111.849 12.084 112.834 C12.108 124.348 9.805 134.478 2.004 143.363 C-7.022 151.776 -18.377 154.644 -30.453 154.562 C-31.286 154.559 -32.119 154.556 -32.978 154.552 C-40.942 154.412 -47.752 152.808 -55.016 149.562 C-58.046 148.29 -59.906 147.956 -63.141 148.25 C-63.058 147.528 -62.976 146.806 -62.891 146.062 C-63.218 142.381 -64.809 141.061 -67.141 138.25 C-68.391 134.938 -68.391 134.938 -69.141 132.25 C-70.131 132.25 -71.121 132.25 -72.141 132.25 C-71.866 131.478 -71.866 131.478 -71.586 130.691 C-70.978 127.359 -71.503 124.529 -72.016 121.188 C-72.199 119.959 -72.382 118.731 -72.57 117.465 C-73.061 114.247 -73.061 114.247 -74.141 111.25 C-73.481 108.61 -72.821 105.97 -72.141 103.25 C-62.901 103.25 -53.661 103.25 -44.141 103.25 C-42.532 106.468 -42.745 109.968 -42.52 113.523 C-42.143 116.231 -41.587 117.949 -40.141 120.25 C-36.761 122.574 -33.925 122.308 -30.016 121.75 C-26.34 120.891 -25.429 120.615 -23.016 117.562 C-21.408 111.477 -21.602 106.775 -24.602 101.25 C-27.627 97.318 -31.752 94.501 -36.141 92.25 C-37.461 92.25 -38.781 92.25 -40.141 92.25 C-40.471 91.26 -40.801 90.27 -41.141 89.25 C-42.726 87.864 -42.726 87.864 -44.711 86.613 C-45.439 86.123 -46.168 85.632 -46.919 85.127 C-47.693 84.611 -48.468 84.094 -49.266 83.562 C-61.168 75.353 -61.168 75.353 -65.141 69.25 C-65.678 68.464 -66.216 67.677 -66.77 66.867 C-73.652 55.225 -75.856 38.696 -72.781 25.59 C-72.57 24.818 -72.358 24.046 -72.141 23.25 C-71.987 22.415 -71.834 21.579 -71.676 20.719 C-69.603 11.158 -64.065 2.882 -56.141 -2.75 C-39.275 -13.197 -15.629 -12.777 0 0 Z " fill="#CE6447" transform="translate(384.140625,275.75)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C1.594 1.977 2.189 1.954 2.801 1.93 C14.921 1.666 26.725 3.667 36.172 11.891 C45.549 22.069 47.339 32.831 47.25 46.25 C47.262 48.025 47.262 48.025 47.273 49.836 C47.271 50.968 47.268 52.1 47.266 53.266 C47.263 54.295 47.261 55.324 47.259 56.384 C47 59 47 59 45 62 C42.388 62.282 40.256 62.057 37.695 61.488 C34.799 60.964 32.128 60.902 29.188 60.938 C28.212 60.947 27.236 60.956 26.23 60.965 C25.494 60.976 24.758 60.988 24 61 C24.495 60.01 24.495 60.01 25 59 C24.01 59.33 23.02 59.66 22 60 C22.33 60.66 22.66 61.32 23 62 C21.68 62 20.36 62 19 62 C18.505 60.02 18.505 60.02 18 58 C16.68 58 15.36 58 14 58 C14.012 57.371 14.023 56.742 14.035 56.094 C14.093 50.623 13.884 45.404 13 40 C11.278 38.278 8.912 38.63 6.562 38.438 C5.574 38.354 4.585 38.27 3.566 38.184 C2.047 38.075 0.523 38 -1 38 C-4.582 43.585 -4.356 48.483 -4.188 54.875 C-4.174 55.85 -4.16 56.824 -4.146 57.828 C-4.111 60.219 -4.062 62.609 -4 65 C-3.67 65 -3.34 65 -3 65 C-3 66.65 -3 68.3 -3 70 C-3.66 70 -4.32 70 -5 70 C-4.892 77.459 -4.78 84.917 -4.661 92.376 C-4.606 95.84 -4.552 99.303 -4.503 102.767 C-4.446 106.754 -4.381 110.74 -4.316 114.727 C-4.3 115.964 -4.283 117.202 -4.266 118.477 C-4.236 120.218 -4.236 120.218 -4.205 121.995 C-4.182 123.52 -4.182 123.52 -4.158 125.077 C-3.994 128.112 -3.554 131.013 -3 134 C0.684 135.179 4.155 135.214 8 135 C10.053 133.158 10.053 133.158 11 131 C11.66 131.33 12.32 131.66 13 132 C13.33 126.72 13.66 121.44 14 116 C15.32 116 16.64 116 18 116 C18.33 115.34 18.66 114.68 19 114 C20.481 114.126 20.481 114.126 21.992 114.254 C23.274 114.356 24.555 114.458 25.875 114.562 C27.151 114.667 28.427 114.771 29.742 114.879 C32.886 115.257 32.886 115.257 35 114 C35.33 113.67 35.66 113.34 36 113 C37.98 113.99 37.98 113.99 40 115 C40.33 114.34 40.66 113.68 41 113 C41.66 113.66 42.32 114.32 43 115 C45.571 115.648 45.571 115.648 48 116 C47.531 144.763 47.531 144.763 39 155 C38.34 155 37.68 155 37 155 C37 155.66 37 156.32 37 157 C22.075 168.776 5.957 168.007 -12.125 166.512 C-14.573 165.844 -16.03 164.586 -18 163 C-19.456 162.079 -20.914 161.162 -22.375 160.25 C-30.972 154.504 -35.737 147.009 -38.088 136.95 C-39.701 128.458 -39.329 119.726 -39.336 111.117 C-39.342 109.174 -39.349 107.232 -39.356 105.289 C-39.368 101.226 -39.372 97.163 -39.371 93.1 C-39.37 87.936 -39.398 82.772 -39.432 77.608 C-39.455 73.597 -39.458 69.585 -39.457 65.573 C-39.46 63.671 -39.468 61.77 -39.484 59.868 C-39.615 42.254 -38.198 25.925 -25.656 12.406 C-19.17 6.53 -11.459 3.574 -2.863 2.422 C-2.248 2.283 -1.634 2.143 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z M30 58 C30 58.33 30 58.66 30 59 C32.64 59 35.28 59 38 59 C38 58.67 38 58.34 38 58 C35.36 58 32.72 58 30 58 Z " fill="#D46543" transform="translate(258,74)"/>
<path d="M0 0 C11.55 0 23.1 0 35 0 C36.581 3.161 35.802 6.491 35.562 9.938 C33.941 38.347 35.12 66.936 35.571 95.372 C35.923 117.583 36.152 139.786 36 162 C31.07 162.025 26.141 162.043 21.211 162.055 C19.534 162.06 17.857 162.067 16.18 162.075 C13.77 162.088 11.359 162.093 8.949 162.098 C8.199 162.103 7.449 162.108 6.676 162.113 C3.51 162.114 1.027 162.009 -2 161 C-1.67 160.01 -1.34 159.02 -1 158 C-0.908 156.481 -0.869 154.959 -0.867 153.438 C-0.866 152.558 -0.865 151.679 -0.863 150.773 C-0.867 149.858 -0.871 148.943 -0.875 148 C-0.871 147.085 -0.867 146.17 -0.863 145.227 C-0.865 144.347 -0.866 143.468 -0.867 142.562 C-0.868 141.759 -0.869 140.956 -0.871 140.129 C-0.92 137.811 -0.92 137.811 -2 135 C-1.34 135 -0.68 135 0 135 C-0.531 131.179 -1.497 128.938 -4 126 C-2.02 125.01 -2.02 125.01 0 124 C-0.66 123.67 -1.32 123.34 -2 123 C-1.34 123 -0.68 123 0 123 C-0.165 122.475 -0.33 121.951 -0.5 121.41 C-1.131 118.37 -1.056 115.54 -1 112.438 C-0.906 107.632 -0.906 107.632 -2 103 C-1.34 103 -0.68 103 0 103 C-0.99 100.36 -1.98 97.72 -3 95 C-2.01 95 -1.02 95 0 95 C-0.114 92.895 -0.242 90.791 -0.375 88.688 C-0.479 86.93 -0.479 86.93 -0.586 85.137 C-0.786 81.843 -0.786 81.843 -3 79 C-2.01 77.35 -1.02 75.7 0 74 C-0.99 73.67 -1.98 73.34 -3 73 C-2.01 72.01 -2.01 72.01 -1 71 C-0.714 68.639 -0.714 68.639 -0.789 65.87 C-0.792 64.798 -0.796 63.725 -0.799 62.62 C-0.813 61.453 -0.826 60.285 -0.84 59.082 C-0.845 57.877 -0.85 56.673 -0.856 55.432 C-0.873 51.559 -0.905 47.686 -0.938 43.812 C-0.965 39.986 -0.99 36.159 -1.009 32.332 C-1.021 29.95 -1.038 27.568 -1.062 25.186 C-1.112 16.694 -0.569 8.535 0 0 Z " fill="#DC6740" transform="translate(564,78)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 1.67 2.98 1.34 4 1 C4.33 1.33 4.66 1.66 5 2 C5.33 1.34 5.66 0.68 6 0 C14.079 0.777 14.079 0.777 17 4 C21 11.543 21 11.543 21 15 C21.99 15 22.98 15 24 15 C24.103 15.866 24.206 16.732 24.312 17.625 C25.346 22.7 26.634 26.089 31 29 C31.33 29.33 31.66 29.66 32 30 C33.963 26.44 35.92 22.877 37.875 19.312 C38.431 18.305 38.986 17.298 39.559 16.26 C40.094 15.283 40.629 14.307 41.18 13.301 C41.672 12.405 42.164 11.51 42.671 10.587 C43.947 8.102 45.009 5.61 46 3 C46.66 3 47.32 3 48 3 C48.33 2.34 48.66 1.68 49 1 C49.99 1.33 50.98 1.66 52 2 C52.33 1.34 52.66 0.68 53 0 C55.31 0.66 57.62 1.32 60 2 C60.33 1.34 60.66 0.68 61 0 C61.99 0 62.98 0 64 0 C66.429 3.644 66.243 5.336 66.227 9.675 C66.227 10.348 66.228 11.021 66.228 11.715 C66.227 13.939 66.211 16.163 66.195 18.387 C66.192 19.929 66.189 21.472 66.187 23.015 C66.179 27.074 66.159 31.133 66.137 35.192 C66.117 39.334 66.108 43.477 66.098 47.619 C66.076 55.746 66.041 63.873 66 72 C60.39 72 54.78 72 49 72 C47.695 69.39 47.903 67.501 47.938 64.582 C47.945 63.492 47.953 62.402 47.961 61.279 C47.987 58.988 48.013 56.696 48.039 54.404 C48.078 48.818 48.019 43.499 47 38 C46.577 38.901 46.577 38.901 46.146 39.82 C44.862 42.533 43.556 45.235 42.25 47.938 C41.585 49.356 41.585 49.356 40.906 50.803 C40.468 51.702 40.03 52.601 39.578 53.527 C39.18 54.36 38.782 55.193 38.372 56.051 C36.696 58.432 35.748 58.41 33 59 C32.67 59.99 32.34 60.98 32 62 C31.67 61.34 31.34 60.68 31 60 C29.021 59.273 27.02 58.602 25 58 C25 57.34 25 56.68 25 56 C23.515 55.505 23.515 55.505 22 55 C22 53.68 22 52.36 22 51 C21.67 52.65 21.34 54.3 21 56 C18.71 52.565 18.822 51.015 19 47 C17.68 47.66 16.36 48.32 15 49 C15.008 49.992 15.008 49.992 15.016 51.004 C15.037 53.982 15.05 56.96 15.062 59.938 C15.071 60.978 15.079 62.019 15.088 63.092 C15.093 64.576 15.093 64.576 15.098 66.09 C15.106 67.465 15.106 67.465 15.114 68.867 C15 71 15 71 14 72 C8.06 72 2.12 72 -4 72 C-4 48.9 -4 25.8 -4 2 C-2.68 1.34 -1.36 0.68 0 0 Z M16 40 C17 43 17 43 17 43 Z M17 43 C16.67 43.66 16.34 44.32 16 45 C16.66 45 17.32 45 18 45 C17.67 44.34 17.34 43.68 17 43 Z " fill="#8EBADF" transform="translate(687,624)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.66 5 1.32 5 2 C5.822 1.898 6.645 1.796 7.492 1.691 C16.209 1.359 16.209 1.359 19.5 4.375 C22.425 8.013 24.698 12.045 26.944 16.126 C28.355 18.629 29.975 20.962 31.625 23.312 C34 26.757 34 26.757 34 29 C34.66 29.33 35.32 29.66 36 30 C36.474 30.846 36.949 31.691 37.438 32.562 C37.953 33.367 38.469 34.171 39 35 C39.99 35 40.98 35 42 35 C42.33 23.78 42.66 12.56 43 1 C43.66 1 44.32 1 45 1 C45 1.66 45 2.32 45 3 C45.999 2.928 46.998 2.856 48.027 2.781 C49.318 2.688 50.608 2.596 51.938 2.5 C53.225 2.407 54.513 2.314 55.84 2.219 C59 2 59 2 60 2 C60.871 17.605 61.11 33.187 61.125 48.812 C61.127 49.732 61.13 50.651 61.133 51.598 C61.138 54.239 61.137 56.879 61.133 59.52 C61.135 60.316 61.137 61.113 61.139 61.935 C61.124 65.461 60.948 68.607 60 72 C47.17 73.897 47.17 73.897 41 72 C36.558 68.501 34.318 63.115 31.994 58.087 C30.565 55.086 28.854 52.363 27.062 49.562 C26.538 48.698 26.013 47.833 25.473 46.941 C24.067 44.709 24.067 44.709 21 44 C21.33 43.34 21.66 42.68 22 42 C21.108 39.883 21.108 39.883 20 38 C17.603 41.596 17.76 43.301 17.812 47.562 C17.819 48.719 17.825 49.875 17.832 51.066 C17.802 53.934 17.802 53.934 19 56 C18.34 56 17.68 56 17 56 C17.141 58.292 17.288 60.584 17.438 62.875 C17.519 64.151 17.6 65.427 17.684 66.742 C17.792 69.883 17.792 69.883 19 72 C12.4 72 5.8 72 -1 72 C-1.023 63.055 -1.041 54.111 -1.052 45.166 C-1.057 41.013 -1.064 36.86 -1.075 32.706 C-1.086 28.699 -1.092 24.692 -1.095 20.685 C-1.097 19.155 -1.1 17.626 -1.106 16.096 C-1.113 13.955 -1.114 11.815 -1.114 9.675 C-1.116 8.455 -1.118 7.236 -1.12 5.98 C-1 3 -1 3 0 0 Z M17 34 C18 36 18 36 18 36 Z M17 37 C18 39 18 39 18 39 Z M40 37 C40.33 37.99 40.66 38.98 41 40 C41.66 40 42.32 40 43 40 C43 39.01 43 38.02 43 37 C42.01 37 41.02 37 40 37 Z " fill="#8CB8DD" transform="translate(532,624)"/>
<path d="M0 0 C0.789 0.257 1.578 0.513 2.391 0.777 C10.659 3.732 16.622 7.939 20.875 15.75 C26.172 27.485 26.418 40.52 22.07 52.578 C18.376 60.884 12.499 66.71 4.188 70.438 C-6.962 74.321 -18.517 73.674 -29.25 68.875 C-36.558 64.925 -41.526 58.942 -44 51 C-44.801 46.682 -45.443 42.356 -46 38 C-46.66 38 -47.32 38 -48 38 C-47.67 36.68 -47.34 35.36 -47 34 C-46.34 34 -45.68 34 -45 34 C-45.062 32.515 -45.062 32.515 -45.125 31 C-45.143 28.052 -44.712 25.53 -43.938 22.688 C-42.625 19.206 -42.625 19.206 -44 17 C-43.01 17 -42.02 17 -41 17 C-40.773 16.299 -40.546 15.597 -40.312 14.875 C-36.813 7.209 -31.663 3.985 -24 1 C-15.372 -1.683 -8.672 -2.941 0 0 Z M-23.938 22.562 C-26.568 31.074 -26.957 40.515 -24 49 C-21.578 52.91 -19.407 55.531 -15 57 C-9.381 57.368 -5.843 57.192 -1 54 C4.276 48.578 5.29 42.958 5.312 35.562 C5.194 27.998 4.53 23.909 -0.5 18.125 C-4.063 15.097 -7.161 14.588 -11.805 14.656 C-16.992 15.469 -20.834 18.458 -23.938 22.562 Z M-47 35 C-46 37 -46 37 -46 37 Z " fill="#89B4D9" transform="translate(404,626)"/>
<path d="M0 0 C1.052 0.186 2.104 0.371 3.188 0.562 C6.595 0.954 8.731 0.769 12 0 C12.33 0.33 12.66 0.66 13 1 C13.33 1 13.66 1 14 1 C14.726 10.368 15.106 19.597 15 29 C24.9 28.505 24.9 28.505 35 28 C35.037 26.291 35.075 24.581 35.113 22.82 C35.179 20.589 35.245 18.357 35.312 16.125 C35.335 14.997 35.358 13.869 35.381 12.707 C35.416 11.631 35.452 10.554 35.488 9.445 C35.514 8.45 35.541 7.455 35.568 6.43 C35.71 5.628 35.853 4.826 36 4 C39.674 1.55 41.668 1.923 46 2 C46.99 1.67 47.98 1.34 49 1 C49 1.66 49 2.32 49 3 C50.65 2.34 52.3 1.68 54 1 C55.566 24.707 54.983 48.276 54 72 C48.06 72 42.12 72 36 72 C35.67 64.08 35.34 56.16 35 48 C28.13 46.626 22.028 47.493 15 48 C15 55.92 15 63.84 15 72 C8.73 72 2.46 72 -4 72 C-4.023 63.629 -4.041 55.257 -4.052 46.886 C-4.057 42.999 -4.064 39.111 -4.075 35.224 C-4.086 31.469 -4.092 27.715 -4.095 23.961 C-4.097 22.531 -4.1 21.102 -4.106 19.672 C-4.113 17.664 -4.113 15.656 -4.114 13.648 C-4.116 12.507 -4.118 11.365 -4.12 10.189 C-4.002 7.044 -3.589 4.088 -3 1 C-2.01 1.33 -1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z M22 45 C26 46 26 46 26 46 Z " fill="#91BDE2" transform="translate(290,624)"/>
<path d="M0 0 C1.772 0.033 3.543 0.103 5.312 0.188 C6.299 0.222 7.285 0.257 8.301 0.293 C11 1 11 1 12.522 3.122 C14.144 6.28 15.254 9.382 16.34 12.762 C16.767 14.058 17.195 15.354 17.623 16.65 C18.285 18.676 18.943 20.703 19.591 22.734 C20.223 24.706 20.874 26.671 21.527 28.637 C21.906 29.809 22.285 30.982 22.676 32.19 C23.748 35.105 23.748 35.105 26.155 36.398 C26.764 36.596 27.373 36.795 28 37 C27.755 37.763 27.51 38.526 27.258 39.312 C26.877 44.759 28.888 48.988 30.875 54 C31.627 55.936 32.375 57.873 33.117 59.812 C33.453 60.665 33.79 61.517 34.136 62.395 C35 65 35 65 36 71 C29.73 71 23.46 71 17 71 C13.125 61.375 13.125 61.375 12 58 C8.917 57.971 5.833 57.953 2.75 57.938 C1.441 57.925 1.441 57.925 0.105 57.912 C-0.739 57.909 -1.583 57.906 -2.453 57.902 C-3.228 57.897 -4.003 57.892 -4.802 57.886 C-7.33 57.946 -7.33 57.946 -11 59 C-11.33 62.96 -11.66 66.92 -12 71 C-15.459 71.081 -18.916 71.14 -22.375 71.188 C-23.36 71.213 -24.345 71.238 -25.359 71.264 C-26.3 71.273 -27.241 71.283 -28.211 71.293 C-29.08 71.309 -29.95 71.324 -30.845 71.341 C-31.556 71.228 -32.267 71.116 -33 71 C-33.66 70.01 -34.32 69.02 -35 68 C-34.01 67.01 -34.01 67.01 -33 66 C-31.363 63.544 -30.465 61.816 -29.507 59.105 C-29.23 58.326 -28.952 57.547 -28.666 56.744 C-28.222 55.478 -28.222 55.478 -27.77 54.188 C-27.456 53.305 -27.143 52.422 -26.82 51.512 C-25.813 48.676 -24.813 45.838 -23.812 43 C-22.508 39.312 -21.202 35.625 -19.895 31.938 C-19.414 30.579 -19.414 30.579 -18.923 29.194 C-15.695 20.099 -12.36 11.046 -9 2 C-4.545 1.505 -4.545 1.505 0 1 C0 0.67 0 0.34 0 0 Z M1 22 C2 25 2 25 2 25 Z M0 25 C1 27 1 27 1 27 Z M2 26 C2 26.66 2 27.32 2 28 C1.01 28 0.02 28 -1 28 C-2.485 33.94 -2.485 33.94 -4 40 C-1.36 40 1.28 40 4 40 C4.33 38.68 4.66 37.36 5 36 C5.33 36.66 5.66 37.32 6 38 C5.67 32.985 5.67 32.985 3 29 C2.67 28.01 2.34 27.02 2 26 Z M25 38 C26 40 26 40 26 40 Z " fill="#87B2D7" transform="translate(636,625)"/>
<path d="M0 0 C0.778 -0 1.557 -0.001 2.359 -0.001 C22.484 -0.01 42.608 -0.01 62.733 -0.006 C74.663 -0.004 86.592 -0.003 98.521 -0.004 C99.391 -0.004 100.261 -0.005 101.157 -0.005 C104.692 -0.005 108.227 -0.005 111.762 -0.006 C144.324 -0.009 176.886 -0.006 209.448 -0.001 C238.141 0.004 266.833 0.004 295.526 -0.001 C328.22 -0.006 360.915 -0.008 393.609 -0.005 C397.101 -0.005 400.593 -0.005 404.085 -0.004 C404.945 -0.004 405.804 -0.004 406.689 -0.004 C419.483 -0.003 432.278 -0.004 445.073 -0.007 C459.581 -0.01 474.089 -0.009 488.597 -0.004 C495.954 -0.001 503.312 0 510.669 -0.003 C546.628 -0.019 582.561 0.316 618.51 1.129 C618.51 2.119 618.51 3.109 618.51 4.129 C193.8 4.624 193.8 4.624 -239.49 5.129 C-239.49 3.809 -239.49 2.489 -239.49 1.129 C-159.66 0.554 -79.832 0.036 0 0 Z " fill="#628CB6" transform="translate(318.489501953125,970.87060546875)"/>
<path d="M0 0 C425.205 0.495 425.205 0.495 859 1 C858.67 1.99 858.34 2.98 858 4 C574.86 4 291.72 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#78A4CB" transform="translate(79,764)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C5.97 2.33 8.94 2.66 12 3 C12.33 2.34 12.66 1.68 13 1 C13 1.66 13 2.32 13 3 C13.516 2.649 14.031 2.299 14.562 1.938 C17.301 0.884 18.985 1.119 21.824 1.66 C24.488 2.076 27.119 2.136 29.812 2.25 C37.203 2.843 43.164 5.235 48.5 10.438 C53.027 18.171 53.559 27.923 51.348 36.543 C48.564 42.72 43.481 47.145 37.312 49.812 C32.051 51.577 26.505 51.734 21 52 C20.67 53.32 20.34 54.64 20 56 C19.34 56 18.68 56 18 56 C17.67 61.28 17.34 66.56 17 72 C10.4 72 3.8 72 -3 72 C-2.67 71.01 -2.34 70.02 -2 69 C-1.906 67.213 -1.869 65.422 -1.867 63.633 C-1.866 62.582 -1.865 61.532 -1.863 60.449 C-1.867 59.352 -1.871 58.255 -1.875 57.125 C-1.871 56.036 -1.867 54.946 -1.863 53.824 C-1.865 52.771 -1.866 51.718 -1.867 50.633 C-1.868 49.673 -1.869 48.712 -1.871 47.723 C-1.998 45.052 -2.404 42.603 -3 40 C-2.34 40 -1.68 40 -1 40 C-1.041 38.966 -1.082 37.932 -1.124 36.866 C-1.272 33.018 -1.407 29.17 -1.537 25.321 C-1.595 23.657 -1.658 21.994 -1.724 20.331 C-1.818 17.936 -1.899 15.54 -1.977 13.145 C-2.009 12.405 -2.041 11.665 -2.074 10.903 C-2.193 6.781 -1.862 3.702 0 0 Z M17 18 C17.33 18.66 17.66 19.32 18 20 C17.67 20 17.34 20 17 20 C17 24.95 17 29.9 17 35 C17.66 34.67 18.32 34.34 19 34 C19.33 34.99 19.66 35.98 20 37 C23.996 36.752 27.762 36.495 31.5 35 C33.986 31.686 34.193 28.068 34 24 C32.936 20.999 32.936 20.999 31 19 C27.851 17.95 25.612 17.899 22.312 17.938 C21.319 17.947 20.325 17.956 19.301 17.965 C18.542 17.976 17.782 17.988 17 18 Z M-2 41 C-1 44 -1 44 -1 44 Z " fill="#8DB8DE" transform="translate(443,624)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.99 3 1.98 3 3 C3.33 2.34 3.66 1.68 4 1 C6.322 0.593 8.657 0.256 11 0 C11 0.99 11 1.98 11 3 C11.66 2.01 12.32 1.02 13 0 C18.445 0.495 18.445 0.495 24 1 C24 1.66 24 2.32 24 3 C24.516 2.67 25.031 2.34 25.562 2 C28.844 0.654 31.484 0.744 35 1 C35.99 1.66 36.98 2.32 38 3 C38.328 5.969 38.328 5.969 38.25 9.5 C38.235 10.665 38.219 11.831 38.203 13.031 C38 16 38 16 37 18 C28.42 18.33 19.84 18.66 11 19 C11.824 24.302 11.824 24.302 15 28 C18.033 28.615 21.037 28.651 24.125 28.688 C32.911 28.911 32.911 28.911 34 30 C34.073 32.353 34.084 34.708 34.062 37.062 C34.053 38.353 34.044 39.643 34.035 40.973 C34.024 41.972 34.012 42.971 34 44 C24.118 46 24.118 46 22 46 C20.515 46.495 20.515 46.495 19 47 C19 46.34 19 45.68 19 45 C17.515 44.505 17.515 44.505 16 44 C16 45.32 16 46.64 16 48 C14.35 47.67 12.7 47.34 11 47 C10.67 46.01 10.34 45.02 10 44 C10 47.96 10 51.92 10 56 C16.93 56 23.86 56 31 56 C31 56.33 31 56.66 31 57 C33.31 57 35.62 57 38 57 C38 58.32 38 59.64 38 61 C38.99 61.495 38.99 61.495 40 62 C39.34 62 38.68 62 38 62 C38.144 63.279 38.289 64.558 38.438 65.875 C38.519 66.594 38.6 67.314 38.684 68.055 C38.913 70.074 38.913 70.074 40 72 C24.49 72 8.98 72 -7 72 C-7.739 67.564 -8.122 63.954 -8.114 59.536 C-8.113 58.275 -8.113 57.014 -8.113 55.715 C-8.108 54.354 -8.103 52.993 -8.098 51.633 C-8.096 50.229 -8.094 48.825 -8.093 47.421 C-8.09 43.742 -8.08 40.063 -8.069 36.384 C-8.058 32.623 -8.054 28.862 -8.049 25.102 C-8.038 17.734 -8.021 10.367 -8 3 C-5.36 3 -2.72 3 0 3 C0 2.01 0 1.02 0 0 Z M13 44 C13 44.66 13 45.32 13 46 C13.66 45.67 14.32 45.34 15 45 C14.34 44.67 13.68 44.34 13 44 Z " fill="#8EBBE0" transform="translate(776,624)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C4.276 2.47 4.276 2.47 8.062 2.625 C9.998 2.737 9.998 2.737 11.973 2.852 C13.471 2.925 13.471 2.925 15 3 C15.365 5.061 15.717 7.124 16.062 9.188 C16.358 10.91 16.358 10.91 16.66 12.668 C16.978 15.786 16.808 17.998 16 21 C14.583 20.688 13.167 20.376 11.75 20.062 C10.961 19.888 10.172 19.714 9.359 19.535 C7.926 19.21 6.498 18.854 5.086 18.445 C2.326 17.856 -0.305 17.83 -3.125 17.812 C-4.707 17.791 -4.707 17.791 -6.32 17.77 C-9.153 17.723 -9.153 17.723 -11 20 C-11.119 22.529 -11.119 22.529 -10 25 C-7.498 26.634 -7.498 26.634 -4.375 27.688 C-3.295 28.097 -2.215 28.507 -1.102 28.93 C2.249 30.086 5.562 31.141 9 32 C9 32.66 9 33.32 9 34 C9.557 34.206 10.114 34.413 10.688 34.625 C15.475 37.472 18.285 41.802 20 47 C20.697 53.216 20.647 58.718 17 64 C10.327 71.358 3.943 74.173 -6 75 C-14.561 74.988 -22.668 73.582 -30 69 C-30 64.05 -30 59.1 -30 54 C-28.68 54.33 -27.36 54.66 -26 55 C-24.628 55.186 -23.252 55.353 -21.875 55.5 C-18 56 -18 56 -16 57 C-16 57.33 -16 57.66 -16 58 C-9.325 59.115 -5.009 59.266 1 56 C1.232 52.627 1.232 52.627 0 49 C-2.795 47.009 -5.899 45.993 -9.125 44.875 C-23.731 39.537 -23.731 39.537 -27.559 33.57 C-30.161 26.33 -31.222 20.274 -28.152 12.988 C-25.293 8.054 -21.404 4.87 -16.055 2.934 C-10.772 1.563 -5.421 0.616 0 0 Z " fill="#88B3D7" transform="translate(254,624)"/>
<path d="M0 0 C0.206 0.928 0.413 1.856 0.625 2.812 C2.166 8.25 4.976 13.251 8 18 C8.66 18.33 9.32 18.66 10 19 C10.33 15.37 10.66 11.74 11 8 C10.34 7.67 9.68 7.34 9 7 C9 6.34 9 5.68 9 5 C9.66 5 10.32 5 11 5 C11 3.35 11 1.7 11 0 C12.258 -0.041 13.516 -0.082 14.812 -0.125 C15.52 -0.148 16.228 -0.171 16.957 -0.195 C19 0 19 0 22 2 C21.34 5.63 20.68 9.26 20 13 C20.66 13 21.32 13 22 13 C22.495 13.99 22.495 13.99 23 15 C23.66 15 24.32 15 25 15 C25.093 13.948 25.093 13.948 25.188 12.875 C26.252 9.109 28.128 6.576 31 4 C35.799 1.334 39.537 0.415 45 1 C51.233 3.007 54.053 5.593 57.25 11.188 C59.285 18.82 57.731 26.584 56.34 34.215 C55.788 37.08 55.788 37.08 57 40 C55.36 39.969 55.36 39.969 53.688 39.938 C50.056 39.815 50.056 39.815 47 41 C40.562 41.728 35.124 41.609 29.727 37.777 C26.473 34.428 24.73 30.285 23 26 C22.34 26 21.68 26 21 26 C21 29.96 21 33.92 21 38 C22.32 38.66 23.64 39.32 25 40 C19.986 41.023 14.848 42.015 10 40 C5.759 35.736 3.3 30.006 0.907 24.561 C0.608 24.046 0.308 23.531 0 23 C-0.66 23 -1.32 23 -2 23 C-2.054 24.896 -2.093 26.791 -2.125 28.688 C-2.148 29.743 -2.171 30.799 -2.195 31.887 C-2.137 35.413 -2.137 35.413 0 40 C-4.29 40 -8.58 40 -13 40 C-12.67 38.35 -12.34 36.7 -12 35 C-12.289 32.769 -12.289 32.769 -12.938 30.688 C-13.463 28.862 -13.463 28.862 -14 27 C-13.34 27 -12.68 27 -12 27 C-12 23.37 -12 19.74 -12 16 C-12.33 16 -12.66 16 -13 16 C-13 14.35 -13 12.7 -13 11 C-12.67 11 -12.34 11 -12 11 C-12.031 9.36 -12.031 9.36 -12.062 7.688 C-12 4 -12 4 -11 1 C-7.51 -0.477 -3.726 -0.072 0 0 Z M-6 1 C-6 1.33 -6 1.66 -6 2 C-4.35 2 -2.7 2 -1 2 C-1 1.67 -1 1.34 -1 1 C-2.65 1 -4.3 1 -6 1 Z M13 1 C17 2 17 2 17 2 Z M35.781 12.09 C33.676 15.529 33.463 17.557 33.5 21.562 C33.49 22.678 33.479 23.793 33.469 24.941 C34.103 28.596 35.048 29.813 38 32 C38.99 32 39.98 32 41 32 C41 31.01 41 30.02 41 29 C41.66 28.67 42.32 28.34 43 28 C42.794 27.361 42.587 26.721 42.375 26.062 C41.852 21.787 42.912 18.127 44 14 C43.01 14 42.02 14 41 14 C41.33 12.68 41.66 11.36 42 10 C37.977 9.543 37.977 9.543 35.781 12.09 Z M46 12 C47 14 47 14 47 14 Z M20 14 C20 14.99 20 15.98 20 17 C20.66 16.67 21.32 16.34 22 16 C21.34 15.34 20.68 14.68 20 14 Z M47 14 C47 17 47 17 47 17 Z M49 15 C49 15.66 49 16.32 49 17 C49.99 17 50.98 17 52 17 C52 16.34 52 15.68 52 15 C51.01 15 50.02 15 49 15 Z M-3 17 C-2 19 -2 19 -2 19 Z M23 17 C23 18.98 23 20.96 23 23 C23.33 23 23.66 23 24 23 C24 21.02 24 19.04 24 17 C23.67 17 23.34 17 23 17 Z M44 18 C44 18.33 44 18.66 44 19 C47.63 19 51.26 19 55 19 C55 18.67 55 18.34 55 18 C51.37 18 47.74 18 44 18 Z " fill="#89B5D9" transform="translate(487,816)"/>
<path d="M0 0 C1.423 0.021 2.846 0.041 4.312 0.062 C5.962 5.013 7.612 9.962 9.312 15.062 C10.303 15.062 11.293 15.062 12.312 15.062 C12.985 13.294 13.65 11.523 14.312 9.75 C14.869 8.271 14.869 8.271 15.438 6.762 C16.467 4.06 16.467 4.06 16.312 1.062 C19.942 0.732 23.572 0.403 27.312 0.062 C27.312 1.053 27.312 2.043 27.312 3.062 C26.653 3.393 25.992 3.722 25.312 4.062 C28.44 3.589 30.79 2.841 33.688 1.5 C37.726 -0.101 40.014 -0.541 44.312 0.062 C49.297 2.301 53.087 4.916 55.312 10.062 C55.312 10.722 55.312 11.383 55.312 12.062 C56.962 12.062 58.612 12.062 60.312 12.062 C59.825 12.604 59.338 13.145 58.836 13.703 C56.962 16.605 56.982 18.385 56.938 21.812 C56.583 27.74 55.18 31.562 51.312 36.062 C48.533 38.524 46.475 40.054 42.703 40.082 C36.401 39.667 32.09 39.337 27.312 35.062 C26.653 34.072 25.992 33.082 25.312 32.062 C24.982 34.372 24.653 36.683 24.312 39.062 C21 39.75 21 39.75 17.312 40.062 C15.375 38.625 15.375 38.625 14.312 37.062 C13.653 38.053 12.992 39.043 12.312 40.062 C7.562 39.188 7.562 39.188 5.312 38.062 C4.68 35.559 4.68 35.559 4.188 32.5 C4.02 31.488 3.852 30.476 3.68 29.434 C3.559 28.651 3.437 27.869 3.312 27.062 C2.322 27.062 1.332 27.062 0.312 27.062 C-0.018 26.403 -0.347 25.742 -0.688 25.062 C-0.809 25.989 -0.93 26.916 -1.055 27.871 C-1.306 29.667 -1.306 29.667 -1.562 31.5 C-1.725 32.695 -1.887 33.89 -2.055 35.121 C-2.264 36.092 -2.472 37.062 -2.688 38.062 C-5.701 39.569 -8.37 39.24 -11.688 39.062 C-13.688 37.062 -13.688 37.062 -13.812 34.438 C-13.751 33.262 -13.751 33.262 -13.688 32.062 C-12.697 32.062 -11.707 32.062 -10.688 32.062 C-10.688 26.122 -10.688 20.183 -10.688 14.062 C-10.028 13.732 -9.367 13.403 -8.688 13.062 C-8.219 10.247 -8.219 10.247 -8.062 7 C-7.95 5.343 -7.95 5.343 -7.836 3.652 C-7.787 2.798 -7.738 1.943 -7.688 1.062 C-4.805 0.102 -2.989 -0.043 0 0 Z M26.312 6.062 C25.817 8.043 25.817 8.043 25.312 10.062 C25.972 9.732 26.633 9.403 27.312 9.062 C26.982 8.072 26.653 7.082 26.312 6.062 Z M34.312 12.062 C32.606 17.183 32.658 22.005 34.625 27 C36.014 30.34 36.014 30.34 39.312 31.062 C42.454 30.404 42.454 30.404 45.312 29.062 C46.324 26.027 46.478 23.865 46.5 20.688 C46.514 19.752 46.528 18.816 46.543 17.852 C46.277 14.637 45.672 11.422 43.312 9.062 C39.263 8.625 37.252 9.228 34.312 12.062 Z M-9.688 17.062 C-8.688 20.062 -8.688 20.062 -8.688 20.062 Z M46.312 17.062 C46.312 19.372 46.312 21.683 46.312 24.062 C46.643 24.062 46.972 24.062 47.312 24.062 C47.312 21.753 47.312 19.442 47.312 17.062 C46.982 17.062 46.653 17.062 46.312 17.062 Z M-0.688 18.062 C0.312 20.062 0.312 20.062 0.312 20.062 Z M-0.688 21.062 C-0.028 21.722 0.633 22.383 1.312 23.062 C1.312 22.403 1.312 21.742 1.312 21.062 C0.653 21.062 -0.008 21.062 -0.688 21.062 Z M1.312 23.062 C2.312 26.062 2.312 26.062 2.312 26.062 Z M16.312 26.062 C17.312 29.062 17.312 29.062 17.312 29.062 Z M15.312 32.062 C14.817 34.043 14.817 34.043 14.312 36.062 C14.643 35.403 14.972 34.742 15.312 34.062 C15.972 34.062 16.633 34.062 17.312 34.062 C17.312 33.403 17.312 32.742 17.312 32.062 C16.653 32.062 15.992 32.062 15.312 32.062 Z " fill="#8BB6DC" transform="translate(586.6875,871.9375)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.078 3.52 2.963 6.671 3.375 10.344 C5.008 19.895 11.237 29.25 19 35 C23.819 37.796 28.71 40.237 34 42 C33.67 43.32 33.34 44.64 33 46 C32.385 46.11 31.77 46.219 31.137 46.332 C30.328 46.491 29.52 46.649 28.688 46.812 C27.887 46.963 27.086 47.114 26.262 47.27 C23.736 48.085 22.745 49.051 21 51 C19.667 51.667 18.333 52.333 17 53 C16.464 53.536 15.928 54.072 15.375 54.625 C14.921 55.079 14.467 55.533 14 56 C13.34 56 12.68 56 12 56 C11.729 56.543 11.459 57.085 11.18 57.645 C10.335 59.331 9.485 61.016 8.633 62.699 C5.588 68.733 2.666 74.789 0 81 C-0.99 80.67 -1.98 80.34 -3 80 C-4.141 77.41 -4.141 77.41 -5.25 74.062 C-7.627 67.541 -10.669 62.396 -15 57 C-15.722 56.092 -16.444 55.185 -17.188 54.25 C-23.279 49.377 -30.6 46.327 -38 44 C-35.442 41.775 -32.84 40.438 -29.75 39.125 C-17.009 33.581 -10.181 26.001 -5 13 C-4.07 9.351 -3.427 5.739 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BDCAD4" transform="translate(871,298)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.33 7 0.66 7 1 C12.94 1.33 18.88 1.66 25 2 C24.67 4.97 24.34 7.94 24 11 C19.05 10.67 14.1 10.34 9 10 C9.33 12.31 9.66 14.62 10 17 C14.646 16.515 14.646 16.515 19 15 C21.797 17.068 22.027 18.144 22.688 21.688 C22.791 22.781 22.894 23.874 23 25 C21 26 21 26 17.062 25.875 C13.849 25.802 13.849 25.802 11.062 27.312 C9.824 29.131 9.824 29.131 9 32 C14.28 32 19.56 32 25 32 C26.245 34.491 25.777 35.411 25 38 C25.66 38 26.32 38 27 38 C26.959 37.092 26.918 36.185 26.875 35.25 C26.995 32.122 27.391 30.614 29 28 C28.01 27.67 27.02 27.34 26 27 C26.99 27 27.98 27 29 27 C28.928 26.264 28.856 25.528 28.781 24.77 C28.688 23.794 28.596 22.818 28.5 21.812 C28.407 20.85 28.314 19.887 28.219 18.895 C27.918 14.913 27.729 11.813 29 8 C29.165 6.804 29.33 5.607 29.5 4.375 C30 1 30 1 31 0 C33.671 -0.141 36.324 -0.042 39 0 C39 0.66 39 1.32 39 2 C39.66 2 40.32 2 41 2 C42.148 4.27 43.294 6.541 44.438 8.812 C44.763 9.455 45.088 10.098 45.424 10.76 C46.792 13.483 48.035 16.104 49 19 C49.66 18.67 50.32 18.34 51 18 C50.505 16.515 50.505 16.515 50 15 C49.34 14.67 48.68 14.34 48 14 C49.32 12.02 50.64 10.04 52 8 C51.34 8 50.68 8 50 8 C50 6.02 50 4.04 50 2 C50.66 2 51.32 2 52 2 C52 1.34 52 0.68 52 0 C60.571 -0.286 60.571 -0.286 64 2 C63.67 2.99 63.34 3.98 63 5 C62.34 5 61.68 5 61 5 C61.084 5.615 61.168 6.23 61.254 6.863 C61.407 8.076 61.407 8.076 61.562 9.312 C61.719 10.513 61.719 10.513 61.879 11.738 C62 14 62 14 61 17 C61.99 17.33 62.98 17.66 64 18 C63.01 19.65 62.02 21.3 61 23 C61.66 23.33 62.32 23.66 63 24 C62.722 24.866 62.722 24.866 62.438 25.75 C61.81 28.978 62.552 31.105 64 34 C63.01 34 62.02 34 61 34 C61.33 34.66 61.66 35.32 62 36 C64.025 36.652 64.025 36.652 66 37 C66 37.99 66 38.98 66 40 C61.05 40 56.1 40 51 40 C49.52 37.375 48.041 34.75 46.562 32.125 C46.142 31.379 45.721 30.632 45.287 29.863 C44.884 29.148 44.481 28.432 44.066 27.695 C43.695 27.035 43.323 26.376 42.94 25.696 C42.265 24.479 41.622 23.244 41 22 C38.825 25.263 38.718 26.234 39 30 C39.495 31.485 39.495 31.485 40 33 C40.495 36.465 40.495 36.465 41 40 C27.14 40 13.28 40 -1 40 C-2.283 36.15 -2.066 33.053 -2 29 C-1.67 29 -1.34 29 -1 29 C-0.67 19.43 -0.34 9.86 0 0 Z M39 19 C40 21 40 21 40 21 Z M-1 34 C0 37 0 37 0 37 Z " fill="#7199BB" transform="translate(598,816)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.495 0.495 2.495 0.495 3 1 C3.959 0.722 3.959 0.722 4.938 0.438 C5.618 0.293 6.299 0.149 7 0 C9.709 2.709 9.293 4.244 9.379 8.031 C9.413 9.234 9.447 10.437 9.482 11.676 C9.509 12.938 9.535 14.2 9.562 15.5 C9.622 17.99 9.686 20.479 9.754 22.969 C9.778 24.077 9.802 25.185 9.826 26.326 C9.824 28.972 9.824 28.972 11 31 C13.425 31.835 13.425 31.835 16 32 C16.66 31.34 17.32 30.68 18 30 C18.33 30 18.66 30 19 30 C19.682 21.437 19.682 21.437 20.254 12.867 C20.295 12.191 20.337 11.515 20.379 10.819 C20.496 8.886 20.607 6.953 20.719 5.02 C21 2 21 2 22 0 C24.31 0 26.62 0 29 0 C29 0.66 29 1.32 29 2 C29.99 2.495 29.99 2.495 31 3 C30.67 12.57 30.34 22.14 30 32 C29.34 32 28.68 32 28 32 C27.732 32.763 27.464 33.526 27.188 34.312 C25.424 38.303 25.424 38.303 23 40 C15.706 42.596 11.241 41.602 4.188 38.938 C-0.159 35.088 -1.103 28.564 -2 23 C-4.218 25.218 -4.501 26.352 -5.312 29.312 C-6.903 34.31 -9.274 37.319 -13.793 40.105 C-18.3 41.932 -23.238 41.517 -28 41 C-33.018 38.581 -36.5 35.001 -39 30 C-39.66 30 -40.32 30 -41 30 C-42.125 24.375 -42.125 24.375 -41 21 C-40.34 21 -39.68 21 -39 21 C-39.124 19.866 -39.248 18.731 -39.375 17.562 C-39.365 11.943 -36.726 8.047 -33 4 C-28.237 1.089 -23.526 0.053 -18 1 C-12.781 2.768 -8.854 5.171 -6 10 C-5.138 12.662 -4.56 15.249 -4 18 C-3.67 17.34 -3.34 16.68 -3 16 C-2.34 16 -1.68 16 -1 16 C-2.837 13.033 -2.837 13.033 -5 11 C-4.34 11 -3.68 11 -3 11 C-1.989 7.951 -1.989 7.951 -3 5 C-2.34 5 -1.68 5 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z M-2 9 C-1 11 -1 11 -1 11 Z M-27.312 11.75 C-30.511 16.015 -30.612 20.769 -30 26 C-28.844 28.363 -27.85 30.15 -26 32 C-21.113 32.374 -21.113 32.374 -16.625 30.688 C-13.287 27.222 -13.734 22.975 -13.773 18.449 C-14.063 15.315 -14.984 13.404 -17 11 C-20.77 9.115 -23.953 9.208 -27.312 11.75 Z M-40 23 C-40 24.98 -40 26.96 -40 29 C-39.67 29 -39.34 29 -39 29 C-39 27.02 -39 25.04 -39 23 C-39.33 23 -39.66 23 -40 23 Z " fill="#84AFD4" transform="translate(251,816)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.32 1.67 3.64 1.34 5 1 C5.437 2.143 5.437 2.143 5.883 3.309 C10.881 15.899 16.37 25.107 28.938 30.875 C30.62 31.595 32.306 32.308 34 33 C33.67 33.99 33.34 34.98 33 36 C30.891 37.027 30.891 37.027 28.25 37.938 C23.631 39.662 19.314 41.371 17 46 C16.01 46.33 15.02 46.66 14 47 C12.703 49.199 11.582 51.324 10.5 53.625 C10.205 54.23 9.91 54.834 9.605 55.457 C8.101 58.566 6.731 61.683 5.469 64.898 C4.984 65.922 4.499 66.945 4 68 C3.01 68.33 2.02 68.66 1 69 C0.897 67.969 0.794 66.938 0.688 65.875 C0.007 62.038 -1.178 59.407 -3 56 C-3 55.34 -3 54.68 -3 54 C-3.66 53.67 -4.32 53.34 -5 53 C-5.598 52.093 -6.196 51.185 -6.812 50.25 C-12.501 41.799 -20.354 37.792 -30 35 C-26.591 31.981 -24.397 30.846 -20 30 C-11.439 26.476 -5.5 20.736 -1.688 12.312 C-1.11 10.881 -0.546 9.444 0 8 C0.412 6.989 0.825 5.979 1.25 4.938 C1.498 4.298 1.745 3.659 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B7C5D0" transform="translate(122,98)"/>
<path d="M0 0 C1.052 0.186 2.104 0.371 3.188 0.562 C6.595 0.954 8.731 0.769 12 0 C12.33 0.33 12.66 0.66 13 1 C13.33 1 13.66 1 14 1 C14.726 10.368 15.106 19.597 15 29 C24.9 28.505 24.9 28.505 35 28 C35.037 26.291 35.075 24.581 35.113 22.82 C35.179 20.589 35.245 18.357 35.312 16.125 C35.335 14.997 35.358 13.869 35.381 12.707 C35.416 11.631 35.452 10.554 35.488 9.445 C35.514 8.45 35.541 7.455 35.568 6.43 C35.71 5.628 35.853 4.826 36 4 C39.674 1.55 41.668 1.923 46 2 C46.99 1.67 47.98 1.34 49 1 C49 1.66 49 2.32 49 3 C50.65 2.34 52.3 1.68 54 1 C55.566 24.707 54.983 48.276 54 72 C48.06 72 42.12 72 36 72 C35.67 64.08 35.34 56.16 35 48 C28.13 46.626 22.028 47.493 15 48 C15 55.92 15 63.84 15 72 C8.73 72 2.46 72 -4 72 C-4.023 63.629 -4.041 55.257 -4.052 46.886 C-4.057 42.999 -4.064 39.111 -4.075 35.224 C-4.086 31.469 -4.092 27.715 -4.095 23.961 C-4.097 22.531 -4.1 21.102 -4.106 19.672 C-4.113 17.664 -4.113 15.656 -4.114 13.648 C-4.116 12.507 -4.118 11.365 -4.12 10.189 C-4.002 7.044 -3.589 4.088 -3 1 C-2.01 1.33 -1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z M-2 4 C-2 26.11 -2 48.22 -2 71 C2.95 71 7.9 71 13 71 C13.33 62.09 13.66 53.18 14 44 C21.92 43.67 29.84 43.34 38 43 C38 52.24 38 61.48 38 71 C38.33 71 38.66 71 39 71 C39 49.22 39 27.44 39 5 C43.29 5 47.58 5 52 5 C52 26.78 52 48.56 52 71 C52.33 71 52.66 71 53 71 C53 48.89 53 26.78 53 4 C48.05 4 43.1 4 38 4 C38 12.58 38 21.16 38 30 C30.08 29.67 22.16 29.34 14 29 C13.67 20.75 13.34 12.5 13 4 C8.05 4 3.1 4 -2 4 Z M22 45 C26 46 26 46 26 46 Z " fill="#8BB7DC" transform="translate(290,624)"/>
<path d="M0 0 C2.895 7.859 4.475 15.791 6 24 C7.975 23.652 7.975 23.652 10 23 C10.33 22.34 10.66 21.68 11 21 C10.01 20.67 9.02 20.34 8 20 C9.485 15.545 9.485 15.545 11 11 C10.34 10.67 9.68 10.34 9 10 C9.66 10 10.32 10 11 10 C11 8.68 11 7.36 11 6 C10.34 6 9.68 6 9 6 C9 4 9 4 10 3 C9.505 1.02 9.505 1.02 9 -1 C12.96 -0.67 16.92 -0.34 21 0 C21.015 0.959 21.029 1.918 21.044 2.906 C21.102 6.458 21.179 10.01 21.262 13.562 C21.296 15.1 21.324 16.639 21.346 18.177 C21.38 20.387 21.432 22.595 21.488 24.805 C21.495 25.494 21.501 26.184 21.508 26.895 C21.54 29.047 21.54 29.047 22 32 C22.99 32.66 23.98 33.32 25 34 C25.188 36.625 25.188 36.625 25 39 C24.01 39 23.02 39 22 39 C21.67 38.34 21.34 37.68 21 37 C20.67 37.33 20.34 37.66 20 38 C18.619 38.075 17.234 38.083 15.852 38.062 C15.011 38.055 14.171 38.047 13.305 38.039 C11.526 38.013 9.747 37.987 7.969 37.961 C6.704 37.949 6.704 37.949 5.414 37.938 C4.64 37.926 3.866 37.914 3.069 37.902 C0.801 37.916 0.801 37.916 -2 39 C-4.638 39.122 -7.237 39.185 -9.875 39.188 C-10.583 39.2 -11.29 39.212 -12.02 39.225 C-16.134 39.236 -19.302 38.835 -23 37 C-23.043 35.334 -23.041 33.666 -23 32 C-22.505 31.505 -22.505 31.505 -22 31 C-21.9 29.17 -21.869 27.336 -21.867 25.504 C-21.866 24.388 -21.865 23.273 -21.863 22.123 C-21.867 20.948 -21.871 19.773 -21.875 18.562 C-21.871 17.391 -21.867 16.22 -21.863 15.014 C-21.865 13.897 -21.866 12.78 -21.867 11.629 C-21.868 10.598 -21.869 9.568 -21.871 8.506 C-21.832 6.002 -21.832 6.002 -23 4 C-22.67 3.67 -22.34 3.34 -22 3 C-22 2.01 -22 1.02 -22 0 C-14.468 -0.757 -7.497 -1.144 0 0 Z M-9 12 C-8.34 12.66 -7.68 13.32 -7 14 C-7 13.34 -7 12.68 -7 12 C-7.66 12 -8.32 12 -9 12 Z M-9 14 C-9.495 15.98 -9.495 15.98 -10 18 C-9.34 16.68 -8.68 15.36 -8 14 C-8.33 14 -8.66 14 -9 14 Z M-12 20 C-11 22 -11 22 -11 22 Z M-6 21 C-6 21.66 -6 22.32 -6 23 C-5.34 22.67 -4.68 22.34 -4 22 C-4.66 21.67 -5.32 21.34 -6 21 Z M-5 24 C-5.33 24.66 -5.66 25.32 -6 26 C-5.34 26 -4.68 26 -4 26 C-4.33 26.99 -4.66 27.98 -5 29 C-4.34 29 -3.68 29 -3 29 C-3.495 26.525 -3.495 26.525 -4 24 C-4.33 24 -4.66 24 -5 24 Z M-8 25 C-8.66 26.65 -9.32 28.3 -10 30 C-8.68 29.67 -7.36 29.34 -6 29 C-6.66 27.68 -7.32 26.36 -8 25 Z M10 24 C9.34 24.66 8.68 25.32 8 26 C8 26.66 8 27.32 8 28 C8.66 28 9.32 28 10 28 C10.33 28.66 10.66 29.32 11 30 C11 28.02 11 26.04 11 24 C10.67 24 10.34 24 10 24 Z " fill="#8DB9E0" transform="translate(351,873)"/>
<path d="M0 0 C1.061 0 2.121 0 3.214 0 C4.907 0.008 4.907 0.008 6.633 0.016 C8.385 0.018 8.385 0.018 10.172 0.02 C13.891 0.026 17.61 0.038 21.329 0.051 C23.855 0.056 26.381 0.061 28.907 0.065 C35.089 0.076 41.271 0.092 47.454 0.114 C46.941 3.905 46.305 6.893 44.387 10.207 C43.984 10.928 43.582 11.648 43.167 12.391 C42.315 13.874 41.461 15.356 40.604 16.836 C38.218 21.118 36.886 24.061 37.454 29.114 C37.784 29.114 38.114 29.114 38.454 29.114 C38.454 31.424 38.454 33.734 38.454 36.114 C37.794 36.114 37.134 36.114 36.454 36.114 C35.794 37.434 35.134 38.754 34.454 40.114 C27.829 39.364 27.829 39.364 24.454 37.114 C24.949 36.124 24.949 36.124 25.454 35.114 C25.602 33.159 25.675 31.198 25.704 29.239 C25.742 27.665 25.742 27.665 25.782 26.059 C25.413 22.752 24.6 21.577 22.454 19.114 C21.742 17.466 21.063 15.802 20.454 14.114 C19.794 14.114 19.134 14.114 18.454 14.114 C18.454 13.454 18.454 12.794 18.454 12.114 C15.039 11.333 11.95 11.067 8.454 11.114 C8.461 12.325 8.461 12.325 8.469 13.56 C8.49 17.203 8.503 20.846 8.516 24.489 C8.524 25.76 8.533 27.031 8.542 28.34 C8.546 30.158 8.546 30.158 8.551 32.012 C8.559 33.693 8.559 33.693 8.567 35.408 C8.454 38.114 8.454 38.114 7.454 40.114 C4.484 40.114 1.514 40.114 -1.546 40.114 C-3.678 35.851 -3.678 35.851 -2.546 32.114 C-2.48 30.365 -2.461 28.613 -2.484 26.864 C-2.493 25.971 -2.502 25.079 -2.511 24.16 C-2.523 23.485 -2.534 22.809 -2.546 22.114 C-3.206 21.784 -3.866 21.454 -4.546 21.114 C-4.237 20.392 -3.928 19.67 -3.609 18.926 C-2.621 16.31 -2.018 13.862 -1.546 11.114 C-3.526 12.104 -3.526 12.104 -5.546 13.114 C-6.206 12.454 -6.866 11.794 -7.546 11.114 C-8.864 10.416 -10.196 9.744 -11.546 9.114 C-11.546 6.474 -11.546 3.834 -11.546 1.114 C-7.647 0.389 -3.965 -0.009 0 0 Z M16.454 4.114 C17.454 8.114 17.454 8.114 17.454 8.114 Z M28.454 8.114 C29.454 10.114 29.454 10.114 29.454 10.114 Z M17.454 9.114 C17.124 9.774 16.794 10.434 16.454 11.114 C17.444 10.784 18.434 10.454 19.454 10.114 C18.794 9.784 18.134 9.454 17.454 9.114 Z M29.454 10.114 C30.454 12.114 30.454 12.114 30.454 12.114 Z M30.454 12.114 C31.454 14.114 31.454 14.114 31.454 14.114 Z M36.454 33.114 C37.454 35.114 37.454 35.114 37.454 35.114 Z " fill="#7299BC" transform="translate(655.54638671875,871.886474609375)"/>
<path d="M0 0 C0.98 -0.017 1.959 -0.034 2.969 -0.051 C4.222 0.129 4.222 0.129 5.5 0.312 C6.16 1.303 6.82 2.293 7.5 3.312 C6.84 3.312 6.18 3.312 5.5 3.312 C5.665 3.931 5.83 4.55 6 5.188 C6.955 11.154 7.066 17.442 5.5 23.312 C6.16 23.643 6.82 23.972 7.5 24.312 C7.17 26.622 6.84 28.933 6.5 31.312 C7.16 31.643 7.82 31.972 8.5 32.312 C9.851 28.464 11.177 24.608 12.5 20.75 C12.882 19.664 13.263 18.578 13.656 17.459 C15.553 11.887 17.042 7.213 16.5 1.312 C19.14 0.982 21.78 0.653 24.5 0.312 C24.5 0.972 24.5 1.633 24.5 2.312 C26.15 2.312 27.8 2.312 29.5 2.312 C31.213 7.402 32.922 12.492 34.627 17.583 C35.208 19.316 35.79 21.049 36.373 22.781 C37.21 25.267 38.043 27.754 38.875 30.242 C39.137 31.019 39.4 31.796 39.67 32.597 C41.5 38.085 41.5 38.085 41.5 40.312 C38.2 40.312 34.9 40.312 31.5 40.312 C31.211 39.199 30.923 38.085 30.625 36.938 C29.911 34.946 29.911 34.946 28.5 33.312 C25.221 32.082 21.977 31.632 18.5 31.312 C17.84 34.283 17.18 37.253 16.5 40.312 C8.58 40.312 0.66 40.312 -7.5 40.312 C-7.83 38.332 -8.16 36.352 -8.5 34.312 C-7.51 33.817 -7.51 33.817 -6.5 33.312 C-6.5 25.062 -6.5 16.812 -6.5 8.312 C-6.83 8.312 -7.16 8.312 -7.5 8.312 C-7.625 5.438 -7.625 5.438 -7.5 2.312 C-4.888 -0.299 -3.628 0.014 0 0 Z M3.5 1.312 C3.5 11.872 3.5 22.433 3.5 33.312 C3.83 33.312 4.16 33.312 4.5 33.312 C5.503 26.185 5.456 19.427 5.062 12.25 C5.01 11.194 4.958 10.137 4.904 9.049 C4.776 6.47 4.641 3.891 4.5 1.312 C4.17 1.312 3.84 1.312 3.5 1.312 Z M23.5 13.312 C24.5 15.312 24.5 15.312 24.5 15.312 Z M22.5 16.312 C22.17 18.952 21.84 21.592 21.5 24.312 C23.15 24.312 24.8 24.312 26.5 24.312 C26.17 22.003 25.84 19.692 25.5 17.312 C24.51 16.982 23.52 16.653 22.5 16.312 Z M3.5 34.312 C4.5 36.312 4.5 36.312 4.5 36.312 Z " fill="#89B4D9" transform="translate(431.5,815.6875)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C10.105 3.314 9.699 4.679 9 8 C9.33 8.33 9.66 8.66 10 9 C10.146 12.366 10.072 14.784 9 18 C9.33 18.66 9.66 19.32 10 20 C9.34 20 8.68 20 8 20 C8.344 22.473 8.344 22.473 9 25 C9.66 25.33 10.32 25.66 11 26 C11.125 32.75 11.125 32.75 10 35 C13.349 32.334 14.123 29.219 15.375 25.215 C15.807 23.867 16.24 22.518 16.672 21.17 C17.339 19.049 18.003 16.926 18.666 14.803 C19.31 12.75 19.967 10.701 20.625 8.652 C21.201 6.811 21.201 6.811 21.789 4.933 C23 2 23 2 26 0 C28.332 -0.079 30.668 -0.088 33 0 C34.438 4.146 35.876 8.291 37.312 12.438 C37.717 13.602 38.121 14.767 38.537 15.967 C45.247 35.334 45.247 35.334 46 40 C42.7 40 39.4 40 36 40 C34 36 34 36 34 32 C28.422 31.875 28.422 31.875 23 33 C22.34 35.31 21.68 37.62 21 40 C13.74 40 6.48 40 -1 40 C-1.023 34.625 -1.043 29.25 -1.055 23.875 C-1.06 22.045 -1.067 20.216 -1.075 18.386 C-1.088 15.76 -1.093 13.134 -1.098 10.508 C-1.103 9.687 -1.108 8.866 -1.113 8.02 C-1.114 2.228 -1.114 2.228 0 0 Z M8 11 C9 15 9 15 9 15 Z M28 14 C27.67 16.31 27.34 18.62 27 21 C29.307 18.285 29.307 18.285 28.625 15.812 C28.419 15.214 28.212 14.616 28 14 Z M29 19 C30 21 30 21 30 21 Z M30 21 C29.01 21.495 29.01 21.495 28 22 C28.33 22.66 28.66 23.32 29 24 C29.66 24 30.32 24 31 24 C30.67 23.01 30.34 22.02 30 21 Z M9 36 C9 36.99 9 37.98 9 39 C11.167 38.167 11.167 38.167 12 36 C11.01 36 10.02 36 9 36 Z " fill="#749CBE" transform="translate(550,816)"/>
<path d="M0 0 C-0.495 -0.495 -0.495 -0.495 -1 -1 C-1 -1.66 -1 -2.32 -1 -3 C15.582 1.965 27.645 14.384 36.002 29.112 C41.804 40.092 45.201 51.613 46 64 C46.054 64.746 46.108 65.493 46.164 66.262 C46.914 86.338 39.517 103.766 27 119 C26.459 119.768 25.917 120.537 25.359 121.328 C24.911 121.88 24.462 122.432 24 123 C23.34 123 22.68 123 22 123 C21.67 123.99 21.34 124.98 21 126 C17.54 128.945 13.755 131.445 10 134 C9.199 134.554 8.399 135.109 7.574 135.68 C0.549 140.237 -7.022 142.697 -15 145 C-15.853 145.249 -16.707 145.498 -17.586 145.754 C-36.329 150.328 -56.295 145.939 -73 137 C-76.503 134.854 -79.773 132.538 -83 130 C-84.547 128.855 -84.547 128.855 -86.125 127.688 C-86.744 127.131 -87.362 126.574 -88 126 C-88 125.34 -88 124.68 -88 124 C-88.866 123.629 -88.866 123.629 -89.75 123.25 C-92.329 121.817 -94.061 120.216 -96 118 C-96 117.34 -96 116.68 -96 116 C-96.66 116 -97.32 116 -98 116 C-110.717 99.1 -115.319 78.515 -112.711 57.696 C-109.482 38.791 -99.144 22.939 -84.25 11.125 C-57.581 -7.571 -30.546 -7.788 0 0 Z M-93.582 22.809 C-97.398 27.352 -100.465 31.621 -103 37 C-103.423 37.837 -103.846 38.673 -104.281 39.535 C-112.775 57.148 -113.148 75.775 -106.898 94.258 C-99.255 113.539 -85.14 129.491 -66 138 C-45.749 145.95 -22.904 147.053 -2.633 138.289 C18.061 128.157 31.535 114.164 39.922 92.469 C46.422 71.554 44.115 49.982 34.203 30.645 C28.035 19.208 18.199 7.354 6 2 C5.34 2.33 4.68 2.66 4 3 C-2.6 3 -8.472 0.971 -14.684 -1.062 C-42.752 -8.997 -74.18 1.704 -93.582 22.809 Z " fill="#4D77A7" transform="translate(177,307)"/>
<path d="M0 0 C2.75 -0.312 2.75 -0.312 6 0 C9.135 3.396 11.433 7.203 13.75 11.188 C14.67 12.756 14.67 12.756 15.609 14.355 C17 17 17 17 17 19 C18.32 19 19.64 19 21 19 C20.67 18.34 20.34 17.68 20 17 C19.876 14.487 19.815 12.013 19.812 9.5 C19.8 8.81 19.788 8.121 19.775 7.41 C19.77 5.605 19.878 3.801 20 2 C20.66 1.34 21.32 0.68 22 0 C22.33 0.33 22.66 0.66 23 1 C23 0.67 23 0.34 23 0 C24.98 0 26.96 0 29 0 C29 0.66 29 1.32 29 2 C29.66 2 30.32 2 31 2 C30.34 3.98 29.68 5.96 29 8 C29.66 8 30.32 8 31 8 C31 8.66 31 9.32 31 10 C30.34 10 29.68 10 29 10 C29 14.62 29 19.24 29 24 C29.66 24 30.32 24 31 24 C30.34 26.31 29.68 28.62 29 31 C29.66 31 30.32 31 31 31 C30.125 38.875 30.125 38.875 29 40 C27.147 40.072 25.292 40.084 23.438 40.062 C22.426 40.053 21.414 40.044 20.371 40.035 C19.197 40.018 19.197 40.018 18 40 C17.732 39.285 17.464 38.569 17.188 37.832 C15.983 34.959 14.588 32.367 13 29.688 C12.484 28.804 11.969 27.921 11.438 27.012 C10.963 26.348 10.489 25.684 10 25 C9.34 25 8.68 25 8 25 C7.67 29.95 7.34 34.9 7 40 C4.03 40 1.06 40 -2 40 C-3.801 29.197 -3.546 17.819 -2 7 C-2.66 7 -3.32 7 -4 7 C-4 6.34 -4 5.68 -4 5 C-3.34 5 -2.68 5 -2 5 C-2 4.01 -2 3.02 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z M7 19 C7.33 20.32 7.66 21.64 8 23 C8.33 22.34 8.66 21.68 9 21 C8.34 20.34 7.68 19.68 7 19 Z " fill="#89B5DA" transform="translate(772,816)"/>
<path d="M0 0 C3.243 3.33 5.312 7.641 5.312 12.375 C5.972 12.705 6.632 13.035 7.312 13.375 C7.312 14.035 7.312 14.695 7.312 15.375 C6.653 15.375 5.993 15.375 5.312 15.375 C5.312 22.305 5.312 29.235 5.312 36.375 C1.312 36.747 -2.681 37.086 -6.688 37.375 C-8.062 37.477 -8.062 37.477 -9.465 37.582 C-15.467 37.832 -18.9 37.109 -23.688 33.375 C-28.26 27.492 -29.601 20.695 -28.688 13.375 C-27.03 7.384 -25.205 2.686 -19.688 -0.625 C-19.069 -1.017 -18.45 -1.409 -17.812 -1.812 C-11.033 -4.405 -6.1 -3.688 0 0 Z M-16.688 8.375 C-16.358 9.695 -16.027 11.015 -15.688 12.375 C-16.678 13.035 -17.668 13.695 -18.688 14.375 C-19.254 19.23 -19.138 21.473 -16.938 25.875 C-16.195 26.7 -15.452 27.525 -14.688 28.375 C-13.368 28.375 -12.048 28.375 -10.688 28.375 C-10.358 27.385 -10.027 26.395 -9.688 25.375 C-9.027 24.715 -8.368 24.055 -7.688 23.375 C-8.348 23.375 -9.007 23.375 -9.688 23.375 C-10.348 21.395 -11.007 19.415 -11.688 17.375 C-11.027 17.375 -10.368 17.375 -9.688 17.375 C-9.688 16.715 -9.688 16.055 -9.688 15.375 C-9.027 15.045 -8.368 14.715 -7.688 14.375 C-9.007 13.715 -10.327 13.055 -11.688 12.375 C-8.387 12.705 -5.087 13.035 -1.688 13.375 C-1.482 11.081 -1.482 11.081 -3.75 8.75 C-6.536 6.068 -6.536 6.068 -10.312 5.938 C-13.811 6.138 -13.811 6.138 -16.688 8.375 Z M-17.688 10.375 C-16.688 12.375 -16.688 12.375 -16.688 12.375 Z M0.312 10.375 C-0.017 11.365 -0.348 12.355 -0.688 13.375 C0.303 13.375 1.293 13.375 2.312 13.375 C1.983 12.385 1.652 11.395 1.312 10.375 C0.983 10.375 0.652 10.375 0.312 10.375 Z M-6.688 14.375 C-6.688 14.705 -6.688 15.035 -6.688 15.375 C-3.717 15.375 -0.748 15.375 2.312 15.375 C2.312 15.045 2.312 14.715 2.312 14.375 C-0.658 14.375 -3.627 14.375 -6.688 14.375 Z M-10.688 18.375 C-9.688 20.375 -9.688 20.375 -9.688 20.375 Z M-5.688 25.375 C-6.678 25.87 -6.678 25.87 -7.688 26.375 C-7.358 27.035 -7.027 27.695 -6.688 28.375 C-6.027 28.045 -5.368 27.715 -4.688 27.375 C-5.017 26.715 -5.348 26.055 -5.688 25.375 Z " fill="#8BB5DA" transform="translate(414.6875,819.625)"/>
<path d="M0 0 C8 0 8 0 10.73 2.043 C13.258 5.336 15.336 8.67 17.312 12.312 C17.672 12.958 18.031 13.603 18.4 14.268 C19.274 15.841 20.138 17.42 21 19 C21.33 19 21.66 19 22 19 C21.988 18.047 21.977 17.095 21.965 16.113 C21.956 14.859 21.947 13.605 21.938 12.312 C21.926 11.071 21.914 9.83 21.902 8.551 C21.985 5.538 22.292 2.917 23 0 C25.97 0 28.94 0 32 0 C31.67 1.98 31.34 3.96 31 6 C31.66 6.33 32.32 6.66 33 7 C32.67 7.33 32.34 7.66 32 8 C31.956 9.666 31.96 11.334 32 13 C32 14.999 31.988 16.997 31.965 18.996 C31.956 20.132 31.947 21.267 31.938 22.438 C31.926 23.571 31.914 24.704 31.902 25.871 C31.984 28.5 32.355 30.49 33 33 C32.727 35.341 32.402 37.678 32 40 C22.567 39.649 22.567 39.649 19.061 35.96 C17.456 33.325 16.105 30.677 14.812 27.875 C14.337 26.93 13.862 25.985 13.373 25.012 C12.212 22.69 11.091 20.356 10 18 C10.186 18.784 10.371 19.567 10.562 20.375 C11 23 11 23 10 25 C10.99 25.33 11.98 25.66 13 26 C12.01 26.66 11.02 27.32 10 28 C9.636 30.437 9.636 30.437 9.812 33.125 C9.84 34.035 9.867 34.945 9.895 35.883 C9.929 36.581 9.964 37.28 10 38 C7.035 38.988 5.082 39.077 2 39 C1.01 39 0.02 39 -1 39 C-1.33 38.34 -1.66 37.68 -2 37 C-1.34 37 -0.68 37 0 37 C0 24.79 0 12.58 0 0 Z " fill="#8DB9DF" transform="translate(415,872)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.197 4.083 2.381 7.166 2.562 10.25 C2.619 11.123 2.675 11.995 2.732 12.895 C2.781 13.739 2.829 14.583 2.879 15.453 C2.926 16.228 2.973 17.003 3.022 17.802 C3 20 3 20 2 24 C8.93 24.33 15.86 24.66 23 25 C23.273 15.901 23.273 15.901 22.375 6.875 C21.971 3.776 22.265 2.551 24 0 C25.32 0.66 26.64 1.32 28 2 C28 2.66 28 3.32 28 4 C28.557 4.101 29.114 4.201 29.688 4.305 C38.317 6.433 41.854 12.218 46.539 19.34 C57.457 37.505 52.88 64.644 52 84 C25.93 84 -0.14 84 -27 84 C-26.845 72.46 -26.845 72.46 -26.688 60.688 C-26.663 58.282 -26.638 55.876 -26.612 53.397 C-26.58 51.456 -26.546 49.515 -26.512 47.574 C-26.502 46.104 -26.502 46.104 -26.493 44.604 C-26.312 35.801 -24.859 27.107 -20.062 19.562 C-19.579 18.78 -19.096 17.998 -18.598 17.191 C-16.879 14.834 -15.245 12.87 -13 11 C-11.68 11 -10.36 11 -9 11 C-9 11.66 -9 12.32 -9 13 C-10.027 13.876 -11.099 14.701 -12.188 15.5 C-17.558 19.923 -20.578 26.243 -22 33 C-22.111 34.618 -22.175 36.239 -22.205 37.86 C-22.226 38.834 -22.246 39.808 -22.266 40.812 C-22.283 41.828 -22.299 42.844 -22.316 43.891 C-22.335 44.83 -22.354 45.77 -22.373 46.738 C-22.441 50.242 -22.501 53.746 -22.562 57.25 C-22.707 65.087 -22.851 72.925 -23 81 C0.76 81 24.52 81 49 81 C48.876 72.956 48.753 64.913 48.625 56.625 C48.591 54.085 48.557 51.545 48.522 48.928 C48.487 46.923 48.451 44.919 48.414 42.914 C48.403 41.866 48.391 40.819 48.38 39.739 C48.36 38.744 48.34 37.75 48.319 36.725 C48.306 35.859 48.292 34.993 48.278 34.101 C48.188 31.782 48.188 31.782 46 30 C45.783 28.329 45.783 28.329 45.562 26.625 C44.983 22.893 44.392 21.76 42 19 C41.608 18.216 41.216 17.433 40.812 16.625 C37.38 11.654 32.333 8.666 27 6 C26.67 12.93 26.34 19.86 26 27 C22.414 27.896 19.791 28.116 16.141 28.098 C15.026 28.094 13.911 28.091 12.762 28.088 C11.603 28.08 10.444 28.071 9.25 28.062 C8.076 28.058 6.901 28.053 5.691 28.049 C2.794 28.037 -0.103 28.021 -3 28 C-3 27.01 -3 26.02 -3 25 C-2.34 25 -1.68 25 -1 25 C-1.03 24.38 -1.061 23.759 -1.092 23.12 C-1.229 20.309 -1.364 17.498 -1.5 14.688 C-1.548 13.711 -1.595 12.735 -1.645 11.729 C-1.69 10.791 -1.735 9.853 -1.781 8.887 C-1.823 8.023 -1.865 7.159 -1.908 6.268 C-1.979 4.513 -2 2.756 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#618BB9" transform="translate(499,492)"/>
<path d="M0 0 C4.573 2.243 7.248 4.793 9.305 9.391 C11.098 16.12 11.663 24.87 8.312 31.188 C5.229 34.937 2.636 37.455 -2 39 C-8.139 39.501 -13.915 39.874 -19 36 C-23.867 29.754 -25.811 23.972 -25 16 C-23.895 11.564 -22.541 7.812 -20 4 C-20.99 4 -21.98 4 -23 4 C-22.67 3.34 -22.34 2.68 -22 2 C-21.34 2.082 -20.68 2.165 -20 2.25 C-16.898 1.992 -15.845 1.422 -13.25 -0.125 C-8.439 -2.685 -4.946 -1.927 0 0 Z M-12 10 C-11.681 12.108 -11.681 12.108 -11 14 C-12.32 14 -13.64 14 -15 14 C-15.457 21.776 -15.457 21.776 -13 29 C-9.798 30.601 -6.475 30.484 -3 30 C-0.016 27.786 0.279 25.141 0.812 21.625 C1.201 16.805 0.465 13.198 -2.25 9.125 C-5.738 6.883 -9.126 7.126 -12 10 Z M-13 10 C-12 12 -12 12 -12 12 Z " fill="#8AB5DA" transform="translate(755,818)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C3.814 3.722 3.629 4.444 3.438 5.188 C2.979 8.138 3.282 10.129 4 13 C4 13.33 4 13.66 4 14 C6.416 14.25 6.416 14.25 9 14 C9.66 13.01 10.32 12.02 11 11 C11.33 11.33 11.66 11.66 12 12 C12.33 10.68 12.66 9.36 13 8 C12.34 8 11.68 8 11 8 C11 7.01 11 6.02 11 5 C11.66 5 12.32 5 13 5 C12.67 4.01 12.34 3.02 12 2 C15.634 -0.326 18.771 -0.163 23 0 C23.66 1.32 24.32 2.64 25 4 C24.34 4 23.68 4 23 4 C22.805 6.583 22.619 9.166 22.438 11.75 C22.381 12.48 22.325 13.209 22.268 13.961 C22.022 17.567 22.064 20.531 23 24 C23.065 25.582 23.086 27.167 23.062 28.75 C23.053 29.549 23.044 30.348 23.035 31.172 C23.024 31.775 23.012 32.378 23 33 C23.33 33 23.66 33 24 33 C24 34.98 24 36.96 24 39 C23.01 39 22.02 39 21 39 C18.326 39.286 15.666 39.65 13 40 C11.837 36.45 12.076 33.442 12.438 29.75 C12.539 28.672 12.641 27.595 12.746 26.484 C12.83 25.665 12.914 24.845 13 24 C8.05 24.495 8.05 24.495 3 25 C3.041 26.382 3.082 27.764 3.125 29.188 C3.119 32.885 2.932 35.746 1 39 C-2.766 40.255 -4.372 39.371 -8 38 C-9.333 18.032 -9.333 18.032 -8 9 C-8.66 9 -9.32 9 -10 9 C-8.125 2.125 -8.125 2.125 -7 1 C-4.672 0.632 -2.338 0.298 0 0 Z M22 36 C23 38 23 38 23 38 Z " fill="#91BEE5" transform="translate(504,872)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C9.292 1.213 12.583 1.376 15.879 1.523 C20.228 2.188 22.924 3.775 25.812 7.062 C27.56 11.386 27.701 15.402 27 20 C25.125 23.125 25.125 23.125 23 25 C22.34 25 21.68 25 21 25 C21 25.99 21 26.98 21 28 C21.99 27.67 22.98 27.34 24 27 C24 28.32 24 29.64 24 31 C25.194 32.921 26.569 34.571 28.004 36.316 C28.333 36.872 28.661 37.428 29 38 C28.67 38.99 28.34 39.98 28 41 C26.542 41.027 25.083 41.046 23.625 41.062 C22.407 41.08 22.407 41.08 21.164 41.098 C19 41 19 41 17 40 C16.071 38.511 15.203 36.984 14.375 35.438 C13.706 34.198 13.706 34.198 13.023 32.934 C12.686 32.296 12.348 31.657 12 31 C11.34 31 10.68 31 10 31 C10 33.97 10 36.94 10 40 C6.04 40 2.08 40 -2 40 C-2.33 39.34 -2.66 38.68 -3 38 C-2.34 37.34 -1.68 36.68 -1 36 C-0.833 32.417 -0.833 32.417 -1 29 C-1.66 28.67 -2.32 28.34 -3 28 C-3.25 22.375 -3.25 22.375 -1 19 C-1.33 19 -1.66 19 -2 19 C-2 17.35 -2 15.7 -2 14 C-1.67 14 -1.34 14 -1 14 C-0.67 9.38 -0.34 4.76 0 0 Z M9 10 C9.99 10.33 10.98 10.66 12 11 C12 11.66 12 12.32 12 13 C11.01 12.67 10.02 12.34 9 12 C9 14.64 9 17.28 9 20 C13.342 19.815 13.342 19.815 17 18 C17.041 15.667 17.042 13.333 17 11 C14.108 10.174 12.113 10 9 10 Z M9 27 C10 29 10 29 10 29 Z " fill="#8CB7DC" transform="translate(287,816)"/>
<path d="M0 0 C8.571 -0.286 8.571 -0.286 12 2 C11.67 2.99 11.34 3.98 11 5 C10.34 5 9.68 5 9 5 C9.126 5.922 9.126 5.922 9.254 6.863 C9.356 7.672 9.458 8.48 9.562 9.312 C9.667 10.113 9.771 10.914 9.879 11.738 C10 14 10 14 9 17 C9.99 17.33 10.98 17.66 12 18 C11.01 19.65 10.02 21.3 9 23 C9.66 23.33 10.32 23.66 11 24 C10.814 24.577 10.629 25.155 10.438 25.75 C9.81 28.978 10.552 31.105 12 34 C11.01 34 10.02 34 9 34 C9.33 34.66 9.66 35.32 10 36 C12.025 36.652 12.025 36.652 14 37 C14 37.99 14 38.98 14 40 C9.05 40 4.1 40 -1 40 C-11.149 22.851 -11.149 22.851 -13 14 C-13.66 14 -14.32 14 -15 14 C-15 22.25 -15 30.5 -15 39 C-16.98 39 -18.96 39 -21 39 C-21 27.12 -21 15.24 -21 3 C-13 3 -13 3 -10.312 5.5 C-9.511 6.646 -8.742 7.815 -8 9 C-7.533 9.696 -7.067 10.392 -6.586 11.109 C-3 16.598 -3 16.598 -3 19 C-2.34 18.67 -1.68 18.34 -1 18 C-1.33 17.01 -1.66 16.02 -2 15 C-2.66 14.67 -3.32 14.34 -4 14 C-2.68 12.02 -1.36 10.04 0 8 C-0.66 8 -1.32 8 -2 8 C-2 6.02 -2 4.04 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8EBADF" transform="translate(650,816)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C3.017 3.134 3.017 3.134 5 4 C5 4.66 5 5.32 5 6 C5.66 6.66 6.32 7.32 7 8 C8.527 16.585 9.056 25.512 4 32.938 C2.677 34.302 1.344 35.656 0 37 C0 37.66 0 38.32 0 39 C-0.562 38.82 -1.124 38.639 -1.703 38.453 C-4.325 37.936 -6.133 38.267 -8.75 38.75 C-13.667 39.308 -16.357 38.427 -20.312 35.508 C-24.936 31.377 -26.912 26.727 -27.945 20.727 C-28.069 14.553 -25.296 9.222 -22.25 4 C-15.731 -1.795 -8.206 -1.746 0 0 Z M-14.812 9.312 C-17.738 13.47 -17.316 18.145 -17 23 C-16.567 25.925 -16.567 25.925 -15 28 C-12.238 29.671 -12.238 29.671 -9 30 C-5.865 28.789 -4.447 27.691 -2.625 24.875 C-1.744 20.822 -2.063 17.034 -3 13 C-3.66 12.01 -4.32 11.02 -5 10 C-5.66 10 -6.32 10 -7 10 C-7 9.34 -7 8.68 -7 8 C-11.167 7.614 -11.167 7.614 -14.812 9.312 Z " fill="#88B3D8" transform="translate(403,873)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C11.31 6.27 13.62 12.54 16 19 C17.601 16.598 18.536 14.848 19.508 12.207 C19.772 11.495 20.036 10.783 20.309 10.049 C20.578 9.311 20.847 8.573 21.125 7.812 C21.402 7.063 21.679 6.313 21.965 5.541 C22.647 3.695 23.324 1.848 24 0 C25.65 0 27.3 0 29 0 C29 8.58 29 17.16 29 26 C29.66 26.33 30.32 26.66 31 27 C30.67 29.97 30.34 32.94 30 36 C26.688 36.688 26.688 36.688 23 37 C21.062 35.562 21.062 35.562 20 34 C19.34 34.99 18.68 35.98 18 37 C13.25 36.125 13.25 36.125 11 35 C10.367 32.496 10.367 32.496 9.875 29.438 C9.707 28.426 9.54 27.414 9.367 26.371 C9.246 25.589 9.125 24.806 9 24 C8.01 24 7.02 24 6 24 C5.34 23.01 4.68 22.02 4 21 C3.34 25.29 2.68 29.58 2 34 C-0.31 34 -2.62 34 -5 34 C-4.154 26.806 -3.05 19.657 -1.938 12.5 C-1.751 11.296 -1.565 10.092 -1.373 8.852 C-0.916 5.901 -0.459 2.95 0 0 Z M5 15 C6 17 6 17 6 17 Z M5 18 C5.66 18.66 6.32 19.32 7 20 C7 19.34 7 18.68 7 18 C6.34 18 5.68 18 5 18 Z M7 20 C8 23 8 23 8 23 Z M22 23 C23 26 23 26 23 26 Z M21 29 C20.505 30.98 20.505 30.98 20 33 C20.33 32.34 20.66 31.68 21 31 C21.66 31 22.32 31 23 31 C23 30.34 23 29.68 23 29 C22.34 29 21.68 29 21 29 Z " fill="#8CB9DF" transform="translate(581,875)"/>
<path d="M0 0 C0.76 -0.004 1.52 -0.008 2.303 -0.012 C6.231 -0.005 9.852 0.161 13.688 1.125 C13.688 3.765 13.688 6.405 13.688 9.125 C12.697 9.455 11.707 9.785 10.688 10.125 C9.479 12.125 9.479 12.125 8.688 14.125 C7.367 12.805 6.048 11.485 4.688 10.125 C1.06 11.991 1.06 11.991 -1.312 15.125 C2.317 15.125 5.947 15.125 9.688 15.125 C11.222 18.194 10.237 20.826 9.688 24.125 C6.717 24.125 3.747 24.125 0.688 24.125 C2.922 27.476 3.95 28.941 7.938 29.938 C9.299 30.03 9.299 30.03 10.688 30.125 C10.688 29.465 10.688 28.805 10.688 28.125 C13.556 29.559 14.852 31.52 16.688 34.125 C17.438 36.938 17.438 36.938 17.688 39.125 C13.667 40.465 9.941 40.109 5.773 39.871 C4.848 39.821 3.922 39.772 2.969 39.721 C1.023 39.615 -0.922 39.505 -2.867 39.393 C-3.79 39.344 -4.713 39.296 -5.664 39.246 C-6.508 39.199 -7.352 39.152 -8.221 39.103 C-10.373 38.973 -10.373 38.973 -12.312 40.125 C-13.609 35.514 -13.024 31.787 -12.312 27.125 C-11.982 27.125 -11.653 27.125 -11.312 27.125 C-10.982 18.545 -10.653 9.965 -10.312 1.125 C-7.077 -0.493 -3.55 -0.018 0 0 Z M-1.312 24.125 C-0.312 28.125 -0.312 28.125 -0.312 28.125 Z M13.688 34.125 C14.688 37.125 14.688 37.125 14.688 37.125 Z " fill="#8DB9E0" transform="translate(542.3125,871.875)"/>
<path d="M0 0 C0 29.7 0 59.4 0 90 C-27.39 90 -54.78 90 -83 90 C-83 60.96 -83 31.92 -83 2 C-83.66 1.67 -84.32 1.34 -85 1 C-82.021 -0.489 -78.961 -0.188 -75.692 -0.221 C-74.563 -0.238 -74.563 -0.238 -73.41 -0.256 C-70.913 -0.293 -68.415 -0.322 -65.918 -0.352 C-64.184 -0.375 -62.45 -0.398 -60.716 -0.422 C-57.077 -0.47 -53.438 -0.515 -49.799 -0.558 C-45.138 -0.612 -40.477 -0.676 -35.817 -0.743 C-32.234 -0.793 -28.65 -0.838 -25.067 -0.882 C-23.349 -0.903 -21.631 -0.926 -19.913 -0.951 C-17.507 -0.985 -15.102 -1.012 -12.696 -1.038 C-11.988 -1.049 -11.28 -1.06 -10.55 -1.072 C-6.79 -1.104 -3.743 -0.749 0 0 Z M-81 2 C-81 30.05 -81 58.1 -81 87 C-77.513 88.162 -74.4 88.132 -70.775 88.114 C-69.659 88.114 -69.659 88.114 -68.521 88.114 C-66.114 88.113 -63.707 88.106 -61.301 88.098 C-59.815 88.096 -58.328 88.095 -56.842 88.095 C-51.332 88.089 -45.822 88.075 -40.312 88.062 C-27.999 88.042 -15.686 88.021 -3 88 C-3 59.62 -3 31.24 -3 2 C-28.74 2 -54.48 2 -81 2 Z " fill="#38689E" transform="translate(941,470)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C9.92 2 17.84 2 26 2 C26 4.64 26 7.28 26 10 C23.03 10.868 20.462 11.083 17.375 11 C14.057 10.873 14.057 10.873 11 12 C11.33 12.99 11.66 13.98 12 15 C13.65 15 15.3 15 17 15 C17 15.66 17 16.32 17 17 C18.224 17.075 18.224 17.075 20 16 C21.32 16.99 22.64 17.98 24 19 C23.67 19.66 23.34 20.32 23 21 C22.835 21.825 22.67 22.65 22.5 23.5 C22 26 22 26 21 28 C20.01 28 19.02 28 18 28 C17.67 28.66 17.34 29.32 17 30 C17 28.68 17 27.36 17 26 C15.68 26 14.36 26 13 26 C13 27.65 13 29.3 13 31 C13.855 31.061 14.709 31.121 15.59 31.184 C16.695 31.267 17.799 31.351 18.938 31.438 C20.591 31.559 20.591 31.559 22.277 31.684 C25 32 25 32 26 33 C26.041 35.333 26.042 37.667 26 40 C23.401 41.299 21.673 41.022 18.773 40.879 C17.845 40.836 16.916 40.793 15.958 40.748 C14.776 40.687 13.593 40.626 12.375 40.562 C8.621 40.377 4.867 40.191 1 40 C0.67 26.8 0.34 13.6 0 0 Z M11 25 C12 29 12 29 12 29 Z " fill="#8FBBE2" transform="translate(806,816)"/>
<path d="M0 0 C1.284 2.569 1.33 4.766 1.562 7.625 C1.688 9.129 1.688 9.129 1.816 10.664 C1.877 11.435 1.938 12.206 2 13 C0.35 13.66 -1.3 14.32 -3 15 C-3.33 14.34 -3.66 13.68 -4 13 C-3.67 12.34 -3.34 11.68 -3 11 C-6.3 11 -9.6 11 -13 11 C-12.67 11.99 -12.34 12.98 -12 14 C-9.624 15.184 -9.624 15.184 -7 16 C-2.312 17.563 0.338 20.915 3 25 C4.211 29.277 3.291 32.419 1.312 36.375 C-1.671 39.762 -4.183 40.794 -8.648 41.301 C-14.576 41.326 -18.75 40.8 -24 38 C-23.67 34.7 -23.34 31.4 -23 28 C-21.68 28.33 -20.36 28.66 -19 29 C-19 29.66 -19 30.32 -19 31 C-14.859 31.21 -14.859 31.21 -11 30 C-11 30.66 -11 31.32 -11 32 C-10.34 31.67 -9.68 31.34 -9 31 C-9 30.34 -9 29.68 -9 29 C-9.99 28.67 -10.98 28.34 -12 28 C-12 27.01 -12 26.02 -12 25 C-12.887 24.773 -13.774 24.546 -14.688 24.312 C-19.344 22.467 -21.791 19.789 -24.562 15.688 C-25.151 12.072 -24.305 10.083 -22.512 6.938 C-16.706 -0.503 -8.775 -0.668 0 0 Z " fill="#87B2D6" transform="translate(700,816)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C7.64 1.33 10.28 1.66 13 2 C13 1.34 13 0.68 13 0 C19.152 0.586 19.152 0.586 21 1 C22.642 4.283 21.519 7.425 21 11 C17.04 10.67 13.08 10.34 9 10 C9 11.98 9 13.96 9 16 C11.97 16 14.94 16 18 16 C20.125 22.625 20.125 22.625 19 26 C16.03 26.33 13.06 26.66 10 27 C9.67 28.65 9.34 30.3 9 32 C13.29 32 17.58 32 22 32 C22 34.64 22 37.28 22 40 C13.42 40 4.84 40 -4 40 C-3.67 38.35 -3.34 36.7 -3 35 C-3.165 34.381 -3.33 33.763 -3.5 33.125 C-4.237 29.994 -3.452 27.161 -3 24 C-3.347 21.678 -3.347 21.678 -4 20 C-3.67 20 -3.34 20 -3 20 C-3.012 18.952 -3.023 17.904 -3.035 16.824 C-3.045 15.445 -3.054 14.066 -3.062 12.688 C-3.071 11.997 -3.079 11.307 -3.088 10.596 C-3.103 7.452 -3.002 5.006 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z M7 12 C8 16 8 16 8 16 Z M7 26 C7 27.98 7 29.96 7 32 C7.33 32 7.66 32 8 32 C8 30.02 8 28.04 8 26 C7.67 26 7.34 26 7 26 Z " fill="#90BDE3" transform="translate(335,816)"/>
<path d="M0 0 C1.147 -0.008 1.147 -0.008 2.316 -0.016 C4.438 0.25 4.438 0.25 7.438 2.25 C9.336 6.425 10.637 10.832 12.062 15.188 C12.47 16.374 12.877 17.561 13.297 18.783 C15.682 25.973 17.18 32.67 17.438 40.25 C14.798 40.25 12.158 40.25 9.438 40.25 C8.181 37.344 7.438 35.454 7.438 32.25 C6.777 31.92 6.118 31.59 5.438 31.25 C4.586 28.965 4.586 28.965 3.812 26.188 C3.422 24.809 3.422 24.809 3.023 23.402 C2.83 22.692 2.637 21.982 2.438 21.25 C2.449 22.203 2.461 23.155 2.473 24.137 C2.482 25.391 2.491 26.645 2.5 27.938 C2.512 29.179 2.523 30.42 2.535 31.699 C2.452 34.712 2.146 37.333 1.438 40.25 C-1.863 40.25 -5.163 40.25 -8.562 40.25 C-8.562 37.28 -8.562 34.31 -8.562 31.25 C-8.233 31.25 -7.902 31.25 -7.562 31.25 C-7.562 26.3 -7.562 21.35 -7.562 16.25 C-7.892 16.25 -8.223 16.25 -8.562 16.25 C-8.589 14.438 -8.609 12.625 -8.625 10.812 C-8.637 9.803 -8.648 8.794 -8.66 7.754 C-8.562 5.25 -8.562 5.25 -7.562 4.25 C-7.892 3.59 -8.223 2.93 -8.562 2.25 C-5.588 0.542 -3.424 -0.023 0 0 Z M-7.562 33.25 C-6.562 37.25 -6.562 37.25 -6.562 37.25 Z " fill="#90BCE2" transform="translate(368.5625,815.75)"/>
<path d="M0 0 C7.449 0.007 14.898 0.009 22.347 0.01 C35.619 0.012 48.891 0.019 62.162 0.028 C75.013 0.037 87.864 0.044 100.715 0.049 C101.914 0.049 101.914 0.049 103.137 0.049 C107.149 0.051 111.161 0.052 115.174 0.053 C148.175 0.063 181.177 0.081 214.179 0.104 C213.849 1.094 213.519 2.084 213.179 3.104 C145.261 3.183 77.345 2.988 9.427 2.658 C-3.414 2.596 -16.255 2.536 -29.096 2.477 C-55.337 2.355 -81.579 2.231 -107.821 2.104 C-107.821 1.774 -107.821 1.444 -107.821 1.104 C-71.879 0.194 -35.951 -0.036 0 0 Z " fill="#6C99C1" transform="translate(723.8206783607602,764.8964622281492)"/>
<path d="M0 0 C0.848 -0.001 1.696 -0.001 2.569 -0.002 C17.737 -0.01 32.894 0.196 48.059 0.527 C50.425 0.576 52.792 0.624 55.158 0.672 C60.771 0.786 66.384 0.905 71.996 1.027 C71.996 1.357 71.996 1.687 71.996 2.027 C70.755 2.052 69.514 2.077 68.236 2.103 C63.618 2.198 58.999 2.298 54.381 2.4 C52.385 2.443 50.389 2.485 48.393 2.525 C45.518 2.583 42.644 2.646 39.77 2.711 C38.882 2.728 37.994 2.744 37.079 2.762 C36.239 2.782 35.399 2.801 34.533 2.822 C33.8 2.838 33.067 2.853 32.312 2.869 C29.935 2.984 29.935 2.984 27.392 3.527 C23.343 4.123 19.426 4.181 15.34 4.186 C14.052 4.193 14.052 4.193 12.738 4.201 C9.869 4.216 6.999 4.224 4.129 4.233 C2.069 4.242 0.009 4.252 -2.051 4.263 C-7.639 4.29 -13.227 4.311 -18.815 4.331 C-24.657 4.353 -30.499 4.38 -36.34 4.407 C-47.401 4.457 -58.461 4.502 -69.521 4.545 C-82.113 4.594 -94.706 4.649 -107.298 4.704 C-133.2 4.818 -159.102 4.925 -185.004 5.027 C-185.004 3.707 -185.004 2.387 -185.004 1.027 C-164.459 0.895 -143.915 0.765 -123.371 0.636 C-113.826 0.576 -104.282 0.516 -94.738 0.455 C-86.397 0.401 -78.055 0.348 -69.714 0.296 C-65.318 0.269 -60.922 0.241 -56.526 0.213 C-37.684 0.089 -18.842 0.006 0 0 Z " fill="#5B86B1" transform="translate(264.00390625,970.97265625)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 1.67 2.98 1.34 4 1 C4.33 1.33 4.66 1.66 5 2 C5.33 1.34 5.66 0.68 6 0 C14.079 0.777 14.079 0.777 17 4 C21 11.543 21 11.543 21 15 C21.99 15 22.98 15 24 15 C24.103 15.866 24.206 16.732 24.312 17.625 C25.346 22.7 26.634 26.089 31 29 C31.33 29.33 31.66 29.66 32 30 C33.963 26.44 35.92 22.877 37.875 19.312 C38.431 18.305 38.986 17.298 39.559 16.26 C40.094 15.283 40.629 14.307 41.18 13.301 C41.672 12.405 42.164 11.51 42.671 10.587 C43.947 8.102 45.009 5.61 46 3 C46.66 3 47.32 3 48 3 C48.33 2.34 48.66 1.68 49 1 C49.99 1.33 50.98 1.66 52 2 C52.33 1.34 52.66 0.68 53 0 C55.31 0.66 57.62 1.32 60 2 C60.33 1.34 60.66 0.68 61 0 C61.99 0 62.98 0 64 0 C66.429 3.644 66.243 5.336 66.227 9.675 C66.227 10.348 66.228 11.021 66.228 11.715 C66.227 13.939 66.211 16.163 66.195 18.387 C66.192 19.929 66.189 21.472 66.187 23.015 C66.179 27.074 66.159 31.133 66.137 35.192 C66.117 39.334 66.108 43.477 66.098 47.619 C66.076 55.746 66.041 63.873 66 72 C60.39 72 54.78 72 49 72 C47.695 69.39 47.903 67.501 47.938 64.582 C47.945 63.492 47.953 62.402 47.961 61.279 C47.987 58.988 48.013 56.696 48.039 54.404 C48.047 53.308 48.055 52.212 48.062 51.082 C48.074 50.084 48.086 49.086 48.098 48.058 C47.99 44.687 47.477 41.339 47 38 C43.7 44.6 40.4 51.2 37 58 C33.7 58 30.4 58 27 58 C27 57.67 27 57.34 27 57 C29.97 57 32.94 57 36 57 C36.238 56.199 36.477 55.399 36.723 54.574 C38.308 50.138 40.326 46.018 42.438 41.812 C42.833 41.006 43.228 40.2 43.635 39.369 C44.022 38.594 44.409 37.818 44.809 37.02 C45.332 35.969 45.332 35.969 45.865 34.898 C47 33 47 33 50 30 C50 43.53 50 57.06 50 71 C54.62 71 59.24 71 64 71 C64 48.89 64 26.78 64 4 C58.39 4 52.78 4 47 4 C46.67 5.65 46.34 7.3 46 9 C45.008 11.161 43.947 13.29 42.84 15.395 C42.531 15.987 42.221 16.579 41.902 17.189 C40.92 19.067 39.929 20.94 38.938 22.812 C38.267 24.09 37.597 25.368 36.928 26.646 C35.292 29.768 33.648 32.885 32 36 C31.34 36 30.68 36 30 36 C28.019 32.088 26.044 28.174 24.073 24.257 C23.403 22.925 22.731 21.595 22.057 20.265 C21.087 18.35 20.123 16.433 19.16 14.516 C18.579 13.364 17.998 12.211 17.399 11.024 C16 8 16 8 15 4 C9.06 4 3.12 4 -3 4 C-3 26.11 -3 48.22 -3 71 C1.95 71 6.9 71 12 71 C12 57.47 12 43.94 12 30 C12.66 30 13.32 30 14 30 C15.959 36.013 17.416 41.696 18 48 C16.515 48.495 16.515 48.495 15 49 C15.008 49.992 15.008 49.992 15.016 51.004 C15.037 53.982 15.05 56.96 15.062 59.938 C15.071 60.978 15.079 62.019 15.088 63.092 C15.093 64.576 15.093 64.576 15.098 66.09 C15.106 67.465 15.106 67.465 15.114 68.867 C15 71 15 71 14 72 C8.06 72 2.12 72 -4 72 C-4 48.9 -4 25.8 -4 2 C-2.68 1.34 -1.36 0.68 0 0 Z " fill="#6A92B4" transform="translate(687,624)"/>
<path d="M0 0 C11.55 0 23.1 0 35 0 C36.581 3.161 35.802 6.491 35.562 9.938 C33.941 38.347 35.12 66.936 35.571 95.372 C35.923 117.583 36.152 139.786 36 162 C31.07 162.025 26.141 162.043 21.211 162.055 C19.534 162.06 17.857 162.067 16.18 162.075 C13.77 162.088 11.359 162.093 8.949 162.098 C8.199 162.103 7.449 162.108 6.676 162.113 C3.51 162.114 1.027 162.009 -2 161 C-1.67 160.01 -1.34 159.02 -1 158 C-0.908 156.481 -0.869 154.959 -0.867 153.438 C-0.866 152.558 -0.865 151.679 -0.863 150.773 C-0.867 149.858 -0.871 148.943 -0.875 148 C-0.871 147.085 -0.867 146.17 -0.863 145.227 C-0.865 144.347 -0.866 143.468 -0.867 142.562 C-0.868 141.759 -0.869 140.956 -0.871 140.129 C-0.92 137.811 -0.92 137.811 -2 135 C-1.34 135 -0.68 135 0 135 C-0.531 131.179 -1.497 128.938 -4 126 C-2.02 125.01 -2.02 125.01 0 124 C-0.66 123.67 -1.32 123.34 -2 123 C-1.34 123 -0.68 123 0 123 C-0.165 122.475 -0.33 121.951 -0.5 121.41 C-1.131 118.37 -1.056 115.54 -1 112.438 C-0.906 107.632 -0.906 107.632 -2 103 C-1.34 103 -0.68 103 0 103 C-0.99 100.36 -1.98 97.72 -3 95 C-2.01 95 -1.02 95 0 95 C-0.114 92.895 -0.242 90.791 -0.375 88.688 C-0.479 86.93 -0.479 86.93 -0.586 85.137 C-0.786 81.843 -0.786 81.843 -3 79 C-2.01 77.35 -1.02 75.7 0 74 C-0.99 73.67 -1.98 73.34 -3 73 C-2.01 72.01 -2.01 72.01 -1 71 C-0.714 68.639 -0.714 68.639 -0.789 65.87 C-0.792 64.798 -0.796 63.725 -0.799 62.62 C-0.813 61.453 -0.826 60.285 -0.84 59.082 C-0.845 57.877 -0.85 56.673 -0.856 55.432 C-0.873 51.559 -0.905 47.686 -0.938 43.812 C-0.965 39.986 -0.99 36.159 -1.009 32.332 C-1.021 29.95 -1.038 27.568 -1.062 25.186 C-1.112 16.694 -0.569 8.535 0 0 Z M1 1 C1 53.14 1 105.28 1 159 C11.56 159 22.12 159 33 159 C33 106.86 33 54.72 33 1 C22.44 1 11.88 1 1 1 Z " fill="#793A2E" transform="translate(564,78)"/>
<path d="M0 0 C10.89 0 21.78 0 33 0 C34.62 40.501 34.62 40.501 33 49 C33.99 49.495 33.99 49.495 35 50 C34.34 50 33.68 50 33 50 C33.077 51.142 33.155 52.283 33.234 53.46 C33.924 64.42 34.012 75.244 33.648 86.216 C33.499 90.751 33.374 95.285 33.254 99.82 C33.229 100.743 33.205 101.666 33.179 102.617 C32.895 114.079 32.947 125.541 33.037 137.005 C33.042 137.762 33.046 138.52 33.05 139.3 C33.062 141.328 33.079 143.356 33.098 145.384 C32.999 149.029 32.588 152.407 32 156 C28.454 156.808 25.177 157.128 21.543 157.133 C20.512 157.134 19.482 157.135 18.42 157.137 C17.353 157.133 16.287 157.129 15.188 157.125 C14.132 157.129 13.077 157.133 11.99 157.137 C6.231 157.13 0.695 156.866 -5 156 C-5.33 153.69 -5.66 151.38 -6 149 C-4 151 -4 151 -4 155 C7.55 155 19.1 155 31 155 C31 104.18 31 53.36 31 1 C21.1 1 11.2 1 1 1 C1 28.72 1 56.44 1 85 C0.34 85 -0.32 85 -1 85 C-1.534 83.588 -2.052 82.17 -2.562 80.75 C-2.853 79.961 -3.143 79.172 -3.441 78.359 C-4.045 75.809 -3.847 74.447 -3 72 C-2.67 72.33 -2.34 72.66 -2 73 C-1.67 64.09 -1.34 55.18 -1 46 C-1.99 45.67 -2.98 45.34 -4 45 C-3.34 44.34 -2.68 43.68 -2 43 C-1.758 41.019 -1.758 41.019 -1.875 38.875 C-1.916 37.596 -1.957 36.317 -2 35 C-2.33 35 -2.66 35 -3 35 C-3 33.02 -3 31.04 -3 29 C-2.67 29 -2.34 29 -2 29 C-2 25.7 -2 22.4 -2 19 C-2.33 19 -2.66 19 -3 19 C-3.33 17.02 -3.66 15.04 -4 13 C-3.34 12.67 -2.68 12.34 -2 12 C-2.041 10.948 -2.082 9.896 -2.125 8.812 C-2 5 -2 5 0 0 Z " fill="#7D453D" transform="translate(692,270)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.99 3 1.98 3 3 C3.33 2.34 3.66 1.68 4 1 C6.322 0.593 8.657 0.256 11 0 C11 0.99 11 1.98 11 3 C11.66 2.01 12.32 1.02 13 0 C18.445 0.495 18.445 0.495 24 1 C24 1.66 24 2.32 24 3 C24.516 2.67 25.031 2.34 25.562 2 C28.844 0.654 31.484 0.744 35 1 C35.99 1.66 36.98 2.32 38 3 C38.328 5.969 38.328 5.969 38.25 9.5 C38.235 10.665 38.219 11.831 38.203 13.031 C38 16 38 16 37 18 C28.42 18.33 19.84 18.66 11 19 C11.824 24.302 11.824 24.302 15 28 C18.033 28.615 21.037 28.651 24.125 28.688 C32.911 28.911 32.911 28.911 34 30 C34.073 32.353 34.084 34.708 34.062 37.062 C34.053 38.353 34.044 39.643 34.035 40.973 C34.024 41.972 34.012 42.971 34 44 C24.118 46 24.118 46 22 46 C20.515 46.495 20.515 46.495 19 47 C19 46.34 19 45.68 19 45 C17.515 44.505 17.515 44.505 16 44 C16 45.32 16 46.64 16 48 C14.35 47.67 12.7 47.34 11 47 C10.67 46.01 10.34 45.02 10 44 C10 47.96 10 51.92 10 56 C16.93 56 23.86 56 31 56 C31 56.33 31 56.66 31 57 C33.31 57 35.62 57 38 57 C38 58.32 38 59.64 38 61 C38.99 61.495 38.99 61.495 40 62 C39.34 62 38.68 62 38 62 C38.144 63.279 38.289 64.558 38.438 65.875 C38.519 66.594 38.6 67.314 38.684 68.055 C38.913 70.074 38.913 70.074 40 72 C24.49 72 8.98 72 -7 72 C-7.739 67.564 -8.122 63.954 -8.114 59.536 C-8.113 58.275 -8.113 57.014 -8.113 55.715 C-8.108 54.354 -8.103 52.993 -8.098 51.633 C-8.096 50.229 -8.094 48.825 -8.093 47.421 C-8.09 43.742 -8.08 40.063 -8.069 36.384 C-8.058 32.623 -8.054 28.862 -8.049 25.102 C-8.038 17.734 -8.021 10.367 -8 3 C-5.36 3 -2.72 3 0 3 C0 2.01 0 1.02 0 0 Z M-6 4 C-6 26.11 -6 48.22 -6 71 C7.86 71 21.72 71 36 71 C36 67.04 36 63.08 36 59 C27.09 59 18.18 59 9 59 C9 53.72 9 48.44 9 43 C16.59 43 24.18 43 32 43 C32 39.04 32 35.08 32 31 C24.41 31 16.82 31 9 31 C9 26.05 9 21.1 9 16 C17.91 16 26.82 16 36 16 C36 12.04 36 8.08 36 4 C22.14 4 8.28 4 -6 4 Z M13 44 C13 44.66 13 45.32 13 46 C13.66 45.67 14.32 45.34 15 45 C14.34 44.67 13.68 44.34 13 44 Z " fill="#6085A7" transform="translate(776,624)"/>
<path d="M0 0 C1.649 3.297 2.326 5.123 2.898 8.598 C3.931 14.104 5.684 19.194 7.625 24.438 C8.267 26.185 8.905 27.933 9.539 29.684 C9.82 30.442 10.1 31.201 10.389 31.982 C11 34 11 34 11 37 C8.69 37 6.38 37 4 37 C3.01 34.36 2.02 31.72 1 29 C-2.96 28.67 -6.92 28.34 -11 28 C-11.66 30.97 -12.32 33.94 -13 37 C-15.64 37 -18.28 37 -21 37 C-17.04 25.12 -13.08 13.24 -9 1 C-4.545 0.505 -4.545 0.505 0 0 Z M-5 12 C-5.33 14.31 -5.66 16.62 -6 19 C-3.693 16.285 -3.693 16.285 -4.375 13.812 C-4.581 13.214 -4.788 12.616 -5 12 Z M-4 17 C-3 19 -3 19 -3 19 Z M-3 19 C-3.99 19.495 -3.99 19.495 -5 20 C-4.67 20.66 -4.34 21.32 -4 22 C-3.34 22 -2.68 22 -2 22 C-2.33 21.01 -2.66 20.02 -3 19 Z " fill="#8FBBE1" transform="translate(583,818)"/>
<path d="M0 0 C0.625 2.812 0.625 2.812 1 6 C0.01 7.485 0.01 7.485 -1 9 C-2.945 9.293 -2.945 9.293 -5.125 9.188 C-6.404 9.126 -7.683 9.064 -9 9 C-8.988 9.692 -8.977 10.384 -8.965 11.098 C-8.888 19.448 -9.363 27.679 -10 36 C-9.34 36.33 -8.68 36.66 -8 37 C-8.99 37 -9.98 37 -11 37 C-11.33 37.66 -11.66 38.32 -12 39 C-18.875 38.125 -18.875 38.125 -20 37 C-20.383 33.974 -20.471 30.92 -20.625 27.875 C-20.677 27.032 -20.728 26.189 -20.781 25.32 C-21.042 20.375 -20.885 15.875 -20 11 C-20.99 10.67 -21.98 10.34 -23 10 C-25 9.333 -27 8.667 -29 8 C-29 5.36 -29 2.72 -29 0 C-25.667 -0.199 -22.334 -0.382 -19 -0.562 C-17.598 -0.647 -17.598 -0.647 -16.168 -0.732 C-10.615 -1.024 -5.494 -0.875 0 0 Z " fill="#90BDE3" transform="translate(493,873)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C4.333 1.04 6.667 1.043 9 1 C9 0.67 9 0.34 9 0 C12.96 0 16.92 0 21 0 C21 0.66 21 1.32 21 2 C21.99 2 22.98 2 24 2 C25.429 10.571 25.429 10.571 23 14 C22.34 13.67 21.68 13.34 21 13 C21 12.01 21 11.02 21 10 C19.35 10.33 17.7 10.66 16 11 C16.008 12.105 16.008 12.105 16.016 13.231 C16.037 16.571 16.05 19.91 16.062 23.25 C16.071 24.409 16.079 25.568 16.088 26.762 C16.093 28.434 16.093 28.434 16.098 30.141 C16.106 31.68 16.106 31.68 16.114 33.251 C16 36 16 36 15 40 C11.7 40 8.4 40 5 40 C5 30.1 5 20.2 5 10 C2.03 10 -0.94 10 -4 10 C-4.33 7.69 -4.66 5.38 -5 3 C-3.35 2.67 -1.7 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8FBCE2" transform="translate(707,816)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-23.592 3.023 -47.183 3.041 -70.775 3.052 C-81.729 3.057 -92.683 3.064 -103.638 3.075 C-113.186 3.085 -122.735 3.092 -132.283 3.094 C-137.338 3.095 -142.393 3.098 -147.449 3.106 C-152.209 3.113 -156.97 3.115 -161.73 3.113 C-163.475 3.113 -165.219 3.115 -166.964 3.119 C-169.352 3.124 -171.74 3.123 -174.129 3.12 C-175.156 3.125 -175.156 3.125 -176.204 3.129 C-180.888 3.115 -185.371 2.65 -190 2 C-190 1.67 -190 1.34 -190 1 C-178.401 0.01 -166.822 -0.332 -155.188 -0.562 C-153.649 -0.594 -153.649 -0.594 -152.08 -0.626 C-101.395 -1.621 -50.677 -1.225 0 0 Z " fill="#527DA9" transform="translate(937,972)"/>
<path d="M0 0 C1.052 0.186 2.104 0.371 3.188 0.562 C6.595 0.954 8.731 0.769 12 0 C12.33 0.33 12.66 0.66 13 1 C13.33 1 13.66 1 14 1 C14.726 10.368 15.106 19.597 15 29 C24.9 28.505 24.9 28.505 35 28 C35.037 26.291 35.075 24.581 35.113 22.82 C35.179 20.589 35.245 18.357 35.312 16.125 C35.335 14.997 35.358 13.869 35.381 12.707 C35.416 11.631 35.452 10.554 35.488 9.445 C35.514 8.45 35.541 7.455 35.568 6.43 C35.71 5.628 35.853 4.826 36 4 C39.674 1.55 41.668 1.923 46 2 C46.99 1.67 47.98 1.34 49 1 C49 1.66 49 2.32 49 3 C50.65 2.34 52.3 1.68 54 1 C55.566 24.707 54.983 48.276 54 72 C48.06 72 42.12 72 36 72 C35.67 64.08 35.34 56.16 35 48 C28.13 46.626 22.028 47.493 15 48 C15 55.92 15 63.84 15 72 C8.73 72 2.46 72 -4 72 C-4.023 63.629 -4.041 55.257 -4.052 46.886 C-4.057 42.999 -4.064 39.111 -4.075 35.224 C-4.086 31.469 -4.092 27.715 -4.095 23.961 C-4.097 22.531 -4.1 21.102 -4.106 19.672 C-4.113 17.664 -4.113 15.656 -4.114 13.648 C-4.116 12.507 -4.118 11.365 -4.12 10.189 C-4.002 7.044 -3.589 4.088 -3 1 C-2.01 1.33 -1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z M-2 4 C-2 26.11 -2 48.22 -2 71 C2.95 71 7.9 71 13 71 C13.33 62.09 13.66 53.18 14 44 C21.92 43.67 29.84 43.34 38 43 C38 52.24 38 61.48 38 71 C42.95 71 47.9 71 53 71 C53 48.89 53 26.78 53 4 C48.05 4 43.1 4 38 4 C38 12.58 38 21.16 38 30 C30.08 29.67 22.16 29.34 14 29 C13.67 20.75 13.34 12.5 13 4 C8.05 4 3.1 4 -2 4 Z M22 45 C26 46 26 46 26 46 Z " fill="#597D9D" transform="translate(290,624)"/>
<path d="M0 0 C12.21 0 24.42 0 37 0 C38.258 3.692 39.516 7.384 40.812 11.188 C44.691 22.471 48.824 33.638 53.105 44.774 C54.413 48.179 55.709 51.588 57 55 C57.241 55.63 57.482 56.261 57.73 56.91 C58.46 58.827 59.177 60.749 59.891 62.672 C60.533 64.383 60.533 64.383 61.188 66.128 C62 69 62 69 61 72 C58.347 64.941 55.695 57.881 53.045 50.821 C51.814 47.541 50.582 44.261 49.349 40.982 C47.926 37.194 46.504 33.406 45.082 29.617 C44.648 28.465 44.215 27.312 43.768 26.124 C40.762 18.109 37.88 10.064 35 2 C23.78 1.67 12.56 1.34 1 1 C1 51.82 1 102.64 1 155 C10.57 155 20.14 155 30 155 C30 126.29 30 97.58 30 68 C30.66 68 31.32 68 32 68 C32.226 68.662 32.451 69.324 32.683 70.006 C36.785 82.019 40.939 94.003 45.438 105.875 C48.648 114.373 51.836 122.878 54.935 131.417 C55.614 133.281 56.302 135.141 56.99 137.001 C57.398 138.131 57.806 139.262 58.227 140.426 C58.593 141.43 58.96 142.435 59.338 143.47 C60 146 60 146 59 149 C54.772 139.049 51.153 128.902 47.625 118.688 C47.396 118.029 47.168 117.371 46.932 116.692 C46.277 114.8 45.627 112.906 44.977 111.012 C44.596 109.903 44.215 108.794 43.823 107.651 C43 105 43 105 43 103 C42.01 102.505 42.01 102.505 41 102 C40.105 98.923 39.461 95.819 38.781 92.688 C38.395 91.357 38.395 91.357 38 90 C37.01 89.505 37.01 89.505 36 89 C35.148 86.496 35.148 86.496 34.375 83.438 C34.115 82.426 33.854 81.414 33.586 80.371 C33.393 79.589 33.199 78.806 33 78 C32.67 78 32.34 78 32 78 C32 103.74 32 129.48 32 156 C29.446 157.277 27.622 157.129 24.766 157.133 C23.192 157.135 23.192 157.135 21.586 157.137 C20.485 157.133 19.384 157.129 18.25 157.125 C17.165 157.129 16.079 157.133 14.961 157.137 C13.379 157.135 13.379 157.135 11.766 157.133 C10.804 157.132 9.843 157.131 8.853 157.129 C6 157 6 157 0 156 C0 104.52 0 53.04 0 0 Z " fill="#803F32" transform="translate(626,270)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C7.338 1.927 7.675 2.854 8.023 3.809 C8.469 5.006 8.915 6.204 9.375 7.438 C9.816 8.632 10.257 9.827 10.711 11.059 C11.671 14.102 11.671 14.102 14 15 C14.289 14.092 14.577 13.185 14.875 12.25 C15.765 9.678 16.715 7.387 18 5 C17.34 5 16.68 5 16 5 C16 5.66 16 6.32 16 7 C13.69 6.67 11.38 6.34 9 6 C8.67 4.02 8.34 2.04 8 0 C14.27 0.33 20.54 0.66 27 1 C23.25 12.786 23.25 12.786 20.75 17.375 C18.979 20.681 18.621 23.208 19 27 C19.33 27 19.66 27 20 27 C20 29.31 20 31.62 20 34 C19.34 34 18.68 34 18 34 C17.34 35.32 16.68 36.64 16 38 C9.375 37.25 9.375 37.25 6 35 C6.33 34.34 6.66 33.68 7 33 C7.148 31.046 7.221 29.085 7.25 27.125 C7.276 26.076 7.302 25.026 7.328 23.945 C6.958 20.62 6.094 19.521 4 17 C3.34 15 3.34 15 2.938 13 C2.426 10.675 1.971 8.943 0.875 6.812 C-0.257 4.467 -0.143 2.569 0 0 Z M18 31 C19 33 19 33 19 33 Z " fill="#8CB8DE" transform="translate(674,874)"/>
<path d="M0 0 C1.518 0.042 3.036 0.09 4.554 0.143 C5.751 0.178 5.751 0.178 6.972 0.214 C9.529 0.291 12.087 0.375 14.644 0.459 C16.374 0.512 18.104 0.565 19.834 0.617 C24.083 0.747 28.333 0.883 32.582 1.022 C35.499 6.783 38.36 12.546 40.957 18.459 C41.742 20.221 41.742 20.221 42.543 22.018 C43.582 25.022 43.582 25.022 42.582 28.022 C41.122 24.793 39.664 21.564 38.207 18.334 C37.588 16.966 37.588 16.966 36.957 15.571 C36.557 14.684 36.158 13.798 35.746 12.885 C35.379 12.073 35.013 11.262 34.635 10.425 C33.564 7.98 32.573 5.5 31.582 3.022 C20.032 2.692 8.482 2.362 -3.418 2.022 C-3.418 54.162 -3.418 106.302 -3.418 160.022 C7.472 160.022 18.362 160.022 29.582 160.022 C29.582 126.032 29.582 92.042 29.582 57.022 C30.242 57.022 30.902 57.022 31.582 57.022 C32.949 60.141 34.298 63.268 35.644 66.397 C36.034 67.286 36.424 68.176 36.826 69.092 C37.19 69.943 37.554 70.794 37.929 71.67 C38.27 72.456 38.61 73.241 38.961 74.051 C39.582 76.022 39.582 76.022 38.582 78.022 C37.592 76.042 36.602 74.062 35.582 72.022 C34.922 72.022 34.262 72.022 33.582 72.022 C33.437 70.887 33.293 69.753 33.144 68.584 C32.97 65.214 32.97 65.214 31.582 64.022 C31.558 71.187 31.541 78.352 31.53 85.517 C31.525 88.847 31.517 92.177 31.506 95.506 C31.461 109.558 31.452 123.572 32.317 137.602 C32.595 142.517 32.493 147.139 31.816 152.02 C31.504 154.348 31.504 154.348 31.582 158.022 C31.582 159.012 31.582 160.002 31.582 161.022 C29.577 163.027 25.582 162.278 22.828 162.338 C21.665 162.369 21.665 162.369 20.478 162.401 C17.992 162.467 15.506 162.526 13.019 162.584 C11.338 162.627 9.656 162.671 7.974 162.715 C3.844 162.822 -0.287 162.924 -4.418 163.022 C-5.492 156.914 -5.56 150.958 -5.548 144.767 C-5.55 143.094 -5.55 143.094 -5.553 141.387 C-5.557 137.698 -5.554 134.008 -5.551 130.319 C-5.552 127.722 -5.553 125.125 -5.554 122.529 C-5.556 117.068 -5.553 111.607 -5.549 106.147 C-5.544 100.614 -5.544 95.08 -5.549 89.547 C-5.594 36.849 -5.594 36.849 -4.973 14.725 C-4.953 13.996 -4.934 13.267 -4.914 12.517 C-4.567 0.029 -4.567 0.029 0 0 Z " fill="#7C3E31" transform="translate(442.418212890625,76.978271484375)"/>
<path d="M0 0 C0.98 -0.017 1.959 -0.034 2.969 -0.051 C4.222 0.129 4.222 0.129 5.5 0.312 C6.16 1.303 6.82 2.293 7.5 3.312 C6.84 3.312 6.18 3.312 5.5 3.312 C5.665 3.931 5.83 4.55 6 5.188 C6.955 11.154 7.066 17.442 5.5 23.312 C6.16 23.643 6.82 23.972 7.5 24.312 C7.17 26.622 6.84 28.933 6.5 31.312 C7.16 31.643 7.82 31.972 8.5 32.312 C12.955 19.442 12.955 19.442 17.5 6.312 C18.842 10.339 17.776 12.173 16.41 16.168 C16.187 16.83 15.963 17.491 15.733 18.173 C15.018 20.285 14.29 22.392 13.562 24.5 C13.075 25.931 12.589 27.363 12.104 28.795 C10.912 32.304 9.709 35.809 8.5 39.312 C11.47 38.817 11.47 38.817 14.5 38.312 C15.16 36.332 15.82 34.352 16.5 32.312 C17.582 35.678 17.425 36.611 16.5 40.312 C8.58 40.312 0.66 40.312 -7.5 40.312 C-7.83 38.332 -8.16 36.352 -8.5 34.312 C-7.51 33.817 -7.51 33.817 -6.5 33.312 C-6.5 25.062 -6.5 16.812 -6.5 8.312 C-6.83 8.312 -7.16 8.312 -7.5 8.312 C-7.625 5.438 -7.625 5.438 -7.5 2.312 C-4.888 -0.299 -3.628 0.014 0 0 Z M3.5 1.312 C3.5 11.872 3.5 22.433 3.5 33.312 C3.83 33.312 4.16 33.312 4.5 33.312 C5.503 26.185 5.456 19.427 5.062 12.25 C5.01 11.194 4.958 10.137 4.904 9.049 C4.776 6.47 4.641 3.891 4.5 1.312 C4.17 1.312 3.84 1.312 3.5 1.312 Z M3.5 34.312 C4.5 36.312 4.5 36.312 4.5 36.312 Z " fill="#7AA2C4" transform="translate(431.5,815.6875)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.02 0.594 6.04 1.189 6.06 1.801 C6.156 4.534 6.266 7.267 6.375 10 C6.406 10.935 6.437 11.869 6.469 12.832 C6.706 18.486 7.262 23.598 9 29 C11.829 30.414 13.874 30.319 17 30 C20.165 26.872 20.405 24.264 20.512 19.922 C20.547 18.762 20.583 17.602 20.619 16.406 C20.642 15.2 20.664 13.993 20.688 12.75 C20.722 11.528 20.756 10.306 20.791 9.047 C20.873 6.031 20.943 3.016 21 0 C22.98 0 24.96 0 27 0 C27.174 4.323 27.3 8.644 27.385 12.97 C27.42 14.44 27.467 15.909 27.528 17.378 C28.02 29.578 28.02 29.578 25.516 33.069 C20.687 36.599 15.828 37.478 10 37 C6.13 35.715 2.805 34.598 0.906 30.828 C-0.15 27.533 -0.227 24.844 -0.195 21.387 C-0.189 20.14 -0.182 18.892 -0.176 17.607 C-0.159 16.314 -0.142 15.02 -0.125 13.688 C-0.116 12.375 -0.107 11.062 -0.098 9.709 C-0.074 6.472 -0.041 3.236 0 0 Z " fill="#8EBAE0" transform="translate(252,819)"/>
<path d="M0 0 C6.93 0 13.86 0 21 0 C21 1.98 21 3.96 21 6 C16.05 6 11.1 6 6 6 C6 9.3 6 12.6 6 16 C9.96 16 13.92 16 18 16 C18 17.98 18 19.96 18 22 C14.37 22 10.74 22 7 22 C7 24.97 7 27.94 7 31 C11.95 31 16.9 31 22 31 C22 32.65 22 34.3 22 36 C14.74 36 7.48 36 0 36 C0 24.12 0 12.24 0 0 Z " fill="#8FBCE2" transform="translate(600,819)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.977 0.808 1.954 1.616 1.93 2.449 C1.912 3.518 1.894 4.586 1.875 5.688 C1.852 6.743 1.829 7.799 1.805 8.887 C2.003 12.056 2.536 14.194 4 17 C6.041 17.887 6.041 17.887 8 18 C8.33 16.35 8.66 14.7 9 13 C12.161 14.37 12.993 14.989 15 18 C12.872 20.341 10.884 21.707 8 23 C8.33 23.66 8.66 24.32 9 25 C8.67 25.99 8.34 26.98 8 28 C8.99 27.67 9.98 27.34 11 27 C12.32 28.65 13.64 30.3 15 32 C14.01 32 13.02 32 12 32 C11.67 32.66 11.34 33.32 11 34 C11.495 34.495 11.495 34.495 12 35 C12.868 36.346 13.72 37.701 14.562 39.062 C15.245 40.163 15.245 40.163 15.941 41.285 C16.291 41.851 16.64 42.417 17 43 C16.01 43.495 16.01 43.495 15 44 C12 42 12 42 11 39 C9.14 37.767 9.14 37.767 6.938 36.812 C6.204 36.468 5.471 36.124 4.715 35.77 C3.866 35.389 3.866 35.389 3 35 C2.951 36.109 2.902 37.217 2.852 38.359 C2.777 39.823 2.701 41.286 2.625 42.75 C2.594 43.48 2.563 44.209 2.531 44.961 C2.325 48.739 2.085 51.779 0 55 C-1.249 47.349 -1.091 39.726 -1 32 C-1.99 32 -2.98 32 -4 32 C-4 32.66 -4 33.32 -4 34 C-6.082 35.98 -6.082 35.98 -8.812 38.188 C-9.706 38.924 -10.599 39.66 -11.52 40.418 C-12.747 41.201 -12.747 41.201 -14 42 C-14.99 41.67 -15.98 41.34 -17 41 C-16.048 39.994 -15.088 38.995 -14.125 38 C-13.591 37.443 -13.058 36.886 -12.508 36.312 C-11 35 -11 35 -9 35 C-6.753 30.805 -6 27.792 -6 23 C-6.66 22.34 -7.32 21.68 -8 21 C-9.503 19.263 -10.983 17.509 -12.465 15.754 C-13.973 13.975 -13.973 13.975 -15.691 12.402 C-17 11 -17 11 -17 9 C-13.319 10.536 -11.012 12.782 -8.25 15.625 C-7.451 16.442 -6.652 17.26 -5.828 18.102 C-4.923 19.041 -4.923 19.041 -4 20 C-3.01 19.67 -2.02 19.34 -1 19 C-0.67 12.73 -0.34 6.46 0 0 Z M8 32 C9 34 9 34 9 34 Z " fill="#5782B1" transform="translate(212,488)"/>
<path d="M0 0 C5.7 4.433 7.345 10.12 9 17 C9.474 21.54 9.474 26.002 9.375 30.562 C9.374 31.723 9.372 32.884 9.371 34.08 C9.282 42.577 9.282 42.577 7 46 C4.388 46.282 2.256 46.057 -0.305 45.488 C-3.201 44.964 -5.872 44.902 -8.812 44.938 C-9.788 44.947 -10.764 44.956 -11.77 44.965 C-12.506 44.976 -13.242 44.988 -14 45 C-13.67 44.34 -13.34 43.68 -13 43 C-14.485 43.495 -14.485 43.495 -16 44 C-15.67 44.66 -15.34 45.32 -15 46 C-16.32 46 -17.64 46 -19 46 C-19.33 44.68 -19.66 43.36 -20 42 C-21.32 42 -22.64 42 -24 42 C-23.988 41.371 -23.977 40.742 -23.965 40.094 C-23.907 34.623 -24.116 29.404 -25 24 C-26.722 22.278 -29.088 22.63 -31.438 22.438 C-32.426 22.354 -33.415 22.27 -34.434 22.184 C-35.953 22.075 -37.477 22 -39 22 C-42.582 27.585 -42.356 32.483 -42.188 38.875 C-42.174 39.85 -42.16 40.824 -42.146 41.828 C-42.111 44.219 -42.062 46.609 -42 49 C-41.67 49 -41.34 49 -41 49 C-41 50.65 -41 52.3 -41 54 C-41.66 54 -42.32 54 -43 54 C-42.892 61.459 -42.78 68.917 -42.661 76.376 C-42.606 79.84 -42.552 83.303 -42.503 86.767 C-42.446 90.754 -42.381 94.74 -42.316 98.727 C-42.291 100.583 -42.291 100.583 -42.266 102.477 C-42.246 103.638 -42.226 104.799 -42.205 105.995 C-42.19 107.012 -42.174 108.029 -42.158 109.077 C-41.994 112.112 -41.554 115.013 -41 118 C-43 117 -43 117 -44.109 113.927 C-45.124 110.02 -45.282 106.193 -45.259 102.172 C-45.262 101.423 -45.266 100.674 -45.269 99.903 C-45.278 97.447 -45.272 94.991 -45.266 92.535 C-45.267 90.816 -45.269 89.096 -45.271 87.376 C-45.274 83.78 -45.27 80.183 -45.261 76.586 C-45.249 71.992 -45.256 67.398 -45.268 62.804 C-45.275 59.255 -45.273 55.706 -45.268 52.157 C-45.266 50.463 -45.268 48.77 -45.273 47.077 C-45.278 44.704 -45.27 42.331 -45.259 39.957 C-45.263 39.268 -45.267 38.578 -45.271 37.868 C-45.209 30.648 -44.179 24.341 -39 19 C-35.775 17.388 -32.496 17.531 -29 18 C-26.181 19.094 -23.596 20.454 -21 22 C-21.33 23.65 -21.66 25.3 -22 27 C-22 31.62 -22 36.24 -22 41 C-12.1 41 -2.2 41 8 41 C7.574 20.592 7.574 20.592 0 2 C0 1.34 0 0.68 0 0 Z M-8 42 C-8 42.33 -8 42.66 -8 43 C-5.36 43 -2.72 43 0 43 C0 42.67 0 42.34 0 42 C-2.64 42 -5.28 42 -8 42 Z " fill="#834435" transform="translate(296,90)"/>
<path d="M0 0 C11.88 0 23.76 0 36 0 C39 8 39 8 39 11 C39.66 11.33 40.32 11.66 41 12 C41.711 13.898 41.711 13.898 42.375 16.375 C43.74 21.264 45.455 25.937 47.328 30.652 C48 33 48 33 47 36 C42.71 24.78 38.42 13.56 34 2 C23.77 1.67 13.54 1.34 3 1 C3 53.14 3 105.28 3 159 C12.57 159 22.14 159 32 159 C31.99 156.587 31.979 154.174 31.968 151.688 C31.935 143.729 31.913 135.77 31.896 127.812 C31.886 122.984 31.872 118.158 31.849 113.331 C31.827 108.675 31.815 104.02 31.81 99.364 C31.807 97.585 31.799 95.806 31.789 94.027 C31.774 91.542 31.772 89.056 31.773 86.571 C31.766 85.83 31.759 85.089 31.751 84.326 C31.772 79.228 31.772 79.228 34 77 C35.125 81.75 35.125 81.75 34 84 C34.247 84.959 34.247 84.959 34.5 85.938 C35 88 35 88 34 91 C33.916 93.049 33.893 95.101 33.902 97.152 C33.906 98.42 33.909 99.688 33.912 100.995 C33.919 102.011 33.919 102.011 33.925 103.047 C33.938 105.17 33.944 107.293 33.949 109.415 C33.962 114.688 33.987 119.96 34.01 125.232 C34.03 129.687 34.046 134.142 34.056 138.598 C34.062 140.687 34.075 142.777 34.088 144.866 C34.091 146.137 34.094 147.409 34.098 148.719 C34.103 149.838 34.108 150.957 34.114 152.109 C34.016 154.595 33.727 156.635 33 159 C33.99 159.33 34.98 159.66 36 160 C32.16 161.785 29.151 162.247 24.93 162.23 C23.786 162.229 22.643 162.227 21.465 162.225 C20.28 162.212 19.096 162.2 17.875 162.188 C16.683 162.187 15.49 162.186 14.262 162.186 C5.422 162.141 5.422 162.141 2 161 C2 111.17 2 61.34 2 10 C1.34 10 0.68 10 0 10 C0.293 7.982 0.586 5.964 0.879 3.945 C1.168 1.919 1.168 1.919 0 0 Z " fill="#733628" transform="translate(616,78)"/>
<path d="M0 0 C2.78 7.84 5.432 15.583 5 24 C4.67 24 4.34 24 4 24 C3.353 21.271 2.707 18.542 2.062 15.812 C1.88 15.044 1.698 14.275 1.51 13.482 C1.333 12.732 1.155 11.981 0.973 11.207 C0.729 10.178 0.729 10.178 0.481 9.128 C-0.054 6.761 -0.524 4.38 -1 2 C-7.27 2 -13.54 2 -20 2 C-20 13.22 -20 24.44 -20 36 C-7.13 36 5.74 36 19 36 C19 24.78 19 13.56 19 2 C17.02 2 15.04 2 13 2 C13 11.57 13 21.14 13 31 C11.35 31 9.7 31 8 31 C8.99 30.67 9.98 30.34 11 30 C10.814 28.886 10.629 27.773 10.438 26.625 C10 23 10 23 11 21 C10.01 20.67 9.02 20.34 8 20 C9.485 15.545 9.485 15.545 11 11 C10.34 10.67 9.68 10.34 9 10 C9.66 10 10.32 10 11 10 C11 8.68 11 7.36 11 6 C10.34 6 9.68 6 9 6 C9 4 9 4 10 3 C9.505 1.02 9.505 1.02 9 -1 C12.96 -0.67 16.92 -0.34 21 0 C21.015 0.959 21.029 1.918 21.044 2.906 C21.102 6.458 21.179 10.01 21.262 13.562 C21.296 15.1 21.324 16.639 21.346 18.177 C21.38 20.387 21.432 22.595 21.488 24.805 C21.495 25.494 21.501 26.184 21.508 26.895 C21.54 29.047 21.54 29.047 22 32 C22.99 32.66 23.98 33.32 25 34 C25.188 36.625 25.188 36.625 25 39 C24.01 39 23.02 39 22 39 C21.67 38.34 21.34 37.68 21 37 C20.67 37.33 20.34 37.66 20 38 C18.619 38.075 17.234 38.083 15.852 38.062 C15.011 38.055 14.171 38.047 13.305 38.039 C11.526 38.013 9.747 37.987 7.969 37.961 C6.704 37.949 6.704 37.949 5.414 37.938 C4.64 37.926 3.866 37.914 3.069 37.902 C0.801 37.916 0.801 37.916 -2 39 C-4.638 39.122 -7.237 39.185 -9.875 39.188 C-10.583 39.2 -11.29 39.212 -12.02 39.225 C-16.134 39.236 -19.302 38.835 -23 37 C-23.043 35.334 -23.041 33.666 -23 32 C-22.505 31.505 -22.505 31.505 -22 31 C-21.9 29.17 -21.869 27.336 -21.867 25.504 C-21.866 24.388 -21.865 23.273 -21.863 22.123 C-21.867 20.948 -21.871 19.773 -21.875 18.562 C-21.871 17.391 -21.867 16.22 -21.863 15.014 C-21.865 13.897 -21.866 12.78 -21.867 11.629 C-21.868 10.598 -21.869 9.568 -21.871 8.506 C-21.832 6.002 -21.832 6.002 -23 4 C-22.67 3.67 -22.34 3.34 -22 3 C-22 2.01 -22 1.02 -22 0 C-14.468 -0.757 -7.497 -1.144 0 0 Z " fill="#7EA6CA" transform="translate(351,873)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C3.814 3.722 3.629 4.444 3.438 5.188 C2.979 8.138 3.282 10.129 4 13 C4 13.33 4 13.66 4 14 C6.416 14.25 6.416 14.25 9 14 C9.66 13.01 10.32 12.02 11 11 C11.33 11.33 11.66 11.66 12 12 C12.33 10.68 12.66 9.36 13 8 C12.34 8 11.68 8 11 8 C11 7.01 11 6.02 11 5 C11.66 5 12.32 5 13 5 C12.67 4.01 12.34 3.02 12 2 C15.634 -0.326 18.771 -0.163 23 0 C23.66 1.32 24.32 2.64 25 4 C24.34 4 23.68 4 23 4 C22.805 6.583 22.619 9.166 22.438 11.75 C22.381 12.48 22.325 13.209 22.268 13.961 C22.022 17.567 22.064 20.531 23 24 C23.065 25.582 23.086 27.167 23.062 28.75 C23.053 29.549 23.044 30.348 23.035 31.172 C23.024 31.775 23.012 32.378 23 33 C23.33 33 23.66 33 24 33 C24 34.98 24 36.96 24 39 C23.01 39 22.02 39 21 39 C18.326 39.286 15.666 39.65 13 40 C11.837 36.45 12.076 33.442 12.438 29.75 C12.539 28.672 12.641 27.595 12.746 26.484 C12.83 25.665 12.914 24.845 13 24 C8.05 24.495 8.05 24.495 3 25 C3.041 26.382 3.082 27.764 3.125 29.188 C3.119 32.885 2.932 35.746 1 39 C-2.766 40.255 -4.372 39.371 -8 38 C-9.333 18.032 -9.333 18.032 -8 9 C-8.66 9 -9.32 9 -10 9 C-8.125 2.125 -8.125 2.125 -7 1 C-4.672 0.632 -2.338 0.298 0 0 Z M-6 2 C-6 13.55 -6 25.1 -6 37 C-4.02 37 -2.04 37 0 37 C0 32.05 0 27.1 0 22 C4.62 22 9.24 22 14 22 C14 26.95 14 31.9 14 37 C15.98 37 17.96 37 20 37 C20 25.78 20 14.56 20 3 C18.02 3 16.04 3 14 3 C13.67 7.29 13.34 11.58 13 16 C6.565 16.495 6.565 16.495 0 17 C0 12.05 0 7.1 0 2 C-1.98 2 -3.96 2 -6 2 Z M22 36 C23 38 23 38 23 38 Z " fill="#7BA3C7" transform="translate(504,872)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.112 4.438 3.115 8.77 3.062 13.312 C3.058 14.053 3.053 14.794 3.049 15.557 C3.037 17.371 3.019 19.186 3 21 C4.98 21.66 6.96 22.32 9 23 C9.33 22.01 9.66 21.02 10 20 C13.057 16.519 15.562 14.395 20 13 C18.043 16.522 16.049 19.344 13 22 C12.34 22 11.68 22 11 22 C9.794 24.332 8.835 26.496 8 29 C8.99 29.33 9.98 29.66 11 30 C11.33 30.66 11.66 31.32 12 32 C14.025 32.652 14.025 32.652 16 33 C16 33.66 16 34.32 16 35 C17.626 36.382 19.293 37.719 21 39 C19.258 39.652 19.258 39.652 17 40 C14.93 39.004 14.93 39.004 12.875 37.562 C10.182 35.528 10.182 35.528 7 35 C7 35.66 7 36.32 7 37 C6.34 37 5.68 37 5 37 C5 39.64 5 42.28 5 45 C4.34 45 3.68 45 3 45 C2.34 42.36 1.68 39.72 1 37 C-2.931 37.575 -2.931 37.575 -6 40 C-6.99 39.67 -7.98 39.34 -9 39 C-8.01 38.134 -7.02 37.267 -6 36.375 C-3.824 34.152 -3.035 33.117 -2.125 30.062 C-3.315 25.897 -5.644 24.321 -8.965 21.668 C-11.368 19.698 -13.141 17.479 -15 15 C-15.99 14.01 -16.98 13.02 -18 12 C-15.03 12 -12.06 12 -9 12 C-9 13.32 -9 14.64 -9 16 C-8.34 16.33 -7.68 16.66 -7 17 C-7 17.66 -7 18.32 -7 19 C-5.374 20.382 -3.707 21.719 -2 23 C-1.34 22.67 -0.68 22.34 0 22 C0 14.74 0 7.48 0 0 Z " fill="#517198" transform="translate(921,147)"/>
<path d="M0 0 C1.656 0.007 1.656 0.007 3.346 0.014 C6.043 0.025 8.74 0.042 11.438 0.062 C12.817 2.821 12.563 5.034 12.564 8.123 C12.567 9.415 12.57 10.708 12.573 12.04 C12.571 13.481 12.569 14.922 12.567 16.363 C12.568 17.874 12.57 19.385 12.572 20.896 C12.577 25.007 12.575 29.119 12.572 33.231 C12.569 37.529 12.572 41.827 12.573 46.126 C12.575 53.347 12.573 60.568 12.568 67.79 C12.562 76.141 12.564 84.492 12.57 92.843 C12.574 100.008 12.575 107.172 12.572 114.336 C12.571 118.618 12.57 122.899 12.574 127.181 C12.577 131.207 12.575 135.234 12.569 139.261 C12.568 140.739 12.568 142.218 12.57 143.697 C12.573 145.714 12.569 147.731 12.564 149.749 C12.564 150.878 12.564 152.007 12.563 153.171 C12.455 155.651 12.161 157.701 11.438 160.062 C12.097 160.393 12.758 160.722 13.438 161.062 C3.892 165.835 -7.891 162.062 -18.562 162.062 C-18.562 127.412 -18.562 92.762 -18.562 57.062 C-18.232 57.062 -17.903 57.062 -17.562 57.062 C-17.562 91.053 -17.562 125.043 -17.562 160.062 C-8.322 160.062 0.918 160.062 10.438 160.062 C10.438 107.923 10.438 55.782 10.438 2.062 C-5.403 2.558 -5.403 2.558 -21.562 3.062 C-21.562 2.403 -21.562 1.743 -21.562 1.062 C-14.339 0.098 -7.282 -0.057 0 0 Z " fill="#663229" transform="translate(533.5625,76.9375)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 0.33 2.66 0.66 3 1 C3.639 0.814 4.279 0.629 4.938 0.438 C5.618 0.293 6.299 0.149 7 0 C9.709 2.709 9.293 4.244 9.379 8.031 C9.413 9.234 9.447 10.437 9.482 11.676 C9.509 12.938 9.535 14.2 9.562 15.5 C9.622 17.99 9.686 20.479 9.754 22.969 C9.778 24.077 9.802 25.185 9.826 26.326 C9.824 28.972 9.824 28.972 11 31 C13.425 31.835 13.425 31.835 16 32 C16.66 31.34 17.32 30.68 18 30 C18.33 30 18.66 30 19 30 C19.682 21.437 19.682 21.437 20.254 12.867 C20.295 12.191 20.337 11.515 20.379 10.819 C20.496 8.886 20.607 6.953 20.719 5.02 C21 2 21 2 22 0 C24.31 0 26.62 0 29 0 C29 0.66 29 1.32 29 2 C29.66 2.33 30.32 2.66 31 3 C30.67 12.57 30.34 22.14 30 32 C29.34 32 28.68 32 28 32 C28 22.43 28 12.86 28 3 C26.02 3 24.04 3 22 3 C22.021 3.761 22.042 4.523 22.063 5.307 C22.147 8.767 22.199 12.227 22.25 15.688 C22.284 16.886 22.317 18.084 22.352 19.318 C22.364 20.475 22.377 21.632 22.391 22.824 C22.412 23.887 22.433 24.95 22.454 26.046 C21.886 29.742 20.63 31.387 18 34 C15 34.562 15 34.562 12 34 C9.253 32.07 8.195 30.801 7.398 27.531 C6.855 22.718 6.895 17.964 6.938 13.125 C6.942 12.15 6.947 11.176 6.951 10.172 C6.963 7.781 6.979 5.391 7 3 C5.02 3 3.04 3 1 3 C1.11 7.563 1.241 12.125 1.375 16.688 C1.406 17.981 1.437 19.275 1.469 20.607 C1.507 21.855 1.546 23.102 1.586 24.387 C1.617 25.534 1.649 26.68 1.681 27.862 C2 31.003 2.517 33.22 4 36 C8.739 38.695 12.586 39.591 18 39 C21.3 37.604 24.021 35.986 27 34 C25.54 37.848 23.661 39.212 20 41 C14.442 42.953 9.461 40.93 4.188 38.938 C-0.159 35.088 -1.103 28.564 -2 23 C-4.68 25.68 -5.165 28.424 -6.219 32.004 C-7 34 -7 34 -9 35 C-8.67 34.113 -8.34 33.226 -8 32.312 C-5.851 24.845 -6.583 16.541 -8 9 C-6 10 -6 10 -5.367 11.848 C-5.205 12.579 -5.042 13.309 -4.875 14.062 C-4.707 14.796 -4.54 15.529 -4.367 16.285 C-4.246 16.851 -4.125 17.417 -4 18 C-3.67 17.34 -3.34 16.68 -3 16 C-2.34 16 -1.68 16 -1 16 C-2.837 13.033 -2.837 13.033 -5 11 C-4.34 11 -3.68 11 -3 11 C-1.989 7.951 -1.989 7.951 -3 5 C-2.34 5 -1.68 5 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z M-2 9 C-1 11 -1 11 -1 11 Z " fill="#749BBE" transform="translate(251,816)"/>
<path d="M0 0 C1.423 0.021 2.846 0.041 4.312 0.062 C5.962 5.013 7.612 9.962 9.312 15.062 C10.303 15.062 11.293 15.062 12.312 15.062 C12.985 13.294 13.65 11.523 14.312 9.75 C14.869 8.271 14.869 8.271 15.438 6.762 C16.467 4.06 16.467 4.06 16.312 1.062 C19.942 0.732 23.572 0.403 27.312 0.062 C27.312 1.053 27.312 2.043 27.312 3.062 C26.653 3.393 25.992 3.722 25.312 4.062 C28.44 3.589 30.79 2.841 33.688 1.5 C37.726 -0.101 40.014 -0.541 44.312 0.062 C48.081 1.913 51.344 4.094 54.312 7.062 C53.982 8.053 53.653 9.043 53.312 10.062 C52.735 9.279 52.158 8.495 51.562 7.688 C48.889 4.569 47.017 3.304 42.945 2.727 C37.237 2.638 33.688 3.007 29.312 7.062 C25.237 13.77 25.539 20.431 26.312 28.062 C27.333 31.177 28.507 33.298 30.312 36.062 C27.312 35.062 27.312 35.062 25.625 32 C25.192 31.031 24.759 30.061 24.312 29.062 C23.982 32.033 23.653 35.003 23.312 38.062 C22.982 38.062 22.653 38.062 22.312 38.062 C22.179 26.353 22.547 14.747 23.312 3.062 C21.663 3.062 20.013 3.062 18.312 3.062 C18.211 3.716 18.109 4.37 18.004 5.043 C17.105 8.971 15.696 12.62 14.25 16.375 C13.968 17.116 13.687 17.856 13.396 18.619 C12.705 20.435 12.009 22.249 11.312 24.062 C8.28 20.595 6.994 16.755 5.625 12.438 C5.4 11.757 5.175 11.076 4.943 10.375 C3.312 5.358 3.312 5.358 3.312 3.062 C0.342 3.062 -2.628 3.062 -5.688 3.062 C-5.713 3.925 -5.739 4.787 -5.766 5.676 C-6.265 16.433 -8.263 26.585 -10.688 37.062 C-8.378 37.062 -6.067 37.062 -3.688 37.062 C-3.357 32.442 -3.028 27.822 -2.688 23.062 C-2.028 23.722 -1.367 24.383 -0.688 25.062 C-0.859 28.066 -0.859 28.066 -1.438 31.625 C-1.618 32.802 -1.798 33.979 -1.984 35.191 C-2.216 36.139 -2.448 37.086 -2.688 38.062 C-5.701 39.569 -8.37 39.24 -11.688 39.062 C-13.688 37.062 -13.688 37.062 -13.812 34.438 C-13.751 33.262 -13.751 33.262 -13.688 32.062 C-12.697 32.062 -11.707 32.062 -10.688 32.062 C-10.688 26.122 -10.688 20.183 -10.688 14.062 C-10.028 13.732 -9.367 13.403 -8.688 13.062 C-8.219 10.247 -8.219 10.247 -8.062 7 C-7.95 5.343 -7.95 5.343 -7.836 3.652 C-7.787 2.798 -7.738 1.943 -7.688 1.062 C-4.805 0.102 -2.989 -0.043 0 0 Z M26.312 6.062 C25.817 8.043 25.817 8.043 25.312 10.062 C25.972 9.732 26.633 9.403 27.312 9.062 C26.982 8.072 26.653 7.082 26.312 6.062 Z M-9.688 17.062 C-8.688 20.062 -8.688 20.062 -8.688 20.062 Z " fill="#759EC0" transform="translate(586.6875,871.9375)"/>
<path d="M0 0 C10.56 0 21.12 0 32 0 C33.405 2.811 33.123 5.132 33.12 8.277 C33.121 8.928 33.122 9.579 33.123 10.249 C33.124 12.441 33.119 14.632 33.114 16.823 C33.113 18.39 33.113 19.956 33.114 21.523 C33.114 25.785 33.108 30.047 33.101 34.31 C33.095 38.76 33.095 43.211 33.093 47.662 C33.09 56.095 33.082 64.527 33.072 72.96 C33.061 82.558 33.055 92.157 33.05 101.755 C33.04 121.503 33.022 141.252 33 161 C22.11 161 11.22 161 0 161 C-1.856 156.072 -3.709 151.143 -5.554 146.211 C-6.183 144.533 -6.813 142.856 -7.446 141.18 C-8.354 138.771 -9.256 136.361 -10.156 133.949 C-10.441 133.199 -10.725 132.449 -11.018 131.676 C-12.142 128.65 -13 126.256 -13 123 C-13.66 122.67 -14.32 122.34 -15 122 C-16.485 118.812 -17.541 115.448 -18.688 112.125 C-19.187 110.739 -19.187 110.739 -19.697 109.324 C-20.007 108.436 -20.317 107.548 -20.637 106.633 C-20.922 105.817 -21.208 105.001 -21.503 104.161 C-22 102 -22 102 -21 99 C-13.321 118.556 -5.983 138.182 1 158 C10.9 158.33 20.8 158.66 31 159 C31 106.86 31 54.72 31 1 C20.77 1 10.54 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5A2923" transform="translate(679,78)"/>
<path d="M0 0 C14.421 -0.047 28.842 -0.082 43.262 -0.104 C49.958 -0.114 56.654 -0.128 63.349 -0.151 C69.807 -0.173 76.265 -0.185 82.723 -0.19 C85.191 -0.193 87.658 -0.201 90.126 -0.211 C93.574 -0.226 97.022 -0.228 100.47 -0.227 C102.011 -0.238 102.011 -0.238 103.583 -0.249 C104.521 -0.246 105.459 -0.243 106.425 -0.241 C107.241 -0.242 108.057 -0.244 108.897 -0.246 C111 0 111 0 114 2 C111.159 2.947 109.372 3.135 106.425 3.158 C105.018 3.173 105.018 3.173 103.583 3.188 C102.555 3.194 101.528 3.199 100.47 3.205 C99.387 3.215 98.304 3.225 97.188 3.235 C93.601 3.267 90.013 3.292 86.426 3.316 C83.941 3.337 81.457 3.358 78.973 3.379 C73.097 3.429 67.222 3.474 61.346 3.517 C54.001 3.572 46.656 3.633 39.311 3.693 C26.207 3.801 13.104 3.903 0 4 C0 2.68 0 1.36 0 0 Z " fill="#4D77A4" transform="translate(79,972)"/>
<path d="M0 0 C0.76 -0.004 1.52 -0.008 2.303 -0.012 C6.231 -0.005 9.852 0.161 13.688 1.125 C13.688 3.765 13.688 6.405 13.688 9.125 C12.697 9.455 11.707 9.785 10.688 10.125 C9.479 12.125 9.479 12.125 8.688 14.125 C7.367 12.805 6.048 11.485 4.688 10.125 C1.06 11.991 1.06 11.991 -1.312 15.125 C2.317 15.125 5.947 15.125 9.688 15.125 C11.222 18.194 10.237 20.826 9.688 24.125 C6.717 24.125 3.747 24.125 0.688 24.125 C2.922 27.476 3.95 28.941 7.938 29.938 C9.299 30.03 9.299 30.03 10.688 30.125 C10.688 29.465 10.688 28.805 10.688 28.125 C13.556 29.559 14.852 31.52 16.688 34.125 C17.438 36.938 17.438 36.938 17.688 39.125 C13.667 40.465 9.941 40.109 5.773 39.871 C4.848 39.821 3.922 39.772 2.969 39.721 C1.023 39.615 -0.922 39.505 -2.867 39.393 C-3.79 39.344 -4.713 39.296 -5.664 39.246 C-6.508 39.199 -7.352 39.152 -8.221 39.103 C-10.373 38.973 -10.373 38.973 -12.312 40.125 C-13.609 35.514 -13.024 31.787 -12.312 27.125 C-11.982 27.125 -11.653 27.125 -11.312 27.125 C-10.982 18.545 -10.653 9.965 -10.312 1.125 C-7.077 -0.493 -3.55 -0.018 0 0 Z M5.238 2.344 C4.092 2.405 4.092 2.405 2.923 2.467 C1.958 2.519 0.994 2.571 0 2.625 C-4.61 2.872 -4.61 2.872 -9.312 3.125 C-9.312 14.345 -9.312 25.565 -9.312 37.125 C-2.053 37.125 5.207 37.125 12.688 37.125 C12.688 35.475 12.688 33.825 12.688 32.125 C7.737 32.125 2.788 32.125 -2.312 32.125 C-2.312 28.825 -2.312 25.525 -2.312 22.125 C1.317 22.125 4.947 22.125 8.688 22.125 C8.688 20.475 8.688 18.825 8.688 17.125 C5.058 17.125 1.428 17.125 -2.312 17.125 C-2.312 14.155 -2.312 11.185 -2.312 8.125 C2.638 8.125 7.587 8.125 12.688 8.125 C12.688 6.145 12.688 4.165 12.688 2.125 C10.189 2.125 7.732 2.21 5.238 2.344 Z M-1.312 24.125 C-0.312 28.125 -0.312 28.125 -0.312 28.125 Z M13.688 34.125 C14.688 37.125 14.688 37.125 14.688 37.125 Z " fill="#7CA5C9" transform="translate(542.3125,871.875)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C9.92 2 17.84 2 26 2 C26 4.64 26 7.28 26 10 C23.03 10.868 20.462 11.083 17.375 11 C14.057 10.873 14.057 10.873 11 12 C11.33 12.99 11.66 13.98 12 15 C13.65 15 15.3 15 17 15 C17 15.66 17 16.32 17 17 C18.224 17.075 18.224 17.075 20 16 C21.32 16.99 22.64 17.98 24 19 C23.67 19.66 23.34 20.32 23 21 C22.835 21.825 22.67 22.65 22.5 23.5 C22 26 22 26 21 28 C20.01 28 19.02 28 18 28 C17.67 28.66 17.34 29.32 17 30 C17 28.68 17 27.36 17 26 C15.68 26 14.36 26 13 26 C13 27.65 13 29.3 13 31 C13.855 31.061 14.709 31.121 15.59 31.184 C16.695 31.267 17.799 31.351 18.938 31.438 C20.591 31.559 20.591 31.559 22.277 31.684 C25 32 25 32 26 33 C26.041 35.333 26.042 37.667 26 40 C23.401 41.299 21.673 41.022 18.773 40.879 C17.845 40.836 16.916 40.793 15.958 40.748 C14.776 40.687 13.593 40.626 12.375 40.562 C8.621 40.377 4.867 40.191 1 40 C0.67 26.8 0.34 13.6 0 0 Z M3 3 C3 14.88 3 26.76 3 39 C10.26 39 17.52 39 25 39 C24.67 37.35 24.34 35.7 24 34 C19.38 34 14.76 34 10 34 C9.67 30.7 9.34 27.4 9 24 C12.96 24.33 16.92 24.66 21 25 C21 23.02 21 21.04 21 19 C17.37 18.67 13.74 18.34 10 18 C9.67 15.03 9.34 12.06 9 9 C13.95 9 18.9 9 24 9 C24 7.02 24 5.04 24 3 C17.07 3 10.14 3 3 3 Z M11 25 C12 29 12 29 12 29 Z " fill="#6F96B9" transform="translate(806,816)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C5.97 2.33 8.94 2.66 12 3 C12.33 2.34 12.66 1.68 13 1 C13 1.66 13 2.32 13 3 C13.516 2.649 14.031 2.299 14.562 1.938 C17.298 0.886 18.98 1.12 21.816 1.656 C24.522 2.082 27.203 2.175 29.938 2.312 C35.533 2.735 39.204 4.141 44 7 C43.34 7.33 42.68 7.66 42 8 C39.03 7.01 36.06 6.02 33 5 C22.44 4.67 11.88 4.34 1 4 C1 26.11 1 48.22 1 71 C5.95 71 10.9 71 16 71 C16 64.07 16 57.14 16 50 C18.248 49.918 20.496 49.835 22.812 49.75 C31.316 49.293 38.433 46.879 46 43 C40.504 49.355 34.705 50.982 26.5 51.688 C24.667 51.803 22.834 51.911 21 52 C20.67 53.32 20.34 54.64 20 56 C19.34 56 18.68 56 18 56 C17.67 61.28 17.34 66.56 17 72 C10.4 72 3.8 72 -3 72 C-2.67 71.01 -2.34 70.02 -2 69 C-1.906 67.213 -1.869 65.422 -1.867 63.633 C-1.866 62.582 -1.865 61.532 -1.863 60.449 C-1.867 59.352 -1.871 58.255 -1.875 57.125 C-1.871 56.036 -1.867 54.946 -1.863 53.824 C-1.865 52.771 -1.866 51.718 -1.867 50.633 C-1.868 49.673 -1.869 48.712 -1.871 47.723 C-1.998 45.052 -2.404 42.603 -3 40 C-2.34 40 -1.68 40 -1 40 C-1.041 38.966 -1.082 37.932 -1.124 36.866 C-1.272 33.018 -1.407 29.17 -1.537 25.321 C-1.595 23.657 -1.658 21.994 -1.724 20.331 C-1.818 17.936 -1.899 15.54 -1.977 13.145 C-2.009 12.405 -2.041 11.665 -2.074 10.903 C-2.193 6.781 -1.862 3.702 0 0 Z M-2 41 C-1 44 -1 44 -1 44 Z " fill="#6F97B8" transform="translate(443,624)"/>
<path d="M0 0 C8.25 0 16.5 0 25 0 C25 1.65 25 3.3 25 5 C20.545 5.495 20.545 5.495 16 6 C15.67 15.24 15.34 24.48 15 34 C13.02 34 11.04 34 9 34 C9 24.43 9 14.86 9 5 C6.03 5 3.06 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#8EBCE2" transform="translate(646,875)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 1.67 2.98 1.34 4 1 C4.33 1.33 4.66 1.66 5 2 C5.33 1.34 5.66 0.68 6 0 C14.079 0.777 14.079 0.777 17 4 C21 11.543 21 11.543 21 15 C21.99 15 22.98 15 24 15 C24.144 15.908 24.289 16.815 24.438 17.75 C24.864 20.215 25.351 22.588 26 25 C22.696 21.911 20.901 18.465 18.812 14.5 C18.444 13.823 18.076 13.146 17.697 12.449 C15 7.375 15 7.375 15 4 C9.06 4 3.12 4 -3 4 C-3 26.11 -3 48.22 -3 71 C1.95 71 6.9 71 12 71 C12 57.47 12 43.94 12 30 C12.66 30 13.32 30 14 30 C15.959 36.013 17.416 41.696 18 48 C16.515 48.495 16.515 48.495 15 49 C15.008 49.992 15.008 49.992 15.016 51.004 C15.037 53.982 15.05 56.96 15.062 59.938 C15.071 60.978 15.079 62.019 15.088 63.092 C15.093 64.576 15.093 64.576 15.098 66.09 C15.106 67.465 15.106 67.465 15.114 68.867 C15 71 15 71 14 72 C8.06 72 2.12 72 -4 72 C-4 48.9 -4 25.8 -4 2 C-2.68 1.34 -1.36 0.68 0 0 Z " fill="#597D9E" transform="translate(687,624)"/>
<path d="M0 0 C32.34 0.33 64.68 0.66 98 1 C98 1.66 98 2.32 98 3 C87.34 4.194 76.724 4.129 66.016 4.098 C64.105 4.096 62.194 4.094 60.283 4.093 C55.292 4.09 50.301 4.08 45.31 4.069 C40.201 4.058 35.093 4.054 29.984 4.049 C19.99 4.038 9.995 4.021 0 4 C0 2.68 0 1.36 0 0 Z " fill="#5B87B2" transform="translate(79,764)"/>
<path d="M0 0 C16.582 4.965 28.645 17.384 37.002 32.112 C42.804 43.092 46.201 54.613 47 67 C47.054 67.746 47.108 68.493 47.164 69.262 C47.373 74.85 46.617 80.195 45.688 85.688 C45.57 86.4 45.452 87.113 45.331 87.848 C44.469 92.592 43.044 96.632 41 101 C40.01 100.505 40.01 100.505 39 100 C39.289 98.967 39.577 97.935 39.875 96.871 C46.057 73.193 44.178 51.163 32.064 29.771 C24.728 17.562 14.426 10.206 2.199 3.453 C1.473 2.974 0.748 2.494 0 2 C0 1.34 0 0.68 0 0 Z " fill="#5B7DA7" transform="translate(176,304)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C7.64 1.33 10.28 1.66 13 2 C13 1.34 13 0.68 13 0 C19.152 0.586 19.152 0.586 21 1 C22.642 4.283 21.519 7.425 21 11 C17.04 10.67 13.08 10.34 9 10 C9 11.98 9 13.96 9 16 C11.97 16 14.94 16 18 16 C20.125 22.625 20.125 22.625 19 26 C16.03 26.33 13.06 26.66 10 27 C9.67 28.65 9.34 30.3 9 32 C13.29 32 17.58 32 22 32 C22 34.64 22 37.28 22 40 C13.42 40 4.84 40 -4 40 C-3.67 38.35 -3.34 36.7 -3 35 C-3.165 34.381 -3.33 33.763 -3.5 33.125 C-4.237 29.994 -3.452 27.161 -3 24 C-3.347 21.678 -3.347 21.678 -4 20 C-3.67 20 -3.34 20 -3 20 C-3.012 18.952 -3.023 17.904 -3.035 16.824 C-3.045 15.445 -3.054 14.066 -3.062 12.688 C-3.071 11.997 -3.079 11.307 -3.088 10.596 C-3.103 7.452 -3.002 5.006 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z M-1 3 C-1 14.88 -1 26.76 -1 39 C6.26 39 13.52 39 21 39 C21 37.35 21 35.7 21 34 C16.05 34 11.1 34 6 34 C6 30.7 6 27.4 6 24 C9.96 24 13.92 24 18 24 C18 22.02 18 20.04 18 18 C12.06 18.495 12.06 18.495 6 19 C6 15.7 6 12.4 6 9 C10.62 9 15.24 9 20 9 C20 7.02 20 5.04 20 3 C13.07 3 6.14 3 -1 3 Z M7 12 C8 16 8 16 8 16 Z M7 26 C7 27.98 7 29.96 7 32 C7.33 32 7.66 32 8 32 C8 30.02 8 28.04 8 26 C7.67 26 7.34 26 7 26 Z " fill="#668EB0" transform="translate(335,816)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.583 1.856 2.165 1.711 2.766 1.562 C9.602 0.015 16.011 -0.267 23 0 C23.749 -0.005 24.498 -0.01 25.27 -0.016 C33.716 0.298 42.355 4.692 48.535 10.301 C56.863 20.061 58.45 29.825 58.674 42.271 C58.691 43.111 58.709 43.951 58.727 44.816 C58.733 45.574 58.738 46.331 58.745 47.112 C58.839 49.243 58.839 49.243 61 51 C60.34 51.66 59.68 52.32 59 53 C58.352 55.571 58.352 55.571 58 58 C57.01 58 56.02 58 55 58 C54.34 58.66 53.68 59.32 53 60 C50.69 59.34 48.38 58.68 46 58 C45.34 58.66 44.68 59.32 44 60 C42.328 59.692 40.662 59.356 39 59 C36.946 59.409 36.946 59.409 35 60 C31 60 31 60 28.188 58.75 C25.45 55.308 25.824 54.036 26.188 49.75 C26.433 47.83 26.696 45.912 27 44 C27.33 44 27.66 44 28 44 C28 47.63 28 51.26 28 55 C37.57 55 47.14 55 57 55 C56.897 51.411 56.794 47.822 56.688 44.125 C56.663 43.015 56.638 41.906 56.612 40.762 C56.224 30.102 53.713 20.486 47 12 C38.692 4.44 28.485 2.47 17.562 2.688 C16.65 2.696 15.737 2.704 14.796 2.713 C8.392 2.827 2.217 3.369 -4 5 C-2.68 3.35 -1.36 1.7 0 0 Z " fill="#864637" transform="translate(753,76)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.66 5 1.32 5 2 C5.846 1.933 6.691 1.866 7.562 1.797 C11.358 1.745 14.35 1.914 18 3 C21.476 6.599 23.773 10.539 26 15 C25.67 15.66 25.34 16.32 25 17 C21.072 13.259 19.047 10.117 17 5 C11.39 4.67 5.78 4.34 0 4 C0 26.11 0 48.22 0 71 C4.95 71 9.9 71 15 71 C15 57.14 15 43.28 15 29 C18.608 32.608 20.879 34.999 23.375 39.188 C23.898 40.047 24.422 40.907 24.961 41.793 C25.304 42.521 25.647 43.25 26 44 C25.67 44.66 25.34 45.32 25 46 C23.68 45.34 22.36 44.68 21 44 C21.33 43.34 21.66 42.68 22 42 C21.108 39.883 21.108 39.883 20 38 C17.603 41.596 17.76 43.301 17.812 47.562 C17.819 48.719 17.825 49.875 17.832 51.066 C17.802 53.934 17.802 53.934 19 56 C18.34 56 17.68 56 17 56 C17.141 58.292 17.288 60.584 17.438 62.875 C17.519 64.151 17.6 65.427 17.684 66.742 C17.792 69.883 17.792 69.883 19 72 C12.4 72 5.8 72 -1 72 C-1.023 63.055 -1.041 54.111 -1.052 45.166 C-1.057 41.013 -1.064 36.86 -1.075 32.706 C-1.086 28.699 -1.092 24.692 -1.095 20.685 C-1.097 19.155 -1.1 17.626 -1.106 16.096 C-1.113 13.955 -1.114 11.815 -1.114 9.675 C-1.116 8.455 -1.118 7.236 -1.12 5.98 C-1 3 -1 3 0 0 Z M17 34 C18 36 18 36 18 36 Z M17 37 C18 39 18 39 18 39 Z " fill="#557899" transform="translate(532,624)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-10.622 3.046 -21.245 3.082 -31.867 3.104 C-36.8 3.114 -41.732 3.128 -46.664 3.151 C-51.423 3.173 -56.182 3.185 -60.942 3.19 C-62.758 3.193 -64.575 3.201 -66.391 3.211 C-68.934 3.226 -71.476 3.228 -74.019 3.227 C-75.147 3.238 -75.147 3.238 -76.298 3.249 C-80.026 3.234 -82.836 3.078 -86 1 C-74.719 -10.281 -15.203 -0.36 0 0 Z " fill="#4A74A1" transform="translate(937,972)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 11.88 7 23.76 7 36 C4.69 36 2.38 36 0 36 C0 24.12 0 12.24 0 0 Z " fill="#8FBBE1" transform="translate(550,819)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 11.88 7 23.76 7 36 C4.69 36 2.38 36 0 36 C0 24.12 0 12.24 0 0 Z " fill="#8FBAE1" transform="translate(427,819)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C2.999 1.928 3.998 1.856 5.027 1.781 C6.318 1.688 7.608 1.596 8.938 1.5 C10.225 1.407 11.513 1.314 12.84 1.219 C16 1 16 1 17 1 C17.871 16.605 18.11 32.187 18.125 47.812 C18.127 48.732 18.13 49.651 18.133 50.598 C18.138 53.239 18.137 55.879 18.133 58.52 C18.135 59.316 18.137 60.113 18.139 60.935 C18.124 64.528 17.878 67.488 17 71 C11.06 71 5.12 71 -1 71 C-1 70.67 -1 70.34 -1 70 C4.61 70 10.22 70 16 70 C16 47.89 16 25.78 16 3 C11.38 3 6.76 3 2 3 C2 16.86 2 30.72 2 45 C-2.353 40.647 -5.214 36.934 -8.375 31.812 C-9.069 30.727 -9.069 30.727 -9.777 29.619 C-10.425 28.58 -10.425 28.58 -11.086 27.52 C-11.478 26.892 -11.871 26.264 -12.275 25.616 C-12.514 25.083 -12.753 24.55 -13 24 C-12.67 23.34 -12.34 22.68 -12 22 C-9 25.75 -9 25.75 -9 28 C-8.34 28.33 -7.68 28.66 -7 29 C-6.526 29.846 -6.051 30.691 -5.562 31.562 C-5.047 32.367 -4.531 33.171 -4 34 C-3.01 34 -2.02 34 -1 34 C-0.67 22.78 -0.34 11.56 0 0 Z M-3 36 C-2.67 36.99 -2.34 37.98 -2 39 C-1.34 39 -0.68 39 0 39 C0 38.01 0 37.02 0 36 C-0.99 36 -1.98 36 -3 36 Z " fill="#5E83A4" transform="translate(575,625)"/>
<path d="M0 0 C9.57 0 19.14 0 29 0 C28.67 3.63 28.34 7.26 28 11 C28.66 12.65 29.32 14.3 30 16 C29.75 18.25 29.75 18.25 29 20 C28.01 20.495 28.01 20.495 27 21 C27.108 22.147 27.108 22.147 27.219 23.316 C28.165 34.24 28.75 45.137 27 56 C26.67 56 26.34 56 26 56 C26 38.51 26 21.02 26 3 C15.11 3 4.22 3 -7 3 C-7 12.9 -7 22.8 -7 33 C-6.34 33 -5.68 33 -5 33 C-5 33.99 -5 34.98 -5 36 C-4.01 35.505 -4.01 35.505 -3 35 C-2.773 41.746 -2.583 48.585 -5 55 C-5.99 55.495 -5.99 55.495 -7 56 C-6.836 55.305 -6.673 54.61 -6.504 53.895 C-5.959 50.765 -5.903 47.862 -5.938 44.688 C-5.947 43.619 -5.956 42.55 -5.965 41.449 C-5.976 40.641 -5.988 39.833 -6 39 C-6.66 38.67 -7.32 38.34 -8 38 C-9.039 33.608 -9.117 29.349 -9.098 24.852 C-9.096 23.865 -9.096 23.865 -9.093 22.859 C-9.088 20.781 -9.075 18.703 -9.062 16.625 C-9.057 15.207 -9.053 13.789 -9.049 12.371 C-9.038 8.914 -9.021 5.457 -9 2 C-4.545 1.505 -4.545 1.505 0 1 C0 0.67 0 0.34 0 0 Z " fill="#88483A" transform="translate(784,152)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.01 1.208 1.021 2.416 1.031 3.66 C1.129 15.062 1.232 26.464 1.339 37.867 C1.394 43.728 1.448 49.589 1.497 55.451 C1.545 61.111 1.598 66.772 1.653 72.432 C1.673 74.588 1.692 76.744 1.709 78.899 C1.733 81.927 1.763 84.955 1.795 87.982 C1.8 88.866 1.806 89.75 1.812 90.661 C1.877 96.173 2.312 101.53 3 107 C0.095 104.095 0.287 100.789 -0.125 96.875 C-0.211 96.121 -0.298 95.367 -0.387 94.59 C-0.599 92.727 -0.8 90.864 -1 89 C-1.66 89 -2.32 89 -3 89 C-2.707 86.982 -2.414 84.964 -2.121 82.945 C-1.832 80.919 -1.832 80.919 -3 79 C-2.34 79 -1.68 79 -1 79 C-1.141 76.708 -1.288 74.416 -1.438 72.125 C-1.559 70.211 -1.559 70.211 -1.684 68.258 C-1.792 65.117 -1.792 65.117 -3 63 C-2.34 63 -1.68 63 -1 63 C-1 62.01 -1 61.02 -1 60 C-1.66 60 -2.32 60 -3 60 C-3 58.35 -3 56.7 -3 55 C-2.67 55 -2.34 55 -2 55 C-2 53.35 -2 51.7 -2 50 C-2.33 50 -2.66 50 -3 50 C-3 48.02 -3 46.04 -3 44 C-2.67 44 -2.34 44 -2 44 C-1.67 41.36 -1.34 38.72 -1 36 C-1.66 36 -2.32 36 -3 36 C-2.838 35.31 -2.675 34.621 -2.508 33.91 C-1.948 30.7 -1.867 27.695 -1.875 24.438 C-1.872 23.322 -1.87 22.207 -1.867 21.059 C-1.978 18.508 -2.253 16.424 -3 14 C-2.34 13.67 -1.68 13.34 -1 13 C-1.66 12.01 -2.32 11.02 -3 10 C-3.125 7.312 -3.125 7.312 -3 5 C-2.34 5 -1.68 5 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#9B503C" transform="translate(333,110)"/>
<path d="M0 0 C0.625 2.812 0.625 2.812 1 6 C0.01 7.485 0.01 7.485 -1 9 C-2.945 9.293 -2.945 9.293 -5.125 9.188 C-6.404 9.126 -7.683 9.064 -9 9 C-8.988 9.692 -8.977 10.384 -8.965 11.098 C-8.888 19.448 -9.363 27.679 -10 36 C-9.34 36.33 -8.68 36.66 -8 37 C-9.32 37 -10.64 37 -12 37 C-12 27.1 -12 17.2 -12 7 C-8.7 7 -5.4 7 -2 7 C-2 5.35 -2 3.7 -2 2 C-9.92 2 -17.84 2 -26 2 C-26.33 3.65 -26.66 5.3 -27 7 C-24.03 7 -21.06 7 -18 7 C-18 16.9 -18 26.8 -18 37 C-18.66 37 -19.32 37 -20 37 C-20.196 33.98 -20.382 30.959 -20.562 27.938 C-20.619 27.09 -20.675 26.242 -20.732 25.369 C-21.02 20.397 -20.898 15.902 -20 11 C-20.99 10.67 -21.98 10.34 -23 10 C-25 9.333 -27 8.667 -29 8 C-29 5.36 -29 2.72 -29 0 C-25.667 -0.199 -22.334 -0.382 -19 -0.562 C-17.598 -0.647 -17.598 -0.647 -16.168 -0.732 C-10.615 -1.024 -5.494 -0.875 0 0 Z " fill="#769DBF" transform="translate(493,873)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.338 0.993 2.338 0.993 2.683 2.006 C6.785 14.019 10.939 26.003 15.438 37.875 C18.648 46.373 21.836 54.878 24.935 63.417 C25.614 65.281 26.302 67.141 26.99 69.001 C27.398 70.131 27.806 71.262 28.227 72.426 C28.777 73.933 28.777 73.933 29.338 75.47 C30 78 30 78 29 81 C24.772 71.049 21.153 60.902 17.625 50.688 C17.396 50.029 17.168 49.371 16.932 48.692 C16.277 46.8 15.627 44.906 14.977 43.012 C14.596 41.903 14.215 40.794 13.823 39.651 C13 37 13 37 13 35 C12.01 34.505 12.01 34.505 11 34 C10.105 30.923 9.461 27.819 8.781 24.688 C8.523 23.801 8.266 22.914 8 22 C7.34 21.67 6.68 21.34 6 21 C5.148 18.496 5.148 18.496 4.375 15.438 C4.115 14.426 3.854 13.414 3.586 12.371 C3.393 11.589 3.199 10.806 3 10 C2.67 10 2.34 10 2 10 C2 35.74 2 61.48 2 88 C-0.562 89.281 -2.403 89.129 -5.27 89.133 C-6.85 89.135 -6.85 89.135 -8.463 89.137 C-10.121 89.131 -10.121 89.131 -11.812 89.125 C-13.453 89.131 -13.453 89.131 -15.127 89.137 C-16.713 89.135 -16.713 89.135 -18.332 89.133 C-19.299 89.132 -20.265 89.131 -21.261 89.129 C-23.939 89.003 -26.391 88.598 -29 88 C-29 87.67 -29 87.34 -29 87 C-19.43 87 -9.86 87 0 87 C0 58.29 0 29.58 0 0 Z " fill="#80443A" transform="translate(656,338)"/>
<path d="M0 0 C1.147 -0.025 1.147 -0.025 2.316 -0.051 C4.916 0.395 5.786 1.313 7.438 3.312 C6.777 3.972 6.118 4.633 5.438 5.312 C5.438 4.653 5.438 3.992 5.438 3.312 C1.808 3.312 -1.822 3.312 -5.562 3.312 C-5.562 15.192 -5.562 27.072 -5.562 39.312 C-3.582 39.312 -1.603 39.312 0.438 39.312 C0.438 31.062 0.438 22.812 0.438 14.312 C1.098 14.312 1.757 14.312 2.438 14.312 C3.776 18.102 5.107 21.895 6.438 25.688 C6.819 26.768 7.201 27.848 7.594 28.961 C7.955 29.992 8.316 31.023 8.688 32.086 C9.023 33.039 9.358 33.992 9.703 34.974 C10.438 37.312 10.438 37.312 10.438 39.312 C13.369 36.89 14.006 35.059 14.438 31.312 C13.938 28.819 13.938 28.819 12.969 26.336 C12.645 25.418 12.322 24.5 11.988 23.555 C11.642 22.609 11.295 21.662 10.938 20.688 C10.252 18.802 9.575 16.915 8.906 15.023 C8.601 14.191 8.297 13.359 7.982 12.501 C7.438 10.312 7.438 10.312 8.438 7.312 C9.39 9.932 10.341 12.553 11.285 15.176 C12.015 17.163 12.77 19.141 13.559 21.105 C16.092 27.537 17.17 33.408 17.438 40.312 C14.798 40.312 12.158 40.312 9.438 40.312 C8.181 37.406 7.438 35.516 7.438 32.312 C6.777 31.982 6.118 31.653 5.438 31.312 C4.586 29.027 4.586 29.027 3.812 26.25 C3.422 24.871 3.422 24.871 3.023 23.465 C2.83 22.755 2.637 22.044 2.438 21.312 C2.449 22.265 2.461 23.218 2.473 24.199 C2.482 25.453 2.491 26.708 2.5 28 C2.512 29.241 2.523 30.483 2.535 31.762 C2.452 34.775 2.146 37.395 1.438 40.312 C-1.863 40.312 -5.163 40.312 -8.562 40.312 C-8.562 37.342 -8.562 34.372 -8.562 31.312 C-8.233 31.312 -7.902 31.312 -7.562 31.312 C-7.562 26.362 -7.562 21.413 -7.562 16.312 C-7.892 16.312 -8.223 16.312 -8.562 16.312 C-8.589 14.5 -8.609 12.688 -8.625 10.875 C-8.637 9.866 -8.648 8.856 -8.66 7.816 C-8.562 5.312 -8.562 5.312 -7.562 4.312 C-7.892 3.653 -8.223 2.992 -8.562 2.312 C-5.603 0.62 -3.406 0.017 0 0 Z M-7.562 33.312 C-6.562 37.312 -6.562 37.312 -6.562 37.312 Z " fill="#749CBE" transform="translate(368.5625,815.6875)"/>
<path d="M0 0 C0 4.029 -1.745 6.394 -3.711 9.844 C-8.058 20.486 -7.294 32.184 -7.266 43.465 C-7.267 45.349 -7.269 47.232 -7.271 49.116 C-7.274 53.049 -7.27 56.981 -7.261 60.914 C-7.249 65.924 -7.256 70.935 -7.268 75.945 C-7.275 79.831 -7.273 83.717 -7.268 87.602 C-7.266 89.448 -7.268 91.294 -7.273 93.141 C-7.457 116.136 -7.457 116.136 2.855 136.168 C9.759 142.631 17.843 145.355 27 147 C27 147.33 27 147.66 27 148 C16.116 147.168 8.895 145.563 1.449 137.273 C0.271 135.873 -0.869 134.439 -2 133 C-3.32 133.33 -4.64 133.66 -6 134 C-6.934 130.99 -7.044 130.133 -6 127 C-6.639 124.991 -7.31 122.992 -8 121 C-8.973 115.56 -9.159 110.287 -9.177 104.783 C-9.184 103.886 -9.19 102.989 -9.197 102.065 C-9.217 99.113 -9.228 96.162 -9.238 93.211 C-9.242 92.197 -9.246 91.182 -9.251 90.137 C-9.271 84.766 -9.286 79.396 -9.295 74.025 C-9.306 68.508 -9.341 62.991 -9.38 57.473 C-9.407 53.204 -9.415 48.935 -9.418 44.666 C-9.423 42.634 -9.435 40.601 -9.453 38.569 C-9.684 11.78 -9.684 11.78 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#653934" transform="translate(423,281)"/>
<path d="M0 0 C5.61 0 11.22 0 17 0 C17.66 1.32 18.32 2.64 19 4 C18.34 4 17.68 4 17 4 C16.67 3.34 16.34 2.68 16 2 C9.07 1.505 9.07 1.505 2 1 C2 22.12 2 43.24 2 65 C5.63 65 9.26 65 13 65 C13.33 51.8 13.66 38.6 14 25 C14.33 25 14.66 25 15 25 C15 38.86 15 52.72 15 67 C10.05 67 5.1 67 0 67 C0 44.89 0 22.78 0 0 Z " fill="#96C3E8" transform="translate(532,628)"/>
<path d="M0 0 C1 0.005 2 0.01 3.031 0.016 C4.003 0.018 4.975 0.021 5.977 0.023 C7.842 0.037 7.842 0.037 9.746 0.051 C13.685 0.072 17.625 0.092 21.683 0.114 C22.013 9.684 22.343 19.254 22.683 29.114 C21.693 29.114 20.703 29.114 19.683 29.114 C19.353 21.194 19.023 13.274 18.683 5.114 C11.814 3.74 5.712 4.607 -1.317 5.114 C-1.317 13.034 -1.317 20.954 -1.317 29.114 C-7.587 29.114 -13.857 29.114 -20.317 29.114 C-20.317 26.144 -20.317 23.174 -20.317 20.114 C-19.657 20.114 -18.997 20.114 -18.317 20.114 C-18.317 22.754 -18.317 25.394 -18.317 28.114 C-13.367 28.114 -8.417 28.114 -3.317 28.114 C-3.328 26.176 -3.34 24.239 -3.352 22.242 C-3.361 19.72 -3.37 17.198 -3.379 14.676 C-3.388 13.397 -3.396 12.117 -3.405 10.799 C-3.409 8.977 -3.409 8.977 -3.414 7.117 C-3.422 5.429 -3.422 5.429 -3.43 3.706 C-3.275 0.16 -3.275 0.16 0 0 Z M5.683 2.114 C9.683 3.114 9.683 3.114 9.683 3.114 Z " fill="#5C82A3" transform="translate(306.316650390625,666.886474609375)"/>
<path d="M0 0 C25.74 0 51.48 0 78 0 C77.67 0.99 77.34 1.98 77 3 C69.766 3.023 62.532 3.041 55.298 3.052 C51.936 3.057 48.575 3.064 45.214 3.075 C30.108 3.123 15.073 3.055 0 2 C0 1.34 0 0.68 0 0 Z " fill="#5783AF" transform="translate(860,765)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C4.333 1.04 6.667 1.043 9 1 C9 0.67 9 0.34 9 0 C12.96 0 16.92 0 21 0 C21 0.66 21 1.32 21 2 C21.99 2 22.98 2 24 2 C24 3.98 24 5.96 24 8 C23.67 8 23.34 8 23 8 C23 6.35 23 4.7 23 3 C14.75 3 6.5 3 -2 3 C-2 4.98 -2 6.96 -2 9 C0.97 9 3.94 9 7 9 C7 18.9 7 28.8 7 39 C9.97 38.505 9.97 38.505 13 38 C13 28.43 13 18.86 13 9 C16.3 9 19.6 9 23 9 C23 9.33 23 9.66 23 10 C20.69 10.33 18.38 10.66 16 11 C16.008 12.105 16.008 12.105 16.016 13.231 C16.037 16.571 16.05 19.91 16.062 23.25 C16.071 24.409 16.079 25.568 16.088 26.762 C16.093 28.434 16.093 28.434 16.098 30.141 C16.106 31.68 16.106 31.68 16.114 33.251 C16 36 16 36 15 40 C11.7 40 8.4 40 5 40 C5 30.1 5 20.2 5 10 C2.03 10 -0.94 10 -4 10 C-4.33 7.69 -4.66 5.38 -5 3 C-3.35 2.67 -1.7 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#739ABC" transform="translate(707,816)"/>
<path d="M0 0 C1.754 1.516 1.754 1.516 3 3 C2.004 2.805 2.004 2.805 0.988 2.605 C-1.739 2.179 -4.239 1.894 -7 2 C-10.389 4.49 -11.677 6.03 -13 10 C-13.081 11.627 -13.108 13.258 -13.098 14.887 C-13.094 15.825 -13.091 16.762 -13.088 17.729 C-13.08 18.705 -13.071 19.681 -13.062 20.688 C-13.058 21.676 -13.053 22.664 -13.049 23.682 C-13.037 26.121 -13.021 28.561 -13 31 C-12.67 31 -12.34 31 -12 31 C-12 32.65 -12 34.3 -12 36 C-12.66 36 -13.32 36 -14 36 C-13.892 43.459 -13.78 50.917 -13.661 58.376 C-13.606 61.84 -13.552 65.303 -13.503 68.767 C-13.446 72.754 -13.381 76.74 -13.316 80.727 C-13.291 82.583 -13.291 82.583 -13.266 84.477 C-13.246 85.638 -13.226 86.799 -13.205 87.995 C-13.19 89.012 -13.174 90.029 -13.158 91.077 C-12.994 94.112 -12.554 97.013 -12 100 C-14 99 -14 99 -15.109 95.927 C-16.124 92.02 -16.282 88.193 -16.259 84.172 C-16.262 83.423 -16.266 82.674 -16.269 81.903 C-16.278 79.447 -16.272 76.991 -16.266 74.535 C-16.267 72.816 -16.269 71.096 -16.271 69.376 C-16.274 65.78 -16.27 62.183 -16.261 58.586 C-16.249 53.992 -16.256 49.398 -16.268 44.804 C-16.275 41.255 -16.273 37.706 -16.268 34.157 C-16.266 32.463 -16.268 30.77 -16.273 29.077 C-16.278 26.704 -16.27 24.331 -16.259 21.957 C-16.263 21.268 -16.267 20.578 -16.271 19.868 C-16.209 12.648 -15.179 6.341 -10 1 C-6.885 -0.558 -3.396 -0.565 0 0 Z " fill="#904B39" transform="translate(267,108)"/>
<path d="M0 0 C1 2 1 2 0.258 4.391 C-0.116 5.334 -0.49 6.278 -0.875 7.25 C-3.625 14.213 -4.149 20.571 -4.145 27.968 C-4.149 28.85 -4.152 29.732 -4.155 30.641 C-4.165 33.547 -4.167 36.453 -4.168 39.359 C-4.171 41.39 -4.175 43.42 -4.178 45.45 C-4.184 49.703 -4.186 53.957 -4.185 58.21 C-4.185 63.641 -4.199 69.072 -4.216 74.504 C-4.227 78.697 -4.229 82.891 -4.229 87.084 C-4.23 89.085 -4.234 91.087 -4.242 93.088 C-4.252 95.897 -4.249 98.706 -4.243 101.515 C-4.249 102.33 -4.254 103.144 -4.26 103.984 C-4.189 117.209 -0.195 129.264 9 139 C2.623 136.141 -0.729 131.173 -3.312 124.812 C-5.354 119.343 -6.123 114.93 -6.098 109.047 C-6.096 108.387 -6.095 107.726 -6.093 107.046 C-6.088 104.947 -6.075 102.849 -6.062 100.75 C-6.057 99.323 -6.053 97.896 -6.049 96.469 C-6.038 92.979 -6.021 89.49 -6 86 C-6.66 85.67 -7.32 85.34 -8 85 C-7.505 84.505 -7.505 84.505 -7 84 C-7.33 83.34 -7.66 82.68 -8 82 C-7.66 80.055 -7.66 80.055 -7.062 77.875 C-6.771 76.788 -6.771 76.788 -6.473 75.68 C-6.317 75.125 -6.161 74.571 -6 74 C-6.66 73.67 -7.32 73.34 -8 73 C-7.836 72.174 -7.673 71.347 -7.504 70.496 C-6.951 66.658 -6.892 62.931 -6.938 59.066 C-6.941 58.311 -6.945 57.556 -6.949 56.779 C-6.965 53.58 -6.993 50.381 -7.021 47.183 C-7.235 19.939 -7.235 19.939 -4 7 C-4 6.01 -4 5.02 -4 4 C-2 1.812 -2 1.812 0 0 Z " fill="#7C4135" transform="translate(733,94)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 27.72 2 55.44 2 84 C1.34 84 0.68 84 0 84 C-0.534 82.588 -1.052 81.17 -1.562 79.75 C-1.853 78.961 -2.143 78.172 -2.441 77.359 C-3.045 74.809 -2.847 73.447 -2 71 C-1.67 71.33 -1.34 71.66 -1 72 C-0.67 63.09 -0.34 54.18 0 45 C-0.99 44.67 -1.98 44.34 -3 44 C-2.34 43.34 -1.68 42.68 -1 42 C-0.758 40.019 -0.758 40.019 -0.875 37.875 C-0.916 36.596 -0.957 35.317 -1 34 C-1.33 34 -1.66 34 -2 34 C-2 32.02 -2 30.04 -2 28 C-1.67 28 -1.34 28 -1 28 C-1 24.7 -1 21.4 -1 18 C-1.33 18 -1.66 18 -2 18 C-2.33 16.02 -2.66 14.04 -3 12 C-2.34 11.67 -1.68 11.34 -1 11 C-1.021 9.928 -1.041 8.855 -1.062 7.75 C-1.014 4.852 -0.868 2.714 0 0 Z " fill="#7D392D" transform="translate(691,271)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C2.973 2.876 1.901 3.701 0.812 4.5 C-5.332 9.588 -8.424 17.294 -10 25 C-10.318 28.581 -10.371 32.153 -10.414 35.746 C-10.432 36.643 -10.449 37.541 -10.468 38.465 C-10.53 41.789 -10.577 45.113 -10.625 48.438 C-10.749 55.883 -10.873 63.329 -11 71 C13.75 71 38.5 71 64 71 C64 71.33 64 71.66 64 72 C38.92 72 13.84 72 -12 72 C-12.082 63.626 -12.165 55.253 -12.25 46.625 C-12.286 44.001 -12.323 41.378 -12.36 38.674 C-12.372 36.567 -12.382 34.459 -12.391 32.352 C-12.411 31.28 -12.432 30.208 -12.453 29.104 C-12.457 20.274 -10.53 12.487 -4.875 5.5 C-4.129 4.556 -4.129 4.556 -3.367 3.594 C-2 2 -2 2 0 0 Z " fill="#537EAB" transform="translate(486,503)"/>
<path d="M0 0 C3.991 3.991 3.152 10.229 3.161 15.591 C3.166 16.327 3.171 17.062 3.176 17.821 C3.191 20.24 3.198 22.659 3.203 25.078 C3.206 25.91 3.209 26.741 3.212 27.598 C3.227 31.999 3.236 36.4 3.24 40.801 C3.246 45.325 3.27 49.848 3.298 54.371 C3.317 57.87 3.322 61.368 3.324 64.867 C3.327 66.533 3.335 68.2 3.348 69.866 C3.412 78.616 3.388 86.759 0 95 C-0.66 94.67 -1.32 94.34 -2 94 C-1.673 93.265 -1.345 92.53 -1.008 91.773 C0.434 86.376 0.21 81.183 0.125 75.625 C0.116 74.507 0.107 73.39 0.098 72.238 C0.074 69.492 0.041 66.746 0 64 C-0.33 64 -0.66 64 -1 64 C-1 59.05 -1 54.1 -1 49 C-0.67 49 -0.34 49 0 49 C0 32.83 0 16.66 0 0 Z " fill="#9F4C36" transform="translate(386,113)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.078 3.52 2.963 6.671 3.375 10.344 C5.008 19.895 11.237 29.25 19 35 C23.819 37.796 28.71 40.237 34 42 C33.67 43.32 33.34 44.64 33 46 C32.385 46.11 31.77 46.219 31.137 46.332 C30.328 46.491 29.52 46.649 28.688 46.812 C27.887 46.963 27.086 47.114 26.262 47.27 C23.733 48.086 22.741 49.041 21 51 C18.938 52.25 18.938 52.25 17 53 C16.01 52.505 16.01 52.505 15 52 C19.468 48.394 23.321 45.42 29 44 C29 43.34 29 42.68 29 42 C28.175 41.794 27.35 41.587 26.5 41.375 C21.988 39.602 18.701 37.089 15 34 C14.399 33.519 13.799 33.038 13.18 32.543 C6.024 26.205 2.385 16.752 -1 8 C-1.352 8.978 -1.704 9.957 -2.066 10.965 C-5.599 20.46 -9.68 27.835 -17 35 C-17.66 34.67 -18.32 34.34 -19 34 C-18.492 33.49 -17.984 32.979 -17.461 32.453 C-9.04 23.559 -4.392 14.193 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7B8D9F" transform="translate(871,298)"/>
<path d="M0 0 C23.43 0 46.86 0 71 0 C71 0.66 71 1.32 71 2 C54.344 2.898 37.74 3.126 21.062 3.062 C18.71 3.057 16.358 3.053 14.006 3.049 C8.337 3.038 2.669 3.021 -3 3 C-2.01 2.67 -1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6490BA" transform="translate(177,765)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.012 2.591 2.012 2.591 2.025 4.213 C2.102 14.191 2.19 24.167 2.288 34.144 C2.338 39.274 2.384 44.403 2.422 49.533 C2.459 54.481 2.505 59.428 2.558 64.375 C2.577 66.265 2.592 68.156 2.603 70.047 C2.62 72.688 2.649 75.329 2.681 77.97 C2.683 78.758 2.685 79.545 2.688 80.357 C2.602 84.641 2.602 84.641 5 88 C4.01 88.99 4.01 88.99 3 90 C2.01 90 1.02 90 0 90 C0 60.96 0 31.92 0 2 C-0.66 1.67 -1.32 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#3F71A8" transform="translate(858,470)"/>
<path d="M0 0 C8.571 -0.286 8.571 -0.286 12 2 C11.67 2.99 11.34 3.98 11 5 C10.34 5 9.68 5 9 5 C9.084 5.615 9.168 6.23 9.254 6.863 C9.356 7.672 9.458 8.48 9.562 9.312 C9.667 10.113 9.771 10.914 9.879 11.738 C10 14 10 14 9 17 C9.99 17.33 10.98 17.66 12 18 C10.515 20.475 10.515 20.475 9 23 C9.66 23.33 10.32 23.66 11 24 C10.814 24.577 10.629 25.155 10.438 25.75 C9.81 28.978 10.552 31.105 12 34 C11.01 34 10.02 34 9 34 C9.33 34.66 9.66 35.32 10 36 C12.025 36.652 12.025 36.652 14 37 C14 37.99 14 38.98 14 40 C9.38 40 4.76 40 0 40 C0 39.67 0 39.34 0 39 C2.31 39 4.62 39 7 39 C7 27.12 7 15.24 7 3 C5.02 3 3.04 3 1 3 C1 10.59 1 18.18 1 26 C0.34 25.67 -0.32 25.34 -1 25 C-0.979 23.907 -0.959 22.814 -0.938 21.688 C-0.815 18.056 -0.815 18.056 -2 15 C-2.66 14.67 -3.32 14.34 -4 14 C-2.68 12.02 -1.36 10.04 0 8 C-0.66 8 -1.32 8 -2 8 C-2 6.02 -2 4.04 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7299BB" transform="translate(650,816)"/>
<path d="M0 0 C2.062 0.688 2.062 0.688 4 2 C4.75 4.125 4.75 4.125 5 6 C5.66 5.67 6.32 5.34 7 5 C7 5.66 7 6.32 7 7 C7.66 7.33 8.32 7.66 9 8 C7.02 8.33 5.04 8.66 3 9 C3 9.66 3 10.32 3 11 C6.3 10.67 9.6 10.34 13 10 C13.054 12.271 13.093 14.542 13.125 16.812 C13.148 18.077 13.171 19.342 13.195 20.645 C13 24 13 24 11 27 C7.375 27.688 7.375 27.688 4 28 C3.34 26.35 2.68 24.7 2 23 C2.639 22.773 3.279 22.546 3.938 22.312 C6.272 21.147 6.272 21.147 6.75 18.375 C6.832 17.591 6.915 16.808 7 16 C4.03 15.505 4.03 15.505 1 15 C1.33 13.35 1.66 11.7 2 10 C1.34 9.67 0.68 9.34 0 9 C0.66 7.35 1.32 5.7 2 4 C1.01 4 0.02 4 -1 4 C-1.33 3.01 -1.66 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#8FBADF" transform="translate(529,826)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.62 1 9.24 1 14 C-9 16 -9 16 -11 16 C-11.99 16.33 -12.98 16.66 -14 17 C-14 16.34 -14 15.68 -14 15 C-14.99 14.67 -15.98 14.34 -17 14 C-17 15.32 -17 16.64 -17 18 C-18.65 17.67 -20.3 17.34 -22 17 C-22.33 16.01 -22.66 15.02 -23 14 C-23 17.96 -23 21.92 -23 26 C-16.07 26 -9.14 26 -2 26 C-2 26.33 -2 26.66 -2 27 C0.31 27 2.62 27 5 27 C5 28.32 5 29.64 5 31 C5.66 31.33 6.32 31.66 7 32 C6.34 32 5.68 32 5 32 C5.144 33.279 5.289 34.558 5.438 35.875 C5.519 36.594 5.6 37.314 5.684 38.055 C5.913 40.074 5.913 40.074 7 42 C5.68 41.67 4.36 41.34 3 41 C3 37.04 3 33.08 3 29 C-5.91 29 -14.82 29 -24 29 C-24 23.72 -24 18.44 -24 13 C-16.41 13 -8.82 13 -1 13 C-1 9.37 -1 5.74 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z M-20 14 C-20 14.66 -20 15.32 -20 16 C-19.34 15.67 -18.68 15.34 -18 15 C-18.66 14.67 -19.32 14.34 -20 14 Z " fill="#678DAE" transform="translate(809,654)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C8.67 1.98 8.34 3.96 8 6 C8.66 6.33 9.32 6.66 10 7 C9.67 7.33 9.34 7.66 9 8 C8.956 9.666 8.96 11.334 9 13 C9 14.999 8.988 16.997 8.965 18.996 C8.951 20.7 8.951 20.7 8.938 22.438 C8.926 23.571 8.914 24.704 8.902 25.871 C8.984 28.5 9.355 30.49 10 33 C9.727 35.341 9.402 37.678 9 40 C7.205 39.799 5.414 39.563 3.625 39.312 C2.627 39.185 1.63 39.057 0.602 38.926 C-2 38 -2 38 -3.352 35.418 C-3.566 34.62 -3.78 33.822 -4 33 C-3.67 33.474 -3.34 33.949 -3 34.438 C-0.361 36.775 -0.361 36.775 7 37 C6.67 29.08 6.34 21.16 6 13 C5.67 14.65 5.34 16.3 5 18 C4.01 18 3.02 18 2 18 C1.67 19.98 1.34 21.96 1 24 C0.34 23.67 -0.32 23.34 -1 23 C-1.027 20.104 -1.047 17.208 -1.062 14.312 C-1.071 13.496 -1.079 12.679 -1.088 11.838 C-1.104 7.755 -1.03 3.983 0 0 Z " fill="#7EA9CE" transform="translate(438,872)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C8.67 0.66 8.34 1.32 8 2 C5.69 1.67 3.38 1.34 1 1 C1.33 2.98 1.66 4.96 2 7 C2.66 7 3.32 7 4 7 C4.168 8.771 4.335 10.542 4.5 12.312 C4.593 13.299 4.686 14.285 4.781 15.301 C5 18 5 18 5 21 C4.67 21 4.34 21 4 21 C3.34 25.29 2.68 29.58 2 34 C-0.31 34 -2.62 34 -5 34 C-4.154 26.806 -3.05 19.657 -1.938 12.5 C-1.751 11.296 -1.565 10.092 -1.373 8.852 C-0.916 5.901 -0.459 2.95 0 0 Z " fill="#8EBBE1" transform="translate(581,875)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C5.084 3.25 5.084 3.25 8 3 C7.67 1.35 7.34 -0.3 7 -2 C9 -1 9 -1 10.125 0.938 C12.328 8.648 10.752 16.472 9.34 24.215 C8.788 27.08 8.788 27.08 10 30 C8.907 29.979 7.814 29.959 6.688 29.938 C3.056 29.815 3.056 29.815 0 31 C-1.809 31.144 -3.623 31.22 -5.438 31.25 C-6.385 31.276 -7.332 31.302 -8.309 31.328 C-11.39 30.952 -12.72 30.048 -15 28 C-14.154 28.107 -13.309 28.214 -12.438 28.324 C-5.367 29.071 -0.373 29.309 6 26 C7.481 23.038 7.353 20.165 7.562 16.875 C7.646 15.594 7.73 14.312 7.816 12.992 C7.907 11.511 7.907 11.511 8 10 C4.7 10.33 1.4 10.66 -2 11 C-2 10.34 -2 9.68 -2 9 C-0.02 8.67 1.96 8.34 4 8 C4.33 7.01 4.66 6.02 5 5 C3.855 4.845 3.855 4.845 2.688 4.688 C0 4 0 4 -1.438 2.438 C-3.022 0.709 -3.022 0.709 -6 0.562 C-9.017 0.702 -9.017 0.702 -10.645 2.605 C-12.406 5.717 -12.51 8.193 -12.688 11.75 C-12.753 12.92 -12.819 14.091 -12.887 15.297 C-12.943 16.635 -12.943 16.635 -13 18 C-15.201 14.038 -15.697 10.473 -15 6 C-13.258 2.517 -11.917 0.579 -8.625 -1.5 C-5.148 -2.162 -3.182 -1.485 0 0 Z " fill="#779FC1" transform="translate(534,826)"/>
<path d="M0 0 C2.661 2.334 3.844 4.53 5.125 7.812 C5.458 8.644 5.79 9.475 6.133 10.332 C8.484 17.567 8.332 25.119 8.605 32.652 C8.662 34.023 8.662 34.023 8.719 35.422 C8.749 36.253 8.779 37.084 8.811 37.94 C8.88 40.063 8.88 40.063 10 42 C6.026 42.967 2.467 43.047 -1.613 42.879 C-2.86 42.831 -4.108 42.782 -5.393 42.732 C-6.686 42.676 -7.98 42.62 -9.312 42.562 C-10.625 42.51 -11.938 42.458 -13.291 42.404 C-16.528 42.275 -19.764 42.14 -23 42 C-23.098 40.831 -23.196 39.662 -23.297 38.457 C-23.447 36.909 -23.598 35.361 -23.75 33.812 C-23.812 33.044 -23.874 32.275 -23.938 31.482 C-24.445 26.559 -25.248 23.533 -29 20 C-32.86 18.877 -35.038 18.637 -38.812 20.062 C-43.632 24.331 -43.489 30.943 -44 37 C-44.33 37 -44.66 37 -45 37 C-45.58 24.937 -45.58 24.937 -42 20 C-39.127 17.606 -37.273 17.04 -33.562 16.5 C-28.977 17.144 -27.172 18.681 -24 22 C-22 27 -22 27 -22 41 C-12.43 41 -2.86 41 7 41 C6.67 40.34 6.34 39.68 6 39 C5.906 37.339 5.868 35.675 5.859 34.012 C5.67 22.183 5.191 12.766 0 2 C0 1.34 0 0.68 0 0 Z " fill="#763E34" transform="translate(387,281)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C4.33 1.34 4.66 0.68 5 0 C5.66 0.66 6.32 1.32 7 2 C9.571 2.648 9.571 2.648 12 3 C11.531 31.763 11.531 31.763 3 42 C2.34 42 1.68 42 1 42 C1.969 40.04 2.949 38.094 4 36.176 C9.506 25.916 9.46 15.422 10 4 C0.1 4 -9.8 4 -20 4 C-20 6.97 -20 9.94 -20 13 C-20.551 15.777 -20.551 15.777 -21.312 17.938 C-21.678 19.034 -21.678 19.034 -22.051 20.152 C-23 22 -23 22 -26 24 C-28.375 24.23 -28.375 24.23 -31 24.188 C-31.866 24.181 -32.733 24.175 -33.625 24.168 C-36 24 -36 24 -39 23 C-39 22.34 -39 21.68 -39 21 C-38.484 21.165 -37.969 21.33 -37.438 21.5 C-34.223 22.159 -31.272 22.182 -28 22 C-25.947 20.158 -25.947 20.158 -25 18 C-24.34 18.33 -23.68 18.66 -23 19 C-22.67 13.72 -22.34 8.44 -22 3 C-20.68 3 -19.36 3 -18 3 C-17.67 2.34 -17.34 1.68 -17 1 C-16.013 1.084 -15.025 1.168 -14.008 1.254 C-12.726 1.356 -11.445 1.458 -10.125 1.562 C-8.849 1.667 -7.573 1.771 -6.258 1.879 C-3.114 2.257 -3.114 2.257 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8B4C41" transform="translate(294,187)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.33 1.34 1.66 0.68 2 0 C3.98 0 5.96 0 8 0 C8 0.66 8 1.32 8 2 C8.66 2 9.32 2 10 2 C9.34 3.98 8.68 5.96 8 8 C8.66 8 9.32 8 10 8 C10 8.66 10 9.32 10 10 C9.34 10 8.68 10 8 10 C8 14.62 8 19.24 8 24 C8.66 24 9.32 24 10 24 C9.01 27.465 9.01 27.465 8 31 C8.66 31 9.32 31 10 31 C9.125 38.875 9.125 38.875 8 40 C6.147 40.072 4.292 40.084 2.438 40.062 C1.426 40.053 0.414 40.044 -0.629 40.035 C-1.803 40.018 -1.803 40.018 -3 40 C-3.285 39.251 -3.57 38.502 -3.863 37.73 C-4.827 35.415 -5.868 33.432 -7.188 31.312 C-7.786 30.219 -8.384 29.126 -9 28 C-8.67 27.34 -8.34 26.68 -8 26 C-6.828 27.788 -5.662 29.581 -4.5 31.375 C-3.85 32.373 -3.201 33.37 -2.531 34.398 C-1 37 -1 37 -1 39 C1.64 39 4.28 39 7 39 C7 27.12 7 15.24 7 3 C5.02 3 3.04 3 1 3 C1 11.25 1 19.5 1 28 C-1.652 26.674 -2.251 25.134 -3.688 22.562 C-4.145 21.759 -4.603 20.956 -5.074 20.129 C-5.38 19.426 -5.685 18.724 -6 18 C-5.67 17.34 -5.34 16.68 -5 16 C-4.67 16.99 -4.34 17.98 -4 19 C-2.68 19 -1.36 19 0 19 C-0.33 18.34 -0.66 17.68 -1 17 C-1.098 14.488 -1.139 12.011 -1.125 9.5 C-1.129 8.81 -1.133 8.121 -1.137 7.41 C-1.127 2.253 -1.127 2.253 0 0 Z " fill="#6C94B6" transform="translate(793,816)"/>
<path d="M0 0 C8 0 8 0 10.938 2.25 C12.76 4.68 13.67 6.033 14 9 C12.292 7.366 11.065 6.131 10 4 C7.36 3.67 4.72 3.34 2 3 C2 14.22 2 25.44 2 37 C3.98 37 5.96 37 8 37 C8 29.41 8 21.82 8 14 C8.99 14.66 9.98 15.32 11 16 C10.814 16.598 10.629 17.196 10.438 17.812 C9.779 20.164 9.779 20.164 11 23 C10.67 23.66 10.34 24.32 10 25 C10.99 25.33 11.98 25.66 13 26 C12.01 26.66 11.02 27.32 10 28 C9.636 30.437 9.636 30.437 9.812 33.125 C9.84 34.035 9.867 34.945 9.895 35.883 C9.929 36.581 9.964 37.28 10 38 C7.035 38.988 5.082 39.077 2 39 C1.01 39 0.02 39 -1 39 C-1.33 38.34 -1.66 37.68 -2 37 C-1.34 37 -0.68 37 0 37 C0 24.79 0 12.58 0 0 Z " fill="#78A0C2" transform="translate(415,872)"/>
<path d="M0 0 C6.974 -0.025 13.949 -0.043 20.923 -0.055 C23.293 -0.06 25.664 -0.067 28.034 -0.075 C31.45 -0.088 34.866 -0.093 38.281 -0.098 C39.334 -0.103 40.386 -0.108 41.47 -0.113 C47.079 -0.114 52.453 0.161 58 1 C57.67 1.66 57.34 2.32 57 3 C38.19 3 19.38 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#547FAB" transform="translate(190,972)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.855 2.299 0.855 2.299 -0.312 3.625 C-2.752 6.536 -4.43 9.529 -6 13 C-6.66 13 -7.32 13 -8 13 C-8.309 14.134 -8.619 15.269 -8.938 16.438 C-9.112 17.076 -9.286 17.714 -9.465 18.371 C-10.9 25.422 -11.144 32.476 -11.114 39.651 C-11.114 40.569 -11.114 41.487 -11.114 42.433 C-11.113 45.436 -11.106 48.439 -11.098 51.441 C-11.096 53.536 -11.094 55.631 -11.093 57.725 C-11.09 63.215 -11.08 68.704 -11.069 74.193 C-11.058 79.804 -11.054 85.415 -11.049 91.025 C-11.038 102.017 -11.021 113.008 -11 124 C-13.911 121.089 -13.324 118.053 -13.351 114.148 C-13.37 112.931 -13.37 112.931 -13.391 111.688 C-13.432 108.989 -13.459 106.289 -13.484 103.59 C-13.508 101.705 -13.533 99.821 -13.557 97.936 C-13.607 93.971 -13.65 90.005 -13.688 86.04 C-13.737 81.003 -13.804 75.968 -13.877 70.932 C-13.931 67.029 -13.975 63.126 -14.015 59.223 C-14.036 57.369 -14.06 55.516 -14.087 53.663 C-14.608 17.022 -14.608 17.022 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#783930" transform="translate(533,276)"/>
<path d="M0 0 C2.921 2.921 3.896 6.567 5.312 10.375 C5.646 11.26 5.979 12.144 6.322 13.055 C12.033 28.314 17.494 43.667 23 59 C37.85 59.495 37.85 59.495 53 60 C53 58.35 53 56.7 53 55 C53.66 55 54.32 55 55 55 C55 57.31 55 59.62 55 62 C44.11 62 33.22 62 22 62 C20.144 57.072 18.291 52.143 16.446 47.211 C15.817 45.533 15.187 43.856 14.554 42.18 C13.646 39.771 12.744 37.361 11.844 34.949 C11.559 34.199 11.275 33.449 10.982 32.676 C9.858 29.65 9 27.256 9 24 C8.34 23.67 7.68 23.34 7 23 C6.13 21.098 6.13 21.098 5.273 18.633 C4.964 17.747 4.655 16.862 4.336 15.949 C3.86 14.551 3.86 14.551 3.375 13.125 C2.888 11.739 2.888 11.739 2.391 10.324 C0 3.403 0 3.403 0 0 Z " fill="#82443A" transform="translate(657,177)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-0.936 5.636 -4.133 6.461 -9.25 7.938 C-10.158 8.288 -11.065 8.639 -12 9 C-12.33 9.99 -12.66 10.98 -13 12 C-13.784 11.814 -14.567 11.629 -15.375 11.438 C-17.971 10.74 -17.971 10.74 -20 12 C-23.316 12.088 -26.626 11.856 -29.938 11.688 C-30.857 11.652 -31.777 11.617 -32.725 11.58 C-33.608 11.537 -34.492 11.494 -35.402 11.449 C-36.212 11.411 -37.021 11.374 -37.855 11.335 C-40 11 -40 11 -43 9 C-44.548 8.417 -46.112 7.877 -47.688 7.375 C-48.496 7.115 -49.304 6.854 -50.137 6.586 C-50.752 6.393 -51.366 6.199 -52 6 C-51.67 5.01 -51.34 4.02 -51 3 C-49.02 3.66 -47.04 4.32 -45 5 C-45 5.66 -45 6.32 -45 7 C-44.227 6.722 -44.227 6.722 -43.438 6.438 C-40.856 5.974 -38.885 6.312 -36.324 6.734 C-28.631 7.614 -21.296 7.67 -14 5 C-10.877 4.223 -7.746 3.499 -4.609 2.781 C-1.869 2.186 -1.869 2.186 0 0 Z " fill="#C06560" transform="translate(593,419)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C9.292 1.213 12.583 1.376 15.879 1.523 C20.228 2.188 22.924 3.775 25.812 7.062 C27.557 11.379 27.715 15.409 27 20 C25 23.125 25 23.125 23 25 C22.34 24.67 21.68 24.34 21 24 C21.474 23.567 21.949 23.134 22.438 22.688 C24.797 20.139 24.997 18.047 25.25 14.625 C24.979 10.695 24.55 8.976 22 6 C14.675 3.278 14.675 3.278 1 3 C1 14.88 1 26.76 1 39 C3.31 39 5.62 39 8 39 C8 34.71 8 30.42 8 26 C9.32 26.33 10.64 26.66 12 27 C11.505 28.268 11.505 28.268 11 29.562 C9.939 33.21 9.849 36.223 10 40 C6.04 40 2.08 40 -2 40 C-2.33 39.34 -2.66 38.68 -3 38 C-2.34 37.34 -1.68 36.68 -1 36 C-0.833 32.417 -0.833 32.417 -1 29 C-1.66 28.67 -2.32 28.34 -3 28 C-3.25 22.375 -3.25 22.375 -1 19 C-1.33 19 -1.66 19 -2 19 C-2 17.35 -2 15.7 -2 14 C-1.67 14 -1.34 14 -1 14 C-0.67 9.38 -0.34 4.76 0 0 Z M9 27 C10 29 10 29 10 29 Z " fill="#7097B9" transform="translate(287,816)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.508 1.727 7.006 3.457 7.5 5.188 C7.778 6.15 8.057 7.113 8.344 8.105 C8.974 10.885 9.235 13.166 9 16 C8 17.333 7 18.667 6 20 C5.749 22.668 5.749 22.668 6 25 C3.048 22.048 2.913 19.045 2 15 C-1.479 17.319 -2.294 19.264 -4 23 C-4.33 22.34 -4.66 21.68 -5 21 C-3.846 17.978 -2.483 15.078 -1.129 12.141 C0.048 8.867 0.212 6.448 0 3 C-1.65 3 -3.3 3 -5 3 C-5 2.67 -5 2.34 -5 2 C-3.35 2 -1.7 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8FBCE2" transform="translate(345,877)"/>
<path d="M0 0 C0.227 0.969 0.454 1.939 0.688 2.938 C1.829 7.05 3.86 10.458 6.074 14.082 C6.38 14.715 6.685 15.348 7 16 C6.67 16.66 6.34 17.32 6 18 C5.515 17.001 5.031 16.002 4.531 14.973 C3.856 13.668 3.179 12.365 2.5 11.062 C2.185 10.403 1.871 9.744 1.547 9.064 C1.212 8.436 0.877 7.808 0.531 7.16 C0.238 6.579 -0.055 5.998 -0.357 5.399 C-2.666 3.173 -2.666 3.173 -10 3 C-10 14.88 -10 26.76 -10 39 C-8.02 39 -6.04 39 -4 39 C-4 31.08 -4 23.16 -4 15 C-3.34 15.33 -2.68 15.66 -2 16 C-2.023 17.314 -2.046 18.627 -2.07 19.98 C-2.089 21.716 -2.107 23.452 -2.125 25.188 C-2.142 26.052 -2.159 26.916 -2.176 27.807 C-2.185 29.068 -2.185 29.068 -2.195 30.355 C-2.206 31.125 -2.216 31.895 -2.227 32.688 C-2.056 35.534 -2.056 35.534 0 40 C-4.29 40 -8.58 40 -13 40 C-12.67 38.35 -12.34 36.7 -12 35 C-12.289 32.769 -12.289 32.769 -12.938 30.688 C-13.463 28.862 -13.463 28.862 -14 27 C-13.34 27 -12.68 27 -12 27 C-12 23.37 -12 19.74 -12 16 C-12.33 16 -12.66 16 -13 16 C-13 14.35 -13 12.7 -13 11 C-12.67 11 -12.34 11 -12 11 C-12.031 9.36 -12.031 9.36 -12.062 7.688 C-12 4 -12 4 -11 1 C-7.51 -0.477 -3.726 -0.072 0 0 Z M-6 1 C-6 1.33 -6 1.66 -6 2 C-4.35 2 -2.7 2 -1 2 C-1 1.67 -1 1.34 -1 1 C-2.65 1 -4.3 1 -6 1 Z " fill="#739BBD" transform="translate(487,816)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.172 10.562 1.338 21.125 1.5 31.688 C1.512 32.463 1.524 33.239 1.536 34.039 C1.896 57.694 2.162 81.342 2 105 C-2.93 105.025 -7.859 105.043 -12.789 105.055 C-14.466 105.06 -16.143 105.067 -17.82 105.075 C-20.23 105.088 -22.641 105.093 -25.051 105.098 C-25.801 105.103 -26.551 105.108 -27.324 105.113 C-30.49 105.114 -32.973 105.009 -36 104 C-35.67 103.01 -35.34 102.02 -35 101 C-34.67 101.66 -34.34 102.32 -34 103 C-22.78 103 -11.56 103 0 103 C0 69.01 0 35.02 0 0 Z " fill="#C3654C" transform="translate(598,135)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C4.276 2.47 4.276 2.47 8.062 2.625 C9.998 2.737 9.998 2.737 11.973 2.852 C13.471 2.925 13.471 2.925 15 3 C15.365 5.061 15.717 7.124 16.062 9.188 C16.358 10.91 16.358 10.91 16.66 12.668 C16.978 15.786 16.808 17.998 16 21 C14.583 20.688 13.167 20.376 11.75 20.062 C10.961 19.888 10.172 19.714 9.359 19.535 C7.926 19.21 6.498 18.854 5.086 18.445 C2.326 17.856 -0.305 17.83 -3.125 17.812 C-4.707 17.791 -4.707 17.791 -6.32 17.77 C-9.153 17.723 -9.153 17.723 -11 20 C-10.647 23.442 -9.954 25.069 -8 28 C-11.875 26.125 -11.875 26.125 -13 25 C-13.492 21.309 -13.478 19.626 -11.188 16.625 C-5.511 13.731 0.118 14.174 6.16 15.906 C8.802 16.861 11.406 17.922 14 19 C14 14.71 14 10.42 14 6 C2.975 2.85 -8.975 2.85 -20 6 C-17.791 3.452 -16.309 2.575 -12.988 2.051 C-12.231 1.925 -11.474 1.799 -10.693 1.67 C-9.908 1.552 -9.122 1.434 -8.312 1.312 C-7.515 1.183 -6.718 1.053 -5.896 0.92 C-3.933 0.602 -1.967 0.3 0 0 Z " fill="#658CAD" transform="translate(254,624)"/>
<path d="M0 0 C1.041 0 2.082 0 3.154 0 C4.276 0.011 5.398 0.021 6.554 0.032 C7.704 0.035 8.854 0.037 10.039 0.04 C13.717 0.052 17.395 0.077 21.073 0.102 C23.565 0.112 26.057 0.121 28.548 0.129 C34.661 0.151 40.773 0.185 46.886 0.227 C46.886 0.887 46.886 1.547 46.886 2.227 C53.156 2.227 59.426 2.227 65.886 2.227 C65.886 2.557 65.886 2.887 65.886 3.227 C41.197 3.357 16.56 3.1 -8.114 2.227 C-4.809 0.024 -3.837 -0.019 0 0 Z " fill="#638FB9" transform="translate(813.114013671875,764.77294921875)"/>
<path d="M0 0 C2.312 -0.027 4.625 -0.046 6.938 -0.062 C8.225 -0.074 9.513 -0.086 10.84 -0.098 C14 0 14 0 15 1 C15.087 2.489 15.107 3.981 15.098 5.473 C15.094 6.372 15.091 7.271 15.088 8.197 C15.08 9.143 15.071 10.088 15.062 11.062 C15.058 12.012 15.053 12.961 15.049 13.939 C15.037 16.293 15.021 18.646 15 21 C-1.775 22.614 -1.775 22.614 -7 22 C-7.66 21.34 -8.32 20.68 -9 20 C-8.359 20.023 -7.719 20.046 -7.059 20.07 C-2.785 20.159 0.617 20.115 4.75 18.875 C8 18 8 18 14 20 C13.67 14.06 13.34 8.12 13 2 C9.04 2 5.08 2 1 2 C1.33 3.65 1.66 5.3 2 7 C3.65 7 5.3 7 7 7 C6.688 9.875 6.688 9.875 6 13 C2.356 15.429 0.288 15.162 -4 15 C-4.33 14.34 -4.66 13.68 -5 13 C-3.68 13 -2.36 13 -1 13 C-1.33 12.01 -1.66 11.02 -2 10 C-1.34 9.34 -0.68 8.68 0 8 C-0.99 6.68 -1.98 5.36 -3 4 C-2.67 3.34 -2.34 2.68 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z M-1 3 C0 5 0 5 0 5 Z M4 10 C3.01 10.495 3.01 10.495 2 11 C2.33 11.66 2.66 12.32 3 13 C3.66 12.67 4.32 12.34 5 12 C4.67 11.34 4.34 10.68 4 10 Z " fill="#7DA5C8" transform="translate(405,835)"/>
<path d="M0 0 C9.24 0 18.48 0 28 0 C29.642 3.283 28.519 6.425 28 10 C27.67 10 27.34 10 27 10 C27 7.36 27 4.72 27 2 C18.42 2 9.84 2 1 2 C1.807 22.142 1.807 22.142 11.074 39.43 C14.98 42.614 19.486 44.807 24 47 C19.819 48.394 17.965 46.709 14 45 C11.207 44.746 11.207 44.746 9 45 C9.124 43.917 9.124 43.917 9.25 42.812 C8.923 39.131 7.331 37.811 5 35 C3.75 31.688 3.75 31.688 3 29 C2.01 29 1.02 29 0 29 C0.183 28.486 0.366 27.971 0.555 27.441 C1.163 24.109 0.638 21.279 0.125 17.938 C-0.058 16.709 -0.241 15.481 -0.43 14.215 C-0.92 10.997 -0.92 10.997 -2 8 C-1.34 5.36 -0.68 2.72 0 0 Z " fill="#754D51" transform="translate(312,379)"/>
<path d="M0 0 C2.43 2.43 2.234 3.694 2.249 7.008 C2.242 7.703 2.234 8.397 2.227 9.112 C2.227 9.844 2.228 10.575 2.228 11.329 C2.227 13.754 2.211 16.177 2.195 18.602 C2.192 20.28 2.189 21.959 2.187 23.638 C2.179 28.06 2.159 32.482 2.137 36.904 C2.117 41.415 2.108 45.926 2.098 50.438 C2.076 59.292 2.042 68.146 2 77 C1.34 77 0.68 77 0 77 C-0.964 74.502 -1.921 72.002 -2.875 69.5 C-3.279 68.456 -3.279 68.456 -3.691 67.391 C-5.492 62.647 -6.853 57.944 -8 53 C-6 54 -6 54 -1 66 C-0.67 44.22 -0.34 22.44 0 0 Z " fill="#A34F36" transform="translate(678,78)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 0.66 5.34 1.32 5 2 C4.914 4.408 4.884 6.787 4.902 9.195 C4.904 9.903 4.905 10.61 4.907 11.339 C4.912 13.601 4.925 15.863 4.938 18.125 C4.943 19.658 4.947 21.19 4.951 22.723 C4.962 26.482 4.979 30.241 5 34 C3.68 34 2.36 34 1 34 C0.67 22.78 0.34 11.56 0 0 Z " fill="#89B4DA" transform="translate(498,874)"/>
<path d="M0 0 C24.255 0.495 24.255 0.495 49 1 C49 1.66 49 2.32 49 3 C38.377 4.054 27.851 4.119 17.188 4.062 C15.512 4.057 13.837 4.053 12.162 4.049 C8.108 4.038 4.054 4.021 0 4 C0 2.68 0 1.36 0 0 Z " fill="#517DAA" transform="translate(79,764)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.697 4.341 1.362 5.674 1 7 C1.324 8.336 1.657 9.669 2 11 C1.67 11.99 1.34 12.98 1 14 C0.916 16.049 0.893 18.101 0.902 20.152 C0.906 21.42 0.909 22.688 0.912 23.995 C0.919 25.011 0.919 25.011 0.925 26.047 C0.938 28.17 0.944 30.293 0.949 32.415 C0.962 37.688 0.987 42.96 1.01 48.232 C1.03 52.687 1.046 57.142 1.056 61.598 C1.062 63.687 1.075 65.777 1.088 67.866 C1.091 69.137 1.094 70.409 1.098 71.719 C1.103 72.838 1.108 73.957 1.114 75.109 C1.016 77.595 0.727 79.635 0 82 C0.99 82.33 1.98 82.66 3 83 C-0.832 84.777 -3.859 85.257 -8.07 85.266 C-9.214 85.268 -10.357 85.271 -11.535 85.273 C-13.312 85.262 -13.312 85.262 -15.125 85.25 C-16.914 85.262 -16.914 85.262 -18.738 85.273 C-19.879 85.271 -21.02 85.268 -22.195 85.266 C-23.237 85.263 -24.279 85.261 -25.352 85.259 C-28 85 -28 85 -31 83 C-21.1 83 -11.2 83 -1 83 C-1.008 79.335 -1.008 79.335 -1.016 75.596 C-1.032 67.538 -1.043 59.479 -1.052 51.42 C-1.057 46.533 -1.064 41.645 -1.075 36.758 C-1.086 32.044 -1.092 27.33 -1.095 22.616 C-1.097 20.815 -1.1 19.014 -1.106 17.212 C-1.113 14.696 -1.114 12.179 -1.114 9.663 C-1.117 8.912 -1.121 8.162 -1.124 7.389 C-1.114 2.228 -1.114 2.228 0 0 Z " fill="#804439" transform="translate(649,155)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 13.53 1 27.06 1 41 C5.95 41 10.9 41 16 41 C16 41.33 16 41.66 16 42 C10.72 42 5.44 42 0 42 C-1.305 39.39 -1.097 37.501 -1.062 34.582 C-1.055 33.492 -1.047 32.402 -1.039 31.279 C-1.013 28.988 -0.987 26.696 -0.961 24.404 C-0.953 23.308 -0.945 22.212 -0.938 21.082 C-0.926 20.084 -0.914 19.086 -0.902 18.058 C-1.01 14.687 -1.523 11.339 -2 8 C-5.3 14.6 -8.6 21.2 -12 28 C-15.3 28 -18.6 28 -22 28 C-22 27.67 -22 27.34 -22 27 C-19.03 27 -16.06 27 -13 27 C-12.763 26.199 -12.526 25.399 -12.281 24.574 C-10.688 20.129 -8.635 16.018 -6.5 11.812 C-6.097 11.006 -5.693 10.2 -5.277 9.369 C-3.637 6.113 -2.026 3.039 0 0 Z " fill="#6286A8" transform="translate(736,654)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C6.588 3.175 5.803 6.538 5.562 10 C4.975 19.65 4.951 29.275 5 38.938 C5.06 51.657 4.817 64.302 4 77 C3.67 77 3.34 77 3 77 C3 51.92 3 26.84 3 1 C2.01 0.67 1.02 0.34 0 0 Z " fill="#984A31" transform="translate(594,78)"/>
<path d="M0 0 C0.76 -0.004 1.52 -0.008 2.303 -0.012 C6.231 -0.005 9.852 0.161 13.688 1.125 C13.688 3.765 13.688 6.405 13.688 9.125 C12.697 9.455 11.707 9.785 10.688 10.125 C10.028 9.465 9.367 8.805 8.688 8.125 C10.008 8.125 11.327 8.125 12.688 8.125 C12.688 6.145 12.688 4.165 12.688 2.125 C11.697 2.455 10.707 2.785 9.688 3.125 C8.206 3.206 6.722 3.233 5.238 3.223 C4.474 3.22 3.71 3.218 2.923 3.215 C1.958 3.206 0.994 3.197 0 3.188 C-4.61 3.157 -4.61 3.157 -9.312 3.125 C-9.312 14.345 -9.312 25.565 -9.312 37.125 C-4.033 37.125 1.247 37.125 6.688 37.125 C6.688 37.455 6.688 37.785 6.688 38.125 C0.747 38.125 -5.192 38.125 -11.312 38.125 C-11.643 38.785 -11.972 39.445 -12.312 40.125 C-13.609 35.514 -13.024 31.787 -12.312 27.125 C-11.982 27.125 -11.653 27.125 -11.312 27.125 C-10.982 18.545 -10.653 9.965 -10.312 1.125 C-7.077 -0.493 -3.55 -0.018 0 0 Z " fill="#749CBE" transform="translate(542.3125,871.875)"/>
<path d="M0 0 C2.78 7.84 5.432 15.583 5 24 C4.67 24 4.34 24 4 24 C3.353 21.271 2.707 18.542 2.062 15.812 C1.88 15.044 1.698 14.275 1.51 13.482 C1.333 12.732 1.155 11.981 0.973 11.207 C0.729 10.178 0.729 10.178 0.481 9.128 C-0.054 6.761 -0.524 4.38 -1 2 C-7.27 2 -13.54 2 -20 2 C-20 13.55 -20 25.1 -20 37 C-20.99 36.67 -21.98 36.34 -23 36 C-22.838 35.296 -22.675 34.593 -22.508 33.868 C-21.98 30.888 -21.871 28.138 -21.867 25.113 C-21.866 24.01 -21.865 22.908 -21.863 21.771 C-21.867 20.63 -21.871 19.489 -21.875 18.312 C-21.871 17.159 -21.867 16.006 -21.863 14.818 C-21.865 13.719 -21.866 12.621 -21.867 11.488 C-21.868 10.483 -21.869 9.478 -21.871 8.443 C-21.837 5.992 -21.837 5.992 -23 4 C-22.67 3.67 -22.34 3.34 -22 3 C-22 2.01 -22 1.02 -22 0 C-14.468 -0.757 -7.497 -1.144 0 0 Z " fill="#779FC2" transform="translate(351,873)"/>
<path d="M0 0 C3.608 3.608 5.879 5.999 8.375 10.188 C8.898 11.047 9.422 11.907 9.961 12.793 C10.304 13.521 10.647 14.25 11 15 C10.67 15.66 10.34 16.32 10 17 C8.68 16.34 7.36 15.68 6 15 C6.33 14.34 6.66 13.68 7 13 C6.108 10.883 6.108 10.883 5 9 C2.603 12.596 2.76 14.301 2.812 18.562 C2.819 19.719 2.825 20.875 2.832 22.066 C2.802 24.934 2.802 24.934 4 27 C3.34 27 2.68 27 2 27 C2.141 29.292 2.288 31.584 2.438 33.875 C2.559 35.789 2.559 35.789 2.684 37.742 C2.792 40.883 2.792 40.883 4 43 C2.68 42.67 1.36 42.34 0 42 C0 28.14 0 14.28 0 0 Z M2 5 C3 7 3 7 3 7 Z M2 8 C3 10 3 10 3 10 Z " fill="#6C94B8" transform="translate(547,653)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.199 3.645 1.382 7.291 1.562 10.938 C1.647 12.47 1.647 12.47 1.732 14.033 C1.993 19.478 2.003 24.663 1.234 30.065 C0.92 32.337 0.92 32.337 1 36 C1 36.99 1 37.98 1 39 C-1.005 41.005 -5 40.256 -7.754 40.316 C-8.917 40.348 -8.917 40.348 -10.104 40.379 C-12.59 40.445 -15.076 40.504 -17.562 40.562 C-19.244 40.606 -20.926 40.649 -22.607 40.693 C-26.738 40.801 -30.869 40.902 -35 41 C-35.495 39.515 -35.495 39.515 -36 38 C-24.45 38 -12.9 38 -1 38 C-0.67 25.46 -0.34 12.92 0 0 Z " fill="#965246" transform="translate(473,199)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 2.31 2.66 4.62 3 7 C3.66 7 4.32 7 5 7 C5 6.34 5 5.68 5 5 C5.66 5 6.32 5 7 5 C6.67 6.32 6.34 7.64 6 9 C5.34 9 4.68 9 4 9 C4.33 9.99 4.66 10.98 5 12 C5.66 12 6.32 12 7 12 C7 12.66 7 13.32 7 14 C6.01 14 5.02 14 4 14 C4 14.66 4 15.32 4 16 C4.66 16 5.32 16 6 16 C6 17.32 6 18.64 6 20 C6.66 18.68 7.32 17.36 8 16 C8.99 16.33 9.98 16.66 11 17 C10.67 19.31 10.34 21.62 10 24 C9.01 24.33 8.02 24.66 7 25 C5.791 27 5.791 27 5 29 C4.795 28.301 4.59 27.603 4.379 26.883 C4.109 25.973 3.84 25.063 3.562 24.125 C3.296 23.22 3.029 22.315 2.754 21.383 C2.074 19.017 2.074 19.017 1 17 C0.96 15 0.957 13 1 11 C0.34 10.67 -0.32 10.34 -1 10 C-0.74 7.982 -0.479 5.964 -0.219 3.945 C0.037 1.995 0.037 1.995 0 0 Z M7 21 C8 23 8 23 8 23 Z " fill="#446287" transform="translate(913,7)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.751 1.794 -0.499 2.585 -1.75 3.375 C-2.446 3.816 -3.142 4.257 -3.859 4.711 C-5.865 5.919 -7.892 6.984 -10 8 C-9.227 8.413 -8.453 8.825 -7.656 9.25 C6.274 16.974 14.446 27.105 20 42 C20.66 42 21.32 42 22 42 C21.67 43.32 21.34 44.64 21 46 C20.01 45.67 19.02 45.34 18 45 C16.859 42.41 16.859 42.41 15.75 39.062 C13.373 32.541 10.331 27.396 6 22 C5.278 21.092 4.556 20.185 3.812 19.25 C-2.279 14.377 -9.6 11.327 -17 9 C-13.746 6.17 -10.143 4.638 -6.219 2.965 C-3.978 2.031 -3.978 2.031 -1.844 0.816 C-0.931 0.412 -0.931 0.412 0 0 Z " fill="#8396A7" transform="translate(850,333)"/>
<path d="M0 0 C3.766 1.859 7.029 4.029 10 7 C9.67 7.99 9.34 8.98 9 10 C8.423 9.216 7.845 8.433 7.25 7.625 C4.577 4.506 2.705 3.242 -1.367 2.664 C-7.076 2.575 -10.624 2.944 -15 7 C-19.075 13.708 -18.774 20.368 -18 28 C-16.98 31.114 -15.805 33.236 -14 36 C-17 35 -17 35 -18.688 31.938 C-19.121 30.968 -19.554 29.999 -20 29 C-20.33 31.97 -20.66 34.94 -21 38 C-21.33 38 -21.66 38 -22 38 C-22.237 15.315 -22.237 15.315 -21 11 C-20.01 10.34 -19.02 9.68 -18 9 C-17.608 8.216 -17.216 7.433 -16.812 6.625 C-12.975 1.068 -6.578 -1.057 0 0 Z " fill="#7199BB" transform="translate(631,872)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C4.408 5.673 5.433 10.121 5.341 16.252 C5.341 17.033 5.341 17.815 5.342 18.62 C5.34 21.174 5.317 23.727 5.293 26.281 C5.287 28.063 5.283 29.846 5.28 31.628 C5.269 36.297 5.239 40.965 5.206 45.634 C5.175 50.407 5.162 55.18 5.146 59.953 C5.114 69.302 5.063 78.651 5 88 C4.67 88 4.34 88 4 88 C3.34 85.03 2.68 82.06 2 79 C2.33 79 2.66 79 3 79 C3.029 75.542 3.047 72.083 3.062 68.625 C3.071 67.64 3.079 66.655 3.088 65.641 C3.093 64.229 3.093 64.229 3.098 62.789 C3.103 61.92 3.108 61.05 3.114 60.155 C3.141 57.95 3.141 57.95 2 56 C2.66 56 3.32 56 4 56 C3.649 54.742 3.299 53.484 2.938 52.188 C2.064 49.053 1.928 48.217 3 45 C2.34 44.67 1.68 44.34 1 44 C1.33 42.35 1.66 40.7 2 39 C2.33 39 2.66 39 3 39 C3.059 34.875 3.094 30.75 3.125 26.625 C3.142 25.469 3.159 24.312 3.176 23.121 C3.221 15.048 2.56 7.679 0 0 Z " fill="#7A3D31" transform="translate(465,301)"/>
<path d="M0 0 C12.667 0 12.667 0 17 3 C19.829 7.244 19.652 11.017 19 16 C17.25 19.062 17.25 19.062 15 21 C11 22 11 22 0 22 C0 14.74 0 7.48 0 0 Z M1 2 C1.33 2.66 1.66 3.32 2 4 C1.67 4 1.34 4 1 4 C1 8.95 1 13.9 1 19 C1.66 18.67 2.32 18.34 3 18 C3.33 18.99 3.66 19.98 4 21 C7.996 20.752 11.762 20.495 15.5 19 C17.986 15.686 18.193 12.068 18 8 C16.936 4.999 16.936 4.999 15 3 C11.851 1.95 9.612 1.899 6.312 1.938 C5.319 1.947 4.325 1.956 3.301 1.965 C2.542 1.976 1.782 1.988 1 2 Z " fill="#668DAF" transform="translate(459,640)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C13.125 6.625 13.125 6.625 12 10 C7.545 10.495 7.545 10.495 3 11 C2.67 12.65 2.34 14.3 2 16 C6.29 16 10.58 16 15 16 C15 18.31 15 20.62 15 23 C14.67 23 14.34 23 14 23 C14 21.35 14 19.7 14 18 C9.05 18 4.1 18 -1 18 C-1 14.7 -1 11.4 -1 8 C2.96 8 6.92 8 11 8 C10.505 5.525 10.505 5.525 10 3 C6.37 3 2.74 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z M0 10 C0 11.98 0 13.96 0 16 C0.33 16 0.66 16 1 16 C1 14.02 1 12.04 1 10 C0.67 10 0.34 10 0 10 Z " fill="#7CA4C6" transform="translate(342,832)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.959 6.013 5.416 11.696 6 18 C5.01 18.33 4.02 18.66 3 19 C3 26.26 3 33.52 3 41 C2.01 41 1.02 41 0 41 C0 27.47 0 13.94 0 0 Z " fill="#648DB1" transform="translate(699,654)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 25.41 1.66 50.82 2 77 C-0.562 78.281 -2.403 78.129 -5.27 78.133 C-6.85 78.135 -6.85 78.135 -8.463 78.137 C-10.121 78.131 -10.121 78.131 -11.812 78.125 C-13.453 78.131 -13.453 78.131 -15.127 78.137 C-16.713 78.135 -16.713 78.135 -18.332 78.133 C-19.299 78.132 -20.265 78.131 -21.261 78.129 C-23.939 78.003 -26.391 77.598 -29 77 C-29 76.67 -29 76.34 -29 76 C-19.43 76 -9.86 76 0 76 C0 50.92 0 25.84 0 0 Z " fill="#5B373A" transform="translate(656,349)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.092 1.949 0.185 2.898 -0.75 3.875 C-9.079 13.543 -9.679 25.642 -9.175 37.812 C-8.464 46.704 -4.195 52.906 2 59 C2.566 59.567 3.132 60.134 3.715 60.719 C4.139 61.142 4.563 61.564 5 62 C-0.645 60.632 -3.686 56.474 -7 52 C-10.525 45.801 -11.108 38.976 -12 32 C-12.66 32 -13.32 32 -14 32 C-13.67 30.68 -13.34 29.36 -13 28 C-12.34 28 -11.68 28 -11 28 C-11.062 26.515 -11.062 26.515 -11.125 25 C-11.143 22.052 -10.712 19.53 -9.938 16.688 C-8.625 13.206 -8.625 13.206 -10 11 C-9.01 11 -8.02 11 -7 11 C-6.773 10.299 -6.546 9.597 -6.312 8.875 C-4.683 5.307 -2.594 2.935 0 0 Z M-13 29 C-12 31 -12 31 -12 31 Z " fill="#5F85A6" transform="translate(370,632)"/>
<path d="M0 0 C1.32 0.99 2.64 1.98 4 3 C3.34 3 2.68 3 2 3 C2.33 3.99 2.66 4.98 3 6 C4.65 6 6.3 6 8 6 C8 6.66 8 7.32 8 8 C9.224 8.075 9.224 8.075 11 7 C12.32 7.99 13.64 8.98 15 10 C14.67 10.66 14.34 11.32 14 12 C13.835 12.825 13.67 13.65 13.5 14.5 C13 17 13 17 12 19 C11.01 19 10.02 19 9 19 C8.67 19.66 8.34 20.32 8 21 C8 19.68 8 18.36 8 17 C6.68 17 5.36 17 4 17 C4 18.65 4 20.3 4 22 C2.68 22.33 1.36 22.66 0 23 C0 20.36 0 17.72 0 15 C1.437 14.973 2.875 14.954 4.312 14.938 C5.513 14.92 5.513 14.92 6.738 14.902 C9 15 9 15 12 16 C12 14.02 12 12.04 12 10 C11.218 10.012 10.435 10.023 9.629 10.035 C8.617 10.044 7.605 10.053 6.562 10.062 C5.553 10.074 4.544 10.086 3.504 10.098 C1 10 1 10 0 9 C-0.072 7.481 -0.084 5.958 -0.062 4.438 C-0.053 3.611 -0.044 2.785 -0.035 1.934 C-0.024 1.296 -0.012 0.657 0 0 Z M2 16 C3 20 3 20 3 20 Z " fill="#779FC4" transform="translate(815,825)"/>
<path d="M0 0 C2 2 2 2 2.227 4.889 C2.217 6.094 2.206 7.298 2.195 8.539 C2.189 9.841 2.182 11.143 2.176 12.484 C2.159 13.865 2.142 15.245 2.125 16.625 C2.115 18.013 2.106 19.401 2.098 20.789 C2.074 24.193 2.041 27.596 2 31 C2.66 31 3.32 31 4 31 C4 31.99 4 32.98 4 34 C4.99 33.505 4.99 33.505 6 33 C6.227 39.746 6.417 46.585 4 53 C3.01 53.495 3.01 53.495 2 54 C2.164 53.305 2.327 52.61 2.496 51.895 C3.041 48.765 3.097 45.862 3.062 42.688 C3.053 41.619 3.044 40.55 3.035 39.449 C3.024 38.641 3.012 37.833 3 37 C2.34 36.67 1.68 36.34 1 36 C-0.039 31.608 -0.117 27.349 -0.098 22.852 C-0.096 21.865 -0.096 21.865 -0.093 20.859 C-0.088 18.781 -0.075 16.703 -0.062 14.625 C-0.057 13.207 -0.053 11.789 -0.049 10.371 C-0.038 6.914 -0.021 3.457 0 0 Z " fill="#9E513D" transform="translate(775,154)"/>
<path d="M0 0 C11.22 0 22.44 0 34 0 C33.67 0.99 33.34 1.98 33 3 C16.665 3.495 16.665 3.495 0 4 C0 2.68 0 1.36 0 0 Z " fill="#4873A0" transform="translate(79,972)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 24.09 1 48.18 1 73 C0.34 72.67 -0.32 72.34 -1 72 C-0.67 63.09 -0.34 54.18 0 45 C-0.99 44.67 -1.98 44.34 -3 44 C-2.34 43.34 -1.68 42.68 -1 42 C-0.758 40.019 -0.758 40.019 -0.875 37.875 C-0.916 36.596 -0.957 35.317 -1 34 C-1.33 34 -1.66 34 -2 34 C-2 32.02 -2 30.04 -2 28 C-1.67 28 -1.34 28 -1 28 C-1 24.7 -1 21.4 -1 18 C-1.33 18 -1.66 18 -2 18 C-2.33 16.02 -2.66 14.04 -3 12 C-2.34 11.67 -1.68 11.34 -1 11 C-1.021 9.928 -1.041 8.855 -1.062 7.75 C-1.014 4.852 -0.868 2.714 0 0 Z " fill="#CB6645" transform="translate(691,271)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C7.251 4.983 6.693 7.587 4.5 12.188 C2.215 16.469 2.215 16.469 3 21 C2.67 21.66 2.34 22.32 2 23 C1.01 22.67 0.02 22.34 -1 22 C-1.043 20 -1.041 18 -1 16 C-0.67 15.67 -0.34 15.34 0 15 C-0.33 15 -0.66 15 -1 15 C-1.103 9.874 -1.036 5.05 0 0 Z M3 5 C3.66 5.66 4.32 6.32 5 7 C5 6.34 5 5.68 5 5 C4.34 5 3.68 5 3 5 Z M3 7 C2.505 8.98 2.505 8.98 2 11 C2.66 9.68 3.32 8.36 4 7 C3.67 7 3.34 7 3 7 Z " fill="#769FC1" transform="translate(339,880)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.075 0.727 2.15 1.454 2.227 2.203 C3.635 12.971 7.076 22.737 15.867 29.605 C22.135 33.704 28.677 35.684 36 37 C36 37.33 36 37.66 36 38 C25.116 37.168 17.895 35.563 10.449 27.273 C9.271 25.873 8.131 24.439 7 23 C5.68 23.33 4.36 23.66 3 24 C2.066 20.99 1.956 20.133 3 17 C2.711 16.175 2.423 15.35 2.125 14.5 C0.589 9.723 0.337 4.987 0 0 Z " fill="#764B4F" transform="translate(414,391)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.061 1.109 1.121 2.217 1.184 3.359 C1.268 4.823 1.353 6.286 1.438 7.75 C1.477 8.48 1.516 9.209 1.557 9.961 C1.762 13.435 2.141 16.638 3 20 C3.065 21.582 3.086 23.167 3.062 24.75 C3.053 25.549 3.044 26.348 3.035 27.172 C3.024 27.775 3.012 28.378 3 29 C3.33 29 3.66 29 4 29 C4 30.98 4 32.96 4 35 C3.01 35 2.02 35 1 35 C-1.674 35.286 -4.334 35.65 -7 36 C-8.163 32.45 -7.924 29.442 -7.562 25.75 C-7.461 24.672 -7.359 23.595 -7.254 22.484 C-7.17 21.665 -7.086 20.845 -7 20 C-6.67 20 -6.34 20 -6 20 C-6 24.29 -6 28.58 -6 33 C-4.02 33 -2.04 33 0 33 C0 22.11 0 11.22 0 0 Z M2 32 C3 34 3 34 3 34 Z " fill="#769FC2" transform="translate(524,876)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.691 2.577 0.381 3.155 0.062 3.75 C-1.286 6.606 -1.142 8.88 -1 12 C-0.67 12 -0.34 12 0 12 C0 14.31 0 16.62 0 19 C-0.66 19 -1.32 19 -2 19 C-2.66 20.32 -3.32 21.64 -4 23 C-10.625 22.25 -10.625 22.25 -14 20 C-13.67 19.34 -13.34 18.68 -13 18 C-12.771 15.963 -12.59 13.92 -12.438 11.875 C-12.354 10.779 -12.27 9.684 -12.184 8.555 C-12.123 7.712 -12.062 6.869 -12 6 C-11.67 6 -11.34 6 -11 6 C-9.794 10.672 -9.906 15.215 -10 20 C-8.02 20 -6.04 20 -4 20 C-4.046 19.143 -4.093 18.286 -4.141 17.402 C-4.365 10.304 -4.289 5.763 0 0 Z M-2 16 C-1 18 -1 18 -1 18 Z " fill="#769FC3" transform="translate(694,889)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.249 7.651 2.091 15.274 2 23 C-0.927 23.976 -2.441 24.061 -5.449 23.879 C-6.297 23.831 -7.144 23.782 -8.018 23.732 C-8.899 23.676 -9.78 23.62 -10.688 23.562 C-11.58 23.51 -12.473 23.458 -13.393 23.404 C-15.595 23.275 -17.798 23.14 -20 23 C-19.67 22.34 -19.34 21.68 -19 21 C-13.06 21 -7.12 21 -1 21 C-1.021 19.02 -1.041 17.04 -1.062 15 C-1.068 9.89 -0.791 5.043 0 0 Z " fill="#557FAE" transform="translate(523,496)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-8.215 13.73 -8.215 13.73 -14 17 C-14.634 17.369 -15.268 17.737 -15.922 18.117 C-17.606 19.094 -19.302 20.049 -21 21 C-20.301 21.326 -19.603 21.652 -18.883 21.988 C-17.973 22.425 -17.063 22.862 -16.125 23.312 C-15.22 23.742 -14.315 24.171 -13.383 24.613 C-11 26 -11 26 -9 29 C-9.5 28.734 -10 28.469 -10.516 28.195 C-13.86 26.586 -17.3 25.238 -20.75 23.875 C-21.447 23.598 -22.145 23.321 -22.863 23.035 C-24.575 22.355 -26.287 21.677 -28 21 C-24.645 18.028 -22.406 16.678 -18 16 C-9.883 12.659 -4.794 7.191 0 0 Z " fill="#788A9B" transform="translate(120,112)"/>
<path d="M0 0 C1.146 3.437 0.899 3.911 -0.447 7.104 C-0.958 8.335 -0.958 8.335 -1.479 9.591 C-1.856 10.472 -2.233 11.354 -2.621 12.262 C-3 13.168 -3.379 14.073 -3.769 15.007 C-4.983 17.904 -6.21 20.796 -7.438 23.688 C-8.262 25.649 -9.086 27.61 -9.908 29.572 C-11.929 34.386 -13.96 39.195 -16 44 C-16.66 44 -17.32 44 -18 44 C-18.697 42.379 -19.382 40.753 -20.062 39.125 C-20.445 38.22 -20.828 37.315 -21.223 36.383 C-22 34 -22 34 -21 32 C-20.67 32.99 -20.34 33.98 -20 35 C-19.34 35.33 -18.68 35.66 -18 36 C-18.33 36.66 -18.66 37.32 -19 38 C-15.617 37.26 -15.617 37.26 -14.191 33.49 C-13.518 31.934 -12.858 30.373 -12.207 28.809 C-11.856 27.989 -11.505 27.169 -11.144 26.324 C-10.023 23.7 -8.918 21.069 -7.812 18.438 C-7.056 16.659 -6.299 14.881 -5.541 13.104 C-3.683 8.74 -1.838 4.372 0 0 Z " fill="#8D4430" transform="translate(511,80)"/>
<path d="M0 0 C4.574 2.244 7.233 4.794 9.312 9.379 C10.892 15.402 11.39 23.669 8.977 29.453 C8.654 29.964 8.332 30.474 8 31 C6.721 28.443 7.288 27.714 8 25 C8.537 18.348 8.583 11.835 5 6 C1.328 2.234 -1.86 1.703 -6.938 1.5 C-12.421 1.664 -15.596 2.797 -20 6 C-20 5.34 -20 4.68 -20 4 C-20.99 4 -21.98 4 -23 4 C-22.67 3.34 -22.34 2.68 -22 2 C-21.34 2.082 -20.68 2.165 -20 2.25 C-16.898 1.992 -15.845 1.422 -13.25 -0.125 C-8.439 -2.685 -4.946 -1.927 0 0 Z " fill="#6A91B2" transform="translate(755,818)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-5.479 7.778 -13.305 8.531 -23 9 C-23.33 10.32 -23.66 11.64 -24 13 C-24.66 13 -25.32 13 -26 13 C-26.33 18.28 -26.66 23.56 -27 29 C-32.61 29 -38.22 29 -44 29 C-44 28.67 -44 28.34 -44 28 C-38.72 28 -33.44 28 -28 28 C-28 21.07 -28 14.14 -28 7 C-25.752 6.918 -23.504 6.835 -21.188 6.75 C-13.834 6.354 -5.425 5.425 0 0 Z " fill="#5C82A3" transform="translate(487,667)"/>
<path d="M0 0 C5.537 -0.426 9.897 1.095 15 3 C14.836 3.674 14.673 4.348 14.504 5.043 C13.912 8.514 13.905 11.793 13.938 15.312 C13.947 16.567 13.956 17.821 13.965 19.113 C13.976 20.066 13.988 21.019 14 22 C15.98 22 17.96 22 20 22 C20.33 23.65 20.66 25.3 21 27 C19.68 27 18.36 27 17 27 C16.67 25.68 16.34 24.36 16 23 C14.68 23 13.36 23 12 23 C12.012 22.371 12.023 21.742 12.035 21.094 C12.093 15.623 11.884 10.404 11 5 C9.26 3.26 6.806 3.628 4.438 3.438 C3.426 3.354 2.414 3.27 1.371 3.184 C0.197 3.093 0.197 3.093 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#9D513C" transform="translate(260,109)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.027 1.896 1.046 3.792 1.062 5.688 C1.074 6.743 1.086 7.799 1.098 8.887 C1.009 11.724 0.649 14.244 0 17 C1.32 17 2.64 17 4 17 C4.33 14.36 4.66 11.72 5 9 C5.66 9.33 6.32 9.66 7 10 C6.67 12.97 6.34 15.94 6 19 C2.688 19.688 2.688 19.688 -1 20 C-2.938 18.562 -2.938 18.562 -4 17 C-3.67 16.01 -3.34 15.02 -3 14 C-2.34 14 -1.68 14 -1 14 C-1 13.34 -1 12.68 -1 12 C-2.32 12.99 -3.64 13.98 -5 15 C-4.519 9.226 -2.815 5.041 0 0 Z M-2 6 C-1 9 -1 9 -1 9 Z " fill="#7EA7CB" transform="translate(605,892)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C5.646 1.515 5.646 1.515 10 0 C12.797 2.068 13.027 3.144 13.688 6.688 C13.791 7.781 13.894 8.874 14 10 C12 11 12 11 8.062 10.875 C4.869 10.791 4.869 10.791 2.125 12.312 C1.568 13.148 1.568 13.148 1 14 C0.34 14.99 -0.32 15.98 -1 17 C-1 14.36 -1 11.72 -1 9 C0.625 8.973 2.25 8.954 3.875 8.938 C4.78 8.926 5.685 8.914 6.617 8.902 C9 9 9 9 11 10 C11 8.02 11 6.04 11 4 C7.04 4 3.08 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#7DA5C8" transform="translate(607,831)"/>
<path d="M0 0 C-3.414 1.138 -3.791 0.891 -6.945 -0.41 C-12.6 -2.414 -17.923 -2.479 -23.875 -2.438 C-24.887 -2.457 -25.899 -2.477 -26.941 -2.498 C-35.281 -2.493 -42.583 -0.786 -49.691 3.698 C-50.453 4.127 -51.215 4.557 -52 5 C-52.66 4.67 -53.32 4.34 -54 4 C-37.042 -7.411 -18.497 -8.462 0 0 Z " fill="#8F4430" transform="translate(401,81)"/>
<path d="M0 0 C0.975 0.005 1.949 0.009 2.953 0.014 C5.344 0.025 7.734 0.042 10.125 0.062 C10.125 1.053 10.125 2.043 10.125 3.062 C1.875 3.062 -6.375 3.062 -14.875 3.062 C-14.875 4.712 -14.875 6.362 -14.875 8.062 C-13.555 8.393 -12.235 8.722 -10.875 9.062 C-11.535 9.722 -12.195 10.383 -12.875 11.062 C-14.195 10.403 -15.515 9.742 -16.875 9.062 C-16.875 6.423 -16.875 3.783 -16.875 1.062 C-11.24 -0.142 -5.73 -0.05 0 0 Z " fill="#7EA5C8" transform="translate(660.875,871.9375)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.027 1.417 2.047 2.833 2.062 4.25 C2.074 5.039 2.086 5.828 2.098 6.641 C2.004 8.907 1.632 10.829 1 13 C1.66 13.33 2.32 13.66 3 14 C0.078 15.461 -2.499 15.111 -5.758 15.098 C-6.752 15.096 -6.752 15.096 -7.767 15.093 C-9.886 15.088 -12.006 15.075 -14.125 15.062 C-15.561 15.057 -16.997 15.053 -18.434 15.049 C-21.956 15.038 -25.478 15.021 -29 15 C-28.67 14.34 -28.34 13.68 -28 13 C-18.76 13 -9.52 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#8C4E43" transform="translate(544,224)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.32 2 2.64 2 4 C4.475 4.99 4.475 4.99 7 6 C7.312 8.312 7.312 8.312 7 11 C4.938 12.875 4.938 12.875 2 14 C-2.231 13.781 -4.458 13.361 -8 11 C-7.67 10.01 -7.34 9.02 -7 8 C-6.35 8.712 -6.35 8.712 -5.688 9.438 C-3.977 11.237 -3.977 11.237 -1 12 C0.123 8.632 -0.297 6.428 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#759DBE" transform="translate(529,836)"/>
<path d="M0 0 C2.661 2.334 3.844 4.53 5.125 7.812 C5.458 8.644 5.79 9.475 6.133 10.332 C8.484 17.567 8.332 25.119 8.605 32.652 C8.643 33.566 8.68 34.48 8.719 35.422 C8.749 36.253 8.779 37.084 8.811 37.94 C8.88 40.063 8.88 40.063 10 42 C7.625 42.625 7.625 42.625 5 43 C4.34 42.34 3.68 41.68 3 41 C4.32 41 5.64 41 7 41 C6.67 40.34 6.34 39.68 6 39 C5.906 37.339 5.868 35.675 5.859 34.012 C5.67 22.183 5.191 12.766 0 2 C0 1.34 0 0.68 0 0 Z " fill="#924B3A" transform="translate(387,281)"/>
<path d="M0 0 C4.511 1.504 6.742 5.046 9 9 C10.804 12.949 12.437 16.95 14 21 C14.554 19.857 15.109 18.713 15.68 17.535 C16.411 16.044 17.143 14.553 17.875 13.062 C18.24 12.308 18.605 11.553 18.98 10.775 C19.335 10.057 19.689 9.338 20.055 8.598 C20.379 7.933 20.704 7.268 21.039 6.582 C22 5 22 5 24 4 C22.725 8.251 21.044 12.047 19.008 15.969 C18.036 17.928 17.231 19.9 16.438 21.938 C15.963 22.948 15.489 23.959 15 25 C14.01 25.33 13.02 25.66 12 26 C11.897 24.969 11.794 23.938 11.688 22.875 C11.007 19.038 9.822 16.407 8 13 C8 12.34 8 11.68 8 11 C7.34 10.67 6.68 10.34 6 10 C3.715 6.787 1.653 3.582 0 0 Z " fill="#7B8C9D" transform="translate(111,141)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C1.66 4.33 2.32 4.66 3 5 C2.67 5.33 2.34 5.66 2 6 C1.956 7.666 1.96 9.334 2 11 C2 12.999 1.988 14.997 1.965 16.996 C1.951 18.7 1.951 18.7 1.938 20.438 C1.926 21.571 1.914 22.704 1.902 23.871 C1.984 26.5 2.355 28.49 3 31 C2.727 33.341 2.402 35.678 2 38 C0.205 37.799 -1.586 37.563 -3.375 37.312 C-4.373 37.185 -5.37 37.057 -6.398 36.926 C-9 36 -9 36 -10.352 33.418 C-10.673 32.221 -10.673 32.221 -11 31 C-10.67 31.474 -10.34 31.949 -10 32.438 C-7.361 34.775 -7.361 34.775 0 35 C0 23.45 0 11.9 0 0 Z " fill="#6C92B5" transform="translate(445,874)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.855 2.299 0.855 2.299 -0.312 3.625 C-2.752 6.536 -4.43 9.529 -6 13 C-6.66 13 -7.32 13 -8 13 C-9.901 19.645 -10.6 26.12 -11.125 33 C-11.211 34.061 -11.298 35.122 -11.387 36.215 C-11.597 38.809 -11.801 41.404 -12 44 C-14.171 40.096 -14.194 36.935 -13.875 32.562 C-13.824 31.856 -13.773 31.15 -13.721 30.422 C-12.638 18.037 -8.811 8.811 0 0 Z " fill="#8D4735" transform="translate(533,276)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C7.64 1.33 10.28 1.66 13 2 C13 1.34 13 0.68 13 0 C19.152 0.586 19.152 0.586 21 1 C22.642 4.283 21.519 7.425 21 11 C17.04 10.67 13.08 10.34 9 10 C9 11.98 9 13.96 9 16 C8.01 16.66 7.02 17.32 6 18 C6 15.03 6 12.06 6 9 C10.62 9 15.24 9 20 9 C20 7.02 20 5.04 20 3 C13.07 3 6.14 3 -1 3 C-1 3.99 -1 4.98 -1 6 C-1.66 5.67 -2.32 5.34 -3 5 C-2.67 4.01 -2.34 3.02 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6F96B7" transform="translate(335,816)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 24.09 1 48.18 1 73 C0.67 73 0.34 73 0 73 C0 48.91 0 24.82 0 0 Z " fill="#C36D59" transform="translate(624,347)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C0.397 1.795 -0.207 1.59 -0.828 1.379 C-1.627 1.109 -2.427 0.84 -3.25 0.562 C-4.039 0.296 -4.828 0.029 -5.641 -0.246 C-6.809 -0.619 -6.809 -0.619 -8 -1 C-8.599 -1.192 -9.199 -1.384 -9.816 -1.582 C-18.968 -3.334 -28.451 -2.54 -37 1 C-38.658 1.372 -40.322 1.729 -42 2 C-31.199 -9.895 -12.583 -6.171 0 0 Z " fill="#648BAD" transform="translate(414,630)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.594 1.977 2.189 1.954 2.801 1.93 C14.853 1.667 27.195 3.636 36.348 12.195 C36.893 12.791 37.438 13.386 38 14 C37.67 14.66 37.34 15.32 37 16 C36.599 15.613 36.198 15.227 35.785 14.828 C28.441 8.1 20.621 5.09 10.699 4.684 C9.723 4.642 8.746 4.6 7.74 4.557 C6.229 4.498 6.229 4.498 4.688 4.438 C3.661 4.394 2.634 4.351 1.576 4.307 C-0.949 4.201 -3.474 4.099 -6 4 C-6 3.67 -6 3.34 -6 3 C-5.196 2.876 -4.391 2.752 -3.562 2.625 C-2.717 2.419 -1.871 2.213 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#904835" transform="translate(258,74)"/>
<path d="M0 0 C2.317 1.52 3.566 2.947 5 5.312 C5 6.303 5 7.293 5 8.312 C3.02 8.643 1.04 8.972 -1 9.312 C-1.392 8.694 -1.784 8.075 -2.188 7.438 C-4.277 4.988 -5.975 4.294 -9 3.312 C-3.365 -1.157 -3.365 -1.157 0 0 Z " fill="#91BCE2" transform="translate(537,820.6875)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C5.97 2.33 8.94 2.66 12 3 C12.33 2.34 12.66 1.68 13 1 C13 1.66 13 2.32 13 3 C13.516 2.649 14.031 2.299 14.562 1.938 C17.298 0.886 18.98 1.12 21.816 1.656 C24.522 2.082 27.203 2.175 29.938 2.312 C35.533 2.735 39.204 4.141 44 7 C43.34 7.33 42.68 7.66 42 8 C40.826 7.54 39.651 7.08 38.441 6.605 C31.209 4.059 23.516 4.425 15.938 4.312 C14.389 4.279 12.84 4.245 11.291 4.209 C7.527 4.125 3.764 4.058 0 4 C0 2.68 0 1.36 0 0 Z " fill="#6288AA" transform="translate(443,624)"/>
<path d="M0 0 C2 1 2 1 2.762 2.687 C2.996 3.404 3.231 4.121 3.473 4.859 C3.732 5.646 3.992 6.432 4.26 7.242 C4.525 8.07 4.79 8.897 5.062 9.75 C5.331 10.562 5.6 11.374 5.877 12.211 C7.151 16.114 8.331 19.939 9 24 C10.98 24 12.96 24 15 24 C14.67 25.32 14.34 26.64 14 28 C9.25 27.125 9.25 27.125 7 26 C6.367 23.496 6.367 23.496 5.875 20.438 C5.707 19.426 5.54 18.414 5.367 17.371 C5.246 16.589 5.125 15.806 5 15 C4.01 15 3.02 15 2 15 C2.33 13.02 2.66 11.04 3 9 C2.01 9 1.02 9 0 9 C0 6.03 0 3.06 0 0 Z M1 6 C2 8 2 8 2 8 Z M3 11 C4 14 4 14 4 14 Z " fill="#729BBF" transform="translate(585,884)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.33 10 0.66 10 1 C7.69 1.33 5.38 1.66 3 2 C3.01 2.736 3.021 3.473 3.032 4.231 C3.073 7.571 3.099 10.91 3.125 14.25 C3.142 15.409 3.159 16.568 3.176 17.762 C3.182 18.877 3.189 19.992 3.195 21.141 C3.206 22.167 3.216 23.193 3.227 24.251 C2.998 27.02 2.424 28.644 1 31 C-0.435 28.13 -0.112 25.665 -0.098 22.461 C-0.094 21.159 -0.091 19.857 -0.088 18.516 C-0.08 17.135 -0.071 15.755 -0.062 14.375 C-0.057 12.987 -0.053 11.599 -0.049 10.211 C-0.037 6.807 -0.021 3.404 0 0 Z " fill="#77A0C3" transform="translate(720,825)"/>
<path d="M0 0 C7.59 0 15.18 0 23 0 C22.67 0.99 22.34 1.98 22 3 C14.74 3 7.48 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#507CA9" transform="translate(915,765)"/>
<path d="M0 0 C2.517 2.517 3.11 4.576 4 8 C3.75 10.312 3.75 10.312 3 12 C2.01 12.495 2.01 12.495 1 13 C1.072 13.764 1.144 14.529 1.219 15.316 C2.165 26.24 2.75 37.137 1 48 C0.67 48 0.34 48 0 48 C0 32.16 0 16.32 0 0 Z " fill="#78433B" transform="translate(810,160)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 12.88 2 24.76 2 37 C1.67 37 1.34 37 1 37 C-0.07 29.726 -0.359 22.468 -0.625 15.125 C-0.677 13.89 -0.728 12.655 -0.781 11.383 C-0.825 10.207 -0.869 9.032 -0.914 7.82 C-0.975 6.216 -0.975 6.216 -1.038 4.579 C-1 2 -1 2 0 0 Z " fill="#6C93B5" transform="translate(442,627)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.228 2.018 1.456 4.036 1.684 6.055 C1.913 8.074 1.913 8.074 3 10 C3.127 12.431 3.19 14.819 3.188 17.25 C3.188 18.31 3.188 18.31 3.189 19.391 C3.121 26.376 2.204 33.118 1 40 C0.67 40 0.34 40 0 40 C0 26.8 0 13.6 0 0 Z " fill="#5683B1" transform="translate(549,528)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.02 0.68 1.04 1.36 1.06 2.06 C1.156 5.186 1.266 8.312 1.375 11.438 C1.406 12.507 1.437 13.576 1.469 14.678 C1.77 22.874 2.918 30.31 5.625 38.062 C5.885 38.817 6.146 39.571 6.414 40.348 C6.704 41.166 6.704 41.166 7 42 C0.811 36.99 0.176 28.545 -0.812 21.188 C-0.959 20.254 -1.105 19.32 -1.256 18.357 C-2.158 11.823 -1.701 6.368 0 0 Z " fill="#8B4A3E" transform="translate(312,301)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C6.33 2.32 6.66 3.64 7 5 C5.68 5.33 4.36 5.66 3 6 C2.67 7.65 2.34 9.3 2 11 C0.824 11.186 0.824 11.186 -0.375 11.375 C-3.141 11.791 -3.141 11.791 -5 14 C-5.99 13.67 -6.98 13.34 -8 13 C-7.259 12.346 -7.259 12.346 -6.504 11.68 C-2.265 7.786 -2.265 7.786 -0.062 2.562 C-0.042 1.717 -0.021 0.871 0 0 Z " fill="#577AA4" transform="translate(920,173)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.32 1.67 3.64 1.34 5 1 C5.291 1.762 5.583 2.524 5.883 3.309 C9.946 13.542 14.311 21.17 23 28 C23.66 28.66 24.32 29.32 25 30 C18.665 28.899 13.946 23.517 10.188 18.562 C7.256 14.175 5 9.339 5 4 C4.34 4 3.68 4 3 4 C2.67 4.66 2.34 5.32 2 6 C2 5.01 2 4.02 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#7F8F9F" transform="translate(122,98)"/>
<path d="M0 0 C8 0 8 0 10.938 2.25 C12.76 4.68 13.67 6.033 14 9 C12.292 7.366 11.065 6.131 10 4 C7.03 3.67 4.06 3.34 1 3 C1 13.89 1 24.78 1 36 C0.67 36 0.34 36 0 36 C0 24.12 0 12.24 0 0 Z " fill="#7198BA" transform="translate(415,872)"/>
<path d="M0 0 C8.571 -0.286 8.571 -0.286 12 2 C11.67 2.99 11.34 3.98 11 5 C10.34 5 9.68 5 9 5 C9.084 5.615 9.168 6.23 9.254 6.863 C9.356 7.672 9.458 8.48 9.562 9.312 C9.667 10.113 9.771 10.914 9.879 11.738 C10 14 10 14 9 17 C9.99 17.33 10.98 17.66 12 18 C10.515 20.475 10.515 20.475 9 23 C9.66 23.33 10.32 23.66 11 24 C10.835 24.598 10.67 25.196 10.5 25.812 C9.889 28.113 9.889 28.113 10 31 C9.34 31 8.68 31 8 31 C8 21.43 8 11.86 8 2 C5.36 2 2.72 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8AB3D6" transform="translate(650,816)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-0.936 5.636 -4.133 6.461 -9.25 7.938 C-10.158 8.288 -11.065 8.639 -12 9 C-12.33 9.99 -12.66 10.98 -13 12 C-13.784 11.814 -14.567 11.629 -15.375 11.438 C-17.971 10.74 -17.971 10.74 -20 12 C-22.378 11.912 -24.753 11.757 -27.125 11.562 C-28.406 11.461 -29.688 11.359 -31.008 11.254 C-31.995 11.17 -32.983 11.086 -34 11 C-34 10.67 -34 10.34 -34 10 C-33.411 9.93 -32.822 9.86 -32.215 9.788 C-29.496 9.457 -26.779 9.103 -24.062 8.75 C-23.136 8.64 -22.21 8.531 -21.256 8.418 C-13.435 7.375 -6.075 5.283 0 0 Z " fill="#784F54" transform="translate(593,419)"/>
<path d="M0 0 C8.25 0 16.5 0 25 0 C25.33 0.66 25.66 1.32 26 2 C25.34 2.66 24.68 3.32 24 4 C21.69 3.34 19.38 2.68 17 2 C16.34 2.66 15.68 3.32 15 4 C13.328 3.692 11.662 3.356 10 3 C7.946 3.409 7.946 3.409 6 4 C4.68 4 3.36 4 2 4 C1.34 2.68 0.68 1.36 0 0 Z " fill="#CA6C51" transform="translate(782,132)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C26.99 1.98 27.98 3.96 29 6 C28.67 6.99 28.34 7.98 28 9 C27.01 7.02 26.02 5.04 25 3 C16.75 2.67 8.5 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EB6D3D" transform="translate(624,79)"/>
<path d="M0 0 C1.479 -0.027 2.958 -0.046 4.438 -0.062 C5.261 -0.074 6.085 -0.086 6.934 -0.098 C9 0 9 0 10 1 C10.087 2.489 10.107 3.981 10.098 5.473 C10.094 6.372 10.091 7.271 10.088 8.197 C10.08 9.143 10.071 10.088 10.062 11.062 C10.058 12.012 10.053 12.961 10.049 13.939 C10.037 16.293 10.021 18.646 10 21 C-6.775 22.614 -6.775 22.614 -12 22 C-12.66 21.34 -13.32 20.68 -14 20 C-13.359 20.023 -12.719 20.046 -12.059 20.07 C-7.785 20.159 -4.383 20.115 -0.25 18.875 C3 18 3 18 9 20 C8.67 14.06 8.34 8.12 8 2 C5.36 1.67 2.72 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#769DBE" transform="translate(410,835)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 20.79 1 41.58 1 63 C0.67 63 0.34 63 0 63 C0 42.21 0 21.42 0 0 Z " fill="#D36E4E" transform="translate(624,283)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 9.9 6 19.8 6 30 C5.01 29.67 4.02 29.34 3 29 C3.165 28.381 3.33 27.763 3.5 27.125 C4.201 22.745 4.077 18.426 4 14 C3.34 13.67 2.68 13.34 2 13 C2.309 12.278 2.619 11.556 2.938 10.812 C3.926 8.197 4.529 5.749 5 3 C3.02 3.99 3.02 3.99 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#759DC0" transform="translate(649,880)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.99 3 1.98 3 3 C3.33 2.34 3.66 1.68 4 1 C6.322 0.593 8.657 0.256 11 0 C11 0.99 11 1.98 11 3 C11.66 2.01 12.32 1.02 13 0 C18.445 0.495 18.445 0.495 24 1 C23 3 23 3 21 4 C19.406 4.084 17.808 4.107 16.211 4.098 C15.27 4.094 14.329 4.091 13.359 4.088 C12.375 4.08 11.39 4.071 10.375 4.062 C9.382 4.058 8.39 4.053 7.367 4.049 C4.911 4.037 2.456 4.021 0 4 C0 2.68 0 1.36 0 0 Z " fill="#7199BC" transform="translate(776,624)"/>
<path d="M0 0 C2.081 2.081 2.521 3.091 3.402 5.816 C3.653 6.581 3.904 7.347 4.162 8.135 C4.418 8.936 4.674 9.737 4.938 10.562 C5.19 11.337 5.442 12.111 5.701 12.908 C6.212 14.476 6.719 16.045 7.225 17.614 C8.251 20.773 9.316 23.919 10.384 27.064 C11.111 29.349 11.618 31.633 12 34 C8.7 34 5.4 34 2 34 C2.33 33.01 2.66 32.02 3 31 C3 31.66 3 32.32 3 33 C5.31 33 7.62 33 10 33 C9.697 32.107 9.394 31.214 9.082 30.294 C7.96 26.979 6.843 23.664 5.728 20.347 C5.244 18.912 4.759 17.478 4.272 16.044 C3.573 13.982 2.88 11.919 2.188 9.855 C1.769 8.614 1.35 7.373 0.918 6.094 C0 3 0 3 0 0 Z " fill="#6E96B8" transform="translate(584,822)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.62 1 9.24 1 14 C-9 16 -9 16 -11 16 C-11.99 16.33 -12.98 16.66 -14 17 C-14 16.34 -14 15.68 -14 15 C-14.99 14.67 -15.98 14.34 -17 14 C-17.33 14.66 -17.66 15.32 -18 16 C-18 15.01 -18 14.02 -18 13 C-12.39 13 -6.78 13 -1 13 C-1.031 10.339 -1.031 10.339 -1.062 7.625 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#6E95B8" transform="translate(809,654)"/>
<path d="M0 0 C0.869 0.01 1.739 0.021 2.634 0.032 C4.046 0.041 4.046 0.041 5.486 0.051 C6.471 0.068 7.456 0.085 8.47 0.102 C9.463 0.111 10.455 0.12 11.478 0.129 C13.934 0.153 16.389 0.186 18.845 0.227 C18.22 4.613 17.343 7.534 14.845 11.227 C14.515 10.567 14.185 9.907 13.845 9.227 C14.505 7.577 15.165 5.927 15.845 4.227 C9.245 3.567 2.645 2.907 -4.155 2.227 C-2.155 0.227 -2.155 0.227 0 0 Z " fill="#82AACD" transform="translate(684.15478515625,871.77294921875)"/>
<path d="M0 0 C1.284 2.569 1.33 4.766 1.562 7.625 C1.688 9.129 1.688 9.129 1.816 10.664 C1.877 11.435 1.938 12.206 2 13 C1.01 13 0.02 13 -1 13 C-0.67 10.03 -0.34 7.06 0 4 C-2.376 3.918 -4.749 3.859 -7.125 3.812 C-8.123 3.775 -8.123 3.775 -9.141 3.736 C-13.869 3.667 -16.951 4.597 -21 7 C-20 4 -20 4 -16.688 1.812 C-11.297 -0.77 -5.845 -0.351 0 0 Z " fill="#7BA3C5" transform="translate(700,816)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C5.68 0.33 4.36 0.66 3 1 C3 12.88 3 24.76 3 37 C2.01 36.67 1.02 36.34 0 36 C0.162 35.296 0.325 34.593 0.492 33.868 C1.02 30.888 1.129 28.138 1.133 25.113 C1.134 24.01 1.135 22.908 1.137 21.771 C1.133 20.63 1.129 19.489 1.125 18.312 C1.129 17.159 1.133 16.006 1.137 14.818 C1.135 13.719 1.134 12.621 1.133 11.488 C1.132 10.483 1.131 9.478 1.129 8.443 C1.163 5.992 1.163 5.992 0 4 C0.33 3.67 0.66 3.34 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#6F95B7" transform="translate(328,873)"/>
<path d="M0 0 C3.608 3.608 5.879 5.999 8.375 10.188 C8.898 11.047 9.422 11.907 9.961 12.793 C10.304 13.521 10.647 14.25 11 15 C10.67 15.66 10.34 16.32 10 17 C8.68 16.34 7.36 15.68 6 15 C6.33 14.34 6.66 13.68 7 13 C6.108 10.883 6.108 10.883 5 9 C2.81 12.285 2.647 13.831 2.375 17.688 C2.3 18.681 2.225 19.675 2.148 20.699 C2.099 21.458 2.05 22.218 2 23 C1.67 23 1.34 23 1 23 C0.67 15.41 0.34 7.82 0 0 Z M2 5 C3 7 3 7 3 7 Z M2 8 C3 10 3 10 3 10 Z " fill="#5C83A5" transform="translate(547,653)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.66 5 1.32 5 2 C5.846 1.933 6.691 1.866 7.562 1.797 C11.358 1.745 14.35 1.914 18 3 C21.476 6.599 23.773 10.539 26 15 C25.67 15.66 25.34 16.32 25 17 C21.072 13.259 19.047 10.117 17 5 C11.39 4.67 5.78 4.34 0 4 C0 2.68 0 1.36 0 0 Z " fill="#6084A6" transform="translate(532,624)"/>
<path d="M0 0 C7.485 0.394 14.363 2.547 21 6 C20.34 6.66 19.68 7.32 19 8 C16.227 7.477 13.693 6.817 11 6 C9.872 5.66 8.744 5.319 7.582 4.969 C4.384 3.992 1.19 3.002 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#5A7EA9" transform="translate(161,304)"/>
<path d="M0 0 C2.729 -0.027 5.458 -0.047 8.188 -0.062 C8.956 -0.071 9.725 -0.079 10.518 -0.088 C14.485 -0.105 18.126 0.014 22 1 C22 3.64 22 6.28 22 9 C21.01 9.33 20.02 9.66 19 10 C18.34 9.34 17.68 8.68 17 8 C18.32 8 19.64 8 21 8 C21 6.02 21 4.04 21 2 C20.01 2.33 19.02 2.66 18 3 C16.519 3.081 15.034 3.108 13.551 3.098 C12.703 3.094 11.856 3.091 10.982 3.088 C10.101 3.08 9.22 3.071 8.312 3.062 C7.42 3.058 6.527 3.053 5.607 3.049 C3.405 3.037 1.202 3.021 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#7FA8CA" transform="translate(534,872)"/>
<path d="M0 0 C7.59 0 15.18 0 23 0 C25 4 25 4 24.625 6.25 C24.419 6.827 24.212 7.405 24 8 C23.34 6.35 22.68 4.7 22 3 C14.74 2.67 7.48 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6B91B3" transform="translate(626,680)"/>
<path d="M0 0 C7.92 0 15.84 0 24 0 C24 2.64 24 5.28 24 8 C20.45 9.163 17.442 8.924 13.75 8.562 C12.134 8.41 12.134 8.41 10.484 8.254 C9.665 8.17 8.845 8.086 8 8 C8 7.67 8 7.34 8 7 C12.62 7 17.24 7 22 7 C22 5.02 22 3.04 22 1 C14.74 1 7.48 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5F85A8" transform="translate(808,818)"/>
<path d="M0 0 C2.562 2 2.562 2 4 4 C0.04 4.068 -3.475 4.097 -7.375 3.375 C-8.241 3.251 -9.108 3.127 -10 3 C-12.313 5.313 -12.496 6.479 -13.125 9.625 C-13.376 10.851 -13.376 10.851 -13.633 12.102 C-13.754 12.728 -13.875 13.355 -14 14 C-15.07 12.223 -15.07 12.223 -16 10 C-14.902 6.523 -13.725 3.864 -11.375 1.062 C-7.64 -0.608 -4.014 -0.7 0 0 Z " fill="#A95338" transform="translate(381,108)"/>
<path d="M0 0 C3.182 2.937 3.847 4.682 4.344 8.965 C4.586 17.285 4.586 17.285 2.473 19.863 C1.987 20.238 1.501 20.614 1 21 C1.035 19.571 1.035 19.571 1.07 18.113 C1.088 16.859 1.106 15.605 1.125 14.312 C1.16 12.45 1.16 12.45 1.195 10.551 C1.01 7.188 0.458 5.001 -1 2 C-5.646 2.485 -5.646 2.485 -10 4 C-9.67 3.01 -9.34 2.02 -9 1 C-5.833 -0.584 -3.485 -0.421 0 0 Z M2 9 C2 11.31 2 13.62 2 16 C2.33 16 2.66 16 3 16 C3 13.69 3 11.38 3 9 C2.67 9 2.34 9 2 9 Z " fill="#78A0C2" transform="translate(631,880)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.33 9.91 2.66 18.82 3 28 C1.35 28.33 -0.3 28.66 -2 29 C-2.33 28.34 -2.66 27.68 -3 27 C-2.34 27 -1.68 27 -1 27 C-1.041 26.092 -1.082 25.185 -1.125 24.25 C-1.005 21.122 -0.609 19.614 1 17 C0.01 16.67 -0.98 16.34 -2 16 C-1.01 16 -0.02 16 1 16 C0.928 15.264 0.856 14.528 0.781 13.77 C0.688 12.794 0.596 11.818 0.5 10.812 C0.407 9.85 0.314 8.887 0.219 7.895 C0.019 5.248 -0.039 2.652 0 0 Z " fill="#89B2D5" transform="translate(626,827)"/>
<path d="M0 0 C2 1 2 1 3.125 2.938 C4.371 7.297 4.157 11.496 4 16 C3.01 16 2.02 16 1 16 C1 14.68 1 13.36 1 12 C-2.3 12.33 -5.6 12.66 -9 13 C-9 12.34 -9 11.68 -9 11 C-7.02 10.67 -5.04 10.34 -3 10 C-2.67 9.01 -2.34 8.02 -2 7 C-1.01 6.67 -0.02 6.34 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#82AACD" transform="translate(541,824)"/>
<path d="M0 0 C2.183 2.657 3.276 4.656 4 8 C6.272 7.94 8.542 7.851 10.812 7.75 C12.077 7.704 13.342 7.657 14.645 7.609 C18.072 7.327 18.072 7.327 19.949 4.953 C20.296 4.309 20.643 3.664 21 3 C20.67 4.98 20.34 6.96 20 9 C14.719 11.363 9.512 11.803 4 10 C1.753 7.759 1.162 6.063 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9A4D38" transform="translate(483,157)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.99 2 1.98 2 3 C2.99 4.485 2.99 4.485 4 6 C4.708 8.321 5.38 10.654 6 13 C3.525 12.01 3.525 12.01 1 11 C1.99 11.99 2.98 12.98 4 14 C2.02 14 0.04 14 -2 14 C-3.211 10.368 -2.628 8.935 -1.562 5.312 C-1.275 4.319 -0.988 3.325 -0.691 2.301 C-0.463 1.542 -0.235 0.782 0 0 Z M2 8 C3 10 3 10 3 10 Z " fill="#7BA2C4" transform="translate(577,827)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 0.33 19 0.66 19 1 C21.97 1.495 21.97 1.495 25 2 C25 2.33 25 2.66 25 3 C16.09 3 7.18 3 -2 3 C-1.34 2.67 -0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6B98C1" transform="translate(200,765)"/>
<path d="M0 0 C2 1 2 1 2.625 2.625 C3.121 5.766 3.061 8.826 3 12 C0.625 12.625 0.625 12.625 -2 13 C-2.66 12.34 -3.32 11.68 -4 11 C-3.34 10.34 -2.68 9.68 -2 9 C-2.66 8.67 -3.32 8.34 -4 8 C-2.25 2.25 -2.25 2.25 0 0 Z M-1 3 C-1 3.66 -1 4.32 -1 5 C-0.34 4.67 0.32 4.34 1 4 C0.34 3.67 -0.32 3.34 -1 3 Z M0 6 C-0.33 6.66 -0.66 7.32 -1 8 C-0.34 8 0.32 8 1 8 C0.67 8.99 0.34 9.98 0 11 C0.66 11 1.32 11 2 11 C1.505 8.525 1.505 8.525 1 6 C0.67 6 0.34 6 0 6 Z " fill="#7EA7C9" transform="translate(346,891)"/>
<path d="M0 0 C2.97 0.99 5.94 1.98 9 3 C8.67 3.99 8.34 4.98 8 6 C5.891 7.027 5.891 7.027 3.25 7.938 C-1.369 9.662 -5.686 11.371 -8 16 C-8.99 15.67 -9.98 15.34 -11 15 C-7.219 10.865 -3.116 7.39 2 5 C3.32 5 4.64 5 6 5 C6 4.34 6 3.68 6 3 C4.02 2.34 2.04 1.68 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8594A3" transform="translate(147,128)"/>
<path d="M0 0 C1.497 1.27 2.995 2.539 4.492 3.809 C8.092 6.653 11.749 6.112 16.125 6.062 C17.256 6.056 17.256 6.056 18.41 6.049 C20.273 6.037 22.137 6.019 24 6 C23.67 6.66 23.34 7.32 23 8 C17.292 9.903 11.144 9.766 5.5 7.75 C3.023 6.016 1.514 4.607 0 2 C0 1.34 0 0.68 0 0 Z " fill="#739BBF" transform="translate(733,848)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 2.32 3.34 3.64 3 5 C2.078 5.164 2.078 5.164 1.137 5.332 C-0.076 5.57 -0.076 5.57 -1.312 5.812 C-2.513 6.039 -2.513 6.039 -3.738 6.27 C-6.267 7.086 -7.259 8.041 -9 10 C-11.062 11.25 -11.062 11.25 -13 12 C-13.66 11.67 -14.32 11.34 -15 11 C-10.431 7.312 -6.645 4.79 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#8195A8" transform="translate(901,339)"/>
<path d="M0 0 C1.971 -0.341 1.971 -0.341 4.352 -0.293 C5.628 -0.278 5.628 -0.278 6.93 -0.264 C7.819 -0.239 8.709 -0.213 9.625 -0.188 C10.971 -0.167 10.971 -0.167 12.344 -0.146 C14.563 -0.111 16.781 -0.062 19 0 C19 1.32 19 2.64 19 4 C16.455 4.073 14.183 4.04 11.688 3.5 C8.791 2.961 6.13 2.902 3.188 2.938 C2.212 2.947 1.236 2.956 0.23 2.965 C-0.506 2.976 -1.242 2.988 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#CB6C4F" transform="translate(284,132)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C11 1.98 11 3.96 11 6 C7.37 5.67 3.74 5.34 0 5 C0.66 3.68 1.32 2.36 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#92C0E6" transform="translate(816,835)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 9.24 7 18.48 7 28 C6.67 28 6.34 28 6 28 C5.67 20.08 5.34 12.16 5 4 C3.35 3.67 1.7 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#7DA6C8" transform="translate(320,668)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.959 6.013 5.416 11.696 6 18 C5.01 18.33 4.02 18.66 3 19 C2.67 16.03 2.34 13.06 2 10 C1.34 10 0.68 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#7BA4C8" transform="translate(699,654)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 9.57 1.66 19.14 2 29 C-0.97 29 -3.94 29 -7 29 C-6.67 28.01 -6.34 27.02 -6 26 C-4.02 26 -2.04 26 0 26 C0 17.42 0 8.84 0 0 Z " fill="#7DA7CC" transform="translate(661,883)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-0.99 3.495 -0.99 3.495 -2 4 C-1.01 4.33 -0.02 4.66 1 5 C0.34 5.66 -0.32 6.32 -1 7 C-1.643 9.069 -1.643 9.069 -2 11 C-2.66 11 -3.32 11 -4 11 C-4 8.36 -4 5.72 -4 3 C-5.98 3 -7.96 3 -10 3 C-10.33 2.34 -10.66 1.68 -11 1 C-7.276 0.307 -3.789 -0.111 0 0 Z " fill="#80A8CC" transform="translate(614,872)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.013 1.954 -1.026 1.907 -2.07 1.859 C-3.422 1.822 -4.773 1.785 -6.125 1.75 C-6.79 1.716 -7.455 1.683 -8.141 1.648 C-14.214 1.529 -17.553 3.716 -21.875 7.75 C-22.432 8.369 -22.432 8.369 -23 9 C-22.11 5.105 -20.54 3.391 -17.312 1.25 C-11.377 -2.152 -6.352 -2.873 0 0 Z " fill="#769EBF" transform="translate(412,818)"/>
<path d="M0 0 C0.922 0.005 1.845 0.009 2.795 0.014 C5.051 0.025 7.307 0.042 9.562 0.062 C1.417 4.292 -8.6 4.026 -17.438 2.062 C-17.438 1.733 -17.438 1.402 -17.438 1.062 C-11.589 0.044 -5.928 -0.055 0 0 Z " fill="#C96962" transform="translate(359.4375,425.9375)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.17 0.615 1.34 1.23 1.516 1.863 C3.25 8.005 3.25 8.005 8 12 C8.66 12.66 9.32 13.32 10 14 C10.66 14.33 11.32 14.66 12 15 C12 15.66 12 16.32 12 17 C13.423 17.433 13.423 17.433 14.875 17.875 C18 19 18 19 20 21 C13.21 19.984 7.553 16.081 3.062 10.938 C1.191 8.273 -0.152 6.136 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#D06C61" transform="translate(317,405)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4 0.68 4 0 4 C-0.33 8.95 -0.66 13.9 -1 19 C-1.33 19 -1.66 19 -2 19 C-2.495 11.575 -2.495 11.575 -3 4 C-5.64 3.67 -8.28 3.34 -11 3 C-8.218 -1.174 -4.534 -0.223 0 0 Z " fill="#86AED2" transform="translate(527,872)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C3.118 2.687 3.118 2.687 5.562 3.125 C6.389 3.293 7.215 3.46 8.066 3.633 C8.704 3.754 9.343 3.875 10 4 C10 4.33 10 4.66 10 5 C2.08 4.67 -5.84 4.34 -14 4 C-12.68 3.34 -11.36 2.68 -10 2 C-9.072 1.443 -9.072 1.443 -8.125 0.875 C-5.346 -0.269 -2.974 -0.135 0 0 Z " fill="#A1513A" transform="translate(569,264)"/>
<path d="M0 0 C0.641 0.361 1.281 0.722 1.941 1.094 C9.496 5.147 14.56 5.647 23 4 C23 5.32 23 6.64 23 8 C22.438 7.82 21.876 7.639 21.297 7.453 C18.675 6.936 16.867 7.267 14.25 7.75 C9.24 8.344 6.596 7.226 2.496 4.422 C1 3 1 3 0 0 Z " fill="#759DBF" transform="translate(380,904)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.33 1.34 1.66 0.68 2 0 C3.98 0 5.96 0 8 0 C8 0.66 8 1.32 8 2 C8.66 2 9.32 2 10 2 C9.34 3.98 8.68 5.96 8 8 C7.67 8 7.34 8 7 8 C7 6.35 7 4.7 7 3 C4.69 3 2.38 3 0 3 C0 7.62 0 12.24 0 17 C-0.33 17 -0.66 17 -1 17 C-1.027 14.542 -1.047 12.083 -1.062 9.625 C-1.071 8.926 -1.079 8.228 -1.088 7.508 C-1.113 2.227 -1.113 2.227 0 0 Z " fill="#79A2C5" transform="translate(793,816)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.312 1.71 1.312 1.71 0.609 2.434 C-5.673 9.205 -8.736 16.132 -11 25 C-12.133 23.336 -12.133 23.336 -13 21 C-10.681 13.088 -6.023 5.593 0 0 Z " fill="#874738" transform="translate(325,275)"/>
<path d="M0 0 C5.28 0 10.56 0 16 0 C17.431 2.862 16.6 4.934 16 8 C15.34 7.67 14.68 7.34 14 7 C14.33 5.35 14.66 3.7 15 2 C10.05 2 5.1 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7AA2C4" transform="translate(607,848)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C15 0.99 15 1.98 15 3 C10.05 3.33 5.1 3.66 0 4 C0 2.68 0 1.36 0 0 Z M8 1 C12 2 12 2 12 2 Z " fill="#8BB4D8" transform="translate(304,668)"/>
<path d="M0 0 C1.642 3.283 0.519 6.425 0 10 C-0.33 10 -0.66 10 -1 10 C-1 7.36 -1 4.72 -1 2 C-9.58 2 -18.16 2 -27 2 C-27 1.67 -27 1.34 -27 1 C-17.979 0.243 -9.053 -0.135 0 0 Z " fill="#4E3439" transform="translate(340,379)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4.33 1.99 4.66 2.98 5 4 C6.874 4.802 6.874 4.802 9.062 5.188 C9.796 5.346 10.529 5.505 11.285 5.668 C11.851 5.778 12.417 5.887 13 6 C13 6.33 13 6.66 13 7 C6.25 7.125 6.25 7.125 4 6 C4 7.32 4 8.64 4 10 C3.01 10 2.02 10 1 10 C0.67 6.7 0.34 3.4 0 0 Z " fill="#93BFE5" transform="translate(677,844)"/>
<path d="M0 0 C2.475 0.99 2.475 0.99 5 2 C4.01 2.33 3.02 2.66 2 3 C2.33 3.66 2.66 4.32 3 5 C5.025 5.652 5.025 5.652 7 6 C7 6.99 7 7.98 7 9 C2.38 9 -2.24 9 -7 9 C-7 8.67 -7 8.34 -7 8 C-4.69 8 -2.38 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#7BA3C5" transform="translate(657,847)"/>
<path d="M0 0 C2.306 2.147 3.919 3.834 5.312 6.688 C6.905 9.406 6.905 9.406 10.688 9.812 C11.781 9.874 12.874 9.936 14 10 C14.33 9.01 14.66 8.02 15 7 C15.66 8.32 16.32 9.64 17 11 C12.675 12.18 9.296 12.381 5 11 C2.544 7.842 0 4.1 0 0 Z " fill="#729ABB" transform="translate(299,845)"/>
<path d="M0 0 C1.147 -0.025 1.147 -0.025 2.316 -0.051 C4.916 0.395 5.786 1.313 7.438 3.312 C6.777 3.972 6.118 4.633 5.438 5.312 C5.438 4.653 5.438 3.992 5.438 3.312 C1.808 3.312 -1.822 3.312 -5.562 3.312 C-5.892 3.972 -6.223 4.633 -6.562 5.312 C-7.223 4.982 -7.882 4.653 -8.562 4.312 C-8.562 3.653 -8.562 2.992 -8.562 2.312 C-5.603 0.62 -3.406 0.017 0 0 Z " fill="#7CA3C5" transform="translate(368.5625,815.6875)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 1.66 4 2.32 4 3 C4.66 3 5.32 3 6 3 C6 3.66 6 4.32 6 5 C6.577 5.227 7.155 5.454 7.75 5.688 C10.566 7.33 12.081 9.395 14 12 C13.67 12.66 13.34 13.32 13 14 C7.909 9.714 3.337 5.804 0 0 Z " fill="#A45341" transform="translate(375,346)"/>
<path d="M0 0 C4.239 2.12 5.484 8.743 7 13 C7.845 16.314 8.499 19.618 9 23 C6.511 20.795 5.418 18.99 4.395 15.84 C4.133 15.046 3.872 14.253 3.604 13.436 C3.342 12.611 3.081 11.787 2.812 10.938 C2.54 10.109 2.267 9.281 1.986 8.428 C0 2.309 0 2.309 0 0 Z " fill="#9A4930" transform="translate(670,131)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3.33 2.32 3.66 3 4 C3.125 10.75 3.125 10.75 2 13 C2.66 13.33 3.32 13.66 4 14 C3.01 14 2.02 14 1 14 C1 14.99 1 15.98 1 17 C1.66 17.33 2.32 17.66 3 18 C2.01 18 1.02 18 0 18 C0 12.06 0 6.12 0 0 Z " fill="#8EB7DC" transform="translate(558,838)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C2.34 4.98 1.68 6.96 1 9 C0.34 8.67 -0.32 8.34 -1 8 C-1 7.01 -1 6.02 -1 5 C-3.64 4.67 -6.28 4.34 -9 4 C-9 6.31 -9 8.62 -9 11 C-9.33 11 -9.66 11 -10 11 C-10 8.36 -10 5.72 -10 3 C-6.7 3 -3.4 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#92BDE3" transform="translate(729,821)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 9.57 1 19.14 1 29 C0.67 29 0.34 29 0 29 C-0.66 26.03 -1.32 23.06 -2 20 C-1.67 20 -1.34 20 -1 20 C-0.67 13.4 -0.34 6.8 0 0 Z " fill="#643531" transform="translate(469,360)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-6.92 3 -14.84 3 -23 3 C-23 2.67 -23 2.34 -23 2 C-21.868 1.939 -20.736 1.879 -19.57 1.816 C-18.089 1.732 -16.607 1.647 -15.125 1.562 C-14.379 1.523 -13.632 1.484 -12.863 1.443 C-8.322 1.177 -4.523 -0.119 0 0 Z " fill="#81A9CD" transform="translate(490,872)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C3.66 2 4.32 2 5 2 C5 5.63 5 9.26 5 13 C4.34 13 3.68 13 3 13 C2.492 11.586 1.994 10.169 1.5 8.75 C1.222 7.961 0.943 7.172 0.656 6.359 C0 4 0 4 0 0 Z " fill="#9E4A34" transform="translate(688,342)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.94 1 11.88 1 18 C-0.32 17.34 -1.64 16.68 -3 16 C-2.01 14.02 -1.02 12.04 0 10 C-0.99 9.67 -1.98 9.34 -3 9 C-2.34 8.34 -1.68 7.68 -1 7 C-0.959 5 -0.957 3 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A34F37" transform="translate(564,142)"/>
<path d="M0 0 C1.185 3.555 1.071 6.745 1 10.438 C0.839 16.303 0.839 16.303 2 22 C-1.49 19.012 -2.057 16.121 -2.859 11.719 C-3.082 7.41 -1.746 3.887 0 0 Z " fill="#7299BB" transform="translate(378,882)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.32 2 2.64 2 4 C2.66 4.33 3.32 4.66 4 5 C3.34 5 2.68 5 2 5 C2.144 6.279 2.289 7.558 2.438 8.875 C2.519 9.594 2.6 10.314 2.684 11.055 C2.913 13.074 2.913 13.074 4 15 C2.68 14.67 1.36 14.34 0 14 C0 9.38 0 4.76 0 0 Z " fill="#6B93B7" transform="translate(812,681)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.697 4.341 1.362 5.674 1 7 C1.173 7.594 1.345 8.189 1.523 8.801 C2.121 11.56 1.676 13.506 1.062 16.25 C0.868 17.142 0.673 18.034 0.473 18.953 C0.317 19.629 0.161 20.304 0 21 C-0.33 21 -0.66 21 -1 21 C-1.029 17.875 -1.047 14.75 -1.062 11.625 C-1.071 10.736 -1.079 9.846 -1.088 8.93 C-1.091 8.079 -1.094 7.228 -1.098 6.352 C-1.103 5.566 -1.108 4.781 -1.114 3.971 C-1 2 -1 2 0 0 Z " fill="#9A4B35" transform="translate(649,155)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.29 1 8.58 1 13 C2.98 13 4.96 13 7 13 C7.33 13.66 7.66 14.32 8 15 C5.36 15.33 2.72 15.66 0 16 C-1.163 12.45 -0.924 9.442 -0.562 5.75 C-0.461 4.672 -0.359 3.595 -0.254 2.484 C-0.17 1.665 -0.086 0.845 0 0 Z " fill="#6B93B6" transform="translate(517,896)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.054 2.208 5.093 4.417 5.125 6.625 C5.148 7.855 5.171 9.085 5.195 10.352 C5.003 13.943 4.303 16.667 3 20 C2.67 20 2.34 20 2 20 C1.958 17.667 1.959 15.333 2 13 C2.33 12.67 2.66 12.34 3 12 C3.234 10.151 3.413 8.295 3.562 6.438 C3.688 4.92 3.688 4.92 3.816 3.371 C3.877 2.589 3.938 1.806 4 1 C3.01 1 2.02 1 1 1 C0.67 1.66 0.34 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#97C4EB" transform="translate(605,875)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.66 2.33 1.32 2.66 2 3 C1.004 3.164 1.004 3.164 -0.012 3.332 C-4.909 4.211 -8.902 4.995 -13 8 C-13 7.34 -13 6.68 -13 6 C-13.99 6 -14.98 6 -16 6 C-15.67 5.34 -15.34 4.68 -15 4 C-14.34 4.082 -13.68 4.165 -13 4.25 C-9.333 3.944 -6.957 2.63 -3.77 0.875 C-2 0 -2 0 0 0 Z " fill="#79A1C3" transform="translate(748,816)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.66 8 1.32 8 2 C8.66 2 9.32 2 10 2 C10.33 3.65 10.66 5.3 11 7 C10.602 6.519 10.203 6.038 9.793 5.543 C7.565 3.626 6.212 3.523 3.312 3.312 C2.1 3.214 2.1 3.214 0.863 3.113 C0.248 3.076 -0.366 3.039 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#7AA1C3" transform="translate(629,816)"/>
<path d="M0 0 C2 0 2 0 4.062 1.938 C4.702 2.618 5.341 3.299 6 4 C6.33 4.33 6.66 4.66 7 5 C7.66 5.66 8.32 6.32 9 7 C8.34 8.32 7.68 9.64 7 11 C6.34 11 5.68 11 5 11 C3.35 7.37 1.7 3.74 0 0 Z " fill="#739BBD" transform="translate(712,649)"/>
<path d="M0 0 C0.557 0.158 1.114 0.317 1.688 0.48 C4.159 1.036 6.456 1.208 8.984 1.316 C10.386 1.379 10.386 1.379 11.816 1.443 C12.784 1.483 13.753 1.522 14.75 1.562 C15.734 1.606 16.717 1.649 17.73 1.693 C20.154 1.799 22.577 1.901 25 2 C25 2.33 25 2.66 25 3 C16.75 3 8.5 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#658FBD" transform="translate(496,517)"/>
<path d="M0 0 C1 2 1 2 0.258 4.391 C-0.116 5.334 -0.49 6.278 -0.875 7.25 C-2.371 11.176 -3.546 14.796 -4 19 C-4.66 19 -5.32 19 -6 19 C-5.856 18.169 -5.711 17.337 -5.562 16.48 C-5.377 15.394 -5.191 14.307 -5 13.188 C-4.814 12.109 -4.629 11.03 -4.438 9.918 C-3.926 6.997 -3.926 6.997 -4 4 C-2 1.812 -2 1.812 0 0 Z " fill="#984A33" transform="translate(733,94)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C4.3 2 7.6 2 11 2 C11.33 2.66 11.66 3.32 12 4 C6.06 4 0.12 4 -6 4 C-6 3.67 -6 3.34 -6 3 C-5.196 2.876 -4.391 2.752 -3.562 2.625 C-2.717 2.419 -1.871 2.213 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#A4533A" transform="translate(258,74)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.65 2 4.3 2 6 2 C5.517 2.534 5.033 3.067 4.535 3.617 C2.622 6.586 2.571 8.619 2.438 12.125 C2.209 17.791 2.209 17.791 0 20 C0 13.4 0 6.8 0 0 Z " fill="#7AA2C6" transform="translate(641,882)"/>
<path d="M0 0 C2.31 0.66 4.62 1.32 7 2 C7 2.66 7 3.32 7 4 C7.516 3.794 8.031 3.587 8.562 3.375 C12.033 2.841 14.706 3.923 18 5 C18 5.33 18 5.66 18 6 C11.283 6.32 6.243 5.479 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CC6A62" transform="translate(541,422)"/>
<path d="M0 0 C1 1 1 1 1.062 3.562 C1.042 4.367 1.021 5.171 1 6 C-0.98 4.68 -2.96 3.36 -5 2 C-6.32 2.99 -7.64 3.98 -9 5 C-9 3.68 -9 2.36 -9 1 C-6.018 -0.491 -3.284 -0.119 0 0 Z " fill="#86AED2" transform="translate(551,880)"/>
<path d="M0 0 C1.212 0.014 1.212 0.014 2.449 0.027 C3.372 0.045 3.372 0.045 4.312 0.062 C4.312 1.053 4.312 2.043 4.312 3.062 C0.683 3.062 -2.947 3.062 -6.688 3.062 C-7.018 2.403 -7.347 1.742 -7.688 1.062 C-4.805 0.102 -2.989 -0.043 0 0 Z " fill="#82ABCD" transform="translate(586.6875,871.9375)"/>
<path d="M0 0 C2.97 0.33 5.94 0.66 9 1 C9.33 1.99 9.66 2.98 10 4 C11.401 5.429 11.401 5.429 13.062 6.688 C14.032 7.451 15.001 8.214 16 9 C15.67 9.66 15.34 10.32 15 11 C14.381 10.42 14.381 10.42 13.75 9.828 C9.441 5.943 5.277 3.407 0 1 C0 0.67 0 0.34 0 0 Z " fill="#924B3A" transform="translate(478,270)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C2.994 4.338 3.993 5.672 5 7 C6.32 6.67 7.64 6.34 9 6 C9.33 6.66 9.66 7.32 10 8 C12.527 8.656 12.527 8.656 15 9 C15 9.33 15 9.66 15 10 C10.466 10.356 7.072 10.581 3 8.375 C0.625 5.555 0.248 3.636 0 0 Z " fill="#8D493B" transform="translate(366,201)"/>
<path d="M0 0 C1.619 2.572 2.044 3.677 1.625 6.75 C1.419 7.492 1.212 8.235 1 9 C0.34 9 -0.32 9 -1 9 C-1 8.01 -1 7.02 -1 6 C-1.99 6 -2.98 6 -4 6 C-4.33 5.01 -4.66 4.02 -5 3 C-3 1 -3 1 0 0 Z " fill="#476388" transform="translate(910,5)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 4.95 1.66 9.9 2 15 C1.01 15.495 1.01 15.495 0 16 C-1.765 12.912 -2 11.767 -2 8 C-1.34 8 -0.68 8 0 8 C0 5.36 0 2.72 0 0 Z M-1 9 C0 12 0 12 0 12 Z " fill="#769FBF" transform="translate(442,656)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C3.125 6.625 3.125 6.625 2 10 C0 10 0 10 -1.625 8.812 C-3.421 6.444 -3.235 4.901 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8B9BAD" transform="translate(871,298)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.66 2 2.32 2 3 C2.66 3.33 3.32 3.66 4 4 C3.67 4.66 3.34 5.32 3 6 C4.32 5.67 5.64 5.34 7 5 C7.33 5.66 7.66 6.32 8 7 C7.062 9.625 7.062 9.625 6 12 C5.34 12 4.68 12 4 12 C3.329 10.377 2.663 8.751 2 7.125 C1.629 6.22 1.257 5.315 0.875 4.383 C0 2 0 2 0 0 Z " fill="#B15537" transform="translate(489,112)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.259 4.32 1.958 8.084 0 12 C-0.33 11.67 -0.66 11.34 -1 11 C-0.91 9.142 -0.754 7.288 -0.562 5.438 C-0.461 4.426 -0.359 3.414 -0.254 2.371 C-0.128 1.197 -0.128 1.197 0 0 Z " fill="#8BB4D8" transform="translate(340,882)"/>
<path d="M0 0 C3.277 1.053 4.605 2.638 6.312 5.5 C6.869 6.655 7.426 7.81 8 9 C7.67 9.66 7.34 10.32 7 11 C5.68 10.34 4.36 9.68 3 9 C3.33 8.34 3.66 7.68 4 7 C3.108 4.883 3.108 4.883 2 3 C1.34 3.99 0.68 4.98 0 6 C-0.382 4.344 -0.714 2.675 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#7199BB" transform="translate(550,659)"/>
<path d="M0 0 C0 2.64 0 5.28 0 8 C-0.99 8.33 -1.98 8.66 -3 9 C-3.66 8.34 -4.32 7.68 -5 7 C-3.68 7 -2.36 7 -1 7 C-1 5.02 -1 3.04 -1 1 C-3.31 1.33 -5.62 1.66 -8 2 C-8.33 1.34 -8.66 0.68 -9 0 C-5.522 -1.159 -3.541 -0.708 0 0 Z " fill="#749BBD" transform="translate(556,873)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 1.99 3.34 2.98 3 4 C2.01 4.33 1.02 4.66 0 5 C-0.33 8.3 -0.66 11.6 -1 15 C-1.33 15 -1.66 15 -2 15 C-2.054 13.063 -2.093 11.125 -2.125 9.188 C-2.148 8.109 -2.171 7.03 -2.195 5.918 C-2 3 -2 3 0 0 Z " fill="#759DBF" transform="translate(741,828)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1.66 1.68 2.32 1 3 C0.958 5 0.958 7 1 9 C0.67 9.66 0.34 10.32 0 11 C-0.66 11 -1.32 11 -2 11 C-2.33 8.03 -2.66 5.06 -3 2 C-2.01 1.34 -1.02 0.68 0 0 Z " fill="#8BB4D8" transform="translate(510,831)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.29 1 8.58 1 13 C0.67 12.34 0.34 11.68 0 11 C-2.025 10.348 -2.025 10.348 -4 10 C-3.34 9.34 -2.68 8.68 -2 8 C-1.67 8.33 -1.34 8.66 -1 9 C-0.67 7.68 -0.34 6.36 0 5 C-0.66 5 -1.32 5 -2 5 C-2 4.01 -2 3.02 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6E96B8" transform="translate(517,875)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.33 0.99 5.66 1.98 6 3 C3 5 3 5 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#8FB8DB" transform="translate(529,843)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.237 1.866 0.474 2.733 -0.312 3.625 C-2.752 6.536 -4.43 9.529 -6 13 C-6.33 13 -6.66 13 -7 13 C-7.308 9.862 -7.278 8.407 -5.469 5.762 C-4.819 5.077 -4.169 4.393 -3.5 3.688 C-2.85 2.99 -2.201 2.293 -1.531 1.574 C-1.026 1.055 -0.521 0.535 0 0 Z " fill="#A25037" transform="translate(533,276)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C0.125 5.75 0.125 5.75 -1 8 C-1.99 7.67 -2.98 7.34 -4 7 C-4.495 4.525 -4.495 4.525 -5 2 C-2.525 2.495 -2.525 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#7DA5C8" transform="translate(690,879)"/>
<path d="M0 0 C1.791 -0.081 3.583 -0.139 5.375 -0.188 C6.872 -0.24 6.872 -0.24 8.398 -0.293 C9.257 -0.196 10.115 -0.1 11 0 C11.66 0.99 12.32 1.98 13 3 C10.085 3 7.658 2.591 4.812 2 C3.911 1.814 3.01 1.629 2.082 1.438 C1.395 1.293 0.708 1.149 0 1 C0 0.67 0 0.34 0 0 Z " fill="#81ABCE" transform="translate(450,846)"/>
<path d="M0 0 C1.769 3.537 0.193 7.378 -1 11 C-3.125 14.062 -3.125 14.062 -5 16 C-4.394 10.24 -2.243 5.28 0 0 Z " fill="#97C5EA" transform="translate(617,660)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.073 1.156 1.073 1.156 1.148 2.336 C1.223 3.339 1.298 4.342 1.375 5.375 C1.445 6.373 1.514 7.37 1.586 8.398 C1.764 11.155 1.764 11.155 4 13 C3.34 13.66 2.68 14.32 2 15 C1.01 15 0.02 15 -1 15 C-0.67 10.05 -0.34 5.1 0 0 Z " fill="#4476AE" transform="translate(859,545)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.081 2.437 3.139 3.874 3.188 5.312 C3.24 6.513 3.24 6.513 3.293 7.738 C2.938 10.479 2.106 11.292 0 13 C0 11.35 0 9.7 0 8 C0.66 8 1.32 8 2 8 C1.34 7.01 0.68 6.02 0 5 C-0.125 2.312 -0.125 2.312 0 0 Z " fill="#CB6A49" transform="translate(330,115)"/>
<path d="M0 0 C4.939 -0.337 5.785 -0.161 10 3 C6.37 3 2.74 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#D46C46" transform="translate(260,109)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 1.65 1.66 3.3 2 5 C3.98 5 5.96 5 8 5 C7.67 6.32 7.34 7.64 7 9 C2.25 8.125 2.25 8.125 0 7 C0 4.69 0 2.38 0 0 Z " fill="#7FA8CC" transform="translate(592,903)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.908 4.629 2.453 7.83 0 12 C-0.99 12.33 -1.98 12.66 -3 13 C-2.01 8.71 -1.02 4.42 0 0 Z " fill="#C76557" transform="translate(499,394)"/>
<path d="M0 0 C3.471 1.409 4.881 2.906 6.75 6.125 C7.392 7.212 7.392 7.212 8.047 8.32 C8.519 9.152 8.519 9.152 9 10 C8.01 10 7.02 10 6 10 C3.628 6.838 1.656 3.588 0 0 Z " fill="#8293A4" transform="translate(111,141)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 4.62 2 9.24 2 14 C1.01 13.67 0.02 13.34 -1 13 C-0.835 12.34 -0.67 11.68 -0.5 11 C0.122 7.269 0.064 3.781 0 0 Z " fill="#6D97BA" transform="translate(653,896)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 2.32 1.34 3.64 1 5 C-1.31 4.67 -3.62 4.34 -6 4 C-6 3.34 -6 2.68 -6 2 C-3 1 -3 1 0 2 C0 1.34 0 0.68 0 0 Z " fill="#88B2D7" transform="translate(689,876)"/>
<path d="M0 0 C1.32 0.99 2.64 1.98 4 3 C3.34 3 2.68 3 2 3 C2.33 3.99 2.66 4.98 3 6 C4.65 6 6.3 6 8 6 C8 6.66 8 7.32 8 8 C5.69 8 3.38 8 1 8 C0.67 5.36 0.34 2.72 0 0 Z " fill="#7CA4C8" transform="translate(815,825)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-0.721 5.296 -3.534 6.334 -8.312 7.625 C-8.869 7.749 -9.426 7.873 -10 8 C-10 7.34 -10 6.68 -10 6 C-9.446 5.734 -8.891 5.469 -8.32 5.195 C-7.596 4.842 -6.871 4.489 -6.125 4.125 C-5.406 3.777 -4.686 3.429 -3.945 3.07 C-1.849 2.034 -1.849 2.034 0 0 Z " fill="#6F494F" transform="translate(593,419)"/>
<path d="M0 0 C2 1 2 1 2.812 3.312 C3 6 3 6 1.562 8.312 C1.047 8.869 0.531 9.426 0 10 C-1 9 -1 9 -1.125 5.562 C-1 2 -1 2 0 0 Z " fill="#C86555" transform="translate(393,391)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C-1 14 -1 14 -2 12 C-1.34 12 -0.68 12 0 12 C-0.371 10.762 -0.743 9.525 -1.125 8.25 C-2.121 4.347 -2.121 4.347 -1.125 1.5 C-0.754 1.005 -0.382 0.51 0 0 Z " fill="#844337" transform="translate(469,345)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 0.99 2.66 1.98 3 3 C3.99 3 4.98 3 6 3 C7.688 5.5 7.688 5.5 9 8 C4.89 6.55 1.567 4.489 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#A35340" transform="translate(363,336)"/>
<path d="M0 0 C2.125 0.375 2.125 0.375 4 1 C-0.75 5.875 -0.75 5.875 -3 7 C-2.25 2.25 -2.25 2.25 0 0 Z " fill="#C45F3E" transform="translate(427,276)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 7.26 1 14.52 1 22 C0.67 22 0.34 22 0 22 C-1.333 7.403 -1.333 7.403 0 0 Z " fill="#D66E4B" transform="translate(385,139)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 6.93 1 13.86 1 21 C0.67 21 0.34 21 0 21 C-0.196 18.625 -0.382 16.251 -0.562 13.875 C-0.619 13.21 -0.675 12.545 -0.732 11.859 C-1.036 7.727 -0.852 4.053 0 0 Z " fill="#8EB7DA" transform="translate(547,835)"/>
<path d="M0 0 C1.208 3.623 0.543 4.641 -1 8 C-1.66 7.67 -2.32 7.34 -3 7 C-3.625 3.938 -3.625 3.938 -4 1 C-2 0 -2 0 0 0 Z " fill="#6A99C8" transform="translate(214,512)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.99 4 1.98 4 3 C4.598 3.268 5.196 3.536 5.812 3.812 C7.972 4.985 9.358 6.194 11 8 C6.404 6.513 3.53 4.202 0 1 C0 0.67 0 0.34 0 0 Z " fill="#D06C62" transform="translate(425,416)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C0.499 4.204 -2.173 5.891 -6 8 C-6.99 7.67 -7.98 7.34 -9 7 C-7.68 6.67 -6.36 6.34 -5 6 C-5 5.34 -5 4.68 -5 4 C-4.01 4 -3.02 4 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#D86A57" transform="translate(291,228)"/>
<path d="M0 0 C-1.589 1.589 -3.248 1.17 -5.438 1.188 C-6.642 1.209 -6.642 1.209 -7.871 1.23 C-10 1 -10 1 -12 -1 C-3.429 -2.429 -3.429 -2.429 0 0 Z " fill="#B3583A" transform="translate(382,77)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 1.98 5.34 3.96 5 6 C4.01 5.67 3.02 5.34 2 5 C1.67 4.01 1.34 3.02 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#446084" transform="translate(917,40)"/>
<path d="M0 0 C3.3 0.33 6.6 0.66 10 1 C9.67 1.66 9.34 2.32 9 3 C6.03 3 3.06 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#7DA5C9" transform="translate(465,879)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.223 1.581 1.428 3.165 1.625 4.75 C1.741 5.632 1.857 6.513 1.977 7.422 C2.002 10.229 1.448 11.634 0 14 C-1.418 11.164 -0.863 9.009 -0.562 5.875 C-0.461 4.779 -0.359 3.684 -0.254 2.555 C-0.17 1.712 -0.086 0.869 0 0 Z " fill="#80A8CB" transform="translate(721,842)"/>
<path d="M0 0 C3.375 -0.188 3.375 -0.188 7 0 C7.66 0.99 8.32 1.98 9 3 C4.545 3.495 4.545 3.495 0 4 C0 2.68 0 1.36 0 0 Z " fill="#436B97" transform="translate(79,972)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2.594 3.649 2.742 6.292 3 9 C2.01 9 1.02 9 0 9 C0 6.03 0 3.06 0 0 Z M1 6 C2 8 2 8 2 8 Z " fill="#79A1C5" transform="translate(585,884)"/>
<path d="M0 0 C2.073 3.11 2.274 3.891 2.25 7.438 C2.255 8.199 2.26 8.961 2.266 9.746 C2 12 2 12 0 16 C0 10.72 0 5.44 0 0 Z " fill="#8BB4DA" transform="translate(373,884)"/>
<path d="M0 0 C1 3 1 3 0.223 5.043 C-0.16 5.751 -0.543 6.458 -0.938 7.188 C-1.318 7.903 -1.698 8.618 -2.09 9.355 C-2.54 10.17 -2.54 10.17 -3 11 C-3.33 10.34 -3.66 9.68 -4 9 C-3.356 7.325 -2.688 5.658 -2 4 C-2 3.01 -2 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#769EC1" transform="translate(702,872)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-1.66 3 -2.32 3 -3 3 C-3 3.66 -3 4.32 -3 5 C-3.99 5 -4.98 5 -6 5 C-6.33 4.01 -6.66 3.02 -7 2 C-4.353 0.539 -3.106 0 0 0 Z " fill="#90B8DC" transform="translate(410,843)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C4.029 2.658 4.029 2.658 7 3 C7 4.32 7 5.64 7 7 C5.68 6.67 4.36 6.34 3 6 C3 5.34 3 4.68 3 4 C2.01 3.67 1.02 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#86AFD2" transform="translate(688,841)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 4.95 1.66 9.9 2 15 C1.01 15 0.02 15 -1 15 C-0.67 10.05 -0.34 5.1 0 0 Z " fill="#759DC1" transform="translate(700,680)"/>
<path d="M0 0 C3.807 1.444 5.266 3.35 7 7 C6.67 7.66 6.34 8.32 6 9 C3.725 6.833 1.764 4.604 0 2 C0 1.34 0 0.68 0 0 Z " fill="#729BBD" transform="translate(551,632)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.63 1 7.26 1 11 C1.99 11.33 2.98 11.66 4 12 C2.68 12.33 1.36 12.66 0 13 C-1.297 8.389 -0.711 4.662 0 0 Z " fill="#884535" transform="translate(780,120)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.34 4.3 3.68 7.6 3 11 C2.67 11 2.34 11 2 11 C1.772 9.014 1.544 7.029 1.316 5.043 C1.028 2.806 1.028 2.806 0 0 Z " fill="#AC5539" transform="translate(270,111)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-2.762 5.762 -5.207 5.579 -9 6 C-6.119 3.599 -3.436 1.527 0 0 Z " fill="#81ABCF" transform="translate(761,849)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.66 2 2.32 2 3 C3.32 3 4.64 3 6 3 C5.67 4.65 5.34 6.3 5 8 C2.201 5.472 0.542 3.796 0 0 Z " fill="#779EC1" transform="translate(787,832)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C0.559 3.362 -1.248 5.592 -4 8 C-4.66 8 -5.32 8 -6 8 C-6 6 -6 6 -4.625 4.375 C-3 3 -3 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#CB6A60" transform="translate(492,413)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.228 2.018 1.456 4.036 1.684 6.055 C1.913 8.074 1.913 8.074 3 10 C0.625 10.625 0.625 10.625 -2 11 C-2.66 10.34 -3.32 9.68 -4 9 C-2.68 9 -1.36 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#87493F" transform="translate(394,313)"/>
<path d="M0 0 C-1.41 0.702 -2.828 1.386 -4.25 2.062 C-5.039 2.445 -5.828 2.828 -6.641 3.223 C-9.26 4.086 -10.449 3.93 -13 3 C-11.59 2.298 -10.172 1.614 -8.75 0.938 C-7.961 0.555 -7.172 0.172 -6.359 -0.223 C-3.74 -1.086 -2.551 -0.93 0 0 Z " fill="#EC7143" transform="translate(344,270)"/>
<path d="M0 0 C4.455 0.99 4.455 0.99 9 2 C9 2.66 9 3.32 9 4 C6.36 3.67 3.72 3.34 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D76853" transform="translate(356,234)"/>
<path d="M0 0 C3.914 1.269 5.791 2.512 8 6 C5.688 5.812 5.688 5.812 3 5 C1.188 2.438 1.188 2.438 0 0 Z " fill="#B45837" transform="translate(401,82)"/>
<path d="M0 0 C3.188 0.312 3.188 0.312 6 1 C6 1.66 6 2.32 6 3 C3.03 3 0.06 3 -3 3 C-2.01 2.01 -1.02 1.02 0 0 Z " fill="#7CA4C6" transform="translate(519,872)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C8.67 0.99 8.34 1.98 8 3 C5.69 3 3.38 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#80A8CA" transform="translate(438,872)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.32 2 2.64 2 4 C2.99 4.66 3.98 5.32 5 6 C3.35 6.33 1.7 6.66 0 7 C-0.934 3.99 -1.044 3.133 0 0 Z " fill="#80A9CD" transform="translate(529,836)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 1.32 5.34 2.64 5 4 C3.35 3.67 1.7 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8EB8DC" transform="translate(320,668)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C4 2 4 2 6 5 C5.01 6.485 5.01 6.485 4 8 C0 2.25 0 2.25 0 0 Z " fill="#6A91B2" transform="translate(404,644)"/>
<path d="M0 0 C5.662 2.462 5.662 2.462 7.375 5.188 C7.581 5.786 7.788 6.384 8 7 C4.663 5.827 2.226 4.811 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CD695F" transform="translate(530,415)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1.33 0.02 1.66 -1 2 C-0.01 3.32 0.98 4.64 2 6 C-2.75 5.25 -2.75 5.25 -5 3 C-2.625 1.438 -2.625 1.438 0 0 Z " fill="#CAD6E0" transform="translate(844,338)"/>
<path d="M0 0 C2 3 2 3 1.758 5.48 C1.549 6.374 1.34 7.267 1.125 8.188 C0.921 9.089 0.718 9.99 0.508 10.918 C0.34 11.605 0.173 12.292 0 13 C-0.33 13 -0.66 13 -1 13 C-1.027 11.396 -1.046 9.792 -1.062 8.188 C-1.074 7.294 -1.086 6.401 -1.098 5.48 C-1 3 -1 3 0 0 Z " fill="#EC7043" transform="translate(315,293)"/>
<path d="M0 0 C0.625 1.812 0.625 1.812 1 4 C0.01 5.485 0.01 5.485 -1 7 C-1.66 7 -2.32 7 -3 7 C-3.33 7.66 -3.66 8.32 -4 9 C-3.469 5.179 -2.503 2.938 0 0 Z " fill="#E86F44" transform="translate(423,283)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2.33 2.32 2.66 3 3 C0.688 4.125 0.688 4.125 -2 5 C-2.99 4.34 -3.98 3.68 -5 3 C-3.35 2.01 -1.7 1.02 0 0 Z " fill="#8E9FB1" transform="translate(97,129)"/>
<path d="M0 0 C1.392 0.093 1.392 0.093 2.812 0.188 C2.812 1.177 2.812 2.168 2.812 3.188 C-0.158 3.188 -3.128 3.188 -6.188 3.188 C-3.188 0.188 -3.188 0.188 0 0 Z " fill="#CF6B47" transform="translate(771.1875,108.8125)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.99 6 1.98 6 3 C3.69 2.67 1.38 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#7EA7CB" transform="translate(475,909)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.352 5.373 1.352 5.373 1 8 C-1 10 -1 10 -3 11 C-3.33 10.34 -3.66 9.68 -4 9 C-3.34 8.34 -2.68 7.68 -2 7 C-1.67 7 -1.34 7 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#769EC1" transform="translate(271,839)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C4.667 2.667 3.333 5.333 2 8 C1.34 5.36 0.68 2.72 0 0 Z " fill="#83ACCF" transform="translate(341,840)"/>
<path d="M0 0 C-1.32 0.33 -2.64 0.66 -4 1 C-4 1.66 -4 2.32 -4 3 C-7 2 -7 2 -10 0 C-6.424 -1.238 -3.663 -0.749 0 0 Z " fill="#96C1E6" transform="translate(534,819)"/>
<path d="M0 0 C1.98 0.495 1.98 0.495 4 1 C3.67 2.32 3.34 3.64 3 5 C1.68 4.34 0.36 3.68 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#93A5B8" transform="translate(901,339)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 2.485 2.01 2.485 1 4 C-0.65 3.67 -2.3 3.34 -4 3 C-2.188 1.438 -2.188 1.438 0 0 Z " fill="#94A5B7" transform="translate(837,339)"/>
<path d="M0 0 C1.945 2.918 2.453 4.625 3 8 C2.34 7.67 1.68 7.34 1 7 C1 6.34 1 5.68 1 5 C0.01 4.67 -0.98 4.34 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#A8533A" transform="translate(343,317)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C-0.194 12.876 -0.38 10.75 -0.562 8.625 C-0.667 7.442 -0.771 6.258 -0.879 5.039 C-1 2 -1 2 0 0 Z " fill="#90B8DD" transform="translate(424,833)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.28 1 10.56 1 16 C0.67 16 0.34 16 0 16 C0 10.72 0 5.44 0 0 Z " fill="#8DB7DB" transform="translate(529,663)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.34 14.67 -0.32 14.34 -1 14 C-0.67 9.38 -0.34 4.76 0 0 Z " fill="#D8704C" transform="translate(513,154)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#8AB3D7" transform="translate(530,883)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.34 7.67 -0.32 7.34 -1 7 C-1.526 5.318 -2 3.773 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#8DB6DA" transform="translate(436,880)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#DB704A" transform="translate(616,121)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.62 1 9.24 1 14 C0.67 14 0.34 14 0 14 C0 9.38 0 4.76 0 0 Z " fill="#CD6C53" transform="translate(616,210)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.96 1.66 7.92 2 12 C1.34 12 0.68 12 0 12 C0 8.04 0 4.08 0 0 Z " fill="#90B8DC" transform="translate(421,844)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 1.32 2.34 2.64 2 4 C1.01 4 0.02 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#416086" transform="translate(925,12)"/>
<path d="M0 0 C2.31 0.66 4.62 1.32 7 2 C6.67 2.66 6.34 3.32 6 4 C3.69 3.34 1.38 2.68 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#CF6D4F" transform="translate(578,265)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.34 7.67 -0.32 7.34 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#90B9DC" transform="translate(496,825)"/>
<path d="M0 0 C2.375 -0.188 2.375 -0.188 5 0 C5.66 0.99 6.32 1.98 7 3 C4.69 2.34 2.38 1.68 0 1 C0 0.67 0 0.34 0 0 Z " fill="#D16C4D" transform="translate(353,301)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.63 1 7.26 1 11 C0.67 11 0.34 11 0 11 C0 7.37 0 3.74 0 0 Z " fill="#D76F4A" transform="translate(616,149)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C-0.429 5.646 -1.087 4.52 -0.625 1.75 C-0.419 1.173 -0.212 0.595 0 0 Z " fill="#D9704C" transform="translate(436,152)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C7.67 0.66 7.34 1.32 7 2 C4.36 2 1.72 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#CF6C4B" transform="translate(698,75)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C-0.97 2.505 -0.97 2.505 -4 2 C-2.125 0.938 -2.125 0.938 0 0 Z " fill="#647B99" transform="translate(101,12)"/>
<path d="M0 0 C2 3 2 3 1.625 5.688 C1.419 6.451 1.212 7.214 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#8DB6DB" transform="translate(373,875)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.64 1.66 5.28 2 8 C1.34 7.67 0.68 7.34 0 7 C0 4.69 0 2.38 0 0 Z " fill="#8DB5D9" transform="translate(741,834)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 1.65 3 3.3 3 5 C2.34 4.67 1.68 4.34 1 4 C0.375 1.938 0.375 1.938 0 0 Z " fill="#8DB7DB" transform="translate(382,835)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.33 10 0.66 10 1 C6.7 1 3.4 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#90B8DA" transform="translate(606,816)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.33 10 0.66 10 1 C6.7 1 3.4 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8EB7DA" transform="translate(293,816)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C5.02 1.99 5.02 1.99 3 3 C2.67 2.34 2.34 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#8FB7DB" transform="translate(240,669)"/>
<path d="M0 0 C2.64 0.33 5.28 0.66 8 1 C7.34 1.66 6.68 2.32 6 3 C4.02 2.34 2.04 1.68 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A47072" transform="translate(335,429)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.99 5 1.98 5 3 C2.03 2.505 2.03 2.505 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D06D4E" transform="translate(565,302)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 2.31 2 4.62 2 7 C1.34 7 0.68 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#CF6E51" transform="translate(776,195)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.34 6.67 -0.32 6.34 -1 6 C-1.043 4.334 -1.041 2.666 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CE6C4E" transform="translate(777,127)"/>
<path d="M0 0 C2.64 0.33 5.28 0.66 8 1 C8 1.33 8 1.66 8 2 C5.03 2 2.06 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D16B49" transform="translate(576,75)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 1.32 2.34 2.64 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#3D5C82" transform="translate(938,32)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.33 1.99 3.66 2.98 4 4 C2.68 4 1.36 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#8BB6DB" transform="translate(586,899)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 2.31 3 4.62 3 7 C2.67 7 2.34 7 2 7 C2 5.02 2 3.04 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#8CB4D9" transform="translate(766,841)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.97 1 5.94 1 9 C0.67 9 0.34 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#8BB4D9" transform="translate(261,836)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.34 6.34 -0.32 5.68 -1 5 C-0.625 2.375 -0.625 2.375 0 0 Z " fill="#90B9DC" transform="translate(424,825)"/>
<path d="M0 0 C2.125 0.375 2.125 0.375 4 1 C4 1.33 4 1.66 4 2 C2.02 2 0.04 2 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#8BB6DD" transform="translate(580,624)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2.625 3.562 2.625 3.562 3 6 C2.01 5.67 1.02 5.34 0 5 C0 3.35 0 1.7 0 0 Z " fill="#B56E66" transform="translate(520,404)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CF6F50" transform="translate(351,264)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#87B2D5" transform="translate(328,896)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#8BB4D8" transform="translate(447,888)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4.99 0.68 5.98 0 7 C0 4.69 0 2.38 0 0 Z " fill="#8FB8DC" transform="translate(421,836)"/>
<path d="M0 0 C2 1 2 1 4 4 C2.68 3.67 1.36 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8EB8DC" transform="translate(381,830)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.33 8 0.66 8 1 C5.36 1 2.72 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A67170" transform="translate(346,431)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C3.924 1.553 2.156 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A36F72" transform="translate(434,427)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 2.31 2 4.62 2 7 C1.67 7 1.34 7 1 7 C0.67 4.69 0.34 2.38 0 0 Z " fill="#C27060" transform="translate(411,377)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#CB6D53" transform="translate(396,304)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.01 3 1.02 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D06D4F" transform="translate(458,300)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#D36E4F" transform="translate(513,178)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 1.65 2.66 3.3 3 5 C2.34 4.67 1.68 4.34 1 4 C0.375 1.938 0.375 1.938 0 0 Z " fill="#8DB6DA" transform="translate(728,843)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#90BADE" transform="translate(360,837)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.01 4.495 1.01 4.495 0 5 C0 3.35 0 1.7 0 0 Z " fill="#8FB8DC" transform="translate(397,836)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#8DB5D8" transform="translate(768,833)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C3.34 2.66 2.68 3.32 2 4 C1.34 2.68 0.68 1.36 0 0 Z " fill="#8FB9DD" transform="translate(608,667)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.65 2 3.3 2 5 C0 2 0 2 0 0 Z " fill="#8FBBDF" transform="translate(428,648)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#8DB8DC" transform="translate(681,643)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 0.66 5.34 1.32 5 2 C3.35 1.67 1.7 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#88B3D7" transform="translate(257,624)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C1.01 3.33 0.02 3.66 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#AE6E6A" transform="translate(523,411)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 1.65 2.66 3.3 3 5 C2.34 4.67 1.68 4.34 1 4 C0.375 1.938 0.375 1.938 0 0 Z " fill="#BA6C5E" transform="translate(674,395)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.01 -0.32 4.02 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#CD715A" transform="translate(412,338)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C4.02 0.99 4.02 0.99 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#C46C57" transform="translate(386,324)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D16D4E" transform="translate(467,316)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C76C52" transform="translate(396,313)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#CE6F56" transform="translate(616,225)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.34 -0.32 4.68 -1 4 C-0.625 1.875 -0.625 1.875 0 0 Z " fill="#CF7056" transform="translate(436,209)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.01 -0.32 4.02 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#D27155" transform="translate(436,202)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D16F50" transform="translate(616,195)"/>
<path d="M0 0 C0.625 1.875 0.625 1.875 1 4 C0.34 4.66 -0.32 5.32 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#B76F61" transform="translate(813,192)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D16D4F" transform="translate(385,191)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D16D4E" transform="translate(385,178)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C1735E" transform="translate(217,178)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C4745E" transform="translate(217,170)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D66F4D" transform="translate(616,169)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.99 2 1.98 2 3 C1.01 2.67 0.02 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D16D4D" transform="translate(483,164)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.98 2 3.96 2 6 C1.67 6 1.34 6 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#D76E49" transform="translate(384,131)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#CF6C4B" transform="translate(254,130)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D66D49" transform="translate(385,122)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C6755B" transform="translate(217,123)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 3.485 2.01 3.485 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#D96E47" transform="translate(669,121)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.01 4.495 1.01 4.495 0 5 C0 3.35 0 1.7 0 0 Z " fill="#8CB6DB" transform="translate(357,892)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.01 4.495 1.01 4.495 0 5 C0 3.35 0 1.7 0 0 Z " fill="#8CB5DB" transform="translate(557,875)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#8EB7DB" transform="translate(615,872)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 3.68 -0.32 2.36 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8AB4D8" transform="translate(285,846)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#8AB3D7" transform="translate(261,829)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.01 3.495 2.01 3.495 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#8EB7DA" transform="translate(751,828)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.01 1.495 4.01 1.495 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#8EB7DB" transform="translate(690,827)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#8DB6DA" transform="translate(596,818)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#8DB6D9" transform="translate(547,816)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.33 6 0.66 6 1 C4.02 1 2.04 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#90BADC" transform="translate(341,816)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.01 1.495 4.01 1.495 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#87B0D4" transform="translate(233,816)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#91BBDE" transform="translate(681,675)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#8CB7DB" transform="translate(529,643)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C3.01 2 2.02 2 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#90BADD" transform="translate(400,644)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.01 1.33 3.02 1.66 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#8BB5DA" transform="translate(261,644)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.34 0.66 3.68 1.32 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#88B4D9" transform="translate(401,624)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#BB6D62" transform="translate(412,386)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#BD6D5F" transform="translate(397,374)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#CD705A" transform="translate(412,346)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.68 1.33 0.36 1.66 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C76E56" transform="translate(378,324)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.01 3 0.02 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#CA6C50" transform="translate(361,320)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.01 -0.32 3.02 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D07156" transform="translate(412,315)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.99 1.34 2.98 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#CF6E4F" transform="translate(521,288)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.01 3 1.02 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C26F5B" transform="translate(336,223)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#CA6E56" transform="translate(513,218)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#CA6E55" transform="translate(513,211)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#CC6C53" transform="translate(667,212)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.67 3.66 1.34 4.32 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#D16C4F" transform="translate(659,188)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#D26F51" transform="translate(513,187)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.01 -0.32 3.02 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D8704E" transform="translate(436,171)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C5725C" transform="translate(217,163)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 1.33 2.02 1.66 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#48688E" transform="translate(940,158)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C5755D" transform="translate(217,154)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C5745C" transform="translate(217,147)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#DA714C" transform="translate(436,147)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.01 -0.32 3.02 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#DD714B" transform="translate(436,139)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C6745C" transform="translate(217,139)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C6755B" transform="translate(217,131)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C1.35 2 -0.3 2 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#D06C49" transform="translate(682,75)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.33 0.02 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#385980" transform="translate(947,5)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8CB6DA" transform="translate(396,896)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#8AB3D7" transform="translate(413,887)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#8CB5DA" transform="translate(413,881)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2.66 0.68 3.32 0 4 C0 2.68 0 1.36 0 0 Z " fill="#88B2D6" transform="translate(780,848)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#8EB7DB" transform="translate(372,842)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#8EB7DA" transform="translate(753,840)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#88B1D6" transform="translate(235,834)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.66 2 2.32 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8AB3D6" transform="translate(233,829)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#86AED3" transform="translate(223,830)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#8EB7DB" transform="translate(547,826)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2 0.02 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#88B2D7" transform="translate(261,816)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#82ABD0" transform="translate(826,816)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#86AFD3" transform="translate(819,816)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8AB3D5" transform="translate(810,816)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7CA6CD" transform="translate(806,763)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#89B2D8" transform="translate(694,763)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#89B3D9" transform="translate(374,763)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8FB8DD" transform="translate(660,697)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#8DB7DC" transform="translate(681,692)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8FB9DD" transform="translate(650,692)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.66 2 2.32 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8FB9DD" transform="translate(569,691)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#8FB9DE" transform="translate(529,691)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-0.66 2.67 -1.32 2.34 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#8CB6DB" transform="translate(423,685)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.32 1.67 -1.64 1.34 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#8DB7DC" transform="translate(715,684)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8EB7DC" transform="translate(383,674)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.01 3.495 1.01 3.495 0 4 C0 2.68 0 1.36 0 0 Z " fill="#8FB8DC" transform="translate(381,671)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#91BCE0" transform="translate(430,654)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#5080B6" transform="translate(861,539)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#567EAD" transform="translate(224,497)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A36F6D" transform="translate(563,431)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A47171" transform="translate(451,431)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A47070" transform="translate(355,431)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 1.67 1.02 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A87071" transform="translate(545,429)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 1.67 1.02 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A26E70" transform="translate(696,427)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#AC6E68" transform="translate(624,421)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.68 1.33 1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C96E56" transform="translate(369,324)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#CF7056" transform="translate(411,321)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D26F4D" transform="translate(624,275)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C46F59" transform="translate(332,212)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#CF7051" transform="translate(616,203)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D16F53" transform="translate(513,195)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C27461" transform="translate(217,187)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#D26E50" transform="translate(436,187)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#D56E4C" transform="translate(509,152)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#DA704A" transform="translate(513,148)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#DA704B" transform="translate(616,139)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D96D47" transform="translate(676,131)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#DA6E47" transform="translate(676,122)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A1B1BF" transform="translate(140,120)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D76C45" transform="translate(676,107)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#DE7046" transform="translate(562,107)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D86F49" transform="translate(616,99)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D76C46" transform="translate(676,91)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CF6D4C" transform="translate(467,76)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CE6C4B" transform="translate(451,76)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CE6E4D" transform="translate(758,75)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#7EA8CF" transform="translate(694,910)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8AB4D8" transform="translate(373,901)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8BB6DB" transform="translate(429,902)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#87B1D7" transform="translate(693,895)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8AB4D8" transform="translate(484,894)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#89B3D9" transform="translate(413,893)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8AB4D8" transform="translate(328,890)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8DB6DA" transform="translate(621,888)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#8BB4D9" transform="translate(485,888)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#8DB4D9" transform="translate(652,884)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#8CB7DA" transform="translate(622,884)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8DB6DA" transform="translate(627,882)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8CB6DB" transform="translate(354,880)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#8DB6DA" transform="translate(434,876)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8EB7DB" transform="translate(432,872)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8EB6DA" transform="translate(404,873)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#8EB5DA" transform="translate(340,857)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#8EB6DA" transform="translate(372,853)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#90B8DC" transform="translate(399,845)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#8FB7DB" transform="translate(398,843)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8DB6D9" transform="translate(728,837)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8EB6DB" transform="translate(596,827)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#8FB9DE" transform="translate(616,827)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#90B8DC" transform="translate(608,827)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#89B1D5" transform="translate(245,824)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#89B1D6" transform="translate(261,820)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#8BB4D9" transform="translate(758,816)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#6E98C1" transform="translate(855,763)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#86AFD4" transform="translate(751,763)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#86AFD5" transform="translate(743,763)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#89B3D8" transform="translate(367,763)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#8FB9DD" transform="translate(580,697)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8EB7DC" transform="translate(370,693)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8FB9DD" transform="translate(368,691)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#90B9DD" transform="translate(529,683)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8EB8DD" transform="translate(357,668)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#90BBDF" transform="translate(429,668)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8FB9DD" transform="translate(681,667)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8DB8DB" transform="translate(681,651)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#8FB8DD" transform="translate(529,651)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#8AB4DA" transform="translate(791,651)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#90BADE" transform="translate(565,648)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8EB6DB" transform="translate(257,643)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#648CB8" transform="translate(551,522)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#5680AE" transform="translate(230,510)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#507FB4" transform="translate(861,486)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#A57171" transform="translate(363,430)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A77173" transform="translate(441,429)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#AB706E" transform="translate(530,420)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C06C5C" transform="translate(556,391)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C9715C" transform="translate(412,355)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C16E5A" transform="translate(320,349)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D56F4B" transform="translate(689,336)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#CE7057" transform="translate(412,332)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D06D50" transform="translate(467,324)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CA6D54" transform="translate(363,324)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D16E4E" transform="translate(605,287)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#CE7053" transform="translate(420,281)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#D06E51" transform="translate(705,268)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D06E4F" transform="translate(639,267)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#CF6F50" transform="translate(469,264)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CD6C50" transform="translate(452,264)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C1705D" transform="translate(745,237)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#B86F63" transform="translate(812,204)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C66D55" transform="translate(330,204)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CF6F54" transform="translate(513,202)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CB6C54" transform="translate(254,198)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D36E4F" transform="translate(385,186)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D66F4D" transform="translate(513,170)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D7704D" transform="translate(436,163)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D9704D" transform="translate(616,161)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CD6F4E" transform="translate(330,147)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#DA6D46" transform="translate(676,139)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#9BA4B2" transform="translate(157,132)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#DC714A" transform="translate(435,124)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#A0AFBD" transform="translate(142,123)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D76D49" transform="translate(777,116)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D86D47" transform="translate(676,115)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#D76F48" transform="translate(494,112)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#DB6F4A" transform="translate(616,108)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D06E4A" transform="translate(302,96)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D76F49" transform="translate(485,96)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#DD734C" transform="translate(436,92)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#DB6F46" transform="translate(562,85)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D8714C" transform="translate(436,83)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D46B47" transform="translate(676,83)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CF6B4A" transform="translate(588,76)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CF6A4A" transform="translate(537,76)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CE6B4B" transform="translate(446,76)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#4C6485" transform="translate(892,8)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#415F86" transform="translate(924,6)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6F98C3" transform="translate(648,970)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#86AFD4" transform="translate(483,911)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#87B0D6" transform="translate(484,904)"/>
<path d="" fill="#88B2D6" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8CB4D9" transform="translate(394,900)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#8DB5DA" transform="translate(623,899)"/>
<path d="" fill="#8DB5DB" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8AB3D8" transform="translate(471,885)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8DB4D8" transform="translate(388,884)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#8AB3D7" transform="translate(465,883)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7FA7CD" transform="translate(815,857)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8EB6DB" transform="translate(445,857)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8EB7DA" transform="translate(365,857)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#89B2D5" transform="translate(292,857)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8EB7DB" transform="translate(572,854)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8EB7DA" transform="translate(374,854)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8CB3D6" transform="translate(724,854)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#85AED2" transform="translate(251,853)"/>
<path d="" fill="#8FB7DC" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#85AFD4" transform="translate(248,853)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#8FB7DA" transform="translate(644,851)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8EB6D9" transform="translate(473,850)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8EB7DB" transform="translate(508,847)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8CB4D7" transform="translate(784,846)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7FA9D0" transform="translate(827,846)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8CB7DB" transform="translate(347,846)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5D8" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8EB7DB" transform="translate(697,833)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#8CB4D9" transform="translate(789,832)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#90B9DE" transform="translate(0,0)"/>
<path d="" fill="#90B7DC" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8EB7DB" transform="translate(420,830)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8FB7DA" transform="translate(625,824)"/>
<path d="" fill="#8FB6DB" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8BB3D7" transform="translate(732,817)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8DB7DB" transform="translate(618,816)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8DB6DA" transform="translate(533,816)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#83ACD3" transform="translate(220,816)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7BA5CC" transform="translate(814,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7DA8CE" transform="translate(800,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#82ABD2" transform="translate(784,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#88B2D7" transform="translate(712,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#89B3D8" transform="translate(688,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8AB3D8" transform="translate(680,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8AB4D9" transform="translate(632,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8AB4DA" transform="translate(552,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8AB3D9" transform="translate(488,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8BB4D9" transform="translate(472,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8BB4DA" transform="translate(400,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8AB3D9" transform="translate(382,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#88B3D8" transform="translate(342,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#87B1D7" transform="translate(310,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#84AED4" transform="translate(280,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#83ACD3" transform="translate(270,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#82ACD2" transform="translate(262,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#81ABD2" transform="translate(256,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7FA8CF" transform="translate(246,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8EB7DB" transform="translate(384,699)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8BB4D9" transform="translate(240,699)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8AB5D8" transform="translate(789,697)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8BB5DA" transform="translate(749,697)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8FB8DC" transform="translate(613,697)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#90BADD" transform="translate(576,697)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8EB8DD" transform="translate(542,697)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8DB7DC" transform="translate(454,697)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#92BCDF" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8EB7DA" transform="translate(242,681)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#90BADE" transform="translate(388,680)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#93BBE0" transform="translate(465,676)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#90B9DD" transform="translate(247,671)"/>
<path d="" fill="#90B9DC" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8FB7DC" transform="translate(494,662)"/>
<path d="" fill="#8EBADE" transform="translate(0,0)"/>
<path d="" fill="#90B9DE" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8FB7DC" transform="translate(320,650)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#91BCDE" transform="translate(473,645)"/>
<path d="" fill="#8CB6DA" transform="translate(0,0)"/>
<path d="" fill="#8DB7DC" transform="translate(0,0)"/>
<path d="" fill="#8CB7DB" transform="translate(0,0)"/>
<path d="" fill="#8AB5DA" transform="translate(0,0)"/>
<path d="" fill="#87B0D6" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#88B3D9" transform="translate(544,625)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#89B5DA" transform="translate(540,624)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#8AB4D9" transform="translate(479,625)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#89B2D8" transform="translate(584,624)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8DB7DC" transform="translate(384,624)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#8DB6DA" transform="translate(244,624)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#89B2D7" transform="translate(240,624)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6895C5" transform="translate(542,571)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6995C4" transform="translate(534,571)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6995C4" transform="translate(518,571)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6996C4" transform="translate(510,571)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6896C5" transform="translate(502,571)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6895C4" transform="translate(494,571)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6996C4" transform="translate(486,571)"/>
<path d="" fill="#4F7FB4" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#6389B9" transform="translate(527,514)"/>
<path d="" fill="#5080B3" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#4F7EB3" transform="translate(891,468)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#517FB3" transform="translate(883,468)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A77070" transform="translate(556,431)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9E6E70" transform="translate(460,431)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A57171" transform="translate(444,430)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9D6D73" transform="translate(720,427)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A47072" transform="translate(705,427)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A27173" transform="translate(329,426)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A57072" transform="translate(315,412)"/>
<path d="" fill="#B56F67" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C16D5C" transform="translate(361,388)"/>
<path d="" fill="#C46D5C" transform="translate(0,0)"/>
<path d="" fill="#C76B55" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BC6E61" transform="translate(339,376)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CA6D56" transform="translate(667,374)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C06D5D" transform="translate(354,373)"/>
<path d="" fill="#C56B55" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C26F5E" transform="translate(350,371)"/>
<path d="" fill="#C66B53" transform="translate(0,0)"/>
<path d="" fill="#CB6C52" transform="translate(0,0)"/>
<path d="" fill="#CB6C52" transform="translate(0,0)"/>
<path d="" fill="#C8715C" transform="translate(0,0)"/>
<path d="" fill="#C1715D" transform="translate(0,0)"/>
<path d="" fill="#C67159" transform="translate(0,0)"/>
<path d="" fill="#C36F5B" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#A5B6C6" transform="translate(877,315)"/>
<path d="" fill="#CE6D4F" transform="translate(0,0)"/>
<path d="" fill="#D76E49" transform="translate(0,0)"/>
<path d="" fill="#CC6E4F" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CD6C4E" transform="translate(452,302)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D16D4D" transform="translate(561,301)"/>
<path d="" fill="#D2704F" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D56E4C" transform="translate(670,287)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D56E4B" transform="translate(668,283)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CE6D53" transform="translate(429,272)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D06C4E" transform="translate(634,267)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CD6F50" transform="translate(479,266)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CE6E50" transform="translate(474,265)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CD6D4F" transform="translate(573,264)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CE6E4F" transform="translate(461,264)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BE6F5D" transform="translate(748,239)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C26D5A" transform="translate(402,236)"/>
<path d="" fill="#CF6F55" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C26C59" transform="translate(413,227)"/>
<path d="" fill="#CF6F57" transform="translate(0,0)"/>
<path d="" fill="#B77164" transform="translate(0,0)"/>
<path d="" fill="#C96F57" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CE6C54" transform="translate(260,208)"/>
<path d="" fill="#CA6D53" transform="translate(0,0)"/>
<path d="" fill="#C76B55" transform="translate(0,0)"/>
<path d="" fill="#D06D50" transform="translate(0,0)"/>
<path d="" fill="#D46F4E" transform="translate(0,0)"/>
<path d="" fill="#D6704F" transform="translate(0,0)"/>
<path d="" fill="#D66F4E" transform="translate(0,0)"/>
<path d="" fill="#CD6A4B" transform="translate(0,0)"/>
<path d="" fill="#DA7252" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BA6E5F" transform="translate(813,176)"/>
<path d="" fill="#D46C4B" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#DA704A" transform="translate(562,166)"/>
<path d="" fill="#CD6D4E" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#587192" transform="translate(907,157)"/>
<path d="" fill="#DB704B" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A0ADBC" transform="translate(142,140)"/>
<path d="" fill="#DF744D" transform="translate(0,0)"/>
<path d="" fill="#DF7147" transform="translate(0,0)"/>
<path d="" fill="#D96F49" transform="translate(0,0)"/>
<path d="" fill="#DF744C" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D66D48" transform="translate(665,112)"/>
<path d="" fill="#D76E48" transform="translate(0,0)"/>
<path d="" fill="#D16E4B" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D56D46" transform="translate(371,111)"/>
<path d="" fill="#BA6D5A" transform="translate(0,0)"/>
<path d="" fill="#D66F49" transform="translate(0,0)"/>
<path d="" fill="#DE7148" transform="translate(0,0)"/>
<path d="" fill="#D96B46" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CE6D4C" transform="translate(742,80)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CC6B48" transform="translate(706,76)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D26E49" transform="translate(642,76)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CF6D4C" transform="translate(462,75)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#84ACD1" transform="translate(680,911)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#87B1D5" transform="translate(612,911)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8AB4D9" transform="translate(437,911)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#87B1D5" transform="translate(406,911)"/>
<path d="" fill="#87B1D6" transform="translate(0,0)"/>
<path d="" fill="#88B2D7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#86B0D5" transform="translate(486,910)"/>
<path d="" fill="#86B0D6" transform="translate(0,0)"/>
<path d="" fill="#88B1D6" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#89B3D7" transform="translate(0,0)"/>
<path d="" fill="#8AB3D8" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB6DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#88B2D7" transform="translate(644,897)"/>
<path d="" fill="#86B0D7" transform="translate(0,0)"/>
<path d="" fill="#8BB4D8" transform="translate(0,0)"/>
<path d="" fill="#8BB6D9" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8CB6DB" transform="translate(623,883)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB5D8" transform="translate(488,883)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#8BB4D6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB7DD" transform="translate(594,880)"/>
<path d="" fill="#87B1D7" transform="translate(0,0)"/>
<path d="" fill="#8EB5DB" transform="translate(0,0)"/>
<path d="" fill="#8DB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB6DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB6DB" transform="translate(512,875)"/>
<path d="" fill="#8DB8DB" transform="translate(0,0)"/>
<path d="" fill="#8DB4D9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB5D9" transform="translate(640,872)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8CB5D7" transform="translate(619,872)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#82ABD0" transform="translate(797,857)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB5DB" transform="translate(621,857)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB7DC" transform="translate(588,857)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB5DA" transform="translate(550,857)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB8DC" transform="translate(502,857)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#91B8DC" transform="translate(471,857)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB7DB" transform="translate(351,857)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#86B1D6" transform="translate(780,855)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8CB6D9" transform="translate(517,855)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#89B2D6" transform="translate(317,855)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#86B0D4" transform="translate(253,855)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB6DB" transform="translate(644,854)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#88B1D5" transform="translate(786,852)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#91B9DE" transform="translate(374,851)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB5DB" transform="translate(508,850)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#85AFD6" transform="translate(0,0)"/>
<path d="" fill="#90BADD" transform="translate(0,0)"/>
<path d="" fill="#8EB7DD" transform="translate(0,0)"/>
<path d="" fill="#8EB9DD" transform="translate(0,0)"/>
<path d="" fill="#88B0D6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#84ADD3" transform="translate(820,846)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB7DA" transform="translate(612,846)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#90BADF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#85ADD3" transform="translate(820,844)"/>
<path d="" fill="#8DB6DB" transform="translate(0,0)"/>
<path d="" fill="#90B7DA" transform="translate(0,0)"/>
<path d="" fill="#8FB9DF" transform="translate(0,0)"/>
<path d="" fill="#8AB3D9" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#7EA9CF" transform="translate(0,0)"/>
<path d="" fill="#89B4DA" transform="translate(0,0)"/>
<path d="" fill="#8EB6DD" transform="translate(0,0)"/>
<path d="" fill="#8EB6DA" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB7DB" transform="translate(355,838)"/>
<path d="" fill="#87B0D5" transform="translate(0,0)"/>
<path d="" fill="#8EB5D9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB7DB" transform="translate(401,836)"/>
<path d="" fill="#8DB6DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB6DA" transform="translate(590,835)"/>
<path d="" fill="#91B9DD" transform="translate(0,0)"/>
<path d="" fill="#8CB4D9" transform="translate(0,0)"/>
<path d="" fill="#81A9D0" transform="translate(0,0)"/>
<path d="" fill="#92BBDF" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#8DB6D9" transform="translate(0,0)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB9DB" transform="translate(399,829)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB6DB" transform="translate(524,828)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8CB6DA" transform="translate(746,827)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB7DA" transform="translate(705,827)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#86AED4" transform="translate(230,827)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#86B0D4" transform="translate(226,827)"/>
<path d="" fill="#8CB6D9" transform="translate(0,0)"/>
<path d="" fill="#89B2D7" transform="translate(0,0)"/>
<path d="" fill="#8EB6DC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB8DA" transform="translate(420,825)"/>
<path d="" fill="#8AB4D8" transform="translate(0,0)"/>
<path d="" fill="#8CB6DB" transform="translate(0,0)"/>
<path d="" fill="#93BADE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB7DB" transform="translate(661,822)"/>
<path d="" fill="#8FB9DB" transform="translate(0,0)"/>
<path d="" fill="#8EB7DC" transform="translate(0,0)"/>
<path d="" fill="#8BB5D8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB7DC" transform="translate(540,820)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB8DE" transform="translate(462,819)"/>
<path d="" fill="#8CB5DB" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#91B9DB" transform="translate(392,818)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#87B1D7" transform="translate(803,817)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#89B1D6" transform="translate(781,817)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB5D9" transform="translate(739,817)"/>
<path d="" fill="#8FB6DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB8DA" transform="translate(519,817)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB7DC" transform="translate(357,817)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB2D7" transform="translate(816,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB5D8" transform="translate(754,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB6D8" transform="translate(712,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB6D9" transform="translate(709,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB7D9" transform="translate(684,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB7DC" transform="translate(409,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB9DB" transform="translate(397,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB6D8" transform="translate(352,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#90B8DA" transform="translate(290,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#5C84B0" transform="translate(919,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6A93BF" transform="translate(871,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6C96C0" transform="translate(862,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#729BC4" transform="translate(847,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8AB4D9" transform="translate(663,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8AB4DA" transform="translate(647,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB5D9" transform="translate(623,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB4DA" transform="translate(591,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB3D9" transform="translate(559,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB4DB" transform="translate(455,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB4DA" transform="translate(425,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#89B3D8" transform="translate(350,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#87AFD7" transform="translate(303,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#749EC7" transform="translate(198,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#729BC4" transform="translate(185,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6891BC" transform="translate(150,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6690BA" transform="translate(143,763)"/>
<path d="" fill="#93BBDF" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#91BADE" transform="translate(398,699)"/>
<path d="" fill="#87B1D7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#89B2D7" transform="translate(799,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8BB4DA" transform="translate(783,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB7DD" transform="translate(743,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB9DD" transform="translate(737,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB9DD" transform="translate(668,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#90B8DD" transform="translate(655,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB9DD" transform="translate(607,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB8DD" transform="translate(535,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB8DD" transform="translate(334,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8CB8DC" transform="translate(302,697)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB8DC" transform="translate(702,695)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB7DC" transform="translate(461,695)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB8DD" transform="translate(372,695)"/>
<path d="" fill="#81ABD0" transform="translate(0,0)"/>
<path d="" fill="#8FBADF" transform="translate(0,0)"/>
<path d="" fill="#8FB9DF" transform="translate(0,0)"/>
<path d="" fill="#92BCDF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#92BADE" transform="translate(630,683)"/>
<path d="" fill="#8CB6DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#92BCE0" transform="translate(560,679)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#90B9DE" transform="translate(470,676)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8FB7DD" transform="translate(478,675)"/>
<path d="" fill="#8FBADD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#83AED4" transform="translate(812,671)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8AB4D8" transform="translate(805,670)"/>
<path d="" fill="#91BADE" transform="translate(0,0)"/>
<path d="" fill="#90B8DC" transform="translate(0,0)"/>
<path d="" fill="#93BCDD" transform="translate(0,0)"/>
<path d="" fill="#90B8DF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB7DB" transform="translate(268,660)"/>
<path d="" fill="#8DB8DC" transform="translate(0,0)"/>
<path d="" fill="#91BADD" transform="translate(0,0)"/>
<path d="" fill="#92BCDF" transform="translate(0,0)"/>
<path d="" fill="#8AB1D5" transform="translate(0,0)"/>
<path d="" fill="#91BBDF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8CB7DC" transform="translate(310,651)"/>
<path d="" fill="#8BB6DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8DB6DB" transform="translate(249,650)"/>
<path d="" fill="#90B9DC" transform="translate(0,0)"/>
<path d="" fill="#88B3DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8CB5DA" transform="translate(790,643)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#92BADE" transform="translate(398,643)"/>
<path d="" fill="#89B2D8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8EB7DB" transform="translate(244,643)"/>
<path d="" fill="#8CB7DC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#92BCE0" transform="translate(391,642)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#81ACD1" transform="translate(814,640)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#90B9E0" transform="translate(494,639)"/>
<path d="" fill="#88B3D8" transform="translate(0,0)"/>
<path d="" fill="#89B2D5" transform="translate(0,0)"/>
<path d="" fill="#87B2D5" transform="translate(0,0)"/>
<path d="" fill="#8AB1D7" transform="translate(0,0)"/>
<path d="" fill="#87B0D5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#86B0D5" transform="translate(769,624)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#84AED2" transform="translate(683,624)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8CB6DA" transform="translate(398,624)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6995C5" transform="translate(526,571)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6896C4" transform="translate(477,571)"/>
<path d="" fill="#5381B4" transform="translate(0,0)"/>
<path d="" fill="#4E80B5" transform="translate(0,0)"/>
<path d="" fill="#4E80B5" transform="translate(0,0)"/>
<path d="" fill="#4D7FB4" transform="translate(0,0)"/>
<path d="" fill="#5783B1" transform="translate(0,0)"/>
<path d="" fill="#4F7EB4" transform="translate(0,0)"/>
<path d="" fill="#5080B3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#618CBA" transform="translate(510,515)"/>
<path d="" fill="#4876AA" transform="translate(0,0)"/>
<path d="" fill="#6982A7" transform="translate(0,0)"/>
<path d="" fill="#4F7FB3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#4F7EB3" transform="translate(899,468)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A07172" transform="translate(629,431)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A77071" transform="translate(571,431)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5706E" transform="translate(560,431)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A56E6F" transform="translate(549,430)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A27171" transform="translate(369,429)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A27172" transform="translate(372,428)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A06F72" transform="translate(333,428)"/>
<path d="" fill="#9D7178" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A76F6B" transform="translate(533,423)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A86E6C" transform="translate(428,423)"/>
<path d="" fill="#B26E6D" transform="translate(0,0)"/>
<path d="" fill="#B56C64" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C17165" transform="translate(350,397)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BE6A5B" transform="translate(558,395)"/>
<path d="" fill="#B66E64" transform="translate(0,0)"/>
<path d="" fill="#C36C5B" transform="translate(0,0)"/>
<path d="" fill="#C36D5E" transform="translate(0,0)"/>
<path d="" fill="#C46E5C" transform="translate(0,0)"/>
<path d="" fill="#4C77A9" transform="translate(0,0)"/>
<path d="" fill="#BD6C5D" transform="translate(0,0)"/>
<path d="" fill="#BB6B5E" transform="translate(0,0)"/>
<path d="" fill="#BE6D5F" transform="translate(0,0)"/>
<path d="" fill="#C16C59" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#95ACC1" transform="translate(868,380)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BD6E5D" transform="translate(396,372)"/>
<path d="" fill="#A3B7C7" transform="translate(0,0)"/>
<path d="" fill="#A2B5C7" transform="translate(0,0)"/>
<path d="" fill="#C16E5C" transform="translate(0,0)"/>
<path d="" fill="#D26F52" transform="translate(0,0)"/>
<path d="" fill="#D26F51" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A4B7C6" transform="translate(844,350)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1B5C5" transform="translate(892,348)"/>
<path d="" fill="#BD6E5E" transform="translate(0,0)"/>
<path d="" fill="#BD6F5F" transform="translate(0,0)"/>
<path d="" fill="#CA7056" transform="translate(0,0)"/>
<path d="" fill="#CF6B4F" transform="translate(0,0)"/>
<path d="" fill="#C6715A" transform="translate(0,0)"/>
<path d="" fill="#D96E4C" transform="translate(0,0)"/>
<path d="" fill="#D06C4E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A4B6C6" transform="translate(889,331)"/>
<path d="" fill="#CF6A4F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ABBBC9" transform="translate(853,328)"/>
<path d="" fill="#C16C58" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C76C55" transform="translate(382,324)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C86B53" transform="translate(366,324)"/>
<path d="" fill="#D26E50" transform="translate(0,0)"/>
<path d="" fill="#C46C56" transform="translate(0,0)"/>
<path d="" fill="#D66E4A" transform="translate(0,0)"/>
<path d="" fill="#C87157" transform="translate(0,0)"/>
<path d="" fill="#D56E50" transform="translate(0,0)"/>
<path d="" fill="#CE7054" transform="translate(0,0)"/>
<path d="" fill="#D5704C" transform="translate(0,0)"/>
<path d="" fill="#D06C4E" transform="translate(0,0)"/>
<path d="" fill="#D76D49" transform="translate(0,0)"/>
<path d="" fill="#D26C4D" transform="translate(0,0)"/>
<path d="" fill="#C67057" transform="translate(0,0)"/>
<path d="" fill="#D66D4B" transform="translate(0,0)"/>
<path d="" fill="#C57058" transform="translate(0,0)"/>
<path d="" fill="#D06F4E" transform="translate(0,0)"/>
<path d="" fill="#CE6B4B" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CD6F51" transform="translate(710,268)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CD6E51" transform="translate(702,268)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CE6E51" transform="translate(699,268)"/>
<path d="" fill="#CC6C50" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D06E4E" transform="translate(659,268)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CF6D4E" transform="translate(646,268)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D06C4E" transform="translate(630,268)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D36E4F" transform="translate(627,268)"/>
<path d="" fill="#D06F51" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D17051" transform="translate(370,267)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D07050" transform="translate(367,266)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D06D4F" transform="translate(554,265)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D36E51" transform="translate(450,265)"/>
<path d="" fill="#CC6F52" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D67251" transform="translate(346,265)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C46F5C" transform="translate(371,242)"/>
<path d="" fill="#B76F63" transform="translate(0,0)"/>
<path d="" fill="#C76E55" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BF6F5C" transform="translate(353,238)"/>
<path d="" fill="#C36B57" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AF7069" transform="translate(236,236)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C16D5B" transform="translate(404,235)"/>
<path d="" fill="#CA6F58" transform="translate(0,0)"/>
<path d="" fill="#CC6F59" transform="translate(0,0)"/>
<path d="" fill="#CC6F58" transform="translate(0,0)"/>
<path d="" fill="#C66D59" transform="translate(0,0)"/>
<path d="" fill="#CE6E54" transform="translate(0,0)"/>
<path d="" fill="#C86E59" transform="translate(0,0)"/>
<path d="" fill="#C86E59" transform="translate(0,0)"/>
<path d="" fill="#B26F66" transform="translate(0,0)"/>
<path d="" fill="#C56D57" transform="translate(0,0)"/>
<path d="" fill="#D06C50" transform="translate(0,0)"/>
<path d="" fill="#C26C57" transform="translate(0,0)"/>
<path d="" fill="#CE6E53" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CD6E56" transform="translate(264,208)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CB6D55" transform="translate(765,207)"/>
<path d="" fill="#C97159" transform="translate(0,0)"/>
<path d="" fill="#BC7160" transform="translate(0,0)"/>
<path d="" fill="#D77355" transform="translate(0,0)"/>
<path d="" fill="#BE7261" transform="translate(0,0)"/>
<path d="" fill="#D9714E" transform="translate(0,0)"/>
<path d="" fill="#C96C54" transform="translate(0,0)"/>
<path d="" fill="#BD7161" transform="translate(0,0)"/>
<path d="" fill="#C86C53" transform="translate(0,0)"/>
<path d="" fill="#CF6C4D" transform="translate(0,0)"/>
<path d="" fill="#BF6F61" transform="translate(0,0)"/>
<path d="" fill="#D56F4D" transform="translate(0,0)"/>
<path d="" fill="#CB6C51" transform="translate(0,0)"/>
<path d="" fill="#D86E4D" transform="translate(0,0)"/>
<path d="" fill="#DC724E" transform="translate(0,0)"/>
<path d="" fill="#CD6C50" transform="translate(0,0)"/>
<path d="" fill="#92A0B2" transform="translate(0,0)"/>
<path d="" fill="#B86B5B" transform="translate(0,0)"/>
<path d="" fill="#9BAAB9" transform="translate(0,0)"/>
<path d="" fill="#CE6D4E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D06E4F" transform="translate(776,152)"/>
<path d="" fill="#446489" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D46E4B" transform="translate(475,151)"/>
<path d="" fill="#D66F4C" transform="translate(0,0)"/>
<path d="" fill="#DC6F49" transform="translate(0,0)"/>
<path d="" fill="#D66E4A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C96D4F" transform="translate(274,134)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D5704F" transform="translate(280,133)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CE6E50" transform="translate(272,133)"/>
<path d="" fill="#B46C5C" transform="translate(0,0)"/>
<path d="" fill="#DA6E4B" transform="translate(0,0)"/>
<path d="" fill="#D97047" transform="translate(0,0)"/>
<path d="" fill="#D06C49" transform="translate(0,0)"/>
<path d="" fill="#CE6C4A" transform="translate(0,0)"/>
<path d="" fill="#D76D49" transform="translate(0,0)"/>
<path d="" fill="#C4745C" transform="translate(0,0)"/>
<path d="" fill="#DF7248" transform="translate(0,0)"/>
<path d="" fill="#BF725E" transform="translate(0,0)"/>
<path d="" fill="#D26C49" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CF6D4D" transform="translate(269,116)"/>
<path d="" fill="#9FB0BD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BA705D" transform="translate(812,113)"/>
<path d="" fill="#CB6D4F" transform="translate(0,0)"/>
<path d="" fill="#C6745A" transform="translate(0,0)"/>
<path d="" fill="#C6735A" transform="translate(0,0)"/>
<path d="" fill="#D46E4A" transform="translate(0,0)"/>
<path d="" fill="#D56F49" transform="translate(0,0)"/>
<path d="" fill="#D77049" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D36F4B" transform="translate(418,97)"/>
<path d="" fill="#D7714A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D36F4A" transform="translate(483,93)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D87149" transform="translate(412,89)"/>
<path d="" fill="#CD6D4D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D06A4B" transform="translate(686,76)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D06B4A" transform="translate(646,76)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D06C49" transform="translate(638,76)"/>
<path d="" fill="#D16D49" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CC6849" transform="translate(622,76)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CA6849" transform="translate(597,76)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CF6C4A" transform="translate(585,76)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CD6B4A" transform="translate(542,76)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CF6C4B" transform="translate(534,76)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D26E4A" transform="translate(386,75)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CF6D4A" transform="translate(366,75)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CA6D4E" transform="translate(260,74)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#3F5C81" transform="translate(932,44)"/>
<path d="" fill="#3F5D82" transform="translate(0,0)"/>
<path d="" fill="#3A5A81" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#3D5D82" transform="translate(933,23)"/>
<path d="" fill="#3D5C82" transform="translate(0,0)"/>
<path d="" fill="#416B94" transform="translate(0,0)"/>
<path d="" fill="#49709E" transform="translate(0,0)"/>
<path d="" fill="#4972A0" transform="translate(0,0)"/>
<path d="" fill="#4E77A5" transform="translate(0,0)"/>
<path d="" fill="#4F76A4" transform="translate(0,0)"/>
<path d="" fill="#5079A6" transform="translate(0,0)"/>
<path d="" fill="#557DAA" transform="translate(0,0)"/>
<path d="" fill="#6C95C0" transform="translate(0,0)"/>
<path d="" fill="#6D96C1" transform="translate(0,0)"/>
<path d="" fill="#6E98C3" transform="translate(0,0)"/>
<path d="" fill="#739BC5" transform="translate(0,0)"/>
<path d="" fill="#729BC6" transform="translate(0,0)"/>
<path d="" fill="#739BC5" transform="translate(0,0)"/>
<path d="" fill="#739CC5" transform="translate(0,0)"/>
<path d="" fill="#7299C5" transform="translate(0,0)"/>
<path d="" fill="#729BC6" transform="translate(0,0)"/>
<path d="" fill="#729BC5" transform="translate(0,0)"/>
<path d="" fill="#6F99C4" transform="translate(0,0)"/>
<path d="" fill="#6F98C3" transform="translate(0,0)"/>
<path d="" fill="#6E98C2" transform="translate(0,0)"/>
<path d="" fill="#85AFD5" transform="translate(0,0)"/>
<path d="" fill="#86ADD4" transform="translate(0,0)"/>
<path d="" fill="#84AED2" transform="translate(0,0)"/>
<path d="" fill="#8AB4D6" transform="translate(0,0)"/>
<path d="" fill="#87B0D5" transform="translate(0,0)"/>
<path d="" fill="#81A7CD" transform="translate(0,0)"/>
<path d="" fill="#82ACD1" transform="translate(0,0)"/>
<path d="" fill="#86ADD4" transform="translate(0,0)"/>
<path d="" fill="#89B2D5" transform="translate(0,0)"/>
<path d="" fill="#7FA9D2" transform="translate(0,0)"/>
<path d="" fill="#86AFD4" transform="translate(0,0)"/>
<path d="" fill="#86B1D7" transform="translate(0,0)"/>
<path d="" fill="#89B0D8" transform="translate(0,0)"/>
<path d="" fill="#89B1D6" transform="translate(0,0)"/>
<path d="" fill="#86B1D6" transform="translate(0,0)"/>
<path d="" fill="#8AB0D6" transform="translate(0,0)"/>
<path d="" fill="#87B1D8" transform="translate(0,0)"/>
<path d="" fill="#88B2D7" transform="translate(0,0)"/>
<path d="" fill="#85AED6" transform="translate(0,0)"/>
<path d="" fill="#81ABD3" transform="translate(0,0)"/>
<path d="" fill="#87B0D7" transform="translate(0,0)"/>
<path d="" fill="#87B0D6" transform="translate(0,0)"/>
<path d="" fill="#87B3D8" transform="translate(0,0)"/>
<path d="" fill="#89B1D7" transform="translate(0,0)"/>
<path d="" fill="#8AB2D9" transform="translate(0,0)"/>
<path d="" fill="#87ACD4" transform="translate(0,0)"/>
<path d="" fill="#85B0D5" transform="translate(0,0)"/>
<path d="" fill="#88B2D7" transform="translate(0,0)"/>
<path d="" fill="#89B5D9" transform="translate(0,0)"/>
<path d="" fill="#88B1D5" transform="translate(0,0)"/>
<path d="" fill="#89B2D7" transform="translate(0,0)"/>
<path d="" fill="#8AB4D8" transform="translate(0,0)"/>
<path d="" fill="#89B4D8" transform="translate(0,0)"/>
<path d="" fill="#8BB6D8" transform="translate(0,0)"/>
<path d="" fill="#8BB4DA" transform="translate(0,0)"/>
<path d="" fill="#8EB5DC" transform="translate(0,0)"/>
<path d="" fill="#8DB6DC" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8DB6DB" transform="translate(0,0)"/>
<path d="" fill="#8AB5D9" transform="translate(0,0)"/>
<path d="" fill="#88B2D8" transform="translate(0,0)"/>
<path d="" fill="#8BB3D9" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#8BB1D6" transform="translate(0,0)"/>
<path d="" fill="#8EB6DA" transform="translate(0,0)"/>
<path d="" fill="#8DB5D7" transform="translate(0,0)"/>
<path d="" fill="#8DB5D9" transform="translate(0,0)"/>
<path d="" fill="#8CB7DB" transform="translate(0,0)"/>
<path d="" fill="#8DB6DC" transform="translate(0,0)"/>
<path d="" fill="#89B2D5" transform="translate(0,0)"/>
<path d="" fill="#89B2D8" transform="translate(0,0)"/>
<path d="" fill="#8EB6D9" transform="translate(0,0)"/>
<path d="" fill="#8BB7DD" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8AB5DA" transform="translate(0,0)"/>
<path d="" fill="#88B3D8" transform="translate(0,0)"/>
<path d="" fill="#89B3D9" transform="translate(0,0)"/>
<path d="" fill="#88B1D7" transform="translate(0,0)"/>
<path d="" fill="#8CB5D9" transform="translate(0,0)"/>
<path d="" fill="#8AB3DA" transform="translate(0,0)"/>
<path d="" fill="#8CB6D8" transform="translate(0,0)"/>
<path d="" fill="#8AB2D6" transform="translate(0,0)"/>
<path d="" fill="#8CB3D9" transform="translate(0,0)"/>
<path d="" fill="#8DB2D8" transform="translate(0,0)"/>
<path d="" fill="#8BB5D8" transform="translate(0,0)"/>
<path d="" fill="#8BB5D8" transform="translate(0,0)"/>
<path d="" fill="#87B2D7" transform="translate(0,0)"/>
<path d="" fill="#8BB4DA" transform="translate(0,0)"/>
<path d="" fill="#8BB4D8" transform="translate(0,0)"/>
<path d="" fill="#8CB7DC" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#8EB6DC" transform="translate(0,0)"/>
<path d="" fill="#8DB4DB" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8EB7DC" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="" fill="#8FBADE" transform="translate(0,0)"/>
<path d="" fill="#8EB4D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5D9" transform="translate(0,0)"/>
<path d="" fill="#89B4D7" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#90B8DC" transform="translate(0,0)"/>
<path d="" fill="#90BADD" transform="translate(0,0)"/>
<path d="" fill="#8EB7DC" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#91BADD" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#96BEE1" transform="translate(0,0)"/>
<path d="" fill="#8BB4DA" transform="translate(0,0)"/>
<path d="" fill="#88B2D8" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#88B0D5" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8BB3D8" transform="translate(0,0)"/>
<path d="" fill="#92B9DC" transform="translate(0,0)"/>
<path d="" fill="#8DB5D9" transform="translate(0,0)"/>
<path d="" fill="#89B4D8" transform="translate(0,0)"/>
<path d="" fill="#8DB6DB" transform="translate(0,0)"/>
<path d="" fill="#90BADB" transform="translate(0,0)"/>
<path d="" fill="#8BB6DA" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8EB7DA" transform="translate(0,0)"/>
<path d="" fill="#8BB3D9" transform="translate(0,0)"/>
<path d="" fill="#8CB6D9" transform="translate(0,0)"/>
<path d="" fill="#8BB6D9" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#8FB8DE" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#8EB6DA" transform="translate(0,0)"/>
<path d="" fill="#90B5DD" transform="translate(0,0)"/>
<path d="" fill="#91B7DD" transform="translate(0,0)"/>
<path d="" fill="#8DB7D9" transform="translate(0,0)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8DB4D8" transform="translate(0,0)"/>
<path d="" fill="#8DB5D9" transform="translate(0,0)"/>
<path d="" fill="#8BB6DC" transform="translate(0,0)"/>
<path d="" fill="#8EB4D9" transform="translate(0,0)"/>
<path d="" fill="#8EB4DA" transform="translate(0,0)"/>
<path d="" fill="#8EB6D8" transform="translate(0,0)"/>
<path d="" fill="#8EB4D8" transform="translate(0,0)"/>
<path d="" fill="#8FB8DA" transform="translate(0,0)"/>
<path d="" fill="#8DB5D9" transform="translate(0,0)"/>
<path d="" fill="#8DB7D9" transform="translate(0,0)"/>
<path d="" fill="#8FB8DC" transform="translate(0,0)"/>
<path d="" fill="#8EB7D9" transform="translate(0,0)"/>
<path d="" fill="#8EB6D9" transform="translate(0,0)"/>
<path d="" fill="#8EB7DA" transform="translate(0,0)"/>
<path d="" fill="#8DB4D8" transform="translate(0,0)"/>
<path d="" fill="#8DB8D9" transform="translate(0,0)"/>
<path d="" fill="#8CB6D8" transform="translate(0,0)"/>
<path d="" fill="#8FBADD" transform="translate(0,0)"/>
<path d="" fill="#90B9DB" transform="translate(0,0)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#90B6DC" transform="translate(0,0)"/>
<path d="" fill="#87B0D4" transform="translate(0,0)"/>
<path d="" fill="#89B1D5" transform="translate(0,0)"/>
<path d="" fill="#8EB4DA" transform="translate(0,0)"/>
<path d="" fill="#90B7DB" transform="translate(0,0)"/>
<path d="" fill="#8EB5D9" transform="translate(0,0)"/>
<path d="" fill="#8FB6DC" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#8DB6D9" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#84AAD2" transform="translate(0,0)"/>
<path d="" fill="#7EA7CD" transform="translate(0,0)"/>
<path d="" fill="#84ACD1" transform="translate(0,0)"/>
<path d="" fill="#88AFD3" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#90B8DB" transform="translate(0,0)"/>
<path d="" fill="#92B8DC" transform="translate(0,0)"/>
<path d="" fill="#8FB8DB" transform="translate(0,0)"/>
<path d="" fill="#91BADD" transform="translate(0,0)"/>
<path d="" fill="#91B7DD" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#8FB8DC" transform="translate(0,0)"/>
<path d="" fill="#91B7DC" transform="translate(0,0)"/>
<path d="" fill="#8DB8DC" transform="translate(0,0)"/>
<path d="" fill="#91B8DD" transform="translate(0,0)"/>
<path d="" fill="#8FB9DB" transform="translate(0,0)"/>
<path d="" fill="#92B9DD" transform="translate(0,0)"/>
<path d="" fill="#8FB9DB" transform="translate(0,0)"/>
<path d="" fill="#91BADC" transform="translate(0,0)"/>
<path d="" fill="#8EB6DA" transform="translate(0,0)"/>
<path d="" fill="#8CB3D8" transform="translate(0,0)"/>
<path d="" fill="#8EB5DA" transform="translate(0,0)"/>
<path d="" fill="#82ABD1" transform="translate(0,0)"/>
<path d="" fill="#84AFD1" transform="translate(0,0)"/>
<path d="" fill="#88B1D2" transform="translate(0,0)"/>
<path d="" fill="#8EB8DB" transform="translate(0,0)"/>
<path d="" fill="#8AB3D6" transform="translate(0,0)"/>
<path d="" fill="#90B9DE" transform="translate(0,0)"/>
<path d="" fill="#87B0D5" transform="translate(0,0)"/>
<path d="" fill="#89B3D6" transform="translate(0,0)"/>
<path d="" fill="#89B1D7" transform="translate(0,0)"/>
<path d="" fill="#86B1D2" transform="translate(0,0)"/>
<path d="" fill="#81ABCF" transform="translate(0,0)"/>
<path d="" fill="#84AACE" transform="translate(0,0)"/>
<path d="" fill="#89B4D7" transform="translate(0,0)"/>
<path d="" fill="#8DB5DB" transform="translate(0,0)"/>
<path d="" fill="#88B3D6" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#90B8DB" transform="translate(0,0)"/>
<path d="" fill="#7DA6CC" transform="translate(0,0)"/>
<path d="" fill="#86B0D5" transform="translate(0,0)"/>
<path d="" fill="#8BB2D6" transform="translate(0,0)"/>
<path d="" fill="#8DB6DB" transform="translate(0,0)"/>
<path d="" fill="#8FB8DB" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#91B9DC" transform="translate(0,0)"/>
<path d="" fill="#8CB8DB" transform="translate(0,0)"/>
<path d="" fill="#8AB3D8" transform="translate(0,0)"/>
<path d="" fill="#8CB7D9" transform="translate(0,0)"/>
<path d="" fill="#8CB5D9" transform="translate(0,0)"/>
<path d="" fill="#87B0D4" transform="translate(0,0)"/>
<path d="" fill="#8AB3D9" transform="translate(0,0)"/>
<path d="" fill="#8FB7DA" transform="translate(0,0)"/>
<path d="" fill="#8BB1D6" transform="translate(0,0)"/>
<path d="" fill="#87B2D6" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8BB3D6" transform="translate(0,0)"/>
<path d="" fill="#88B1D6" transform="translate(0,0)"/>
<path d="" fill="#8BB6D9" transform="translate(0,0)"/>
<path d="" fill="#90B8DB" transform="translate(0,0)"/>
<path d="" fill="#8FB8DB" transform="translate(0,0)"/>
<path d="" fill="#8DB8DA" transform="translate(0,0)"/>
<path d="" fill="#8CB4DA" transform="translate(0,0)"/>
<path d="" fill="#8CB3DA" transform="translate(0,0)"/>
<path d="" fill="#89AFD4" transform="translate(0,0)"/>
<path d="" fill="#8BB2D7" transform="translate(0,0)"/>
<path d="" fill="#8BB7D8" transform="translate(0,0)"/>
<path d="" fill="#92BBDD" transform="translate(0,0)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="" fill="#90BBDE" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#8FB7DD" transform="translate(0,0)"/>
<path d="" fill="#8BB2DB" transform="translate(0,0)"/>
<path d="" fill="#8CB4D8" transform="translate(0,0)"/>
<path d="" fill="#8CB8DB" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#86AFD4" transform="translate(0,0)"/>
<path d="" fill="#86AFD4" transform="translate(0,0)"/>
<path d="" fill="#88B1D6" transform="translate(0,0)"/>
<path d="" fill="#8FB8DB" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8DB4D8" transform="translate(0,0)"/>
<path d="" fill="#87AED3" transform="translate(0,0)"/>
<path d="" fill="#8EB8D9" transform="translate(0,0)"/>
<path d="" fill="#8EB9DC" transform="translate(0,0)"/>
<path d="" fill="#8EB7DA" transform="translate(0,0)"/>
<path d="" fill="#7FA7CD" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8DB7DA" transform="translate(0,0)"/>
<path d="" fill="#8EB6D9" transform="translate(0,0)"/>
<path d="" fill="#8DB7DA" transform="translate(0,0)"/>
<path d="" fill="#90B8DD" transform="translate(0,0)"/>
<path d="" fill="#91BADE" transform="translate(0,0)"/>
<path d="" fill="#8FB9DE" transform="translate(0,0)"/>
<path d="" fill="#8FB9DE" transform="translate(0,0)"/>
<path d="" fill="#90B8DD" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8AB4D8" transform="translate(0,0)"/>
<path d="" fill="#7FA8CF" transform="translate(0,0)"/>
<path d="" fill="#90B7DB" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#90B9DC" transform="translate(0,0)"/>
<path d="" fill="#8AB3D9" transform="translate(0,0)"/>
<path d="" fill="#8CB5D9" transform="translate(0,0)"/>
<path d="" fill="#8FB8DC" transform="translate(0,0)"/>
<path d="" fill="#8EB9DD" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#92BADF" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#8DB7DC" transform="translate(0,0)"/>
<path d="" fill="#8AB3D8" transform="translate(0,0)"/>
<path d="" fill="#88B1D5" transform="translate(0,0)"/>
<path d="" fill="#8BB3D8" transform="translate(0,0)"/>
<path d="" fill="#82AACF" transform="translate(0,0)"/>
<path d="" fill="#8FB9DC" transform="translate(0,0)"/>
<path d="" fill="#91B9DF" transform="translate(0,0)"/>
<path d="" fill="#92BBDF" transform="translate(0,0)"/>
<path d="" fill="#8FBAE0" transform="translate(0,0)"/>
<path d="" fill="#92BDE0" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="" fill="#91B8DC" transform="translate(0,0)"/>
<path d="" fill="#8EB8DD" transform="translate(0,0)"/>
<path d="" fill="#8EB4DA" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#8EB6DA" transform="translate(0,0)"/>
<path d="" fill="#8FB9DC" transform="translate(0,0)"/>
<path d="" fill="#89B0D6" transform="translate(0,0)"/>
<path d="" fill="#87B1D8" transform="translate(0,0)"/>
<path d="" fill="#8AB5D8" transform="translate(0,0)"/>
<path d="" fill="#8BB2D6" transform="translate(0,0)"/>
<path d="" fill="#8FB7DD" transform="translate(0,0)"/>
<path d="" fill="#8DB5DC" transform="translate(0,0)"/>
<path d="" fill="#8FB9DB" transform="translate(0,0)"/>
<path d="" fill="#8EB6DD" transform="translate(0,0)"/>
<path d="" fill="#8DB7DC" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#90B7DA" transform="translate(0,0)"/>
<path d="" fill="#90B9DE" transform="translate(0,0)"/>
<path d="" fill="#90B6DB" transform="translate(0,0)"/>
<path d="" fill="#8FBADE" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#8DB5D9" transform="translate(0,0)"/>
<path d="" fill="#8FB9DC" transform="translate(0,0)"/>
<path d="" fill="#91B9DD" transform="translate(0,0)"/>
<path d="" fill="#8DB5DC" transform="translate(0,0)"/>
<path d="" fill="#8AB4DB" transform="translate(0,0)"/>
<path d="" fill="#8CB6DB" transform="translate(0,0)"/>
<path d="" fill="#8EB6DC" transform="translate(0,0)"/>
<path d="" fill="#8FB8DC" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#8CB6D9" transform="translate(0,0)"/>
<path d="" fill="#8CB5D9" transform="translate(0,0)"/>
<path d="" fill="#89B1D5" transform="translate(0,0)"/>
<path d="" fill="#90B8DA" transform="translate(0,0)"/>
<path d="" fill="#90BADD" transform="translate(0,0)"/>
<path d="" fill="#8DB8DB" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#8BB6DB" transform="translate(0,0)"/>
<path d="" fill="#8AB4DC" transform="translate(0,0)"/>
<path d="" fill="#8CB4D8" transform="translate(0,0)"/>
<path d="" fill="#91B6DA" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#8FB9DB" transform="translate(0,0)"/>
<path d="" fill="#87B0D5" transform="translate(0,0)"/>
<path d="" fill="#8EB8DA" transform="translate(0,0)"/>
<path d="" fill="#90B6DC" transform="translate(0,0)"/>
<path d="" fill="#8CB5DD" transform="translate(0,0)"/>
<path d="" fill="#8DB8DC" transform="translate(0,0)"/>
<path d="" fill="#8DB7DA" transform="translate(0,0)"/>
<path d="" fill="#81ACD2" transform="translate(0,0)"/>
<path d="" fill="#82ACD4" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#8DB4DA" transform="translate(0,0)"/>
<path d="" fill="#90B7DC" transform="translate(0,0)"/>
<path d="" fill="#8DB5DA" transform="translate(0,0)"/>
<path d="" fill="#8DB5DB" transform="translate(0,0)"/>
<path d="" fill="#8AB2D8" transform="translate(0,0)"/>
<path d="" fill="#87AFD7" transform="translate(0,0)"/>
<path d="" fill="#87B1D6" transform="translate(0,0)"/>
<path d="" fill="#8AB2DB" transform="translate(0,0)"/>
<path d="" fill="#8DB8DA" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8FB6D9" transform="translate(0,0)"/>
<path d="" fill="#8AB3D6" transform="translate(0,0)"/>
<path d="" fill="#88B0D6" transform="translate(0,0)"/>
<path d="" fill="#86AFD5" transform="translate(0,0)"/>
<path d="" fill="#82ABD1" transform="translate(0,0)"/>
<path d="" fill="#8BB5D9" transform="translate(0,0)"/>
<path d="" fill="#8FB9DA" transform="translate(0,0)"/>
<path d="" fill="#8EB7DC" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8FB8DE" transform="translate(0,0)"/>
<path d="" fill="#8DB4DB" transform="translate(0,0)"/>
<path d="" fill="#85ADD4" transform="translate(0,0)"/>
<path d="" fill="#81A9CD" transform="translate(0,0)"/>
<path d="" fill="#87B0D6" transform="translate(0,0)"/>
<path d="" fill="#87B0D9" transform="translate(0,0)"/>
<path d="" fill="#8BB4DA" transform="translate(0,0)"/>
<path d="" fill="#8DB6DB" transform="translate(0,0)"/>
<path d="" fill="#8DB8DD" transform="translate(0,0)"/>
<path d="" fill="#8FB8DE" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#8FB7DC" transform="translate(0,0)"/>
<path d="" fill="#8FB7DD" transform="translate(0,0)"/>
<path d="" fill="#91B9DC" transform="translate(0,0)"/>
<path d="" fill="#91BADD" transform="translate(0,0)"/>
<path d="" fill="#8FB7DC" transform="translate(0,0)"/>
<path d="" fill="#8EB7DD" transform="translate(0,0)"/>
<path d="" fill="#80ACCF" transform="translate(0,0)"/>
<path d="" fill="#8CB5D9" transform="translate(0,0)"/>
<path d="" fill="#8BB7DA" transform="translate(0,0)"/>
<path d="" fill="#83ADD1" transform="translate(0,0)"/>
<path d="" fill="#8BB4D8" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#97BFE1" transform="translate(0,0)"/>
<path d="" fill="#8FB8DC" transform="translate(0,0)"/>
<path d="" fill="#90BADC" transform="translate(0,0)"/>
<path d="" fill="#8DBBDC" transform="translate(0,0)"/>
<path d="" fill="#8EB8DD" transform="translate(0,0)"/>
<path d="" fill="#91BBDE" transform="translate(0,0)"/>
<path d="" fill="#87B0D5" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8EB6DA" transform="translate(0,0)"/>
<path d="" fill="#8CB5DD" transform="translate(0,0)"/>
<path d="" fill="#8FB7DC" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8BB5D8" transform="translate(0,0)"/>
<path d="" fill="#90BCDF" transform="translate(0,0)"/>
<path d="" fill="#8FB8DA" transform="translate(0,0)"/>
<path d="" fill="#8EB8DB" transform="translate(0,0)"/>
<path d="" fill="#8DB4D8" transform="translate(0,0)"/>
<path d="" fill="#8CB5D8" transform="translate(0,0)"/>
<path d="" fill="#90B9DB" transform="translate(0,0)"/>
<path d="" fill="#8AB2D7" transform="translate(0,0)"/>
<path d="" fill="#8BB0D7" transform="translate(0,0)"/>
<path d="" fill="#8DB7DC" transform="translate(0,0)"/>
<path d="" fill="#8DB8DB" transform="translate(0,0)"/>
<path d="" fill="#8DB6DC" transform="translate(0,0)"/>
<path d="" fill="#8EB7DA" transform="translate(0,0)"/>
<path d="" fill="#8EB7DA" transform="translate(0,0)"/>
<path d="" fill="#8EB7DA" transform="translate(0,0)"/>
<path d="" fill="#8CB6D9" transform="translate(0,0)"/>
<path d="" fill="#88B0D5" transform="translate(0,0)"/>
<path d="" fill="#8DB7DA" transform="translate(0,0)"/>
<path d="" fill="#91B7DC" transform="translate(0,0)"/>
<path d="" fill="#8CB5D9" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#8DB9DD" transform="translate(0,0)"/>
<path d="" fill="#8DB7DC" transform="translate(0,0)"/>
<path d="" fill="#8AB4D8" transform="translate(0,0)"/>
<path d="" fill="#8DB7D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5D8" transform="translate(0,0)"/>
<path d="" fill="#8EBAD9" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#8EB8DB" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8EB7DC" transform="translate(0,0)"/>
<path d="" fill="#8CB6DA" transform="translate(0,0)"/>
<path d="" fill="#87B3D4" transform="translate(0,0)"/>
<path d="" fill="#89B1D5" transform="translate(0,0)"/>
<path d="" fill="#527BA7" transform="translate(0,0)"/>
<path d="" fill="#5D87B1" transform="translate(0,0)"/>
<path d="" fill="#5F87B2" transform="translate(0,0)"/>
<path d="" fill="#608AB4" transform="translate(0,0)"/>
<path d="" fill="#618BB6" transform="translate(0,0)"/>
<path d="" fill="#638CB7" transform="translate(0,0)"/>
<path d="" fill="#648FB8" transform="translate(0,0)"/>
<path d="" fill="#658EBA" transform="translate(0,0)"/>
<path d="" fill="#6591BA" transform="translate(0,0)"/>
<path d="" fill="#6791BC" transform="translate(0,0)"/>
<path d="" fill="#6893BE" transform="translate(0,0)"/>
<path d="" fill="#6B96BF" transform="translate(0,0)"/>
<path d="" fill="#6D97C0" transform="translate(0,0)"/>
<path d="" fill="#719AC4" transform="translate(0,0)"/>
<path d="" fill="#709BC4" transform="translate(0,0)"/>
<path d="" fill="#739EC6" transform="translate(0,0)"/>
<path d="" fill="#749DC7" transform="translate(0,0)"/>
<path d="" fill="#759FC8" transform="translate(0,0)"/>
<path d="" fill="#78A2CA" transform="translate(0,0)"/>
<path d="" fill="#79A4CB" transform="translate(0,0)"/>
<path d="" fill="#7FA9CE" transform="translate(0,0)"/>
<path d="" fill="#80AAD0" transform="translate(0,0)"/>
<path d="" fill="#81AAD1" transform="translate(0,0)"/>
<path d="" fill="#84ACD3" transform="translate(0,0)"/>
<path d="" fill="#84AFD5" transform="translate(0,0)"/>
<path d="" fill="#86AFD4" transform="translate(0,0)"/>
<path d="" fill="#86AFD5" transform="translate(0,0)"/>
<path d="" fill="#87AFD5" transform="translate(0,0)"/>
<path d="" fill="#87B1D6" transform="translate(0,0)"/>
<path d="" fill="#88B1D7" transform="translate(0,0)"/>
<path d="" fill="#87B2D6" transform="translate(0,0)"/>
<path d="" fill="#89B1D7" transform="translate(0,0)"/>
<path d="" fill="#87B1D7" transform="translate(0,0)"/>
<path d="" fill="#8AB1D7" transform="translate(0,0)"/>
<path d="" fill="#87B3D7" transform="translate(0,0)"/>
<path d="" fill="#89B2D9" transform="translate(0,0)"/>
<path d="" fill="#8AB3D8" transform="translate(0,0)"/>
<path d="" fill="#8AB4D8" transform="translate(0,0)"/>
<path d="" fill="#8AB4DA" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#89B4DA" transform="translate(0,0)"/>
<path d="" fill="#8CB5D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5D9" transform="translate(0,0)"/>
<path d="" fill="#8AB5DA" transform="translate(0,0)"/>
<path d="" fill="#8BB4DA" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#8BB4DA" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB4DB" transform="translate(0,0)"/>
<path d="" fill="#8BB4DA" transform="translate(0,0)"/>
<path d="" fill="#8CB4DB" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB4DA" transform="translate(0,0)"/>
<path d="" fill="#8AB3D8" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#8AB3DB" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#8BB4D9" transform="translate(0,0)"/>
<path d="" fill="#8CB4DA" transform="translate(0,0)"/>
<path d="" fill="#8AB3D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5D9" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB5DB" transform="translate(0,0)"/>
<path d="" fill="#8BB5D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB5DB" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8CB5DA" transform="translate(0,0)"/>
<path d="" fill="#8AB5DA" transform="translate(0,0)"/>
<path d="" fill="#8AB5D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5DB" transform="translate(0,0)"/>
<path d="" fill="#8BB5D9" transform="translate(0,0)"/>
<path d="" fill="#8CB4DA" transform="translate(0,0)"/>
<path d="" fill="#8AB4DA" transform="translate(0,0)"/>
<path d="" fill="#8AB5DB" transform="translate(0,0)"/>
<path d="" fill="#8AB5DA" transform="translate(0,0)"/>
<path d="" fill="#8AB3D9" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#8AB3D9" transform="translate(0,0)"/>
<path d="" fill="#8AB4D8" transform="translate(0,0)"/>
<path d="" fill="#8AB4D8" transform="translate(0,0)"/>
<path d="" fill="#88B3D8" transform="translate(0,0)"/>
<path d="" fill="#89B3D8" transform="translate(0,0)"/>
<path d="" fill="#89B3D8" transform="translate(0,0)"/>
<path d="" fill="#88B3D9" transform="translate(0,0)"/>
<path d="" fill="#89B4D8" transform="translate(0,0)"/>
<path d="" fill="#89B3D9" transform="translate(0,0)"/>
<path d="" fill="#88B2D7" transform="translate(0,0)"/>
<path d="" fill="#88B1D8" transform="translate(0,0)"/>
<path d="" fill="#87B2D7" transform="translate(0,0)"/>
<path d="" fill="#86B2D7" transform="translate(0,0)"/>
<path d="" fill="#86B0D6" transform="translate(0,0)"/>
<path d="" fill="#85AFD5" transform="translate(0,0)"/>
<path d="" fill="#86AFD6" transform="translate(0,0)"/>
<path d="" fill="#84AED4" transform="translate(0,0)"/>
<path d="" fill="#82ADD3" transform="translate(0,0)"/>
<path d="" fill="#81ACD3" transform="translate(0,0)"/>
<path d="" fill="#80AAD0" transform="translate(0,0)"/>
<path d="" fill="#7FA8D0" transform="translate(0,0)"/>
<path d="" fill="#7EA9D0" transform="translate(0,0)"/>
<path d="" fill="#7FA9D0" transform="translate(0,0)"/>
<path d="" fill="#7EA9D0" transform="translate(0,0)"/>
<path d="" fill="#7DA8CF" transform="translate(0,0)"/>
<path d="" fill="#7CA6CC" transform="translate(0,0)"/>
<path d="" fill="#7BA4CB" transform="translate(0,0)"/>
<path d="" fill="#79A4CB" transform="translate(0,0)"/>
<path d="" fill="#7AA4CB" transform="translate(0,0)"/>
<path d="" fill="#78A3CA" transform="translate(0,0)"/>
<path d="" fill="#739EC6" transform="translate(0,0)"/>
<path d="" fill="#739DC6" transform="translate(0,0)"/>
<path d="" fill="#7099C3" transform="translate(0,0)"/>
<path d="" fill="#709AC2" transform="translate(0,0)"/>
<path d="" fill="#6F98C2" transform="translate(0,0)"/>
<path d="" fill="#6E99C0" transform="translate(0,0)"/>
<path d="" fill="#6C98C0" transform="translate(0,0)"/>
<path d="" fill="#6C96BF" transform="translate(0,0)"/>
<path d="" fill="#6A93BD" transform="translate(0,0)"/>
<path d="" fill="#6791BB" transform="translate(0,0)"/>
<path d="" fill="#6791BB" transform="translate(0,0)"/>
<path d="" fill="#6490B8" transform="translate(0,0)"/>
<path d="" fill="#648EB9" transform="translate(0,0)"/>
<path d="" fill="#638DB7" transform="translate(0,0)"/>
<path d="" fill="#628DB6" transform="translate(0,0)"/>
<path d="" fill="#618AB4" transform="translate(0,0)"/>
<path d="" fill="#5C87B0" transform="translate(0,0)"/>
<path d="" fill="#8FBADC" transform="translate(0,0)"/>
<path d="" fill="#8AB2D9" transform="translate(0,0)"/>
<path d="" fill="#87B2D7" transform="translate(0,0)"/>
<path d="" fill="#90B9DE" transform="translate(0,0)"/>
<path d="" fill="#90B9DC" transform="translate(0,0)"/>
<path d="" fill="#8EB8DD" transform="translate(0,0)"/>
<path d="" fill="#8DB8DE" transform="translate(0,0)"/>
<path d="" fill="#89B2D7" transform="translate(0,0)"/>
<path d="" fill="#90B9DF" transform="translate(0,0)"/>
<path d="" fill="#91BADF" transform="translate(0,0)"/>
<path d="" fill="#93BCE1" transform="translate(0,0)"/>
<path d="" fill="#8FB9DE" transform="translate(0,0)"/>
<path d="" fill="#8AB4D8" transform="translate(0,0)"/>
<path d="" fill="#8FB8DE" transform="translate(0,0)"/>
<path d="" fill="#87B0D7" transform="translate(0,0)"/>
<path d="" fill="#90B8DD" transform="translate(0,0)"/>
<path d="" fill="#81ACD3" transform="translate(0,0)"/>
<path d="" fill="#87B0D7" transform="translate(0,0)"/>
<path d="" fill="#8AB3D8" transform="translate(0,0)"/>
<path d="" fill="#8CB5D9" transform="translate(0,0)"/>
<path d="" fill="#8FB5DA" transform="translate(0,0)"/>
<path d="" fill="#8DB7DC" transform="translate(0,0)"/>
<path d="" fill="#90BADD" transform="translate(0,0)"/>
<path d="" fill="#8EB8DE" transform="translate(0,0)"/>
<path d="" fill="#8EB8DD" transform="translate(0,0)"/>
<path d="" fill="#91B7DD" transform="translate(0,0)"/>
<path d="" fill="#91BBDE" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#8CB7DD" transform="translate(0,0)"/>
<path d="" fill="#8EB7DC" transform="translate(0,0)"/>
<path d="" fill="#8EB7DC" transform="translate(0,0)"/>
<path d="" fill="#8FB9DC" transform="translate(0,0)"/>
<path d="" fill="#91B9DC" transform="translate(0,0)"/>
<path d="" fill="#90BBE0" transform="translate(0,0)"/>
<path d="" fill="#8AB5D9" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#85ADD1" transform="translate(0,0)"/>
<path d="" fill="#8EB8DD" transform="translate(0,0)"/>
<path d="" fill="#8EB6DC" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#8EB7DC" transform="translate(0,0)"/>
<path d="" fill="#90B9E0" transform="translate(0,0)"/>
<path d="" fill="#92BEE0" transform="translate(0,0)"/>
<path d="" fill="#8FBAE0" transform="translate(0,0)"/>
<path d="" fill="#90B7DD" transform="translate(0,0)"/>
<path d="" fill="#90BADC" transform="translate(0,0)"/>
<path d="" fill="#8EB9DB" transform="translate(0,0)"/>
<path d="" fill="#94BDE0" transform="translate(0,0)"/>
<path d="" fill="#8FB6DE" transform="translate(0,0)"/>
<path d="" fill="#91BADF" transform="translate(0,0)"/>
<path d="" fill="#92BBDF" transform="translate(0,0)"/>
<path d="" fill="#93BEE1" transform="translate(0,0)"/>
<path d="" fill="#90B9DF" transform="translate(0,0)"/>
<path d="" fill="#84AFD2" transform="translate(0,0)"/>
<path d="" fill="#93BDE2" transform="translate(0,0)"/>
<path d="" fill="#93BEE1" transform="translate(0,0)"/>
<path d="" fill="#94BCE2" transform="translate(0,0)"/>
<path d="" fill="#8FB7DC" transform="translate(0,0)"/>
<path d="" fill="#91BADE" transform="translate(0,0)"/>
<path d="" fill="#8DB5DB" transform="translate(0,0)"/>
<path d="" fill="#8DB5DC" transform="translate(0,0)"/>
<path d="" fill="#8FBBDE" transform="translate(0,0)"/>
<path d="" fill="#8FB9DB" transform="translate(0,0)"/>
<path d="" fill="#91BADD" transform="translate(0,0)"/>
<path d="" fill="#93BBDE" transform="translate(0,0)"/>
<path d="" fill="#92BBDF" transform="translate(0,0)"/>
<path d="" fill="#8DB8DA" transform="translate(0,0)"/>
<path d="" fill="#92BADE" transform="translate(0,0)"/>
<path d="" fill="#8FBBDF" transform="translate(0,0)"/>
<path d="" fill="#8EB7DD" transform="translate(0,0)"/>
<path d="" fill="#8FB6DA" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#80ABD1" transform="translate(0,0)"/>
<path d="" fill="#91BCDE" transform="translate(0,0)"/>
<path d="" fill="#8EB8DB" transform="translate(0,0)"/>
<path d="" fill="#8FB7DB" transform="translate(0,0)"/>
<path d="" fill="#92B9DE" transform="translate(0,0)"/>
<path d="" fill="#90BBDF" transform="translate(0,0)"/>
<path d="" fill="#90B9DC" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#91BADF" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#92BADE" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#8EB6DB" transform="translate(0,0)"/>
<path d="" fill="#8FB8DE" transform="translate(0,0)"/>
<path d="" fill="#8EB9DC" transform="translate(0,0)"/>
<path d="" fill="#91B9DF" transform="translate(0,0)"/>
<path d="" fill="#91BBE0" transform="translate(0,0)"/>
<path d="" fill="#83ACD2" transform="translate(0,0)"/>
<path d="" fill="#93BADF" transform="translate(0,0)"/>
<path d="" fill="#91BADD" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#8FB9DE" transform="translate(0,0)"/>
<path d="" fill="#91BADF" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#8FB8DC" transform="translate(0,0)"/>
<path d="" fill="#8FB8DA" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#90BADD" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#91BBDC" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#8FB8DB" transform="translate(0,0)"/>
<path d="" fill="#8DB7D9" transform="translate(0,0)"/>
<path d="" fill="#85AFD3" transform="translate(0,0)"/>
<path d="" fill="#8AB4D9" transform="translate(0,0)"/>
<path d="" fill="#92BCDF" transform="translate(0,0)"/>
<path d="" fill="#91B8DF" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#82AED3" transform="translate(0,0)"/>
<path d="" fill="#8DB9DC" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#92BCDF" transform="translate(0,0)"/>
<path d="" fill="#92BDDE" transform="translate(0,0)"/>
<path d="" fill="#8EB8DA" transform="translate(0,0)"/>
<path d="" fill="#83ADD5" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#8FB9DE" transform="translate(0,0)"/>
<path d="" fill="#8EB7DB" transform="translate(0,0)"/>
<path d="" fill="#90BADC" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#8FBADE" transform="translate(0,0)"/>
<path d="" fill="#90BBDF" transform="translate(0,0)"/>
<path d="" fill="#92B9DD" transform="translate(0,0)"/>
<path d="" fill="#8EBADE" transform="translate(0,0)"/>
<path d="" fill="#92BBE0" transform="translate(0,0)"/>
<path d="" fill="#88B2D8" transform="translate(0,0)"/>
<path d="" fill="#8DB6DA" transform="translate(0,0)"/>
<path d="" fill="#91BDE1" transform="translate(0,0)"/>
<path d="" fill="#94BDE0" transform="translate(0,0)"/>
<path d="" fill="#8EB6DD" transform="translate(0,0)"/>
<path d="" fill="#83AFD4" transform="translate(0,0)"/>
<path d="" fill="#8DB8DD" transform="translate(0,0)"/>
<path d="" fill="#92B9DE" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#90B9DB" transform="translate(0,0)"/>
<path d="" fill="#8FB8DD" transform="translate(0,0)"/>
<path d="" fill="#86B0D5" transform="translate(0,0)"/>
<path d="" fill="#8DBADC" transform="translate(0,0)"/>
<path d="" fill="#90B9DC" transform="translate(0,0)"/>
<path d="" fill="#91B9DE" transform="translate(0,0)"/>
<path d="" fill="#8FB7DC" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#8AB2D8" transform="translate(0,0)"/>
<path d="" fill="#8FBADF" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#91BCE0" transform="translate(0,0)"/>
<path d="" fill="#8FBADD" transform="translate(0,0)"/>
<path d="" fill="#91BADE" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#8DB7DB" transform="translate(0,0)"/>
<path d="" fill="#8DB6DC" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#86B2D8" transform="translate(0,0)"/>
<path d="" fill="#88B7DB" transform="translate(0,0)"/>
<path d="" fill="#8CB4DB" transform="translate(0,0)"/>
<path d="" fill="#92BADF" transform="translate(0,0)"/>
<path d="" fill="#8EB9DB" transform="translate(0,0)"/>
<path d="" fill="#90B9DE" transform="translate(0,0)"/>
<path d="" fill="#8FBADF" transform="translate(0,0)"/>
<path d="" fill="#8EB8DC" transform="translate(0,0)"/>
<path d="" fill="#8DB6DC" transform="translate(0,0)"/>
<path d="" fill="#90B8DC" transform="translate(0,0)"/>
<path d="" fill="#90B8DC" transform="translate(0,0)"/>
<path d="" fill="#8BB6DB" transform="translate(0,0)"/>
<path d="" fill="#90B9DD" transform="translate(0,0)"/>
<path d="" fill="#91BBDF" transform="translate(0,0)"/>
<path d="" fill="#8AB6DC" transform="translate(0,0)"/>
<path d="" fill="#8FBADD" transform="translate(0,0)"/>
<path d="" fill="#8FB8DC" transform="translate(0,0)"/>
<path d="" fill="#8DB9DE" transform="translate(0,0)"/>
<path d="" fill="#8EB8DD" transform="translate(0,0)"/>
<path d="" fill="#92BDE3" transform="translate(0,0)"/>
<path d="" fill="#8BB6D9" transform="translate(0,0)"/>
<path d="" fill="#8CB7DA" transform="translate(0,0)"/>
<path d="" fill="#90B9DC" transform="translate(0,0)"/>
<path d="" fill="#8DB5DA" transform="translate(0,0)"/>
<path d="" fill="#81AED3" transform="translate(0,0)"/>
<path d="" fill="#82ACD4" transform="translate(0,0)"/>
<path d="" fill="#8AB5DA" transform="translate(0,0)"/>
<path d="" fill="#8EB8DB" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#91B9DF" transform="translate(0,0)"/>
<path d="" fill="#8DB8DC" transform="translate(0,0)"/>
<path d="" fill="#8EB8DD" transform="translate(0,0)"/>
<path d="" fill="#90BADE" transform="translate(0,0)"/>
<path d="" fill="#91BBE0" transform="translate(0,0)"/>
<path d="" fill="#8CB5DB" transform="translate(0,0)"/>
<path d="" fill="#93BBDF" transform="translate(0,0)"/>
<path d="" fill="#8FB9DE" transform="translate(0,0)"/>
<path d="" fill="#8BB7D9" transform="translate(0,0)"/>
<path d="" fill="#8EB9DE" transform="translate(0,0)"/>
<path d="" fill="#8CB6DB" transform="translate(0,0)"/>
<path d="" fill="#8CB6DB" transform="translate(0,0)"/>
<path d="" fill="#8DB8DD" transform="translate(0,0)"/>
<path d="" fill="#8DB8DD" transform="translate(0,0)"/>
<path d="" fill="#8EB7DD" transform="translate(0,0)"/>
<path d="" fill="#8DB6DB" transform="translate(0,0)"/>
<path d="" fill="#8BB6D9" transform="translate(0,0)"/>
<path d="" fill="#8AB6DA" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#88B4DB" transform="translate(0,0)"/>
<path d="" fill="#8BB3D7" transform="translate(0,0)"/>
<path d="" fill="#87B1D8" transform="translate(0,0)"/>
<path d="" fill="#89B4D9" transform="translate(0,0)"/>
<path d="" fill="#87B3DB" transform="translate(0,0)"/>
<path d="" fill="#8AB3D9" transform="translate(0,0)"/>
<path d="" fill="#8BB5DA" transform="translate(0,0)"/>
<path d="" fill="#7AA4CC" transform="translate(0,0)"/>
<path d="" fill="#8CB9DD" transform="translate(0,0)"/>
<path d="" fill="#8FBAE0" transform="translate(0,0)"/>
<path d="" fill="#8CB7DB" transform="translate(0,0)"/>
<path d="" fill="#89B2D8" transform="translate(0,0)"/>
<path d="" fill="#86B0D5" transform="translate(0,0)"/>
<path d="" fill="#87B1D6" transform="translate(0,0)"/>
<path d="" fill="#85AED3" transform="translate(0,0)"/>
<path d="" fill="#7AA5CD" transform="translate(0,0)"/>
<path d="" fill="#89B7DE" transform="translate(0,0)"/>
<path d="" fill="#8EBADE" transform="translate(0,0)"/>
<path d="" fill="#8CB7DC" transform="translate(0,0)"/>
<path d="" fill="#8BB7DD" transform="translate(0,0)"/>
<path d="" fill="#8CB8DC" transform="translate(0,0)"/>
<path d="" fill="#84AFD5" transform="translate(0,0)"/>
<path d="" fill="#8CB8DD" transform="translate(0,0)"/>
<path d="" fill="#89B4D8" transform="translate(0,0)"/>
<path d="" fill="#89B3D8" transform="translate(0,0)"/>
<path d="" fill="#8FB9DD" transform="translate(0,0)"/>
<path d="" fill="#8DBADF" transform="translate(0,0)"/>
<path d="" fill="#8BB4DA" transform="translate(0,0)"/>
<path d="" fill="#88B4DA" transform="translate(0,0)"/>
<path d="" fill="#88B6DA" transform="translate(0,0)"/>
<path d="" fill="#84AED6" transform="translate(0,0)"/>
<path d="" fill="#7EA6CB" transform="translate(0,0)"/>
<path d="" fill="#87B1D7" transform="translate(0,0)"/>
<path d="" fill="#87B1D8" transform="translate(0,0)"/>
<path d="" fill="#87B3D8" transform="translate(0,0)"/>
<path d="" fill="#86AFD5" transform="translate(0,0)"/>
<path d="" fill="#89B4D8" transform="translate(0,0)"/>
<path d="" fill="#89B1D5" transform="translate(0,0)"/>
<path d="" fill="#88B3D8" transform="translate(0,0)"/>
<path d="" fill="#86B3D8" transform="translate(0,0)"/>
<path d="" fill="#8CB8DC" transform="translate(0,0)"/>
<path d="" fill="#8BB2D8" transform="translate(0,0)"/>
<path d="" fill="#85B0D5" transform="translate(0,0)"/>
<path d="" fill="#6995C6" transform="translate(0,0)"/>
<path d="" fill="#6895C5" transform="translate(0,0)"/>
<path d="" fill="#6897C4" transform="translate(0,0)"/>
<path d="" fill="#6896C4" transform="translate(0,0)"/>
<path d="" fill="#6896C6" transform="translate(0,0)"/>
<path d="" fill="#6796C5" transform="translate(0,0)"/>
<path d="" fill="#6997C6" transform="translate(0,0)"/>
<path d="" fill="#6996C6" transform="translate(0,0)"/>
<path d="" fill="#6494C3" transform="translate(0,0)"/>
<path d="" fill="#6A96C4" transform="translate(0,0)"/>
<path d="" fill="#4A78AA" transform="translate(0,0)"/>
<path d="" fill="#6794C2" transform="translate(0,0)"/>
<path d="" fill="#4978AD" transform="translate(0,0)"/>
<path d="" fill="#4E7CB0" transform="translate(0,0)"/>
<path d="" fill="#4E7EB3" transform="translate(0,0)"/>
<path d="" fill="#4D7DB1" transform="translate(0,0)"/>
<path d="" fill="#4F7FB4" transform="translate(0,0)"/>
<path d="" fill="#4E80B4" transform="translate(0,0)"/>
<path d="" fill="#4A76A9" transform="translate(0,0)"/>
<path d="" fill="#4976A9" transform="translate(0,0)"/>
<path d="" fill="#4574A8" transform="translate(0,0)"/>
<path d="" fill="#4C79AF" transform="translate(0,0)"/>
<path d="" fill="#4877AB" transform="translate(0,0)"/>
<path d="" fill="#5182B5" transform="translate(0,0)"/>
<path d="" fill="#4976AB" transform="translate(0,0)"/>
<path d="" fill="#5181B3" transform="translate(0,0)"/>
<path d="" fill="#4F7FB5" transform="translate(0,0)"/>
<path d="" fill="#4877AA" transform="translate(0,0)"/>
<path d="" fill="#4A77AA" transform="translate(0,0)"/>
<path d="" fill="#648EBE" transform="translate(0,0)"/>
<path d="" fill="#5B84B2" transform="translate(0,0)"/>
<path d="" fill="#5883B2" transform="translate(0,0)"/>
<path d="" fill="#5B85B1" transform="translate(0,0)"/>
<path d="" fill="#638EBD" transform="translate(0,0)"/>
<path d="" fill="#668EBC" transform="translate(0,0)"/>
<path d="" fill="#618DBB" transform="translate(0,0)"/>
<path d="" fill="#5885B3" transform="translate(0,0)"/>
<path d="" fill="#5A83B1" transform="translate(0,0)"/>
<path d="" fill="#4876AB" transform="translate(0,0)"/>
<path d="" fill="#4F80B4" transform="translate(0,0)"/>
<path d="" fill="#668EBC" transform="translate(0,0)"/>
<path d="" fill="#5882B0" transform="translate(0,0)"/>
<path d="" fill="#668EBC" transform="translate(0,0)"/>
<path d="" fill="#507FB4" transform="translate(0,0)"/>
<path d="" fill="#658DBC" transform="translate(0,0)"/>
<path d="" fill="#638BBA" transform="translate(0,0)"/>
<path d="" fill="#4E7FB6" transform="translate(0,0)"/>
<path d="" fill="#5885B5" transform="translate(0,0)"/>
<path d="" fill="#648CBA" transform="translate(0,0)"/>
<path d="" fill="#4E80B5" transform="translate(0,0)"/>
<path d="" fill="#648DBB" transform="translate(0,0)"/>
<path d="" fill="#4C7AAE" transform="translate(0,0)"/>
<path d="" fill="#5883B2" transform="translate(0,0)"/>
<path d="" fill="#4976AA" transform="translate(0,0)"/>
<path d="" fill="#668DB9" transform="translate(0,0)"/>
<path d="" fill="#648CBA" transform="translate(0,0)"/>
<path d="" fill="#5982AE" transform="translate(0,0)"/>
<path d="" fill="#5984B4" transform="translate(0,0)"/>
<path d="" fill="#658CB8" transform="translate(0,0)"/>
<path d="" fill="#658BBA" transform="translate(0,0)"/>
<path d="" fill="#648CBA" transform="translate(0,0)"/>
<path d="" fill="#648CBD" transform="translate(0,0)"/>
<path d="" fill="#628BBB" transform="translate(0,0)"/>
<path d="" fill="#648BB9" transform="translate(0,0)"/>
<path d="" fill="#628ABA" transform="translate(0,0)"/>
<path d="" fill="#638BB9" transform="translate(0,0)"/>
<path d="" fill="#6689B5" transform="translate(0,0)"/>
<path d="" fill="#648ABA" transform="translate(0,0)"/>
<path d="" fill="#5780B1" transform="translate(0,0)"/>
<path d="" fill="#4877AA" transform="translate(0,0)"/>
<path d="" fill="#5A84B1" transform="translate(0,0)"/>
<path d="" fill="#577FB0" transform="translate(0,0)"/>
<path d="" fill="#4E7FB4" transform="translate(0,0)"/>
<path d="" fill="#668AB7" transform="translate(0,0)"/>
<path d="" fill="#5882B0" transform="translate(0,0)"/>
<path d="" fill="#4E7FB4" transform="translate(0,0)"/>
<path d="" fill="#648AB6" transform="translate(0,0)"/>
<path d="" fill="#658AB5" transform="translate(0,0)"/>
<path d="" fill="#6389B3" transform="translate(0,0)"/>
<path d="" fill="#5681AE" transform="translate(0,0)"/>
<path d="" fill="#4876AA" transform="translate(0,0)"/>
<path d="" fill="#5080B2" transform="translate(0,0)"/>
<path d="" fill="#6387B3" transform="translate(0,0)"/>
<path d="" fill="#6387B3" transform="translate(0,0)"/>
<path d="" fill="#4F7EB4" transform="translate(0,0)"/>
<path d="" fill="#6888B1" transform="translate(0,0)"/>
<path d="" fill="#567EAE" transform="translate(0,0)"/>
<path d="" fill="#507FB4" transform="translate(0,0)"/>
<path d="" fill="#6485B1" transform="translate(0,0)"/>
<path d="" fill="#6487B1" transform="translate(0,0)"/>
<path d="" fill="#6286B0" transform="translate(0,0)"/>
<path d="" fill="#4E7EB3" transform="translate(0,0)"/>
<path d="" fill="#6586B1" transform="translate(0,0)"/>
<path d="" fill="#4F80B3" transform="translate(0,0)"/>
<path d="" fill="#4F80B4" transform="translate(0,0)"/>
<path d="" fill="#6984A9" transform="translate(0,0)"/>
<path d="" fill="#6983AA" transform="translate(0,0)"/>
<path d="" fill="#6A82AA" transform="translate(0,0)"/>
<path d="" fill="#6986AC" transform="translate(0,0)"/>
<path d="" fill="#6B85AB" transform="translate(0,0)"/>
<path d="" fill="#6C85AA" transform="translate(0,0)"/>
<path d="" fill="#6C81A8" transform="translate(0,0)"/>
<path d="" fill="#6980A7" transform="translate(0,0)"/>
<path d="" fill="#6C83A8" transform="translate(0,0)"/>
<path d="" fill="#4977AA" transform="translate(0,0)"/>
<path d="" fill="#5080B3" transform="translate(0,0)"/>
<path d="" fill="#547EAD" transform="translate(0,0)"/>
<path d="" fill="#4B79AE" transform="translate(0,0)"/>
<path d="" fill="#4F7FB3" transform="translate(0,0)"/>
<path d="" fill="#5080B6" transform="translate(0,0)"/>
<path d="" fill="#4C79AD" transform="translate(0,0)"/>
<path d="" fill="#4878A9" transform="translate(0,0)"/>
<path d="" fill="#527FB1" transform="translate(0,0)"/>
<path d="" fill="#5080B4" transform="translate(0,0)"/>
<path d="" fill="#4C79AD" transform="translate(0,0)"/>
<path d="" fill="#4E7CAF" transform="translate(0,0)"/>
<path d="" fill="#4F7DAF" transform="translate(0,0)"/>
<path d="" fill="#4B76A9" transform="translate(0,0)"/>
<path d="" fill="#4C7BB0" transform="translate(0,0)"/>
<path d="" fill="#4F7DB4" transform="translate(0,0)"/>
<path d="" fill="#4F7FB3" transform="translate(0,0)"/>
<path d="" fill="#4F7FB4" transform="translate(0,0)"/>
<path d="" fill="#4F79AB" transform="translate(0,0)"/>
<path d="" fill="#517CAC" transform="translate(0,0)"/>
<path d="" fill="#4F77A6" transform="translate(0,0)"/>
<path d="" fill="#527CAE" transform="translate(0,0)"/>
<path d="" fill="#9C6F74" transform="translate(0,0)"/>
<path d="" fill="#A37175" transform="translate(0,0)"/>
<path d="" fill="#A07174" transform="translate(0,0)"/>
<path d="" fill="#A1706F" transform="translate(0,0)"/>
<path d="" fill="#A37375" transform="translate(0,0)"/>
<path d="" fill="#9F7276" transform="translate(0,0)"/>
<path d="" fill="#967179" transform="translate(0,0)"/>
<path d="" fill="#A57172" transform="translate(0,0)"/>
<path d="" fill="#4A74A6" transform="translate(0,0)"/>
<path d="" fill="#987278" transform="translate(0,0)"/>
<path d="" fill="#9F6E74" transform="translate(0,0)"/>
<path d="" fill="#9F7071" transform="translate(0,0)"/>
<path d="" fill="#A26F74" transform="translate(0,0)"/>
<path d="" fill="#A17071" transform="translate(0,0)"/>
<path d="" fill="#A47172" transform="translate(0,0)"/>
<path d="" fill="#A07374" transform="translate(0,0)"/>
<path d="" fill="#547DAC" transform="translate(0,0)"/>
<path d="" fill="#986C74" transform="translate(0,0)"/>
<path d="" fill="#A27070" transform="translate(0,0)"/>
<path d="" fill="#A66F72" transform="translate(0,0)"/>
<path d="" fill="#A77173" transform="translate(0,0)"/>
<path d="" fill="#A37071" transform="translate(0,0)"/>
<path d="" fill="#A67173" transform="translate(0,0)"/>
<path d="" fill="#A97172" transform="translate(0,0)"/>
<path d="" fill="#A9726F" transform="translate(0,0)"/>
<path d="" fill="#A67072" transform="translate(0,0)"/>
<path d="" fill="#A6706E" transform="translate(0,0)"/>
<path d="" fill="#A26E6E" transform="translate(0,0)"/>
<path d="" fill="#A57271" transform="translate(0,0)"/>
<path d="" fill="#A47070" transform="translate(0,0)"/>
<path d="" fill="#A77172" transform="translate(0,0)"/>
<path d="" fill="#A57273" transform="translate(0,0)"/>
<path d="" fill="#A27173" transform="translate(0,0)"/>
<path d="" fill="#A06F74" transform="translate(0,0)"/>
<path d="" fill="#A36E6F" transform="translate(0,0)"/>
<path d="" fill="#A66F71" transform="translate(0,0)"/>
<path d="" fill="#A46E71" transform="translate(0,0)"/>
<path d="" fill="#A76D6A" transform="translate(0,0)"/>
<path d="" fill="#A86F68" transform="translate(0,0)"/>
<path d="" fill="#AE706C" transform="translate(0,0)"/>
<path d="" fill="#4A75A6" transform="translate(0,0)"/>
<path d="" fill="#AA6E6E" transform="translate(0,0)"/>
<path d="" fill="#4A76A7" transform="translate(0,0)"/>
<path d="" fill="#AA6C6E" transform="translate(0,0)"/>
<path d="" fill="#AE6E6B" transform="translate(0,0)"/>
<path d="" fill="#A96B6A" transform="translate(0,0)"/>
<path d="" fill="#AC6F6A" transform="translate(0,0)"/>
<path d="" fill="#AC6E6C" transform="translate(0,0)"/>
<path d="" fill="#A66E6F" transform="translate(0,0)"/>
<path d="" fill="#AD6E6D" transform="translate(0,0)"/>
<path d="" fill="#AE6E69" transform="translate(0,0)"/>
<path d="" fill="#AD6F6B" transform="translate(0,0)"/>
<path d="" fill="#AA6E69" transform="translate(0,0)"/>
<path d="" fill="#A07074" transform="translate(0,0)"/>
<path d="" fill="#B26E67" transform="translate(0,0)"/>
<path d="" fill="#AD706C" transform="translate(0,0)"/>
<path d="" fill="#B16E6C" transform="translate(0,0)"/>
<path d="" fill="#A77071" transform="translate(0,0)"/>
<path d="" fill="#A47071" transform="translate(0,0)"/>
<path d="" fill="#577EAC" transform="translate(0,0)"/>
<path d="" fill="#4D76A8" transform="translate(0,0)"/>
<path d="" fill="#AB6D6B" transform="translate(0,0)"/>
<path d="" fill="#B16E65" transform="translate(0,0)"/>
<path d="" fill="#B16D66" transform="translate(0,0)"/>
<path d="" fill="#B16E69" transform="translate(0,0)"/>
<path d="" fill="#AE6F69" transform="translate(0,0)"/>
<path d="" fill="#4975A8" transform="translate(0,0)"/>
<path d="" fill="#BD6E66" transform="translate(0,0)"/>
<path d="" fill="#B96C62" transform="translate(0,0)"/>
<path d="" fill="#B16E68" transform="translate(0,0)"/>
<path d="" fill="#AD6C69" transform="translate(0,0)"/>
<path d="" fill="#B5675B" transform="translate(0,0)"/>
<path d="" fill="#B86F68" transform="translate(0,0)"/>
<path d="" fill="#BF6D60" transform="translate(0,0)"/>
<path d="" fill="#B56F66" transform="translate(0,0)"/>
<path d="" fill="#C66D60" transform="translate(0,0)"/>
<path d="" fill="#BC6E62" transform="translate(0,0)"/>
<path d="" fill="#BE6C5E" transform="translate(0,0)"/>
<path d="" fill="#B86A60" transform="translate(0,0)"/>
<path d="" fill="#4D76A8" transform="translate(0,0)"/>
<path d="" fill="#B66D65" transform="translate(0,0)"/>
<path d="" fill="#BE6A5C" transform="translate(0,0)"/>
<path d="" fill="#B26D65" transform="translate(0,0)"/>
<path d="" fill="#C26E5D" transform="translate(0,0)"/>
<path d="" fill="#BD6F62" transform="translate(0,0)"/>
<path d="" fill="#BE7162" transform="translate(0,0)"/>
<path d="" fill="#BA6A5F" transform="translate(0,0)"/>
<path d="" fill="#5E7EA9" transform="translate(0,0)"/>
<path d="" fill="#C06B5A" transform="translate(0,0)"/>
<path d="" fill="#C26B5A" transform="translate(0,0)"/>
<path d="" fill="#B26D63" transform="translate(0,0)"/>
<path d="" fill="#BF6D5C" transform="translate(0,0)"/>
<path d="" fill="#BD6D5F" transform="translate(0,0)"/>
<path d="" fill="#C26A5C" transform="translate(0,0)"/>
<path d="" fill="#BE6F60" transform="translate(0,0)"/>
<path d="" fill="#C36956" transform="translate(0,0)"/>
<path d="" fill="#C06C59" transform="translate(0,0)"/>
<path d="" fill="#C26D5A" transform="translate(0,0)"/>
<path d="" fill="#C16F61" transform="translate(0,0)"/>
<path d="" fill="#BB6C62" transform="translate(0,0)"/>
<path d="" fill="#94AAC1" transform="translate(0,0)"/>
<path d="" fill="#C9705D" transform="translate(0,0)"/>
<path d="" fill="#BF7161" transform="translate(0,0)"/>
<path d="" fill="#BC6A5A" transform="translate(0,0)"/>
<path d="" fill="#BE6F62" transform="translate(0,0)"/>
<path d="" fill="#C36C58" transform="translate(0,0)"/>
<path d="" fill="#96A9C0" transform="translate(0,0)"/>
<path d="" fill="#99AEC2" transform="translate(0,0)"/>
<path d="" fill="#CA715E" transform="translate(0,0)"/>
<path d="" fill="#BC6E60" transform="translate(0,0)"/>
<path d="" fill="#BE725F" transform="translate(0,0)"/>
<path d="" fill="#C76E59" transform="translate(0,0)"/>
<path d="" fill="#BB7264" transform="translate(0,0)"/>
<path d="" fill="#C17364" transform="translate(0,0)"/>
<path d="" fill="#C36F5F" transform="translate(0,0)"/>
<path d="" fill="#C66E5A" transform="translate(0,0)"/>
<path d="" fill="#99AEC2" transform="translate(0,0)"/>
<path d="" fill="#A0B5C5" transform="translate(0,0)"/>
<path d="" fill="#5D7EAB" transform="translate(0,0)"/>
<path d="" fill="#C6715F" transform="translate(0,0)"/>
<path d="" fill="#C46C5A" transform="translate(0,0)"/>
<path d="" fill="#A2B5C6" transform="translate(0,0)"/>
<path d="" fill="#CA6C53" transform="translate(0,0)"/>
<path d="" fill="#C46E58" transform="translate(0,0)"/>
<path d="" fill="#C66F5F" transform="translate(0,0)"/>
<path d="" fill="#9FB3C5" transform="translate(0,0)"/>
<path d="" fill="#BE6D5C" transform="translate(0,0)"/>
<path d="" fill="#BE7061" transform="translate(0,0)"/>
<path d="" fill="#C6705C" transform="translate(0,0)"/>
<path d="" fill="#CA6C53" transform="translate(0,0)"/>
<path d="" fill="#BE715B" transform="translate(0,0)"/>
<path d="" fill="#C06E5D" transform="translate(0,0)"/>
<path d="" fill="#BE6F5C" transform="translate(0,0)"/>
<path d="" fill="#A4B7CA" transform="translate(0,0)"/>
<path d="" fill="#A8B9C9" transform="translate(0,0)"/>
<path d="" fill="#CC6A51" transform="translate(0,0)"/>
<path d="" fill="#C46D5B" transform="translate(0,0)"/>
<path d="" fill="#A5BAC9" transform="translate(0,0)"/>
<path d="" fill="#CE7054" transform="translate(0,0)"/>
<path d="" fill="#C76F59" transform="translate(0,0)"/>
<path d="" fill="#C56F5A" transform="translate(0,0)"/>
<path d="" fill="#A6B7C8" transform="translate(0,0)"/>
<path d="" fill="#A9BBCA" transform="translate(0,0)"/>
<path d="" fill="#C97058" transform="translate(0,0)"/>
<path d="" fill="#CC6A53" transform="translate(0,0)"/>
<path d="" fill="#AABBCB" transform="translate(0,0)"/>
<path d="" fill="#CB715B" transform="translate(0,0)"/>
<path d="" fill="#BF7061" transform="translate(0,0)"/>
<path d="" fill="#A3B6C6" transform="translate(0,0)"/>
<path d="" fill="#C5715D" transform="translate(0,0)"/>
<path d="" fill="#C77059" transform="translate(0,0)"/>
<path d="" fill="#A0B4C6" transform="translate(0,0)"/>
<path d="" fill="#CE6F55" transform="translate(0,0)"/>
<path d="" fill="#9FB2C3" transform="translate(0,0)"/>
<path d="" fill="#A6B8C7" transform="translate(0,0)"/>
<path d="" fill="#C47159" transform="translate(0,0)"/>
<path d="" fill="#CA7058" transform="translate(0,0)"/>
<path d="" fill="#A4B8C9" transform="translate(0,0)"/>
<path d="" fill="#A5B3C4" transform="translate(0,0)"/>
<path d="" fill="#CB684C" transform="translate(0,0)"/>
<path d="" fill="#C87057" transform="translate(0,0)"/>
<path d="" fill="#C17161" transform="translate(0,0)"/>
<path d="" fill="#CE6E54" transform="translate(0,0)"/>
<path d="" fill="#BC7362" transform="translate(0,0)"/>
<path d="" fill="#A0B1C2" transform="translate(0,0)"/>
<path d="" fill="#BB715F" transform="translate(0,0)"/>
<path d="" fill="#6580A7" transform="translate(0,0)"/>
<path d="" fill="#6081A8" transform="translate(0,0)"/>
<path d="" fill="#CC6B50" transform="translate(0,0)"/>
<path d="" fill="#9EB4C6" transform="translate(0,0)"/>
<path d="" fill="#CC7055" transform="translate(0,0)"/>
<path d="" fill="#9FB3C4" transform="translate(0,0)"/>
<path d="" fill="#A6B7C6" transform="translate(0,0)"/>
<path d="" fill="#A3B5C4" transform="translate(0,0)"/>
<path d="" fill="#ABBBCA" transform="translate(0,0)"/>
<path d="" fill="#ADBDCA" transform="translate(0,0)"/>
<path d="" fill="#C77258" transform="translate(0,0)"/>
<path d="" fill="#D9704B" transform="translate(0,0)"/>
<path d="" fill="#D86E4B" transform="translate(0,0)"/>
<path d="" fill="#CF6F58" transform="translate(0,0)"/>
<path d="" fill="#CE6F50" transform="translate(0,0)"/>
<path d="" fill="#D7704B" transform="translate(0,0)"/>
<path d="" fill="#CD664A" transform="translate(0,0)"/>
<path d="" fill="#C87159" transform="translate(0,0)"/>
<path d="" fill="#C96E55" transform="translate(0,0)"/>
<path d="" fill="#D46F51" transform="translate(0,0)"/>
<path d="" fill="#BC7161" transform="translate(0,0)"/>
<path d="" fill="#D16E4F" transform="translate(0,0)"/>
<path d="" fill="#CA6F56" transform="translate(0,0)"/>
<path d="" fill="#C36C58" transform="translate(0,0)"/>
<path d="" fill="#C77059" transform="translate(0,0)"/>
<path d="" fill="#CA6E55" transform="translate(0,0)"/>
<path d="" fill="#A2B6C6" transform="translate(0,0)"/>
<path d="" fill="#C97156" transform="translate(0,0)"/>
<path d="" fill="#D5704E" transform="translate(0,0)"/>
<path d="" fill="#D06E52" transform="translate(0,0)"/>
<path d="" fill="#A7B9C8" transform="translate(0,0)"/>
<path d="" fill="#D6704E" transform="translate(0,0)"/>
<path d="" fill="#A3B7C8" transform="translate(0,0)"/>
<path d="" fill="#ABBDCB" transform="translate(0,0)"/>
<path d="" fill="#D06E4F" transform="translate(0,0)"/>
<path d="" fill="#A9BBCA" transform="translate(0,0)"/>
<path d="" fill="#CB7056" transform="translate(0,0)"/>
<path d="" fill="#CF7051" transform="translate(0,0)"/>
<path d="" fill="#527BAB" transform="translate(0,0)"/>
<path d="" fill="#A6B8C8" transform="translate(0,0)"/>
<path d="" fill="#C97054" transform="translate(0,0)"/>
<path d="" fill="#D16F51" transform="translate(0,0)"/>
<path d="" fill="#A4B6C5" transform="translate(0,0)"/>
<path d="" fill="#6081A9" transform="translate(0,0)"/>
<path d="" fill="#527BA7" transform="translate(0,0)"/>
<path d="" fill="#527AA8" transform="translate(0,0)"/>
<path d="" fill="#D16E4B" transform="translate(0,0)"/>
<path d="" fill="#D06D50" transform="translate(0,0)"/>
<path d="" fill="#D3704B" transform="translate(0,0)"/>
<path d="" fill="#A1B2C2" transform="translate(0,0)"/>
<path d="" fill="#CE6C51" transform="translate(0,0)"/>
<path d="" fill="#C77155" transform="translate(0,0)"/>
<path d="" fill="#D46E4D" transform="translate(0,0)"/>
<path d="" fill="#D16E4F" transform="translate(0,0)"/>
<path d="" fill="#CE6C4E" transform="translate(0,0)"/>
<path d="" fill="#9FB0C0" transform="translate(0,0)"/>
<path d="" fill="#CF6B4B" transform="translate(0,0)"/>
<path d="" fill="#CD6C4E" transform="translate(0,0)"/>
<path d="" fill="#5F7FA8" transform="translate(0,0)"/>
<path d="" fill="#D76F4C" transform="translate(0,0)"/>
<path d="" fill="#5C7EAA" transform="translate(0,0)"/>
<path d="" fill="#C97057" transform="translate(0,0)"/>
<path d="" fill="#CA6B4D" transform="translate(0,0)"/>
<path d="" fill="#CE6D4F" transform="translate(0,0)"/>
<path d="" fill="#CC6E53" transform="translate(0,0)"/>
<path d="" fill="#CF6E4F" transform="translate(0,0)"/>
<path d="" fill="#D76B4B" transform="translate(0,0)"/>
<path d="" fill="#D16F4D" transform="translate(0,0)"/>
<path d="" fill="#D36B4A" transform="translate(0,0)"/>
<path d="" fill="#D1694E" transform="translate(0,0)"/>
<path d="" fill="#CB6F55" transform="translate(0,0)"/>
<path d="" fill="#98A7BB" transform="translate(0,0)"/>
<path d="" fill="#D26C4B" transform="translate(0,0)"/>
<path d="" fill="#D66E49" transform="translate(0,0)"/>
<path d="" fill="#CC6C4F" transform="translate(0,0)"/>
<path d="" fill="#D96D4B" transform="translate(0,0)"/>
<path d="" fill="#D36E4C" transform="translate(0,0)"/>
<path d="" fill="#CE6E50" transform="translate(0,0)"/>
<path d="" fill="#C96F55" transform="translate(0,0)"/>
<path d="" fill="#C97157" transform="translate(0,0)"/>
<path d="" fill="#D3704C" transform="translate(0,0)"/>
<path d="" fill="#CE7357" transform="translate(0,0)"/>
<path d="" fill="#CC6C50" transform="translate(0,0)"/>
<path d="" fill="#C56F56" transform="translate(0,0)"/>
<path d="" fill="#D06D4F" transform="translate(0,0)"/>
<path d="" fill="#C96C52" transform="translate(0,0)"/>
<path d="" fill="#CF7055" transform="translate(0,0)"/>
<path d="" fill="#D46E4D" transform="translate(0,0)"/>
<path d="" fill="#CF6E51" transform="translate(0,0)"/>
<path d="" fill="#D27051" transform="translate(0,0)"/>
<path d="" fill="#D16E50" transform="translate(0,0)"/>
<path d="" fill="#CC6E53" transform="translate(0,0)"/>
<path d="" fill="#CD7153" transform="translate(0,0)"/>
<path d="" fill="#D46E4C" transform="translate(0,0)"/>
<path d="" fill="#CD6D53" transform="translate(0,0)"/>
<path d="" fill="#D4704C" transform="translate(0,0)"/>
<path d="" fill="#D36E4E" transform="translate(0,0)"/>
<path d="" fill="#CD6E52" transform="translate(0,0)"/>
<path d="" fill="#CE6F51" transform="translate(0,0)"/>
<path d="" fill="#C47057" transform="translate(0,0)"/>
<path d="" fill="#D46E4B" transform="translate(0,0)"/>
<path d="" fill="#D06D4D" transform="translate(0,0)"/>
<path d="" fill="#D17153" transform="translate(0,0)"/>
<path d="" fill="#C07059" transform="translate(0,0)"/>
<path d="" fill="#C96D50" transform="translate(0,0)"/>
<path d="" fill="#D57250" transform="translate(0,0)"/>
<path d="" fill="#CB6E50" transform="translate(0,0)"/>
<path d="" fill="#D0704F" transform="translate(0,0)"/>
<path d="" fill="#CC6D50" transform="translate(0,0)"/>
<path d="" fill="#CF7150" transform="translate(0,0)"/>
<path d="" fill="#CF6C4E" transform="translate(0,0)"/>
<path d="" fill="#CE7050" transform="translate(0,0)"/>
<path d="" fill="#CB6F53" transform="translate(0,0)"/>
<path d="" fill="#C57056" transform="translate(0,0)"/>
<path d="" fill="#CE6F54" transform="translate(0,0)"/>
<path d="" fill="#CD6B4D" transform="translate(0,0)"/>
<path d="" fill="#D26E4D" transform="translate(0,0)"/>
<path d="" fill="#CB6E51" transform="translate(0,0)"/>
<path d="" fill="#C26E56" transform="translate(0,0)"/>
<path d="" fill="#CE6C4D" transform="translate(0,0)"/>
<path d="" fill="#CF6E54" transform="translate(0,0)"/>
<path d="" fill="#CA7253" transform="translate(0,0)"/>
<path d="" fill="#C57259" transform="translate(0,0)"/>
<path d="" fill="#D16F51" transform="translate(0,0)"/>
<path d="" fill="#CB6F55" transform="translate(0,0)"/>
<path d="" fill="#CD6F53" transform="translate(0,0)"/>
<path d="" fill="#D37052" transform="translate(0,0)"/>
<path d="" fill="#D36F4F" transform="translate(0,0)"/>
<path d="" fill="#D06D50" transform="translate(0,0)"/>
<path d="" fill="#C36D56" transform="translate(0,0)"/>
<path d="" fill="#C46F55" transform="translate(0,0)"/>
<path d="" fill="#CF6F55" transform="translate(0,0)"/>
<path d="" fill="#CE6F51" transform="translate(0,0)"/>
<path d="" fill="#CF6F51" transform="translate(0,0)"/>
<path d="" fill="#CD6E4F" transform="translate(0,0)"/>
<path d="" fill="#CD6E54" transform="translate(0,0)"/>
<path d="" fill="#D27052" transform="translate(0,0)"/>
<path d="" fill="#CE6F52" transform="translate(0,0)"/>
<path d="" fill="#D06E50" transform="translate(0,0)"/>
<path d="" fill="#D06F50" transform="translate(0,0)"/>
<path d="" fill="#D06D4D" transform="translate(0,0)"/>
<path d="" fill="#D26E52" transform="translate(0,0)"/>
<path d="" fill="#CE6F52" transform="translate(0,0)"/>
<path d="" fill="#CD6F4F" transform="translate(0,0)"/>
<path d="" fill="#D37253" transform="translate(0,0)"/>
<path d="" fill="#D16E4F" transform="translate(0,0)"/>
<path d="" fill="#CD6F50" transform="translate(0,0)"/>
<path d="" fill="#D07052" transform="translate(0,0)"/>
<path d="" fill="#CD6E52" transform="translate(0,0)"/>
<path d="" fill="#D06F4F" transform="translate(0,0)"/>
<path d="" fill="#CF704F" transform="translate(0,0)"/>
<path d="" fill="#CE6E52" transform="translate(0,0)"/>
<path d="" fill="#B2746B" transform="translate(0,0)"/>
<path d="" fill="#B97366" transform="translate(0,0)"/>
<path d="" fill="#BC7465" transform="translate(0,0)"/>
<path d="" fill="#BD7266" transform="translate(0,0)"/>
<path d="" fill="#BC7165" transform="translate(0,0)"/>
<path d="" fill="#C8705C" transform="translate(0,0)"/>
<path d="" fill="#B6746A" transform="translate(0,0)"/>
<path d="" fill="#B47269" transform="translate(0,0)"/>
<path d="" fill="#C2715D" transform="translate(0,0)"/>
<path d="" fill="#BC7061" transform="translate(0,0)"/>
<path d="" fill="#C76F59" transform="translate(0,0)"/>
<path d="" fill="#C36D58" transform="translate(0,0)"/>
<path d="" fill="#C16F5B" transform="translate(0,0)"/>
<path d="" fill="#B77062" transform="translate(0,0)"/>
<path d="" fill="#C06E5F" transform="translate(0,0)"/>
<path d="" fill="#AE726B" transform="translate(0,0)"/>
<path d="" fill="#C76D5A" transform="translate(0,0)"/>
<path d="" fill="#C76E59" transform="translate(0,0)"/>
<path d="" fill="#B76E5F" transform="translate(0,0)"/>
<path d="" fill="#B27266" transform="translate(0,0)"/>
<path d="" fill="#CC6E57" transform="translate(0,0)"/>
<path d="" fill="#C26E5C" transform="translate(0,0)"/>
<path d="" fill="#AF746E" transform="translate(0,0)"/>
<path d="" fill="#B37266" transform="translate(0,0)"/>
<path d="" fill="#BD6E5F" transform="translate(0,0)"/>
<path d="" fill="#C86F58" transform="translate(0,0)"/>
<path d="" fill="#C86E59" transform="translate(0,0)"/>
<path d="" fill="#C06E5C" transform="translate(0,0)"/>
<path d="" fill="#C76D58" transform="translate(0,0)"/>
<path d="" fill="#C76F59" transform="translate(0,0)"/>
<path d="" fill="#CC6D58" transform="translate(0,0)"/>
<path d="" fill="#D16E55" transform="translate(0,0)"/>
<path d="" fill="#C36A5C" transform="translate(0,0)"/>
<path d="" fill="#B56E61" transform="translate(0,0)"/>
<path d="" fill="#C66E58" transform="translate(0,0)"/>
<path d="" fill="#C96E5A" transform="translate(0,0)"/>
<path d="" fill="#C36C59" transform="translate(0,0)"/>
<path d="" fill="#AF7269" transform="translate(0,0)"/>
<path d="" fill="#C86A5A" transform="translate(0,0)"/>
<path d="" fill="#C16F59" transform="translate(0,0)"/>
<path d="" fill="#CD7059" transform="translate(0,0)"/>
<path d="" fill="#C26A59" transform="translate(0,0)"/>
<path d="" fill="#BE6D5E" transform="translate(0,0)"/>
<path d="" fill="#AF736A" transform="translate(0,0)"/>
<path d="" fill="#AB736E" transform="translate(0,0)"/>
<path d="" fill="#BF6E5E" transform="translate(0,0)"/>
<path d="" fill="#C26B58" transform="translate(0,0)"/>
<path d="" fill="#C36B59" transform="translate(0,0)"/>
<path d="" fill="#AE7169" transform="translate(0,0)"/>
<path d="" fill="#AF776B" transform="translate(0,0)"/>
<path d="" fill="#C26C5A" transform="translate(0,0)"/>
<path d="" fill="#C46D5B" transform="translate(0,0)"/>
<path d="" fill="#D06F54" transform="translate(0,0)"/>
<path d="" fill="#C2705E" transform="translate(0,0)"/>
<path d="" fill="#B37169" transform="translate(0,0)"/>
<path d="" fill="#CB6E59" transform="translate(0,0)"/>
<path d="" fill="#C66B57" transform="translate(0,0)"/>
<path d="" fill="#CB6D58" transform="translate(0,0)"/>
<path d="" fill="#B26E65" transform="translate(0,0)"/>
<path d="" fill="#C36C59" transform="translate(0,0)"/>
<path d="" fill="#B4736A" transform="translate(0,0)"/>
<path d="" fill="#C26C58" transform="translate(0,0)"/>
<path d="" fill="#C96C59" transform="translate(0,0)"/>
<path d="" fill="#CF7057" transform="translate(0,0)"/>
<path d="" fill="#C86C55" transform="translate(0,0)"/>
<path d="" fill="#C16E5B" transform="translate(0,0)"/>
<path d="" fill="#AC7168" transform="translate(0,0)"/>
<path d="" fill="#C66D58" transform="translate(0,0)"/>
<path d="" fill="#BC7164" transform="translate(0,0)"/>
<path d="" fill="#B17065" transform="translate(0,0)"/>
<path d="" fill="#CD6D52" transform="translate(0,0)"/>
<path d="" fill="#D87250" transform="translate(0,0)"/>
<path d="" fill="#BD6D5C" transform="translate(0,0)"/>
<path d="" fill="#C86C54" transform="translate(0,0)"/>
<path d="" fill="#CC7055" transform="translate(0,0)"/>
<path d="" fill="#CE6B51" transform="translate(0,0)"/>
<path d="" fill="#D26E53" transform="translate(0,0)"/>
<path d="" fill="#BB7364" transform="translate(0,0)"/>
<path d="" fill="#C66E56" transform="translate(0,0)"/>
<path d="" fill="#CE6E54" transform="translate(0,0)"/>
<path d="" fill="#CD6D53" transform="translate(0,0)"/>
<path d="" fill="#D37057" transform="translate(0,0)"/>
<path d="" fill="#C96C55" transform="translate(0,0)"/>
<path d="" fill="#D06B4F" transform="translate(0,0)"/>
<path d="" fill="#D36E50" transform="translate(0,0)"/>
<path d="" fill="#CE6F55" transform="translate(0,0)"/>
<path d="" fill="#C5705B" transform="translate(0,0)"/>
<path d="" fill="#CC6F50" transform="translate(0,0)"/>
<path d="" fill="#D36F53" transform="translate(0,0)"/>
<path d="" fill="#CD6D50" transform="translate(0,0)"/>
<path d="" fill="#CD6C51" transform="translate(0,0)"/>
<path d="" fill="#54739A" transform="translate(0,0)"/>
<path d="" fill="#D6714E" transform="translate(0,0)"/>
<path d="" fill="#5B7598" transform="translate(0,0)"/>
<path d="" fill="#D06C4D" transform="translate(0,0)"/>
<path d="" fill="#547498" transform="translate(0,0)"/>
<path d="" fill="#CE6E50" transform="translate(0,0)"/>
<path d="" fill="#D16F52" transform="translate(0,0)"/>
<path d="" fill="#54739B" transform="translate(0,0)"/>
<path d="" fill="#547399" transform="translate(0,0)"/>
<path d="" fill="#537399" transform="translate(0,0)"/>
<path d="" fill="#D26D51" transform="translate(0,0)"/>
<path d="" fill="#D16E51" transform="translate(0,0)"/>
<path d="" fill="#D86D4E" transform="translate(0,0)"/>
<path d="" fill="#CD6D51" transform="translate(0,0)"/>
<path d="" fill="#CA6E52" transform="translate(0,0)"/>
<path d="" fill="#CB6D53" transform="translate(0,0)"/>
<path d="" fill="#CC6F51" transform="translate(0,0)"/>
<path d="" fill="#CA6B4D" transform="translate(0,0)"/>
<path d="" fill="#C2705C" transform="translate(0,0)"/>
<path d="" fill="#D8704E" transform="translate(0,0)"/>
<path d="" fill="#52729A" transform="translate(0,0)"/>
<path d="" fill="#DA714D" transform="translate(0,0)"/>
<path d="" fill="#C46D57" transform="translate(0,0)"/>
<path d="" fill="#C66E57" transform="translate(0,0)"/>
<path d="" fill="#C36F5A" transform="translate(0,0)"/>
<path d="" fill="#C77059" transform="translate(0,0)"/>
<path d="" fill="#CF6B4D" transform="translate(0,0)"/>
<path d="" fill="#CF6D50" transform="translate(0,0)"/>
<path d="" fill="#CF6D4D" transform="translate(0,0)"/>
<path d="" fill="#D46E4C" transform="translate(0,0)"/>
<path d="" fill="#BB6F5F" transform="translate(0,0)"/>
<path d="" fill="#CE6E52" transform="translate(0,0)"/>
<path d="" fill="#CD6C52" transform="translate(0,0)"/>
<path d="" fill="#5F7595" transform="translate(0,0)"/>
<path d="" fill="#C96E51" transform="translate(0,0)"/>
<path d="" fill="#D36E51" transform="translate(0,0)"/>
<path d="" fill="#D8714F" transform="translate(0,0)"/>
<path d="" fill="#DE734B" transform="translate(0,0)"/>
<path d="" fill="#CB6B51" transform="translate(0,0)"/>
<path d="" fill="#CE6C4A" transform="translate(0,0)"/>
<path d="" fill="#D5704F" transform="translate(0,0)"/>
<path d="" fill="#D06D4F" transform="translate(0,0)"/>
<path d="" fill="#D36F4E" transform="translate(0,0)"/>
<path d="" fill="#D3704F" transform="translate(0,0)"/>
<path d="" fill="#D26C4D" transform="translate(0,0)"/>
<path d="" fill="#57749A" transform="translate(0,0)"/>
<path d="" fill="#BE6F5F" transform="translate(0,0)"/>
<path d="" fill="#D16E4C" transform="translate(0,0)"/>
<path d="" fill="#D56E4E" transform="translate(0,0)"/>
<path d="" fill="#4C6B94" transform="translate(0,0)"/>
<path d="" fill="#CC6C4F" transform="translate(0,0)"/>
<path d="" fill="#B96D5C" transform="translate(0,0)"/>
<path d="" fill="#547297" transform="translate(0,0)"/>
<path d="" fill="#91A3B3" transform="translate(0,0)"/>
<path d="" fill="#4B6B92" transform="translate(0,0)"/>
<path d="" fill="#506E96" transform="translate(0,0)"/>
<path d="" fill="#D76F4E" transform="translate(0,0)"/>
<path d="" fill="#C8674A" transform="translate(0,0)"/>
<path d="" fill="#4F6E94" transform="translate(0,0)"/>
<path d="" fill="#D16B4B" transform="translate(0,0)"/>
<path d="" fill="#8E9CAE" transform="translate(0,0)"/>
<path d="" fill="#4D6D93" transform="translate(0,0)"/>
<path d="" fill="#D46F4E" transform="translate(0,0)"/>
<path d="" fill="#DB6F4B" transform="translate(0,0)"/>
<path d="" fill="#D46E4D" transform="translate(0,0)"/>
<path d="" fill="#CE6F4F" transform="translate(0,0)"/>
<path d="" fill="#CD6B4D" transform="translate(0,0)"/>
<path d="" fill="#D86F4E" transform="translate(0,0)"/>
<path d="" fill="#D36D4B" transform="translate(0,0)"/>
<path d="" fill="#5B7291" transform="translate(0,0)"/>
<path d="" fill="#D56D4C" transform="translate(0,0)"/>
<path d="" fill="#D36E4E" transform="translate(0,0)"/>
<path d="" fill="#CE6F52" transform="translate(0,0)"/>
<path d="" fill="#537092" transform="translate(0,0)"/>
<path d="" fill="#DB6F4B" transform="translate(0,0)"/>
<path d="" fill="#D36E4C" transform="translate(0,0)"/>
<path d="" fill="#D46E4B" transform="translate(0,0)"/>
<path d="" fill="#98A8B9" transform="translate(0,0)"/>
<path d="" fill="#5F7491" transform="translate(0,0)"/>
<path d="" fill="#D66F4D" transform="translate(0,0)"/>
<path d="" fill="#536F92" transform="translate(0,0)"/>
<path d="" fill="#5D7292" transform="translate(0,0)"/>
<path d="" fill="#B96D5D" transform="translate(0,0)"/>
<path d="" fill="#D16C4C" transform="translate(0,0)"/>
<path d="" fill="#D76E4E" transform="translate(0,0)"/>
<path d="" fill="#4C6B90" transform="translate(0,0)"/>
<path d="" fill="#DC704A" transform="translate(0,0)"/>
<path d="" fill="#D5704E" transform="translate(0,0)"/>
<path d="" fill="#5B7190" transform="translate(0,0)"/>
<path d="" fill="#B66D5B" transform="translate(0,0)"/>
<path d="" fill="#5C708F" transform="translate(0,0)"/>
<path d="" fill="#DB6E49" transform="translate(0,0)"/>
<path d="" fill="#BB6C5C" transform="translate(0,0)"/>
<path d="" fill="#D06B49" transform="translate(0,0)"/>
<path d="" fill="#CF6E4E" transform="translate(0,0)"/>
<path d="" fill="#CD6D4F" transform="translate(0,0)"/>
<path d="" fill="#D76F4D" transform="translate(0,0)"/>
<path d="" fill="#D76F4B" transform="translate(0,0)"/>
<path d="" fill="#9EB1BE" transform="translate(0,0)"/>
<path d="" fill="#4D698F" transform="translate(0,0)"/>
<path d="" fill="#9EAEBD" transform="translate(0,0)"/>
<path d="" fill="#D66E4D" transform="translate(0,0)"/>
<path d="" fill="#D06A46" transform="translate(0,0)"/>
<path d="" fill="#D77050" transform="translate(0,0)"/>
<path d="" fill="#E07147" transform="translate(0,0)"/>
<path d="" fill="#DD6F4A" transform="translate(0,0)"/>
<path d="" fill="#D66F4A" transform="translate(0,0)"/>
<path d="" fill="#D6724B" transform="translate(0,0)"/>
<path d="" fill="#D5714F" transform="translate(0,0)"/>
<path d="" fill="#CD6D4C" transform="translate(0,0)"/>
<path d="" fill="#A0B0BD" transform="translate(0,0)"/>
<path d="" fill="#9DAEBE" transform="translate(0,0)"/>
<path d="" fill="#D96E4B" transform="translate(0,0)"/>
<path d="" fill="#D56E4C" transform="translate(0,0)"/>
<path d="" fill="#D56E4D" transform="translate(0,0)"/>
<path d="" fill="#D66E49" transform="translate(0,0)"/>
<path d="" fill="#D86E4C" transform="translate(0,0)"/>
<path d="" fill="#DD7248" transform="translate(0,0)"/>
<path d="" fill="#CE6D4D" transform="translate(0,0)"/>
<path d="" fill="#D76F4F" transform="translate(0,0)"/>
<path d="" fill="#CF6F4E" transform="translate(0,0)"/>
<path d="" fill="#DE7048" transform="translate(0,0)"/>
<path d="" fill="#9EAFBE" transform="translate(0,0)"/>
<path d="" fill="#D86D47" transform="translate(0,0)"/>
<path d="" fill="#9DAEBE" transform="translate(0,0)"/>
<path d="" fill="#D76F4D" transform="translate(0,0)"/>
<path d="" fill="#D76A45" transform="translate(0,0)"/>
<path d="" fill="#CF6D4C" transform="translate(0,0)"/>
<path d="" fill="#B66F5F" transform="translate(0,0)"/>
<path d="" fill="#DC7049" transform="translate(0,0)"/>
<path d="" fill="#CB6F52" transform="translate(0,0)"/>
<path d="" fill="#9BA4B0" transform="translate(0,0)"/>
<path d="" fill="#CC6D51" transform="translate(0,0)"/>
<path d="" fill="#DE7148" transform="translate(0,0)"/>
<path d="" fill="#D26F4F" transform="translate(0,0)"/>
<path d="" fill="#B56A59" transform="translate(0,0)"/>
<path d="" fill="#B66D5D" transform="translate(0,0)"/>
<path d="" fill="#D76F4C" transform="translate(0,0)"/>
<path d="" fill="#D06F4B" transform="translate(0,0)"/>
<path d="" fill="#B7705D" transform="translate(0,0)"/>
<path d="" fill="#DF6F47" transform="translate(0,0)"/>
<path d="" fill="#CF704E" transform="translate(0,0)"/>
<path d="" fill="#CA6F51" transform="translate(0,0)"/>
<path d="" fill="#9FAAB8" transform="translate(0,0)"/>
<path d="" fill="#98AABB" transform="translate(0,0)"/>
<path d="" fill="#CD6E51" transform="translate(0,0)"/>
<path d="" fill="#D8714D" transform="translate(0,0)"/>
<path d="" fill="#DA6E4B" transform="translate(0,0)"/>
<path d="" fill="#D46E4D" transform="translate(0,0)"/>
<path d="" fill="#D86F4C" transform="translate(0,0)"/>
<path d="" fill="#A0AFBE" transform="translate(0,0)"/>
<path d="" fill="#DA714C" transform="translate(0,0)"/>
<path d="" fill="#E17347" transform="translate(0,0)"/>
<path d="" fill="#9FB0BF" transform="translate(0,0)"/>
<path d="" fill="#D66F4A" transform="translate(0,0)"/>
<path d="" fill="#D16E4C" transform="translate(0,0)"/>
<path d="" fill="#9FB0BF" transform="translate(0,0)"/>
<path d="" fill="#9EAEBD" transform="translate(0,0)"/>
<path d="" fill="#DF7047" transform="translate(0,0)"/>
<path d="" fill="#D56E4B" transform="translate(0,0)"/>
<path d="" fill="#D16F4F" transform="translate(0,0)"/>
<path d="" fill="#CC6D50" transform="translate(0,0)"/>
<path d="" fill="#D76E48" transform="translate(0,0)"/>
<path d="" fill="#CE6C4E" transform="translate(0,0)"/>
<path d="" fill="#D86E48" transform="translate(0,0)"/>
<path d="" fill="#D16D4A" transform="translate(0,0)"/>
<path d="" fill="#D36C49" transform="translate(0,0)"/>
<path d="" fill="#D4704B" transform="translate(0,0)"/>
<path d="" fill="#D46D4A" transform="translate(0,0)"/>
<path d="" fill="#DC6E4B" transform="translate(0,0)"/>
<path d="" fill="#DD7147" transform="translate(0,0)"/>
<path d="" fill="#D86F48" transform="translate(0,0)"/>
<path d="" fill="#D76D49" transform="translate(0,0)"/>
<path d="" fill="#C7765B" transform="translate(0,0)"/>
<path d="" fill="#D16D4A" transform="translate(0,0)"/>
<path d="" fill="#D66E4B" transform="translate(0,0)"/>
<path d="" fill="#DA704D" transform="translate(0,0)"/>
<path d="" fill="#9EB0BD" transform="translate(0,0)"/>
<path d="" fill="#D96E4A" transform="translate(0,0)"/>
<path d="" fill="#D6704A" transform="translate(0,0)"/>
<path d="" fill="#D16C4A" transform="translate(0,0)"/>
<path d="" fill="#9EAFBE" transform="translate(0,0)"/>
<path d="" fill="#DA6E48" transform="translate(0,0)"/>
<path d="" fill="#A1B0BE" transform="translate(0,0)"/>
<path d="" fill="#A1B0BE" transform="translate(0,0)"/>
<path d="" fill="#DB7048" transform="translate(0,0)"/>
<path d="" fill="#D7724E" transform="translate(0,0)"/>
<path d="" fill="#DF714B" transform="translate(0,0)"/>
<path d="" fill="#C5755D" transform="translate(0,0)"/>
<path d="" fill="#D66E48" transform="translate(0,0)"/>
<path d="" fill="#D56F49" transform="translate(0,0)"/>
<path d="" fill="#CE6E4B" transform="translate(0,0)"/>
<path d="" fill="#DE714B" transform="translate(0,0)"/>
<path d="" fill="#DC6F49" transform="translate(0,0)"/>
<path d="" fill="#9CABB9" transform="translate(0,0)"/>
<path d="" fill="#DD7249" transform="translate(0,0)"/>
<path d="" fill="#D5704A" transform="translate(0,0)"/>
<path d="" fill="#B56C5B" transform="translate(0,0)"/>
<path d="" fill="#D9704C" transform="translate(0,0)"/>
<path d="" fill="#DD6E46" transform="translate(0,0)"/>
<path d="" fill="#B86E5A" transform="translate(0,0)"/>
<path d="" fill="#DA6E47" transform="translate(0,0)"/>
<path d="" fill="#DD6F49" transform="translate(0,0)"/>
<path d="" fill="#D86F49" transform="translate(0,0)"/>
<path d="" fill="#C3735C" transform="translate(0,0)"/>
<path d="" fill="#97A6B6" transform="translate(0,0)"/>
<path d="" fill="#D76E48" transform="translate(0,0)"/>
<path d="" fill="#D96E48" transform="translate(0,0)"/>
<path d="" fill="#D5704B" transform="translate(0,0)"/>
<path d="" fill="#D66E4A" transform="translate(0,0)"/>
<path d="" fill="#DE734D" transform="translate(0,0)"/>
<path d="" fill="#97A6B4" transform="translate(0,0)"/>
<path d="" fill="#D76F49" transform="translate(0,0)"/>
<path d="" fill="#DF7247" transform="translate(0,0)"/>
<path d="" fill="#D86F48" transform="translate(0,0)"/>
<path d="" fill="#D96D48" transform="translate(0,0)"/>
<path d="" fill="#94A4B2" transform="translate(0,0)"/>
<path d="" fill="#D76F4A" transform="translate(0,0)"/>
<path d="" fill="#E07048" transform="translate(0,0)"/>
<path d="" fill="#D6704A" transform="translate(0,0)"/>
<path d="" fill="#DC7249" transform="translate(0,0)"/>
<path d="" fill="#D46F4A" transform="translate(0,0)"/>
<path d="" fill="#DE7249" transform="translate(0,0)"/>
<path d="" fill="#D16C49" transform="translate(0,0)"/>
<path d="" fill="#CE6D4C" transform="translate(0,0)"/>
<path d="" fill="#C26B54" transform="translate(0,0)"/>
<path d="" fill="#D16F4B" transform="translate(0,0)"/>
<path d="" fill="#DA714B" transform="translate(0,0)"/>
<path d="" fill="#DD7048" transform="translate(0,0)"/>
<path d="" fill="#D7714B" transform="translate(0,0)"/>
<path d="" fill="#D26E4B" transform="translate(0,0)"/>
<path d="" fill="#CE6E4A" transform="translate(0,0)"/>
<path d="" fill="#D06D4A" transform="translate(0,0)"/>
<path d="" fill="#D66E47" transform="translate(0,0)"/>
<path d="" fill="#D7704C" transform="translate(0,0)"/>
<path d="" fill="#DC6F47" transform="translate(0,0)"/>
<path d="" fill="#CD6E4C" transform="translate(0,0)"/>
<path d="" fill="#D7704A" transform="translate(0,0)"/>
<path d="" fill="#BD6C55" transform="translate(0,0)"/>
<path d="" fill="#D36B47" transform="translate(0,0)"/>
<path d="" fill="#CF6D4E" transform="translate(0,0)"/>
<path d="" fill="#CE6D4B" transform="translate(0,0)"/>
<path d="" fill="#D06B49" transform="translate(0,0)"/>
<path d="" fill="#D3704B" transform="translate(0,0)"/>
<path d="" fill="#D96D49" transform="translate(0,0)"/>
<path d="" fill="#DB6E4A" transform="translate(0,0)"/>
<path d="" fill="#DD7146" transform="translate(0,0)"/>
<path d="" fill="#D26F4B" transform="translate(0,0)"/>
<path d="" fill="#D06E4C" transform="translate(0,0)"/>
<path d="" fill="#D56D4B" transform="translate(0,0)"/>
<path d="" fill="#DC6E47" transform="translate(0,0)"/>
<path d="" fill="#D26F4C" transform="translate(0,0)"/>
<path d="" fill="#CD6F4E" transform="translate(0,0)"/>
<path d="" fill="#CD6D4B" transform="translate(0,0)"/>
<path d="" fill="#CE6E4A" transform="translate(0,0)"/>
<path d="" fill="#CB6A4B" transform="translate(0,0)"/>
<path d="" fill="#C66D52" transform="translate(0,0)"/>
<path d="" fill="#D8714A" transform="translate(0,0)"/>
<path d="" fill="#CE6D4C" transform="translate(0,0)"/>
<path d="" fill="#CE6E4D" transform="translate(0,0)"/>
<path d="" fill="#CE6E4D" transform="translate(0,0)"/>
<path d="" fill="#D16D4C" transform="translate(0,0)"/>
<path d="" fill="#CE6D4B" transform="translate(0,0)"/>
<path d="" fill="#D56D4C" transform="translate(0,0)"/>
<path d="" fill="#D26E4B" transform="translate(0,0)"/>
<path d="" fill="#D26D4B" transform="translate(0,0)"/>
<path d="" fill="#CE6B47" transform="translate(0,0)"/>
<path d="" fill="#D26E4B" transform="translate(0,0)"/>
<path d="" fill="#D36C4A" transform="translate(0,0)"/>
<path d="" fill="#D36E4C" transform="translate(0,0)"/>
<path d="" fill="#D36E4D" transform="translate(0,0)"/>
<path d="" fill="#CF6C4C" transform="translate(0,0)"/>
<path d="" fill="#D06E4D" transform="translate(0,0)"/>
<path d="" fill="#D06C4C" transform="translate(0,0)"/>
<path d="" fill="#D26E4E" transform="translate(0,0)"/>
<path d="" fill="#D06D4D" transform="translate(0,0)"/>
<path d="" fill="#CE6C4C" transform="translate(0,0)"/>
<path d="" fill="#CE6E4B" transform="translate(0,0)"/>
<path d="" fill="#C96D4E" transform="translate(0,0)"/>
<path d="" fill="#CB6E50" transform="translate(0,0)"/>
<path d="" fill="#C56B4F" transform="translate(0,0)"/>
<path d="" fill="#CA6F4E" transform="translate(0,0)"/>
<path d="" fill="#D2704F" transform="translate(0,0)"/>
<path d="" fill="#CB6A4C" transform="translate(0,0)"/>
<path d="" fill="#CD6C4C" transform="translate(0,0)"/>
<path d="" fill="#CF6B4B" transform="translate(0,0)"/>
<path d="" fill="#CF6E4B" transform="translate(0,0)"/>
<path d="" fill="#D06C4C" transform="translate(0,0)"/>
<path d="" fill="#D5704B" transform="translate(0,0)"/>
<path d="" fill="#CF6D4A" transform="translate(0,0)"/>
<path d="" fill="#D06E4C" transform="translate(0,0)"/>
<path d="" fill="#D16E4B" transform="translate(0,0)"/>
<path d="" fill="#CA6E4E" transform="translate(0,0)"/>
<path d="" fill="#D36F4E" transform="translate(0,0)"/>
<path d="" fill="#D47151" transform="translate(0,0)"/>
<path d="" fill="#C86F51" transform="translate(0,0)"/>
<path d="" fill="#C86E4C" transform="translate(0,0)"/>
<path d="" fill="#C96B4C" transform="translate(0,0)"/>
<path d="" fill="#CA6B4B" transform="translate(0,0)"/>
<path d="" fill="#CE6D49" transform="translate(0,0)"/>
<path d="" fill="#CF6E4A" transform="translate(0,0)"/>
<path d="" fill="#CA6E4D" transform="translate(0,0)"/>
<path d="" fill="#C76B51" transform="translate(0,0)"/>
<path d="" fill="#C86C4F" transform="translate(0,0)"/>
<path d="" fill="#415D82" transform="translate(0,0)"/>
<path d="" fill="#466083" transform="translate(0,0)"/>
<path d="" fill="#415D82" transform="translate(0,0)"/>
<path d="" fill="#456082" transform="translate(0,0)"/>
<path d="" fill="#3D5C80" transform="translate(0,0)"/>
<path d="" fill="#405D82" transform="translate(0,0)"/>
<path d="" fill="#476184" transform="translate(0,0)"/>
<path d="" fill="#415D81" transform="translate(0,0)"/>
<path d="" fill="#3C5B81" transform="translate(0,0)"/>
<path d="" fill="#405E83" transform="translate(0,0)"/>
<path d="" fill="#3F5B83" transform="translate(0,0)"/>
<path d="" fill="#3A597D" transform="translate(0,0)"/>
<path d="" fill="#3C5A81" transform="translate(0,0)"/>
<path d="" fill="#3E5C82" transform="translate(0,0)"/>
<path d="" fill="#3D5B81" transform="translate(0,0)"/>
<path d="" fill="#3E5D83" transform="translate(0,0)"/>
<path d="" fill="#3B5B7F" transform="translate(0,0)"/>
<path d="" fill="#405F85" transform="translate(0,0)"/>
<path d="" fill="#416085" transform="translate(0,0)"/>
<path d="" fill="#3C5B80" transform="translate(0,0)"/>
<path d="" fill="#476286" transform="translate(0,0)"/>
<path d="" fill="#476489" transform="translate(0,0)"/>
<path d="" fill="#476387" transform="translate(0,0)"/>
<path d="" fill="#4E6385" transform="translate(0,0)"/>
<path d="" fill="#3D5C81" transform="translate(0,0)"/>
<path d="" fill="#3F5C82" transform="translate(0,0)"/>
<path d="" fill="#496386" transform="translate(0,0)"/>
<path d="" fill="#496386" transform="translate(0,0)"/>
<path d="" fill="#496286" transform="translate(0,0)"/>
<path d="" fill="#416085" transform="translate(0,0)"/>
<path d="" fill="#425F84" transform="translate(0,0)"/>
<path d="" fill="#3B5A82" transform="translate(0,0)"/>
<path d="" fill="#3C5B80" transform="translate(0,0)"/>
<path d="" fill="#3C5C83" transform="translate(0,0)"/>
<path d="" fill="#486388" transform="translate(0,0)"/>
<path d="" fill="#4A6386" transform="translate(0,0)"/>
<path d="" fill="#3A5981" transform="translate(0,0)"/>
<path d="" fill="#3B5B80" transform="translate(0,0)"/>
<path d="" fill="#476388" transform="translate(0,0)"/>
<path d="" fill="#496489" transform="translate(0,0)"/>
</svg>
