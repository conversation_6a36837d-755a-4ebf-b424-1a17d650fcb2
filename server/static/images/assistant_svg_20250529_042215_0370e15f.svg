<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C2.935 2.283 4.616 4.04 6 7.5 C6 10.821 4.878 12.287 3 15 C1.117 16.633 1.117 16.633 -1.125 18.125 C-2.009 18.718 -2.894 19.311 -3.805 19.922 C-4.859 20.608 -5.914 21.293 -7 22 C-7.628 22.411 -8.256 22.821 -8.903 23.244 C-15.253 27.392 -21.632 31.495 -28.031 35.566 C-28.66 35.967 -29.289 36.369 -29.937 36.782 C-31.638 37.866 -33.342 38.947 -35.047 40.027 C-40.728 43.527 -40.728 43.527 -44 49 C-44.071 51.739 -44.094 54.45 -44.062 57.188 C-44.056 58.312 -44.056 58.312 -44.049 59.459 C-44.037 61.306 -44.019 63.153 -44 65 C-42.298 64.598 -42.298 64.598 -40.562 64.188 C-38.709 63.787 -36.855 63.392 -35 63 C-33.751 62.724 -32.502 62.448 -31.215 62.164 C-11.648 58.085 7.698 60.334 25 71 C28.132 73.189 31.073 75.546 34 78 C34.668 78.522 35.335 79.044 36.023 79.582 C54.073 94.243 62.241 117.763 64.617 140.242 C66.8 165.112 60.024 186.48 50.308 208.958 C49.061 211.858 47.84 214.768 46.629 217.684 C35.701 243.877 22.425 268.059 -4.438 280.605 C-14.352 284.592 -23.774 286.36 -34.438 286.25 C-35.364 286.246 -36.291 286.242 -37.246 286.238 C-42.71 286.169 -47.733 285.647 -53.07 284.453 C-57.217 283.812 -61.31 283.77 -65.5 283.75 C-66.272 283.741 -67.043 283.732 -67.838 283.723 C-72.081 283.744 -75.989 284.142 -80.145 285.008 C-87.145 286.382 -94.079 286.217 -101.188 286.125 C-102.534 286.115 -103.882 286.106 -105.229 286.098 C-108.486 286.074 -111.743 286.042 -115 286 C-115 285.34 -115 284.68 -115 284 C-116.011 283.876 -117.021 283.753 -118.062 283.625 C-134.488 281.018 -147.884 268.723 -158 256 C-170.637 238.555 -178.788 217.913 -186.938 198.125 C-187.327 197.179 -187.717 196.233 -188.119 195.259 C-188.481 194.372 -188.842 193.484 -189.215 192.57 C-189.696 191.391 -189.696 191.391 -190.187 190.188 C-190.951 188.133 -191.516 186.136 -192 184 C-192.237 182.965 -192.474 181.93 -192.719 180.863 C-192.977 179.692 -193.234 178.52 -193.5 177.312 C-193.758 176.154 -194.016 174.995 -194.281 173.801 C-198.335 152.362 -196.07 131.336 -188.691 110.933 C-188.059 109.164 -187.465 107.381 -186.875 105.598 C-185.555 101.68 -183.582 99.217 -181 96 C-179.722 94.213 -178.451 92.422 -177.188 90.625 C-164.923 73.775 -147.063 63.772 -126.586 60.445 C-109.814 58.363 -94.373 61.34 -79 68 C-79.023 64.769 -79.1 61.542 -79.188 58.312 C-79.193 57.4 -79.199 56.488 -79.205 55.549 C-79.356 50.933 -79.474 48.679 -82.375 44.938 C-84.881 43.088 -87.037 41.931 -90 41 C-90 40.34 -90 39.68 -90 39 C-90.673 38.771 -91.346 38.541 -92.039 38.305 C-95.8 36.648 -98.999 34.45 -102.375 32.125 C-106.143 29.544 -109.887 27.02 -113.875 24.789 C-117 23 -117 23 -118.625 21.25 C-119.262 17.429 -117.873 14.312 -116 11 C-113.75 8.938 -113.75 8.938 -111 8 C-105.867 8.363 -102.55 9.503 -98.27 12.316 C-97.218 12.999 -96.167 13.682 -95.084 14.385 C-94.004 15.103 -92.925 15.822 -91.812 16.562 C-90.222 17.6 -90.222 17.6 -88.6 18.658 C-86.462 20.056 -84.331 21.463 -82.206 22.879 C-80.041 24.311 -77.861 25.718 -75.666 27.101 C-74.604 27.778 -73.543 28.455 -72.449 29.152 C-71.497 29.749 -70.546 30.346 -69.565 30.961 C-66.619 33.303 -64.93 35.791 -63 39 C-59.673 38.227 -59.673 38.227 -57.773 34.738 C-54.999 30.68 -51.841 28.601 -47.625 26.188 C-46.189 25.329 -44.754 24.468 -43.32 23.605 C-42.597 23.174 -41.875 22.743 -41.13 22.298 C-37.851 20.3 -34.677 18.155 -31.5 16 C-26.124 12.377 -20.686 8.87 -15.188 5.438 C-14.155 4.793 -14.155 4.793 -13.102 4.135 C-5.419 -0.638 -5.419 -0.638 0 0 Z M-63 40 C-63 41.98 -63 43.96 -63 46 C-62.34 45.01 -61.68 44.02 -61 43 C-61.33 42.01 -61.66 41.02 -62 40 C-62.33 40 -62.66 40 -63 40 Z " fill="#51D8FE" transform="translate(786,281)"/>
<path d="M0 0 C28.242 26.006 43.522 63.285 45.328 101.246 C45.993 123.876 41.561 146.314 31.403 166.558 C30.834 167.709 30.266 168.86 29.68 170.046 C24.248 180.576 17.536 189.606 9.481 198.269 C7.36 200.547 7.36 200.547 5.434 203.089 C-10.529 222.491 -38.218 234.603 -61.722 241.246 C-62.796 241.55 -62.796 241.55 -63.892 241.86 C-66.119 242.466 -68.352 243.023 -70.597 243.558 C-71.777 243.844 -71.777 243.844 -72.98 244.135 C-80.906 245.824 -88.81 245.896 -96.874 245.874 C-99.198 245.871 -101.52 245.894 -103.843 245.919 C-118 245.981 -131.474 244.055 -144.91 239.433 C-146.489 238.909 -148.068 238.385 -149.648 237.863 C-150.648 237.522 -150.648 237.522 -151.669 237.175 C-153.589 236.529 -153.589 236.529 -155.57 236.115 C-179.716 229.475 -203.712 205.986 -217.257 185.519 C-218.583 183.449 -218.583 183.449 -220.597 181.558 C-221.287 180.299 -221.93 179.013 -222.542 177.714 C-222.897 176.963 -223.251 176.211 -223.617 175.437 C-223.982 174.652 -224.346 173.867 -224.722 173.058 C-225.281 171.881 -225.281 171.881 -225.851 170.679 C-228.597 164.815 -228.597 164.815 -228.597 162.558 C-229.257 162.228 -229.917 161.898 -230.597 161.558 C-231.948 158.351 -232.712 154.919 -233.597 151.558 C-234.098 149.688 -234.098 149.688 -234.609 147.781 C-244.694 108.6 -239.56 66.707 -219.116 31.691 C-199.064 -1.368 -166.885 -24.535 -129.444 -33.819 C-82.867 -44.492 -35.269 -31.84 0 0 Z M-132.597 -14.442 C-133.679 -14.176 -134.76 -13.911 -135.875 -13.637 C-165.507 -5.101 -190.758 18.11 -205.672 44.458 C-214.42 60.346 -219.182 76.638 -221.597 94.558 C-218.144 95.344 -214.997 95.67 -211.459 95.624 C-210.461 95.614 -209.463 95.605 -208.434 95.594 C-207.367 95.58 -206.299 95.565 -205.199 95.55 C-202.923 95.533 -200.647 95.516 -198.371 95.5 C-194.794 95.469 -191.217 95.433 -187.64 95.387 C-184.18 95.344 -180.721 95.321 -177.261 95.3 C-176.197 95.282 -175.134 95.263 -174.038 95.244 C-167.357 95.221 -162.118 95.575 -156.824 100.06 C-156.413 100.576 -156.002 101.093 -155.578 101.625 C-155.113 102.192 -154.648 102.76 -154.169 103.345 C-153.712 103.931 -153.255 104.517 -152.785 105.121 C-151.841 106.289 -150.897 107.457 -149.953 108.625 C-149.175 109.593 -148.398 110.561 -147.597 111.558 C-146.937 112.218 -146.277 112.878 -145.597 113.558 C-142.071 106.045 -138.95 98.452 -136.035 90.683 C-132.266 80.765 -128.344 70.922 -124.285 61.121 C-123.793 59.932 -123.302 58.743 -122.796 57.518 C-121.412 54.191 -120.007 50.874 -118.597 47.558 C-118.16 46.496 -117.722 45.433 -117.271 44.339 C-116.849 43.348 -116.427 42.357 -115.992 41.335 C-115.442 40.022 -115.442 40.022 -114.882 38.683 C-113.369 36.18 -112.35 35.474 -109.597 34.558 C-103.542 34.877 -103.542 34.877 -101.07 36.659 C-98.924 39.426 -97.814 42.509 -96.597 45.757 C-96.155 46.917 -96.155 46.917 -95.704 48.101 C-90.962 60.71 -86.679 73.491 -82.326 86.238 C-81.358 89.071 -80.386 91.903 -79.414 94.734 C-74.066 110.317 -68.768 125.916 -63.597 141.558 C-61.173 139.235 -59.876 137.348 -58.582 134.234 C-58.221 133.377 -57.86 132.521 -57.489 131.638 C-57.112 130.725 -56.735 129.812 -56.347 128.871 C-55.545 126.971 -54.743 125.071 -53.941 123.171 C-52.814 120.479 -51.691 117.785 -50.578 115.087 C-49.677 112.906 -48.763 110.732 -47.847 108.558 C-47.59 107.919 -47.333 107.28 -47.069 106.622 C-45.247 102.323 -43.39 99.317 -39.597 96.558 C-32.065 94.972 -24.023 95.427 -16.371 95.46 C-15.142 95.462 -13.913 95.463 -12.647 95.465 C-8.755 95.47 -4.864 95.483 -0.972 95.496 C1.68 95.501 4.333 95.505 6.985 95.509 C13.458 95.52 19.93 95.537 26.403 95.558 C26.508 88.943 25.971 83.118 24.403 76.683 C24.186 75.783 23.969 74.882 23.745 73.955 C20.834 62.546 16.954 50.726 9.403 41.558 C8.732 39.893 8.066 38.226 7.403 36.558 C-3.223 20.312 -3.223 20.312 -18.597 9.558 C-18.597 8.898 -18.597 8.238 -18.597 7.558 C-20.092 6.237 -20.092 6.237 -22.078 4.949 C-22.823 4.444 -23.568 3.94 -24.336 3.42 C-25.144 2.888 -25.952 2.356 -26.785 1.808 C-27.607 1.265 -28.43 0.723 -29.278 0.163 C-59.851 -19.654 -97.661 -23.628 -132.597 -14.442 Z M-108.597 65.558 C-109.952 68.996 -111.307 72.434 -112.662 75.873 C-113.835 78.85 -115.009 81.827 -116.183 84.804 C-119.059 92.098 -121.927 99.395 -124.769 106.703 C-125.263 107.969 -125.756 109.236 -126.25 110.502 C-127.168 112.857 -128.082 115.213 -128.993 117.571 C-134.995 132.955 -134.995 132.955 -138.597 136.558 C-143.029 137.74 -145.567 137.741 -149.597 135.558 C-151.511 133.403 -153.216 131.31 -154.91 128.996 C-156.274 127.167 -157.645 125.355 -159.074 123.577 C-160.597 121.558 -160.597 121.558 -161.944 119.125 C-164.303 115.5 -166.437 113.055 -170.797 112.052 C-177.54 111.205 -184.314 111.354 -191.097 111.433 C-193.161 111.421 -195.225 111.406 -197.289 111.386 C-205.483 111.341 -213.477 111.377 -221.597 112.558 C-223.236 141.211 -209.53 168.772 -191.097 189.871 C-189.615 191.45 -188.116 193.013 -186.597 194.558 C-185.514 195.66 -185.514 195.66 -184.41 196.785 C-159.575 220.529 -127.13 228.197 -93.722 227.835 C-60.98 226.952 -31.046 213.02 -7.597 190.558 C-6.955 189.956 -6.313 189.354 -5.652 188.734 C-4.253 187.39 -2.916 185.981 -1.597 184.558 C-1.597 183.898 -1.597 183.238 -1.597 182.558 C-0.937 182.558 -0.277 182.558 0.403 182.558 C11.101 170.249 18.741 154.179 23.278 138.621 C23.469 137.979 23.661 137.337 23.858 136.675 C26.033 128.498 26.561 119.953 27.403 111.558 C19.804 111.533 12.205 111.515 4.606 111.503 C2.021 111.498 -0.564 111.491 -3.149 111.483 C-6.865 111.471 -10.58 111.465 -14.296 111.46 C-15.452 111.455 -16.607 111.45 -17.798 111.445 C-18.878 111.445 -19.959 111.445 -21.073 111.445 C-22.021 111.442 -22.969 111.44 -23.946 111.438 C-26.622 111.559 -29.014 111.844 -31.597 112.558 C-33.197 114.634 -33.197 114.634 -34.289 117.359 C-34.753 118.386 -35.217 119.413 -35.695 120.472 C-36.178 121.593 -36.662 122.715 -37.16 123.871 C-37.672 125.017 -38.184 126.164 -38.712 127.345 C-41.522 133.672 -44.235 140.039 -46.941 146.41 C-47.348 147.366 -47.348 147.366 -47.763 148.342 C-49.081 151.441 -50.387 154.544 -51.676 157.655 C-52.146 158.777 -52.616 159.898 -53.101 161.054 C-53.717 162.542 -53.717 162.542 -54.345 164.06 C-55.871 167.104 -57.23 169.055 -60.464 170.246 C-61.127 170.349 -61.79 170.452 -62.472 170.558 C-63.13 170.682 -63.787 170.806 -64.464 170.933 C-67.925 170.325 -69.33 168.153 -71.597 165.558 C-73.155 162.279 -73.155 162.279 -74.464 158.554 C-74.705 157.882 -74.946 157.21 -75.194 156.518 C-75.967 154.348 -76.72 152.172 -77.472 149.996 C-78 148.502 -78.529 147.009 -79.059 145.517 C-80.354 141.864 -81.632 138.206 -82.905 134.545 C-83.894 131.707 -84.896 128.873 -85.898 126.039 C-87.832 120.557 -89.759 115.073 -91.597 109.558 C-93.412 104.178 -95.234 98.801 -97.062 93.426 C-97.667 91.645 -98.27 89.863 -98.872 88.081 C-99.742 85.503 -100.618 82.927 -101.496 80.351 C-101.894 79.165 -101.894 79.165 -102.301 77.955 C-103.778 73.636 -105.443 69.599 -107.597 65.558 C-107.927 65.558 -108.257 65.558 -108.597 65.558 Z " fill="#3BB9FE" transform="translate(424.59716796875,329.44189453125)"/>
<path d="M0 0 C2.935 2.283 4.616 4.04 6 7.5 C6 10.821 4.878 12.287 3 15 C1.117 16.633 1.117 16.633 -1.125 18.125 C-2.009 18.718 -2.894 19.311 -3.805 19.922 C-4.859 20.608 -5.914 21.293 -7 22 C-7.628 22.411 -8.256 22.821 -8.903 23.244 C-15.253 27.392 -21.632 31.495 -28.031 35.566 C-28.66 35.967 -29.289 36.369 -29.937 36.782 C-31.638 37.866 -33.342 38.947 -35.047 40.027 C-40.728 43.527 -40.728 43.527 -44 49 C-44.071 51.739 -44.094 54.45 -44.062 57.188 C-44.056 58.312 -44.056 58.312 -44.049 59.459 C-44.037 61.306 -44.019 63.153 -44 65 C-42.298 64.598 -42.298 64.598 -40.562 64.188 C-38.709 63.787 -36.855 63.392 -35 63 C-33.751 62.724 -32.502 62.448 -31.215 62.164 C-11.648 58.085 7.698 60.334 25 71 C28.132 73.189 31.073 75.546 34 78 C34.668 78.522 35.335 79.044 36.023 79.582 C54.073 94.243 62.241 117.763 64.617 140.242 C66.8 165.112 60.024 186.48 50.308 208.958 C49.061 211.858 47.84 214.768 46.629 217.684 C35.701 243.877 22.425 268.059 -4.438 280.605 C-14.352 284.592 -23.774 286.36 -34.438 286.25 C-35.364 286.246 -36.291 286.242 -37.246 286.238 C-42.71 286.169 -47.733 285.647 -53.07 284.453 C-57.217 283.812 -61.31 283.77 -65.5 283.75 C-66.272 283.741 -67.043 283.732 -67.838 283.723 C-72.081 283.744 -75.989 284.142 -80.145 285.008 C-87.145 286.382 -94.079 286.217 -101.188 286.125 C-102.534 286.115 -103.882 286.106 -105.229 286.098 C-108.486 286.074 -111.743 286.042 -115 286 C-115 285.34 -115 284.68 -115 284 C-116.011 283.876 -117.021 283.753 -118.062 283.625 C-134.488 281.018 -147.884 268.723 -158 256 C-170.637 238.555 -178.788 217.913 -186.938 198.125 C-187.327 197.179 -187.717 196.233 -188.119 195.259 C-188.481 194.372 -188.842 193.484 -189.215 192.57 C-189.696 191.391 -189.696 191.391 -190.187 190.188 C-190.951 188.133 -191.516 186.136 -192 184 C-192.237 182.965 -192.474 181.93 -192.719 180.863 C-192.977 179.692 -193.234 178.52 -193.5 177.312 C-193.758 176.154 -194.016 174.995 -194.281 173.801 C-198.335 152.362 -196.07 131.336 -188.691 110.933 C-188.059 109.164 -187.465 107.381 -186.875 105.598 C-185.555 101.68 -183.582 99.217 -181 96 C-179.722 94.213 -178.451 92.422 -177.188 90.625 C-164.923 73.775 -147.063 63.772 -126.586 60.445 C-109.814 58.363 -94.373 61.34 -79 68 C-79.023 64.769 -79.1 61.542 -79.188 58.312 C-79.193 57.4 -79.199 56.488 -79.205 55.549 C-79.356 50.933 -79.474 48.679 -82.375 44.938 C-84.881 43.088 -87.037 41.931 -90 41 C-90 40.34 -90 39.68 -90 39 C-90.673 38.771 -91.346 38.541 -92.039 38.305 C-95.8 36.648 -98.999 34.45 -102.375 32.125 C-106.143 29.544 -109.887 27.02 -113.875 24.789 C-117 23 -117 23 -118.625 21.25 C-119.262 17.429 -117.873 14.312 -116 11 C-113.75 8.938 -113.75 8.938 -111 8 C-105.867 8.363 -102.55 9.503 -98.27 12.316 C-97.218 12.999 -96.167 13.682 -95.084 14.385 C-94.004 15.103 -92.925 15.822 -91.812 16.562 C-90.222 17.6 -90.222 17.6 -88.6 18.658 C-86.462 20.056 -84.331 21.463 -82.206 22.879 C-80.041 24.311 -77.861 25.718 -75.666 27.101 C-74.604 27.778 -73.543 28.455 -72.449 29.152 C-71.497 29.749 -70.546 30.346 -69.565 30.961 C-66.619 33.303 -64.93 35.791 -63 39 C-59.673 38.227 -59.673 38.227 -57.773 34.738 C-54.999 30.68 -51.841 28.601 -47.625 26.188 C-46.189 25.329 -44.754 24.468 -43.32 23.605 C-42.597 23.174 -41.875 22.743 -41.13 22.298 C-37.851 20.3 -34.677 18.155 -31.5 16 C-26.124 12.377 -20.686 8.87 -15.188 5.438 C-14.155 4.793 -14.155 4.793 -13.102 4.135 C-5.419 -0.638 -5.419 -0.638 0 0 Z M-63 40 C-63 41.98 -63 43.96 -63 46 C-62.34 45.01 -61.68 44.02 -61 43 C-61.33 42.01 -61.66 41.02 -62 40 C-62.33 40 -62.66 40 -63 40 Z M-163 99 C-168.985 106.59 -173.194 114.74 -176 124 C-176.392 125.031 -176.784 126.062 -177.188 127.125 C-185.668 157.133 -173.94 187.434 -161.334 214.147 C-160.148 216.684 -158.994 219.232 -157.859 221.793 C-148.953 241.727 -136.911 259.687 -115.891 268.168 C-109.915 270.271 -104.478 270.576 -98.188 270.438 C-97.134 270.427 -96.081 270.417 -94.996 270.406 C-88.088 270.262 -81.971 269.385 -75.281 267.68 C-66.577 265.877 -57.721 266.415 -49.125 268.5 C-32.734 272.379 -17.625 270.456 -3.125 261.688 C-1.395 260.49 0.315 259.261 2 258 C2.779 257.428 3.557 256.855 4.359 256.266 C19.982 243.66 27.814 221.198 34.847 203.052 C35.822 200.537 36.808 198.027 37.795 195.518 C38.433 193.888 39.07 192.259 39.707 190.629 C39.995 189.894 40.282 189.16 40.578 188.403 C49.368 165.697 51.797 140.954 42.75 117.812 C41.864 115.861 40.947 113.923 40 112 C39.519 111.014 39.038 110.028 38.543 109.012 C30.888 94.652 18.606 83.861 3 79 C-13.526 76.162 -27.93 77.513 -41.938 87.125 C-42.553 87.569 -43.168 88.013 -43.801 88.47 C-51.643 94.116 -59.417 95.419 -69 94 C-74.652 92.029 -79.817 88.954 -85 86 C-95.756 80.187 -104.807 77.744 -117.062 77.625 C-118.446 77.604 -118.446 77.604 -119.856 77.583 C-136.761 77.699 -152.032 86.402 -163 99 Z " fill="#32AFFE" transform="translate(786,281)"/>
<path d="M0 0 C33.563 0 33.563 0 40.527 6.074 C44.883 11.77 45.466 17.319 45.625 24.25 C45.729 30.21 45.729 30.21 47 36 C47.969 35.938 48.939 35.876 49.938 35.812 C55.987 35.837 61.213 37.524 65.875 41.375 C73.111 50.314 72.211 60.146 72.24 71.208 C72.25 73.429 72.281 75.649 72.312 77.869 C72.378 86.664 71.97 94.05 67.312 101.812 C62.061 106.78 56.114 108.444 49.102 109.605 C46.952 109.909 46.952 109.909 45 111 C45.016 112.018 45.031 113.037 45.048 114.086 C45.102 117.895 45.136 121.705 45.165 125.514 C45.18 127.157 45.2 128.8 45.226 130.443 C45.263 132.816 45.28 135.189 45.293 137.562 C45.308 138.287 45.324 139.011 45.34 139.757 C45.342 145.719 44.18 151.205 40.422 156.023 C39.87 156.469 39.318 156.915 38.75 157.375 C37.938 158.067 37.938 158.067 37.109 158.773 C29.901 162.965 22.152 162.235 14.062 162.125 C12.806 162.116 11.55 162.107 10.256 162.098 C7.17 162.074 4.085 162.041 1 162 C0.67 162.99 0.34 163.98 0 165 C0 110.55 0 56.1 0 0 Z " fill="#B4B3B3" transform="translate(0,361)"/>
<path d="M0 0 C0 51.81 0 103.62 0 157 C-1.32 156.34 -2.64 155.68 -4 155 C-7.873 153.27 -11.599 152.394 -15.77 151.633 C-17.084 151.385 -18.399 151.136 -19.713 150.887 C-21.759 150.505 -23.806 150.126 -25.854 149.755 C-45.653 146.148 -45.653 146.148 -52 137 C-55.212 131.058 -55.627 124.399 -53.938 117.938 C-52.456 113.52 -50.254 110.174 -47.305 106.594 C-45.917 105.019 -45.917 105.019 -45 103 C-45.928 102.319 -46.856 101.639 -47.812 100.938 C-51.264 98.406 -52.81 95.02 -54 91 C-54.417 88.173 -54.478 85.417 -54.438 82.562 C-54.43 81.813 -54.422 81.063 -54.415 80.29 C-54.221 73.825 -53.091 69.269 -48.574 64.492 C-43.489 59.894 -38.822 58.793 -32.125 58.5 C-27.095 58.194 -22.675 57.913 -18 56 C-15.456 52.184 -15.703 49.063 -15.691 44.641 C-15.666 42.951 -15.641 41.26 -15.615 39.57 C-15.587 36.913 -15.563 34.256 -15.544 31.599 C-15.522 29.029 -15.482 26.46 -15.441 23.891 C-15.441 23.105 -15.441 22.319 -15.441 21.51 C-15.326 15.013 -13.965 10.337 -9.688 5.25 C-3.417 0 -3.417 0 0 0 Z " fill="#B5B4B3" transform="translate(1024,411)"/>
<path d="M0 0 C8.91 0 17.82 0 27 0 C31.5 4.5 31.5 4.5 31.501 9.238 C31.511 10.297 31.521 11.357 31.531 12.449 C31.521 13.607 31.512 14.764 31.502 15.957 C31.507 17.18 31.513 18.403 31.518 19.663 C31.528 23.015 31.521 26.365 31.504 29.717 C31.491 33.225 31.497 36.733 31.501 40.241 C31.503 46.133 31.49 52.024 31.467 57.915 C31.44 64.725 31.438 71.534 31.45 78.344 C31.461 84.896 31.455 91.449 31.44 98.001 C31.435 100.789 31.435 103.577 31.439 106.365 C31.445 110.258 31.426 114.15 31.404 118.043 C31.409 119.2 31.413 120.358 31.418 121.551 C31.408 122.611 31.398 123.67 31.388 124.762 C31.385 125.683 31.383 126.603 31.38 127.551 C30.918 130.526 30.116 131.873 28 134 C18.94 136.265 9.339 135 0 135 C0 90.45 0 45.9 0 0 Z " fill="#D9D9D9" transform="translate(0,375)"/>
<path d="M0 0 C2.297 1.957 2.329 3.496 2.688 6.438 C3.183 7.923 3.183 7.923 3.688 9.438 C4.88 9.463 6.072 9.489 7.301 9.516 C8.867 9.572 10.434 9.629 12 9.688 C12.786 9.702 13.571 9.716 14.381 9.73 C20.122 9.975 20.122 9.975 22.531 11.945 C24.217 15.579 25.587 19.272 26.875 23.062 C27.148 23.839 27.42 24.615 27.701 25.414 C29.688 31.149 29.688 31.149 29.688 33.438 C30.347 33.438 31.007 33.438 31.688 33.438 C31.791 32.619 31.894 31.8 32 30.957 C32.811 26.803 34.168 23.064 35.688 19.125 C35.961 18.397 36.234 17.67 36.516 16.92 C38.547 11.578 38.547 11.578 39.688 10.438 C41.207 10.366 42.729 10.354 44.25 10.375 C45.076 10.384 45.903 10.393 46.754 10.402 C47.392 10.414 48.03 10.426 48.688 10.438 C49.688 12.438 49.688 12.438 49.102 14.55 C48.77 15.418 48.439 16.286 48.098 17.18 C47.729 18.154 47.361 19.129 46.981 20.134 C46.372 21.707 46.372 21.707 45.75 23.312 C45.136 24.918 45.136 24.918 44.509 26.556 C43.66 28.769 42.806 30.98 41.948 33.189 C40.771 36.223 39.607 39.261 38.445 42.301 C37.757 44.076 37.066 45.851 36.375 47.625 C35.906 48.852 35.906 48.852 35.428 50.103 C33.398 55.232 31.339 59.771 26.5 62.812 C22.476 63.707 18.709 63.26 14.688 62.438 C14.358 62.107 14.028 61.778 13.688 61.438 C13.864 58.747 14.306 56.109 14.688 53.438 C17.67 52.664 20.663 52.026 23.688 51.438 C23.327 44.355 21.449 38.707 18.746 32.16 C17.688 29.438 17.688 29.438 17.688 27.438 C16.202 26.942 16.202 26.942 14.688 26.438 C14.471 24.767 14.471 24.767 14.25 23.062 C13.904 19.616 13.904 19.616 12.688 17.438 C12.028 18.097 11.368 18.758 10.688 19.438 C8.688 19.771 6.688 20.104 4.688 20.438 C2.482 22.224 2.482 22.224 2.46 24.592 C2.471 25.462 2.481 26.331 2.492 27.227 C2.499 28.168 2.505 29.109 2.512 30.078 C2.528 31.063 2.545 32.048 2.562 33.062 C2.572 34.055 2.581 35.048 2.59 36.07 C2.613 38.526 2.646 40.982 2.688 43.438 C6.153 42.942 6.153 42.942 9.688 42.438 C10.017 43.428 10.347 44.418 10.688 45.438 C11.347 45.768 12.007 46.097 12.688 46.438 C11.613 49.353 10.909 51.216 8.688 53.438 C6.445 53.484 6.445 53.484 3.812 53.188 C2.949 53.1 2.085 53.012 1.195 52.922 C-2.629 52.183 -4.597 51.432 -6.911 48.217 C-8.492 45.082 -8.661 43.186 -8.605 39.699 C-8.591 38.099 -8.591 38.099 -8.576 36.467 C-8.551 35.364 -8.526 34.261 -8.5 33.125 C-8.48 31.442 -8.48 31.442 -8.459 29.725 C-8.424 26.962 -8.374 24.2 -8.312 21.438 C-11.418 20.227 -11.418 20.227 -15.312 20.438 C-15.642 20.438 -15.972 20.438 -16.312 20.438 C-16.35 21.838 -16.35 21.838 -16.388 23.267 C-16.483 26.721 -16.583 30.174 -16.685 33.627 C-16.728 35.124 -16.77 36.621 -16.81 38.117 C-16.867 40.265 -16.931 42.412 -16.996 44.559 C-17.033 45.852 -17.069 47.146 -17.107 48.478 C-17.312 51.438 -17.312 51.438 -18.312 52.438 C-20.967 52.892 -23.634 53.14 -26.312 53.438 C-27.922 50.218 -27.555 46.689 -27.629 43.148 C-27.65 42.33 -27.67 41.512 -27.692 40.668 C-27.757 38.05 -27.816 35.431 -27.875 32.812 C-27.918 31.039 -27.962 29.266 -28.006 27.492 C-28.113 23.141 -28.214 18.789 -28.312 14.438 C-30.894 19.483 -33.237 24.508 -35.328 29.781 C-35.589 30.436 -35.85 31.09 -36.118 31.765 C-36.936 33.821 -37.749 35.879 -38.562 37.938 C-39.122 39.346 -39.682 40.755 -40.242 42.164 C-41.603 45.587 -42.959 49.012 -44.312 52.438 C-50.367 53.414 -50.367 53.414 -52.312 53.438 C-54.952 50.798 -55.607 47.852 -56.688 44.375 C-59.597 34.126 -59.597 34.126 -65.312 25.438 C-65.312 24.117 -65.312 22.798 -65.312 21.438 C-65.837 19.09 -65.837 19.09 -66.625 16.75 C-66.869 15.962 -67.112 15.175 -67.363 14.363 C-67.677 13.728 -67.99 13.092 -68.312 12.438 C-69.303 12.107 -70.293 11.778 -71.312 11.438 C-68.668 10.115 -66.635 10.341 -63.688 10.375 C-62.183 10.389 -62.183 10.389 -60.648 10.402 C-59.878 10.414 -59.107 10.426 -58.312 10.438 C-55.066 17.197 -52.354 23.944 -50.109 31.102 C-49.527 33.37 -49.527 33.37 -48.312 34.438 C-47.911 33.4 -47.911 33.4 -47.502 32.342 C-46.296 29.227 -45.085 26.113 -43.875 23 C-43.454 21.911 -43.033 20.823 -42.6 19.701 C-41.995 18.149 -41.995 18.149 -41.379 16.566 C-41.007 15.608 -40.635 14.65 -40.252 13.662 C-39.312 11.438 -39.312 11.438 -38.312 10.438 C-36.667 10.39 -35.02 10.401 -33.375 10.438 C-27.956 10.494 -22.688 10.091 -17.312 9.438 C-17.312 10.097 -17.312 10.758 -17.312 11.438 C-16.653 11.438 -15.993 11.438 -15.312 11.438 C-15.312 12.758 -15.312 14.077 -15.312 15.438 C-14.983 13.788 -14.653 12.138 -14.312 10.438 C-12.332 10.438 -10.353 10.438 -8.312 10.438 C-8.312 7.467 -8.312 4.497 -8.312 1.438 C-3.009 -0.732 -3.009 -0.732 0 0 Z " fill="#E1E2E3" transform="translate(402.3125,673.5625)"/>
<path d="M0 0 C2.935 2.283 4.616 4.04 6 7.5 C6 10.821 4.878 12.287 3 15 C1.117 16.633 1.117 16.633 -1.125 18.125 C-2.009 18.718 -2.894 19.311 -3.805 19.922 C-5.386 20.951 -5.386 20.951 -7 22 C-7.628 22.411 -8.256 22.821 -8.903 23.244 C-15.253 27.392 -21.632 31.495 -28.031 35.566 C-28.66 35.967 -29.289 36.369 -29.937 36.782 C-31.638 37.866 -33.342 38.947 -35.047 40.027 C-40.728 43.527 -40.728 43.527 -44 49 C-44.071 51.739 -44.094 54.45 -44.062 57.188 C-44.058 57.937 -44.053 58.687 -44.049 59.459 C-44.037 61.306 -44.019 63.153 -44 65 C-43.455 64.867 -42.909 64.734 -42.348 64.598 C-36.901 63.322 -31.569 62.53 -26 62 C-28.806 63.871 -30.501 64.497 -33.688 65.312 C-39.928 67.114 -44.681 70.112 -49.688 74.188 C-53.754 77.375 -56.921 78.102 -62 78 C-62.168 73.535 -62.329 69.069 -62.482 64.603 C-62.535 63.085 -62.591 61.567 -62.648 60.049 C-62.731 57.862 -62.805 55.675 -62.879 53.488 C-62.926 52.174 -62.973 50.859 -63.022 49.505 C-63.003 46.472 -62.608 43.946 -62 41 C-62.33 40.34 -62.66 39.68 -63 39 C-61.515 38.505 -61.515 38.505 -60 38 C-59.265 36.924 -58.53 35.847 -57.773 34.738 C-54.999 30.68 -51.841 28.601 -47.625 26.188 C-46.189 25.329 -44.754 24.468 -43.32 23.605 C-42.597 23.174 -41.875 22.743 -41.13 22.298 C-37.851 20.3 -34.677 18.155 -31.5 16 C-26.124 12.377 -20.686 8.87 -15.188 5.438 C-14.499 5.008 -13.811 4.578 -13.102 4.135 C-5.419 -0.638 -5.419 -0.638 0 0 Z " fill="#BBB7B6" transform="translate(786,281)"/>
<path d="M0 0 C1.236 0.017 1.236 0.017 2.496 0.035 C3.322 0.044 4.149 0.053 5 0.062 C5.638 0.074 6.276 0.086 6.934 0.098 C8.541 3.312 7.991 6.534 7.934 10.098 C8.572 10.086 9.21 10.074 9.867 10.062 C10.693 10.053 11.52 10.044 12.371 10.035 C13.195 10.024 14.019 10.012 14.867 10 C16.934 10.098 16.934 10.098 17.934 11.098 C18.059 14.598 18.059 14.598 17.934 18.098 C16.934 19.098 16.934 19.098 14.648 19.195 C13.732 19.184 12.815 19.172 11.871 19.16 C10.952 19.151 10.033 19.142 9.086 19.133 C8.376 19.121 7.665 19.11 6.934 19.098 C7.429 20.583 7.429 20.583 7.934 22.098 C8.08 23.65 8.181 25.208 8.25 26.766 C8.292 27.658 8.334 28.551 8.377 29.471 C8.416 30.399 8.456 31.328 8.496 32.285 C8.539 33.226 8.582 34.166 8.627 35.135 C8.733 37.456 8.835 39.777 8.934 42.098 C11.244 42.098 13.554 42.098 15.934 42.098 C16.924 44.408 17.914 46.718 18.934 49.098 C15.877 51.78 13.989 52.491 9.934 52.41 C9.088 52.406 8.242 52.402 7.371 52.398 C3.821 51.96 1.786 51.018 -0.652 48.387 C-2.425 45.518 -2.492 43.597 -2.578 40.238 C-2.614 39.123 -2.649 38.008 -2.686 36.859 C-2.719 35.121 -2.719 35.121 -2.754 33.348 C-2.788 32.173 -2.822 30.999 -2.857 29.789 C-2.94 26.892 -3.009 23.995 -3.066 21.098 C-6.977 19.627 -6.977 19.627 -11.066 19.098 C-10.983 19.972 -10.899 20.846 -10.812 21.746 C-9.966 31.968 -10.305 41.878 -11.066 52.098 C-14.036 52.098 -17.006 52.098 -20.066 52.098 C-20.066 51.108 -20.066 50.118 -20.066 49.098 C-20.695 49.398 -21.325 49.698 -21.973 50.008 C-28.075 52.719 -31.55 53.221 -38.066 52.098 C-40.49 52.886 -40.49 52.886 -42.066 54.098 C-42.561 52.613 -42.561 52.613 -43.066 51.098 C-42.076 50.603 -42.076 50.603 -41.066 50.098 C-41.685 49.561 -42.304 49.025 -42.941 48.473 C-48.148 42.653 -47.429 35.094 -47.545 27.734 C-47.571 26.911 -47.598 26.087 -47.625 25.238 C-47.638 24.491 -47.652 23.744 -47.666 22.974 C-47.926 20.687 -47.926 20.687 -51.066 19.098 C-51.359 16.934 -51.359 16.934 -51.254 14.473 C-51.227 13.655 -51.2 12.838 -51.172 11.996 C-51.12 11.056 -51.12 11.056 -51.066 10.098 C-48.465 8.797 -45.927 8.533 -43.066 8.098 C-43.066 8.758 -43.066 9.418 -43.066 10.098 C-42.448 9.912 -41.829 9.726 -41.191 9.535 C-39.066 9.098 -39.066 9.098 -37.066 10.098 C-35.999 13.107 -35.961 15.92 -36.004 19.09 C-36.012 20.042 -36.019 20.995 -36.027 21.977 C-36.053 23.966 -36.079 25.956 -36.105 27.945 C-36.145 32.787 -36.099 37.337 -35.066 42.098 C-30.075 42.391 -27.092 42.176 -23.066 39.098 C-21.727 36.42 -21.87 34.245 -21.785 31.25 C-21.748 30.088 -21.71 28.926 -21.672 27.729 C-21.601 25.262 -21.531 22.796 -21.461 20.33 C-21.424 19.169 -21.386 18.008 -21.348 16.812 C-21.317 15.741 -21.287 14.67 -21.256 13.566 C-21.066 11.098 -21.066 11.098 -20.066 10.098 C-18.547 10.026 -17.025 10.014 -15.504 10.035 C-14.678 10.044 -13.851 10.053 -13 10.062 C-12.362 10.074 -11.724 10.086 -11.066 10.098 C-11.066 11.418 -11.066 12.738 -11.066 14.098 C-10.406 14.098 -9.746 14.098 -9.066 14.098 C-8.736 12.778 -8.406 11.458 -8.066 10.098 C-6.416 9.768 -4.766 9.438 -3.066 9.098 C-3.087 7.798 -3.108 6.499 -3.129 5.16 C-3.15 3.806 -3.138 2.45 -3.066 1.098 C-2.066 0.098 -2.066 0.098 0 0 Z M-11.066 15.098 C-10.076 16.583 -10.076 16.583 -9.066 18.098 C-9.066 17.108 -9.066 16.118 -9.066 15.098 C-9.726 15.098 -10.386 15.098 -11.066 15.098 Z " fill="#D6D6D6" transform="translate(51.06640625,673.90234375)"/>
<path d="M0 0 C8 0 8 0 10.25 1.766 C10.827 2.585 11.405 3.405 12 4.25 C12.99 5.621 12.99 5.621 14 7.02 C14.66 8.003 15.32 8.987 16 10 C16.99 11.46 16.99 11.46 18 12.949 C19.334 14.943 20.658 16.943 21.973 18.949 C25.155 23.739 28.436 28.485 32 33 C32.33 33 32.66 33 33 33 C33.038 31.607 33.038 31.607 33.076 30.186 C33.171 26.746 33.27 23.305 33.372 19.865 C33.416 18.375 33.457 16.885 33.497 15.396 C33.555 13.256 33.619 11.116 33.684 8.977 C33.72 7.688 33.757 6.4 33.795 5.073 C34 2 34 2 35 0 C41.75 -0.125 41.75 -0.125 44 1 C44.168 5.769 44.328 10.537 44.482 15.307 C44.535 16.928 44.591 18.55 44.648 20.172 C44.73 22.505 44.805 24.838 44.879 27.172 C44.906 27.895 44.933 28.618 44.961 29.363 C45.061 32.741 45.084 35.774 44 39 C44.99 39.495 44.99 39.495 46 40 C45.505 40.99 45.505 40.99 45 42 C44.955 43.999 44.962 46 45 48 C45.111 53.778 45.111 53.778 44 56 C38.609 57.217 38.609 57.217 35 56 C31.034 51.745 28.074 46.918 25 42 C23.702 40.032 22.391 38.072 21.062 36.125 C20.538 35.323 20.013 34.521 19.473 33.695 C18.987 33.136 18.501 32.576 18 32 C17.01 32 16.02 32 15 32 C14.918 31.299 14.835 30.597 14.75 29.875 C13.831 26.351 12.061 23.976 10 21 C9.975 25.093 9.957 29.187 9.945 33.28 C9.94 34.673 9.933 36.066 9.925 37.459 C9.912 39.46 9.907 41.46 9.902 43.461 C9.897 44.665 9.892 45.87 9.886 47.111 C9.799 49.941 9.799 49.941 11 52 C10.333 53.333 9.667 54.667 9 56 C9.33 56.66 9.66 57.32 10 58 C6.7 58 3.4 58 0 58 C0 38.86 0 19.72 0 0 Z M33 34 C34 36 34 36 34 36 Z " fill="#E7E6E6" transform="translate(581,670)"/>
<path d="M0 0 C5.733 8.073 8.523 18.909 11.997 28.154 C12.779 30.229 13.567 32.302 14.355 34.375 C14.816 35.6 15.276 36.824 15.751 38.086 C16.812 41.06 16.812 41.06 19 43 C19.636 44.999 20.19 47.025 20.688 49.062 C20.959 50.147 21.231 51.231 21.512 52.348 C21.673 53.223 21.834 54.098 22 55 C21 56 21 56 18.934 56.098 C18.11 56.086 17.286 56.074 16.438 56.062 C15.611 56.053 14.785 56.044 13.934 56.035 C13.296 56.024 12.657 56.012 12 56 C11.662 55.169 11.325 54.337 10.977 53.48 C10.308 51.85 10.308 51.85 9.625 50.188 C9.184 49.109 8.743 48.03 8.289 46.918 C7.052 43.893 7.052 43.893 5 41 C3.68 41 2.36 41 1 41 C0.67 40.34 0.34 39.68 0 39 C-4.62 39 -9.24 39 -14 39 C-14.424 40.216 -14.848 41.431 -15.285 42.684 C-15.876 44.269 -16.469 45.853 -17.062 47.438 C-17.338 48.24 -17.613 49.042 -17.896 49.869 C-18.766 52.133 -19.567 54.041 -21 56 C-24.594 57 -24.594 57 -28 57 C-29.339 57.309 -30.677 57.629 -32 58 C-32 52.454 -29.436 47.534 -27.242 42.52 C-26.978 41.913 -26.714 41.306 -26.441 40.681 C-25.588 38.724 -24.732 36.768 -23.875 34.812 C-22.698 32.118 -21.522 29.422 -20.348 26.727 C-20.058 26.063 -19.769 25.4 -19.471 24.716 C-17.756 20.77 -16.076 16.813 -14.434 12.836 C-14.068 11.952 -13.703 11.068 -13.327 10.157 C-12.666 8.547 -12.01 6.936 -11.361 5.322 C-8.759 -1.007 -6.826 -2.198 0 0 Z M-5 15 C-4 17 -4 17 -4 17 Z M-6 17 C-6.671 18.623 -7.337 20.249 -8 21.875 C-8.371 22.78 -8.743 23.685 -9.125 24.617 C-10.115 26.89 -10.115 26.89 -10 29 C-6.7 29 -3.4 29 0 29 C-0.569 23.188 -0.569 23.188 -3 18 C-3.99 17.67 -4.98 17.34 -6 17 Z " fill="#E5E5E5" transform="translate(232,670)"/>
<path d="M0 0 C1.514 0.017 1.514 0.017 3.059 0.035 C4.071 0.044 5.082 0.053 6.125 0.062 C6.907 0.074 7.69 0.086 8.496 0.098 C9.957 3.02 9.607 5.597 9.594 8.855 C9.592 9.519 9.591 10.182 9.589 10.865 C9.584 12.984 9.571 15.103 9.559 17.223 C9.554 18.659 9.549 20.095 9.545 21.531 C9.534 25.053 9.517 28.576 9.496 32.098 C11.476 32.098 13.456 32.098 15.496 32.098 C15.991 35.563 15.991 35.563 16.496 39.098 C14.516 39.758 12.536 40.418 10.496 41.098 C10.405 42.596 10.405 42.596 10.312 44.125 C10.229 45.415 10.145 46.706 10.059 48.035 C9.977 49.323 9.896 50.611 9.812 51.938 C9.496 55.098 9.496 55.098 8.496 56.098 C7.171 56.355 5.835 56.555 4.496 56.723 C3.774 56.818 3.052 56.913 2.309 57.012 C1.71 57.04 1.112 57.068 0.496 57.098 C-0.504 56.098 -0.504 56.098 -0.602 52.719 C-0.595 51.324 -0.583 49.93 -0.566 48.535 C-0.562 47.824 -0.557 47.113 -0.553 46.381 C-0.541 44.62 -0.523 42.859 -0.504 41.098 C-5.949 41.593 -5.949 41.593 -11.504 42.098 C-11.891 43.097 -12.277 44.096 -12.676 45.125 C-13.201 46.429 -13.727 47.732 -14.254 49.035 C-14.507 49.695 -14.759 50.354 -15.02 51.033 C-16.994 55.847 -16.994 55.847 -19.473 57.066 C-22.547 57.114 -25.482 56.621 -28.504 56.098 C-7.906 0.308 -7.906 0.308 0 0 Z M-1.504 18.098 C-1.999 20.078 -1.999 20.078 -2.504 22.098 C-1.844 21.768 -1.184 21.438 -0.504 21.098 C-0.834 20.108 -1.164 19.118 -1.504 18.098 Z M-3.504 22.098 C-2.504 24.098 -2.504 24.098 -2.504 24.098 Z M-4.504 24.098 C-5.824 26.738 -7.144 29.378 -8.504 32.098 C-5.864 32.098 -3.224 32.098 -0.504 32.098 C-0.504 29.458 -0.504 26.818 -0.504 24.098 C-1.494 24.098 -2.484 24.098 -3.504 24.098 C-3.834 24.098 -4.164 24.098 -4.504 24.098 Z " fill="#EEEEEE" transform="translate(983.50390625,669.90234375)"/>
<path d="M0 0 C0.33 2.97 0.66 5.94 1 9 C2.279 9.082 3.558 9.165 4.875 9.25 C8.765 9.747 8.765 9.747 10.625 11.75 C11.117 14.702 10.319 16.362 9 19 C6.03 19 3.06 19 0 19 C0.495 30.88 0.495 30.88 1 43 C3.64 42.67 6.28 42.34 9 42 C11 45 11 45 11 48 C11.66 48 12.32 48 13 48 C13.027 45.708 13.046 43.417 13.062 41.125 C13.074 39.849 13.086 38.573 13.098 37.258 C13.232 34.113 13.232 34.113 12 32 C12.66 32 13.32 32 14 32 C13.834 31.435 13.667 30.871 13.496 30.289 C12.818 27.158 13.028 24.208 13.16 21.023 C13.202 18.769 13.202 18.769 11 17 C11.33 16.01 11.66 15.02 12 14 C12.66 14 13.32 14 14 14 C13.67 13.01 13.34 12.02 13 11 C14 10 14 10 16.066 9.902 C17.302 9.92 17.302 9.92 18.562 9.938 C19.389 9.947 20.215 9.956 21.066 9.965 C21.704 9.976 22.343 9.988 23 10 C24.321 12.643 24.13 14.645 24.133 17.602 C24.134 18.732 24.135 19.863 24.137 21.027 C24.133 22.215 24.129 23.402 24.125 24.625 C24.131 26.371 24.131 26.371 24.137 28.152 C24.13 34.197 23.929 40.025 23 46 C23.99 46.495 23.99 46.495 25 47 C25 47.66 25 48.32 25 49 C24.34 49 23.68 49 23 49 C23 49.99 23 50.98 23 52 C18.161 52.095 13.751 52.008 9 51 C8.01 51.495 8.01 51.495 7 52 C4.181 52.313 1.36 52.51 -1.469 52.719 C-3.991 52.851 -3.991 52.851 -6 54 C-6.103 53.278 -6.206 52.556 -6.312 51.812 C-6.806 48.983 -6.806 48.983 -8.5 47.031 C-10.793 42.398 -10.573 37.792 -10.688 32.688 C-10.722 31.661 -10.756 30.634 -10.791 29.576 C-10.873 27.051 -10.942 24.526 -11 22 C-11.99 22 -12.98 22 -14 22 C-14 21.34 -14 20.68 -14 20 C-14.66 19.67 -15.32 19.34 -16 19 C-16 18.34 -16 17.68 -16 17 C-16.66 16.67 -17.32 16.34 -18 16 C-17.34 14.02 -16.68 12.04 -16 10 C-14.02 10 -12.04 10 -10 10 C-10.062 8.721 -10.124 7.442 -10.188 6.125 C-10.293 3.945 -10.293 3.945 -10 2 C-6.356 -0.429 -4.288 -0.162 0 0 Z " fill="#EFF0F0" transform="translate(758,674)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 13.53 1 27.06 1 41 C-4.28 40.34 -9.56 39.68 -15 39 C-17.496 38.691 -19.991 38.381 -22.562 38.062 C-33.734 36.421 -33.734 36.421 -37.652 32.234 C-40.117 28.148 -39.507 23.616 -39 19 C-36.894 13.447 -36.894 13.447 -34 12 C-32.585 11.884 -31.165 11.821 -29.746 11.789 C-28.891 11.761 -28.036 11.732 -27.154 11.703 C-25.371 11.652 -23.587 11.607 -21.803 11.57 C-15.227 11.335 -7.828 10.982 -3.062 5.957 C-2.537 4.895 -2.537 4.895 -2 3.812 C-1.339 2.539 -0.674 1.267 0 0 Z " fill="#D8D9D9" transform="translate(1023,470)"/>
<path d="M0 0 C2.774 1.765 5.319 3.533 7.75 5.75 C7.75 6.41 7.75 7.07 7.75 7.75 C8.41 7.75 9.07 7.75 9.75 7.75 C12.334 16.633 13.034 24.293 8.75 32.75 C5.795 37.566 2.039 40.007 -3.25 41.75 C-6.914 42.329 -10.545 42.614 -14.25 42.75 C-14.25 42.09 -14.25 41.43 -14.25 40.75 C-15.075 40.606 -15.9 40.461 -16.75 40.312 C-21.954 37.989 -25.188 34.528 -28.25 29.75 C-30.435 22.832 -29.278 16.059 -26.246 9.617 C-20.914 0.079 -10.326 -3.971 0 0 Z M-17.25 12.75 C-19.009 18.026 -18.898 22.968 -17 28.188 C-15.15 30.896 -14.369 31.859 -11.25 32.75 C-7.581 33.218 -5.765 33.031 -2.5 31.25 C0.463 27.957 1.464 25.154 1.75 20.75 C1.278 17.213 0.354 13.957 -1.25 10.75 C-2.57 11.08 -3.89 11.41 -5.25 11.75 C-5.25 10.76 -5.25 9.77 -5.25 8.75 C-10.731 8.32 -13.368 8.754 -17.25 12.75 Z " fill="#101010" transform="translate(814.25,684.25)"/>
<path d="M0 0 C1.265 -0.005 1.265 -0.005 2.555 -0.01 C8.876 0.043 13.755 1.013 20.25 2.312 C20.25 11.553 20.25 20.793 20.25 30.312 C15.795 29.549 11.34 28.786 6.75 28 C4.667 27.649 4.667 27.649 2.541 27.292 C-13.211 24.534 -13.211 24.534 -18.25 20.125 C-20.208 16.454 -20.523 13.336 -19.75 9.312 C-15.464 0.575 -8.843 -0.166 0 0 Z " fill="#D8D9DA" transform="translate(1003.75,521.6875)"/>
<path d="M0 0 C3.909 4.529 4.122 8.207 4.098 14.074 C4.096 14.758 4.095 15.442 4.093 16.146 C4.088 18.327 4.075 20.507 4.062 22.688 C4.057 24.167 4.053 25.647 4.049 27.127 C4.038 30.751 4.021 34.376 4 38 C1.69 38.66 -0.62 39.32 -3 40 C-5.403 38.174 -5.961 37.268 -6.401 34.229 C-6.414 33.132 -6.428 32.036 -6.441 30.906 C-6.468 29.711 -6.494 28.516 -6.521 27.285 C-6.535 26.036 -6.549 24.787 -6.562 23.5 C-6.597 21.031 -6.639 18.562 -6.691 16.094 C-6.704 14.997 -6.716 13.901 -6.729 12.771 C-6.801 9.788 -6.801 9.788 -9 7 C-12.849 6.501 -15.058 6.281 -18.207 8.684 C-20.425 11.549 -20.446 12.917 -20.32 16.5 C-20.298 17.552 -20.276 18.604 -20.254 19.688 C-20.211 20.781 -20.169 21.874 -20.125 23 C-20.056 25.166 -19.996 27.333 -19.945 29.5 C-19.909 30.459 -19.873 31.418 -19.836 32.406 C-20.008 35.122 -20.61 36.689 -22 39 C-28.152 39.098 -28.152 39.098 -30 39 C-33.019 35.981 -31.937 28.139 -32 24 C-31.944 19.393 -31.798 14.792 -31.625 10.188 C-31.582 9.012 -31.539 7.836 -31.495 6.625 C-31.13 -1.87 -31.13 -1.87 -30 -3 C-22.478 -3.239 -22.478 -3.239 -20 -2 C-18.486 -2.547 -16.988 -3.138 -15.5 -3.75 C-9.267 -5.749 -5.104 -3.812 0 0 Z " fill="#F0F0F0" transform="translate(860,687)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10.014 1.241 10.014 1.241 10.028 2.507 C10.082 6.255 10.166 10.002 10.25 13.75 C10.264 15.052 10.278 16.354 10.293 17.695 C10.325 18.946 10.357 20.196 10.391 21.484 C10.412 22.636 10.433 23.789 10.454 24.976 C11.109 28.606 12.05 29.835 15 32 C18.557 32.325 18.557 32.325 22 31 C26.748 25.823 26.464 21.155 26.188 14.375 C26.135 12.508 26.087 10.641 26.043 8.773 C26.012 7.953 25.982 7.132 25.95 6.287 C26 4 26 4 27 0 C29.97 0 32.94 0 36 0 C36.918 4.675 37.146 9.205 37.133 13.953 C37.134 14.695 37.135 15.436 37.136 16.2 C37.136 17.756 37.135 19.313 37.13 20.869 C37.125 23.257 37.13 25.644 37.137 28.031 C37.136 29.547 37.135 31.063 37.133 32.578 C37.135 33.293 37.137 34.007 37.139 34.743 C37.115 39.77 37.115 39.77 36 42 C33.715 42.414 33.715 42.414 30.938 42.625 C30.018 42.7 29.099 42.775 28.152 42.852 C27.442 42.901 26.732 42.95 26 43 C25.67 42.01 25.34 41.02 25 40 C24.441 40.324 23.881 40.647 23.305 40.98 C18.312 43.189 12.717 42.541 7.625 40.938 C3.411 37.827 1.654 33.962 0 29 C-0.084 26.789 -0.107 24.576 -0.098 22.363 C-0.094 21.065 -0.091 19.766 -0.088 18.428 C-0.08 17.056 -0.071 15.684 -0.062 14.312 C-0.057 12.929 -0.053 11.546 -0.049 10.162 C-0.037 6.775 -0.021 3.387 0 0 Z " fill="#DDDDDD" transform="translate(630,684)"/>
<path d="M0 0 C3.111 2.204 5.256 4.161 6.625 7.812 C6.312 10.625 6.312 10.625 5.625 12.812 C2.375 13.625 2.375 13.625 -1.375 13.812 C-3.625 11.438 -3.625 11.438 -5.375 8.812 C-6.365 8.482 -7.355 8.153 -8.375 7.812 C-8.705 8.803 -9.035 9.793 -9.375 10.812 C-10.035 10.482 -10.695 10.153 -11.375 9.812 C-11.375 9.153 -11.375 8.492 -11.375 7.812 C-13.827 7.919 -13.827 7.919 -16.375 8.812 C-18.246 12.39 -18.624 15.375 -18.562 19.375 C-18.556 20.304 -18.55 21.234 -18.543 22.191 C-18.391 24.567 -18.165 26.57 -17.375 28.812 C-12.94 31.723 -8.332 30.612 -3.375 29.812 C-3.086 29.008 -2.798 28.204 -2.5 27.375 C-1.375 24.812 -1.375 24.812 0.625 23.812 C2.985 24.748 5.321 25.746 7.625 26.812 C5.988 32.678 3.941 36.384 -1.312 39.625 C-4.293 40.781 -6.223 41.091 -9.375 40.812 C-9.705 41.472 -10.035 42.133 -10.375 42.812 C-10.375 42.153 -10.375 41.492 -10.375 40.812 C-11.365 40.751 -12.355 40.689 -13.375 40.625 C-18.758 39.817 -23.45 37.401 -26.734 32.984 C-30.407 26.224 -31.169 19.479 -29.219 12.051 C-27.028 6.239 -23.645 2.094 -18.062 -0.688 C-11.599 -3.106 -6.091 -3.519 0 0 Z " fill="#EBEBEB" transform="translate(283.375,685.1875)"/>
<path d="M0 0 C1.607 3.215 1.057 6.436 1 10 C1.638 9.988 2.276 9.977 2.934 9.965 C3.76 9.956 4.586 9.947 5.438 9.938 C6.261 9.926 7.085 9.914 7.934 9.902 C10 10 10 10 11 11 C11.041 13.333 11.042 15.667 11 18 C9.36 18.464 9.36 18.464 7.688 18.938 C5.446 19.583 3.213 20.262 1 21 C0.973 23.375 0.953 25.75 0.938 28.125 C0.929 28.79 0.921 29.455 0.912 30.141 C0.892 34.207 1.211 38.012 2 42 C4.64 42 7.28 42 10 42 C11.257 44.906 12 46.796 12 50 C10.756 50.535 9.504 51.052 8.25 51.562 C7.554 51.853 6.858 52.143 6.141 52.441 C3.7 53.078 2.36 52.802 0 52 C-0.33 52.66 -0.66 53.32 -1 54 C-2 53 -3 52 -4 51 C-4.701 50.319 -5.403 49.639 -6.125 48.938 C-9.896 45.041 -9.347 40.533 -9.605 35.41 C-9.764 31.579 -9.764 31.579 -11 28 C-10.692 26.328 -10.356 24.662 -10 23 C-10 21.68 -10 20.36 -10 19 C-11.98 19 -13.96 19 -16 19 C-17.125 17.312 -17.125 17.312 -18 15 C-17.125 12.25 -17.125 12.25 -16 10 C-13.69 10 -11.38 10 -9 10 C-9 7.03 -9 4.06 -9 1 C-6.043 -0.478 -3.258 -0.06 0 0 Z " fill="#E7E9EA" transform="translate(686,674)"/>
<path d="M0 0 C5 0 5 0 6.773 1.617 C7.302 2.362 7.83 3.107 8.375 3.875 C8.971 4.699 9.566 5.522 10.18 6.371 C10.78 7.239 11.381 8.106 12 9 C12.58 9.816 13.16 10.632 13.758 11.473 C16.116 14.809 18.431 18.175 20.75 21.539 C24.239 26.571 27.853 31.434 31.715 36.188 C33 38 33 38 33 40 C33.66 40 34.32 40 35 40 C35 26.8 35 13.6 35 0 C36.65 0 38.3 0 40 0 C40 16.5 40 33 40 50 C35 50 35 50 33.404 48.754 C32.734 47.89 32.734 47.89 32.051 47.008 C31.546 46.366 31.042 45.725 30.522 45.063 C29.769 44.042 29.769 44.042 29 43 C28.52 42.353 28.039 41.705 27.544 41.038 C26.066 39.001 24.623 36.943 23.188 34.875 C22.673 34.139 22.158 33.404 21.627 32.646 C20.081 30.434 18.54 28.217 17 26 C16.54 25.339 16.081 24.679 15.607 23.998 C9.022 14.532 9.022 14.532 6 10 C5.67 23.2 5.34 36.4 5 50 C3.35 50 1.7 50 0 50 C0 33.5 0 17 0 0 Z " fill="#212121" transform="translate(583,673)"/>
<path d="M0 0 C1.607 3.215 1.057 6.436 1 10 C1.638 9.988 2.276 9.977 2.934 9.965 C3.76 9.956 4.586 9.947 5.438 9.938 C6.261 9.926 7.085 9.914 7.934 9.902 C10 10 10 10 11 11 C11.375 14.375 11.375 14.375 11 18 C8.5 19.938 8.5 19.938 6 21 C6 20.34 6 19.68 6 19 C4.02 19 2.04 19 0 19 C0.33 19.66 0.66 20.32 1 21 C1.152 22.593 1.249 24.191 1.316 25.789 C1.358 26.73 1.4 27.671 1.443 28.641 C1.483 29.625 1.522 30.61 1.562 31.625 C1.606 32.618 1.649 33.61 1.693 34.633 C1.8 37.088 1.902 39.544 2 42 C4.64 42 7.28 42 10 42 C11.478 44.957 11.06 47.742 11 51 C6.238 52.975 3.005 53.341 -2 52 C-4.909 50.135 -7.428 48.144 -9 45 C-9.154 43.042 -9.25 41.08 -9.316 39.117 C-9.358 37.951 -9.4 36.784 -9.443 35.582 C-9.483 34.359 -9.522 33.135 -9.562 31.875 C-9.606 30.644 -9.649 29.413 -9.693 28.145 C-9.8 25.096 -9.902 22.048 -10 19 C-11.65 19 -13.3 19 -15 19 C-16.125 13.375 -16.125 13.375 -15 10 C-13.02 10 -11.04 10 -9 10 C-9 7.03 -9 4.06 -9 1 C-6.043 -0.478 -3.258 -0.06 0 0 Z " fill="#E5E5E6" transform="translate(306,674)"/>
<path d="M0 0 C10 1 10 1 13.086 2.571 C15.708 5.899 15.703 8.803 15.629 12.871 C15.631 13.63 15.634 14.39 15.636 15.172 C15.635 16.775 15.622 18.378 15.597 19.981 C15.563 22.426 15.576 24.869 15.596 27.314 C15.589 28.875 15.579 30.436 15.566 31.996 C15.571 32.723 15.576 33.451 15.581 34.2 C15.469 39.065 14.461 41.585 11 45 C9 46 9 46 0 46 C0 30.82 0 15.64 0 0 Z " fill="#DADADA" transform="translate(44,410)"/>
<path d="M0 0 C1.472 2.944 1.223 5.646 1.316 8.93 C1.337 9.628 1.358 10.327 1.38 11.047 C1.446 13.323 1.504 15.599 1.562 17.875 C1.605 19.427 1.648 20.979 1.691 22.531 C1.989 33.688 2.123 44.838 2 56 C-1.96 56 -5.92 56 -10 56 C-10 43.13 -10 30.26 -10 17 C-12.31 17 -14.62 17 -17 17 C-17.705 13.461 -17.923 10.512 -17 7 C-12.06 2.095 -6.992 -0.477 0 0 Z " fill="#D1D1D2" transform="translate(947,670)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 1.32 2.66 2.64 3 4 C3.804 3.484 4.609 2.969 5.438 2.438 C10.176 0.525 13.003 1.167 18 2 C18.33 4.97 18.66 7.94 19 11 C16.084 12.458 13.395 11.871 10.191 11.645 C7.885 11.813 7.885 11.813 6.262 13.594 C3.037 19.744 3.62 28.319 4.562 35.062 C4.991 38.915 4.833 40.657 3 44 C-1.455 44.495 -1.455 44.495 -6 45 C-6.495 24.21 -6.495 24.21 -7 3 C-4.69 2.34 -2.38 1.68 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E6E7E8" transform="translate(707,682)"/>
<path d="M0 0 C1.236 0.017 1.236 0.017 2.496 0.035 C3.322 0.044 4.149 0.053 5 0.062 C5.638 0.074 6.276 0.086 6.934 0.098 C7.934 2.098 7.934 2.098 7.348 4.21 C7.017 5.078 6.685 5.946 6.344 6.84 C5.975 7.815 5.607 8.789 5.227 9.794 C4.618 11.367 4.618 11.367 3.996 12.973 C3.587 14.043 3.177 15.113 2.755 16.216 C1.906 18.429 1.052 20.64 0.194 22.849 C-0.983 25.883 -2.146 28.921 -3.309 31.961 C-3.997 33.736 -4.687 35.511 -5.379 37.285 C-5.691 38.103 -6.004 38.921 -6.326 39.763 C-8.356 44.892 -10.415 49.431 -15.254 52.473 C-19.278 53.367 -23.045 52.92 -27.066 52.098 C-27.396 51.768 -27.726 51.438 -28.066 51.098 C-27.89 48.407 -27.448 45.769 -27.066 43.098 C-24.084 42.324 -21.091 41.686 -18.066 41.098 C-18.427 34.015 -20.305 28.367 -23.008 21.82 C-24.066 19.098 -24.066 19.098 -24.066 17.098 C-25.056 16.768 -26.046 16.438 -27.066 16.098 C-27.221 14.613 -27.221 14.613 -27.379 13.098 C-27.974 8.815 -29.452 5.098 -31.066 1.098 C-28.096 1.098 -25.126 1.098 -22.066 1.098 C-22.066 1.428 -22.066 1.758 -22.066 2.098 C-23.716 2.098 -25.366 2.098 -27.066 2.098 C-25.537 5.994 -24.007 9.89 -22.477 13.786 C-21.957 15.11 -21.437 16.434 -20.917 17.758 C-20.167 19.667 -19.418 21.575 -18.668 23.484 C-18.218 24.631 -17.767 25.778 -17.303 26.96 C-16.532 28.916 -15.733 30.861 -14.892 32.788 C-13.681 36.176 -14.249 38.64 -15.066 42.098 C-16.191 44.473 -16.191 44.473 -18.066 46.098 C-21.754 46.848 -21.754 46.848 -25.066 47.098 C-25.396 48.088 -25.726 49.078 -26.066 50.098 C-24.67 50.064 -23.274 49.994 -21.879 49.91 C-21.102 49.875 -20.324 49.841 -19.523 49.805 C-15.99 48.788 -14.487 46.832 -12.066 44.098 C-10.501 41.151 -10.501 41.151 -9.348 37.906 C-8.89 36.713 -8.432 35.52 -7.961 34.291 C-7.496 33.039 -7.031 31.787 -6.566 30.535 C-5.641 28.096 -4.713 25.657 -3.785 23.219 C-3.114 21.443 -3.114 21.443 -2.43 19.632 C-0.415 14.409 1.743 9.249 3.934 4.098 C1.459 3.603 1.459 3.603 -1.066 3.098 C-1.148 3.853 -1.229 4.608 -1.312 5.387 C-2.376 10.621 -4.263 15.477 -6.129 20.473 C-6.506 21.495 -6.883 22.517 -7.271 23.57 C-8.198 26.081 -9.13 28.59 -10.066 31.098 C-10.726 31.098 -11.386 31.098 -12.066 31.098 C-13.605 27.188 -15.119 23.269 -16.629 19.348 C-17.067 18.236 -17.504 17.125 -17.955 15.98 C-18.364 14.911 -18.774 13.841 -19.195 12.738 C-19.769 11.261 -19.769 11.261 -20.354 9.755 C-21.09 7.011 -21.048 5.72 -20.066 3.098 C-17.513 4.374 -17.477 5.198 -16.559 7.844 C-16.294 8.598 -16.03 9.353 -15.758 10.131 C-15.488 10.924 -15.219 11.718 -14.941 12.535 C-14.526 13.719 -14.526 13.719 -14.102 14.928 C-12.066 20.808 -12.066 20.808 -12.066 23.098 C-11.406 23.098 -10.746 23.098 -10.066 23.098 C-9.963 22.279 -9.86 21.461 -9.754 20.617 C-8.942 16.463 -7.586 12.724 -6.066 8.785 C-5.793 8.057 -5.52 7.33 -5.238 6.58 C-2.786 0.132 -2.786 0.132 0 0 Z " fill="#E1E1E1" transform="translate(444.06640625,683.90234375)"/>
<path d="M0 0 C0.928 0.206 1.856 0.413 2.812 0.625 C5.028 6.04 5.028 6.04 3.938 9 C3.566 9.536 3.195 10.072 2.812 10.625 C1.822 10.625 0.832 10.625 -0.188 10.625 C-0.188 11.285 -0.188 11.945 -0.188 12.625 C0.803 12.625 1.793 12.625 2.812 12.625 C3.758 17.397 3.967 22.027 3.938 26.875 C3.936 27.647 3.934 28.419 3.932 29.214 C3.87 37.701 3.373 46.159 2.812 54.625 C-0.158 54.625 -3.127 54.625 -6.188 54.625 C-6.517 41.095 -6.848 27.565 -7.188 13.625 C-5.538 13.295 -3.887 12.965 -2.188 12.625 C-3.033 11.924 -3.879 11.222 -4.75 10.5 C-7.188 7.625 -7.188 7.625 -7.188 4.438 C-5.731 0.341 -4.303 -0.506 0 0 Z " fill="#F2F3F3" transform="translate(327.1875,671.375)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2 2.32 2 3 2 C3 3.32 3 4.64 3 6 C3.33 6.66 3.66 7.32 4 8 C3.01 9.485 3.01 9.485 2 11 C1.646 14.282 1.646 14.282 1.621 18.039 C1.604 18.721 1.587 19.402 1.57 20.104 C1.517 22.278 1.476 24.451 1.438 26.625 C1.394 28.806 1.347 30.987 1.295 33.167 C1.248 35.141 1.211 37.115 1.174 39.088 C1 42 1 42 0 43 C-2.655 43.455 -5.322 43.702 -8 44 C-9.61 40.781 -9.242 37.252 -9.316 33.711 C-9.337 32.893 -9.358 32.074 -9.379 31.231 C-9.445 28.612 -9.504 25.994 -9.562 23.375 C-9.606 21.602 -9.649 19.828 -9.693 18.055 C-9.801 13.703 -9.902 9.352 -10 5 C-10.99 5.495 -10.99 5.495 -12 6 C-12 5.01 -12 4.02 -12 3 C-14.64 2.67 -17.28 2.34 -20 2 C-20 1.67 -20 1.34 -20 1 C-19.31 1.012 -18.621 1.023 -17.91 1.035 C-11.842 1.093 -6.016 0.789 0 0 Z " fill="#BEBEBF" transform="translate(384,683)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 12.21 5 24.42 5 37 C3.35 37 1.7 37 0 37 C0 35.35 0 33.7 0 32 C-0.675 32.541 -0.675 32.541 -1.363 33.094 C-7.071 37.212 -12.043 38.026 -19 37 C-21.934 36.106 -22.876 35.168 -24.707 32.68 C-26.627 27.215 -26.574 21.879 -26.688 16.125 C-26.722 14.96 -26.756 13.794 -26.791 12.594 C-26.873 9.729 -26.942 6.865 -27 4 C-27.99 3.67 -28.98 3.34 -30 3 C-30 2.34 -30 1.68 -30 1 C-27.03 1 -24.06 1 -21 1 C-20.996 1.827 -20.992 2.655 -20.988 3.507 C-20.955 7.255 -20.884 11.002 -20.812 14.75 C-20.807 16.052 -20.801 17.354 -20.795 18.695 C-20.766 19.946 -20.737 21.196 -20.707 22.484 C-20.691 23.636 -20.676 24.789 -20.659 25.976 C-20.239 29.174 -20.239 29.174 -17.678 31.415 C-14.027 33.575 -12.04 33.028 -8 32 C-4.06 30.687 -2.857 28.648 -1 25 C-0.503 22.11 -0.503 22.11 -0.488 19.043 C-0.453 17.931 -0.417 16.819 -0.381 15.674 C-0.358 14.523 -0.336 13.373 -0.312 12.188 C-0.278 11.018 -0.244 9.848 -0.209 8.643 C-0.127 5.762 -0.057 2.881 0 0 Z " fill="#2F2E2F" transform="translate(33,686)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.02 0.797 5.04 1.594 5.06 2.415 C5.155 6.027 5.265 9.638 5.375 13.25 C5.406 14.504 5.437 15.759 5.469 17.051 C5.507 18.256 5.546 19.461 5.586 20.703 C5.617 21.813 5.649 22.924 5.681 24.067 C5.991 26.916 6.435 28.623 8 31 C10.258 32.129 11.738 32.264 14.25 32.375 C14.998 32.424 15.745 32.473 16.516 32.523 C19.416 31.912 21.555 30.925 23.773 28.935 C26.58 24.508 25.886 18.756 25.875 13.688 C25.896 12.47 25.916 11.252 25.938 9.998 C25.985 6.998 26.004 4 26 1 C27.65 1 29.3 1 31 1 C31 12.88 31 24.76 31 37 C29.35 37 27.7 37 26 37 C25.67 35.02 25.34 33.04 25 31 C24.67 31.99 24.34 32.98 24 34 C18.789 37.703 13.157 37.845 7 37 C2.81 34.691 1.437 32.464 0 28 C-0.227 24.862 -0.227 24.862 -0.195 21.387 C-0.189 20.14 -0.182 18.892 -0.176 17.607 C-0.159 16.314 -0.142 15.02 -0.125 13.688 C-0.116 12.375 -0.107 11.062 -0.098 9.709 C-0.074 6.472 -0.041 3.236 0 0 Z " fill="#2E2E2E" transform="translate(633,686)"/>
<path d="M0 0 C3.737 1.625 5.848 3.556 8 7 C9.099 10.297 9.114 12.73 9.098 16.199 C9.094 17.408 9.091 18.616 9.088 19.861 C9.08 21.124 9.071 22.387 9.062 23.688 C9.058 24.962 9.053 26.236 9.049 27.549 C9.037 30.699 9.021 33.85 9 37 C7.35 37 5.7 37 4 37 C3.985 36.239 3.971 35.477 3.956 34.693 C3.881 31.232 3.785 27.772 3.688 24.312 C3.665 23.114 3.642 21.916 3.619 20.682 C3.584 19.525 3.548 18.368 3.512 17.176 C3.486 16.113 3.459 15.05 3.432 13.954 C2.946 10.628 1.975 8.699 0 6 C-3.149 4.425 -5.52 4.652 -9 5 C-12.023 6.578 -13.216 7.221 -15 10 C-15.897 14.598 -15.885 19.138 -15.875 23.812 C-15.896 25.078 -15.916 26.343 -15.938 27.646 C-15.985 30.765 -16.004 33.881 -16 37 C-17.65 37 -19.3 37 -21 37 C-21 25.12 -21 13.24 -21 1 C-19.35 1 -17.7 1 -16 1 C-15.67 1.99 -15.34 2.98 -15 4 C-14.196 3.34 -13.391 2.68 -12.562 2 C-8.543 -0.677 -4.685 -0.719 0 0 Z " fill="#2E2E2F" transform="translate(853,686)"/>
<path d="M0 0 C0.946 0.162 0.946 0.162 1.91 0.328 C4.454 4.144 4.181 7.225 4.141 11.645 C4.14 12.863 4.14 12.863 4.139 14.106 C4.136 15.822 4.128 17.537 4.116 19.253 C4.098 21.884 4.095 24.515 4.096 27.146 C4.091 28.813 4.085 30.479 4.078 32.145 C4.077 32.934 4.076 33.724 4.075 34.537 C4.026 40.096 4.026 40.096 2.91 42.328 C-0.06 42.328 -3.03 42.328 -6.09 42.328 C-6.257 36.508 -6.418 30.687 -6.572 24.866 C-6.626 22.884 -6.681 20.902 -6.738 18.921 C-6.82 16.079 -6.895 13.237 -6.969 10.395 C-6.996 9.503 -7.023 8.611 -7.051 7.693 C-7.103 5.572 -7.102 3.45 -7.09 1.328 C-5.199 -0.563 -2.519 0.091 0 0 Z " fill="#DFDFDF" transform="translate(735.08984375,683.671875)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C7.678 6.325 10.299 12.67 12.812 19.062 C13.231 20.126 13.65 21.189 14.082 22.285 C15 25 15 25 15 28 C15.66 28 16.32 28 17 28 C17.127 27.289 17.254 26.578 17.385 25.845 C18.01 22.952 18.865 20.272 19.848 17.48 C20.384 15.948 20.384 15.948 20.932 14.385 C21.305 13.33 21.678 12.275 22.062 11.188 C22.44 10.113 22.817 9.039 23.205 7.932 C24.134 5.287 25.065 2.643 26 0 C27.98 0 29.96 0 32 0 C29.118 7.562 26.215 15.115 23.291 22.661 C22.496 24.717 21.705 26.775 20.92 28.835 C20.023 31.188 19.109 33.532 18.188 35.875 C17.935 36.55 17.682 37.226 17.422 37.922 C15.725 42.162 13.926 44.63 10 47 C7.543 47.391 7.543 47.391 5.188 47.25 C4.397 47.214 3.607 47.178 2.793 47.141 C2.201 47.094 1.61 47.048 1 47 C1.33 45.68 1.66 44.36 2 43 C3.093 42.938 4.186 42.876 5.312 42.812 C9.03 42.349 9.03 42.349 11 39.938 C13.878 31.484 8.705 22.795 5.438 15.125 C4.411 12.692 3.39 10.257 2.371 7.82 C1.917 6.751 1.462 5.681 0.994 4.579 C0 2 0 2 0 0 Z " fill="#191818" transform="translate(417,687)"/>
<path d="M0 0 C0.167 5.66 0.328 11.32 0.482 16.98 C0.536 18.906 0.591 20.833 0.648 22.759 C0.73 25.524 0.805 28.289 0.879 31.055 C0.906 31.919 0.933 32.784 0.961 33.675 C1.114 39.772 1.114 39.772 0 42 C-2.97 42 -5.94 42 -9 42 C-9.957 34.159 -10.155 26.435 -10.13 18.538 C-10.125 16.476 -10.13 14.414 -10.137 12.352 C-10.135 11.05 -10.134 9.748 -10.133 8.406 C-10.132 7.224 -10.131 6.041 -10.129 4.822 C-10 2 -10 2 -9 0 C-5.724 -1.287 -3.364 -0.741 0 0 Z " fill="#EFEFF0" transform="translate(1014,684)"/>
<path d="M0 0 C1.454 0.031 1.454 0.031 2.938 0.062 C4.477 3.638 5.991 7.224 7.5 10.812 C7.938 11.828 8.375 12.844 8.826 13.891 C9.235 14.87 9.645 15.85 10.066 16.859 C10.449 17.76 10.831 18.661 11.225 19.589 C11.938 22.062 11.938 22.062 10.938 26.062 C8.958 21.442 6.977 16.822 4.938 12.062 C5.783 14.146 6.629 16.229 7.5 18.375 C10.062 24.688 10.062 24.688 8.938 28.062 C5.535 20.852 2.546 13.597 -0.062 6.062 C-3.483 13.693 -6.781 21.372 -10.062 29.062 C-9.497 29.047 -8.932 29.031 -8.35 29.015 C-5.796 28.953 -3.242 28.914 -0.688 28.875 C0.202 28.85 1.091 28.825 2.008 28.799 C2.859 28.789 3.709 28.779 4.586 28.77 C5.371 28.754 6.157 28.738 6.966 28.722 C7.617 28.834 8.267 28.947 8.938 29.062 C9.597 30.053 10.257 31.043 10.938 32.062 C3.347 32.062 -4.243 32.062 -12.062 32.062 C-13.053 35.033 -14.042 38.003 -15.062 41.062 C-15.76 42.763 -16.481 44.455 -17.25 46.125 C-17.594 46.879 -17.938 47.633 -18.293 48.41 C-18.547 48.955 -18.801 49.501 -19.062 50.062 C-20.713 50.062 -22.363 50.062 -24.062 50.062 C-22.053 43.715 -19.705 37.588 -17.086 31.469 C-16.715 30.596 -16.345 29.723 -15.963 28.824 C-15.186 26.998 -14.408 25.173 -13.628 23.349 C-12.43 20.544 -11.238 17.737 -10.047 14.93 C-9.289 13.151 -8.532 11.372 -7.773 9.594 C-7.416 8.752 -7.059 7.911 -6.691 7.044 C-3.706 0.076 -3.706 0.076 0 0 Z " fill="#262425" transform="translate(228.0625,672.9375)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 10.56 8 21.12 8 32 C10.31 32 12.62 32 15 32 C15 32.99 15 33.98 15 35 C14.34 35 13.68 35 13 35 C13 34.34 13 33.68 13 33 C12.031 33.021 11.061 33.041 10.062 33.062 C7 33 7 33 6 32 C5.912 29.781 5.893 27.56 5.902 25.34 C5.904 24.675 5.905 24.011 5.907 23.326 C5.912 21.197 5.925 19.067 5.938 16.938 C5.943 15.497 5.947 14.056 5.951 12.615 C5.962 9.077 5.979 5.538 6 2 C4.693 3.081 4.693 3.081 4.886 5.684 C4.894 7.436 4.894 7.436 4.902 9.223 C4.907 11.113 4.907 11.113 4.912 13.041 C4.92 14.382 4.929 15.722 4.938 17.062 C4.943 18.407 4.947 19.751 4.951 21.096 C4.963 24.397 4.979 27.699 5 31 C4.67 31 4.34 31 4 31 C4 22.42 4 13.84 4 5 C3.34 5 2.68 5 2 5 C1.764 5.811 1.528 6.622 1.285 7.457 C-0.282 11.777 -2.364 15.661 -4.562 19.688 C-5.378 21.19 -6.192 22.694 -7.004 24.199 C-7.543 25.187 -7.543 25.187 -8.092 26.194 C-9.13 28.151 -9.13 28.151 -10 31 C-5.71 31 -1.42 31 3 31 C3 31.33 3 31.66 3 32 C2.169 32.061 1.337 32.121 0.48 32.184 C-0.606 32.267 -1.693 32.351 -2.812 32.438 C-3.891 32.519 -4.97 32.6 -6.082 32.684 C-9.06 32.902 -9.06 32.902 -12 34 C-12.193 35.075 -12.193 35.075 -12.391 36.172 C-12.994 38.972 -13.768 41.561 -14.75 44.25 C-15.054 45.121 -15.358 45.993 -15.672 46.891 C-17 49 -17 49 -19.641 49.797 C-20.419 49.864 -21.198 49.931 -22 50 C-21.404 44.226 -19.244 39.384 -16.938 34.125 C-16.537 33.202 -16.137 32.279 -15.724 31.328 C-11.019 20.595 -5.875 10.142 0 0 Z " fill="#323132" transform="translate(982,673)"/>
<path d="M0 0 C2.569 2.265 3.681 4.236 4.824 7.449 C5.13 8.297 5.437 9.144 5.752 10.018 C6.215 11.339 6.215 11.339 6.688 12.688 C7.008 13.58 7.328 14.473 7.658 15.393 C8.446 17.592 9.227 19.795 10 22 C9.696 21.114 9.696 21.114 9.385 20.209 C8.008 16.147 6.681 12.145 5.812 7.938 C5.654 7.183 5.495 6.429 5.332 5.652 C5.222 5.107 5.113 4.562 5 4 C7.605 6.605 8.341 9.179 9.625 12.625 C10.071 13.814 10.517 15.002 10.977 16.227 C11.314 17.142 11.652 18.057 12 19 C12.66 19 13.32 19 14 19 C17.3 27.58 20.6 36.16 24 45 C22.02 45 20.04 45 18 45 C16.996 42.543 15.996 40.085 15 37.625 C14.571 36.577 14.571 36.577 14.133 35.508 C12 30.227 12 30.227 12 28 C4.41 28 -3.18 28 -11 28 C-7.508 18.579 -3.954 9.238 0 0 Z M0 7 C1 9 1 9 1 9 Z M-1 9 C-1.671 10.623 -2.337 12.249 -3 13.875 C-3.371 14.78 -3.743 15.685 -4.125 16.617 C-5.115 18.89 -5.115 18.89 -5 21 C-1.7 21 1.6 21 5 21 C4.431 15.188 4.431 15.188 2 10 C1.01 9.67 0.02 9.34 -1 9 Z " fill="#464546" transform="translate(227,678)"/>
<path d="M0 0 C3.855 1.997 5.598 3.397 8 7 C8 7.99 8 8.98 8 10 C4.787 9.643 3.389 9.458 1.25 6.938 C-1.777 4.33 -3.914 4.427 -7.812 4.613 C-11.133 5.2 -12.779 6.51 -15 9 C-17.591 13.318 -17.561 18.106 -17 23 C-15.485 27.09 -14.014 30.099 -10 32 C-6.893 32.525 -4.097 32.623 -1 32 C1.75 29.583 1.75 29.583 4 27 C6.812 26.688 6.812 26.688 9 27 C5.855 33.74 5.855 33.74 3 36 C-1.98 37.66 -6.819 37.787 -12 37 C-16.74 34.532 -19.897 30.957 -22 26.062 C-23.703 20.286 -23.865 15.365 -21.375 9.812 C-16.633 1.24 -9.405 -1.082 0 0 Z " fill="#1B1B1B" transform="translate(279,686)"/>
<path d="M0 0 C2.774 1.765 5.319 3.533 7.75 5.75 C7.75 6.41 7.75 7.07 7.75 7.75 C8.41 7.75 9.07 7.75 9.75 7.75 C12.334 16.633 13.034 24.293 8.75 32.75 C5.795 37.566 2.039 40.007 -3.25 41.75 C-6.914 42.329 -10.545 42.614 -14.25 42.75 C-14.25 42.09 -14.25 41.43 -14.25 40.75 C-15.075 40.606 -15.9 40.461 -16.75 40.312 C-21.954 37.989 -25.188 34.528 -28.25 29.75 C-30.435 22.832 -29.278 16.059 -26.246 9.617 C-20.914 0.079 -10.326 -3.971 0 0 Z M-24.25 10.312 C-26.241 17.157 -26.576 24.81 -23.5 31.375 C-21.108 35.39 -18.691 37.27 -14.25 38.75 C-8.71 39.238 -3.851 39.493 1.25 37.125 C6.585 32.057 8.883 27.344 9.125 20.062 C9.053 14.069 8.009 10.061 3.75 5.75 C-0.915 2.109 -5.924 0.982 -11.785 1.234 C-17.326 2.393 -20.865 5.976 -24.25 10.312 Z " fill="#E3E4E4" transform="translate(814.25,684.25)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.33 9.9 5.66 19.8 6 30 C7.98 30.33 9.96 30.66 12 31 C12 31.66 12 32.32 12 33 C10.35 33 8.7 33 7 33 C7 37.95 7 42.9 7 48 C5.35 48 3.7 48 2 48 C2 43.05 2 38.1 2 33 C-2.95 33 -7.9 33 -13 33 C-12.34 31.68 -11.68 30.36 -11 29 C-10.845 28.197 -10.691 27.395 -10.531 26.568 C-9.932 23.673 -8.886 21.522 -7.496 18.918 C-7.003 17.987 -6.51 17.055 -6.002 16.096 C-5.486 15.136 -4.969 14.176 -4.438 13.188 C-3.657 11.719 -3.657 11.719 -2.861 10.221 C-1.58 7.811 -0.293 5.404 1 3 C1.66 3 2.32 3 3 3 C3.33 2.34 3.66 1.68 4 1 C2.68 0.67 1.36 0.34 0 0 Z M-1 13 C-1.495 14.98 -1.495 14.98 -2 17 C-1.34 16.67 -0.68 16.34 0 16 C-0.33 15.01 -0.66 14.02 -1 13 Z M-3 17 C-2 19 -2 19 -2 19 Z M-4 19 C-5.32 21.64 -6.64 24.28 -8 27 C-5.36 27 -2.72 27 0 27 C0 24.36 0 21.72 0 19 C-0.99 19 -1.98 19 -3 19 C-3.33 19 -3.66 19 -4 19 Z " fill="#3A393A" transform="translate(983,675)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C8.565 7.303 11.419 14.741 14.032 22.424 C15.026 25.988 15.026 25.988 17 29 C20.63 19.76 24.26 10.52 28 1 C29.65 1 31.3 1 33 1 C30.618 8.453 27.803 15.684 24.875 22.938 C24.192 24.64 24.192 24.64 23.496 26.377 C20.155 34.69 20.155 34.69 19 37 C17.35 37 15.7 37 14 37 C12.002 32.054 10.008 27.106 8.019 22.156 C7.341 20.472 6.662 18.788 5.981 17.104 C5.005 14.688 4.033 12.27 3.062 9.852 C2.756 9.096 2.45 8.341 2.135 7.563 C0 2.228 0 2.228 0 0 Z " fill="#1B1B1B" transform="translate(337,686)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 2.97 5 5.94 5 9 C8.63 9 12.26 9 16 9 C16 10.32 16 11.64 16 13 C12.37 13 8.74 13 5 13 C5.138 17.292 5.287 21.583 5.438 25.875 C5.477 27.098 5.516 28.322 5.557 29.582 C5.599 30.749 5.64 31.915 5.684 33.117 C5.72 34.196 5.757 35.275 5.795 36.386 C5.834 38.981 5.834 38.981 7 41 C9.126 41.467 9.126 41.467 11.562 41.625 C12.389 41.7 13.215 41.775 14.066 41.852 C14.704 41.901 15.343 41.95 16 42 C16 43.32 16 44.64 16 46 C14.251 46.081 12.5 46.139 10.75 46.188 C9.775 46.222 8.801 46.257 7.797 46.293 C4.572 45.955 3.292 45.255 1 43 C-0.099 39.703 -0.114 37.27 -0.098 33.801 C-0.093 31.988 -0.093 31.988 -0.088 30.139 C-0.08 28.876 -0.071 27.613 -0.062 26.312 C-0.056 24.401 -0.056 24.401 -0.049 22.451 C-0.037 19.301 -0.021 16.15 0 13 C-2.31 13 -4.62 13 -7 13 C-7 12.01 -7 11.02 -7 10 C-4.69 10 -2.38 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#181819" transform="translate(679,677)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 2.97 5 5.94 5 9 C8.3 9 11.6 9 15 9 C15 10.32 15 11.64 15 13 C11.7 13 8.4 13 5 13 C5.109 17.459 5.24 21.917 5.375 26.375 C5.406 27.646 5.437 28.917 5.469 30.227 C5.507 31.438 5.546 32.65 5.586 33.898 C5.617 35.019 5.649 36.14 5.681 37.294 C5.734 40.147 5.734 40.147 8 42 C11.583 42.167 11.583 42.167 15 42 C14.67 43.32 14.34 44.64 14 46 C12.418 46.109 10.834 46.186 9.25 46.25 C8.368 46.296 7.487 46.343 6.578 46.391 C3.101 45.864 2.145 44.758 0 42 C-0.454 38.884 -0.454 38.884 -0.391 35.266 C-0.378 33.97 -0.365 32.675 -0.352 31.34 C-0.318 29.977 -0.284 28.613 -0.25 27.25 C-0.23 25.871 -0.212 24.492 -0.195 23.113 C-0.148 19.741 -0.082 16.371 0 13 C-2.31 13 -4.62 13 -7 13 C-7 12.01 -7 11.02 -7 10 C-5.02 9.67 -3.04 9.34 -1 9 C-0.67 6.03 -0.34 3.06 0 0 Z " fill="#2D2D2D" transform="translate(751,677)"/>
<path d="M0 0 C8 0 8 0 10.25 1.766 C10.827 2.585 11.405 3.405 12 4.25 C12.99 5.621 12.99 5.621 14 7.02 C14.66 8.003 15.32 8.987 16 10 C16.99 11.46 16.99 11.46 18 12.949 C19.334 14.943 20.658 16.943 21.973 18.949 C25.155 23.739 28.436 28.485 32 33 C32.33 33 32.66 33 33 33 C33.33 22.77 33.66 12.54 34 2 C34.33 2 34.66 2 35 2 C35 14.21 35 26.42 35 39 C30.665 37.916 30.416 37.743 28.152 34.273 C27.635 33.495 27.117 32.717 26.584 31.916 C26.041 31.077 25.497 30.239 24.938 29.375 C23.782 27.645 22.624 25.916 21.465 24.188 C20.886 23.319 20.307 22.45 19.71 21.555 C17.191 17.791 14.625 14.066 12 10.375 C11.568 9.743 11.136 9.11 10.691 8.458 C7.809 4.441 7.166 4.033 2 3 C2 19.5 2 36 2 53 C4.31 53 6.62 53 9 53 C9.33 52.01 9.66 51.02 10 50 C10.33 50.66 10.66 51.32 11 52 C10.333 53.333 9.667 54.667 9 56 C9.33 56.66 9.66 57.32 10 58 C6.7 58 3.4 58 0 58 C0 38.86 0 19.72 0 0 Z M33 34 C34 36 34 36 34 36 Z " fill="#EEEFEF" transform="translate(581,670)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 3.63 5 7.26 5 11 C8.3 11 11.6 11 15 11 C15 11.99 15 12.98 15 14 C11.7 14 8.4 14 5 14 C5.11 18.271 5.24 22.542 5.375 26.812 C5.406 28.027 5.437 29.242 5.469 30.494 C5.507 31.658 5.546 32.821 5.586 34.02 C5.617 35.093 5.649 36.167 5.681 37.273 C5.816 40.216 5.816 40.216 8 43 C11.584 43.25 11.584 43.25 15 43 C15 44.32 15 45.64 15 47 C13.252 47.136 11.501 47.232 9.75 47.312 C8.775 47.371 7.801 47.429 6.797 47.488 C4 47 4 47 1.85 44.883 C-0.383 41.403 -0.466 39.49 -0.391 35.387 C-0.378 34.14 -0.365 32.892 -0.352 31.607 C-0.301 29.667 -0.301 29.667 -0.25 27.688 C-0.232 26.375 -0.214 25.062 -0.195 23.709 C-0.148 20.472 -0.082 17.236 0 14 C-1.98 14 -3.96 14 -6 14 C-6 12.68 -6 11.36 -6 10 C-4.02 10 -2.04 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#3A393B" transform="translate(51,676)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 3.3 5 6.6 5 10 C8.3 10 11.6 10 15 10 C15 10.99 15 11.98 15 13 C11.7 13 8.4 13 5 13 C5.11 17.271 5.24 21.542 5.375 25.812 C5.406 27.027 5.437 28.242 5.469 29.494 C5.507 30.658 5.546 31.821 5.586 33.02 C5.617 34.093 5.649 35.167 5.681 36.273 C5.816 39.216 5.816 39.216 8 42 C11.584 42.25 11.584 42.25 15 42 C15 43.32 15 44.64 15 46 C13.584 46.054 12.167 46.093 10.75 46.125 C9.961 46.148 9.172 46.171 8.359 46.195 C5.695 45.975 4.21 45.501 2 44 C-0.231 40.59 -0.233 37.717 -0.195 33.703 C-0.186 31.895 -0.186 31.895 -0.176 30.051 C-0.151 28.169 -0.151 28.169 -0.125 26.25 C-0.116 24.98 -0.107 23.711 -0.098 22.402 C-0.074 19.268 -0.041 16.134 0 13 C-1.98 13 -3.96 13 -6 13 C-6 11.68 -6 10.36 -6 9 C-4.02 9 -2.04 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#434244" transform="translate(299,677)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 3.3 4 6.6 4 10 C7.3 10 10.6 10 14 10 C14 10.99 14 11.98 14 13 C10.7 13 7.4 13 4 13 C4.11 17.271 4.24 21.542 4.375 25.812 C4.406 27.027 4.437 28.242 4.469 29.494 C4.507 30.658 4.546 31.821 4.586 33.02 C4.617 34.093 4.649 35.167 4.681 36.273 C4.816 39.216 4.816 39.216 7 42 C10.568 42.736 10.568 42.736 14 43 C14 43.99 14 44.98 14 46 C12.418 46.109 10.834 46.186 9.25 46.25 C8.368 46.296 7.487 46.343 6.578 46.391 C3.101 45.864 2.145 44.758 0 42 C-0.454 38.884 -0.454 38.884 -0.391 35.266 C-0.378 33.97 -0.365 32.675 -0.352 31.34 C-0.318 29.977 -0.284 28.613 -0.25 27.25 C-0.23 25.871 -0.212 24.492 -0.195 23.113 C-0.148 19.741 -0.082 16.371 0 13 C-2.31 13 -4.62 13 -7 13 C-7 12.01 -7 11.02 -7 10 C-4.69 10 -2.38 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#292829" transform="translate(397,677)"/>
<path d="M0 0 C0 16.5 0 33 0 50 C-1.32 50 -2.64 50 -4 50 C-4 35.81 -4 21.62 -4 7 C-5.98 8.32 -7.96 9.64 -10 11 C-10.66 11 -11.32 11 -12 11 C-12.495 8.525 -12.495 8.525 -13 6 C-11.591 4.989 -10.173 3.991 -8.75 3 C-7.567 2.165 -7.567 2.165 -6.359 1.312 C-4 0 -4 0 0 0 Z " fill="#212122" transform="translate(945,673)"/>
<path d="M0 0 C0 1.32 0 2.64 0 4 C-0.603 4.075 -1.207 4.15 -1.828 4.227 C-3.027 4.424 -3.027 4.424 -4.25 4.625 C-5.039 4.741 -5.828 4.857 -6.641 4.977 C-9.301 6.131 -10.728 7.414 -12.228 9.905 C-13.981 14.66 -13.688 19.866 -13.75 24.875 C-13.78 26.04 -13.809 27.206 -13.84 28.406 C-13.91 31.271 -13.963 34.135 -14 37 C-15.65 37 -17.3 37 -19 37 C-19 25.12 -19 13.24 -19 1 C-17.35 1 -15.7 1 -14 1 C-13.67 2.32 -13.34 3.64 -13 5 C-11.824 4.041 -11.824 4.041 -10.625 3.062 C-6.775 0.038 -4.851 -0.105 0 0 Z " fill="#373637" transform="translate(722,686)"/>
<path d="M0 0 C0.015 0.827 0.029 1.655 0.044 2.507 C0.119 6.255 0.215 10.002 0.312 13.75 C0.335 15.052 0.358 16.354 0.381 17.695 C0.416 18.946 0.452 20.196 0.488 21.484 C0.514 22.636 0.541 23.789 0.568 24.976 C1.037 28.256 1.67 29.679 4 32 C6.725 32.697 6.725 32.697 9.688 32.75 C11.178 32.835 11.178 32.835 12.699 32.922 C13.838 32.961 13.838 32.961 15 33 C15 31.68 15 30.36 15 29 C12.69 29.33 10.38 29.66 8 30 C5.009 26.702 4.64 24.542 4.707 20.141 C4.717 19.026 4.726 17.911 4.736 16.762 C4.761 15.603 4.787 14.444 4.812 13.25 C4.826 12.076 4.84 10.901 4.854 9.691 C4.889 6.794 4.938 3.897 5 1 C7.64 1 10.28 1 13 1 C13 1.33 13 1.66 13 2 C11.02 2 9.04 2 7 2 C7.158 2.562 7.317 3.123 7.48 3.702 C8.022 6.097 8.206 8.314 8.316 10.766 C8.379 12.1 8.379 12.1 8.443 13.461 C8.483 14.381 8.522 15.302 8.562 16.25 C8.606 17.186 8.649 18.122 8.693 19.086 C8.799 21.39 8.901 23.695 9 26 C11.31 26 13.62 26 16 26 C17.485 29.465 17.485 29.465 19 33 C15.943 35.682 14.056 36.393 10 36.312 C9.154 36.309 8.309 36.305 7.438 36.301 C3.856 35.859 1.824 34.927 -0.583 32.219 C-2.199 29.689 -2.428 28.236 -2.547 25.262 C-2.587 24.359 -2.627 23.456 -2.668 22.525 C-2.695 21.589 -2.722 20.652 -2.75 19.688 C-2.793 18.763 -2.835 17.838 -2.879 16.885 C-3.075 11.773 -2.917 7.034 -2 2 C-2.99 1.67 -3.98 1.34 -5 1 C-2 0 -2 0 0 0 Z " fill="#DCDBDB" transform="translate(51,690)"/>
<path d="M0 0 C3.806 2.108 6.401 4.956 8 9 C8.718 15.172 8.661 20.77 5 26 C1.069 29.609 -2.211 29.559 -7.367 29.422 C-10 29 -10 29 -13.062 26.75 C-17.022 21.13 -17.853 16.841 -17 10 C-15.716 6.266 -13.76 3.654 -10.75 1.062 C-7.09 -0.352 -3.826 -0.809 0 0 Z M-13 7 C-14.759 12.276 -14.648 17.218 -12.75 22.438 C-10.9 25.146 -10.119 26.109 -7 27 C-3.331 27.468 -1.515 27.281 1.75 25.5 C4.713 22.207 5.714 19.404 6 15 C5.528 11.463 4.604 8.207 3 5 C1.68 5.33 0.36 5.66 -1 6 C-1 5.01 -1 4.02 -1 3 C-6.481 2.57 -9.118 3.004 -13 7 Z " fill="#DEDEDE" transform="translate(810,690)"/>
<path d="M0 0 C5.529 2.061 10.188 5.122 15.125 8.312 C16.031 8.882 16.937 9.451 17.871 10.037 C18.741 10.598 19.611 11.16 20.508 11.738 C21.298 12.244 22.089 12.75 22.903 13.271 C26.061 15.875 27.782 18.406 28.341 22.495 C28.325 23.349 28.309 24.203 28.293 25.082 C28.283 26.013 28.274 26.945 28.264 27.904 C28.239 28.864 28.213 29.824 28.188 30.812 C28.167 32.281 28.167 32.281 28.146 33.779 C28.111 36.187 28.062 38.593 28 41 C24.129 40.458 20.872 39.664 17.312 38.062 C0.55 31.274 -21.057 31.753 -38 38 C-41.867 39.666 -45.557 41.574 -49.242 43.609 C-52 45 -52 45 -55 45 C-39.048 33.005 -18.585 28.837 1.043 31.504 C9.847 32.892 17.81 35.452 26 39 C25.977 35.769 25.9 32.542 25.812 29.312 C25.807 28.4 25.801 27.488 25.795 26.549 C25.644 21.933 25.526 19.679 22.625 15.938 C20.119 14.088 17.963 12.931 15 12 C15 11.34 15 10.68 15 10 C14.285 9.743 13.569 9.487 12.832 9.223 C9.813 7.919 7.367 6.339 4.688 4.438 C3.804 3.817 2.921 3.197 2.012 2.559 C1.348 2.044 0.684 1.53 0 1 C0 0.67 0 0.34 0 0 Z " fill="#91D4FB" transform="translate(681,310)"/>
<path d="M0 0 C1.236 0.017 1.236 0.017 2.496 0.035 C3.322 0.044 4.149 0.053 5 0.062 C5.638 0.074 6.276 0.086 6.934 0.098 C8.541 3.312 7.991 6.534 7.934 10.098 C8.572 10.086 9.21 10.074 9.867 10.062 C10.693 10.053 11.52 10.044 12.371 10.035 C13.195 10.024 14.019 10.012 14.867 10 C16.934 10.098 16.934 10.098 17.934 11.098 C18.059 14.598 18.059 14.598 17.934 18.098 C16.267 19.764 14.277 19.308 11.996 19.348 C11.1 19.373 10.204 19.399 9.281 19.426 C8.119 19.263 8.119 19.263 6.934 19.098 C6.274 18.108 5.614 17.118 4.934 16.098 C8.234 16.098 11.534 16.098 14.934 16.098 C14.934 15.108 14.934 14.118 14.934 13.098 C11.634 13.098 8.334 13.098 4.934 13.098 C4.934 9.468 4.934 5.838 4.934 2.098 C3.284 2.098 1.634 2.098 -0.066 2.098 C-0.066 5.398 -0.066 8.698 -0.066 12.098 C-2.046 12.098 -4.026 12.098 -6.066 12.098 C-6.066 13.418 -6.066 14.738 -6.066 16.098 C-4.746 16.428 -3.426 16.758 -2.066 17.098 C-2.066 19.408 -2.066 21.718 -2.066 24.098 C-2.396 23.108 -2.726 22.118 -3.066 21.098 C-7.473 19.178 -7.473 19.178 -12.066 20.098 C-12.066 17.128 -12.066 14.158 -12.066 11.098 C-14.706 11.098 -17.346 11.098 -20.066 11.098 C-20.066 10.768 -20.066 10.438 -20.066 10.098 C-17.096 10.098 -14.126 10.098 -11.066 10.098 C-11.066 11.418 -11.066 12.738 -11.066 14.098 C-10.406 14.098 -9.746 14.098 -9.066 14.098 C-8.736 12.778 -8.406 11.458 -8.066 10.098 C-6.416 9.768 -4.766 9.438 -3.066 9.098 C-3.087 7.798 -3.108 6.499 -3.129 5.16 C-3.15 3.806 -3.138 2.45 -3.066 1.098 C-2.066 0.098 -2.066 0.098 0 0 Z M-11.066 15.098 C-10.076 16.583 -10.076 16.583 -9.066 18.098 C-9.066 17.108 -9.066 16.118 -9.066 15.098 C-9.726 15.098 -10.386 15.098 -11.066 15.098 Z " fill="#E4E5E5" transform="translate(51.06640625,673.90234375)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 11.88 5 23.76 5 36 C3.35 36 1.7 36 0 36 C-0.33 34.02 -0.66 32.04 -1 30 C-1.33 30.99 -1.66 31.98 -2 33 C-3.485 33.495 -3.485 33.495 -5 34 C-5 33.34 -5 32.68 -5 32 C-5.66 31.67 -6.32 31.34 -7 31 C-5.542 30.174 -5.542 30.174 -4.055 29.332 C-1.659 27.336 -1.051 26.246 -0.411 23.15 C-0.184 19.652 -0.117 16.192 -0.125 12.688 C-0.104 11.47 -0.084 10.252 -0.062 8.998 C-0.015 5.998 0.004 3 0 0 Z " fill="#0E0E0F" transform="translate(659,687)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.292 0.777 3.585 1.554 3.886 2.355 C5.21 5.864 6.543 9.369 7.875 12.875 C8.335 14.098 8.795 15.322 9.27 16.582 C9.714 17.749 10.159 18.915 10.617 20.117 C11.026 21.196 11.434 22.275 11.855 23.386 C12.853 26.095 12.853 26.095 15 28 C15.636 29.999 16.19 32.025 16.688 34.062 C16.959 35.147 17.231 36.231 17.512 37.348 C17.673 38.223 17.834 39.098 18 40 C17 41 17 41 14.934 41.098 C14.11 41.086 13.286 41.074 12.438 41.062 C11.611 41.053 10.785 41.044 9.934 41.035 C9.296 41.024 8.657 41.012 8 41 C7.206 39.126 6.415 37.251 5.625 35.375 C4.964 33.809 4.964 33.809 4.289 32.211 C3.074 29.185 2.031 26.093 1 23 C-5.6 22.67 -12.2 22.34 -19 22 C-19 21.67 -19 21.34 -19 21 C-11.74 21 -4.48 21 3 21 C3.99 23.434 4.98 25.867 6 28.375 C6.313 29.14 6.626 29.905 6.949 30.693 C9 35.778 9 35.778 9 38 C11.475 37.505 11.475 37.505 14 37 C13.417 35.585 13.417 35.585 12.823 34.142 C11.377 30.631 9.938 27.117 8.501 23.603 C7.879 22.084 7.255 20.566 6.63 19.049 C5.729 16.864 4.835 14.676 3.941 12.488 C3.662 11.814 3.383 11.14 3.095 10.445 C1.69 6.987 0.575 3.693 0 0 Z " fill="#E7E7E7" transform="translate(236,685)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 12.21 5 24.42 5 37 C3.35 37 1.7 37 0 37 C0 35.35 0 33.7 0 32 C-0.66 32 -1.32 32 -2 32 C-2.625 30.188 -2.625 30.188 -3 28 C-2.01 26.515 -2.01 26.515 -1 25 C-0.632 22.289 -0.632 22.289 -0.586 19.238 C-0.547 18.12 -0.509 17.002 -0.469 15.85 C-0.438 14.682 -0.407 13.515 -0.375 12.312 C-0.336 11.134 -0.298 9.955 -0.258 8.74 C-0.163 5.827 -0.078 2.914 0 0 Z " fill="#242425" transform="translate(33,686)"/>
<path d="M0 0 C7 0 7 0 9 1 C9.168 5.787 9.328 10.574 9.482 15.362 C9.536 16.99 9.591 18.619 9.648 20.247 C9.73 22.588 9.805 24.929 9.879 27.27 C9.906 27.998 9.933 28.726 9.961 29.476 C10.054 32.63 10.007 34.978 9 38 C8.774 40.393 8.592 42.789 8.438 45.188 C8.354 46.46 8.27 47.732 8.184 49.043 C8.123 50.019 8.062 50.995 8 52 C7.67 52 7.34 52 7 52 C7 35.83 7 19.66 7 3 C5.35 3 3.7 3 2 3 C2 16.2 2 29.4 2 43 C1.34 43 0.68 43 0 43 C-0.66 41.35 -1.32 39.7 -2 38 C-1.67 38 -1.34 38 -1 38 C-0.67 25.46 -0.34 12.92 0 0 Z " fill="#FAFBFB" transform="translate(616,670)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C6.257 2.906 7 4.796 7 8 C6.67 8.33 6.34 8.66 6 9 C5.842 10.998 5.749 13.001 5.684 15.004 C5.642 16.219 5.6 17.434 5.557 18.686 C5.498 20.605 5.498 20.605 5.438 22.562 C5.394 23.846 5.351 25.129 5.307 26.451 C5.2 29.634 5.098 32.817 5 36 C3.35 36 1.7 36 0 36 C0 24.12 0 12.24 0 0 Z " fill="#313132" transform="translate(832,687)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 14.52 1 29.04 1 44 C-2.689 40.311 -2.906 38.288 -3.114 33.229 C-3.108 32.25 -3.103 31.271 -3.098 30.262 C-3.094 29.195 -3.091 28.128 -3.088 27.029 C-3.08 25.927 -3.071 24.824 -3.062 23.688 C-3.058 22.565 -3.053 21.443 -3.049 20.287 C-3.037 17.525 -3.021 14.762 -3 12 C-4.65 12.33 -6.3 12.66 -8 13 C-7.213 9.59 -6.129 6.311 -5 3 C-5.66 2.67 -6.32 2.34 -7 2 C-4.69 2 -2.38 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#F7F8F8" transform="translate(1023,681)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.015 0.863 1.029 1.726 1.044 2.615 C1.105 5.818 1.179 9.022 1.262 12.225 C1.296 13.611 1.324 14.997 1.346 16.384 C1.38 18.377 1.434 20.37 1.488 22.363 C1.514 23.563 1.541 24.762 1.568 25.997 C1.759 28.977 1.759 28.977 3.377 30.896 C5.691 32.47 7.424 32.295 10.188 32.188 C11.089 32.16 11.99 32.133 12.918 32.105 C13.605 32.071 14.292 32.036 15 32 C15.33 31.01 15.66 30.02 16 29 C13.69 29.33 11.38 29.66 9 30 C8.34 29.01 7.68 28.02 7 27 C10.3 26.67 13.6 26.34 17 26 C19 29 19 29 19 32 C19.66 32 20.32 32 21 32 C21 29.36 21 26.72 21 24 C21.33 24 21.66 24 22 24 C22.027 25.458 22.046 26.917 22.062 28.375 C22.074 29.187 22.086 29.999 22.098 30.836 C22 33 22 33 21 35 C18.171 35.451 15.409 35.797 12.562 36.062 C7.116 36.391 7.116 36.391 2 38 C1.897 37.278 1.794 36.556 1.688 35.812 C1.194 32.983 1.194 32.983 -0.5 31.031 C-2.793 26.398 -2.573 21.792 -2.688 16.688 C-2.722 15.661 -2.756 14.634 -2.791 13.576 C-2.873 11.051 -2.942 8.526 -3 6 C-3.99 6 -4.98 6 -6 6 C-6 5.34 -6 4.68 -6 4 C-6.66 3.34 -7.32 2.68 -8 2 C-5.69 2 -3.38 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#CDCDCD" transform="translate(750,690)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 12.21 5 24.42 5 37 C3.35 37 1.7 37 0 37 C0 24.79 0 12.58 0 0 Z " fill="#303030" transform="translate(1007,686)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 11.88 5 23.76 5 36 C3.35 36 1.7 36 0 36 C0 24.12 0 12.24 0 0 Z " fill="#222223" transform="translate(774,687)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 11.88 5 23.76 5 36 C3.35 36 1.7 36 0 36 C0 24.12 0 12.24 0 0 Z " fill="#292929" transform="translate(323,687)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 11.88 5 23.76 5 36 C3.35 36 1.7 36 0 36 C0 24.12 0 12.24 0 0 Z " fill="#282829" transform="translate(731,687)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 9.9 1 19.8 1 30 C2.98 30 4.96 30 7 30 C7.33 32.31 7.66 34.62 8 37 C6.02 37.66 4.04 38.32 2 39 C1.909 40.499 1.909 40.499 1.816 42.027 C1.733 43.318 1.649 44.608 1.562 45.938 C1.481 47.225 1.4 48.513 1.316 49.84 C1 53 1 53 0 54 C-1.325 54.257 -2.661 54.458 -4 54.625 C-4.722 54.72 -5.444 54.816 -6.188 54.914 C-6.786 54.942 -7.384 54.971 -8 55 C-9 54 -9 54 -9.098 50.621 C-9.091 49.227 -9.079 47.832 -9.062 46.438 C-9.058 45.727 -9.053 45.016 -9.049 44.283 C-9.037 42.522 -9.019 40.761 -9 39 C-14.94 39.495 -14.94 39.495 -21 40 C-21 39.34 -21 38.68 -21 38 C-16.71 38 -12.42 38 -8 38 C-7.67 41.96 -7.34 45.92 -7 50 C-5.35 50.33 -3.7 50.66 -2 51 C-2 46.05 -2 41.1 -2 36 C0.31 36 2.62 36 5 36 C5 35.01 5 34.02 5 33 C2.69 33 0.38 33 -2 33 C-1.34 32.34 -0.68 31.68 0 31 C0.279 27.792 0.279 27.792 0.195 24.023 C0.191 22.996 0.191 22.996 0.187 21.947 C0.176 19.756 0.15 17.566 0.125 15.375 C0.115 13.891 0.106 12.406 0.098 10.922 C0.076 7.281 0.041 3.641 0 0 Z " fill="#D8D8D7" transform="translate(992,672)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 15.84 1 31.68 1 48 C-2.927 46.036 -3.815 45.287 -6.164 41.867 C-6.729 41.062 -7.293 40.256 -7.875 39.426 C-8.452 38.584 -9.03 37.742 -9.625 36.875 C-10.778 35.202 -11.936 33.532 -13.102 31.867 C-13.61 31.126 -14.119 30.384 -14.643 29.621 C-15.091 29.086 -15.539 28.551 -16 28 C-16.66 28 -17.32 28 -18 28 C-18.392 27.01 -18.784 26.02 -19.188 25 C-21.011 20.713 -23.447 16.885 -26 13 C-25.01 13 -24.02 13 -23 13 C-21.062 15.52 -21.062 15.52 -19 18.812 C-15.925 23.756 -15.925 23.756 -12 28 C-9.075 30.831 -6.418 33.719 -4 37 C-4 37.66 -4 38.32 -4 39 C-3.01 39 -2.02 39 -1 39 C-0.67 26.13 -0.34 13.26 0 0 Z " fill="#403F41" transform="translate(620,674)"/>
<path d="M0 0 C2.541 2.248 3.675 4.201 4.824 7.379 C5.13 8.217 5.437 9.056 5.752 9.92 C6.061 10.792 6.369 11.664 6.688 12.562 C7.008 13.438 7.328 14.314 7.658 15.217 C10 21.703 10 21.703 10 24 C3.73 24 -2.54 24 -9 24 C-7.071 18.213 -5.11 12.57 -2.812 6.938 C-2.404 5.925 -2.404 5.925 -1.986 4.893 C-1.327 3.261 -0.664 1.63 0 0 Z M0 7 C1 9 1 9 1 9 Z M-1 9 C-1.671 10.623 -2.337 12.249 -3 13.875 C-3.371 14.78 -3.743 15.685 -4.125 16.617 C-5.115 18.89 -5.115 18.89 -5 21 C-1.7 21 1.6 21 5 21 C4.431 15.188 4.431 15.188 2 10 C1.01 9.67 0.02 9.34 -1 9 Z " fill="#E4E4E5" transform="translate(227,678)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.041 2.176 1.041 2.176 0.062 3.375 C-2.78 7.554 -2.805 12.076 -3 17 C-2.34 17.33 -1.68 17.66 -1 18 C-0.546 18.949 -0.092 19.898 0.375 20.875 C2.394 24.786 2.394 24.786 6 27 C9.107 27.525 11.903 27.623 15 27 C17.75 24.583 17.75 24.583 20 22 C22.812 21.688 22.812 21.688 25 22 C21.855 28.74 21.855 28.74 19 31 C15.792 32.069 13.37 32.167 10 32.188 C8.948 32.202 7.896 32.216 6.812 32.23 C4 32 4 32 1 30 C-0.5 27.75 -0.5 27.75 -2 25 C-2.557 24.092 -3.114 23.185 -3.688 22.25 C-5.991 16.546 -5.674 11.323 -3.625 5.562 C-2 2 -2 2 0 0 Z " fill="#2E2D2E" transform="translate(263,691)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C6.018 3.053 6.139 5.205 6.176 8.402 C6.2 9.457 6.223 10.513 6.248 11.6 C6.291 13.821 6.322 16.042 6.342 18.264 C6.37 19.32 6.397 20.376 6.426 21.465 C6.44 22.43 6.455 23.396 6.47 24.39 C7.149 27.735 8.31 28.952 11 31 C14.557 31.325 14.557 31.325 18 30 C21.086 27.072 21.912 25.456 22.168 21.215 C22.139 19.14 22.085 17.066 22.008 14.992 C21.997 11.014 22.835 7.786 24 4 C24.33 4 24.66 4 25 4 C25.204 7.874 25.328 11.747 25.438 15.625 C25.496 16.719 25.555 17.814 25.615 18.941 C25.783 26.85 25.783 26.85 23.575 30.093 C21.015 32.421 18.827 33.772 15.488 34.691 C7.881 34.241 7.881 34.241 5 31 C3.901 27.703 3.886 25.27 3.902 21.801 C3.907 19.988 3.907 19.988 3.912 18.139 C3.92 16.876 3.929 15.613 3.938 14.312 C3.944 12.401 3.944 12.401 3.951 10.451 C3.963 7.301 3.979 4.15 4 1 C2.68 0.67 1.36 0.34 0 0 Z " fill="#F4F4F4" transform="translate(634,685)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C-0.98 6 -2.96 6 -5 6 C-5 7.32 -5 8.64 -5 10 C-2.69 10 -0.38 10 2 10 C2.015 10.863 2.029 11.726 2.044 12.615 C2.105 15.818 2.179 19.022 2.262 22.225 C2.296 23.611 2.324 24.997 2.346 26.384 C2.38 28.377 2.434 30.37 2.488 32.363 C2.514 33.563 2.541 34.762 2.568 35.997 C2.757 38.965 2.757 38.965 4.333 40.841 C6.713 42.496 9.233 43.201 12 44 C12 44.33 12 44.66 12 45 C4.638 45.491 4.638 45.491 1.562 43.125 C-0.959 39.696 -1.12 37.778 -1.098 33.555 C-1.093 31.94 -1.093 31.94 -1.088 30.293 C-1.08 29.165 -1.071 28.037 -1.062 26.875 C-1.058 25.739 -1.053 24.604 -1.049 23.434 C-1.037 20.622 -1.021 17.811 -1 15 C-1.99 14.67 -2.98 14.34 -4 14 C-4.99 13.67 -5.98 13.34 -7 13 C-7 10.36 -7 7.72 -7 5 C-4.69 5 -2.38 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#DADADB" transform="translate(395,680)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9.921 4.687 10.146 9.228 10.133 13.988 C10.134 14.732 10.135 15.476 10.136 16.242 C10.136 17.803 10.135 19.363 10.13 20.924 C10.125 23.322 10.13 25.721 10.137 28.119 C10.136 29.638 10.135 31.157 10.133 32.676 C10.135 33.396 10.137 34.115 10.139 34.857 C10.115 39.885 10.115 39.885 9 41 C6.329 41.141 3.676 41.042 1 41 C1.66 40.34 2.32 39.68 3 39 C4.32 39 5.64 39 7 39 C7 27.12 7 15.24 7 3 C5.35 3 3.7 3 2 3 C1.67 5.31 1.34 7.62 1 10 C0.34 9.67 -0.32 9.34 -1 9 C-0.67 6.03 -0.34 3.06 0 0 Z " fill="#E7E8E8" transform="translate(657,684)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 2.97 5 5.94 5 9 C8.3 9 11.6 9 15 9 C15 9.33 15 9.66 15 10 C13.928 10.124 12.855 10.247 11.75 10.375 C7.832 10.839 7.832 10.839 4 13 C3.67 21.58 3.34 30.16 3 39 C2.34 39 1.68 39 1 39 C1 30.42 1 21.84 1 13 C-1.64 13 -4.28 13 -7 13 C-7 12.01 -7 11.02 -7 10 C-5.02 9.67 -3.04 9.34 -1 9 C-0.67 6.03 -0.34 3.06 0 0 Z " fill="#454647" transform="translate(751,677)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 12.21 4.66 24.42 5 37 C3.35 37 1.7 37 0 37 C0 24.79 0 12.58 0 0 Z " fill="#222122" transform="translate(378,686)"/>
<path d="M0 0 C5.75 1.875 5.75 1.875 8 3 C6.363 8.865 4.316 12.571 -0.938 15.812 C-3.918 16.968 -5.848 17.278 -9 17 C-9.33 17.66 -9.66 18.32 -10 19 C-10 18.34 -10 17.68 -10 17 C-10.99 16.938 -11.98 16.876 -13 16.812 C-17.609 16.172 -21.447 13.962 -25 11 C-25 10.34 -25 9.68 -25 9 C-22.182 9.391 -20.404 9.659 -18.199 11.52 C-15.348 13.439 -13.475 13.401 -10.062 13.375 C-9.043 13.383 -8.023 13.39 -6.973 13.398 C-3.595 12.946 -1.73 12.009 1 10 C2.808 7.434 2.808 7.434 4 5 C-0.126 4.552 -0.126 4.552 -2.75 7.5 C-3.864 8.737 -3.864 8.737 -5 10 C-5.66 9.01 -6.32 8.02 -7 7 C-6.381 6.938 -5.762 6.876 -5.125 6.812 C-2.732 6.178 -2.732 6.178 -1.812 3.5 C-1.544 2.675 -1.276 1.85 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D6D5D6" transform="translate(283,709)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 10.56 8 21.12 8 32 C10.31 32 12.62 32 15 32 C15 32.99 15 33.98 15 35 C14.34 35 13.68 35 13 35 C13 34.34 13 33.68 13 33 C12.031 33.021 11.061 33.041 10.062 33.062 C7 33 7 33 6 32 C5.912 29.781 5.893 27.56 5.902 25.34 C5.904 24.675 5.905 24.011 5.907 23.326 C5.912 21.197 5.925 19.067 5.938 16.938 C5.943 15.497 5.947 14.056 5.951 12.615 C5.962 9.077 5.979 5.538 6 2 C4.693 3.081 4.693 3.081 4.886 5.684 C4.892 6.852 4.897 8.019 4.902 9.223 C4.906 10.483 4.909 11.743 4.912 13.041 C4.92 14.382 4.929 15.722 4.938 17.062 C4.943 18.407 4.947 19.751 4.951 21.096 C4.963 24.397 4.979 27.699 5 31 C4.67 31 4.34 31 4 31 C4 22.42 4 13.84 4 5 C3.34 5 2.68 5 2 5 C1.67 5.99 1.34 6.98 1 8 C0.01 8 -0.98 8 -2 8 C-2.99 9.98 -3.98 11.96 -5 14 C-6 11 -6 11 -4.785 8.301 C-4.217 7.315 -3.648 6.329 -3.062 5.312 C-2.497 4.319 -1.931 3.325 -1.348 2.301 C-0.903 1.542 -0.458 0.782 0 0 Z " fill="#121212" transform="translate(982,673)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C7.701 4.941 5.959 8.573 4.035 13.145 C3.723 13.914 3.411 14.684 3.089 15.477 C2.092 17.927 1.078 20.37 0.062 22.812 C-0.618 24.475 -1.297 26.139 -1.975 27.803 C-3.636 31.874 -5.314 35.939 -7 40 C-13.055 40.977 -13.055 40.977 -15 41 C-15.66 40.34 -16.32 39.68 -17 39 C-16.67 38.01 -16.34 37.02 -16 36 C-15.01 35.505 -15.01 35.505 -14 35 C-14 35.66 -14 36.32 -14 37 C-12.35 37 -10.7 37 -9 37 C-8.622 35.805 -8.245 34.61 -7.855 33.379 C-5.654 26.775 -2.963 20.371 -0.312 13.938 C0.204 12.678 0.72 11.418 1.236 10.158 C2.488 7.104 3.743 4.052 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DEDEDE" transform="translate(365,686)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.01 0.33 3.02 0.66 2 1 C2 13.21 2 25.42 2 38 C3.65 38 5.3 38 7 38 C7 25.79 7 13.58 7 1 C7.33 1 7.66 1 8 1 C8 14.2 8 27.4 8 41 C5.36 41.33 2.72 41.66 0 42 C0 28.14 0 14.28 0 0 Z " fill="#F9F9F9" transform="translate(376,685)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 8.58 1 17.16 1 26 C-3.29 26 -7.58 26 -12 26 C-11.168 21.839 -10.423 19.528 -8.496 15.918 C-8.003 14.987 -7.51 14.055 -7.002 13.096 C-6.486 12.136 -5.969 11.176 -5.438 10.188 C-4.917 9.208 -4.397 8.229 -3.861 7.221 C-2.58 4.811 -1.293 2.404 0 0 Z M-2 10 C-2.495 11.98 -2.495 11.98 -3 14 C-2.34 13.67 -1.68 13.34 -1 13 C-1.33 12.01 -1.66 11.02 -2 10 Z M-4 14 C-3 16 -3 16 -3 16 Z M-5 16 C-6.32 18.64 -7.64 21.28 -9 24 C-6.36 24 -3.72 24 -1 24 C-1 21.36 -1 18.72 -1 16 C-1.99 16 -2.98 16 -4 16 C-4.33 16 -4.66 16 -5 16 Z " fill="#E4E4E3" transform="translate(984,678)"/>
<path d="M0 0 C0.875 0.047 1.749 0.094 2.65 0.143 C4.067 0.215 4.067 0.215 5.511 0.289 C6.505 0.345 7.498 0.401 8.521 0.459 C9.518 0.511 10.515 0.564 11.543 0.617 C14.015 0.748 16.487 0.883 18.959 1.022 C18.959 2.342 18.959 3.662 18.959 5.022 C17.639 5.022 16.319 5.022 14.959 5.022 C14.464 4.032 14.464 4.032 13.959 3.022 C9.339 3.022 4.719 3.022 -0.041 3.022 C-0.465 4.237 -0.889 5.453 -1.326 6.705 C-1.917 8.291 -2.51 9.875 -3.104 11.459 C-3.379 12.262 -3.654 13.064 -3.938 13.891 C-4.807 16.154 -5.609 18.063 -7.041 20.022 C-10.635 21.022 -10.635 21.022 -14.041 21.022 C-15.38 21.331 -16.718 21.651 -18.041 22.022 C-18.041 17.26 -16.278 13.176 -14.041 9.022 C-13.381 9.022 -12.721 9.022 -12.041 9.022 C-12.916 14.772 -12.916 14.772 -14.041 17.022 C-11.626 17.113 -11.626 17.113 -9.041 16.022 C-7.69 13.462 -6.715 11.228 -5.791 8.522 C-2.706 0.029 -2.706 0.029 0 0 Z " fill="#D8D8D8" transform="translate(218.041259765625,705.978271484375)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C9.432 9.079 12.744 18.456 16 28 C16.99 28 17.98 28 19 28 C19.66 27.34 20.32 26.68 21 26 C20.493 29.719 19.884 32.735 18 36 C17.01 36 16.02 36 15 36 C14.713 35.244 14.426 34.488 14.13 33.708 C12.819 30.263 11.503 26.819 10.188 23.375 C9.51 21.59 9.51 21.59 8.818 19.77 C8.377 18.616 7.935 17.462 7.48 16.273 C7.077 15.216 6.674 14.158 6.259 13.068 C4.982 9.957 3.539 6.989 2 4 C1.333 2.667 0.667 1.333 0 0 Z " fill="#333334" transform="translate(337,686)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 11.23 3 21.46 3 32 C0.69 32.66 -1.62 33.32 -4 34 C-6.478 32.145 -6.969 31.206 -7.432 28.089 C-7.459 26.932 -7.485 25.774 -7.512 24.582 C-7.547 23.328 -7.583 22.075 -7.619 20.783 C-7.642 19.473 -7.664 18.163 -7.688 16.812 C-7.721 15.477 -7.755 14.142 -7.791 12.807 C-7.874 9.538 -7.943 6.269 -8 3 C-7.34 3 -6.68 3 -6 3 C-5.67 8.94 -5.34 14.88 -5 21 C-4.67 21 -4.34 21 -4 21 C-4 23.97 -4 26.94 -4 30 C-2.35 30 -0.7 30 1 30 C0.67 20.1 0.34 10.2 0 0 Z " fill="#EEEEEE" transform="translate(861,693)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C9.511 13.833 10.282 27.128 9 41 C6.03 41 3.06 41 0 41 C0 40.01 0 39.02 0 38 C-1.98 38.66 -3.96 39.32 -6 40 C-6 39.01 -6 38.02 -6 37 C-0.743 33 -0.743 33 2 33 C2 34.65 2 36.3 2 38 C3.65 38 5.3 38 7 38 C7 25.79 7 13.58 7 1 C5.02 1.33 3.04 1.66 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#EEEEEE" transform="translate(31,685)"/>
<path d="M0 0 C3.389 2.137 4.726 3.177 6 7 C6.122 9.866 6.185 12.697 6.188 15.562 C6.2 16.343 6.212 17.124 6.225 17.928 C6.23 19.953 6.122 21.978 6 24 C5.34 24.66 4.68 25.32 4 26 C4 19.4 4 12.8 4 6 C-0.283 3.858 -2.305 3.497 -7 4 C-10.204 6.361 -11.268 8.178 -12 12 C-12.144 13.732 -12.246 15.467 -12.316 17.203 C-12.358 18.183 -12.4 19.163 -12.443 20.172 C-12.483 21.188 -12.522 22.203 -12.562 23.25 C-12.606 24.281 -12.649 25.312 -12.693 26.375 C-12.799 28.917 -12.901 31.458 -13 34 C-13.33 34 -13.66 34 -14 34 C-14.204 29.459 -14.328 24.919 -14.438 20.375 C-14.496 19.09 -14.555 17.805 -14.615 16.48 C-14.784 7.188 -14.784 7.188 -12.538 3.785 C-8.561 0.229 -5.43 -1.005 0 0 Z " fill="#EFEFEF" transform="translate(851,690)"/>
<path d="M0 0 C3 1 3 1 3.891 2.642 C4.127 3.337 4.363 4.031 4.605 4.746 C4.867 5.501 5.128 6.256 5.396 7.033 C5.658 7.827 5.919 8.62 6.188 9.438 C6.46 10.227 6.733 11.017 7.014 11.83 C9 17.67 9 17.67 9 20 C9.66 20 10.32 20 11 20 C11.082 19.175 11.165 18.35 11.25 17.5 C12.441 11.942 14.19 7.207 19 4 C16.694 12.138 13.896 20.057 11 28 C10.34 28 9.68 28 9 28 C7.489 24.087 5.993 20.169 4.5 16.25 C4.071 15.139 3.641 14.028 3.199 12.883 C2.793 11.813 2.387 10.743 1.969 9.641 C1.592 8.656 1.215 7.672 0.826 6.657 C0 4 0 4 0 0 Z " fill="#E5E5E5" transform="translate(423,687)"/>
<path d="M0 0 C0.971 -0.014 1.941 -0.028 2.941 -0.043 C5.438 0.188 5.438 0.188 7.438 2.188 C6.713 2.225 5.989 2.262 5.242 2.301 C-1.678 2.771 -6.836 3.309 -11.812 8.562 C-15.2 13.643 -16.326 18.07 -15.562 24.188 C-14.558 27.672 -13.186 30.941 -11.562 34.188 C-13.212 33.857 -14.863 33.528 -16.562 33.188 C-19.358 26.65 -20.354 21.156 -18.562 14.188 C-16.33 8.71 -13.653 4.403 -8.562 1.188 C-5.48 0.16 -3.231 0.022 0 0 Z " fill="#EAEAEA" transform="translate(272.5625,683.8125)"/>
<path d="M0 0 C1.607 3.215 1.057 6.436 1 10 C1.638 9.988 2.276 9.977 2.934 9.965 C3.76 9.956 4.586 9.947 5.438 9.938 C6.261 9.926 7.085 9.914 7.934 9.902 C10 10 10 10 11 11 C11.375 14.375 11.375 14.375 11 18 C8.5 19.938 8.5 19.938 6 21 C6 20.34 6 19.68 6 19 C4.02 19 2.04 19 0 19 C0 18.67 0 18.34 0 18 C2.64 18 5.28 18 8 18 C8 16.35 8 14.7 8 13 C4.7 13 1.4 13 -2 13 C-2 9.7 -2 6.4 -2 3 C-3.65 3 -5.3 3 -7 3 C-7.33 5.64 -7.66 8.28 -8 11 C-10.31 11 -12.62 11 -15 11 C-15 10.67 -15 10.34 -15 10 C-13.02 10 -11.04 10 -9 10 C-9 7.03 -9 4.06 -9 1 C-6.043 -0.478 -3.258 -0.06 0 0 Z " fill="#E1E3E5" transform="translate(306,674)"/>
<path d="M0 0 C2.433 2.433 3.307 4.721 4.688 7.875 C11.418 22.634 20.112 35.887 32 47 C27.923 47 26.219 44.948 23.375 42.312 C22.844 41.823 22.314 41.333 21.767 40.829 C16.19 35.497 12.058 29.547 8 23 C7.194 21.706 7.194 21.706 6.371 20.387 C3.913 16.219 2.565 12.863 2 8 C1.649 7.299 1.299 6.598 0.938 5.875 C0 4 0 4 0 0 Z " fill="#54C2FD" transform="translate(209,479)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.02 0.726 2.04 1.452 2.06 2.2 C2.156 5.508 2.265 8.817 2.375 12.125 C2.421 13.838 2.421 13.838 2.469 15.586 C2.507 16.695 2.546 17.803 2.586 18.945 C2.617 19.961 2.649 20.977 2.681 22.024 C2.955 25.575 2.955 25.575 5 31 C12.425 31.99 12.425 31.99 20 33 C20 33.99 20 34.98 20 36 C16.683 36.711 14.238 37.043 10.875 36.438 C7.725 35.958 6.616 36.29 4 38 C3.505 36.515 3.505 36.515 3 35 C3.99 34.505 3.99 34.505 5 34 C4.361 33.484 3.721 32.969 3.062 32.438 C-1.589 26.94 -0.118 17.894 -0.062 11.062 C-0.058 9.997 -0.053 8.932 -0.049 7.834 C-0.037 5.223 -0.021 2.611 0 0 Z " fill="#E3E3E3" transform="translate(5,690)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C-0.98 10 -2.96 10 -5 10 C-5 11.32 -5 12.64 -5 14 C-3.02 14 -1.04 14 1 14 C1.014 15.172 1.014 15.172 1.028 16.367 C1.082 19.933 1.166 23.497 1.25 27.062 C1.264 28.292 1.278 29.521 1.293 30.787 C1.479 37.681 1.839 42.399 6 48 C3.411 47.643 2.231 47.24 0.395 45.333 C-1.369 42.383 -1.428 40.348 -1.512 36.922 C-1.547 35.762 -1.583 34.602 -1.619 33.406 C-1.642 32.2 -1.664 30.993 -1.688 29.75 C-1.722 28.528 -1.756 27.306 -1.791 26.047 C-1.873 23.031 -1.943 20.016 -2 17 C-3.65 17 -5.3 17 -7 17 C-8.125 11.25 -8.125 11.25 -7 9 C-4.69 9 -2.38 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#EDEDED" transform="translate(298,676)"/>
<path d="M0 0 C2.962 2.788 5.488 5.577 7.875 8.875 C16.032 19.882 26.675 27.436 40 31 C44.4 31.56 48.756 31.693 53.188 31.75 C54.902 31.794 54.902 31.794 56.65 31.84 C59.434 31.91 62.216 31.963 65 32 C65 32.33 65 32.66 65 33 C50.96 36.354 36.085 34.038 23.723 26.781 C14.754 20.913 3.647 12.4 0.258 1.645 C0.173 1.102 0.088 0.559 0 0 Z " fill="#22A2FF" transform="translate(636,519)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 1.98 1.66 3.96 2 6 C4.64 6 7.28 6 10 6 C11.257 8.906 12 10.796 12 14 C10.756 14.535 9.504 15.052 8.25 15.562 C7.554 15.853 6.858 16.143 6.141 16.441 C3.7 17.078 2.36 16.802 0 16 C-0.33 16.66 -0.66 17.32 -1 18 C-2 17 -3 16 -4 15 C-4.701 14.423 -5.403 13.845 -6.125 13.25 C-8.605 10.273 -8.708 7.798 -9 4 C-8.67 4.99 -8.34 5.98 -8 7 C-7.34 7 -6.68 7 -6 7 C-5.567 7.959 -5.567 7.959 -5.125 8.938 C-4.172 11.152 -4.172 11.152 -2 12 C-0.129 12.228 1.747 12.41 3.625 12.562 C4.628 12.646 5.631 12.73 6.664 12.816 C7.82 12.907 7.82 12.907 9 13 C9 11.68 9 10.36 9 9 C8.092 9.206 7.185 9.413 6.25 9.625 C3 10 3 10 0.688 9.438 C-1 8 -1 8 -1.875 5 C-1.916 4.01 -1.957 3.02 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#F0F0F1" transform="translate(686,710)"/>
<path d="M0 0 C4 1 4 1 6.312 3.875 C6.869 4.906 7.426 5.938 8 7 C7.67 7.99 7.34 8.98 7 10 C6.34 8.35 5.68 6.7 5 5 C4.34 5 3.68 5 3 5 C3 18.86 3 32.72 3 47 C2.01 47 1.02 47 0 47 C0 31.49 0 15.98 0 0 Z " fill="#545355" transform="translate(585,675)"/>
<path d="M0 0 C1.279 -0.062 2.558 -0.124 3.875 -0.188 C4.954 -0.24 4.954 -0.24 6.055 -0.293 C6.697 -0.196 7.339 -0.1 8 0 C10.695 4.042 10.232 6.205 10 11 C9.67 11.33 9.34 11.66 9 12 C8.842 13.925 8.749 15.855 8.684 17.785 C8.642 18.955 8.6 20.125 8.557 21.33 C8.517 22.562 8.478 23.793 8.438 25.062 C8.394 26.298 8.351 27.534 8.307 28.807 C8.2 31.871 8.098 34.935 8 38 C7.67 38 7.34 38 7 38 C7 26.45 7 14.9 7 3 C5.35 3 3.7 3 2 3 C2 13.89 2 24.78 2 36 C1.67 36 1.34 36 1 36 C1 25.11 1 14.22 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#F3F4F4" transform="translate(729,684)"/>
<path d="M0 0 C4.409 0.504 6.826 1.911 10 5 C11.312 7.75 11.312 7.75 12 10 C12.66 10 13.32 10 14 10 C14.452 17.539 14.581 23.718 10 30 C7.615 32.462 6.672 32.954 3.25 33.188 C2.136 33.095 2.136 33.095 1 33 C1.763 32.443 2.526 31.886 3.312 31.312 C6.97 28.165 9.291 24.82 10 20 C10.325 14.172 10.325 14.172 8 9 C7.484 8.484 6.969 7.969 6.438 7.438 C5 6 5 6 4 3 C2.003 1.315 2.003 1.315 0 0 Z " fill="#2F2F30" transform="translate(809,688)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 8.58 1 17.16 1 26 C1.66 26 2.32 26 3 26 C3 17.42 3 8.84 3 0 C3.33 0 3.66 0 4 0 C4.02 0.772 4.04 1.544 4.06 2.339 C4.155 5.831 4.265 9.321 4.375 12.812 C4.406 14.027 4.437 15.242 4.469 16.494 C4.507 17.658 4.546 18.821 4.586 20.02 C4.617 21.093 4.649 22.167 4.681 23.273 C4.816 26.216 4.816 26.216 7 29 C10.584 29.25 10.584 29.25 14 29 C14 30.32 14 31.64 14 33 C12.584 33.054 11.167 33.093 9.75 33.125 C8.961 33.148 8.172 33.171 7.359 33.195 C4.754 32.98 3.2 32.392 1 31 C-0.435 28.13 -0.112 25.665 -0.098 22.461 C-0.094 21.159 -0.091 19.857 -0.088 18.516 C-0.08 17.135 -0.071 15.755 -0.062 14.375 C-0.057 12.987 -0.053 11.599 -0.049 10.211 C-0.037 6.807 -0.021 3.404 0 0 Z " fill="#141314" transform="translate(300,690)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 9.24 1.66 18.48 2 28 C2.33 18.76 2.66 9.52 3 0 C3.33 0 3.66 0 4 0 C4.02 0.772 4.04 1.544 4.06 2.339 C4.155 5.831 4.265 9.321 4.375 12.812 C4.406 14.027 4.437 15.242 4.469 16.494 C4.507 17.658 4.546 18.821 4.586 20.02 C4.617 21.093 4.649 22.167 4.681 23.273 C4.816 26.216 4.816 26.216 7 29 C10.584 29.25 10.584 29.25 14 29 C14 30.32 14 31.64 14 33 C12.23 33.081 10.459 33.139 8.688 33.188 C7.701 33.222 6.715 33.257 5.699 33.293 C3 33 3 33 1.35 31.91 C-0.687 29.028 -0.337 26.637 -0.293 23.145 C-0.289 22.483 -0.284 21.821 -0.28 21.14 C-0.263 19.03 -0.226 16.922 -0.188 14.812 C-0.172 13.381 -0.159 11.949 -0.146 10.518 C-0.113 7.011 -0.062 3.506 0 0 Z " fill="#121212" transform="translate(52,690)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 2.97 5 5.94 5 9 C8.63 9 12.26 9 16 9 C16 9.99 16 10.98 16 12 C0.255 13.333 0.255 13.333 -7 12 C-7 11.34 -7 10.68 -7 10 C-4.69 10 -2.38 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#222325" transform="translate(679,677)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.619 1.814 2.238 1.629 2.875 1.438 C5 1 5 1 7 2 C7.625 4.062 7.625 4.062 8 6 C7.34 6 6.68 6 6 6 C5.67 6.99 5.34 7.98 5 9 C5 7.68 5 6.36 5 5 C2.03 5 -0.94 5 -4 5 C-4 5.66 -4 6.32 -4 7 C-3.01 7.33 -2.02 7.66 -1 8 C-1 16.91 -1 25.82 -1 35 C-3.066 31.901 -3.252 31.145 -3.309 27.621 C-3.346 26.363 -3.346 26.363 -3.385 25.08 C-3.42 23.315 -3.451 21.549 -3.479 19.783 C-3.505 18.946 -3.531 18.109 -3.559 17.246 C-3.572 16.48 -3.586 15.713 -3.599 14.924 C-3.85 12.593 -3.85 12.593 -7 11 C-7.293 8.836 -7.293 8.836 -7.188 6.375 C-7.16 5.558 -7.133 4.74 -7.105 3.898 C-7.053 2.959 -7.053 2.959 -7 2 C-4.692 0.846 -2.528 0.466 0 0 Z " fill="#D1D0D0" transform="translate(7,682)"/>
<path d="M0 0 C0.928 0.206 1.856 0.413 2.812 0.625 C4.956 5.865 4.956 5.865 4.375 8.938 C1.935 11.572 -0.733 11.369 -4.188 11.625 C-5.75 10 -5.75 10 -7.188 7.625 C-7.062 4.5 -7.062 4.5 -6.188 1.625 C-3.188 -0.375 -3.188 -0.375 0 0 Z " fill="#DEE0E2" transform="translate(327.1875,671.375)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C4.671 5.175 4.837 10.585 3.465 16.184 C1.605 21.559 -0.847 25.477 -5.562 28.812 C-10.397 30.483 -14.935 30.243 -20 30 C-20 29.67 -20 29.34 -20 29 C-19.348 28.902 -18.695 28.804 -18.023 28.703 C-11.84 27.645 -6.933 26.547 -2.75 21.488 C1.297 15.011 0.344 7.331 0 0 Z " fill="#EAEAEB" transform="translate(822,695)"/>
<path d="M0 0 C0 1.32 0 2.64 0 4 C-0.592 4.086 -1.183 4.173 -1.793 4.262 C-7.498 5.276 -10.324 6.494 -14 11 C-14.66 9.68 -15.32 8.36 -16 7 C-16.33 14.92 -16.66 22.84 -17 31 C-17.33 31 -17.66 31 -18 31 C-18.33 21.1 -18.66 11.2 -19 1 C-17.35 1 -15.7 1 -14 1 C-13.67 2.32 -13.34 3.64 -13 5 C-11.824 4.041 -11.824 4.041 -10.625 3.062 C-6.775 0.038 -4.851 -0.105 0 0 Z " fill="#1A191A" transform="translate(722,686)"/>
<path d="M0 0 C2.875 0.75 2.875 0.75 3.875 1.75 C4.456 8.029 4.456 8.029 2.312 10.688 C-0.926 12.099 -3.633 11.951 -7.125 11.75 C-6.465 11.42 -5.805 11.09 -5.125 10.75 C-5.785 9.76 -6.445 8.77 -7.125 7.75 C-6.918 4.983 -6.422 3.219 -4.938 0.875 C-3.125 -0.25 -3.125 -0.25 0 0 Z " fill="#DEE0E1" transform="translate(735.125,671.25)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.625 5.625 1.625 5.625 1 9 C-2.188 9.812 -2.188 9.812 -6 10 C-8.938 7.562 -8.938 7.562 -11 5 C-8.094 -0.397 -6.028 -1.7 0 0 Z " fill="#DCDFE0" transform="translate(780,672)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C9.34 1.32 8.68 2.64 8 4 C8 3.01 8 2.02 8 1 C6.02 1 4.04 1 2 1 C1.882 1.865 1.764 2.73 1.643 3.622 C0.998 7.009 0.013 9.866 -1.285 13.055 C-1.957 14.718 -1.957 14.718 -2.643 16.414 C-3.111 17.556 -3.58 18.698 -4.062 19.875 C-4.535 21.04 -5.007 22.206 -5.494 23.406 C-6.657 26.273 -7.826 29.138 -9 32 C-11.125 28.812 -11.502 26.735 -12 23 C-11.01 23 -10.02 23 -9 23 C-6.03 15.41 -3.06 7.82 0 0 Z " fill="#EBEBEB" transform="translate(363,685)"/>
<path d="M0 0 C5.529 2.061 10.188 5.122 15.125 8.312 C16.031 8.882 16.937 9.451 17.871 10.037 C18.741 10.598 19.611 11.16 20.508 11.738 C21.298 12.244 22.089 12.75 22.903 13.271 C26.061 15.875 27.782 18.406 28.341 22.495 C28.325 23.349 28.309 24.203 28.293 25.082 C28.283 26.013 28.274 26.945 28.264 27.904 C28.239 28.864 28.213 29.824 28.188 30.812 C28.167 32.281 28.167 32.281 28.146 33.779 C28.111 36.187 28.062 38.593 28 41 C22.25 40.25 22.25 40.25 20 38 C23 38 23 38 26 39 C25.977 35.769 25.9 32.542 25.812 29.312 C25.807 28.4 25.801 27.488 25.795 26.549 C25.644 21.933 25.526 19.679 22.625 15.938 C20.119 14.088 17.963 12.931 15 12 C15 11.34 15 10.68 15 10 C14.285 9.743 13.569 9.487 12.832 9.223 C9.813 7.919 7.367 6.339 4.688 4.438 C3.804 3.817 2.921 3.197 2.012 2.559 C1.348 2.044 0.684 1.53 0 1 C0 0.67 0 0.34 0 0 Z " fill="#83CEFB" transform="translate(681,310)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 17.83 2 34.66 2 52 C4.31 52.33 6.62 52.66 9 53 C9.33 52.01 9.66 51.02 10 50 C10.33 50.66 10.66 51.32 11 52 C10.333 53.333 9.667 54.667 9 56 C9.33 56.66 9.66 57.32 10 58 C6.7 58 3.4 58 0 58 C0 38.86 0 19.72 0 0 Z " fill="#E7E8E8" transform="translate(581,670)"/>
<path d="M0 0 C2 3 2 3 1.75 6.125 C1.503 7.074 1.255 8.023 1 9 C-1.763 10.381 -3.947 10.191 -7 10 C-9 8 -9 8 -9.375 5.062 C-9 2 -9 2 -7.312 0.062 C-4.369 -1.29 -3.015 -1.111 0 0 Z " fill="#EEEFEE" transform="translate(1013,672)"/>
<path d="M0 0 C2.875 0.688 2.875 0.688 3.875 1.688 C4.062 5.125 4.062 5.125 3.875 8.688 C1.48 11.082 0.217 11.443 -3.125 11.688 C-4.938 10.688 -4.938 10.688 -6.125 8.688 C-6.5 5.125 -6.5 5.125 -6.125 1.688 C-3.125 -0.312 -3.125 -0.312 0 0 Z " fill="#EAEBEC" transform="translate(381.125,671.3125)"/>
<path d="M0 0 C1.152 1.661 1.152 1.661 2 4 C1.125 6.565 1.125 6.565 -0.43 9.422 C-0.978 10.447 -1.525 11.471 -2.09 12.527 C-2.679 13.591 -3.268 14.654 -3.875 15.75 C-4.456 16.829 -5.038 17.908 -5.637 19.02 C-7.077 21.688 -8.532 24.347 -10 27 C-12.027 23.298 -12.027 23.298 -11.785 20.613 C-10.899 17.663 -9.748 14.976 -8.438 12.188 C-7.982 11.212 -7.527 10.236 -7.059 9.23 C-6.709 8.494 -6.36 7.758 -6 7 C-5.34 7 -4.68 7 -4 7 C-2.68 4.69 -1.36 2.38 0 0 Z " fill="#1D1C1D" transform="translate(981,678)"/>
<path d="M0 0 C4.083 0.698 5.445 2.476 7.858 5.814 C8.317 6.474 8.777 7.133 9.25 7.812 C9.728 8.482 10.206 9.151 10.698 9.84 C14.933 15.803 19.015 21.868 23 28 C22.67 28.66 22.34 29.32 22 30 C21.301 29.013 20.603 28.025 19.883 27.008 C18.964 25.713 18.044 24.419 17.125 23.125 C16.665 22.474 16.205 21.823 15.73 21.152 C15.063 20.215 15.063 20.215 14.383 19.258 C13.77 18.394 13.77 18.394 13.145 17.512 C11.939 15.848 11.939 15.848 10 14 C10.517 14.771 11.034 15.542 11.566 16.336 C12.225 17.339 12.884 18.342 13.562 19.375 C14.554 20.872 14.554 20.872 15.566 22.398 C16.039 23.257 16.513 24.115 17 25 C16.67 25.66 16.34 26.32 16 27 C14.39 24.795 12.787 22.587 11.188 20.375 C10.736 19.758 10.284 19.14 9.818 18.504 C7.153 14.808 4.923 11.153 3 7 C3.66 7 4.32 7 5 7 C5.33 7.33 5.66 7.66 6 8 C4.264 4.942 2.615 2.366 0 0 Z " fill="#151515" transform="translate(585,673)"/>
<path d="M0 0 C1.392 4.176 -0.177 6.135 -2 10 C-1.435 9.984 -0.87 9.969 -0.288 9.952 C2.267 9.89 4.82 9.851 7.375 9.812 C8.264 9.787 9.154 9.762 10.07 9.736 C10.921 9.727 11.772 9.717 12.648 9.707 C13.827 9.683 13.827 9.683 15.029 9.659 C15.679 9.772 16.33 9.884 17 10 C17.99 11.485 17.99 11.485 19 13 C11.41 13 3.82 13 -4 13 C-4.99 15.97 -5.98 18.94 -7 22 C-7.698 23.701 -8.419 25.393 -9.188 27.062 C-9.532 27.817 -9.876 28.571 -10.23 29.348 C-10.484 29.893 -10.738 30.438 -11 31 C-11.99 30.67 -12.98 30.34 -14 30 C-13.34 30 -12.68 30 -12 30 C-11.818 29.455 -11.636 28.909 -11.449 28.348 C-11.198 27.594 -10.946 26.839 -10.688 26.062 C-10.417 25.248 -10.146 24.433 -9.867 23.594 C-9.295 21.883 -8.717 20.175 -8.133 18.469 C-7.862 17.675 -7.591 16.881 -7.312 16.062 C-7.061 15.332 -6.81 14.601 -6.551 13.848 C-5.905 12.002 -5.905 12.002 -6 10 C-5.01 10 -4.02 10 -3 10 C-2.876 9.092 -2.752 8.185 -2.625 7.25 C-2.079 4.41 -1.427 2.474 0 0 Z " fill="#0F0E0E" transform="translate(220,692)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 12.54 1 25.08 1 38 C3.31 38 5.62 38 8 38 C8.33 31.07 8.66 24.14 9 17 C9.33 17 9.66 17 10 17 C10.226 20.249 10.428 23.499 10.625 26.75 C10.689 27.67 10.754 28.591 10.82 29.539 C10.872 30.429 10.923 31.318 10.977 32.234 C11.055 33.46 11.055 33.46 11.135 34.71 C10.984 37.268 10.289 38.809 9 41 C2.848 41.098 2.848 41.098 1 41 C-2.016 37.984 -0.945 30.136 -1 26 C-0.851 17.328 -0.431 8.662 0 0 Z " fill="#CACACA" transform="translate(829,685)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.034 0.761 1.069 1.523 1.104 2.307 C1.274 5.769 1.481 9.228 1.688 12.688 C1.741 13.886 1.794 15.084 1.85 16.318 C2.231 23.227 2.231 23.227 5.281 29.281 C8.032 31.02 9.596 31.668 12.812 31.812 C13.998 31.884 13.998 31.884 15.207 31.957 C16.095 31.978 16.095 31.978 17 32 C17.102 31.376 17.204 30.752 17.309 30.109 C18 28 18 28 21.062 26.25 C22.032 25.837 23.001 25.425 24 25 C23.747 28.125 23.409 29.605 21.125 31.812 C15.981 33.767 10.15 33.943 4.848 32.355 C1.902 30.194 1.199 28.497 0 25 C-0.352 20.7 -0.282 16.435 -0.188 12.125 C-0.174 10.96 -0.16 9.794 -0.146 8.594 C-0.111 5.729 -0.062 2.865 0 0 Z " fill="#0F0F0F" transform="translate(7,690)"/>
<path d="M0 0 C4 5 4 5 4 7 C3.34 7 2.68 7 2 7 C2 18.55 2 30.1 2 42 C1.34 42 0.68 42 0 42 C0 28.14 0 14.28 0 0 Z " fill="#F5F5F6" transform="translate(588,681)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 12.21 1 24.42 1 37 C-3.967 35.758 -4.548 34.367 -7.312 30.188 C-8.031 29.129 -8.749 28.071 -9.488 26.98 C-11 24 -11 24 -10.73 21.613 C-10.489 21.081 -10.248 20.549 -10 20 C-9.434 20.794 -8.868 21.588 -8.285 22.406 C-7.552 23.427 -6.818 24.448 -6.062 25.5 C-5.332 26.521 -4.601 27.542 -3.848 28.594 C-3.238 29.388 -2.628 30.182 -2 31 C-1.67 31 -1.34 31 -1 31 C-0.505 15.655 -0.505 15.655 0 0 Z M-1 32 C0 34 0 34 0 34 Z " fill="#CFCFD0" transform="translate(615,672)"/>
<path d="M0 0 C0 11.55 0 23.1 0 35 C-0.99 35.495 -0.99 35.495 -2 36 C-2.061 34.94 -2.121 33.881 -2.184 32.789 C-2.268 31.401 -2.353 30.013 -2.438 28.625 C-2.477 27.926 -2.516 27.228 -2.557 26.508 C-2.743 22.626 -2.743 22.626 -4 19 C-3.34 19 -2.68 19 -2 19 C-2.166 18.435 -2.333 17.871 -2.504 17.289 C-3.182 14.158 -2.972 11.208 -2.84 8.023 C-2.798 5.769 -2.798 5.769 -5 4 C-4.67 3.01 -4.34 2.02 -4 1 C-2 0 -2 0 0 0 Z " fill="#F6F7F7" transform="translate(774,687)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 11.22 2 22.44 2 34 C1.34 34 0.68 34 0 34 C-0.33 32.68 -0.66 31.36 -1 30 C-1.66 30 -2.32 30 -3 30 C-3.33 28.68 -3.66 27.36 -4 26 C-3.01 25.34 -2.02 24.68 -1 24 C-0.49 21.486 -0.49 21.486 -0.488 18.555 C-0.453 17.478 -0.417 16.402 -0.381 15.293 C-0.358 14.165 -0.336 13.037 -0.312 11.875 C-0.278 10.739 -0.244 9.604 -0.209 8.434 C-0.126 5.622 -0.057 2.812 0 0 Z " fill="#424142" transform="translate(34,688)"/>
<path d="M0 0 C2.774 1.765 5.319 3.533 7.75 5.75 C7.75 6.41 7.75 7.07 7.75 7.75 C8.41 7.75 9.07 7.75 9.75 7.75 C9.75 8.74 9.75 9.73 9.75 10.75 C8.76 11.08 7.77 11.41 6.75 11.75 C6.234 10.822 5.719 9.894 5.188 8.938 C2.73 5.353 0.004 3.602 -4.25 2.75 C-8.596 2.522 -12.902 2.573 -17.25 2.75 C-13.437 -2.97 -5.928 -2.28 0 0 Z " fill="#DCDDDD" transform="translate(814.25,684.25)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C10.478 2.957 10.06 5.742 10 9 C5.255 10.967 2.01 11.156 -3 10 C-3.66 9.34 -4.32 8.68 -5 8 C-4.67 7.34 -4.34 6.68 -4 6 C-0.37 6 3.26 6 7 6 C6.67 5.34 6.34 4.68 6 4 C4.02 4 2.04 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#D6D5D5" transform="translate(307,716)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.061 0.82 1.121 1.64 1.184 2.484 C1.267 3.562 1.351 4.64 1.438 5.75 C1.559 7.351 1.559 7.351 1.684 8.984 C2.054 12.56 2.054 12.56 3 16 C6.497 17.898 10.087 18.454 14 19 C14 19.66 14 20.32 14 21 C6.232 21.67 6.232 21.67 2.625 18.812 C-1.845 14.023 -2.223 10.319 -2.266 3.988 C-2 2 -2 2 0 0 Z " fill="#EDEDED" transform="translate(263,698)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.988 0.626 0.977 1.253 0.965 1.898 C0.956 2.716 0.947 3.533 0.938 4.375 C0.926 5.187 0.914 5.999 0.902 6.836 C0.855 9.049 0.855 9.049 2 11 C0.02 11 -1.96 11 -4 11 C-4 11.99 -4 12.98 -4 14 C-2.02 14 -0.04 14 2 14 C2 22.58 2 31.16 2 40 C1.67 40 1.34 40 1 40 C1 32.08 1 24.16 1 16 C-1.31 16 -3.62 16 -6 16 C-6.66 15.34 -7.32 14.68 -8 14 C-7.34 12.02 -6.68 10.04 -6 8 C-4.02 8 -2.04 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#E7E8E8" transform="translate(748,676)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C2.99 2 3.98 2 5 2 C5.849 6.437 6.163 10.671 6.188 15.188 C6.202 16.398 6.216 17.608 6.23 18.855 C6 22 6 22 4 25 C3.67 18.73 3.34 12.46 3 6 C1.35 5.67 -0.3 5.34 -2 5 C-2.33 7.97 -2.66 10.94 -3 14 C-3.33 14 -3.66 14 -4 14 C-4.33 10.37 -4.66 6.74 -5 3 C-3.35 2.67 -1.7 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DADBDC" transform="translate(325,682)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.027 1.458 1.046 2.917 1.062 4.375 C1.074 5.187 1.086 5.999 1.098 6.836 C1 9 1 9 0 11 C-2.066 11.414 -2.066 11.414 -4.562 11.625 C-5.389 11.7 -6.215 11.775 -7.066 11.852 C-8.024 11.925 -8.024 11.925 -9 12 C-9 11.34 -9 10.68 -9 10 C-10.32 9.67 -11.64 9.34 -13 9 C-9.535 8.505 -9.535 8.505 -6 8 C-5.67 7.01 -5.34 6.02 -5 5 C-7.31 5.33 -9.62 5.66 -12 6 C-12.66 5.01 -13.32 4.02 -14 3 C-10.7 2.67 -7.4 2.34 -4 2 C-2 5 -2 5 -2 8 C-1.34 8 -0.68 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#D8D8D8" transform="translate(771,714)"/>
<path d="M0 0 C1.081 3.244 1.003 5.416 0.875 8.812 C0.846 13.633 0.975 17.036 4 21 C7.88 23.371 11.538 23.175 16 23 C16.33 23.99 16.66 24.98 17 26 C6.177 26.777 6.177 26.777 1.875 23.938 C-2.05 17.789 -2.816 11.163 -2 4 C-0.984 1.699 -0.984 1.699 0 0 Z " fill="#484849" transform="translate(792,696)"/>
<path d="M0 0 C3.293 4.55 5.069 9.493 6 15 C5.01 15 4.02 15 3 15 C2.326 13.566 1.661 12.127 1 10.688 C0.629 9.887 0.257 9.086 -0.125 8.262 C-1 6 -1 6 -1 3 C-3.934 3.267 -3.934 3.267 -7 4 C-7.99 5.485 -7.99 5.485 -9 7 C-9.66 6.67 -10.32 6.34 -11 6 C-10.398 3.98 -9.727 1.979 -9 0 C-5.566 -1.717 -3.609 -1.162 0 0 Z " fill="#E1E1E1" transform="translate(232,670)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 11.22 1 22.44 1 34 C0.34 34 -0.32 34 -1 34 C-1.193 29.962 -1.372 25.924 -1.537 21.885 C-1.595 20.512 -1.657 19.14 -1.724 17.767 C-1.818 15.789 -1.898 13.811 -1.977 11.832 C-2.055 10.049 -2.055 10.049 -2.135 8.23 C-2 5.004 -1.394 2.886 0 0 Z " fill="#2F2F2F" transform="translate(776,688)"/>
<path d="M0 0 C-1.391 2.782 -3.387 3.021 -6.145 4.152 C-8.862 5.394 -10.892 6.892 -13 9 C-13 10.32 -13 11.64 -13 13 C-12.01 12.67 -11.02 12.34 -10 12 C-10.66 13.32 -11.32 14.64 -12 16 C-13.32 15.67 -14.64 15.34 -16 15 C-16.568 11.842 -16.745 9.138 -16 6 C-11.521 1.073 -6.506 -0.831 0 0 Z " fill="#E9EAEA" transform="translate(946,671)"/>
<path d="M0 0 C4.021 -0.029 8.042 -0.047 12.062 -0.062 C13.763 -0.075 13.763 -0.075 15.498 -0.088 C27.027 -0.122 27.027 -0.122 32 1 C32.495 1.99 32.495 1.99 33 3 C22.11 3 11.22 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#ACABAA" transform="translate(0,361)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C1.957 7.983 1.957 7.983 2.934 7.965 C4.173 7.951 4.173 7.951 5.438 7.938 C6.261 7.926 7.085 7.914 7.934 7.902 C10 8 10 8 11 9 C11.125 12.5 11.125 12.5 11 16 C9.333 17.667 7.344 17.211 5.062 17.25 C4.167 17.276 3.271 17.302 2.348 17.328 C1.186 17.166 1.186 17.166 0 17 C-0.66 16.01 -1.32 15.02 -2 14 C1.3 14 4.6 14 8 14 C8 13.01 8 12.02 8 11 C4.7 11 1.4 11 -2 11 C-1.34 10.34 -0.68 9.68 0 9 C0.243 6.789 0.243 6.789 0.125 4.375 C0.107 3.558 0.089 2.74 0.07 1.898 C0.047 1.272 0.024 0.645 0 0 Z " fill="#C5C5C5" transform="translate(58,676)"/>
<path d="M0 0 C3.884 6.732 3.123 14.359 3.062 21.875 C3.058 22.945 3.053 24.015 3.049 25.117 C3.037 27.745 3.021 30.372 3 33 C7.455 32.505 7.455 32.505 12 32 C9.201 34.799 6.869 34.724 3 35 C1 33 1 33 0.84 29.664 C0.862 28.318 0.895 26.971 0.938 25.625 C1.042 21.577 1.011 17.937 0 14 C-0.068 11.584 -0.085 9.167 -0.062 6.75 C-0.053 5.487 -0.044 4.223 -0.035 2.922 C-0.024 1.958 -0.012 0.993 0 0 Z " fill="#1EA7FC" transform="translate(721,326)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.346 6.768 2.321 11.018 -1 16 C-3.252 18.089 -4.497 18.943 -7.578 19.293 C-8.46 19.258 -9.342 19.223 -10.25 19.188 C-11.142 19.16 -12.034 19.133 -12.953 19.105 C-13.629 19.071 -14.304 19.036 -15 19 C-15 18.34 -15 17.68 -15 17 C-13.969 16.853 -13.969 16.853 -12.918 16.703 C-12.017 16.554 -11.116 16.404 -10.188 16.25 C-9.294 16.111 -8.401 15.972 -7.48 15.828 C-4.376 14.792 -3.562 13.851 -2 11 C-1.217 8.197 -1.217 8.197 -0.812 5.25 C-0.575 3.773 -0.575 3.773 -0.332 2.266 C-0.222 1.518 -0.113 0.77 0 0 Z " fill="#F0F0F0" transform="translate(816,700)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C6.62 3.67 11.24 3.34 16 3 C16.33 4.32 16.66 5.64 17 7 C10.73 7 4.46 7 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z " fill="#D9D9D9" transform="translate(220,695)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 5.94 2.66 11.88 3 18 C3.33 18 3.66 18 4 18 C4 20.97 4 23.94 4 27 C5.65 27 7.3 27 9 27 C9.33 27.99 9.66 28.98 10 30 C7.188 30.688 7.188 30.688 4 31 C2.333 29.921 2.333 29.921 1 28 C0.568 25.089 0.568 25.089 0.488 21.582 C0.453 20.328 0.417 19.075 0.381 17.783 C0.358 16.473 0.336 15.163 0.312 13.812 C0.279 12.477 0.245 11.142 0.209 9.807 C0.126 6.538 0.057 3.269 0 0 Z " fill="#D0D0D0" transform="translate(853,696)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.66 0.66 8.32 1.32 9 2 C6.69 2 4.38 2 2 2 C2 10.25 2 18.5 2 27 C1.34 26.67 0.68 26.34 0 26 C0 17.42 0 8.84 0 0 Z " fill="#E5E4E5" transform="translate(401,690)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.287 0.777 2.574 1.554 2.87 2.355 C4.174 5.866 5.493 9.37 6.812 12.875 C7.264 14.098 7.716 15.322 8.182 16.582 C8.623 17.749 9.065 18.915 9.52 20.117 C9.923 21.196 10.326 22.275 10.741 23.386 C11.858 26.273 11.858 26.273 15 28 C15 30.84 14.597 33.237 14 36 C11.661 31.615 9.984 26.946 8.211 22.312 C6.653 18.318 6.653 18.318 4 15 C3.438 13.047 3.438 13.047 3 10.75 C2.248 7.055 1.276 3.556 0 0 Z " fill="#4A494B" transform="translate(419,688)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.66 2.33 1.32 2.66 2 3 C0.926 5.915 0.222 7.778 -2 10 C-4.383 9.977 -4.383 9.977 -7.125 9.625 C-8.035 9.514 -8.945 9.403 -9.883 9.289 C-10.581 9.194 -11.28 9.098 -12 9 C-11.34 8.01 -10.68 7.02 -10 6 C-8.055 5.707 -8.055 5.707 -5.875 5.812 C-4.596 5.874 -3.317 5.936 -2 6 C-2 5.01 -2 4.02 -2 3 C-4.31 3 -6.62 3 -9 3 C-8.67 2.01 -8.34 1.02 -8 0 C-4.713 -0.8 -3.29 -1.097 0 0 Z " fill="#D3D2D2" transform="translate(413,717)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C15 4.62 15 9.24 15 14 C14.67 14 14.34 14 14 14 C14 10.04 14 6.08 14 2 C9.71 2 5.42 2 1 2 C0.783 3.671 0.783 3.671 0.562 5.375 C0 9 0 9 -1 11 C-1.66 11 -2.32 11 -3 11 C-2.506 6.722 -1.952 3.904 0 0 Z " fill="#F7F8F8" transform="translate(970,708)"/>
<path d="M0 0 C3.062 1.5 3.062 1.5 5 3 C4.67 3.66 4.34 4.32 4 5 C2.35 4.34 0.7 3.68 -1 3 C-1 3.33 -1 3.66 -1 4 C-2.582 4.338 -4.166 4.67 -5.75 5 C-7.073 5.278 -7.073 5.278 -8.422 5.562 C-11 6 -11 6 -15 6 C-14.041 3.603 -13.427 2.264 -11.199 0.887 C-7.515 -0.599 -3.907 -0.48 0 0 Z " fill="#1C1C1D" transform="translate(853,686)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.107 11.721 1.991 23.316 1 35 C0.67 35 0.34 35 0 35 C-0.168 30.498 -0.328 25.996 -0.482 21.493 C-0.536 19.961 -0.591 18.43 -0.648 16.898 C-0.73 14.697 -0.805 12.495 -0.879 10.293 C-0.906 9.608 -0.933 8.924 -0.961 8.219 C-1.057 5.153 -0.98 2.941 0 0 Z " fill="#A8B3BF" transform="translate(724,324)"/>
<path d="M0 0 C1.125 1.688 1.125 1.688 2 4 C1.375 6.688 1.375 6.688 0 9 C-3.501 10.815 -7.26 9.647 -11 9 C-10.01 6.36 -9.02 3.72 -8 1 C-7.01 1.66 -6.02 2.32 -5 3 C-5.33 3.99 -5.66 4.98 -6 6 C-4.041 5.887 -4.041 5.887 -2 5 C-0.703 2.51 -0.703 2.51 0 0 Z " fill="#D3D3D2" transform="translate(966,717)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.037 0.831 1.075 1.663 1.113 2.52 C1.212 4.15 1.212 4.15 1.312 5.812 C1.371 6.891 1.429 7.97 1.488 9.082 C1.787 11.957 1.787 11.957 3.25 13.844 C5.451 15.298 7.067 15.498 9.688 15.688 C10.496 15.753 11.304 15.819 12.137 15.887 C12.752 15.924 13.366 15.961 14 16 C14 17.32 14 18.64 14 20 C12.04 19.718 10.082 19.424 8.125 19.125 C6.489 18.881 6.489 18.881 4.82 18.633 C2 18 2 18 0 16 C-0.145 13.255 -0.187 10.615 -0.125 7.875 C-0.111 6.744 -0.111 6.744 -0.098 5.59 C-0.074 3.726 -0.038 1.863 0 0 Z " fill="#383738" transform="translate(681,703)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 8.58 7 17.16 7 26 C4.895 22.843 4.691 21.601 4.438 17.938 C4.224 14.332 4.224 14.332 3 11 C3.34 8.617 3.34 8.617 3.938 5.875 C4.132 4.965 4.327 4.055 4.527 3.117 C4.761 2.069 4.761 2.069 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#F5F5F5" transform="translate(672,691)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C12 13.53 12 27.06 12 41 C11.67 41 11.34 41 11 41 C10.67 28.79 10.34 16.58 10 4 C9.01 4.495 9.01 4.495 8 5 C8 4.01 8 3.02 8 2 C5.36 1.67 2.72 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C5C6C6" transform="translate(364,684)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.539 5.262 1.539 5.262 0.625 7.688 C0.329 8.496 0.032 9.304 -0.273 10.137 C-0.633 11.059 -0.633 11.059 -1 12 C-7.055 12.977 -7.055 12.977 -9 13 C-9.66 12.34 -10.32 11.68 -11 11 C-10.67 10.01 -10.34 9.02 -10 8 C-9.34 7.67 -8.68 7.34 -8 7 C-8 7.66 -8 8.32 -8 9 C-6.35 9 -4.7 9 -3 9 C-2.01 6.03 -1.02 3.06 0 0 Z " fill="#D9D9D9" transform="translate(359,714)"/>
<path d="M0 0 C0.971 -0.014 1.941 -0.028 2.941 -0.043 C5.438 0.188 5.438 0.188 7.438 2.188 C6.713 2.225 5.989 2.262 5.242 2.301 C-3.094 2.868 -8.136 4.706 -14.562 10.188 C-14.268 6.652 -13.814 5.425 -11.188 2.938 C-7.361 0.386 -4.505 0.03 0 0 Z " fill="#ECEDED" transform="translate(272.5625,683.8125)"/>
<path d="M0 0 C4.614 3.032 6.592 8.826 8 14 C7.67 14.99 7.34 15.98 7 17 C6.674 16.169 6.348 15.337 6.012 14.48 C5.356 12.85 5.356 12.85 4.688 11.188 C4.258 10.109 3.829 9.03 3.387 7.918 C2.132 5.278 1.333 3.71 -1 2 C-5.594 1.45 -5.594 1.45 -9.75 3.125 C-11.425 5.638 -12.724 8.266 -14 11 C-15 8 -15 8 -13.312 4.438 C-12.041 2.392 -11.053 1.044 -9.188 -0.5 C-5.833 -1.267 -3.321 -0.83 0 0 Z " fill="#7FD1FC" transform="translate(322,364)"/>
<path d="M0 0 C3 1 3 1 4.805 2.996 C7.585 5.534 9.423 5.667 13.125 6.062 C16.32 6.41 16.32 6.41 19 7 C19.99 8.485 19.99 8.485 21 10 C17.37 10.33 13.74 10.66 10 11 C10 10.34 10 9.68 10 9 C9.113 8.794 8.226 8.587 7.312 8.375 C3.077 6.617 1.695 4.646 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E4E4E4" transform="translate(790,716)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 10.89 5 21.78 5 33 C4.67 33 4.34 33 4 33 C3.985 32.228 3.971 31.456 3.956 30.661 C3.881 27.169 3.785 23.679 3.688 20.188 C3.654 18.365 3.654 18.365 3.619 16.506 C3.584 15.342 3.548 14.179 3.512 12.98 C3.486 11.907 3.459 10.833 3.432 9.727 C2.939 6.617 2.211 6.073 0 4 C0 2.68 0 1.36 0 0 Z " fill="#505051" transform="translate(1019,687)"/>
<path d="M0 0 C6.75 -0.125 6.75 -0.125 9 1 C9.33 12.22 9.66 23.44 10 35 C9.34 35 8.68 35 8 35 C7.67 24.44 7.34 13.88 7 3 C5.35 3 3.7 3 2 3 C1.34 2.01 0.68 1.02 0 0 Z " fill="#E9EBEC" transform="translate(616,670)"/>
<path d="M0 0 C4.099 3.843 7.519 8.077 10.996 12.484 C13.583 15.731 16.276 18.868 19 22 C11.87 18.939 4.817 12.408 1.312 5.5 C0 2 0 2 0 0 Z " fill="#62C6FD" transform="translate(222,505)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.02 0.772 1.04 1.544 1.06 2.339 C1.155 5.831 1.265 9.321 1.375 12.812 C1.406 14.027 1.437 15.242 1.469 16.494 C1.507 17.658 1.546 18.821 1.586 20.02 C1.617 21.093 1.649 22.167 1.681 23.273 C1.816 26.216 1.816 26.216 4 29 C7.568 29.736 7.568 29.736 11 30 C11 30.99 11 31.98 11 33 C8.36 33 5.72 33 3 33 C0.771 28.542 -0.121 25.976 -0.098 20.898 C-0.094 19.687 -0.091 18.475 -0.088 17.227 C-0.08 15.956 -0.071 14.685 -0.062 13.375 C-0.058 12.096 -0.053 10.817 -0.049 9.5 C-0.037 6.333 -0.021 3.167 0 0 Z " fill="#050505" transform="translate(400,690)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.33 10 0.66 10 1 C9.051 1.124 8.103 1.247 7.125 1.375 C3.913 1.717 3.913 1.717 2 4 C1.722 6.328 1.722 6.328 1.805 9.008 C1.811 9.994 1.818 10.98 1.824 11.996 C1.849 13.545 1.849 13.545 1.875 15.125 C1.884 16.165 1.893 17.206 1.902 18.277 C1.926 20.852 1.959 23.426 2 26 C3.65 26 5.3 26 7 26 C7 26.33 7 26.66 7 27 C4.69 27 2.38 27 0 27 C-1.349 24.302 -1.016 22.347 -0.879 19.336 C-0.831 18.214 -0.782 17.093 -0.732 15.938 C-0.648 14.174 -0.648 14.174 -0.562 12.375 C-0.51 11.192 -0.458 10.008 -0.404 8.789 C-0.274 5.859 -0.139 2.93 0 0 Z " fill="#D1D1D1" transform="translate(403,692)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C2.34 2.66 1.68 3.32 1 4 C-0.32 4 -1.64 4 -3 4 C-3 7.3 -3 10.6 -3 14 C-5.31 14 -7.62 14 -10 14 C-10 13.67 -10 13.34 -10 13 C-8.02 13 -6.04 13 -4 13 C-5.184 11.584 -5.184 11.584 -8.562 11.938 C-10.264 11.968 -10.264 11.968 -12 12 C-12 11.67 -12 11.34 -12 11 C-10.02 11 -8.04 11 -6 11 C-6 8.03 -6 5.06 -6 2 C-3.188 0.875 -3.188 0.875 0 0 Z " fill="#C1C3C4" transform="translate(400,673)"/>
<path d="M0 0 C2.793 0.388 4.578 0.638 6.75 2.5 C9.75 4.5 12.33 4.45 15.879 4.594 C18.455 5.087 19.349 6.017 21 8 C19.35 8 17.7 8 16 8 C15.67 8.66 15.34 9.32 15 10 C15 9.34 15 8.68 15 8 C14.01 7.938 13.02 7.876 12 7.812 C7.391 7.172 3.553 4.962 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D3D3D2" transform="translate(258,718)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C-0.65 6 -2.3 6 -4 6 C-4 7.32 -4 8.64 -4 10 C-3.01 10.33 -2.02 10.66 -1 11 C-1.99 12.485 -1.99 12.485 -3 14 C-3 13.34 -3 12.68 -3 12 C-4.65 12.33 -6.3 12.66 -8 13 C-7.213 9.59 -6.129 6.311 -5 3 C-5.66 2.67 -6.32 2.34 -7 2 C-4.69 2 -2.38 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DBDCDC" transform="translate(1023,681)"/>
<path d="M0 0 C4.923 1.231 7.265 2.687 10 7 C10 7.99 10 8.98 10 10 C5.657 9.517 3.86 8.255 1 5 C0.34 4.34 -0.32 3.68 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#2E2C2D" transform="translate(277,686)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C3.358 3.957 3.358 3.957 3.723 4.934 C4.809 7.175 4.809 7.175 7.059 7.723 C7.844 7.835 8.629 7.947 9.438 8.062 C13 8.585 14.656 9.218 17 12 C11.25 13.125 11.25 13.125 9 12 C8.67 12.66 8.34 13.32 8 14 C7 13 6 12 5 11 C4.299 10.423 3.597 9.845 2.875 9.25 C0.395 6.273 0.292 3.798 0 0 Z " fill="#D8D7D7" transform="translate(677,714)"/>
<path d="M0 0 C2.09 2.09 2.925 3.7 4.125 6.375 C4.478 7.146 4.831 7.917 5.195 8.711 C6 11 6 11 6 15 C6.33 14.67 6.66 14.34 7 14 C6.748 12.302 6.424 10.615 6.062 8.938 C5.868 8.018 5.673 7.099 5.473 6.152 C5.317 5.442 5.161 4.732 5 4 C7.951 5.476 8.069 7.501 9.125 10.562 C9.655 12.08 9.655 12.08 10.195 13.629 C10.461 14.411 10.726 15.194 11 16 C9.02 16 7.04 16 5 16 C4.161 13.898 3.329 11.794 2.5 9.688 C2.036 8.516 1.572 7.344 1.094 6.137 C0 3 0 3 0 0 Z " fill="#121111" transform="translate(240,707)"/>
<path d="M0 0 C3.375 -0.188 3.375 -0.188 7 0 C7.66 0.99 8.32 1.98 9 3 C6.69 3 4.38 3 2 3 C2 3.99 2 4.98 2 6 C4.31 6 6.62 6 9 6 C8.34 6.66 7.68 7.32 7 8 C6.833 10.584 6.833 10.584 7 13 C6.67 11.68 6.34 10.36 6 9 C4.02 9 2.04 9 0 9 C-1.125 7.312 -1.125 7.312 -2 5 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#D2D3D4" transform="translate(670,684)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.33 8 0.66 8 1 C6.02 1 4.04 1 2 1 C2.167 1.58 2.335 2.16 2.508 2.757 C3.275 6.254 2.835 9.71 2.562 13.25 C2.484 14.368 2.484 14.368 2.404 15.508 C2.275 17.339 2.138 19.17 2 21 C1.34 20.67 0.68 20.34 0 20 C0 13.4 0 6.8 0 0 Z " fill="#F6F6F6" transform="translate(56,691)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9.33 1.98 9.66 3.96 10 6 C9.01 6 8.02 6 7 6 C7 5.01 7 4.02 7 3 C5.35 3 3.7 3 2 3 C1.67 5.31 1.34 7.62 1 10 C0.34 9.67 -0.32 9.34 -1 9 C-0.67 6.03 -0.34 3.06 0 0 Z " fill="#D9DBDB" transform="translate(657,684)"/>
<path d="M0 0 C3.729 2.486 4.356 5.062 5.688 9.125 C5.912 9.784 6.137 10.442 6.369 11.121 C6.922 12.744 7.462 14.372 8 16 C6.68 16 5.36 16 4 16 C3.807 15.29 3.613 14.579 3.414 13.848 C3.154 12.929 2.893 12.009 2.625 11.062 C2.37 10.146 2.115 9.229 1.852 8.285 C1.233 5.833 1.233 5.833 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#E3E3E3" transform="translate(228,682)"/>
<path d="M0 0 C9.257 1.272 18.463 8.811 24.375 15.812 C24.911 16.534 25.447 17.256 26 18 C25.67 18.66 25.34 19.32 25 20 C24.322 19.276 23.644 18.551 22.945 17.805 C15.998 10.533 9.051 5.474 0 1 C0 0.67 0 0.34 0 0 Z " fill="#23A2FF" transform="translate(793,361)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 8.58 1 17.16 1 26 C-3.29 26 -7.58 26 -12 26 C-10 24 -10 24 -7.836 23.805 C-5.557 23.87 -3.279 23.935 -1 24 C-1 18.39 -1 12.78 -1 7 C-1.66 6.67 -2.32 6.34 -3 6 C-2.01 4.02 -1.02 2.04 0 0 Z " fill="#F9FAFA" transform="translate(984,678)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.812 3.559 2.135 6.851 2.125 10.5 C2.129 11.877 2.129 11.877 2.133 13.281 C2.002 15.95 1.594 18.399 1 21 C-1 19 -1 19 -1.243 17.136 C-1.239 16.399 -1.235 15.662 -1.23 14.902 C-1.229 14.101 -1.227 13.3 -1.225 12.475 C-1.206 11.219 -1.206 11.219 -1.188 9.938 C-1.187 9.096 -1.186 8.255 -1.186 7.389 C-1.14 1.14 -1.14 1.14 0 0 Z " fill="#F1F1F2" transform="translate(685,693)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C7.35 1 5.7 1 4 1 C4.289 1.675 4.577 2.351 4.875 3.047 C5.246 3.939 5.618 4.831 6 5.75 C6.371 6.632 6.743 7.513 7.125 8.422 C8 11 8 11 8 15 C6.105 14.344 6.105 14.344 4 13 C2.988 10.469 2.988 10.469 2.312 7.5 C1.454 3.642 1.454 3.642 0 0 Z " fill="#E5E6E7" transform="translate(413,685)"/>
<path d="M0 0 C3.417 3.197 6.143 6.741 8.812 10.562 C9.232 11.139 9.652 11.716 10.084 12.311 C11.27 14.004 11.27 14.004 13 17 C12.67 17.99 12.34 18.98 12 20 C9.272 17.454 7.633 15.313 6 12 C4.81 10.072 3.6 8.156 2.375 6.25 C1.743 5.265 1.112 4.28 0.461 3.266 C-0.021 2.518 -0.503 1.77 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#414143" transform="translate(595,686)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C5.29 7 9.58 7 14 7 C14 7.99 14 8.98 14 10 C12.042 10.027 10.083 10.046 8.125 10.062 C7.034 10.074 5.944 10.086 4.82 10.098 C2 10 2 10 0 9 C0 6.03 0 3.06 0 0 Z " fill="#464647" transform="translate(681,679)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.3 1.66 6.6 2 10 C3.299 9.691 4.599 9.381 5.938 9.062 C10.614 8.016 15.231 7.454 20 7 C17.219 8.854 15.406 9.57 12.25 10.438 C7.982 11.664 4.036 13.157 0 15 C0 10.05 0 5.1 0 0 Z " fill="#9DCDE8" transform="translate(740,336)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.197 3.437 1.381 6.875 1.562 10.312 C1.619 11.289 1.675 12.265 1.732 13.271 C1.781 14.209 1.829 15.147 1.879 16.113 C1.926 16.977 1.973 17.841 2.022 18.732 C2 21 2 21 1 24 C0.34 24 -0.32 24 -1 24 C-1.029 20.563 -1.047 17.125 -1.062 13.688 C-1.071 12.711 -1.079 11.735 -1.088 10.729 C-1.091 9.791 -1.094 8.853 -1.098 7.887 C-1.106 6.591 -1.106 6.591 -1.114 5.268 C-1 3 -1 3 0 0 Z " fill="#F7F7F7" transform="translate(13,688)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.54 2.859 0.08 3.717 -0.395 4.602 C-2.321 8.679 -2.547 11.933 -2.688 16.375 C-2.722 17.111 -2.756 17.847 -2.791 18.605 C-2.873 20.403 -2.938 22.202 -3 24 C-3.66 23.67 -4.32 23.34 -5 23 C-6.986 16.445 -5.677 10.15 -3 4 C-2.043 2.636 -1.053 1.292 0 0 Z " fill="#AFAEAE" transform="translate(975,520)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 10.56 1 21.12 1 32 C-1.333 28.5 -1.635 26.156 -2 22 C-1.684 20.662 -1.35 19.329 -1 18 C-0.86 16.56 -0.756 15.117 -0.684 13.672 C-0.642 12.873 -0.6 12.073 -0.557 11.25 C-0.517 10.425 -0.478 9.6 -0.438 8.75 C-0.394 7.91 -0.351 7.069 -0.307 6.203 C-0.201 4.136 -0.1 2.068 0 0 Z " fill="#F9F9F9" transform="translate(1006,687)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.144 0.763 1.289 1.526 1.438 2.312 C1.963 5.082 1.963 5.082 3 8 C8.61 8.33 14.22 8.66 20 9 C20.66 10.32 21.32 11.64 22 13 C19.36 12.67 16.72 12.34 14 12 C14 11.67 14 11.34 14 11 C7.07 10.505 7.07 10.505 0 10 C0 6.7 0 3.4 0 0 Z " fill="#C7C9CA" transform="translate(403,675)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 11.22 1 22.44 1 34 C-1 31 -1 31 -1 28 C-1.66 27.67 -2.32 27.34 -3 27 C-2.01 25.515 -2.01 25.515 -1 24 C-0.633 21.387 -0.633 21.387 -0.586 18.457 C-0.547 17.384 -0.509 16.311 -0.469 15.205 C-0.438 14.086 -0.407 12.966 -0.375 11.812 C-0.336 10.681 -0.298 9.55 -0.258 8.385 C-0.163 5.59 -0.078 2.795 0 0 Z " fill="#535254" transform="translate(661,688)"/>
<path d="M0 0 C4.251 0.561 6.916 2.035 10.438 4.438 C13.408 6.462 15.831 7.945 19.262 9.059 C22 10 22 10 23.375 12.625 C23.581 13.409 23.788 14.192 24 15 C21.323 13.7 18.775 12.298 16.25 10.723 C15.559 10.291 14.868 9.86 14.156 9.416 C13.445 8.969 12.733 8.523 12 8.062 C11.288 7.62 10.577 7.177 9.844 6.721 C6.458 4.601 3.14 2.478 0 0 Z " fill="#74CDFC" transform="translate(382,322)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.649 2.619 1.299 3.237 0.938 3.875 C0.628 4.576 0.319 5.278 0 6 C0.33 6.66 0.66 7.32 1 8 C-2.3 8 -5.6 8 -9 8 C-8.67 6.02 -8.34 4.04 -8 2 C-5.36 2.33 -2.72 2.66 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C0C0C0" transform="translate(590,720)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-5.464 8.823 -5.464 8.823 -10.797 9.195 C-11.771 9.172 -12.746 9.149 -13.75 9.125 C-14.735 9.107 -15.72 9.089 -16.734 9.07 C-17.856 9.036 -17.856 9.036 -19 9 C-19 8.67 -19 8.34 -19 8 C-18.022 7.853 -18.022 7.853 -17.023 7.703 C-11.432 6.746 -7.563 5.562 -3 2 C-2.01 1.34 -1.02 0.68 0 0 Z " fill="#EAEAE9" transform="translate(821,716)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.217 2.149 4.423 4.294 3.625 6.438 C3.184 7.632 2.743 8.827 2.289 10.059 C1 13 1 13 -1 14 C-0.814 13.092 -0.629 12.185 -0.438 11.25 C-0.028 8.209 -0.24 6.849 -1 4 C-0.562 1.75 -0.562 1.75 0 0 Z " fill="#1A1A1A" transform="translate(443,687)"/>
<path d="M0 0 C0.592 0.191 1.183 0.382 1.793 0.578 C0.803 0.908 -0.187 1.238 -1.207 1.578 C-1.537 2.568 -1.867 3.558 -2.207 4.578 C-2.867 4.248 -3.527 3.918 -4.207 3.578 C-4.207 2.918 -4.207 2.258 -4.207 1.578 C-6.659 1.684 -6.659 1.684 -9.207 2.578 C-10.582 5.054 -10.582 5.054 -11.207 7.578 C-12.197 7.578 -13.187 7.578 -14.207 7.578 C-13.278 4.539 -12.208 2.579 -10.145 0.141 C-6.242 -1.935 -4.128 -1.158 0 0 Z " fill="#D9D9DA" transform="translate(276.20703125,691.421875)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.96 1 7.92 1 12 C7.54 11.487 10.469 9.531 15 5 C14.674 8.043 14.021 8.98 11.75 11.125 C7.632 13.933 5.017 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#E1B9A4" transform="translate(725,344)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.703 2.769 0.95 4.062 -0.812 6.25 C-5.701 8.934 -10.538 8.314 -16 8 C-16 7.67 -16 7.34 -16 7 C-15.18 6.83 -14.36 6.66 -13.516 6.484 C-12.438 6.242 -11.36 6 -10.25 5.75 C-9.183 5.518 -8.115 5.286 -7.016 5.047 C-3.713 3.9 -2.327 2.537 0 0 Z " fill="#E9E9E9" transform="translate(286,717)"/>
<path d="M0 0 C6.155 -0.641 10.273 -0.014 15.402 3.555 C17.501 5.453 18.373 7.257 19 10 C15.171 9.453 13.562 7.81 11 5 C10.587 4.443 10.175 3.886 9.75 3.312 C6.828 1.121 3.588 1.294 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DADBDC" transform="translate(270,683)"/>
<path d="M0 0 C1.236 0.017 1.236 0.017 2.496 0.035 C3.322 0.044 4.149 0.053 5 0.062 C5.638 0.074 6.276 0.086 6.934 0.098 C7.264 0.758 7.594 1.418 7.934 2.098 C6.944 3.583 6.944 3.583 5.934 5.098 C5.934 3.778 5.934 2.458 5.934 1.098 C5.604 1.758 5.274 2.418 4.934 3.098 C2.954 3.098 0.974 3.098 -1.066 3.098 C-1.066 4.418 -1.066 5.738 -1.066 7.098 C-3.046 8.088 -3.046 8.088 -5.066 9.098 C-4.758 7.784 -4.445 6.472 -4.129 5.16 C-3.955 4.429 -3.781 3.698 -3.602 2.945 C-2.862 0.392 -2.695 0.127 0 0 Z " fill="#CCCDCD" transform="translate(444.06640625,683.90234375)"/>
<path d="M0 0 C1.236 0.017 1.236 0.017 2.496 0.035 C3.322 0.044 4.149 0.053 5 0.062 C5.638 0.074 6.276 0.086 6.934 0.098 C6.934 0.428 6.934 0.758 6.934 1.098 C4.294 1.098 1.654 1.098 -1.066 1.098 C-0.736 4.728 -0.406 8.358 -0.066 12.098 C-1.015 12.221 -1.964 12.345 -2.941 12.473 C-6.153 12.815 -6.153 12.815 -8.066 15.098 C-8.066 13.448 -8.066 11.798 -8.066 10.098 C-6.416 9.768 -4.766 9.438 -3.066 9.098 C-3.087 7.798 -3.108 6.499 -3.129 5.16 C-3.15 3.806 -3.138 2.45 -3.066 1.098 C-2.066 0.098 -2.066 0.098 0 0 Z " fill="#DBDBDC" transform="translate(51.06640625,673.90234375)"/>
<path d="M0 0 C4.667 4 9.333 8 14 12 C9.029 12 5.847 9.718 2.125 6.562 C0 4 0 4 0 0 Z " fill="#48B8FC" transform="translate(634,540)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.517 9.41 2.517 9.41 0 14 C-4.455 14.495 -4.455 14.495 -9 15 C-9 13 -9 13 -7 11 C-5.35 10.67 -3.7 10.34 -2 10 C-1.34 6.7 -0.68 3.4 0 0 Z " fill="#C8C8C8" transform="translate(710,712)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 7.26 1 14.52 1 22 C0.34 22 -0.32 22 -1 22 C-2.517 7.403 -2.517 7.403 0 0 Z " fill="#F8F8F9" transform="translate(658,689)"/>
<path d="M0 0 C1 3 1 3 0.812 6.875 C0.807 9.854 1.021 11.043 2.375 13.812 C6.155 16.962 10.269 17.889 15 19 C15 19.33 15 19.66 15 20 C9.671 19.541 5.602 18.958 1 16 C-0.986 12.923 -1.247 10.498 -1.188 6.875 C-1.178 5.572 -1.178 5.572 -1.168 4.242 C-1 2 -1 2 0 0 Z " fill="#ACAAA9" transform="translate(984,528)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C4.874 6.55 6.81 8.263 11.312 8.812 C13.138 8.905 13.138 8.905 15 9 C15 9.33 15 9.66 15 10 C11.642 10.659 8.427 11.131 5 11 C1.611 8.51 0.323 6.97 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#D8D8D8" transform="translate(633,714)"/>
<path d="M0 0 C0 9.57 0 19.14 0 29 C-0.33 29 -0.66 29 -1 29 C-1.33 20.09 -1.66 11.18 -2 2 C-2.99 1.67 -3.98 1.34 -5 1 C-2 0 -2 0 0 0 Z " fill="#FAFAFB" transform="translate(51,690)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.94 1 11.88 1 18 C2.65 18 4.3 18 6 18 C6.66 18.99 7.32 19.98 8 21 C4.04 21.495 4.04 21.495 0 22 C0 14.74 0 7.48 0 0 Z " fill="#E9EAEA" transform="translate(376,705)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.208 6.042 2.158 11.93 1 18 C0.34 18.66 -0.32 19.32 -1 20 C-1.029 16.854 -1.047 13.708 -1.062 10.562 C-1.071 9.665 -1.079 8.767 -1.088 7.842 C-1.091 6.988 -1.094 6.134 -1.098 5.254 C-1.106 4.068 -1.106 4.068 -1.114 2.858 C-1 1 -1 1 0 0 Z " fill="#F8F8F8" transform="translate(709,702)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 8.91 7 17.82 7 27 C6.67 27 6.34 27 6 27 C6 19.08 6 11.16 6 3 C5.01 2.67 4.02 2.34 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#D8D8D8" transform="translate(388,692)"/>
<path d="M0 0 C2.741 1.827 3.814 3.323 5.062 6.375 C4.25 9.688 4.25 9.688 3.062 12.375 C2.732 12.375 2.403 12.375 2.062 12.375 C1.072 7.425 1.072 7.425 0.062 2.375 C-1.918 3.365 -1.918 3.365 -3.938 4.375 C-3.938 3.055 -3.938 1.735 -3.938 0.375 C-1.938 -0.625 -1.938 -0.625 0 0 Z " fill="#D9D9D9" transform="translate(812.9375,692.625)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.312 3.312 1.312 3.312 1 7 C-1.5 8.938 -1.5 8.938 -4 10 C-4 9.34 -4 8.68 -4 8 C-5.98 8 -7.96 8 -10 8 C-10 7.67 -10 7.34 -10 7 C-7.36 7 -4.72 7 -2 7 C-2 5.35 -2 3.7 -2 2 C-5.3 2 -8.6 2 -12 2 C-12 1.67 -12 1.34 -12 1 C-11.218 1.012 -10.435 1.023 -9.629 1.035 C-8.617 1.044 -7.605 1.053 -6.562 1.062 C-5.048 1.08 -5.048 1.08 -3.504 1.098 C-1.06 1.286 -1.06 1.286 0 0 Z " fill="#C5C6C7" transform="translate(316,685)"/>
<path d="M0 0 C0.027 1.917 0.046 3.833 0.062 5.75 C0.074 6.817 0.086 7.885 0.098 8.984 C0.015 11.534 -0.254 13.579 -1 16 C-1.66 15.67 -2.32 15.34 -3 15 C-2.965 14.06 -2.965 14.06 -2.93 13.102 C-2.903 11.876 -2.903 11.876 -2.875 10.625 C-2.852 9.813 -2.829 9.001 -2.805 8.164 C-2.786 5.787 -2.786 5.787 -5 4 C-4.67 3.01 -4.34 2.02 -4 1 C-2 0 -2 0 0 0 Z " fill="#EDEEEF" transform="translate(774,687)"/>
<path d="M0 0 C1.212 0.014 1.212 0.014 2.449 0.027 C3.372 0.045 3.372 0.045 4.312 0.062 C4.643 1.053 4.972 2.043 5.312 3.062 C0.362 3.393 -4.587 3.722 -9.688 4.062 C-6.921 -0.087 -4.68 -0.068 0 0 Z " fill="#E7E9E9" transform="translate(806.6875,682.9375)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 4.96 3 8.92 3 13 C2.01 13 1.02 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#D0D1D0" transform="translate(0,669)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C6.592 9.41 6.592 9.41 6 14 C2.51 10.976 1.119 8.475 0 4 C0 2.68 0 1.36 0 0 Z " fill="#E8E8E8" transform="translate(344,709)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.049 1.181 1.098 2.362 1.148 3.578 C1.223 5.135 1.299 6.693 1.375 8.25 C1.406 9.027 1.437 9.805 1.469 10.605 C1.673 14.582 1.921 17.512 4 21 C4.125 23.75 4.125 23.75 4 26 C1.014 23.533 0.52 21.796 0 18 C-0.113 14.894 -0.094 11.795 -0.062 8.688 C-0.058 7.852 -0.053 7.016 -0.049 6.154 C-0.037 4.103 -0.019 2.051 0 0 Z " fill="#D5D4D3" transform="translate(1020,699)"/>
<path d="M0 0 C0 5.94 0 11.88 0 18 C-0.33 18 -0.66 18 -1 18 C-1.33 15.69 -1.66 13.38 -2 11 C-2.99 11.495 -2.99 11.495 -4 12 C-3.629 11.072 -3.258 10.144 -2.875 9.188 C-2.586 8.136 -2.298 7.084 -2 6 C-2.66 5.01 -3.32 4.02 -4 3 C-4 2.34 -4 1.68 -4 1 C-1 0 -1 0 0 0 Z " fill="#DDDDDC" transform="translate(984,684)"/>
<path d="M0 0 C-2.286 1.531 -4.578 3.05 -6.875 4.562 C-7.526 5 -8.177 5.438 -8.848 5.889 C-10.526 6.988 -12.26 8.001 -14 9 C-14.66 8.67 -15.32 8.34 -16 8 C-5.225 -1.595 -5.225 -1.595 0 0 Z " fill="#62C6FC" transform="translate(413,547)"/>
<path d="M0 0 C-3.604 2.403 -7.209 3.477 -11.309 4.805 C-14.247 6.11 -15.805 7.694 -18 10 C-18.66 10 -19.32 10 -20 10 C-18.572 6.4 -17.023 5.102 -13.625 3.312 C-12.854 2.896 -12.083 2.48 -11.289 2.051 C-7.425 0.277 -4.237 -0.118 0 0 Z " fill="#16A7FE" transform="translate(758,345)"/>
<path d="M0 0 C4.413 3.152 7.008 6.555 10 11 C9.67 11.66 9.34 12.32 9 13 C5.436 10.327 2.503 7.708 0 4 C0 2.68 0 1.36 0 0 Z " fill="#58C2FB" transform="translate(434,341)"/>
<path d="M0 0 C1.32 3.63 2.64 7.26 4 11 C3.01 11.33 2.02 11.66 1 12 C0.734 11.374 0.469 10.747 0.195 10.102 C-0.158 9.284 -0.511 8.467 -0.875 7.625 C-1.397 6.407 -1.397 6.407 -1.93 5.164 C-2.924 2.862 -2.924 2.862 -5 1 C-3 0 -3 0 0 0 Z " fill="#DADADB" transform="translate(342,698)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-1.023 4.255 -4.141 6.783 -8 9 C-8.99 8.67 -9.98 8.34 -11 8 C-7.716 4.322 -4.372 2.186 0 0 Z " fill="#4EC0FD" transform="translate(257,311)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.205 0.554 4.41 1.109 4.621 1.68 C4.891 2.404 5.16 3.129 5.438 3.875 C5.704 4.594 5.971 5.314 6.246 6.055 C6.979 8.036 6.979 8.036 8 10 C6.35 9.67 4.7 9.34 3 9 C2.01 6.03 1.02 3.06 0 0 Z " fill="#E3E3E3" transform="translate(253,708)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C3.062 1.938 3.062 1.938 3.438 4.062 C2.778 4.722 2.117 5.383 1.438 6.062 C-0.688 5.688 -0.688 5.688 -2.562 5.062 C-3.125 3.125 -3.125 3.125 -3.562 1.062 C-2.562 0.062 -2.562 0.062 0 0 Z " fill="#1B1D1D" transform="translate(776.5625,673.9375)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.609 4.211 -1.609 4.211 -3.75 5.375 C-7.324 7.46 -9.63 9.544 -12 13 C-11.583 8.136 -9.673 6.101 -6 3 C-2.688 1.188 -2.688 1.188 0 0 Z " fill="#ABAAA9" transform="translate(1023,411)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2.33 2.32 2.66 3 3 C3 3.66 3 4.32 3 5 C2.34 5 1.68 5 1 5 C1 5.99 1 6.98 1 8 C-1.97 8 -4.94 8 -8 8 C-7.67 6.68 -7.34 5.36 -7 4 C-4.062 4.375 -4.062 4.375 -1 5 C-0.67 5.66 -0.34 6.32 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D2D3D3" transform="translate(780,718)"/>
<path d="M0 0 C1.547 0.712 1.547 0.712 3.125 1.438 C6.385 2.845 9.461 3.568 13 4 C13 4.66 13 5.32 13 6 C5.783 6.489 5.783 6.489 2 4.125 C0 2 0 2 0 0 Z " fill="#EEEEEF" transform="translate(264,713)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.293 4.68 -0.293 4.68 -2.188 7.875 C-2.8 8.924 -3.412 9.974 -4.043 11.055 C-5.829 13.742 -7.632 15.826 -10 18 C-8.843 12.988 -7.703 10.485 -4 7 C-2.543 4.72 -1.284 2.385 0 0 Z " fill="#70CBFB" transform="translate(453,498)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C1.59 3.039 -0.089 5.594 -2.125 8.25 C-2.664 8.956 -3.203 9.663 -3.758 10.391 C-4.168 10.922 -4.578 11.453 -5 12 C-6 10 -6 10 -5.312 7.688 C-3.854 4.702 -2.28 2.418 0 0 Z " fill="#5AC4FD" transform="translate(230,353)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C1.66 4.34 2.32 3.68 3 3 C6.125 2.875 6.125 2.875 9 3 C9.33 2.01 9.66 1.02 10 0 C10.33 1.98 10.66 3.96 11 6 C7.04 6 3.08 6 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#C7C7C7" transform="translate(938,720)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.66 2.33 1.32 2.66 2 3 C1.34 4.32 0.68 5.64 0 7 C-0.66 5.68 -1.32 4.36 -2 3 C-4.31 3 -6.62 3 -9 3 C-8.67 2.01 -8.34 1.02 -8 0 C-4.713 -0.8 -3.29 -1.097 0 0 Z " fill="#C2C2C2" transform="translate(413,717)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.99 0.34 3.98 0 5 C-2.525 6.262 -4.312 6.099 -7.125 6.062 C-8.035 6.053 -8.945 6.044 -9.883 6.035 C-10.581 6.024 -11.28 6.012 -12 6 C-12 5.34 -12 4.68 -12 4 C-10.952 3.818 -10.952 3.818 -9.883 3.633 C-8.518 3.381 -8.518 3.381 -7.125 3.125 C-5.768 2.881 -5.768 2.881 -4.383 2.633 C-1.835 2.17 -1.835 2.17 0 0 Z " fill="#EAEAE9" transform="translate(813,713)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2 2.32 2 3 2 C1.509 5.795 -0.757 7.61 -4 10 C-4.66 9.01 -5.32 8.02 -6 7 C-5.196 6.216 -4.391 5.433 -3.562 4.625 C-1.013 2.28 -1.013 2.28 0 0 Z " fill="#D9DADA" transform="translate(656,709)"/>
<path d="M0 0 C2.422 3.759 2.267 7.175 2.25 11.562 C2.258 12.235 2.265 12.907 2.273 13.6 C2.259 17.284 2.096 18.856 0 22 C-0.196 18.709 -0.381 15.417 -0.562 12.125 C-0.619 11.188 -0.675 10.251 -0.732 9.285 C-0.781 8.389 -0.829 7.493 -0.879 6.57 C-0.926 5.743 -0.973 4.915 -1.022 4.063 C-1 2 -1 2 0 0 Z " fill="#E0E1E1" transform="translate(329,685)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C11 1.98 11 3.96 11 6 C10.67 4.68 10.34 3.36 10 2 C8.541 2.114 7.083 2.242 5.625 2.375 C4.813 2.445 4.001 2.514 3.164 2.586 C0.797 2.829 0.797 2.829 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#DFDFE0" transform="translate(2,685)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 1.65 4.66 3.3 5 5 C3.35 5.33 1.7 5.66 0 6 C-0.382 4.344 -0.714 2.675 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#1D1D20" transform="translate(732,674)"/>
<path d="M0 0 C1.75 0.125 1.75 0.125 4 1 C6.312 3.875 6.312 3.875 8 7 C7.67 7.99 7.34 8.98 7 10 C6.34 8.35 5.68 6.7 5 5 C3.35 5.33 1.7 5.66 0 6 C0 4.02 0 2.04 0 0 Z " fill="#414042" transform="translate(585,675)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C4.67 2.65 4.34 4.3 4 6 C2.35 5.67 0.7 5.34 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#212120" transform="translate(378,674)"/>
<path d="M0 0 C-0.99 1.485 -0.99 1.485 -2 3 C-3.667 3.333 -5.333 3.667 -7 4 C-7.99 5.485 -7.99 5.485 -9 7 C-9.66 6.67 -10.32 6.34 -11 6 C-10.398 3.98 -9.727 1.979 -9 0 C-5.931 -1.534 -3.299 -0.55 0 0 Z " fill="#C4C5C7" transform="translate(232,670)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 1.98 1.66 3.96 2 6 C4.64 6 7.28 6 10 6 C10 6.99 10 7.98 10 9 C8.376 9.081 6.75 9.139 5.125 9.188 C3.768 9.24 3.768 9.24 2.383 9.293 C1.203 9.148 1.203 9.148 0 9 C-2 6 -2 6 -1.688 3.938 C-1 2 -1 2 0 0 Z " fill="#DADADA" transform="translate(686,710)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.99 4.62 2.98 9.24 4 14 C3.01 14 2.02 14 1 14 C-1.366 10.451 -1.109 9.424 -0.625 5.312 C-0.514 4.319 -0.403 3.325 -0.289 2.301 C-0.194 1.542 -0.098 0.782 0 0 Z " fill="#E5E5E4" transform="translate(787,702)"/>
<path d="M0 0 C0.081 1.749 0.139 3.5 0.188 5.25 C0.222 6.225 0.257 7.199 0.293 8.203 C-0.039 11.375 -0.887 12.67 -3 15 C-3.768 10.053 -4.32 5.921 -3 1 C-1 0 -1 0 0 0 Z " fill="#C8C8C8" transform="translate(789,697)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C3 4.32 3 5.64 3 7 C3.99 6.67 4.98 6.34 6 6 C5.34 7.32 4.68 8.64 4 10 C2.68 9.67 1.36 9.34 0 9 C0 6.03 0 3.06 0 0 Z " fill="#E1E3E2" transform="translate(930,677)"/>
<path d="M0 0 C1.675 0.286 3.344 0.618 5 1 C4.67 2.32 4.34 3.64 4 5 C2.68 5.33 1.36 5.66 0 6 C-0.382 4.344 -0.714 2.675 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#1B1D1E" transform="translate(324,674)"/>
<path d="M0 0 C1.675 0.286 3.344 0.618 5 1 C4.67 2.65 4.34 4.3 4 6 C2.68 5.67 1.36 5.34 0 5 C-0.562 3.062 -0.562 3.062 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#1C1C1C" transform="translate(1007,674)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C2.67 2.66 2.34 3.32 2 4 C1.955 6.02 1.961 8.042 2 10.062 C2.038 12.041 2.047 14.021 2 16 C1 17 1 17 -2.062 17.062 C-3.032 17.042 -4.001 17.021 -5 17 C-3.68 16.67 -2.36 16.34 -1 16 C-0.67 10.72 -0.34 5.44 0 0 Z " fill="#E9EAEA" transform="translate(624,708)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C-2.816 7.976 -2.816 7.976 -7.125 8.062 C-8.548 8.032 -8.548 8.032 -10 8 C-7.829 6.311 -5.694 4.822 -3.312 3.438 C-0.886 2.209 -0.886 2.209 0 0 Z " fill="#E8E7E8" transform="translate(29,711)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.66 10 1.32 10 2 C7.36 2 4.72 2 2 2 C1.67 4.31 1.34 6.62 1 9 C0.67 9 0.34 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#F2F1F2" transform="translate(304,690)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9.33 0.66 9.66 1.32 10 2 C9.34 2 8.68 2 8 2 C7.67 2.66 7.34 3.32 7 4 C4.69 3.67 2.38 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D4D7D6" transform="translate(772,684)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.67 3.34 0.34 2.68 0 2 C-3.63 2 -7.26 2 -11 2 C-8.745 -2.51 -4.157 -0.706 0 0 Z " fill="#E9EBEC" transform="translate(723,684)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.619 1.814 2.238 1.629 2.875 1.438 C5 1 5 1 7 2 C7.625 4.062 7.625 4.062 8 6 C7.34 6 6.68 6 6 6 C6 5.01 6 4.02 6 3 C4.207 3.06 2.416 3.149 0.625 3.25 C-0.373 3.296 -1.37 3.343 -2.398 3.391 C-5.153 3.722 -5.153 3.722 -6.352 6.047 C-6.566 6.691 -6.78 7.336 -7 8 C-7 6.02 -7 4.04 -7 2 C-4.692 0.846 -2.528 0.466 0 0 Z " fill="#C2C2C2" transform="translate(7,682)"/>
<path d="M0 0 C4.785 2.485 7.861 4.48 11 9 C5.738 7.588 2.278 5.251 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#5CC3FC" transform="translate(245,549)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.095 2.537 -1.822 4.054 -3.75 5.562 C-4.289 6 -4.828 6.438 -5.383 6.889 C-6.984 8.129 -6.984 8.129 -10 10 C-12.188 9.617 -12.188 9.617 -14 9 C-11.714 7.469 -9.422 5.95 -7.125 4.438 C-6.149 3.781 -6.149 3.781 -5.152 3.111 C-3.474 2.012 -1.74 0.999 0 0 Z " fill="#B0B0B1" transform="translate(771,286)"/>
<path d="M0 0 C1.052 0.928 1.052 0.928 2.125 1.875 C5.058 4.043 7.512 5.027 11 6 C11 6.33 11 6.66 11 7 C3.615 7.492 3.615 7.492 0.5 5 C0.005 4.34 -0.49 3.68 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#EDECEC" transform="translate(396,718)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.99 10 1.98 10 3 C6.37 3 2.74 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#141515" transform="translate(756,687)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C10.01 1.485 10.01 1.485 9 3 C7.055 3.293 7.055 3.293 4.875 3.188 C3.596 3.126 2.317 3.064 1 3 C0.67 3.66 0.34 4.32 0 5 C-0.66 5 -1.32 5 -2 5 C-1.34 3.35 -0.68 1.7 0 0 Z " fill="#CFD0D0" transform="translate(981,670)"/>
<path d="M0 0 C1.791 -0.081 3.583 -0.139 5.375 -0.188 C6.872 -0.24 6.872 -0.24 8.398 -0.293 C9.257 -0.196 10.115 -0.1 11 0 C11.66 0.99 12.32 1.98 13 3 C9.37 3.33 5.74 3.66 2 4 C2 3.34 2 2.68 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C8C7C7" transform="translate(798,723)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C-0.98 5.32 -2.96 6.64 -5 8 C-5.33 7.01 -5.66 6.02 -6 5 C-4.067 2.975 -2.368 1.579 0 0 Z " fill="#1F1E20" transform="translate(30,715)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.551 2.482 2.09 3.961 1.625 5.438 C1.242 6.673 1.242 6.673 0.852 7.934 C0 10 0 10 -2 11 C-1.125 3.375 -1.125 3.375 0 0 Z " fill="#E7E6E6" transform="translate(362,706)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 1.33 2.34 1.66 2 2 C1.844 3.34 1.75 4.688 1.684 6.035 C1.642 6.844 1.6 7.653 1.557 8.486 C1.517 9.336 1.478 10.187 1.438 11.062 C1.394 11.917 1.351 12.771 1.307 13.65 C1.2 15.767 1.1 17.883 1 20 C0.34 19.67 -0.32 19.34 -1 19 C-1.027 16.021 -1.047 13.042 -1.062 10.062 C-1.071 9.212 -1.079 8.362 -1.088 7.486 C-1.091 6.677 -1.094 5.869 -1.098 5.035 C-1.103 4.286 -1.108 3.537 -1.114 2.766 C-1 1 -1 1 0 0 Z " fill="#EBECEC" transform="translate(1005,685)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 2.97 4 5.94 4 9 C3.34 9 2.68 9 2 9 C0 7 0 7 -0.125 3.375 C-0.084 2.261 -0.043 1.148 0 0 Z " fill="#0E0F0F" transform="translate(680,677)"/>
<path d="M0 0 C2.5 0.188 2.5 0.188 5 1 C7 4 7 4 6.625 6.188 C6.419 6.786 6.212 7.384 6 8 C5.34 7.67 4.68 7.34 4 7 C3.67 6.01 3.34 5.02 3 4 C0.496 2.781 0.496 2.781 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#D7D7D8" transform="translate(1008,671)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C-2 12 -2 12 -2.188 9.312 C-2.126 8.549 -2.064 7.786 -2 7 C-1.67 7 -1.34 7 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#D8D8D8" transform="translate(58,704)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.94 1.605 2.851 3.209 2.75 4.812 C2.704 5.706 2.657 6.599 2.609 7.52 C2 10 2 10 -2 13 C-1.34 8.71 -0.68 4.42 0 0 Z " fill="#3B3A3C" transform="translate(441,693)"/>
<path d="M0 0 C4.833 0.414 6.827 2.464 10 6 C9.01 6.99 8.02 7.98 7 9 C6.587 8.072 6.175 7.144 5.75 6.188 C4.066 3.119 2.915 1.81 0 0 Z " fill="#3C3C3D" transform="translate(809,688)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.66 5 1.32 5 2 C7.475 2.99 7.475 2.99 10 4 C9.67 4.66 9.34 5.32 9 6 C8.01 5.34 7.02 4.68 6 4 C3.721 3.546 3.721 3.546 1.312 3.375 C0.504 3.3 -0.304 3.225 -1.137 3.148 C-1.752 3.099 -2.366 3.05 -3 3 C-2.01 2.67 -1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#1A1B1B" transform="translate(804,687)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 2.31 1.34 4.62 1 7 C-1.31 7.33 -3.62 7.66 -6 8 C-6 7.34 -6 6.68 -6 6 C-5.196 5.753 -4.391 5.505 -3.562 5.25 C-0.829 4.278 -0.829 4.278 -0.188 1.875 C-0.126 1.256 -0.064 0.638 0 0 Z " fill="#EBECED" transform="translate(780,674)"/>
<path d="M0 0 C6.75 1.625 6.75 1.625 9 5 C6.03 5 3.06 5 0 5 C-0.33 3.68 -0.66 2.36 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D3D3D3" transform="translate(616,721)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 2.64 2.66 5.28 3 8 C4.32 8.33 5.64 8.66 7 9 C5.02 9 3.04 9 1 9 C0.67 6.03 0.34 3.06 0 0 Z " fill="#EBEBEB" transform="translate(1004,716)"/>
<path d="M0 0 C2.528 1.264 2.764 2.528 4 5 C4.66 5.33 5.32 5.66 6 6 C6 8.84 5.597 11.237 5 14 C3.383 10.99 2.324 8.029 1.375 4.75 C1.115 3.858 0.854 2.966 0.586 2.047 C0.393 1.371 0.199 0.696 0 0 Z " fill="#4C4B4D" transform="translate(428,710)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.65 1.34 3.3 1 5 C3.97 5.495 3.97 5.495 7 6 C7 6.33 7 6.66 7 7 C4.667 7.042 2.333 7.041 0 7 C-1 6 -1 6 -1.125 3.5 C-1 1 -1 1 0 0 Z " fill="#EEEEEE" transform="translate(417,729)"/>
<path d="M0 0 C-0.33 1.32 -0.66 2.64 -1 4 C-4.3 4 -7.6 4 -11 4 C-11.33 3.34 -11.66 2.68 -12 2 C-10.35 2 -8.7 2 -7 2 C-7 1.67 -7 1.34 -7 1 C-2.25 0 -2.25 0 0 0 Z " fill="#131313" transform="translate(766,719)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C9.67 0.99 9.34 1.98 9 3 C5.7 3 2.4 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#474649" transform="translate(644,719)"/>
<path d="M0 0 C2.951 1.476 3.069 3.501 4.125 6.562 C4.478 7.574 4.831 8.586 5.195 9.629 C5.461 10.411 5.726 11.194 6 12 C5.01 12 4.02 12 3 12 C2.494 10.564 1.996 9.126 1.5 7.688 C1.222 6.887 0.943 6.086 0.656 5.262 C0 3 0 3 0 0 Z " fill="#1E1F1E" transform="translate(245,711)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.67 2.97 4.34 5.94 4 9 C3.01 9.33 2.02 9.66 1 10 C1.557 6.656 2.352 3.967 4 1 C2.68 0.67 1.36 0.34 0 0 Z " fill="#F0F0F1" transform="translate(445,685)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.33 0.99 7.66 1.98 8 3 C5.36 3 2.72 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#DDDEDD" transform="translate(830,723)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-3.3 3 -6.6 3 -10 3 C-10 2.34 -10 1.68 -10 1 C-3.375 0 -3.375 0 0 0 Z " fill="#EAE9E9" transform="translate(651,723)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 3.64 3 6.28 3 9 C2.01 8.34 1.02 7.68 0 7 C-0.188 3.375 -0.188 3.375 0 0 Z " fill="#3E3D3E" transform="translate(617,713)"/>
<path d="M0 0 C0 1.32 0 2.64 0 4 C-2.31 4 -4.62 4 -7 4 C-7.33 3.01 -7.66 2.02 -8 1 C-5.291 -0.354 -2.991 -0.065 0 0 Z " fill="#0C0D0C" transform="translate(722,686)"/>
<path d="M0 0 C1.279 -0.062 2.558 -0.124 3.875 -0.188 C4.954 -0.24 4.954 -0.24 6.055 -0.293 C6.697 -0.196 7.339 -0.1 8 0 C8.66 0.99 9.32 1.98 10 3 C6.672 4.109 5.301 4.016 2 3 C1.34 2.01 0.68 1.02 0 0 Z " fill="#D2D3D4" transform="translate(830,684)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.25 2.312 1.25 2.312 1 5 C-0.812 6.875 -0.812 6.875 -3 8 C-3.99 7.67 -4.98 7.34 -6 7 C-6 6.34 -6 5.68 -6 5 C-5.381 4.918 -4.763 4.835 -4.125 4.75 C-1.816 4.187 -1.816 4.187 -0.75 1.938 C-0.503 1.298 -0.255 0.659 0 0 Z " fill="#EDEEEF" transform="translate(738,675)"/>
<path d="M0 0 C-0.99 0.33 -1.98 0.66 -3 1 C-3 1.66 -3 2.32 -3 3 C-5.97 3 -8.94 3 -12 3 C-11.67 2.34 -11.34 1.68 -11 1 C-7.318 -0.052 -3.809 -0.089 0 0 Z " fill="#AAA9A8" transform="translate(1003,470)"/>
<path d="M0 0 C0 4.246 -1.818 6.385 -4 10 C-4.33 10 -4.66 10 -5 10 C-4.508 3.477 -4.508 3.477 -1.938 1.062 C-0.978 0.537 -0.978 0.537 0 0 Z " fill="#60C5FD" transform="translate(223,365)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-3.97 3 -6.94 3 -10 3 C-8 0 -8 0 -5.562 -0.75 C-3 -1 -3 -1 0 0 Z " fill="#D2D2D2" transform="translate(1015,723)"/>
<path d="M0 0 C1.751 2.626 3.387 5.288 5 8 C3.35 7.67 1.7 7.34 0 7 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#363537" transform="translate(261,709)"/>
<path d="M0 0 C-0.33 2.97 -0.66 5.94 -1 9 C-1.66 9 -2.32 9 -3 9 C-3.688 6.188 -3.688 6.188 -4 3 C-1.8 0 -1.8 0 0 0 Z " fill="#EFEFEF" transform="translate(435,706)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.542 3.469 0.109 6.674 -1 10 C-1.66 10 -2.32 10 -3 10 C-2.25 3.375 -2.25 3.375 0 0 Z " fill="#F1F1F2" transform="translate(224,687)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 1.66 4 2.32 4 3 C1.094 5.018 -1.517 6.355 -5 7 C-3.859 3.577 -2.841 3.016 0 1 C0 0.67 0 0.34 0 0 Z " fill="#464649" transform="translate(800,686)"/>
<path d="M0 0 C2 2 2 2 2.195 3.945 C2.13 5.964 2.065 7.982 2 10 C1.01 10.495 1.01 10.495 0 11 C-0.195 9.543 -0.381 8.084 -0.562 6.625 C-0.667 5.813 -0.771 5.001 -0.879 4.164 C-1 2 -1 2 0 0 Z " fill="#E3E4E3" transform="translate(737,685)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.99 9 1.98 9 3 C5.7 3 2.4 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#080808" transform="translate(402,687)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-3.31 2.67 -5.62 2.34 -8 2 C-8 1.34 -8 0.68 -8 0 C-4.713 -0.8 -3.29 -1.097 0 0 Z " fill="#DCDDDD" transform="translate(853,683)"/>
<path d="M0 0 C2.97 0.33 5.94 0.66 9 1 C9 1.99 9 2.98 9 4 C6.36 4 3.72 4 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#131414" transform="translate(402,719)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.125 6.75 1.125 6.75 0 9 C-0.66 9 -1.32 9 -2 9 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#E8E8E8" transform="translate(969,710)"/>
<path d="M0 0 C2.993 1.098 3.846 1.677 5.25 4.625 C5.497 5.409 5.745 6.192 6 7 C5.01 7 4.02 7 3 7 C0 2.778 0 2.778 0 0 Z " fill="#E9E8E8" transform="translate(609,701)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.67 2.66 3.34 3.32 3 4 C2.34 4 1.68 4 1 4 C0.67 4.99 0.34 5.98 0 7 C-0.66 6.34 -1.32 5.68 -2 5 C-2 4.01 -2 3.02 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D7D7D7" transform="translate(2,688)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C-0.333 6.333 -2.667 8.667 -5 11 C-3.775 6.947 -2.301 3.556 0 0 Z " fill="#3F3F40" transform="translate(981,678)"/>
<path d="M0 0 C-2.762 2.762 -5.207 2.579 -9 3 C-8.67 2.01 -8.34 1.02 -8 0 C-5.138 -1.431 -3.066 -0.6 0 0 Z " fill="#DADBDD" transform="translate(384,672)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.33 2.31 6.66 4.62 7 7 C4 5 4 5 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#E9EAEB" transform="translate(227,671)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 0.99 5.34 1.98 5 3 C3.02 3.33 1.04 3.66 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#363536" transform="translate(419,730)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C5.812 2.562 5.812 2.562 4 4 C1.312 3.688 1.312 3.688 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#CACAC9" transform="translate(418,726)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C0.35 4.65 -1.3 6.3 -3 8 C-3.66 7.01 -4.32 6.02 -5 5 C-3.68 4.67 -2.36 4.34 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#E0DFE1" transform="translate(281,711)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 4.95 1.66 9.9 2 15 C1.01 15 0.02 15 -1 15 C-0.67 10.05 -0.34 5.1 0 0 Z " fill="#EBECEB" transform="translate(757,702)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.34 2.31 1.68 4.62 1 7 C0.01 7 -0.98 7 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z " fill="#DCDBDB" transform="translate(445,695)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-5.412 6 -5.412 6 -8 6 C-7.812 4.125 -7.812 4.125 -7 2 C-4.557 0.747 -2.755 0 0 0 Z " fill="#212123" transform="translate(941,678)"/>
<path d="M0 0 C4.455 1.98 4.455 1.98 9 4 C9 4.33 9 4.66 9 5 C6.69 5.33 4.38 5.66 2 6 C0 3 0 3 0 0 Z " fill="#E6E8E9" transform="translate(321,677)"/>
<path d="M0 0 C1.875 0.312 1.875 0.312 4 1 C6 4 6 4 6 7 C3 6 3 6 1.312 2.938 C0.879 1.968 0.446 0.999 0 0 Z " fill="#EBEBEC" transform="translate(590,675)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.814 1.619 2.629 2.237 2.438 2.875 C1.763 5.042 1.763 5.042 3 7 C2.01 7.33 1.02 7.66 0 8 C-1 7 -1 7 -1.125 4 C-1 1 -1 1 0 0 Z " fill="#E4E5E5" transform="translate(1005,673)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.625 1.875 3.625 1.875 4 4 C1.861 6.139 0.867 6.427 -2 7 C-1.34 6.01 -0.68 5.02 0 4 C-0.33 3.34 -0.66 2.68 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#404041" transform="translate(429,724)"/>
<path d="M0 0 C2 3 2 3 2 6 C-0.64 6 -3.28 6 -6 6 C-5.34 5.34 -4.68 4.68 -4 4 C-2.68 4 -1.36 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#F0F0F0" transform="translate(664,719)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.25 4.75 2.25 4.75 0 7 C-0.66 7 -1.32 7 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z " fill="#DCDCDB" transform="translate(822,710)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.66 3.96 2.32 7.92 3 12 C2.01 12 1.02 12 0 12 C-1.532 7.916 -0.631 4.205 0 0 Z " fill="#DBD9D8" transform="translate(263,702)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.99 2.97 3.98 5.94 5 9 C4.34 9 3.68 9 3 9 C2.711 8.216 2.423 7.433 2.125 6.625 C1.151 3.903 1.151 3.903 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#403E3F" transform="translate(424,701)"/>
<path d="M0 0 C6.055 -0.195 6.055 -0.195 8 0 C8.66 0.66 9.32 1.32 10 2 C6.063 3.312 3.875 2.243 0 1 C0 0.67 0 0.34 0 0 Z " fill="#F3F4F4" transform="translate(802,690)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1.66 1.68 2.32 1 3 C0.01 4.485 0.01 4.485 -1 6 C-1.99 5.67 -2.98 5.34 -4 5 C-2.25 1.125 -2.25 1.125 0 0 Z " fill="#161616" transform="translate(842,687)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.33 0.99 6.66 1.98 7 3 C4.69 3 2.38 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#0C0C0C" transform="translate(293,687)"/>
<path d="M0 0 C2 0 2 0 3.5 1.438 C3.995 1.953 4.49 2.469 5 3 C5.66 3.33 6.32 3.66 7 4 C7 4.99 7 5.98 7 7 C6.01 7.33 5.02 7.66 4 8 C2.68 5.36 1.36 2.72 0 0 Z " fill="#262425" transform="translate(588,680)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.75 3.375 2.75 3.375 3 6 C2.01 7.485 2.01 7.485 1 9 C0.01 9 -0.98 9 -2 9 C-1.67 8.01 -1.34 7.02 -1 6 C-0.34 6 0.32 6 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#D0D4D4" transform="translate(329,672)"/>
<path d="M0 0 C2.354 3.531 3.663 6.99 5 11 C2 10 2 10 0.812 8.312 C-0.193 5.45 -0.137 3.012 0 0 Z " fill="#ADABAB" transform="translate(971,499)"/>
<path d="M0 0 C-2.25 2.062 -2.25 2.062 -5 4 C-7.312 3.75 -7.312 3.75 -9 3 C-6.382 -0.01 -3.867 -1.933 0 0 Z " fill="#45BDFD" transform="translate(246,322)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-4.63 3 -8.26 3 -12 3 C-12 2.67 -12 2.34 -12 2 C-10.375 1.664 -8.75 1.332 -7.125 1 C-6.22 0.814 -5.315 0.629 -4.383 0.438 C-2 0 -2 0 0 0 Z " fill="#F8F8F8" transform="translate(814,722)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.65 2 3.3 2 5 C3.65 5 5.3 5 7 5 C7 5.33 7 5.66 7 6 C4.69 6 2.38 6 0 6 C-1.245 3.509 -0.777 2.589 0 0 Z " fill="#E3E3E4" transform="translate(403,713)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 2.65 4 4.3 4 6 C3.01 6.495 3.01 6.495 2 7 C1.34 4.69 0.68 2.38 0 0 Z " fill="#E1DFDF" transform="translate(996,703)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 2.31 2.66 4.62 3 7 C2.01 7 1.02 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#E8E7E8" transform="translate(427,717)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.222 1.456 1.427 2.915 1.625 4.375 C1.741 5.187 1.857 5.999 1.977 6.836 C1.984 7.55 1.992 8.264 2 9 C1.34 9.66 0.68 10.32 0 11 C-0.33 10.67 -0.66 10.34 -1 10 C-0.91 8.309 -0.754 6.62 -0.562 4.938 C-0.461 4.018 -0.359 3.099 -0.254 2.152 C-0.17 1.442 -0.086 0.732 0 0 Z " fill="#E3E2E3" transform="translate(750,711)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.625 2.5 3.625 2.5 5 5 C4.01 5.33 3.02 5.66 2 6 C1.01 4.35 0.02 2.7 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#DFDFDD" transform="translate(605,707)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 3.64 3 6.28 3 9 C0.903 6.64 0.031 5.454 -0.188 2.25 C-0.126 1.508 -0.064 0.765 0 0 Z " fill="#D5D5D6" transform="translate(708,698)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C11 0.33 11 0.66 11 1 C8.021 1.293 8.021 1.293 5.043 1.586 C2.7 1.927 2.7 1.927 0 4 C0 2.68 0 1.36 0 0 Z " fill="#E8E9EA" transform="translate(684,690)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.33 0.99 6.66 1.98 7 3 C4.69 3 2.38 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#0A0A0A" transform="translate(744,687)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C7.35 1 5.7 1 4 1 C3.67 2.32 3.34 3.64 3 5 C2.01 3.35 1.02 1.7 0 0 Z " fill="#F2F2F2" transform="translate(413,685)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.99 7 1.98 7 3 C4.625 3.188 4.625 3.188 2 3 C1.34 2.01 0.68 1.02 0 0 Z " fill="#D0D3D4" transform="translate(616,670)"/>
<path d="M0 0 C2.375 0.188 2.375 0.188 5 1 C6.312 3.562 6.312 3.562 7 6 C4.506 4.796 2.319 3.546 0 2 C0 1.34 0 0.68 0 0 Z " fill="#5DC5FD" transform="translate(399,331)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C3C3C4" transform="translate(827,700)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.66 3 2.32 3 3 C2.34 3 1.68 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#B5B5B5" transform="translate(792,723)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C6C8C9" transform="translate(318,686)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BEBFC0" transform="translate(852,697)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#AEAFB0" transform="translate(209,698)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#A7A8A7" transform="translate(835,727)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B0B0AE" transform="translate(728,726)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BEBCBD" transform="translate(785,715)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BDBDBE" transform="translate(222,709)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BABABB" transform="translate(600,707)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C6C5C6" transform="translate(827,696)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C6C8C9" transform="translate(628,692)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#C5C8C6" transform="translate(672,694)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BEBEBE" transform="translate(28,692)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C3C4C5" transform="translate(936,689)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C5C6C8" transform="translate(698,687)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B8BFC0" transform="translate(396,672)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#ACACAC" transform="translate(944,727)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B2B1B3" transform="translate(378,727)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#ABAFAE" transform="translate(680,725)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C0C0C1" transform="translate(699,717)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BBBDBE" transform="translate(744,715)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C1C1C0" transform="translate(272,715)"/>
<path d="" fill="#C4C2C2" transform="translate(0,0)"/>
<path d="" fill="#C6C6C4" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BDBCBC" transform="translate(849,695)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BCBCBE" transform="translate(844,694)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C8C8C8" transform="translate(759,694)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C8C9CA" transform="translate(693,694)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#55B9F2" transform="translate(291,573)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#5DB9F0" transform="translate(707,566)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#5BB5EC" transform="translate(657,562)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B9BBBA" transform="translate(969,504)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#54B3EE" transform="translate(603,492)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#51BDFA" transform="translate(277,468)"/>
<path d="" fill="#54BFFC" transform="translate(0,0)"/>
<path d="" fill="#5AB9F2" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#4EBDFB" transform="translate(433,374)"/>
<path d="" fill="#5BBEFC" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#54BBF9" transform="translate(697,322)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ABAAAC" transform="translate(1012,727)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B0B0B0" transform="translate(1008,727)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ABACAB" transform="translate(831,727)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B0AFB0" transform="translate(804,727)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AFB2AF" transform="translate(733,727)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2B2B1" transform="translate(324,727)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ADADAD" transform="translate(302,727)"/>
<path d="" fill="#B3B2B3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5B6B6" transform="translate(769,726)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3B1B2" transform="translate(398,726)"/>
<path d="" fill="#B2B3B3" transform="translate(0,0)"/>
<path d="" fill="#AAACAB" transform="translate(0,0)"/>
<path d="" fill="#B8B6B9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1B3B2" transform="translate(633,723)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0BFBF" transform="translate(424,723)"/>
<path d="" fill="#B4B3B3" transform="translate(0,0)"/>
<path d="" fill="#B5B1B3" transform="translate(0,0)"/>
<path d="" fill="#BAB9B8" transform="translate(0,0)"/>
<path d="" fill="#BCBBB9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B0B0B0" transform="translate(609,719)"/>
<path d="" fill="#B7B7B9" transform="translate(0,0)"/>
<path d="" fill="#B3B2B5" transform="translate(0,0)"/>
<path d="" fill="#BDBCBC" transform="translate(0,0)"/>
<path d="" fill="#C2C2C2" transform="translate(0,0)"/>
<path d="" fill="#C2C2C2" transform="translate(0,0)"/>
<path d="" fill="#BABBBB" transform="translate(0,0)"/>
<path d="" fill="#BFBFC0" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C0C0" transform="translate(405,712)"/>
<path d="" fill="#B7B7B7" transform="translate(0,0)"/>
<path d="" fill="#BBBBBE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C1C3C3" transform="translate(284,708)"/>
<path d="" fill="#ADAFAE" transform="translate(0,0)"/>
<path d="" fill="#C7C6C7" transform="translate(0,0)"/>
<path d="" fill="#C9C9CA" transform="translate(0,0)"/>
<path d="" fill="#C2C4C4" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6C7C7" transform="translate(996,700)"/>
<path d="" fill="#C9C9C9" transform="translate(0,0)"/>
<path d="" fill="#BDBEBD" transform="translate(0,0)"/>
<path d="" fill="#C4C5C6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3C4C4" transform="translate(801,695)"/>
<path d="" fill="#B7B9B7" transform="translate(0,0)"/>
<path d="" fill="#C5C5C5" transform="translate(0,0)"/>
<path d="" fill="#C6C7CA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3C5C7" transform="translate(315,694)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4C7C7" transform="translate(276,694)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDBDBD" transform="translate(60,694)"/>
<path d="" fill="#C6C7C9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C1C3C2" transform="translate(822,689)"/>
<path d="" fill="#BFC0BF" transform="translate(0,0)"/>
<path d="" fill="#C6C9CA" transform="translate(0,0)"/>
<path d="" fill="#C2C6C6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFC4C4" transform="translate(811,682)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDC3C4" transform="translate(661,682)"/>
<path d="" fill="#B8BDBC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C5C7C9" transform="translate(1021,680)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC1C4" transform="translate(670,680)"/>
<path d="" fill="#BBC3C6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5A7AB" transform="translate(216,675)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BABDBE" transform="translate(976,673)"/>
<path d="" fill="#B4BEC2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6BCBD" transform="translate(401,672)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1A7AC" transform="translate(228,668)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#54BAF3" transform="translate(295,574)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#61BDF1" transform="translate(673,567)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#5DBAF0" transform="translate(731,566)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C0BF" transform="translate(1010,564)"/>
<path d="" fill="#51B3ED" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#50BAF8" transform="translate(353,499)"/>
<path d="" fill="#51B7F0" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C1C1" transform="translate(997,468)"/>
<path d="" fill="#50BCFE" transform="translate(0,0)"/>
<path d="" fill="#51B7F0" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2C3C4" transform="translate(1001,467)"/>
<path d="" fill="#5AB7EC" transform="translate(0,0)"/>
<path d="" fill="#55BAF3" transform="translate(0,0)"/>
<path d="" fill="#5AB7F0" transform="translate(0,0)"/>
<path d="" fill="#BEBFC1" transform="translate(0,0)"/>
<path d="" fill="#58B6EB" transform="translate(0,0)"/>
<path d="" fill="#BABCBE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#4DBCFA" transform="translate(429,367)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#5FB8EC" transform="translate(801,346)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#5ABAF7" transform="translate(671,339)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ABB5BC" transform="translate(752,296)"/>
<path d="" fill="#B0AEAB" transform="translate(0,0)"/>
<path d="" fill="#ADB2AE" transform="translate(0,0)"/>
<path d="" fill="#B2B0B1" transform="translate(0,0)"/>
<path d="" fill="#B2B2B3" transform="translate(0,0)"/>
<path d="" fill="#AFB0AE" transform="translate(0,0)"/>
<path d="" fill="#A2A4A3" transform="translate(0,0)"/>
<path d="" fill="#B0B0B4" transform="translate(0,0)"/>
<path d="" fill="#B2B3B3" transform="translate(0,0)"/>
<path d="" fill="#B3B6B3" transform="translate(0,0)"/>
<path d="" fill="#B0B1B0" transform="translate(0,0)"/>
<path d="" fill="#AEAFAE" transform="translate(0,0)"/>
<path d="" fill="#B0AFAE" transform="translate(0,0)"/>
<path d="" fill="#B3B3B6" transform="translate(0,0)"/>
<path d="" fill="#B0B5B2" transform="translate(0,0)"/>
<path d="" fill="#B3B3B4" transform="translate(0,0)"/>
<path d="" fill="#B3B3B5" transform="translate(0,0)"/>
<path d="" fill="#AFAFB1" transform="translate(0,0)"/>
<path d="" fill="#AEB0AE" transform="translate(0,0)"/>
<path d="" fill="#ADAFAC" transform="translate(0,0)"/>
<path d="" fill="#B6B5B6" transform="translate(0,0)"/>
<path d="" fill="#B1B3B2" transform="translate(0,0)"/>
<path d="" fill="#B6B3B1" transform="translate(0,0)"/>
<path d="" fill="#B0B2B0" transform="translate(0,0)"/>
<path d="" fill="#ACAEAC" transform="translate(0,0)"/>
<path d="" fill="#AFB3B2" transform="translate(0,0)"/>
<path d="" fill="#B0B0B2" transform="translate(0,0)"/>
<path d="" fill="#B0B2AD" transform="translate(0,0)"/>
<path d="" fill="#AAADAC" transform="translate(0,0)"/>
<path d="" fill="#ADAEB2" transform="translate(0,0)"/>
<path d="" fill="#A3A1A1" transform="translate(0,0)"/>
<path d="" fill="#A8AAA8" transform="translate(0,0)"/>
<path d="" fill="#A8A6A6" transform="translate(0,0)"/>
<path d="" fill="#B7B7B6" transform="translate(0,0)"/>
<path d="" fill="#A9AAAA" transform="translate(0,0)"/>
<path d="" fill="#AEAFAE" transform="translate(0,0)"/>
<path d="" fill="#B0AFB1" transform="translate(0,0)"/>
<path d="" fill="#B2B4B3" transform="translate(0,0)"/>
<path d="" fill="#B3B7B6" transform="translate(0,0)"/>
<path d="" fill="#B1B2B0" transform="translate(0,0)"/>
<path d="" fill="#B0B2B4" transform="translate(0,0)"/>
<path d="" fill="#B3B4B4" transform="translate(0,0)"/>
<path d="" fill="#9C9A9D" transform="translate(0,0)"/>
<path d="" fill="#ADACAC" transform="translate(0,0)"/>
<path d="" fill="#ADACAC" transform="translate(0,0)"/>
<path d="" fill="#B5B3B5" transform="translate(0,0)"/>
<path d="" fill="#B7B8B8" transform="translate(0,0)"/>
<path d="" fill="#A7A5A7" transform="translate(0,0)"/>
<path d="" fill="#B9BCB9" transform="translate(0,0)"/>
<path d="" fill="#AEAEAF" transform="translate(0,0)"/>
<path d="" fill="#AAABAC" transform="translate(0,0)"/>
<path d="" fill="#BABCB8" transform="translate(0,0)"/>
<path d="" fill="#B1B0B1" transform="translate(0,0)"/>
<path d="" fill="#B4B0B3" transform="translate(0,0)"/>
<path d="" fill="#B4B1B0" transform="translate(0,0)"/>
<path d="" fill="#BCBABC" transform="translate(0,0)"/>
<path d="" fill="#B7B6B6" transform="translate(0,0)"/>
<path d="" fill="#B3B2B3" transform="translate(0,0)"/>
<path d="" fill="#AAAAA9" transform="translate(0,0)"/>
<path d="" fill="#B7B6B6" transform="translate(0,0)"/>
<path d="" fill="#B7B8B5" transform="translate(0,0)"/>
<path d="" fill="#B5B6B7" transform="translate(0,0)"/>
<path d="" fill="#B3B4B3" transform="translate(0,0)"/>
<path d="" fill="#BAB9B5" transform="translate(0,0)"/>
<path d="" fill="#A4A5A4" transform="translate(0,0)"/>
<path d="" fill="#B7B5B6" transform="translate(0,0)"/>
<path d="" fill="#BDBCBD" transform="translate(0,0)"/>
<path d="" fill="#BCBDBD" transform="translate(0,0)"/>
<path d="" fill="#BABCB7" transform="translate(0,0)"/>
<path d="" fill="#BCBDC0" transform="translate(0,0)"/>
<path d="" fill="#BCC0BE" transform="translate(0,0)"/>
<path d="" fill="#B5B7B8" transform="translate(0,0)"/>
<path d="" fill="#B3B2B2" transform="translate(0,0)"/>
<path d="" fill="#C0C0C0" transform="translate(0,0)"/>
<path d="" fill="#C1C2C1" transform="translate(0,0)"/>
<path d="" fill="#C4C2C3" transform="translate(0,0)"/>
<path d="" fill="#C0BFC1" transform="translate(0,0)"/>
<path d="" fill="#C2C2C1" transform="translate(0,0)"/>
<path d="" fill="#C0BFC1" transform="translate(0,0)"/>
<path d="" fill="#C4C4C2" transform="translate(0,0)"/>
<path d="" fill="#C3C3C3" transform="translate(0,0)"/>
<path d="" fill="#BBBDBF" transform="translate(0,0)"/>
<path d="" fill="#A5A5A7" transform="translate(0,0)"/>
<path d="" fill="#BEBEC0" transform="translate(0,0)"/>
<path d="" fill="#C4C5C3" transform="translate(0,0)"/>
<path d="" fill="#BEC4C0" transform="translate(0,0)"/>
<path d="" fill="#BFC2BE" transform="translate(0,0)"/>
<path d="" fill="#C0C3BF" transform="translate(0,0)"/>
<path d="" fill="#BDBDBE" transform="translate(0,0)"/>
<path d="" fill="#BFC0C3" transform="translate(0,0)"/>
<path d="" fill="#C0C3C1" transform="translate(0,0)"/>
<path d="" fill="#C5C4C4" transform="translate(0,0)"/>
<path d="" fill="#BFBFBF" transform="translate(0,0)"/>
<path d="" fill="#BFC0BF" transform="translate(0,0)"/>
<path d="" fill="#BFC3C1" transform="translate(0,0)"/>
<path d="" fill="#C6C6C7" transform="translate(0,0)"/>
<path d="" fill="#C3C6C2" transform="translate(0,0)"/>
<path d="" fill="#C4C3C5" transform="translate(0,0)"/>
<path d="" fill="#C1C3C1" transform="translate(0,0)"/>
<path d="" fill="#BABFBF" transform="translate(0,0)"/>
<path d="" fill="#C3C4C2" transform="translate(0,0)"/>
<path d="" fill="#C0C1C1" transform="translate(0,0)"/>
<path d="" fill="#BFC2C2" transform="translate(0,0)"/>
<path d="" fill="#BCBAB9" transform="translate(0,0)"/>
<path d="" fill="#C1C2C1" transform="translate(0,0)"/>
<path d="" fill="#BEBEC0" transform="translate(0,0)"/>
<path d="" fill="#C1C2C3" transform="translate(0,0)"/>
<path d="" fill="#C6C6C6" transform="translate(0,0)"/>
<path d="" fill="#C5C5C5" transform="translate(0,0)"/>
<path d="" fill="#C1C0BF" transform="translate(0,0)"/>
<path d="" fill="#C1C3C4" transform="translate(0,0)"/>
<path d="" fill="#C4C3C2" transform="translate(0,0)"/>
<path d="" fill="#C2C0C0" transform="translate(0,0)"/>
<path d="" fill="#C2C4C1" transform="translate(0,0)"/>
<path d="" fill="#C0C0C1" transform="translate(0,0)"/>
<path d="" fill="#C7C6C6" transform="translate(0,0)"/>
<path d="" fill="#C8C9C7" transform="translate(0,0)"/>
<path d="" fill="#C5C5C8" transform="translate(0,0)"/>
<path d="" fill="#C5C5C5" transform="translate(0,0)"/>
<path d="" fill="#C4C4C4" transform="translate(0,0)"/>
<path d="" fill="#B3B2B3" transform="translate(0,0)"/>
<path d="" fill="#BCBDBD" transform="translate(0,0)"/>
<path d="" fill="#BABBBC" transform="translate(0,0)"/>
<path d="" fill="#C9C9C6" transform="translate(0,0)"/>
<path d="" fill="#BEBEBD" transform="translate(0,0)"/>
<path d="" fill="#C0C2C5" transform="translate(0,0)"/>
<path d="" fill="#B4B4B5" transform="translate(0,0)"/>
<path d="" fill="#BFC6C4" transform="translate(0,0)"/>
<path d="" fill="#CACACC" transform="translate(0,0)"/>
<path d="" fill="#C6C6C8" transform="translate(0,0)"/>
<path d="" fill="#C4C2C4" transform="translate(0,0)"/>
<path d="" fill="#C3C3C4" transform="translate(0,0)"/>
<path d="" fill="#C8C9CB" transform="translate(0,0)"/>
<path d="" fill="#C4C5C7" transform="translate(0,0)"/>
<path d="" fill="#C3C2C3" transform="translate(0,0)"/>
<path d="" fill="#C0C0BF" transform="translate(0,0)"/>
<path d="" fill="#CCCFCC" transform="translate(0,0)"/>
<path d="" fill="#C8C7C7" transform="translate(0,0)"/>
<path d="" fill="#C5C6C6" transform="translate(0,0)"/>
<path d="" fill="#C1BDBD" transform="translate(0,0)"/>
<path d="" fill="#C3C0C1" transform="translate(0,0)"/>
<path d="" fill="#C5C1C6" transform="translate(0,0)"/>
<path d="" fill="#C4C5C4" transform="translate(0,0)"/>
<path d="" fill="#C2C1C5" transform="translate(0,0)"/>
<path d="" fill="#CACBCA" transform="translate(0,0)"/>
<path d="" fill="#C2C4BF" transform="translate(0,0)"/>
<path d="" fill="#C5C8C7" transform="translate(0,0)"/>
<path d="" fill="#C4C4C3" transform="translate(0,0)"/>
<path d="" fill="#CCCDCD" transform="translate(0,0)"/>
<path d="" fill="#CACACA" transform="translate(0,0)"/>
<path d="" fill="#C0C1C2" transform="translate(0,0)"/>
<path d="" fill="#C3C4C4" transform="translate(0,0)"/>
<path d="" fill="#ABABB0" transform="translate(0,0)"/>
<path d="" fill="#CAC9C7" transform="translate(0,0)"/>
<path d="" fill="#C4C5C6" transform="translate(0,0)"/>
<path d="" fill="#C8C9C9" transform="translate(0,0)"/>
<path d="" fill="#C2C5C5" transform="translate(0,0)"/>
<path d="" fill="#C4C6C8" transform="translate(0,0)"/>
<path d="" fill="#C9CAC9" transform="translate(0,0)"/>
<path d="" fill="#ABACAB" transform="translate(0,0)"/>
<path d="" fill="#B9B8B8" transform="translate(0,0)"/>
<path d="" fill="#C4C8C6" transform="translate(0,0)"/>
<path d="" fill="#C6C8C8" transform="translate(0,0)"/>
<path d="" fill="#C0C4C4" transform="translate(0,0)"/>
<path d="" fill="#C3C6C6" transform="translate(0,0)"/>
<path d="" fill="#C2C4C6" transform="translate(0,0)"/>
<path d="" fill="#C8C7C6" transform="translate(0,0)"/>
<path d="" fill="#C4C5C4" transform="translate(0,0)"/>
<path d="" fill="#B1B1B3" transform="translate(0,0)"/>
<path d="" fill="#B4B3B4" transform="translate(0,0)"/>
<path d="" fill="#C4C5C4" transform="translate(0,0)"/>
<path d="" fill="#C7C8C9" transform="translate(0,0)"/>
<path d="" fill="#C5C6C5" transform="translate(0,0)"/>
<path d="" fill="#ADACAB" transform="translate(0,0)"/>
<path d="" fill="#C2C2C5" transform="translate(0,0)"/>
<path d="" fill="#C8C9C8" transform="translate(0,0)"/>
<path d="" fill="#BDC4C2" transform="translate(0,0)"/>
<path d="" fill="#C6C5C7" transform="translate(0,0)"/>
<path d="" fill="#C2C3C5" transform="translate(0,0)"/>
<path d="" fill="#C5C8C9" transform="translate(0,0)"/>
<path d="" fill="#CACCCD" transform="translate(0,0)"/>
<path d="" fill="#C6C8C9" transform="translate(0,0)"/>
<path d="" fill="#C7C5C6" transform="translate(0,0)"/>
<path d="" fill="#C6C6C7" transform="translate(0,0)"/>
<path d="" fill="#C3C4C7" transform="translate(0,0)"/>
<path d="" fill="#CACAC7" transform="translate(0,0)"/>
<path d="" fill="#C1C4C2" transform="translate(0,0)"/>
<path d="" fill="#C5C7C3" transform="translate(0,0)"/>
<path d="" fill="#C1C3C2" transform="translate(0,0)"/>
<path d="" fill="#CAC8CA" transform="translate(0,0)"/>
<path d="" fill="#CBCBC8" transform="translate(0,0)"/>
<path d="" fill="#B3B8B7" transform="translate(0,0)"/>
<path d="" fill="#C3C4C3" transform="translate(0,0)"/>
<path d="" fill="#CACBCA" transform="translate(0,0)"/>
<path d="" fill="#A2A5A4" transform="translate(0,0)"/>
<path d="" fill="#A7A9A7" transform="translate(0,0)"/>
<path d="" fill="#C8C9C8" transform="translate(0,0)"/>
<path d="" fill="#C2C6C5" transform="translate(0,0)"/>
<path d="" fill="#CBCECC" transform="translate(0,0)"/>
<path d="" fill="#BFC3C4" transform="translate(0,0)"/>
<path d="" fill="#C2C9CA" transform="translate(0,0)"/>
<path d="" fill="#C6CBCC" transform="translate(0,0)"/>
<path d="" fill="#B2B2B1" transform="translate(0,0)"/>
<path d="" fill="#C1C3C5" transform="translate(0,0)"/>
<path d="" fill="#C5C4C3" transform="translate(0,0)"/>
<path d="" fill="#BEC1C3" transform="translate(0,0)"/>
<path d="" fill="#C1C8C8" transform="translate(0,0)"/>
<path d="" fill="#C2C4C6" transform="translate(0,0)"/>
<path d="" fill="#C1C7C8" transform="translate(0,0)"/>
<path d="" fill="#BFC6C5" transform="translate(0,0)"/>
<path d="" fill="#BDBFBB" transform="translate(0,0)"/>
<path d="" fill="#B1B3AD" transform="translate(0,0)"/>
<path d="" fill="#B7BABA" transform="translate(0,0)"/>
<path d="" fill="#BCBFC2" transform="translate(0,0)"/>
<path d="" fill="#BDC0C3" transform="translate(0,0)"/>
<path d="" fill="#BABEBD" transform="translate(0,0)"/>
<path d="" fill="#BDC6C6" transform="translate(0,0)"/>
<path d="" fill="#C3C7CA" transform="translate(0,0)"/>
<path d="" fill="#C0C5C5" transform="translate(0,0)"/>
<path d="" fill="#BFC8CB" transform="translate(0,0)"/>
<path d="" fill="#BCC2C2" transform="translate(0,0)"/>
<path d="" fill="#C2C4C5" transform="translate(0,0)"/>
<path d="" fill="#ABB0AC" transform="translate(0,0)"/>
<path d="" fill="#BABFC0" transform="translate(0,0)"/>
<path d="" fill="#BFC4C7" transform="translate(0,0)"/>
<path d="" fill="#C2C5C7" transform="translate(0,0)"/>
<path d="" fill="#C7C8C9" transform="translate(0,0)"/>
<path d="" fill="#B8BCBD" transform="translate(0,0)"/>
<path d="" fill="#BBBDBF" transform="translate(0,0)"/>
<path d="" fill="#B7BABD" transform="translate(0,0)"/>
<path d="" fill="#BABDBD" transform="translate(0,0)"/>
<path d="" fill="#BDC0C2" transform="translate(0,0)"/>
<path d="" fill="#C2C7C8" transform="translate(0,0)"/>
<path d="" fill="#C4C9CB" transform="translate(0,0)"/>
<path d="" fill="#C0C9CB" transform="translate(0,0)"/>
<path d="" fill="#C3C8CB" transform="translate(0,0)"/>
<path d="" fill="#C5C7CA" transform="translate(0,0)"/>
<path d="" fill="#C0C4C7" transform="translate(0,0)"/>
<path d="" fill="#BCC1C2" transform="translate(0,0)"/>
<path d="" fill="#C5CCD0" transform="translate(0,0)"/>
<path d="" fill="#C0C4C9" transform="translate(0,0)"/>
<path d="" fill="#BBBFC3" transform="translate(0,0)"/>
<path d="" fill="#B3B0AE" transform="translate(0,0)"/>
<path d="" fill="#C3C4C4" transform="translate(0,0)"/>
<path d="" fill="#B8B9B8" transform="translate(0,0)"/>
<path d="" fill="#BCC4C5" transform="translate(0,0)"/>
<path d="" fill="#BCC3C6" transform="translate(0,0)"/>
<path d="" fill="#B9BCC0" transform="translate(0,0)"/>
<path d="" fill="#BDC3C5" transform="translate(0,0)"/>
<path d="" fill="#C3C3C0" transform="translate(0,0)"/>
<path d="" fill="#BEC5C6" transform="translate(0,0)"/>
<path d="" fill="#BFC3C2" transform="translate(0,0)"/>
<path d="" fill="#BEBFBD" transform="translate(0,0)"/>
<path d="" fill="#BBC7C8" transform="translate(0,0)"/>
<path d="" fill="#B2B6B8" transform="translate(0,0)"/>
<path d="" fill="#BFC1C3" transform="translate(0,0)"/>
<path d="" fill="#BBC2C2" transform="translate(0,0)"/>
<path d="" fill="#B7BFC2" transform="translate(0,0)"/>
<path d="" fill="#BAC1CA" transform="translate(0,0)"/>
<path d="" fill="#BCC5C8" transform="translate(0,0)"/>
<path d="" fill="#B2B8BC" transform="translate(0,0)"/>
<path d="" fill="#BBC1C4" transform="translate(0,0)"/>
<path d="" fill="#B2B3B5" transform="translate(0,0)"/>
<path d="" fill="#B6B9BB" transform="translate(0,0)"/>
<path d="" fill="#B7B8BB" transform="translate(0,0)"/>
<path d="" fill="#B2BBC2" transform="translate(0,0)"/>
<path d="" fill="#B3BEC2" transform="translate(0,0)"/>
<path d="" fill="#B6C1C3" transform="translate(0,0)"/>
<path d="" fill="#B3B6B6" transform="translate(0,0)"/>
<path d="" fill="#B2B7B9" transform="translate(0,0)"/>
<path d="" fill="#B5BEC3" transform="translate(0,0)"/>
<path d="" fill="#B2BFC0" transform="translate(0,0)"/>
<path d="" fill="#AFB7BC" transform="translate(0,0)"/>
<path d="" fill="#A2A7A9" transform="translate(0,0)"/>
<path d="" fill="#A9ACAC" transform="translate(0,0)"/>
<path d="" fill="#B1B9B7" transform="translate(0,0)"/>
<path d="" fill="#A2A8AC" transform="translate(0,0)"/>
<path d="" fill="#56B9F1" transform="translate(0,0)"/>
<path d="" fill="#52B5F2" transform="translate(0,0)"/>
<path d="" fill="#65BDF3" transform="translate(0,0)"/>
<path d="" fill="#5FBAF1" transform="translate(0,0)"/>
<path d="" fill="#5BB5ED" transform="translate(0,0)"/>
<path d="" fill="#5CB5ED" transform="translate(0,0)"/>
<path d="" fill="#55B6EE" transform="translate(0,0)"/>
<path d="" fill="#52BAF2" transform="translate(0,0)"/>
<path d="" fill="#C2C2C1" transform="translate(0,0)"/>
<path d="" fill="#5AB9F0" transform="translate(0,0)"/>
<path d="" fill="#5EBAF1" transform="translate(0,0)"/>
<path d="" fill="#5BB9F3" transform="translate(0,0)"/>
<path d="" fill="#5CB4EC" transform="translate(0,0)"/>
<path d="" fill="#5EB8ED" transform="translate(0,0)"/>
<path d="" fill="#5AB5ED" transform="translate(0,0)"/>
<path d="" fill="#BABDBF" transform="translate(0,0)"/>
<path d="" fill="#54B7EF" transform="translate(0,0)"/>
<path d="" fill="#B8B9B9" transform="translate(0,0)"/>
<path d="" fill="#50BDFB" transform="translate(0,0)"/>
<path d="" fill="#51BEFA" transform="translate(0,0)"/>
<path d="" fill="#51BEFB" transform="translate(0,0)"/>
<path d="" fill="#5BB5EB" transform="translate(0,0)"/>
<path d="" fill="#53B5F0" transform="translate(0,0)"/>
<path d="" fill="#B6B9B7" transform="translate(0,0)"/>
<path d="" fill="#55B4EC" transform="translate(0,0)"/>
<path d="" fill="#52BEF8" transform="translate(0,0)"/>
<path d="" fill="#55BBEF" transform="translate(0,0)"/>
<path d="" fill="#B8B8B9" transform="translate(0,0)"/>
<path d="" fill="#58B6ED" transform="translate(0,0)"/>
<path d="" fill="#53BBF5" transform="translate(0,0)"/>
<path d="" fill="#51BAF9" transform="translate(0,0)"/>
<path d="" fill="#51B8ED" transform="translate(0,0)"/>
<path d="" fill="#4CB9F7" transform="translate(0,0)"/>
<path d="" fill="#50B3EC" transform="translate(0,0)"/>
<path d="" fill="#4FBEFB" transform="translate(0,0)"/>
<path d="" fill="#4BB2EA" transform="translate(0,0)"/>
<path d="" fill="#4CB0E9" transform="translate(0,0)"/>
<path d="" fill="#4EB3EC" transform="translate(0,0)"/>
<path d="" fill="#4EBCF8" transform="translate(0,0)"/>
<path d="" fill="#53B1EB" transform="translate(0,0)"/>
<path d="" fill="#BFC0BF" transform="translate(0,0)"/>
<path d="" fill="#56B6ED" transform="translate(0,0)"/>
<path d="" fill="#4EBBF9" transform="translate(0,0)"/>
<path d="" fill="#B8BBBA" transform="translate(0,0)"/>
<path d="" fill="#4EB4ED" transform="translate(0,0)"/>
<path d="" fill="#53BEFC" transform="translate(0,0)"/>
<path d="" fill="#4FBFFD" transform="translate(0,0)"/>
<path d="" fill="#BBBEBE" transform="translate(0,0)"/>
<path d="" fill="#54B5EE" transform="translate(0,0)"/>
<path d="" fill="#4FBBF8" transform="translate(0,0)"/>
<path d="" fill="#54B5ED" transform="translate(0,0)"/>
<path d="" fill="#BABCBC" transform="translate(0,0)"/>
<path d="" fill="#B6BABA" transform="translate(0,0)"/>
<path d="" fill="#BABABC" transform="translate(0,0)"/>
<path d="" fill="#55B6F0" transform="translate(0,0)"/>
<path d="" fill="#53BFFC" transform="translate(0,0)"/>
<path d="" fill="#4BB9F7" transform="translate(0,0)"/>
<path d="" fill="#BDBFC0" transform="translate(0,0)"/>
<path d="" fill="#4DBBFB" transform="translate(0,0)"/>
<path d="" fill="#4FBCF9" transform="translate(0,0)"/>
<path d="" fill="#4DBBFA" transform="translate(0,0)"/>
<path d="" fill="#58B5EF" transform="translate(0,0)"/>
<path d="" fill="#BCBCBE" transform="translate(0,0)"/>
<path d="" fill="#54B2ED" transform="translate(0,0)"/>
<path d="" fill="#53B3EB" transform="translate(0,0)"/>
<path d="" fill="#57B5EF" transform="translate(0,0)"/>
<path d="" fill="#5BB7F0" transform="translate(0,0)"/>
<path d="" fill="#4EBBF8" transform="translate(0,0)"/>
<path d="" fill="#4BBAFB" transform="translate(0,0)"/>
<path d="" fill="#5BB8ED" transform="translate(0,0)"/>
<path d="" fill="#52C0FB" transform="translate(0,0)"/>
<path d="" fill="#55BCF5" transform="translate(0,0)"/>
<path d="" fill="#BEBEBF" transform="translate(0,0)"/>
<path d="" fill="#5AB7EE" transform="translate(0,0)"/>
<path d="" fill="#56B4EE" transform="translate(0,0)"/>
<path d="" fill="#51B6F1" transform="translate(0,0)"/>
<path d="" fill="#BCBEBC" transform="translate(0,0)"/>
<path d="" fill="#5DB9EC" transform="translate(0,0)"/>
<path d="" fill="#51BEFD" transform="translate(0,0)"/>
<path d="" fill="#4FBEFE" transform="translate(0,0)"/>
<path d="" fill="#BEC0C1" transform="translate(0,0)"/>
<path d="" fill="#C0BDBF" transform="translate(0,0)"/>
<path d="" fill="#C1C4C4" transform="translate(0,0)"/>
<path d="" fill="#54C0FB" transform="translate(0,0)"/>
<path d="" fill="#58C0FB" transform="translate(0,0)"/>
<path d="" fill="#4FBEFD" transform="translate(0,0)"/>
<path d="" fill="#4DBEFF" transform="translate(0,0)"/>
<path d="" fill="#4EBFFF" transform="translate(0,0)"/>
<path d="" fill="#4EC1FF" transform="translate(0,0)"/>
<path d="" fill="#4EBFFE" transform="translate(0,0)"/>
<path d="" fill="#4FBCFC" transform="translate(0,0)"/>
<path d="" fill="#51C0FF" transform="translate(0,0)"/>
<path d="" fill="#50C0FF" transform="translate(0,0)"/>
<path d="" fill="#50C2FF" transform="translate(0,0)"/>
<path d="" fill="#5AB9F0" transform="translate(0,0)"/>
<path d="" fill="#4CB6F2" transform="translate(0,0)"/>
<path d="" fill="#ADAEAE" transform="translate(0,0)"/>
<path d="" fill="#5BB5EA" transform="translate(0,0)"/>
<path d="" fill="#52C2FF" transform="translate(0,0)"/>
<path d="" fill="#50C2FF" transform="translate(0,0)"/>
<path d="" fill="#51C0FD" transform="translate(0,0)"/>
<path d="" fill="#5BB4EB" transform="translate(0,0)"/>
<path d="" fill="#4FBBFA" transform="translate(0,0)"/>
<path d="" fill="#57B6EF" transform="translate(0,0)"/>
<path d="" fill="#5CB5EA" transform="translate(0,0)"/>
<path d="" fill="#56B3EE" transform="translate(0,0)"/>
<path d="" fill="#BDBEBF" transform="translate(0,0)"/>
<path d="" fill="#57B5EB" transform="translate(0,0)"/>
<path d="" fill="#53BFFD" transform="translate(0,0)"/>
<path d="" fill="#52B5EB" transform="translate(0,0)"/>
<path d="" fill="#53BEFB" transform="translate(0,0)"/>
<path d="" fill="#54B6F0" transform="translate(0,0)"/>
<path d="" fill="#56AFE7" transform="translate(0,0)"/>
<path d="" fill="#4FBAF9" transform="translate(0,0)"/>
<path d="" fill="#58B0E6" transform="translate(0,0)"/>
<path d="" fill="#4FBDFA" transform="translate(0,0)"/>
<path d="" fill="#58B1EB" transform="translate(0,0)"/>
<path d="" fill="#58B1E7" transform="translate(0,0)"/>
<path d="" fill="#59B3E8" transform="translate(0,0)"/>
<path d="" fill="#50BDFD" transform="translate(0,0)"/>
<path d="" fill="#4FBEFB" transform="translate(0,0)"/>
<path d="" fill="#50AFE6" transform="translate(0,0)"/>
<path d="" fill="#B9B9B8" transform="translate(0,0)"/>
<path d="" fill="#56B5EE" transform="translate(0,0)"/>
<path d="" fill="#61B7EC" transform="translate(0,0)"/>
<path d="" fill="#5DBFF7" transform="translate(0,0)"/>
<path d="" fill="#57BCFB" transform="translate(0,0)"/>
<path d="" fill="#4CB3EC" transform="translate(0,0)"/>
<path d="" fill="#4CB4EB" transform="translate(0,0)"/>
<path d="" fill="#68BBEA" transform="translate(0,0)"/>
<path d="" fill="#62BAEC" transform="translate(0,0)"/>
<path d="" fill="#75BFE9" transform="translate(0,0)"/>
<path d="" fill="#5ABCF7" transform="translate(0,0)"/>
<path d="" fill="#5ABCF8" transform="translate(0,0)"/>
<path d="" fill="#50B2EB" transform="translate(0,0)"/>
<path d="" fill="#4BB3EC" transform="translate(0,0)"/>
<path d="" fill="#58BBFB" transform="translate(0,0)"/>
<path d="" fill="#50B0E9" transform="translate(0,0)"/>
<path d="" fill="#4DBDFF" transform="translate(0,0)"/>
<path d="" fill="#4CBCFD" transform="translate(0,0)"/>
<path d="" fill="#4FBFF9" transform="translate(0,0)"/>
<path d="" fill="#4DBBF9" transform="translate(0,0)"/>
<path d="" fill="#4FACE6" transform="translate(0,0)"/>
<path d="" fill="#54BCFC" transform="translate(0,0)"/>
<path d="" fill="#53BEFA" transform="translate(0,0)"/>
<path d="" fill="#4DBCFF" transform="translate(0,0)"/>
<path d="" fill="#48BCFB" transform="translate(0,0)"/>
<path d="" fill="#54C0FD" transform="translate(0,0)"/>
<path d="" fill="#4FBDFD" transform="translate(0,0)"/>
<path d="" fill="#5BBBF5" transform="translate(0,0)"/>
<path d="" fill="#57B8F5" transform="translate(0,0)"/>
<path d="" fill="#4FBFFE" transform="translate(0,0)"/>
<path d="" fill="#5AB9F4" transform="translate(0,0)"/>
<path d="" fill="#71B9E1" transform="translate(0,0)"/>
<path d="" fill="#58B8F3" transform="translate(0,0)"/>
<path d="" fill="#4DB5EA" transform="translate(0,0)"/>
<path d="" fill="#52ABE6" transform="translate(0,0)"/>
<path d="" fill="#5BB0E3" transform="translate(0,0)"/>
<path d="" fill="#52B5EC" transform="translate(0,0)"/>
<path d="" fill="#5DB3E6" transform="translate(0,0)"/>
<path d="" fill="#50B3EA" transform="translate(0,0)"/>
<path d="" fill="#52B5EC" transform="translate(0,0)"/>
<path d="" fill="#50B1E9" transform="translate(0,0)"/>
<path d="" fill="#4FB3EB" transform="translate(0,0)"/>
<path d="" fill="#52B2E9" transform="translate(0,0)"/>
<path d="" fill="#4FB4EF" transform="translate(0,0)"/>
<path d="" fill="#A7B2B9" transform="translate(0,0)"/>
</svg>
