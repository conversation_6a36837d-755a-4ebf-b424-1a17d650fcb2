<svg
  xmlns="http://www.w3.org/2000/svg"
  width="1080"
  height="1080"
  viewBox="0 0 1080 1080"
>
  <defs>
    <style type="text/css"><![CDATA[
      @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@700&family=Open+Sans:wght@400&display=swap');
      @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 700;
        src: url('https://fonts.gstatic.com/s/roboto/v29/KFOlCnqEu92Fr1MmWUlfBBc9AMP6lQ.woff2') format('woff2');
      }
      @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 400;
        src: url('https://fonts.gstatic.com/s/opensans/v29/mem8YaGs126MiZpBA-UFVZ0e.woff2') format('woff2');
      }
    ]]></style>
  </defs>

  <rect width="100%" height="100%" fill="#FFFFFF" />

  <text
    x="50%"
    y="470"
    font-family="Roboto, sans-serif"
    font-weight="700"
    font-size="60px"
    fill="#003366"
    text-anchor="middle"
    dominant-baseline="middle"
  >
    COMING SOON
  </text>

  <text
    x="50%"
    y="540"
    font-family="&quot;Open Sans&quot;, sans-serif"
    font-weight="400"
    font-size="24px"
    fill="#666666"
    text-anchor="middle"
    dominant-baseline="middle"
  >
    React Native App Launching Soon
  </text>

  <text
    x="50%"
    y="1020"
    font-family="&quot;Open Sans&quot;, sans-serif"
    font-weight="400"
    font-size="18px"
    fill="#999999"
    text-anchor="middle"
    dominant-baseline="middle"
  >
    <EMAIL>
  </text>
</svg>