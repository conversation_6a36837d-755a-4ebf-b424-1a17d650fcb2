<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Oswald:wght@700&family=Roboto:wght@400&family=Permanent+Marker&display=swap');
      .title { font-family: '<PERSON>', <PERSON>l, sans-serif; font-size:140px; fill:#fff; font-weight:700; letter-spacing:0.01em;}
      .subtitle { font-family: 'Roboto', Arial, sans-serif; font-size:40px; fill:#d9e3e1; letter-spacing:0.01em;}
      .subtitle-strike { text-decoration: line-through; }
      .button-text { font-family: 'Permanent Marker', Arial, sans-serif; font-size:38px; fill:#19232d; letter-spacing:0.03em;}
    </style>
    <filter id="round" x="0" y="0" width="1" height="1" />
    <linearGradient id="bgGrad" x1="0" y1="0" x2="1080" y2="1080" gradientUnits="userSpaceOnUse">
      <stop stop-color="#101D27"/>
      <stop offset="1" stop-color="#14202C"/>
    </linearGradient>
  </defs>
  <rect width="1080" height="1080" fill="url(#bgGrad)" />
  <!-- Decorative corners -->
  <g opacity="0.11" stroke="#5F7692" stroke-width="4">
    <path d="M52 0V120M0 52H120"/>
    <path d="M1028 1080V960M1080 1028H960"/>
    <path d="M0 1028H120M52 1080V960"/>
    <path d="M1080 52H960M1028 0V120"/>
  </g>

  <!-- Title -->
  <text x="540" y="95" text-anchor="middle" class="title" font-size="140" font-family="'Oswald', Arial, sans-serif" letter-spacing="0.015em">COMING SOON</text>

  <!-- Subtitle -->
  <g>
    <text x="540" y="185" text-anchor="middle" class="subtitle" font-size="40">The Ruylere<tspan class="subtitle-strike">t Trakels</tspan>-Discherday</text>
    <text x="382" y="185" class="subtitle-strike subtitle" font-size="40">t Trakels</text>
  </g>

  <!-- Mobile outline -->
  <g>
    <rect x="350" y="256" width="380" height="600" rx="56" fill="none" stroke="#C9D7E0" stroke-width="5"/>
    <rect x="474" y="256" width="132" height="34" rx="17" fill="#101D27"/>
    <rect x="474" y="256" width="132" height="18" rx="9" fill="#101D27"/>
    <path d="M540 256h67a56 56 0 0 1 56 56v488a56 56 0 0 1-56 56h-67" stroke="#C9D7E0" stroke-width="0"/>
    <!-- Notch -->
    <path d="M485 256a18 18 0 0 0-18 18v6a7 7 0 0 0 7 7h32c2 0 5-3 5-5v-7h66v7a5 5 0 0 0 5 5h32c4 0 7-3 7-7v-6a18 18 0 0 0-18-18h-120z" fill="#101D27"/>
    <!-- Notch outline -->
    <path d="M483 256a19 19 0 0 0-19 19v5.5c0 5.5 4.5 10 10 10h32.7c3.5 0 5.3-2.8 5.3-5.2v-7.3h56v7.3c0 2.4 1.8 5.2 5.3 5.2h32.7c5.5 0 10-4.5 10-10V275a19 19 0 0 0-19-19h-120z" stroke="#C9D7E0" stroke-width="2"/>
  </g>

  <!-- Button -->
  <g>
    <rect x="290" y="986" width="500" height="94" rx="32" fill="#49E8BC"/>
    <text x="540" y="1046" text-anchor="middle" class="button-text" font-size="38">DON KRUNG</text>
  </g>
</svg>