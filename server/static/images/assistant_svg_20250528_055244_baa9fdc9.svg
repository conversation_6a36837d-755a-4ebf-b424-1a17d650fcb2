<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C16.364 -0.106 32.666 -0.152 49 1 C49 1.66 49 2.32 49 3 C49.804 3.021 49.804 3.021 50.625 3.043 C63.049 3.799 74.938 8.548 86 14 C87.578 14.717 87.578 14.717 89.188 15.449 C99.289 20.19 108.051 26.389 117 33 C117.802 33.571 118.604 34.142 119.43 34.73 C121 36 121 36 122 38.062 C122.831 40.142 122.831 40.142 124.836 40.914 C137.572 47.305 146.479 64.936 153 77 C153.592 78.052 154.183 79.104 154.793 80.188 C158.943 87.601 158.943 87.601 160 92 C160.605 93.947 161.21 95.894 161.816 97.84 C164.092 105.234 165.815 112.208 166 120 C166.99 120 167.98 120 169 120 C168.754 121.471 168.754 121.471 168.504 122.973 C167.908 127.141 167.888 131.234 167.938 135.438 C167.944 136.535 167.944 136.535 167.951 137.654 C167.963 139.436 167.981 141.218 168 143 C168.66 143 169.32 143 170 143 C165.993 177.605 158.708 211.102 135 238 C134.12 239.054 134.12 239.054 133.223 240.129 C124.159 250.787 112.417 259.64 100.645 267.137 C99.335 268.059 99.335 268.059 98 269 C98 269.66 98 270.32 98 271 C97.197 271.077 96.394 271.155 95.566 271.234 C90.96 272.223 87.152 274.192 82.938 276.25 C73.179 280.86 63.395 283.479 52.837 285.57 C50.918 285.944 50.918 285.944 49 287 C47 287 45 287 43 287 C42.01 287.33 41.02 287.66 40 288 C36.609 288.234 33.211 288.19 29.812 288.188 C28.834 288.188 27.855 288.189 26.847 288.189 C18.435 288.129 10.303 287.332 2 286 C2 286.33 2 286.66 2 287 C0.334 287.043 -1.334 287.041 -3 287 C-4 286 -5 285 -6 284 C-7.839 283.302 -7.839 283.302 -9.973 282.773 C-10.783 282.55 -11.592 282.326 -12.427 282.096 C-13.297 281.858 -14.166 281.62 -15.062 281.375 C-24.921 278.571 -33.888 274.651 -43 270 C-43.33 270.66 -43.66 271.32 -44 272 C-45.32 271.34 -46.64 270.68 -48 270 C-47.34 269.34 -46.68 268.68 -46 268 C-46.554 267.723 -47.109 267.446 -47.68 267.16 C-58.14 261.93 -58.14 261.93 -62.367 257.602 C-64.411 255.597 -66.546 253.712 -68.688 251.812 C-73.309 247.703 -77.617 243.358 -81.871 238.871 C-83.903 236.67 -83.903 236.67 -87 237 C-87.093 236.109 -87.186 235.219 -87.281 234.301 C-88.076 230.652 -89.164 229.057 -91.5 226.188 C-99.245 215.977 -105.218 204.246 -109 192 C-109.66 192 -110.32 192 -111 192 C-111.562 190.062 -111.562 190.062 -112 188 C-111.67 187.67 -111.34 187.34 -111 187 C-111.66 186.67 -112.32 186.34 -113 186 C-112.67 185.01 -112.34 184.02 -112 183 C-112.367 181.007 -112.819 179.029 -113.312 177.062 C-117.91 156.586 -117.587 135.804 -116 115 C-115.34 115 -114.68 115 -114 115 C-113.938 113.845 -113.876 112.69 -113.812 111.5 C-113.225 105.928 -111.493 100.788 -109.728 95.498 C-108.748 92.54 -108 90.14 -108 87 C-107.34 87 -106.68 87 -106 87 C-105.34 84.03 -104.68 81.06 -104 78 C-103.34 78 -102.68 78 -102 78 C-101.733 77.291 -101.466 76.582 -101.191 75.852 C-97.118 66.102 -90.896 57.908 -84 50 C-83.267 49.144 -82.533 48.288 -81.777 47.406 C-63.528 26.549 -36.075 7.944 -7.941 4.422 C-7.301 4.283 -6.66 4.143 -6 4 C-5.67 3.34 -5.34 2.68 -5 2 C-3.35 2 -1.7 2 0 2 C0 1.34 0 0.68 0 0 Z M1 1 C1 1.33 1 1.66 1 2 C3.31 2 5.62 2 8 2 C8 1.67 8 1.34 8 1 C5.69 1 3.38 1 1 1 Z M17 11.312 C15.742 11.371 14.484 11.429 13.188 11.488 C12.136 11.657 11.084 11.826 10 12 C9.34 12.99 8.68 13.98 8 15 C4.768 15.239 1.961 15.247 -1.188 14.438 C-11.619 11.92 -31.992 22.949 -40.982 28.146 C-70.988 46.561 -93.963 73.895 -102.25 108.5 C-105.507 123.073 -111.132 148.736 -104 163 C-103.893 164.332 -103.815 165.666 -103.75 167 C-101.369 191.735 -88.923 215.195 -71.934 232.926 C-70 235 -70 235 -69 237 C-68.34 237 -67.68 237 -67 237 C-66.67 237.99 -66.34 238.98 -66 240 C-62.536 243.244 -58.752 246.097 -55 249 C-54.246 249.592 -53.492 250.183 -52.715 250.793 C-29.941 267.703 -1.922 276.337 26.312 276.062 C27.632 276.056 27.632 276.056 28.979 276.049 C30.603 276.04 32.228 276.029 33.853 276.016 C35.902 276.001 37.951 276 40 276 C53.758 273.946 67.49 271.255 80 265 C81.22 264.407 82.44 263.815 83.66 263.223 C85.128 262.503 86.595 261.783 88.062 261.062 C88.808 260.702 89.554 260.342 90.322 259.971 C91.032 259.621 91.742 259.271 92.473 258.91 C93.125 258.592 93.777 258.274 94.449 257.946 C96.183 257.077 96.183 257.077 97 255 C98.145 254.567 98.145 254.567 99.312 254.125 C102.349 252.854 103.723 251.346 106 249 C108 247.667 110 246.333 112 245 C114.335 243.001 114.335 243.001 116 241 C116 240.34 116 239.68 116 239 C116.681 238.835 117.361 238.67 118.062 238.5 C134.438 230.138 145.852 202.772 151.355 186.094 C155.723 171.911 157.213 158.324 157.25 143.5 C157.252 142.84 157.255 142.179 157.257 141.499 C157.271 131.031 156.503 121.16 154 111 C152.515 111.495 152.515 111.495 151 112 C151.66 111.34 152.32 110.68 153 110 C153.052 86.33 132.103 63.628 116.855 47.77 C114.921 45.822 114.921 45.822 112 44 C111.031 43.175 110.061 42.35 109.062 41.5 C106.283 39.044 106.283 39.044 103.875 37.938 C102 37 102 37 100.441 35.273 C87.679 23.389 64.526 14 47 14 C46.274 13.673 45.549 13.345 44.801 13.008 C41.441 11.799 38.747 11.803 35.188 11.875 C34.026 11.893 32.865 11.911 31.668 11.93 C30.788 11.953 29.907 11.976 29 12 C29 12.66 29 13.32 29 14 C28.546 13.505 28.092 13.01 27.625 12.5 C24.8 9.892 20.697 11.119 17 11.312 Z M3 13 C2.67 13.66 2.34 14.32 2 15 C5.465 14.505 5.465 14.505 9 14 C9 13.67 9 13.34 9 13 C7.02 13 5.04 13 3 13 Z M157 142 C158 144 158 144 158 144 Z " fill="#9496F9" transform="translate(484,112)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.65 2 4.3 2 6 2 C11.371 18.708 11.371 18.708 13 27 C13.99 27 14.98 27 16 27 C17.125 30.75 17.125 30.75 16 33 C16.517 35.173 16.517 35.173 17.441 37.629 C17.775 38.574 18.109 39.52 18.453 40.494 C18.819 41.507 19.185 42.519 19.562 43.562 C19.927 44.59 20.292 45.617 20.668 46.676 C21.414 48.775 22.163 50.873 22.915 52.97 C24.193 56.54 25.453 60.116 26.711 63.693 C27.802 66.797 28.901 69.899 30 73 C29.67 71.68 29.34 70.36 29 69 C30.32 69.33 31.64 69.66 33 70 C33.061 69.374 33.121 68.747 33.184 68.102 C33.309 66.876 33.309 66.876 33.438 65.625 C33.559 64.407 33.559 64.407 33.684 63.164 C34 61 34 61 35 59 C35 58.01 35 57.02 35 56 C35.999 54.999 36.999 53.999 38 53 C39.044 50.765 39.044 50.765 39.854 48.209 C40.184 47.238 40.514 46.267 40.855 45.266 C41.202 44.219 41.549 43.172 41.906 42.094 C42.272 41.018 42.638 39.941 43.015 38.832 C44.185 35.391 45.344 31.946 46.5 28.5 C48.021 23.965 49.554 19.435 51.094 14.906 C51.441 13.859 51.788 12.812 52.145 11.734 C52.476 10.763 52.806 9.792 53.146 8.791 C53.433 7.936 53.719 7.081 54.014 6.201 C55.056 3.875 56.014 2.582 58 1 C60.417 0.644 60.417 0.644 63.262 0.672 C64.299 0.677 65.336 0.682 66.404 0.688 C68.029 0.718 68.029 0.718 69.688 0.75 C70.752 0.755 71.816 0.76 72.912 0.766 C78.757 0.823 84.146 1.268 90 2 C90 39.95 90 77.9 90 117 C86.969 118.01 85.256 118.05 82.113 117.879 C81.175 117.831 80.238 117.782 79.271 117.732 C78.295 117.676 77.319 117.62 76.312 117.562 C75.324 117.51 74.336 117.458 73.318 117.404 C70.879 117.275 68.439 117.14 66 117 C66 116.34 66 115.68 66 115 C66.66 115 67.32 115 68 115 C67.675 114.306 67.349 113.612 67.014 112.897 C65.869 109.625 65.759 107.111 65.773 103.649 C65.773 102.388 65.773 101.127 65.773 99.828 C65.784 98.462 65.794 97.096 65.805 95.73 C65.808 94.325 65.811 92.92 65.813 91.514 C65.821 87.827 65.841 84.14 65.863 80.452 C65.883 76.685 65.892 72.918 65.902 69.15 C65.924 61.767 65.958 54.383 66 47 C66.66 46.67 67.32 46.34 68 46 C66.032 46.031 66.032 46.031 64 47 C62.695 49.996 62.695 49.996 62 53 C62.66 53.33 63.32 53.66 64 54 C63.01 54 62.02 54 61 54 C60.865 54.562 60.729 55.124 60.59 55.703 C58.915 62.241 56.809 68.579 54.562 74.938 C54.104 76.236 54.104 76.236 53.636 77.561 C52.518 80.723 51.397 83.884 50.273 87.044 C49.675 88.735 49.087 90.43 48.5 92.125 C48.02 93.49 48.02 93.49 47.531 94.883 C46.747 97.036 46.747 97.036 48 99 C46.667 101 45.333 103 44 105 C43.481 107.272 43.118 109.542 42.758 111.844 C41.71 114.825 40.844 115.587 38 117 C35.072 117.351 32.194 117.283 29.25 117.188 C28.457 117.174 27.664 117.16 26.848 117.146 C24.898 117.111 22.949 117.057 21 117 C17.873 110.142 14.83 103.332 13 96 C11.912 92.584 10.733 89.201 9.562 85.812 C9.25 84.885 8.937 83.958 8.615 83.002 C8.158 81.68 8.158 81.68 7.691 80.332 C7.415 79.525 7.139 78.718 6.854 77.886 C6.155 75.815 6.155 75.815 4 75 C3.505 71.04 3.505 71.04 3 67 C-1.455 54.625 -1.455 54.625 -6 42 C-6 66.75 -6 91.5 -6 117 C-26 117 -26 117 -29 116 C-29.495 116.99 -29.495 116.99 -30 118 C-29.988 117.313 -29.977 116.626 -29.965 115.918 C-29.956 115.017 -29.947 114.116 -29.938 113.188 C-29.926 112.294 -29.914 111.401 -29.902 110.48 C-29.89 107.866 -29.89 107.866 -31 105 C-30.34 105 -29.68 105 -29 105 C-29.072 103.778 -29.144 102.556 -29.219 101.297 C-29.986 86.759 -30.112 72.242 -30.125 57.688 C-30.127 56.69 -30.13 55.693 -30.133 54.666 C-30.138 51.802 -30.137 48.938 -30.133 46.074 C-30.135 45.203 -30.137 44.332 -30.139 43.435 C-30.136 42.631 -30.133 41.827 -30.129 40.999 C-30.129 40.296 -30.128 39.594 -30.127 38.87 C-30 37 -30 37 -29 34 C-29.66 33.67 -30.32 33.34 -31 33 C-30.34 33 -29.68 33 -29 33 C-29.33 22.44 -29.66 11.88 -30 1 C-26.349 0.979 -22.699 0.959 -18.938 0.938 C-17.232 0.924 -17.232 0.924 -15.492 0.91 C-10.215 0.896 -5.197 1.064 0 2 C0 1.34 0 0.68 0 0 Z M14 28 C15 30 15 30 15 30 Z M67 38 C68 42 68 42 68 42 Z M67 62 C67 77.84 67 93.68 67 110 C67.33 110 67.66 110 68 110 C68 94.16 68 78.32 68 62 C67.67 62 67.34 62 67 62 Z " fill="#EDE6FE" transform="translate(462,466)"/>
<path d="M0 0 C0.75 -0.004 1.5 -0.008 2.273 -0.012 C9.394 0.001 9.394 0.001 12.375 1.125 C13.384 2.976 14.376 4.839 15.25 6.758 C16.44 9.262 17.801 11.575 19.25 13.938 C21.316 17.324 23.291 20.74 25.184 24.227 C25.704 25.185 26.225 26.144 26.761 27.131 C28.342 30.064 29.913 33.002 31.48 35.941 C31.989 36.89 32.498 37.838 33.021 38.814 C34.066 40.762 35.105 42.713 36.139 44.666 C36.643 45.603 37.148 46.539 37.668 47.504 C38.341 48.766 38.341 48.766 39.028 50.053 C40.36 52.4 40.36 52.4 43.375 54.125 C43.375 54.785 43.375 55.445 43.375 56.125 C44.336 57.979 45.343 59.809 46.375 61.625 C49.375 66.903 49.375 66.903 49.375 69.125 C50.035 69.455 50.695 69.785 51.375 70.125 C52.035 70.785 52.695 71.445 53.375 72.125 C53.189 71.362 53.004 70.599 52.812 69.812 C52.331 65.751 52.686 62.197 53.113 58.148 C53.951 49.019 53.951 49.019 52.375 40.125 C52.705 39.135 53.035 38.145 53.375 37.125 C53.459 34.803 53.482 32.479 53.473 30.156 C53.471 29.48 53.47 28.803 53.468 28.106 C53.463 25.945 53.45 23.785 53.438 21.625 C53.432 20.171 53.428 18.716 53.424 17.262 C53.412 13.549 53.394 9.837 53.375 6.125 C53.375 3.125 53.375 3.125 54.375 1.125 C57.27 0.93 60.166 0.743 63.062 0.562 C63.879 0.506 64.696 0.45 65.537 0.393 C66.731 0.32 66.731 0.32 67.949 0.246 C68.677 0.199 69.405 0.152 70.155 0.103 C72.665 0.128 74.932 0.567 77.375 1.125 C77.364 2.955 77.364 2.955 77.353 4.822 C77.304 17.646 77.429 30.465 77.622 43.288 C77.664 46.121 77.705 48.954 77.746 51.787 C77.773 53.712 77.801 55.636 77.828 57.561 C77.849 59.003 77.849 59.003 77.87 60.474 C77.941 65.424 78.017 70.373 78.099 75.323 C78.179 80.181 78.249 85.039 78.313 89.897 C78.338 91.733 78.368 93.568 78.401 95.403 C78.447 97.95 78.48 100.497 78.51 103.044 C78.535 104.166 78.535 104.166 78.56 105.312 C78.593 109.355 78.31 112.535 76.375 116.125 C73.342 117.517 70.237 117.168 66.953 117.004 C65.928 116.956 64.904 116.907 63.848 116.857 C62.784 116.801 61.721 116.745 60.625 116.688 C59.546 116.635 58.467 116.583 57.355 116.529 C54.695 116.4 52.035 116.265 49.375 116.125 C49.121 115.487 48.867 114.849 48.605 114.191 C48.261 113.365 47.917 112.539 47.562 111.688 C47.226 110.864 46.89 110.04 46.543 109.191 C45.491 106.791 45.491 106.791 42.375 106.125 C42.22 104.795 42.22 104.795 42.062 103.438 C41.36 100.054 40.215 98.006 38.375 95.125 C38.375 94.465 38.375 93.805 38.375 93.125 C37.385 92.795 36.395 92.465 35.375 92.125 C34.642 90.148 33.985 88.143 33.375 86.125 C32.453 84.24 31.484 82.376 30.477 80.535 C29.915 79.503 29.352 78.471 28.773 77.408 C28.188 76.345 27.603 75.283 27 74.188 C26.423 73.129 25.845 72.07 25.25 70.979 C22.869 66.621 20.477 62.277 17.961 57.996 C16.375 55.125 16.375 55.125 16.375 53.125 C15.055 52.795 13.735 52.465 12.375 52.125 C13.365 51.63 13.365 51.63 14.375 51.125 C11.9 46.67 11.9 46.67 9.375 42.125 C9.045 66.545 8.715 90.965 8.375 116.125 C-10.625 116.125 -10.625 116.125 -12.625 115.125 C-13.12 116.115 -13.12 116.115 -13.625 117.125 C-14.285 117.125 -14.945 117.125 -15.625 117.125 C-15.648 102.443 -15.666 87.762 -15.677 73.08 C-15.682 66.264 -15.689 59.447 -15.7 52.63 C-15.711 46.055 -15.717 39.481 -15.72 32.906 C-15.722 30.394 -15.725 27.882 -15.731 25.369 C-15.738 21.859 -15.739 18.349 -15.739 14.839 C-15.742 13.793 -15.746 12.747 -15.749 11.669 C-15.748 10.714 -15.747 9.76 -15.745 8.776 C-15.746 7.945 -15.747 7.115 -15.748 6.259 C-15.625 4.125 -15.625 4.125 -14.625 1.125 C-9.696 0.139 -5.001 -0.026 0 0 Z M53.375 72.125 C53.045 72.785 52.715 73.445 52.375 74.125 C53.035 74.785 53.695 75.445 54.375 76.125 C54.045 74.805 53.715 73.485 53.375 72.125 Z " fill="#ECE5FE" transform="translate(639.625,466.875)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 2.98 2 4.96 2 7 C2.99 7.495 2.99 7.495 4 8 C3.34 8 2.68 8 2 8 C2.008 8.815 2.008 8.815 2.016 9.647 C2.141 22.867 2.266 36.087 2.391 49.307 C2.452 55.7 2.512 62.093 2.573 68.486 C2.631 74.652 2.69 80.818 2.748 86.984 C2.77 89.34 2.792 91.696 2.815 94.052 C2.846 97.344 2.877 100.637 2.908 103.929 C2.918 104.909 2.927 105.89 2.937 106.9 C2.945 107.796 2.953 108.692 2.962 109.615 C2.969 110.394 2.977 111.173 2.984 111.975 C3.005 114.65 3 117.325 3 120 C-1.416 120.174 -5.831 120.281 -10.25 120.375 C-12.131 120.45 -12.131 120.45 -14.051 120.527 C-23.088 120.672 -23.088 120.672 -25.752 118.708 C-27.246 116.54 -28 114.645 -28 112 C-28.99 111.67 -29.98 111.34 -31 111 C-31 110.34 -31 109.68 -31 109 C-32.294 106.592 -33.619 104.232 -35 101.875 C-35.382 101.216 -35.763 100.558 -36.156 99.879 C-37.1 98.25 -38.05 96.625 -39 95 C-39.66 95 -40.32 95 -41 95 C-42 92 -42 92 -42 89 C-42.848 87.233 -43.777 85.504 -44.746 83.801 C-45.337 82.755 -45.928 81.709 -46.537 80.631 C-47.164 79.536 -47.791 78.441 -48.438 77.312 C-49.03 76.268 -49.622 75.223 -50.232 74.146 C-52.151 70.762 -54.076 67.381 -56 64 C-58.97 58.72 -61.94 53.44 -65 48 C-65.33 71.76 -65.66 95.52 -66 120 C-74.25 120 -82.5 120 -91 120 C-90.67 118.68 -90.34 117.36 -90 116 C-89.915 114.29 -89.882 112.577 -89.886 110.864 C-89.887 109.844 -89.887 108.823 -89.887 107.771 C-89.892 106.676 -89.897 105.581 -89.902 104.453 C-89.904 103.327 -89.905 102.2 -89.907 101.04 C-89.912 97.443 -89.925 93.847 -89.938 90.25 C-89.943 87.811 -89.947 85.372 -89.951 82.934 C-89.962 76.956 -89.979 70.978 -90 65 C-90.33 65 -90.66 65 -91 65 C-92.361 56.836 -92.361 56.836 -90 53 C-90.33 52.34 -90.66 51.68 -91 51 C-90.34 51 -89.68 51 -89 51 C-89.036 50.393 -89.071 49.787 -89.108 49.162 C-89.764 37.486 -90.159 25.878 -89.688 14.188 C-89.653 13.21 -89.618 12.233 -89.582 11.226 C-89.538 10.32 -89.494 9.414 -89.449 8.48 C-89.411 7.685 -89.374 6.89 -89.335 6.071 C-89 4 -89 4 -87 1 C-86.01 1.33 -85.02 1.66 -84 2 C-84 2.66 -84 3.32 -84 4 C-76.74 4 -69.48 4 -62 4 C-61.67 5.65 -61.34 7.3 -61 9 C-60.156 10.952 -60.156 10.952 -59.066 12.793 C-58.67 13.496 -58.273 14.2 -57.865 14.925 C-57.435 15.671 -57.005 16.418 -56.562 17.188 C-55.648 18.802 -54.734 20.417 -53.82 22.031 C-53.351 22.855 -52.883 23.679 -52.399 24.527 C-50.633 27.648 -48.906 30.789 -47.179 33.931 C-43.396 40.805 -39.493 47.582 -35.414 54.284 C-30.71 62.057 -26.37 70.036 -22 78 C-22.33 76.02 -22.66 74.04 -23 72 C-22.34 72 -21.68 72 -21 72 C-21 49.56 -21 27.12 -21 4 C-15.39 4 -9.78 4 -4 4 C-4 3.34 -4 2.68 -4 2 C-2.68 2.66 -1.36 3.32 0 4 C0 2.68 0 1.36 0 0 Z M-22 73 C-21 75 -21 75 -21 75 Z " fill="#EEE7FE" transform="translate(732,640)"/>
<path d="M0 0 C1.179 0.4 1.179 0.4 2.383 0.809 C15.56 5.974 25.808 17.544 32 30 C38.637 46.74 39.435 65.054 33 82 C25.89 97.735 15.264 108.533 -1 115 C-4.162 115.784 -7.061 116.118 -10.312 116.062 C-12.138 116.032 -12.138 116.032 -14 116 C-14.33 116.66 -14.66 117.32 -15 118 C-24.397 116.964 -33.726 115.883 -43 114 C-43 113.34 -43 112.68 -43 112 C-43.878 111.754 -44.756 111.508 -45.66 111.254 C-58.381 106.478 -66.191 94.208 -71.934 82.602 C-73 80 -73 80 -73 77 C-73.99 77 -74.98 77 -76 77 C-75.814 76.464 -75.629 75.928 -75.438 75.375 C-74.85 72.955 -74.85 72.955 -75.461 70.344 C-76.067 66.585 -76.191 62.921 -76.188 59.125 C-76.188 58.03 -76.188 58.03 -76.189 56.912 C-76.117 49.188 -75.15 41.628 -74 34 C-73.34 34 -72.68 34 -72 34 C-71.731 33.117 -71.461 32.234 -71.184 31.324 C-66.872 19.215 -56.88 8.593 -46 2 C-43.219 0.77 -43.219 0.77 -40.5 -0.188 C-39.603 -0.511 -38.706 -0.835 -37.781 -1.168 C-25.735 -4.772 -11.813 -4.049 0 0 Z M-24 20 C-30.915 22.126 -37.227 24.298 -42 30 C-42 30.66 -42 31.32 -42 32 C-42.66 32 -43.32 32 -44 32 C-44 32.66 -44 33.32 -44 34 C-44.66 34 -45.32 34 -46 34 C-52.508 47.761 -53.144 59.448 -49 74 C-45.855 82.121 -40.536 86.844 -33 91 C-24.89 94.519 -14.282 94.25 -6.062 91.047 C2.933 86.481 7.957 80.373 11.18 70.895 C13.667 62.116 14.442 51.217 10 43 C9.691 41.948 9.381 40.896 9.062 39.812 C8.537 38.42 8.537 38.42 8 37 C7.01 36.67 6.02 36.34 5 36 C5 35.01 5 34.02 5 33 C1.038 26.632 -5.093 23.622 -12 21 C-12.66 21.33 -13.32 21.66 -14 22 C-14.99 21.34 -15.98 20.68 -17 20 C-19.625 19.833 -19.625 19.833 -22 20 C-22.33 20.99 -22.66 21.98 -23 23 C-23.33 22.01 -23.66 21.02 -24 20 Z " fill="#EBE4FD" transform="translate(591,646)"/>
<path d="M0 0 C1.662 0.356 3.328 0.695 5 1 C5 1.66 5 2.32 5 3 C5.99 3.309 5.99 3.309 7 3.625 C19.984 9.576 27.987 21.024 33 34 C38.656 50.16 37.247 70.62 30 86 C25.713 93.695 19.287 104.509 11 108 C9.624 108.811 8.249 109.624 6.875 110.438 C-4.541 116.782 -16.224 117.195 -29 117 C-29 117.33 -29 117.66 -29 118 C-30.65 118 -32.3 118 -34 118 C-34 117.01 -34 116.02 -34 115 C-35.279 114.794 -36.558 114.587 -37.875 114.375 C-49.426 111.768 -59.426 103.292 -65.812 93.562 C-75.982 76.542 -78.511 58.564 -73.91 39.219 C-69.739 24.466 -61.025 11.469 -47.844 3.539 C-32.913 -4.542 -16.017 -5.339 0 0 Z M-42.688 29.75 C-44.306 31.757 -45.668 33.793 -47 36 C-47.415 36.66 -47.83 37.32 -48.258 38 C-53.25 47.775 -52.762 62.866 -49.562 73.188 C-45.603 81.973 -38.918 88.523 -30 92 C-20.803 94.786 -11.461 93.203 -3 89 C3.965 84.72 8.617 77.572 10.559 69.75 C12.289 61.92 12.317 54.903 11 47 C10.01 47.495 10.01 47.495 9 48 C9.205 47.274 9.41 46.549 9.621 45.801 C10.295 40.817 7.647 37.091 5 33 C0.299 27.384 0.299 27.384 -6 24 C-7.011 23.505 -8.021 23.01 -9.062 22.5 C-20.697 17.563 -33.943 20.682 -42.688 29.75 Z " fill="#ECE6FD" transform="translate(466,646)"/>
<path d="M0 0 C1.578 0.014 1.578 0.014 3.188 0.027 C5.75 0.051 8.312 0.083 10.875 0.125 C11.37 1.115 11.37 1.115 11.875 2.125 C13.685 3.043 13.685 3.043 15.875 3.875 C20.976 6.036 25.369 8.929 29.875 12.125 C30.535 11.795 31.195 11.465 31.875 11.125 C32.37 13.105 32.37 13.105 32.875 15.125 C33.865 15.125 34.855 15.125 35.875 15.125 C36.266 16.208 36.266 16.208 36.664 17.312 C37.916 20.221 39.44 22.532 41.25 25.125 C48.733 36.678 50.064 49.202 49.938 62.625 C49.933 63.543 49.928 64.461 49.924 65.406 C49.912 67.646 49.896 69.885 49.875 72.125 C49.215 72.125 48.555 72.125 47.875 72.125 C47.793 73.404 47.71 74.683 47.625 76 C46.571 85.2 42.602 92.04 36.875 99.125 C36.215 100.115 35.555 101.105 34.875 102.125 C28.089 108.836 20.065 114.42 10.875 117.125 C10.224 117.33 9.573 117.535 8.902 117.746 C-1.25 120.498 -12.059 119.809 -22.125 117.125 C-23.115 117.125 -24.105 117.125 -25.125 117.125 C-27.609 115.813 -29.984 114.464 -32.375 113 C-33.049 112.593 -33.724 112.185 -34.419 111.766 C-39.055 108.916 -43.091 105.784 -47.125 102.125 C-47.785 102.125 -48.445 102.125 -49.125 102.125 C-49.682 100.795 -49.682 100.795 -50.25 99.438 C-51.304 96.951 -52.418 94.516 -53.59 92.082 C-56.627 85.748 -58.879 80.036 -60.125 73.125 C-60.455 72.465 -60.785 71.805 -61.125 71.125 C-62.012 53.237 -59.873 38.816 -51.125 23.125 C-50.783 22.382 -50.442 21.64 -50.09 20.875 C-45.515 12.577 -33.038 5.532 -24.253 2.737 C-16.128 0.401 -8.429 -0.139 0 0 Z M-30.375 35.625 C-35.796 43.342 -38.182 53.788 -37.125 63.125 C-37.019 64.093 -36.914 65.061 -36.805 66.059 C-35.728 73.897 -33.524 79.526 -29.125 86.125 C-28.465 86.125 -27.805 86.125 -27.125 86.125 C-26.63 87.61 -26.63 87.61 -26.125 89.125 C-18.781 95.162 -10.44 96.99 -1.125 96.125 C7.509 94.668 14.413 90.744 19.699 83.691 C23.901 77.007 26.048 71.075 25.875 63.125 C26.061 62.341 26.246 61.558 26.438 60.75 C27.835 52.366 24.528 43.438 20.184 36.375 C14.637 28.845 8.139 24.629 -1.125 23.125 C-13.102 22.313 -22.581 26.525 -30.375 35.625 Z " fill="#EAE3FC" transform="translate(365.125,465.875)"/>
<path d="M0 0 C2.341 2.078 4.641 4.197 6.941 6.32 C8.815 8.211 8.815 8.211 11 8 C11.651 9.56 12.296 11.123 12.938 12.688 C13.477 13.993 13.477 13.993 14.027 15.324 C14.957 17.882 15.542 20.322 16 23 C17.485 23.495 17.485 23.495 19 24 C18.505 24.495 18.505 24.495 18 25 C18.287 27.007 18.619 29.009 19 31 C12.662 32.533 6.499 33.484 0 34 C-0.33 34.99 -0.66 35.98 -1 37 C-3 35 -3 35 -3.5 31.938 C-3.665 30.968 -3.83 29.999 -4 29 C-4.66 28.67 -5.32 28.34 -6 28 C-6.402 27.01 -6.402 27.01 -6.812 26 C-7.86 23.715 -7.86 23.715 -10.625 23.25 C-11.801 23.126 -11.801 23.126 -13 23 C-12.505 21.515 -12.505 21.515 -12 20 C-12.66 20.66 -13.32 21.32 -14 22 C-14.66 22.33 -15.32 22.66 -16 23 C-16.66 21.35 -17.32 19.7 -18 18 C-20.97 18.495 -20.97 18.495 -24 19 C-24 18.01 -24 17.02 -24 16 C-34.115 17.281 -42.176 18.77 -48.688 27.086 C-56.318 38.214 -57.521 50.877 -55.535 64.094 C-53.217 73.861 -48.626 80.552 -40.148 85.906 C-34.017 89.028 -26.229 88.941 -19.613 87.469 C-12.801 85.081 -8.388 81.323 -5 75 C-3.814 71.037 -2.864 67.044 -2 63 C-2.648 63.171 -3.297 63.343 -3.965 63.52 C-7.901 64.143 -11.598 63.936 -15.562 63.688 C-16.695 63.634 -16.695 63.634 -17.85 63.58 C-21.65 63.356 -24.762 63.104 -28 61 C-27.34 61 -26.68 61 -26 61 C-26.165 60.319 -26.33 59.639 -26.5 58.938 C-27.483 53.163 -27.198 49.496 -25 44 C-11.47 44 2.06 44 16 44 C16.495 43.01 16.495 43.01 17 42 C17 42.66 17 43.32 17 44 C18.32 44 19.64 44 21 44 C20.967 45.986 20.935 47.971 20.902 49.957 C20.917 52.203 20.917 52.203 22 55 C21.34 55 20.68 55 20 55 C20.023 55.739 20.046 56.477 20.07 57.238 C20.365 73.787 16.314 86.929 4.934 99.234 C-1.123 104.998 -8.42 110 -17 110 C-17.33 110.66 -17.66 111.32 -18 112 C-21.021 112.027 -24.042 112.047 -27.062 112.062 C-27.91 112.071 -28.758 112.079 -29.631 112.088 C-41.871 112.135 -52.79 109.01 -62.348 101.043 C-64.075 99.111 -65.543 97.142 -67 95 C-67.601 94.216 -68.201 93.433 -68.82 92.625 C-78.822 79.234 -81.806 63.51 -81 47 C-80.957 45.752 -80.915 44.504 -80.871 43.219 C-80.379 33.076 -80.379 33.076 -77 29 C-76.239 27.315 -75.531 25.604 -74.875 23.875 C-70.069 11.32 -59.954 2.488 -47.941 -3.197 C-33.35 -9.23 -13.311 -8.874 0 0 Z " fill="#ECE5FD" transform="translate(816,473)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C2.99 1.67 3.98 1.34 5 1 C8.35 0.744 11.704 0.769 15.062 0.75 C15.967 0.729 16.871 0.709 17.803 0.688 C26.506 0.638 34.172 2.437 41.625 7.062 C42.882 7.822 42.882 7.822 44.164 8.598 C44.77 9.06 45.376 9.523 46 10 C46 10.66 46 11.32 46 12 C46.99 12.33 47.98 12.66 49 13 C52.015 16.685 54.082 20.916 55.688 25.375 C56.706 28.989 56.706 28.989 59 31 C58.34 31.66 57.68 32.32 57 33 C56.34 33.99 55.68 34.98 55 36 C51.006 37.202 46.833 37.291 42.688 37.562 C41.433 37.646 40.179 37.73 38.887 37.816 C37.934 37.877 36.981 37.938 36 38 C35.567 36.886 35.134 35.773 34.688 34.625 C33.301 30.825 33.301 30.825 30 29 C30 28.34 30 27.68 30 27 C22.236 22.664 16.113 21.309 7.312 23.254 C4.282 24.232 2.238 25.762 0 28 C0.33 28.66 0.66 29.32 1 30 C0.01 30 -0.98 30 -2 30 C-1.959 31.217 -1.918 32.434 -1.875 33.688 C-1.852 34.372 -1.829 35.056 -1.805 35.762 C-0.551 39.248 1.962 41.086 5 43 C7.627 44.188 10.292 45.024 13.062 45.812 C13.757 46.028 14.451 46.244 15.166 46.467 C18.859 47.572 22.143 48.263 26 48 C26 48.66 26 49.32 26 50 C27.98 49.505 27.98 49.505 30 49 C30 49.66 30 50.32 30 51 C31.279 51.247 32.558 51.495 33.875 51.75 C44.077 54.056 53.845 57.909 59.875 66.875 C64.819 75.664 65.153 85.074 62.75 94.75 C61.458 99.077 59.755 102.383 57 106 C56.62 106.793 56.239 107.586 55.848 108.402 C53.299 111.985 50.344 113.323 46.438 115.188 C45.772 115.53 45.106 115.872 44.42 116.225 C35.657 120.527 26.781 121.37 17.125 121.312 C16.165 121.308 15.205 121.304 14.215 121.3 C-2.727 121.03 -2.727 121.03 -8.055 116.098 C-8.697 115.405 -9.339 114.713 -10 114 C-11.327 112.888 -12.66 111.784 -14 110.688 C-17.756 107.448 -19.76 104.394 -22 100 C-22.66 99.34 -23.32 98.68 -24 98 C-25.23 94.311 -25.557 90.863 -26 87 C-21.373 84.833 -16.689 84.149 -11.688 83.375 C-10.434 83.174 -10.434 83.174 -9.154 82.969 C-7.104 82.641 -5.052 82.32 -3 82 C-2.671 83.121 -2.671 83.121 -2.336 84.266 C-0.916 88.675 0.214 92.173 3.438 95.562 C13.057 99.444 22.388 99.892 32.066 96.215 C35.491 94.564 37.056 92.911 39.062 89.688 C40.172 85.324 39.802 83.095 38 79 C37.01 78.67 36.02 78.34 35 78 C34.67 77.34 34.34 76.68 34 76 C31.66 74.869 31.66 74.869 28.938 73.875 C28.018 73.522 27.099 73.169 26.152 72.805 C25.442 72.539 24.732 72.274 24 72 C23.67 72.66 23.34 73.32 23 74 C22.67 73.34 22.34 72.68 22 72 C20.11 71.271 20.11 71.271 17.801 70.738 C16.491 70.396 16.491 70.396 15.154 70.047 C14.217 69.804 13.279 69.562 12.312 69.312 C-1.4 65.581 -13.533 61.136 -22 49 C-25.841 41.317 -26.35 33.152 -24.168 24.824 C-22.77 20.87 -21.331 17.497 -19 14 C-18.34 14 -17.68 14 -17 14 C-16.771 13.403 -16.541 12.806 -16.305 12.191 C-14.648 9.409 -12.94 8.504 -10.125 6.938 C-4.735 3.928 -4.735 3.928 0 0 Z " fill="#ECE5FD" transform="translate(313,642)"/>
<path d="M0 0 C6.412 5.729 11.066 13.776 13.309 22.062 C13.406 24.855 13.406 24.855 13.309 27.062 C-4.12 31.062 -4.12 31.062 -10.691 31.062 C-11.681 28.587 -11.681 28.587 -12.691 26.062 C-13.351 26.062 -14.011 26.062 -14.691 26.062 C-14.753 25.341 -14.815 24.619 -14.879 23.875 C-15.998 20.002 -17.531 18.564 -20.691 16.062 C-29.024 12.009 -37.492 12.006 -46.199 14.895 C-52.886 18.028 -57.905 23.215 -60.938 29.91 C-63.155 36.24 -64.048 41.718 -64.004 48.375 C-64 49.214 -63.997 50.052 -63.993 50.917 C-63.847 59.28 -62.323 66.004 -57.691 73.062 C-57.691 73.722 -57.691 74.382 -57.691 75.062 C-57.031 75.062 -56.371 75.062 -55.691 75.062 C-54.404 76.35 -53.155 77.676 -51.957 79.047 C-49.239 81.465 -46.908 82.258 -43.441 83.312 C-41.825 83.815 -41.825 83.815 -40.176 84.328 C-39.356 84.57 -38.536 84.813 -37.691 85.062 C-37.361 84.403 -37.031 83.743 -36.691 83.062 C-36.691 83.722 -36.691 84.382 -36.691 85.062 C-25.066 83.459 -25.066 83.459 -15.691 77.062 C-14.077 74.49 -12.864 71.858 -11.691 69.062 C-11.033 67.725 -10.369 66.39 -9.691 65.062 C3.772 64.698 3.772 64.698 10.309 67.062 C11.64 67.404 12.972 67.742 14.309 68.062 C14.025 75.576 11.144 80.697 7.309 87.062 C6.731 88.114 6.154 89.166 5.559 90.25 C0.225 96.917 -6.866 101.768 -14.691 105.062 C-15.681 105.062 -16.671 105.062 -17.691 105.062 C-18.021 105.722 -18.351 106.382 -18.691 107.062 C-33.82 110.053 -50.249 108.855 -63.629 100.812 C-67.916 97.861 -71.534 94.507 -75.035 90.664 C-76.706 88.818 -76.706 88.818 -79.691 88.062 C-79.795 87.361 -79.898 86.66 -80.004 85.938 C-80.784 82.675 -82.169 80.041 -83.691 77.062 C-83.691 76.403 -83.691 75.743 -83.691 75.062 C-84.351 74.732 -85.011 74.403 -85.691 74.062 C-86.021 73.072 -86.351 72.082 -86.691 71.062 C-87.228 70.526 -87.764 69.99 -88.316 69.438 C-90.097 66.362 -89.481 64.358 -88.945 60.949 C-88.624 57.299 -88.941 53.763 -89.254 50.125 C-89.769 43.797 -89.47 38.179 -87.691 32.062 C-88.021 32.062 -88.351 32.062 -88.691 32.062 C-88.691 30.413 -88.691 28.762 -88.691 27.062 C-87.701 27.392 -86.711 27.722 -85.691 28.062 C-85.258 26.237 -85.258 26.237 -84.816 24.375 C-81.776 12.192 -73.974 3.538 -63.594 -3.324 C-43.562 -15.089 -18.694 -13.457 0 0 Z M-87.691 64.062 C-86.691 68.062 -86.691 68.062 -86.691 68.062 Z " fill="#ECE5FD" transform="translate(273.69140625,476.9375)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.008 1.83 3.016 2.661 3.025 3.517 C3.102 11.345 3.19 19.172 3.288 27 C3.337 31.024 3.384 35.048 3.422 39.073 C3.459 42.957 3.505 46.841 3.558 50.724 C3.577 52.206 3.592 53.687 3.603 55.169 C3.62 57.245 3.649 59.32 3.681 61.396 C3.694 62.577 3.708 63.758 3.722 64.975 C3.993 67.919 4.599 69.463 6 72 C6 72.99 6 73.98 6 75 C6.99 75.33 7.98 75.66 9 76 C8.01 76.495 8.01 76.495 7 77 C8.196 77.516 9.393 78.031 10.625 78.562 C17.203 81.898 20.55 86.419 23.172 93.242 C23.692 94.582 23.692 94.582 24.222 95.949 C24.479 96.626 24.735 97.303 25 98 C25.695 97.998 26.39 97.996 27.106 97.993 C33.654 97.973 40.201 97.958 46.749 97.948 C50.115 97.943 53.482 97.936 56.848 97.925 C60.718 97.912 64.587 97.907 68.457 97.902 C69.666 97.897 70.876 97.892 72.122 97.887 C73.803 97.887 73.803 97.887 75.519 97.886 C76.507 97.884 77.495 97.882 78.513 97.88 C81 98 81 98 84 99 C84 102.3 84 105.6 84 109 C82.298 109.186 82.298 109.186 80.562 109.375 C78.799 109.684 78.799 109.684 77 110 C76.67 110.66 76.34 111.32 76 112 C74.667 112 73.333 112 72 112 C72 111.01 72 110.02 72 109 C67.052 109.118 62.104 109.242 57.156 109.372 C55.472 109.416 53.788 109.457 52.104 109.497 C49.687 109.555 47.269 109.619 44.852 109.684 C44.096 109.7 43.341 109.717 42.563 109.734 C38.653 109.776 38.653 109.776 35 111 C32.723 110.991 30.465 110.87 28.191 110.742 C25.395 111.071 24.74 111.831 23 114 C22.024 115.299 21.066 116.613 20.125 117.938 C16.137 123.288 11.613 127.326 5 129 C-2.645 129.885 -9.499 129.378 -16 125 C-17.923 123.11 -19.387 121.183 -21 119 C-21.99 118.34 -22.98 117.68 -24 117 C-24 116.01 -24 115.02 -24 114 C-24.33 113.196 -24.66 112.391 -25 111.562 C-27.221 103.651 -26.814 96.24 -23.094 88.906 C-22.157 87.273 -21.176 85.665 -20.188 84.062 C-19.796 83.382 -19.404 82.701 -19 82 C-19.33 81.34 -19.66 80.68 -20 80 C-19.278 80.082 -18.556 80.165 -17.812 80.25 C-14.165 79.926 -12.654 78.427 -10 76 C-10.33 74.68 -10.66 73.36 -11 72 C-10.01 72.33 -9.02 72.66 -8 73 C-8.002 72.138 -8.004 71.276 -8.007 70.387 C-8.027 62.279 -8.042 54.171 -8.052 46.062 C-8.057 41.893 -8.064 37.724 -8.075 33.555 C-8.086 29.535 -8.092 25.515 -8.095 21.495 C-8.097 19.958 -8.1 18.421 -8.106 16.884 C-8.113 14.739 -8.114 12.593 -8.114 10.448 C-8.116 9.224 -8.118 8 -8.12 6.74 C-8 4 -8 4 -7 3 C-4.667 2.959 -2.333 2.958 0 3 C0 2.01 0 1.02 0 0 Z M-12 92 C-14.7 96.769 -15.583 100.554 -15 106 C-13.355 111.109 -11.732 114.326 -7 117 C-2.449 118.22 0.927 118.397 5.375 116.75 C9.932 113.803 9.932 113.803 11.688 108.812 C11.791 107.884 11.894 106.956 12 106 C12.33 106 12.66 106 13 106 C13.377 101.288 13.463 97.926 11.312 93.625 C8.301 90.206 6.288 88.31 1.668 87.684 C-4.011 87.42 -7.722 87.931 -12 92 Z " fill="#9699FA" transform="translate(512,152)"/>
<path d="M0 0 C1.98 0.99 3.96 1.98 6 3 C5.01 3.33 4.02 3.66 3 4 C3 41.95 3 79.9 3 119 C-18 119 -18 119 -21 118 C-21 117.34 -21 116.68 -21 116 C-21.66 115.67 -22.32 115.34 -23 115 C-22.691 113.824 -22.691 113.824 -22.375 112.625 C-22.251 111.759 -22.128 110.893 -22 110 C-22.66 109.34 -23.32 108.68 -24 108 C-23.67 107.01 -23.34 106.02 -23 105 C-22.214 101.307 -21.76 98.596 -23 95 C-23.33 94.01 -23.66 93.02 -24 92 C-23.34 91.01 -22.68 90.02 -22 89 C-22.165 88.051 -22.33 87.102 -22.5 86.125 C-23.057 82.645 -22.55 81.1 -21 78 C-21.66 78 -22.32 78 -23 78 C-23.33 74.7 -23.66 71.4 -24 68 C-23.01 67.505 -23.01 67.505 -22 67 C-22.186 65.804 -22.371 64.607 -22.562 63.375 C-22.957 60.832 -22.993 58.964 -22.516 56.417 C-21.886 52.243 -21.857 48.202 -21.867 43.988 C-21.866 43.138 -21.865 42.288 -21.864 41.412 C-21.864 39.625 -21.865 37.837 -21.87 36.05 C-21.875 33.311 -21.87 30.573 -21.863 27.834 C-21.864 26.094 -21.865 24.354 -21.867 22.613 C-21.865 21.795 -21.863 20.976 -21.861 20.132 C-21.875 16.833 -21.95 14.151 -23 11 C-22.545 8.262 -21.747 5.68 -21 3 C-14.312 2.376 -7.719 1.871 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#EDE6FE" transform="translate(600,465)"/>
<path d="M0 0 C0.66 -0.66 1.32 -1.32 2 -2 C5.625 -2.125 5.625 -2.125 9 -2 C9.495 0.97 9.495 0.97 10 4 C10.33 4 10.66 4 11 4 C12.25 11.625 12.25 11.625 10 15 C9.833 18.125 9.833 18.125 10 21 C10.33 21 10.66 21 11 21 C11.495 24.465 11.495 24.465 12 28 C11.01 28 10.02 28 9 28 C9 29.32 9 30.64 9 32 C9.66 32 10.32 32 11 32 C10.67 32.99 10.34 33.98 10 35 C9.658 36.331 9.322 37.664 9 39 C9.66 39 10.32 39 11 39 C11 40.65 11 42.3 11 44 C10.34 44 9.68 44 9 44 C8.546 45.217 8.092 46.434 7.625 47.688 C6.455 50.808 5.254 53.912 4 57 C3.01 57 2.02 57 1 57 C1.33 57.99 1.66 58.98 2 60 C1.01 60 0.02 60 -1 60 C-1 59.34 -1 58.68 -1 58 C-1.629 58.156 -2.258 58.312 -2.906 58.473 C-10.056 60.099 -15.435 60.714 -22.25 57.438 C-25.604 54.465 -27.714 51.318 -29 47 C-28 44 -28 44 -26 41 C-25.34 41 -24.68 41 -24 41 C-24 41.66 -24 42.32 -24 43 C-23.34 43 -22.68 43 -22 43 C-22.66 42.01 -23.32 41.02 -24 40 C-24.99 40 -25.98 40 -27 40 C-26.814 39.423 -26.629 38.845 -26.438 38.25 C-25.953 35.758 -26.105 34.36 -27 32 C-27.66 31.67 -28.32 31.34 -29 31 C-31.215 27.252 -32.609 24.388 -32 20 C-31.34 19.34 -30.68 18.68 -30 18 C-30.66 17.67 -31.32 17.34 -32 17 C-31.34 17 -30.68 17 -30 17 C-29.986 16.336 -29.972 15.672 -29.957 14.988 C-29.567 7.767 -29.567 7.767 -26.875 4.125 C-17.565 -2.756 -11.121 -3.961 0 0 Z M-20 11 C-21.874 16.621 -21.9 23.366 -19.938 29 C-17.418 32.902 -15.448 33.794 -11 35 C-6.657 35.087 -5.387 34.361 -2.188 31.375 C0.269 28.094 0.921 25.118 1 21 C0.34 20.34 -0.32 19.68 -1 19 C-1.625 16.875 -1.625 16.875 -2 15 C-1.34 15 -0.68 15 0 15 C-0.849 11.528 -1.436 9.483 -4.188 7.125 C-11.212 4.315 -15.026 5.908 -20 11 Z M0 37 C1 39 1 39 1 39 Z M-1 39 C-1.887 39.454 -2.774 39.908 -3.688 40.375 C-8.009 42.495 -11.859 43.459 -16.625 42.438 C-19.648 41.881 -21.281 42.679 -24 44 C-24 44.66 -24 45.32 -24 46 C-22.35 46 -20.7 46 -19 46 C-18.711 46.639 -18.423 47.279 -18.125 47.938 C-17.172 50.152 -17.172 50.152 -15 51 C-10.554 51.533 -7.059 51.563 -3.062 49.438 C-0.528 46.442 0.195 43.806 1 40 C0.34 39.67 -0.32 39.34 -1 39 Z " fill="#EADFFE" transform="translate(507,868)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.701 2.186 1.403 2.371 2.125 2.562 C7.556 5.278 10.778 9.708 12.812 15.375 C14.131 19.882 14.629 24.327 15 29 C15.66 29 16.32 29 17 29 C16.505 30.485 16.505 30.485 16 32 C15.34 31.67 14.68 31.34 14 31 C14.046 31.594 14.093 32.189 14.141 32.801 C14.564 42.533 11.7 50.987 5.488 58.539 C3.306 60.681 2.066 61 -1 61 C-1.744 61.058 -2.488 61.116 -3.254 61.176 C-9.376 61.503 -14.033 60.726 -19 57 C-23.566 52.624 -24.992 47.381 -26.422 41.355 C-26.908 39 -26.908 39 -28 37 C-28.07 34.237 -27.997 31.489 -27.938 28.727 C-27.884 26.06 -27.884 26.06 -28.621 23.82 C-28.746 23.22 -28.871 22.619 -29 22 C-27.062 19.25 -27.062 19.25 -25 17 C-26.32 16.67 -27.64 16.34 -29 16 C-28.097 15.901 -28.097 15.901 -27.176 15.801 C-24.117 14.675 -23.643 13.052 -22.188 10.188 C-18.897 4.372 -15.956 2.187 -9.5 0.25 C-6.322 -0.068 -3.191 -0.063 0 0 Z M-9 9 C-9.66 9.66 -10.32 10.32 -11 11 C-10.67 11.33 -10.34 11.66 -10 12 C-11.65 12.33 -13.3 12.66 -15 13 C-18.924 22.507 -20.156 35.194 -16.562 45 C-15.29 47.81 -15.29 47.81 -14 49 C-13.585 49.503 -13.17 50.005 -12.742 50.523 C-9.867 52.96 -7.088 52.656 -3.449 52.539 C-0.609 51.914 0.294 51.272 2.062 49 C5.251 43.476 5.576 37.136 4.438 30.938 C4 28 4 28 5 26 C5.166 20.178 3.874 15.98 0.449 11.312 C-2.407 8.725 -5.241 9 -9 9 Z M-27 21 C-26 23 -26 23 -26 23 Z " fill="#8375DA" transform="translate(757,851)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 2.98 2 4.96 2 7 C2.99 7.495 2.99 7.495 4 8 C3.34 8 2.68 8 2 8 C2.008 8.815 2.008 8.815 2.016 9.647 C2.141 22.867 2.266 36.087 2.391 49.307 C2.452 55.7 2.512 62.093 2.573 68.486 C2.631 74.652 2.69 80.818 2.748 86.984 C2.77 89.34 2.792 91.696 2.815 94.052 C2.846 97.344 2.877 100.637 2.908 103.929 C2.918 104.909 2.927 105.89 2.937 106.9 C2.945 107.796 2.953 108.692 2.962 109.615 C2.969 110.394 2.977 111.173 2.984 111.975 C3.005 114.65 3 117.325 3 120 C-1.416 120.174 -5.831 120.281 -10.25 120.375 C-12.131 120.45 -12.131 120.45 -14.051 120.527 C-23.088 120.672 -23.088 120.672 -25.752 118.708 C-27.246 116.54 -28 114.645 -28 112 C-28.99 111.67 -29.98 111.34 -31 111 C-31 110.34 -31 109.68 -31 109 C-32.294 106.592 -33.619 104.232 -35 101.875 C-35.382 101.216 -35.763 100.558 -36.156 99.879 C-37.1 98.25 -38.05 96.625 -39 95 C-39.66 95 -40.32 95 -41 95 C-42 92 -42 92 -42 89 C-42.848 87.233 -43.777 85.504 -44.746 83.801 C-45.337 82.755 -45.928 81.709 -46.537 80.631 C-47.164 79.536 -47.791 78.441 -48.438 77.312 C-49.03 76.268 -49.622 75.223 -50.232 74.146 C-52.151 70.762 -54.076 67.381 -56 64 C-58.97 58.72 -61.94 53.44 -65 48 C-65.33 71.76 -65.66 95.52 -66 120 C-74.25 120 -82.5 120 -91 120 C-90.67 118.68 -90.34 117.36 -90 116 C-89.915 114.29 -89.882 112.577 -89.886 110.864 C-89.887 109.844 -89.887 108.823 -89.887 107.771 C-89.892 106.676 -89.897 105.581 -89.902 104.453 C-89.904 103.327 -89.905 102.2 -89.907 101.04 C-89.912 97.443 -89.925 93.847 -89.938 90.25 C-89.943 87.811 -89.947 85.372 -89.951 82.934 C-89.962 76.956 -89.979 70.978 -90 65 C-90.33 65 -90.66 65 -91 65 C-92.361 56.836 -92.361 56.836 -90 53 C-90.33 52.34 -90.66 51.68 -91 51 C-90.34 51 -89.68 51 -89 51 C-89.036 50.393 -89.071 49.787 -89.108 49.162 C-89.764 37.486 -90.159 25.878 -89.688 14.188 C-89.653 13.21 -89.618 12.233 -89.582 11.226 C-89.538 10.32 -89.494 9.414 -89.449 8.48 C-89.411 7.685 -89.374 6.89 -89.335 6.071 C-89 4 -89 4 -87 1 C-86.01 1.33 -85.02 1.66 -84 2 C-84 2.66 -84 3.32 -84 4 C-76.74 4 -69.48 4 -62 4 C-61.67 5.65 -61.34 7.3 -61 9 C-60.156 10.952 -60.156 10.952 -59.066 12.793 C-58.67 13.496 -58.273 14.2 -57.865 14.925 C-57.435 15.671 -57.005 16.418 -56.562 17.188 C-55.648 18.802 -54.734 20.417 -53.82 22.031 C-53.351 22.855 -52.883 23.679 -52.399 24.527 C-50.633 27.648 -48.906 30.789 -47.179 33.931 C-43.396 40.805 -39.493 47.582 -35.414 54.284 C-30.71 62.057 -26.37 70.036 -22 78 C-22.33 76.02 -22.66 74.04 -23 72 C-22.34 72 -21.68 72 -21 72 C-21 49.56 -21 27.12 -21 4 C-15.39 4 -9.78 4 -4 4 C-4 3.34 -4 2.68 -4 2 C-2.68 2.66 -1.36 3.32 0 4 C0 2.68 0 1.36 0 0 Z M-87 6 C-87 42.96 -87 79.92 -87 118 C-80.4 118 -73.8 118 -67 118 C-67 92.26 -67 66.52 -67 40 C-64.36 45.28 -61.72 50.56 -59 56 C-58.34 56.66 -57.68 57.32 -57 58 C-56.67 58.99 -56.34 59.98 -56 61 C-55.13 62.494 -54.23 63.971 -53.312 65.438 C-51.147 68.995 -49.185 72.563 -47.375 76.312 C-43.152 84.925 -38.255 93.167 -33.46 101.469 C-31.379 105.077 -29.314 108.695 -27.25 112.312 C-26.178 114.189 -25.105 116.066 -24 118 C-16.08 118 -8.16 118 0 118 C0 81.04 0 44.08 0 6 C-6.27 6 -12.54 6 -19 6 C-19.33 32.07 -19.66 58.14 -20 85 C-32.014 64.249 -32.014 64.249 -35.644 57.51 C-38.229 52.726 -40.903 47.992 -43.562 43.25 C-44.16 42.184 -44.758 41.118 -45.374 40.02 C-47.248 36.679 -49.124 33.339 -51 30 C-51.626 28.886 -52.252 27.771 -52.896 26.623 C-54.7 23.414 -56.506 20.207 -58.312 17 C-58.869 16.009 -59.426 15.018 -60 13.997 C-60.512 13.091 -61.024 12.184 -61.551 11.25 C-62.001 10.451 -62.451 9.652 -62.915 8.828 C-63.87 7.048 -63.87 7.048 -65 6 C-72.26 6 -79.52 6 -87 6 Z M-22 73 C-21 75 -21 75 -21 75 Z " fill="#8176D3" transform="translate(732,640)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C1.66 2 2.32 2 3 2 C3.025 6.948 3.043 11.896 3.055 16.844 C3.06 18.528 3.067 20.212 3.075 21.896 C3.088 24.313 3.093 26.731 3.098 29.148 C3.103 29.904 3.108 30.659 3.113 31.437 C3.114 36.772 3.114 36.772 2 39 C2.99 38.67 3.98 38.34 5 38 C5 39.32 5 40.64 5 42 C6.98 41.505 6.98 41.505 9 41 C9.778 44.406 10.1 47.508 10 51 C7.69 51.66 5.38 52.32 3 53 C3.041 53.949 3.082 54.898 3.125 55.875 C3 59 3 59 1 61 C-1.729 60.967 -4.297 60.436 -7 60 C-7 57.36 -7 54.72 -7 52 C-7.955 52.238 -7.955 52.238 -8.93 52.48 C-13.007 53.17 -16.996 53.263 -21.125 53.312 C-21.936 53.342 -22.747 53.371 -23.582 53.4 C-29.452 53.463 -29.452 53.463 -32.074 51.246 C-33.604 47.535 -32.715 44.898 -32 41 C-30.515 40.505 -30.515 40.505 -29 40 C-28.281 38.355 -27.63 36.681 -27 35 C-25.746 32.672 -24.439 30.378 -23.121 28.086 C-21.875 25.91 -21.875 25.91 -21 23 C-20.67 23.33 -20.34 23.66 -20 24 C-19.636 23.31 -19.273 22.621 -18.898 21.91 C-9.853 5.002 -9.853 5.002 -5 1 C-2.312 1.312 -2.312 1.312 0 2 C0 1.34 0 0.68 0 0 Z M-7 17 C-7.66 18.32 -8.32 19.64 -9 21 C-8.01 20.67 -7.02 20.34 -6 20 C-6.33 19.01 -6.66 18.02 -7 17 Z M-9 22 C-9 22.99 -9 23.98 -9 25 C-8.34 25 -7.68 25 -7 25 C-7.33 24.01 -7.66 23.02 -8 22 C-8.33 22 -8.66 22 -9 22 Z M-11 24 C-12.364 26.119 -13.716 28.244 -15.062 30.375 C-15.647 31.28 -15.647 31.28 -16.244 32.203 C-17.225 33.765 -18.119 35.38 -19 37 C-18.67 37.66 -18.34 38.32 -18 39 C-17.34 39 -16.68 39 -16 39 C-15.34 39.66 -14.68 40.32 -14 41 C-10.403 40.677 -10.403 40.677 -7 40 C-6.173 37.52 -5.98 36.098 -6.5 33.5 C-7.02 30.902 -6.827 29.48 -6 27 C-6.639 26.876 -7.279 26.753 -7.938 26.625 C-8.958 26.316 -8.958 26.316 -10 26 C-10.33 25.34 -10.66 24.68 -11 24 Z M-20 39 C-19 41 -19 41 -19 41 Z M3 40 C2.67 40.66 2.34 41.32 2 42 C2.66 42 3.32 42 4 42 C3.67 41.34 3.34 40.68 3 40 Z M-29 50 C-29 50.33 -29 50.66 -29 51 C-23.06 51 -17.12 51 -11 51 C-11 50.67 -11 50.34 -11 50 C-16.94 50 -22.88 50 -29 50 Z M3 50 C7 51 7 51 7 51 Z " fill="#7B6DD7" transform="translate(846,851)"/>
<path d="M0 0 C0.75 -0.004 1.5 -0.008 2.273 -0.012 C9.394 0.001 9.394 0.001 12.375 1.125 C13.384 2.976 14.376 4.839 15.25 6.758 C16.44 9.262 17.801 11.575 19.25 13.938 C21.316 17.324 23.291 20.74 25.184 24.227 C25.704 25.185 26.225 26.144 26.761 27.131 C28.342 30.064 29.913 33.002 31.48 35.941 C31.989 36.89 32.498 37.838 33.021 38.814 C34.066 40.762 35.105 42.713 36.139 44.666 C36.643 45.603 37.148 46.539 37.668 47.504 C38.341 48.766 38.341 48.766 39.028 50.053 C40.36 52.4 40.36 52.4 43.375 54.125 C43.375 54.785 43.375 55.445 43.375 56.125 C44.336 57.979 45.343 59.809 46.375 61.625 C49.375 66.903 49.375 66.903 49.375 69.125 C50.035 69.455 50.695 69.785 51.375 70.125 C52.035 70.785 52.695 71.445 53.375 72.125 C53.189 71.362 53.004 70.599 52.812 69.812 C52.331 65.751 52.686 62.197 53.113 58.148 C53.951 49.019 53.951 49.019 52.375 40.125 C52.705 39.135 53.035 38.145 53.375 37.125 C53.459 34.803 53.482 32.479 53.473 30.156 C53.471 29.48 53.47 28.803 53.468 28.106 C53.463 25.945 53.45 23.785 53.438 21.625 C53.432 20.171 53.428 18.716 53.424 17.262 C53.412 13.549 53.394 9.837 53.375 6.125 C53.375 3.125 53.375 3.125 54.375 1.125 C57.27 0.93 60.166 0.743 63.062 0.562 C63.879 0.506 64.696 0.45 65.537 0.393 C66.731 0.32 66.731 0.32 67.949 0.246 C68.677 0.199 69.405 0.152 70.155 0.103 C72.665 0.128 74.932 0.567 77.375 1.125 C77.364 2.955 77.364 2.955 77.353 4.822 C77.304 17.646 77.429 30.465 77.622 43.288 C77.664 46.121 77.705 48.954 77.746 51.787 C77.773 53.712 77.801 55.636 77.828 57.561 C77.849 59.003 77.849 59.003 77.87 60.474 C77.941 65.424 78.017 70.373 78.099 75.323 C78.179 80.181 78.249 85.039 78.313 89.897 C78.338 91.733 78.368 93.568 78.401 95.403 C78.447 97.95 78.48 100.497 78.51 103.044 C78.535 104.166 78.535 104.166 78.56 105.312 C78.593 109.355 78.31 112.535 76.375 116.125 C73.342 117.517 70.237 117.168 66.953 117.004 C65.928 116.956 64.904 116.907 63.848 116.857 C62.784 116.801 61.721 116.745 60.625 116.688 C59.546 116.635 58.467 116.583 57.355 116.529 C54.695 116.4 52.035 116.265 49.375 116.125 C49.121 115.487 48.867 114.849 48.605 114.191 C48.261 113.365 47.917 112.539 47.562 111.688 C47.226 110.864 46.89 110.04 46.543 109.191 C45.491 106.791 45.491 106.791 42.375 106.125 C42.22 104.795 42.22 104.795 42.062 103.438 C41.36 100.054 40.215 98.006 38.375 95.125 C38.375 94.465 38.375 93.805 38.375 93.125 C37.385 92.795 36.395 92.465 35.375 92.125 C34.642 90.148 33.985 88.143 33.375 86.125 C32.453 84.24 31.484 82.376 30.477 80.535 C29.915 79.503 29.352 78.471 28.773 77.408 C28.188 76.345 27.603 75.283 27 74.188 C26.423 73.129 25.845 72.07 25.25 70.979 C22.869 66.621 20.477 62.277 17.961 57.996 C16.375 55.125 16.375 55.125 16.375 53.125 C15.055 52.795 13.735 52.465 12.375 52.125 C13.365 51.63 13.365 51.63 14.375 51.125 C11.9 46.67 11.9 46.67 9.375 42.125 C9.045 66.545 8.715 90.965 8.375 116.125 C-10.625 116.125 -10.625 116.125 -12.625 115.125 C-13.12 116.115 -13.12 116.115 -13.625 117.125 C-14.285 117.125 -14.945 117.125 -15.625 117.125 C-15.648 102.443 -15.666 87.762 -15.677 73.08 C-15.682 66.264 -15.689 59.447 -15.7 52.63 C-15.711 46.055 -15.717 39.481 -15.72 32.906 C-15.722 30.394 -15.725 27.882 -15.731 25.369 C-15.738 21.859 -15.739 18.349 -15.739 14.839 C-15.742 13.793 -15.746 12.747 -15.749 11.669 C-15.748 10.714 -15.747 9.76 -15.745 8.776 C-15.746 7.945 -15.747 7.115 -15.748 6.259 C-15.625 4.125 -15.625 4.125 -14.625 1.125 C-9.696 0.139 -5.001 -0.026 0 0 Z M-12.625 2.125 C-12.625 39.085 -12.625 76.045 -12.625 114.125 C-6.355 114.125 -0.085 114.125 6.375 114.125 C6.375 87.725 6.375 61.325 6.375 34.125 C7.035 34.125 7.695 34.125 8.375 34.125 C8.447 34.768 8.519 35.411 8.594 36.074 C9.7 40.393 11.763 44.031 13.875 47.938 C14.545 49.191 14.545 49.191 15.228 50.47 C20.459 60.224 25.784 69.934 31.562 79.375 C33.598 82.716 35.367 86.1 37.062 89.625 C37.574 90.61 38.086 91.595 38.613 92.609 C39.57 94.519 40.507 96.443 41.326 98.416 C41.666 99.233 42.005 100.049 42.355 100.891 C42.655 101.641 42.954 102.392 43.262 103.165 C45.45 107.149 48.529 110.581 51.375 114.125 C59.295 114.125 67.215 114.125 75.375 114.125 C75.375 77.165 75.375 40.205 75.375 2.125 C68.775 2.125 62.175 2.125 55.375 2.125 C55.045 27.865 54.715 53.605 54.375 80.125 C52.395 76.495 50.415 72.865 48.375 69.125 C39.741 53.358 31.074 37.626 21.912 22.159 C19.006 17.24 16.217 12.27 13.531 7.227 C12.307 4.931 12.307 4.931 10.375 2.125 C2.785 2.125 -4.805 2.125 -12.625 2.125 Z M53.375 72.125 C53.045 72.785 52.715 73.445 52.375 74.125 C53.035 74.785 53.695 75.445 54.375 76.125 C54.045 74.805 53.715 73.485 53.375 72.125 Z " fill="#5C56B8" transform="translate(639.625,466.875)"/>
<path d="M0 0 C1.915 -0.108 3.833 -0.186 5.75 -0.25 C7.351 -0.32 7.351 -0.32 8.984 -0.391 C12.675 0.087 13.644 1.219 16 4 C16.66 4.33 17.32 4.66 18 5 C18 5.66 18 6.32 18 7 C18.99 6.67 19.98 6.34 21 6 C22.333 11.333 23.667 16.667 25 22 C24.34 22 23.68 22 23 22 C22.959 22.969 22.918 23.939 22.875 24.938 C21.767 32.554 15.964 38.32 10.613 43.434 C8.515 45.471 6.699 47.621 5 50 C5 50.66 5 51.32 5 52 C12.26 52.33 19.52 52.66 27 53 C26.01 53.495 26.01 53.495 25 54 C25 55.65 25 57.3 25 59 C25.99 59.33 26.98 59.66 28 60 C24.133 62.574 20.058 62.306 15.586 62.301 C14.775 62.305 13.964 62.309 13.129 62.314 C11.419 62.319 9.71 62.32 8 62.316 C5.377 62.313 2.755 62.336 0.133 62.361 C-1.529 62.364 -3.19 62.364 -4.852 62.363 C-5.638 62.372 -6.424 62.382 -7.235 62.391 C-9.44 62.372 -9.44 62.372 -13 62 C-15 59 -15 59 -14.625 56.25 C-12.694 52.388 -10.408 50.445 -7.102 47.746 C-5.137 46.114 -3.387 44.407 -1.625 42.562 C1 40 1 40 3 40 C3.266 39.413 3.531 38.827 3.805 38.223 C5.384 35.287 7.498 33.111 9.758 30.684 C11.297 28.946 11.297 28.946 11 26 C13 23 13 23 14 22 C13.713 19.661 13.381 17.326 13 15 C7.495 13.05 3.743 12.735 -2 14 C-3.96 16.941 -4.378 18.623 -5 22 C-6 23 -6 23 -9.5 23.125 C-13 23 -13 23 -14 22 C-13.713 19.661 -13.381 17.326 -13 15 C-13.66 14.67 -14.32 14.34 -15 14 C-14.01 14 -13.02 14 -12 14 C-11.918 13.443 -11.835 12.886 -11.75 12.312 C-10.761 9.263 -9.152 7.348 -7 5 C-6.34 5 -5.68 5 -5 5 C-4.67 4.34 -4.34 3.68 -4 3 C-1.938 1.875 -1.938 1.875 0 1 C0 0.67 0 0.34 0 0 Z M6 1 C10 2 10 2 10 2 Z M2 10 C2 10.66 2 11.32 2 12 C2.99 11.67 3.98 11.34 5 11 C4.01 10.67 3.02 10.34 2 10 Z " fill="#EEE4FD" transform="translate(700,850)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.746 2.286 2.493 2.572 3.262 2.867 C6.405 4.168 8.323 5.821 10.812 8.125 C11.603 8.849 12.393 9.574 13.207 10.32 C13.799 10.875 14.39 11.429 15 12 C14.648 12.608 14.296 13.217 13.934 13.844 C13.626 14.555 13.317 15.267 13 16 C13.351 16.825 13.701 17.65 14.062 18.5 C14.372 19.325 14.681 20.15 15 21 C13.875 22.812 13.875 22.812 12 25 C11.464 26.196 10.928 27.393 10.375 28.625 C8.083 33.424 4.607 36.92 0.926 40.703 C-0.94 42.928 -1.617 44.169 -2 47 C-2.928 47.155 -2.928 47.155 -3.875 47.312 C-6.365 47.875 -6.365 47.875 -8 51 C-7.374 50.995 -6.749 50.99 -6.104 50.984 C-3.278 50.963 -0.451 50.95 2.375 50.938 C3.36 50.929 4.345 50.921 5.359 50.912 C6.771 50.907 6.771 50.907 8.211 50.902 C9.515 50.894 9.515 50.894 10.845 50.886 C13 51 13 51 15 52 C14.67 54.64 14.34 57.28 14 60 C8.258 61.748 2.128 61.184 -3.812 61.188 C-5.701 61.206 -5.701 61.206 -7.627 61.225 C-13.784 61.234 -19.209 61.19 -25 59 C-23.79 53.48 -22.348 50.754 -18.133 46.785 C-17.657 46.326 -17.181 45.866 -16.69 45.393 C-15.18 43.937 -13.655 42.498 -12.125 41.062 C-1.69 31.826 -1.69 31.826 3.391 19.25 C3.344 18.508 3.298 17.765 3.25 17 C3.196 15.855 3.196 15.855 3.141 14.688 C3.094 14.131 3.048 13.574 3 13 C1.907 13 0.814 13 -0.312 13 C-2.542 13 -4.771 13 -7 13 C-7.66 12.01 -8.32 11.02 -9 10 C-12.393 12.857 -14.226 14.942 -15.125 19.25 C-15.414 20.487 -15.702 21.725 -16 23 C-16.99 23.33 -17.98 23.66 -19 24 C-19 23.34 -19 22.68 -19 22 C-19.99 22.33 -20.98 22.66 -22 23 C-22.66 22.34 -23.32 21.68 -24 21 C-24 20.01 -24 19.02 -24 18 C-24.66 17.67 -25.32 17.34 -26 17 C-25.01 16.67 -24.02 16.34 -23 16 C-23.33 14.68 -23.66 13.36 -24 12 C-23.34 12 -22.68 12 -22 12 C-21.751 11.242 -21.502 10.484 -21.246 9.703 C-19.143 5.141 -15.473 3.06 -11 1 C-7.319 0.271 -3.748 0.014 0 0 Z M-4 9 C-3 11 -3 11 -3 11 Z " fill="#CABFF2" transform="translate(800,851)"/>
<path d="M0 0 C4.148 4.861 4.404 9.449 4.566 15.547 C4.59 16.317 4.613 17.087 4.637 17.881 C4.684 19.507 4.729 21.132 4.772 22.758 C4.84 25.255 4.917 27.753 4.996 30.25 C5.041 31.828 5.085 33.406 5.129 34.984 C5.153 35.735 5.177 36.486 5.201 37.26 C5.245 39.051 5.235 40.842 5.215 42.633 C4.215 43.633 4.215 43.633 1.34 43.695 C0.309 43.675 -0.723 43.654 -1.785 43.633 C-2.775 43.633 -3.765 43.633 -4.785 43.633 C-5.445 43.303 -6.105 42.973 -6.785 42.633 C-8.648 42.964 -10.501 43.347 -12.348 43.758 C-17.895 44.739 -22.984 44.981 -27.785 41.633 C-30.503 36.562 -30.373 31.243 -29.785 25.633 C-28.8 23.625 -27.804 21.623 -26.785 19.633 C-27.327 16.339 -27.327 16.339 -28.785 13.633 C-29.445 13.303 -30.105 12.973 -30.785 12.633 C-30.91 10.258 -30.91 10.258 -30.785 7.633 C-30.125 6.973 -29.465 6.313 -28.785 5.633 C-28.785 4.973 -28.785 4.313 -28.785 3.633 C-20.694 -2.396 -9.135 -6.996 0 0 Z M-19.785 7.633 C-20.609 11.152 -20.609 11.152 -20.785 14.633 C-20.455 14.963 -20.125 15.293 -19.785 15.633 C-20.775 16.128 -20.775 16.128 -21.785 16.633 C-21.785 15.643 -21.785 14.653 -21.785 13.633 C-22.775 13.963 -23.765 14.293 -24.785 14.633 C-23.795 15.128 -23.795 15.128 -22.785 15.633 C-22.785 16.623 -22.785 17.613 -22.785 18.633 C-20.804 18.352 -18.826 18.058 -16.848 17.758 C-15.746 17.595 -14.643 17.433 -13.508 17.266 C-12.16 16.952 -12.16 16.952 -10.785 16.633 C-10.455 15.973 -10.125 15.313 -9.785 14.633 C-7.723 14.008 -7.723 14.008 -5.785 13.633 C-5.455 14.293 -5.125 14.953 -4.785 15.633 C-4.455 14.643 -4.125 13.653 -3.785 12.633 C-4.445 11.973 -5.105 11.313 -5.785 10.633 C-5.785 9.643 -5.785 8.653 -5.785 7.633 C-7.105 7.633 -8.425 7.633 -9.785 7.633 C-10.115 6.973 -10.445 6.313 -10.785 5.633 C-15.575 5.399 -15.575 5.399 -19.785 7.633 Z M-20.785 26.633 C-21.996 30.266 -21.454 31.295 -19.785 34.633 C-16.658 36.196 -13.205 36.032 -9.785 35.633 C-8.795 34.973 -7.805 34.313 -6.785 33.633 C-6.785 32.973 -6.785 32.313 -6.785 31.633 C-6.125 31.303 -5.465 30.973 -4.785 30.633 C-4.785 29.973 -4.785 29.313 -4.785 28.633 C-5.445 28.633 -6.105 28.633 -6.785 28.633 C-6.455 26.983 -6.125 25.333 -5.785 23.633 C-11.72 23.32 -15.544 23.745 -20.785 26.633 Z M-4.785 24.633 C-3.785 26.633 -3.785 26.633 -3.785 26.633 Z " fill="#ECE3FE" transform="translate(234.78515625,868.3671875)"/>
<path d="M0 0 C6.667 0 13.333 0 20 0 C24.78 4.78 26.849 9.184 27.114 15.863 C27.108 16.884 27.103 17.905 27.098 18.957 C27.094 20.069 27.091 21.181 27.088 22.326 C27.075 24.052 27.075 24.052 27.062 25.812 C27.058 26.982 27.053 28.152 27.049 29.357 C27.037 32.238 27.021 35.119 27 38 C27.66 38 28.32 38 29 38 C27.125 44.875 27.125 44.875 26 46 C22.5 45.688 22.5 45.688 19 45 C18.67 44.34 18.34 43.68 18 43 C18 43.66 18 44.32 18 45 C11.481 46.337 4.007 47.432 -2 44 C-5.385 41.273 -6.52 39.32 -7 35 C-7.66 35 -8.32 35 -9 35 C-8.333 31.667 -7.667 28.333 -7 25 C-6.01 25 -5.02 25 -4 25 C-4 24.34 -4 23.68 -4 23 C1.625 20 1.625 20 5 20 C5.33 18.35 5.66 16.7 6 15 C6.577 15.186 7.155 15.371 7.75 15.562 C10.207 16.04 11.644 15.757 14 15 C14.33 15.66 14.66 16.32 15 17 C15.99 17.33 16.98 17.66 18 18 C17.34 17.01 16.68 16.02 16 15 C15.875 12.312 15.875 12.312 16 10 C15.01 10 14.02 10 13 10 C11.237 9.691 11.237 9.691 9.438 9.375 C8.303 9.251 7.169 9.128 6 9 C5.34 9.66 4.68 10.32 4 11 C3.01 11.495 3.01 11.495 2 12 C2.66 13.98 3.32 15.96 4 18 C2.375 18.75 2.375 18.75 0 19 C-3.188 17.438 -3.188 17.438 -6 15 C-6.875 11.75 -6.875 11.75 -7 9 C-6.34 9 -5.68 9 -5 9 C-4.732 8.423 -4.464 7.845 -4.188 7.25 C-2.984 4.969 -1.555 3.05 0 1 C0 0.67 0 0.34 0 0 Z M6 18 C10 19 10 19 10 19 Z M8 26 C7.67 26.99 7.34 27.98 7 29 C6.34 28.34 5.68 27.68 5 27 C1.857 28.67 1.857 28.67 1.125 31.375 C0.775 34.021 0.775 34.021 2.148 36.16 C3.878 38.25 3.878 38.25 6.594 38.348 C12.506 37.963 12.506 37.963 17.188 34.75 C18.002 31.993 18.245 29.856 18 27 C15.773 28.062 15.773 28.062 13 30 C13.33 28.68 13.66 27.36 14 26 C12.02 26 10.04 26 8 26 Z " fill="#E0D6FD" transform="translate(590,866)"/>
<path d="M0 0 C0.922 2.766 1.118 4.383 1.098 7.23 C1.094 8.033 1.091 8.835 1.088 9.662 C1.08 10.495 1.071 11.329 1.062 12.188 C1.058 13.032 1.053 13.877 1.049 14.748 C1.037 16.832 1.019 18.916 1 21 C1.797 20.571 1.797 20.571 2.609 20.134 C3.316 19.765 4.022 19.396 4.75 19.016 C5.446 18.647 6.142 18.279 6.859 17.899 C10.064 16.553 12.567 16.714 16 17 C16 17.66 16 18.32 16 19 C16.722 19.165 17.444 19.33 18.188 19.5 C22.431 21.763 24.395 24.576 26 29 C26.614 35.014 26.472 40.964 26.312 47 C26.287 48.65 26.265 50.299 26.244 51.949 C26.189 55.967 26.103 59.983 26 64 C23.03 63.67 20.06 63.34 17 63 C17.01 62.148 17.021 61.295 17.032 60.417 C17.066 57.24 17.091 54.062 17.11 50.885 C17.12 49.512 17.134 48.14 17.151 46.767 C17.175 44.789 17.185 42.81 17.195 40.832 C17.206 39.643 17.216 38.455 17.227 37.23 C17.004 34.053 16.361 31.857 15 29 C14.01 29.33 13.02 29.66 12 30 C11.01 29.34 10.02 28.68 9 28 C9 27.34 9 26.68 9 26 C7.02 26.99 5.04 27.98 3 29 C3 29.99 3 30.98 3 32 C2.01 32 1.02 32 0 32 C0.33 42.23 0.66 52.46 1 63 C-1.728 64.364 -3.61 63.878 -6.625 63.562 C-7.628 63.461 -8.631 63.359 -9.664 63.254 C-10.435 63.17 -11.206 63.086 -12 63 C-11.625 60.562 -11.625 60.562 -11 58 C-10.01 57.505 -10.01 57.505 -9 57 C-9.33 56.34 -9.66 55.68 -10 55 C-9.34 55 -8.68 55 -8 55 C-8 52.69 -8 50.38 -8 48 C-8.66 48 -9.32 48 -10 48 C-10 45.03 -10 42.06 -10 39 C-9.34 39 -8.68 39 -8 39 C-8.33 33.06 -8.66 27.12 -9 21 C-9.33 21 -9.66 21 -10 21 C-10 19.02 -10 17.04 -10 15 C-9.34 15 -8.68 15 -8 15 C-8.351 13.907 -8.701 12.814 -9.062 11.688 C-10 8 -10 8 -9 5 C-9.389 2.857 -9.389 2.857 -10 1 C-6.62 -0.04 -3.522 -0.08 0 0 Z M11 26 C11 26.66 11 27.32 11 28 C11.99 27.67 12.98 27.34 14 27 C13.01 26.67 12.02 26.34 11 26 Z M-9 40 C-9 42.31 -9 44.62 -9 47 C-8.67 47 -8.34 47 -8 47 C-8 44.69 -8 42.38 -8 40 C-8.33 40 -8.66 40 -9 40 Z " fill="#796CD8" transform="translate(383,848)"/>
<path d="M0 0 C3.125 -0.188 3.125 -0.188 6 0 C6.33 1.65 6.66 3.3 7 5 C9.224 3.075 9.224 3.075 10 0 C11.604 -0.054 13.208 -0.093 14.812 -0.125 C15.706 -0.148 16.599 -0.171 17.52 -0.195 C20 0 20 0 23 2 C24.155 2.619 25.31 3.237 26.5 3.875 C29.44 5.534 31.171 7.112 33 10 C33.66 10.66 34.32 11.32 35 12 C35 13.32 35 14.64 35 16 C34.34 16 33.68 16 33 16 C33 24.25 33 32.5 33 41 C33.99 41.495 33.99 41.495 35 42 C35 43.32 35 44.64 35 46 C35.99 46.495 35.99 46.495 37 47 C32.477 48.508 27.749 48 23 48 C23.012 47.229 23.023 46.458 23.035 45.664 C23.044 44.661 23.053 43.658 23.062 42.625 C23.074 41.627 23.086 40.63 23.098 39.602 C23.002 37.046 22.816 35.816 21 34 C20.913 32.511 20.893 31.019 20.902 29.527 C20.906 28.628 20.909 27.729 20.912 26.803 C20.92 25.857 20.929 24.912 20.938 23.938 C20.942 22.988 20.947 22.039 20.951 21.061 C20.963 18.707 20.979 16.354 21 14 C21.66 14 22.32 14 23 14 C20.802 12.263 20.802 12.263 18 11 C15.218 11.695 15.218 11.695 13 13 C13 12.34 13 11.68 13 11 C11.35 11.66 9.7 12.32 8 13 C8.33 13.99 8.66 14.98 9 16 C8.34 16 7.68 16 7 16 C7.01 16.898 7.021 17.797 7.032 18.722 C7.066 22.051 7.091 25.379 7.11 28.708 C7.12 30.149 7.134 31.59 7.151 33.032 C7.175 35.102 7.186 37.172 7.195 39.242 C7.206 40.489 7.216 41.735 7.227 43.019 C7 46 7 46 5 48 C1.875 48.125 1.875 48.125 -1 48 C-2.428 44.162 -3.221 41.094 -3 37 C-3 36.01 -3 35.02 -3 34 C-3 25.625 -3 25.625 -2 22 C-2.66 22 -3.32 22 -4 22 C-4.33 21.01 -4.66 20.02 -5 19 C-4.01 17.515 -4.01 17.515 -3 16 C-2.755 13.841 -2.598 11.671 -2.5 9.5 C-2.222 3.333 -2.222 3.333 0 0 Z M23 17 C24 19 24 19 24 19 Z M23 20 C24 24 24 24 24 24 Z " fill="#EAE0FE" transform="translate(291,864)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.495 0.99 2.495 0.99 3 2 C3.99 1.67 4.98 1.34 6 1 C7.125 6.625 7.125 6.625 6 10 C5.01 10 4.02 10 3 10 C2.67 10.99 2.34 11.98 2 13 C1.34 12.34 0.68 11.68 0 11 C-2.6 10.719 -2.6 10.719 -5.5 10.812 C-6.48 10.819 -7.459 10.825 -8.469 10.832 C-10.994 10.836 -10.994 10.836 -13 12 C-13 11.34 -13 10.68 -13 10 C-16.3 10 -19.6 10 -23 10 C-22.01 10.66 -21.02 11.32 -20 12 C-20 12.66 -20 13.32 -20 14 C-20.66 14 -21.32 14 -22 14 C-22.027 15.583 -22.046 17.167 -22.062 18.75 C-22.074 19.632 -22.086 20.513 -22.098 21.422 C-22.006 23.831 -21.884 25.757 -21 28 C-18.341 28.886 -16.519 29.18 -13.77 29.316 C-12.967 29.358 -12.165 29.4 -11.338 29.443 C-10.505 29.483 -9.671 29.522 -8.812 29.562 C-7.968 29.606 -7.123 29.649 -6.252 29.693 C-4.168 29.799 -2.084 29.9 0 30 C0 32.64 0 35.28 0 38 C-7.59 38 -15.18 38 -23 38 C-22.67 41.63 -22.34 45.26 -22 49 C-21.67 49 -21.34 49 -21 49 C-20.67 51.31 -20.34 53.62 -20 56 C-20.66 56 -21.32 56 -22 56 C-22.495 58.97 -22.495 58.97 -23 62 C-26.3 62 -29.6 62 -33 62 C-32.756 60.709 -32.756 60.709 -32.506 59.392 C-32.016 56.105 -31.886 53.047 -31.902 49.727 C-31.907 47.996 -31.907 47.996 -31.912 46.23 C-31.92 45.041 -31.929 43.851 -31.938 42.625 C-31.942 41.412 -31.947 40.199 -31.951 38.949 C-31.963 35.966 -31.979 32.983 -32 30 C-32.33 30 -32.66 30 -33 30 C-33.042 27.667 -33.041 25.333 -33 23 C-32.67 22.67 -32.34 22.34 -32 22 C-31.763 19.474 -31.578 16.969 -31.438 14.438 C-31.394 13.727 -31.351 13.016 -31.307 12.283 C-31.2 10.522 -31.1 8.761 -31 7 C-31.66 7 -32.32 7 -33 7 C-32.875 5.125 -32.875 5.125 -32 3 C-28.393 1.353 -26.815 0.728 -23 2 C-15.722 2.47 -8.218 1.963 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M-32 23 C-31 25 -31 25 -31 25 Z " fill="#EEE5FE" transform="translate(577,850)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2 2.98 2 4 2 C4 2.99 4 3.98 4 5 C4.99 3.68 5.98 2.36 7 1 C7.99 1.33 8.98 1.66 10 2 C11.093 1.814 12.186 1.629 13.312 1.438 C17.263 0.969 19.335 1.636 23 3 C23.66 2.67 24.32 2.34 25 2 C25.66 2.33 26.32 2.66 27 3 C27.39 4.318 27.71 5.657 28 7 C29.294 11.083 29.294 11.083 31 15 C31.085 16.959 31.107 18.922 31.098 20.883 C31.094 22.049 31.091 23.216 31.088 24.418 C31.075 26.253 31.075 26.253 31.062 28.125 C31.058 29.356 31.053 30.587 31.049 31.855 C31.037 34.904 31.021 37.952 31 41 C31.33 41 31.66 41 32 41 C32 42.98 32 44.96 32 47 C30.398 47.222 28.793 47.427 27.188 47.625 C26.294 47.741 25.401 47.857 24.48 47.977 C22 48 22 48 19 46 C19.33 44.907 19.66 43.814 20 42.688 C21.131 39.154 21.131 39.154 21 36 C21.012 35.288 21.023 34.577 21.035 33.844 C21.044 33.07 21.053 32.297 21.062 31.5 C21.074 30.727 21.086 29.953 21.098 29.156 C21.09 26.815 21.09 26.815 20 24 C20.66 24 21.32 24 22 24 C21.552 22.019 21.091 20.04 20.625 18.062 C20.37 16.96 20.115 15.858 19.852 14.723 C19.571 13.824 19.29 12.926 19 12 C15.943 10.472 13.37 10.768 10 11 C7.5 11.833 7.5 11.833 6 14 C5.042 19.759 4.817 25.42 4.812 31.25 C4.8 32.039 4.788 32.828 4.775 33.641 C4.765 37.699 4.905 40.424 7 44 C6.125 46.25 6.125 46.25 5 48 C1.333 48 -2.333 48 -6 48 C-7.333 45.333 -6.671 43.833 -6 41 C-5.34 41 -4.68 41 -4 41 C-4.041 39.961 -4.082 38.921 -4.124 37.851 C-4.272 33.989 -4.407 30.128 -4.537 26.266 C-4.595 24.596 -4.658 22.925 -4.724 21.255 C-4.818 18.853 -4.899 16.45 -4.977 14.047 C-5.009 13.302 -5.041 12.557 -5.074 11.789 C-5.183 8.003 -5.144 5.206 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z M-5 42 C-4 46 -4 46 -4 46 Z " fill="#9C8EE6" transform="translate(440,864)"/>
<path d="M0 0 C6.412 5.729 11.066 13.776 13.309 22.062 C13.406 24.855 13.406 24.855 13.309 27.062 C-4.12 31.062 -4.12 31.062 -10.691 31.062 C-11.681 28.587 -11.681 28.587 -12.691 26.062 C-13.351 26.062 -14.011 26.062 -14.691 26.062 C-14.753 25.341 -14.815 24.619 -14.879 23.875 C-15.998 20.002 -17.531 18.564 -20.691 16.062 C-29.024 12.009 -37.492 12.006 -46.199 14.895 C-52.886 18.028 -57.905 23.215 -60.938 29.91 C-63.155 36.24 -64.048 41.718 -64.004 48.375 C-64 49.214 -63.997 50.052 -63.993 50.917 C-63.847 59.28 -62.323 66.004 -57.691 73.062 C-57.691 73.722 -57.691 74.382 -57.691 75.062 C-57.031 75.062 -56.371 75.062 -55.691 75.062 C-54.404 76.35 -53.155 77.676 -51.957 79.047 C-49.239 81.465 -46.908 82.258 -43.441 83.312 C-41.825 83.815 -41.825 83.815 -40.176 84.328 C-39.356 84.57 -38.536 84.813 -37.691 85.062 C-37.361 84.403 -37.031 83.743 -36.691 83.062 C-36.691 83.722 -36.691 84.382 -36.691 85.062 C-25.066 83.459 -25.066 83.459 -15.691 77.062 C-14.077 74.49 -12.864 71.858 -11.691 69.062 C-11.033 67.725 -10.369 66.39 -9.691 65.062 C3.772 64.698 3.772 64.698 10.309 67.062 C11.64 67.404 12.972 67.742 14.309 68.062 C14.025 75.576 11.144 80.697 7.309 87.062 C6.731 88.114 6.154 89.166 5.559 90.25 C0.225 96.917 -6.866 101.768 -14.691 105.062 C-15.681 105.062 -16.671 105.062 -17.691 105.062 C-18.021 105.722 -18.351 106.382 -18.691 107.062 C-33.82 110.053 -50.249 108.855 -63.629 100.812 C-67.916 97.861 -71.534 94.507 -75.035 90.664 C-76.706 88.818 -76.706 88.818 -79.691 88.062 C-79.795 87.361 -79.898 86.66 -80.004 85.938 C-80.784 82.675 -82.169 80.041 -83.691 77.062 C-83.691 76.403 -83.691 75.743 -83.691 75.062 C-84.351 74.732 -85.011 74.403 -85.691 74.062 C-86.021 73.072 -86.351 72.082 -86.691 71.062 C-87.228 70.526 -87.764 69.99 -88.316 69.438 C-90.097 66.362 -89.481 64.358 -88.945 60.949 C-88.624 57.299 -88.941 53.763 -89.254 50.125 C-89.769 43.797 -89.47 38.179 -87.691 32.062 C-88.021 32.062 -88.351 32.062 -88.691 32.062 C-88.691 30.413 -88.691 28.762 -88.691 27.062 C-87.701 27.392 -86.711 27.722 -85.691 28.062 C-85.258 26.237 -85.258 26.237 -84.816 24.375 C-81.776 12.192 -73.974 3.538 -63.594 -3.324 C-43.562 -15.089 -18.694 -13.457 0 0 Z M-74.691 8.062 C-86.657 24.318 -88.784 43.834 -86.403 63.438 C-84.031 77.134 -77.045 89.124 -65.746 97.156 C-52.189 106.002 -39.099 107.754 -23.254 105.594 C-11.838 103.096 -1.849 97.386 5.309 88.062 C8.703 82.529 12.044 76.465 13.309 70.062 C5.989 68.202 -1.159 67.505 -8.691 67.062 C-9.111 68.041 -9.111 68.041 -9.539 69.039 C-12.762 76.065 -16.102 81.149 -23.398 84.191 C-31.441 86.319 -41.638 86.571 -49.316 83.062 C-57.953 77.804 -62.112 70.687 -64.691 61.062 C-67.223 48.338 -66.055 36.187 -59.879 24.688 C-53.719 17.295 -48.903 12.644 -38.977 11.715 C-30.653 11.297 -23.596 11.958 -16.691 17.062 C-13.091 20.57 -11.499 24.452 -9.691 29.062 C-2.557 28.547 4.327 27.622 11.309 26.062 C10.424 15.664 5.858 8.17 -1.691 1.062 C-23.174 -16.421 -56.498 -11.563 -74.691 8.062 Z M-87.691 64.062 C-86.691 68.062 -86.691 68.062 -86.691 68.062 Z " fill="#5F5BC3" transform="translate(273.69140625,476.9375)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C4.02 3.954 4.04 4.907 4.06 5.89 C4.136 9.429 4.225 12.968 4.317 16.507 C4.356 18.039 4.391 19.57 4.422 21.102 C4.468 23.304 4.526 25.505 4.586 27.707 C4.597 28.392 4.609 29.076 4.621 29.781 C4.733 33.385 4.944 34.916 7 38 C7.99 38 8.98 38 10 38 C10.639 38.206 11.279 38.413 11.938 38.625 C14.043 39.173 14.043 39.173 16.188 38.312 C18.18 37.131 18.18 37.131 18.75 34.875 C18.832 34.256 18.915 33.638 19 33 C19.66 33 20.32 33 21 33 C20.861 29.895 20.713 26.791 20.562 23.688 C20.523 22.806 20.484 21.925 20.443 21.018 C20.401 20.17 20.36 19.322 20.316 18.449 C20.28 17.669 20.243 16.889 20.205 16.085 C20.059 13.807 20.059 13.807 19 11 C19.375 8.562 19.375 8.562 20 6 C20.186 5.237 20.371 4.474 20.562 3.688 C20.707 3.131 20.851 2.574 21 2 C23.333 1.958 25.667 1.959 28 2 C29.888 3.888 29.265 7.147 29.352 9.668 C29.375 10.34 29.398 11.013 29.422 11.706 C29.469 13.134 29.515 14.562 29.558 15.99 C29.624 18.161 29.702 20.332 29.781 22.502 C29.826 23.891 29.871 25.279 29.914 26.668 C29.955 27.929 29.996 29.19 30.038 30.489 C30.002 33.782 29.641 36.776 29 40 C29.99 40.495 29.99 40.495 31 41 C30.34 41 29.68 41 29 41 C29 42.32 29 43.64 29 45 C29.99 45.495 29.99 45.495 31 46 C30.34 46.66 29.68 47.32 29 48 C26.271 47.967 23.703 47.436 21 47 C20.67 46.01 20.34 45.02 20 44 C19.196 44.66 18.391 45.32 17.562 46 C13.53 48.669 9.694 48.826 5 48 C0.1 45.596 -2.434 42.775 -5 38 C-5.647 34.525 -5.745 31.091 -5.812 27.562 C-5.844 26.595 -5.876 25.627 -5.908 24.63 C-6.114 16.703 -5.799 8.889 -5 1 C-3.35 1.33 -1.7 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#F2EBFE" transform="translate(252,864)"/>
<path d="M0 0 C6.868 1.652 12.437 3.263 17 9 C18.848 12.449 20.054 14.818 19.875 18.75 C18.941 21.151 17.972 22.371 16 24 C12.795 22.398 11.952 19.167 10.414 16.031 C8.717 13.594 7.828 13.512 5 13 C4.67 12.34 4.34 11.68 4 11 C-1.551 11.744 -1.551 11.744 -7 13 C-6.814 13.763 -6.629 14.526 -6.438 15.312 C-6 18 -6 18 -7 21 C-7.66 21.33 -8.32 21.66 -9 22 C-8.999 29.283 -8.999 29.283 -5.938 35.562 C-5.298 36.037 -4.659 36.511 -4 37 C-4 37.66 -4 38.32 -4 39 C0.921 39.167 4.503 39.248 9 37 C9 36.34 9 35.68 9 35 C10 32 10 32 12.062 30.812 C12.702 30.544 13.341 30.276 14 30 C14 30.66 14 31.32 14 32 C15.65 31.67 17.3 31.34 19 31 C19.797 33.32 20.114 34.62 19.398 37 C16.978 41.729 14.62 45.714 9.328 47.594 C5.973 48.179 2.643 48.104 -0.75 48.062 C-1.447 48.058 -2.145 48.053 -2.863 48.049 C-4.576 48.037 -6.288 48.019 -8 48 C-8 47.34 -8 46.68 -8 46 C-9.459 44.56 -9.459 44.56 -11.312 43.125 C-13.969 40.935 -16.011 38.826 -18 36 C-18.66 35.67 -19.32 35.34 -20 35 C-19.67 34.34 -19.34 33.68 -19 33 C-18.34 33.33 -17.68 33.66 -17 34 C-17.33 33.34 -17.66 32.68 -18 32 C-18.755 20.801 -17.935 14.031 -10.598 5.48 C-7.963 3.04 -5.838 2.038 -2.25 1.875 C-1.507 1.916 -0.765 1.957 0 2 C0 1.34 0 0.68 0 0 Z M-9 17 C-8 20 -8 20 -8 20 Z " fill="#DFD6F9" transform="translate(348,864)"/>
<path d="M0 0 C2.341 2.078 4.641 4.197 6.941 6.32 C8.815 8.211 8.815 8.211 11 8 C11.651 9.56 12.296 11.123 12.938 12.688 C13.477 13.993 13.477 13.993 14.027 15.324 C14.957 17.882 15.542 20.322 16 23 C17.485 23.495 17.485 23.495 19 24 C18.505 24.495 18.505 24.495 18 25 C18.287 27.007 18.619 29.009 19 31 C12.662 32.533 6.499 33.484 0 34 C-0.33 34.99 -0.66 35.98 -1 37 C-3 35 -3 35 -3.5 31.938 C-3.665 30.968 -3.83 29.999 -4 29 C-4.66 28.67 -5.32 28.34 -6 28 C-6.402 27.01 -6.402 27.01 -6.812 26 C-7.86 23.715 -7.86 23.715 -10.625 23.25 C-11.801 23.126 -11.801 23.126 -13 23 C-12.505 21.515 -12.505 21.515 -12 20 C-12.66 20.66 -13.32 21.32 -14 22 C-14.66 22.33 -15.32 22.66 -16 23 C-16.66 21.35 -17.32 19.7 -18 18 C-20.97 18.495 -20.97 18.495 -24 19 C-24 18.01 -24 17.02 -24 16 C-34.115 17.281 -42.176 18.77 -48.688 27.086 C-56.318 38.214 -57.521 50.877 -55.535 64.094 C-53.217 73.861 -48.626 80.552 -40.148 85.906 C-34.017 89.028 -26.229 88.941 -19.613 87.469 C-12.6 85.011 -8.823 81.23 -5 75 C-4.67 75.99 -4.34 76.98 -4 78 C-8.113 84.789 -14.067 88.52 -21.5 90.75 C-29.996 91.6 -37.554 91.143 -44.719 86.078 C-49.863 81.69 -53.809 77.453 -56 71 C-56.528 69.476 -56.528 69.476 -57.066 67.922 C-60.179 56.708 -59.522 42.235 -54.605 31.699 C-49.919 23.821 -44.053 17.49 -35 15 C-25.602 13.844 -16.845 14.06 -9 20 C-4.921 23.677 -2.837 26.505 -2 32 C-1.131 31.856 -0.262 31.711 0.633 31.562 C1.785 31.377 2.938 31.191 4.125 31 C5.83 30.722 5.83 30.722 7.57 30.438 C10.761 30.03 13.788 29.922 17 30 C12.15 16.517 7.486 6.584 -6 0 C-17.575 -5.224 -31.359 -5.838 -43.449 -1.969 C-57.486 3.362 -66.221 11.503 -73 25 C-76.004 32.501 -77.686 39.935 -78 48 C-78.04 48.678 -78.08 49.356 -78.121 50.055 C-78.631 65.946 -75.266 81.494 -65 94 C-56.046 103.505 -45.428 108.865 -32.345 109.283 C-19.132 109.467 -6.106 106.91 3.688 97.387 C5.869 94.977 7.969 92.537 10 90 C10 94.292 7.937 95.852 5.129 98.938 C-0.773 104.655 -8.602 110 -17 110 C-17.33 110.66 -17.66 111.32 -18 112 C-21.021 112.027 -24.042 112.047 -27.062 112.062 C-27.91 112.071 -28.758 112.079 -29.631 112.088 C-41.871 112.135 -52.79 109.01 -62.348 101.043 C-64.075 99.111 -65.543 97.142 -67 95 C-67.601 94.216 -68.201 93.433 -68.82 92.625 C-78.822 79.234 -81.806 63.51 -81 47 C-80.957 45.752 -80.915 44.504 -80.871 43.219 C-80.379 33.076 -80.379 33.076 -77 29 C-76.239 27.315 -75.531 25.604 -74.875 23.875 C-70.069 11.32 -59.954 2.488 -47.941 -3.197 C-33.35 -9.23 -13.311 -8.874 0 0 Z " fill="#5D58C0" transform="translate(816,473)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.65 2 4.3 2 6 2 C11.371 18.708 11.371 18.708 13 27 C13.99 27 14.98 27 16 27 C17.125 30.75 17.125 30.75 16 33 C16.517 35.173 16.517 35.173 17.441 37.629 C17.775 38.574 18.109 39.52 18.453 40.494 C18.819 41.507 19.185 42.519 19.562 43.562 C19.927 44.59 20.292 45.617 20.668 46.676 C21.414 48.775 22.163 50.873 22.915 52.97 C24.193 56.54 25.453 60.116 26.711 63.693 C27.802 66.797 28.901 69.899 30 73 C29.67 71.68 29.34 70.36 29 69 C30.32 69.33 31.64 69.66 33 70 C33.061 69.374 33.121 68.747 33.184 68.102 C33.309 66.876 33.309 66.876 33.438 65.625 C33.559 64.407 33.559 64.407 33.684 63.164 C34 61 34 61 35 59 C35 58.01 35 57.02 35 56 C35.999 54.999 36.999 53.999 38 53 C39.044 50.765 39.044 50.765 39.854 48.209 C40.184 47.238 40.514 46.267 40.855 45.266 C41.202 44.219 41.549 43.172 41.906 42.094 C42.272 41.018 42.638 39.941 43.015 38.832 C44.185 35.391 45.344 31.946 46.5 28.5 C48.021 23.965 49.554 19.435 51.094 14.906 C51.441 13.859 51.788 12.812 52.145 11.734 C52.476 10.763 52.806 9.792 53.146 8.791 C53.433 7.936 53.719 7.081 54.014 6.201 C55.056 3.875 56.014 2.582 58 1 C60.417 0.644 60.417 0.644 63.262 0.672 C64.299 0.677 65.336 0.682 66.404 0.688 C68.029 0.718 68.029 0.718 69.688 0.75 C70.752 0.755 71.816 0.76 72.912 0.766 C78.757 0.823 84.146 1.268 90 2 C90 39.95 90 77.9 90 117 C86.218 118.261 82.848 117.94 78.938 117.688 C77.838 117.634 77.838 117.634 76.717 117.58 C71.243 117.243 71.243 117.243 69 115 C75.27 115 81.54 115 88 115 C88 78.04 88 41.08 88 3 C78.1 3.33 68.2 3.66 58 4 C51.835 21.072 45.847 38.192 40 55.375 C39.667 56.352 39.335 57.329 38.992 58.336 C34.968 70.158 34.968 70.158 31 82 C29 81 29 81 27.957 77.922 C27.57 76.533 27.189 75.142 26.812 73.75 C25.005 67.37 23.099 61.04 21 54.75 C20.724 53.924 20.449 53.098 20.165 52.248 C18.054 45.991 15.864 39.763 13.66 33.539 C13.366 32.706 13.071 31.873 12.768 31.015 C11.275 26.792 9.776 22.572 8.268 18.354 C7.711 16.786 7.154 15.217 6.598 13.648 C6.335 12.921 6.073 12.194 5.803 11.445 C4.761 8.495 4 6.154 4 3 C-6.23 3 -16.46 3 -27 3 C-26.979 7.826 -26.959 12.652 -26.938 17.625 C-26.901 36.191 -27.187 54.75 -27.5 73.312 C-27.512 74.022 -27.524 74.732 -27.536 75.463 C-27.686 84.309 -27.84 93.154 -28 102 C-28.33 102 -28.66 102 -29 102 C-29.779 88.252 -30.161 74.551 -30.13 60.782 C-30.125 57.814 -30.13 54.847 -30.137 51.879 C-30.136 49.979 -30.135 48.079 -30.133 46.18 C-30.135 45.299 -30.137 44.418 -30.139 43.51 C-30.136 42.697 -30.133 41.884 -30.129 41.046 C-30.129 40.335 -30.128 39.623 -30.127 38.89 C-30 37 -30 37 -29 34 C-29.66 33.67 -30.32 33.34 -31 33 C-30.34 33 -29.68 33 -29 33 C-29.33 22.44 -29.66 11.88 -30 1 C-26.349 0.979 -22.699 0.959 -18.938 0.938 C-17.232 0.924 -17.232 0.924 -15.492 0.91 C-10.215 0.896 -5.197 1.064 0 2 C0 1.34 0 0.68 0 0 Z M14 28 C15 30 15 30 15 30 Z " fill="#5F58BE" transform="translate(462,466)"/>
<path d="M0 0 C2.5 0.25 2.5 0.25 4.5 2.25 C4.741 4.389 4.741 4.389 4.727 7.067 C4.727 8.068 4.727 9.069 4.727 10.1 C4.716 11.183 4.706 12.267 4.695 13.383 C4.692 14.49 4.69 15.597 4.687 16.737 C4.676 20.283 4.65 23.829 4.625 27.375 C4.615 29.775 4.606 32.174 4.598 34.574 C4.576 40.466 4.542 46.358 4.5 52.25 C5.181 52.245 5.862 52.24 6.564 52.234 C9.647 52.213 12.73 52.2 15.812 52.188 C16.884 52.179 17.956 52.171 19.061 52.162 C20.089 52.159 21.117 52.156 22.176 52.152 C23.124 52.147 24.072 52.142 25.048 52.136 C27.5 52.25 27.5 52.25 30.5 53.25 C30.005 50.28 30.005 50.28 29.5 47.25 C30.49 47.745 30.49 47.745 31.5 48.25 C31.893 50.573 32.007 52.868 32.156 55.219 C32.27 55.889 32.383 56.559 32.5 57.25 C33.49 57.745 33.49 57.745 34.5 58.25 C33.51 58.91 32.52 59.57 31.5 60.25 C31.5 59.59 31.5 58.93 31.5 58.25 C30.84 58.25 30.18 58.25 29.5 58.25 C29.17 59.24 28.84 60.23 28.5 61.25 C24.313 61.279 20.125 61.297 15.938 61.312 C14.756 61.321 13.575 61.329 12.357 61.338 C11.207 61.341 10.056 61.344 8.871 61.348 C7.818 61.353 6.766 61.358 5.681 61.364 C2.231 61.24 -1.092 60.793 -4.5 60.25 C-4.546 52.722 -4.582 45.195 -4.604 37.667 C-4.614 34.172 -4.628 30.676 -4.651 27.181 C-4.677 23.165 -4.686 19.149 -4.695 15.133 C-4.706 13.874 -4.716 12.616 -4.727 11.32 C-4.727 10.159 -4.727 8.998 -4.727 7.801 C-4.731 6.775 -4.736 5.749 -4.741 4.692 C-4.399 1.227 -3.453 0.345 0 0 Z M29.5 54.25 C30.16 54.91 30.82 55.57 31.5 56.25 C31.5 55.59 31.5 54.93 31.5 54.25 C30.84 54.25 30.18 54.25 29.5 54.25 Z " fill="#EDE6FB" transform="translate(172.5,850.75)"/>
<path d="M0 0 C1.226 0.014 1.226 0.014 2.477 0.027 C3.416 0.045 3.416 0.045 4.375 0.062 C4.37 0.698 4.365 1.333 4.359 1.987 C4.338 4.887 4.325 7.787 4.312 10.688 C4.304 11.687 4.296 12.686 4.287 13.715 C4.284 14.688 4.281 15.661 4.277 16.664 C4.272 17.554 4.267 18.445 4.261 19.362 C4.321 22.116 4.321 22.116 4.875 25.008 C5.394 28.177 5.489 31.127 5.473 34.336 C5.469 35.49 5.466 36.643 5.463 37.832 C5.455 39.022 5.446 40.212 5.438 41.438 C5.433 42.651 5.428 43.864 5.424 45.113 C5.412 48.096 5.396 51.079 5.375 54.062 C7.685 54.393 9.995 54.722 12.375 55.062 C12.705 57.702 13.035 60.342 13.375 63.062 C3.462 64.628 3.462 64.628 -1.625 63.062 C-3.451 60.844 -4.392 58.712 -5.625 56.062 C-6.285 55.072 -6.945 54.082 -7.625 53.062 C-7.602 50.18 -7.602 50.18 -7.25 46.938 C-7.139 45.862 -7.028 44.787 -6.914 43.68 C-6.819 42.816 -6.723 41.952 -6.625 41.062 C-6.572 39.694 -6.572 39.694 -7.625 37.062 C-7.453 33.725 -7.111 30.419 -6.781 27.094 C-6.438 24.016 -6.438 24.016 -7.625 21.062 C-7.857 18.339 -7.756 15.812 -7.625 13.062 C-6.965 12.403 -6.305 11.742 -5.625 11.062 C-5.955 10.072 -6.285 9.082 -6.625 8.062 C-6.665 5.73 -6.669 3.395 -6.625 1.062 C-4.22 -0.14 -2.675 -0.038 0 0 Z M-5.625 22.062 C-4.625 24.062 -4.625 24.062 -4.625 24.062 Z M-5.625 29.062 C-4.625 31.062 -4.625 31.062 -4.625 31.062 Z M-5.625 32.062 C-4.625 36.062 -4.625 36.062 -4.625 36.062 Z M-5.625 43.062 C-5.625 44.712 -5.625 46.362 -5.625 48.062 C-5.295 48.062 -4.965 48.062 -4.625 48.062 C-4.625 46.413 -4.625 44.763 -4.625 43.062 C-4.955 43.062 -5.285 43.062 -5.625 43.062 Z " fill="#F1E9FE" transform="translate(650.625,847.9375)"/>
<path d="M0 0 C0.33 2.31 0.66 4.62 1 7 C0.01 7.33 -0.98 7.66 -2 8 C-1.67 23.18 -1.34 38.36 -1 54 C-0.01 53.505 -0.01 53.505 1 53 C1 53.66 1 54.32 1 55 C2.98 54.505 2.98 54.505 5 54 C6.485 58.455 6.485 58.455 8 63 C2.001 64.071 -2.261 64.237 -8 62 C-10 58.875 -10 58.875 -11 56 C-11.33 56 -11.66 56 -12 56 C-11.967 54.079 -11.935 52.159 -11.902 50.238 C-11.925 48.01 -11.925 48.01 -12.514 45.951 C-13.199 41.791 -13.009 37.748 -12.879 33.543 C-12.855 32.64 -12.832 31.737 -12.807 30.807 C-12.731 27.933 -12.647 25.061 -12.562 22.188 C-12.509 20.235 -12.457 18.283 -12.404 16.33 C-12.275 11.553 -12.14 6.777 -12 2 C-7.78 0.499 -4.496 -0.214 0 0 Z M-2 3 C-1 7 -1 7 -1 7 Z M-11 9 C-10 11 -10 11 -10 11 Z M-11 17 C-10 19 -10 19 -10 19 Z M-11 25 C-10 28 -10 28 -10 28 Z M-11 33 C-10 36 -10 36 -10 36 Z M-11 41 C-10 43 -10 43 -10 43 Z M-11 50 C-10 52 -10 52 -10 52 Z " fill="#E6DCFE" transform="translate(636,848)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 27.06 1 54.12 1 82 C0.01 82 -0.98 82 -2 82 C-2 61.21 -2 40.42 -2 19 C-1.01 18.505 -1.01 18.505 0 18 C-1.968 18.031 -1.968 18.031 -4 19 C-5.305 21.996 -5.305 21.996 -6 25 C-5.34 25.33 -4.68 25.66 -4 26 C-4.99 26 -5.98 26 -7 26 C-7.135 26.562 -7.271 27.124 -7.41 27.703 C-9.085 34.241 -11.191 40.579 -13.438 46.938 C-13.896 48.236 -13.896 48.236 -14.364 49.561 C-15.482 52.723 -16.603 55.884 -17.727 59.044 C-18.325 60.735 -18.913 62.43 -19.5 64.125 C-19.98 65.49 -19.98 65.49 -20.469 66.883 C-21.253 69.036 -21.253 69.036 -20 71 C-21.333 73 -22.667 75 -24 77 C-24.519 79.272 -24.882 81.542 -25.242 83.844 C-26.29 86.825 -27.156 87.587 -30 89 C-32.928 89.351 -35.806 89.283 -38.75 89.188 C-39.543 89.174 -40.336 89.16 -41.152 89.146 C-43.102 89.111 -45.051 89.057 -47 89 C-50.127 82.142 -53.17 75.332 -55 68 C-56.088 64.584 -57.267 61.201 -58.438 57.812 C-58.75 56.885 -59.063 55.958 -59.385 55.002 C-59.842 53.68 -59.842 53.68 -60.309 52.332 C-60.723 51.121 -60.723 51.121 -61.146 49.886 C-61.845 47.815 -61.845 47.815 -64 47 C-64.19 45.188 -64.378 43.375 -64.562 41.562 C-65.483 36.175 -67.654 31.102 -69.562 26 C-69.987 24.844 -70.412 23.687 -70.85 22.496 C-71.892 19.661 -72.942 16.829 -74 14 C-73.998 14.868 -73.996 15.736 -73.993 16.631 C-73.973 24.811 -73.958 32.992 -73.948 41.173 C-73.943 45.379 -73.936 49.585 -73.925 53.791 C-73.914 57.85 -73.908 61.909 -73.905 65.968 C-73.903 67.516 -73.9 69.065 -73.894 70.613 C-73.887 72.782 -73.886 74.951 -73.886 77.12 C-73.884 78.355 -73.882 79.589 -73.88 80.862 C-74 84 -74 84 -75 88 C-79.95 88 -84.9 88 -90 88 C-90 87.67 -90 87.34 -90 87 C-85.38 87 -80.76 87 -76 87 C-76 58.62 -76 30.24 -76 1 C-74.206 3.692 -73.667 4.987 -73 8 C-72.108 10.625 -71.198 13.24 -70.27 15.852 C-69.997 16.624 -69.724 17.397 -69.443 18.194 C-68.568 20.671 -67.69 23.148 -66.812 25.625 C-63.14 35.989 -59.518 46.362 -56.066 56.801 C-53.886 63.38 -51.575 69.909 -49.195 76.418 C-48.907 77.226 -48.618 78.034 -48.32 78.867 C-47.938 79.921 -47.938 79.921 -47.547 80.996 C-47 83 -47 83 -47 87 C-41.06 87 -35.12 87 -29 87 C-28.67 84.69 -28.34 82.38 -28 80 C-27.403 77.921 -27.403 77.921 -26.723 76.18 C-26.462 75.496 -26.201 74.812 -25.933 74.107 C-25.645 73.37 -25.358 72.634 -25.062 71.875 C-21.98 63.735 -19.075 55.545 -16.25 47.312 C-15.63 45.506 -15.63 45.506 -14.997 43.663 C-14.127 41.127 -13.258 38.592 -12.39 36.056 C-10.901 31.71 -9.408 27.367 -7.914 23.023 C-7.402 21.535 -6.89 20.046 -6.378 18.557 C-6.004 17.47 -6.004 17.47 -5.622 16.361 C-3.747 10.908 -1.874 5.454 0 0 Z M-1 10 C0 14 0 14 0 14 Z " fill="#8279D3" transform="translate(530,494)"/>
<path d="M0 0 C2.698 2.59 2.329 5.877 2.562 9.5 C1.903 9.005 1.242 8.51 0.562 8 C-3.527 5.955 -5.881 6.066 -10.438 6.5 C-13.922 8.513 -15.117 10.283 -16.188 14.125 C-18.678 24.443 -19.482 38.845 -14.438 48.5 C-11.421 50.663 -8.828 50.721 -5.188 50.625 C-3.849 50.598 -3.849 50.598 -2.484 50.57 C-1.809 50.547 -1.133 50.524 -0.438 50.5 C-0.768 51.16 -1.097 51.82 -1.438 52.5 C-3.752 53.231 -6.086 53.901 -8.438 54.5 C-5.138 54.5 -1.837 54.5 1.562 54.5 C-1.295 57.357 -3.839 57.276 -7.77 57.594 C-13.344 57.398 -17.061 54.632 -21.25 51.188 C-23.779 48.08 -25.075 45.263 -26.438 41.5 C-26.829 40.428 -27.221 39.355 -27.625 38.25 C-28.531 34.069 -28.549 30.309 -28.41 26.051 C-28.325 23.534 -28.325 23.534 -29.055 21.34 C-29.244 20.429 -29.244 20.429 -29.438 19.5 C-27.5 16.75 -27.5 16.75 -25.438 14.5 C-26.758 14.17 -28.077 13.84 -29.438 13.5 C-28.833 13.436 -28.228 13.371 -27.605 13.305 C-24.543 12.168 -24.147 10.521 -22.75 7.625 C-20.614 3.859 -19.197 1.827 -15.062 0.375 C-9.946 -0.842 -5.183 -1.063 0 0 Z M-27.438 18.5 C-26.438 20.5 -26.438 20.5 -26.438 20.5 Z " fill="#D3C9F4" transform="translate(757.4375,853.5)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 1.66 4 2.32 4 3 C11.26 3 18.52 3 26 3 C26.33 4.65 26.66 6.3 27 8 C27.844 9.952 27.844 9.952 28.934 11.793 C29.33 12.496 29.727 13.2 30.135 13.925 C30.565 14.671 30.995 15.418 31.438 16.188 C32.352 17.802 33.266 19.417 34.18 21.031 C34.649 21.855 35.117 22.679 35.601 23.527 C37.367 26.648 39.094 29.789 40.821 32.931 C44.267 39.193 47.79 45.391 51.5 51.5 C54.928 57.156 58.253 62.863 61.5 68.625 C61.934 69.384 62.368 70.142 62.814 70.924 C64.865 74.594 66.529 77.747 67 82 C63.925 79.335 62.137 76.009 60.188 72.5 C59.219 70.783 59.219 70.783 58.23 69.031 C56.519 65.952 54.832 62.86 53.148 59.766 C51.055 55.921 48.943 52.088 46.809 48.266 C46.264 47.29 45.719 46.314 45.158 45.309 C43.976 43.207 42.782 41.11 41.58 39.02 C38.443 33.534 35.527 28.079 32.973 22.297 C32.471 21.238 31.97 20.18 31.453 19.089 C30 16 30 16 28.845 12.997 C27.417 9.674 27.417 9.674 23 6 C15.74 5.67 8.48 5.34 1 5 C1 41.96 1 78.92 1 117 C7.6 117 14.2 117 21 117 C20.67 90.93 20.34 64.86 20 38 C23.453 39.727 24.523 43.576 26.188 46.938 C26.759 48.05 26.759 48.05 27.342 49.186 C28.294 51.092 29.154 53.044 30 55 C29.505 55.99 29.505 55.99 29 57 C28.186 55.709 27.374 54.417 26.562 53.125 C26.11 52.406 25.658 51.686 25.191 50.945 C24 49 24 49 23 47 C22.67 70.76 22.34 94.52 22 119 C13.75 119 5.5 119 -3 119 C-2.67 117.68 -2.34 116.36 -2 115 C-1.915 113.29 -1.882 111.577 -1.886 109.864 C-1.887 108.844 -1.887 107.823 -1.887 106.771 C-1.892 105.676 -1.897 104.581 -1.902 103.453 C-1.904 102.327 -1.905 101.2 -1.907 100.04 C-1.912 96.443 -1.925 92.847 -1.938 89.25 C-1.943 86.811 -1.947 84.372 -1.951 81.934 C-1.962 75.956 -1.979 69.978 -2 64 C-2.33 64 -2.66 64 -3 64 C-4.361 55.836 -4.361 55.836 -2 52 C-2.33 51.34 -2.66 50.68 -3 50 C-2.34 50 -1.68 50 -1 50 C-1.036 49.393 -1.071 48.787 -1.108 48.162 C-1.764 36.479 -2.149 24.884 -1.625 13.188 C-1.582 12.21 -1.539 11.233 -1.494 10.226 C-1.14 3.421 -1.14 3.421 0 0 Z " fill="#5F58BF" transform="translate(644,641)"/>
<path d="M0 0 C1.662 0.356 3.328 0.695 5 1 C5 1.66 5 2.32 5 3 C5.99 3.309 5.99 3.309 7 3.625 C19.984 9.576 27.987 21.024 33 34 C38.656 50.16 37.247 70.62 30 86 C25.713 93.695 19.287 104.509 11 108 C9.624 108.811 8.249 109.624 6.875 110.438 C-4.541 116.782 -16.224 117.195 -29 117 C-29 117.33 -29 117.66 -29 118 C-30.65 118 -32.3 118 -34 118 C-34 117.01 -34 116.02 -34 115 C-35.279 114.794 -36.558 114.587 -37.875 114.375 C-49.426 111.768 -59.426 103.292 -65.812 93.562 C-75.982 76.542 -78.511 58.564 -73.91 39.219 C-69.739 24.466 -61.025 11.469 -47.844 3.539 C-32.913 -4.542 -16.017 -5.339 0 0 Z M-61.312 16.688 C-72.418 31.594 -76.114 49.986 -73.707 68.219 C-70.934 85.157 -62.94 97.931 -49 108 C-46.415 109.641 -43.853 110.894 -41 112 C-39.875 112.447 -39.875 112.447 -38.727 112.902 C-24.24 117.738 -8.517 115.862 5.109 109.562 C18.905 102.28 27.543 90.406 32.203 75.832 C36.725 59.763 35.281 41.221 27.875 26.375 C19.839 12.307 8.671 3.961 -6.831 -0.531 C-27.211 -4.942 -47.671 0.803 -61.312 16.688 Z " fill="#6059C1" transform="translate(466,646)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 13.2 6 26.4 6 40 C7.98 40 9.96 40 12 40 C12 41.65 12 43.3 12 45 C9.525 45.495 9.525 45.495 7 46 C6.67 49.3 6.34 52.6 6 56 C4.02 56 2.04 56 0 56 C0 52.37 0 48.74 0 45 C-7.92 45 -15.84 45 -24 45 C-24.495 42.525 -24.495 42.525 -25 40 C-24.01 39.505 -24.01 39.505 -23 39 C-23 40.65 -23 42.3 -23 44 C-18.38 43.67 -13.76 43.34 -9 43 C-11.64 42.67 -14.28 42.34 -17 42 C-17 41.67 -17 41.34 -17 41 C-11.114 40.144 -6.123 40 0 40 C-0.33 30.43 -0.66 20.86 -1 11 C-3.549 15.559 -3.549 15.559 -6.085 20.125 C-8.778 24.889 -8.778 24.889 -11 26 C-11 25.01 -11 24.02 -11 23 C-13.829 25.551 -15.679 28.466 -17.688 31.688 C-18.621 33.178 -18.621 33.178 -19.574 34.699 C-20.045 35.458 -20.515 36.218 -21 37 C-21.33 36.34 -21.66 35.68 -22 35 C-21.155 33.159 -21.155 33.159 -19.789 30.922 C-19.29 30.097 -18.791 29.271 -18.277 28.421 C-17.459 27.099 -17.459 27.099 -16.625 25.75 C-16.079 24.855 -15.534 23.96 -14.971 23.038 C-2.241 2.241 -2.241 2.241 0 0 Z " fill="#F0E7FD" transform="translate(841,854)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C2.99 1.67 3.98 1.34 5 1 C8.35 0.744 11.704 0.769 15.062 0.75 C15.967 0.729 16.871 0.709 17.803 0.688 C26.506 0.638 34.172 2.437 41.625 7.062 C42.882 7.822 42.882 7.822 44.164 8.598 C44.77 9.06 45.376 9.523 46 10 C46 10.66 46 11.32 46 12 C46.99 12.33 47.98 12.66 49 13 C52.015 16.685 54.082 20.916 55.688 25.375 C56.706 28.989 56.706 28.989 59 31 C58.34 31.66 57.68 32.32 57 33 C56.34 33.99 55.68 34.98 55 36 C51.006 37.202 46.833 37.291 42.688 37.562 C41.433 37.646 40.179 37.73 38.887 37.816 C37.934 37.877 36.981 37.938 36 38 C35.567 36.886 35.134 35.773 34.688 34.625 C33.301 30.825 33.301 30.825 30 29 C30 28.34 30 27.68 30 27 C22.236 22.664 16.113 21.309 7.312 23.254 C4.282 24.232 2.238 25.762 0 28 C0.33 28.66 0.66 29.32 1 30 C0.01 30 -0.98 30 -2 30 C-1.959 31.217 -1.918 32.434 -1.875 33.688 C-1.852 34.372 -1.829 35.056 -1.805 35.762 C-0.551 39.248 1.962 41.086 5 43 C7.627 44.188 10.292 45.024 13.062 45.812 C13.757 46.028 14.451 46.244 15.166 46.467 C18.859 47.572 22.143 48.263 26 48 C26 48.66 26 49.32 26 50 C27.98 49.505 27.98 49.505 30 49 C30 49.66 30 50.32 30 51 C31.279 51.247 32.558 51.495 33.875 51.75 C44.077 54.056 53.845 57.909 59.875 66.875 C64.819 75.664 65.153 85.074 62.75 94.75 C61.458 99.077 59.755 102.383 57 106 C56.526 106.887 56.051 107.774 55.562 108.688 C53.694 111.453 52.032 112.636 49 114 C47.515 113.505 47.515 113.505 46 113 C46.675 112.443 47.351 111.886 48.047 111.312 C55.118 105.263 59.512 100.29 61 91 C61.711 81.457 61.646 72.696 55.375 65.008 C46.936 56.815 35.519 54.51 24.45 51.879 C2.74 46.642 2.74 46.642 -2.812 39.75 C-4.35 36.189 -4.473 33.851 -4 30 C-1.806 25.338 0.901 22.751 5.531 20.695 C13.104 18.562 21.14 19.101 28.129 22.73 C32.862 25.942 34.866 29.665 37 35 C42.94 34.34 48.88 33.68 55 33 C52.916 26.3 51.153 20.636 47 15 C47 14.34 47 13.68 47 13 C46.408 12.72 45.817 12.441 45.207 12.152 C43.181 11.095 41.52 9.915 39.688 8.562 C29.317 2.023 14.593 1.593 2.938 4.188 C-4.416 6.205 -10.988 9.3 -17 14 C-15.533 10.183 -13.65 8.899 -10.125 6.938 C-4.735 3.928 -4.735 3.928 0 0 Z " fill="#605AC1" transform="translate(313,642)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 2.01 2.32 1.02 3 0 C5 1 5 1 6 2 C6.144 3.155 6.289 4.31 6.438 5.5 C6.623 6.655 6.809 7.81 7 9 C7.66 9.33 8.32 9.66 9 10 C8.01 10.33 7.02 10.66 6 11 C6.165 11.536 6.33 12.072 6.5 12.625 C7 15 7 15 6.938 18.062 C6.741 21.193 6.741 21.193 9 24 C8.67 24.33 8.34 24.66 8 25 C8.66 25.33 9.32 25.66 10 26 C8.125 30.875 8.125 30.875 7 32 C7.33 32.99 7.66 33.98 8 35 C7.505 35.495 7.505 35.495 7 36 C7.66 36.66 8.32 37.32 9 38 C8.34 38 7.68 38 7 38 C6.833 40.917 6.833 40.917 7 44 C7.66 44.66 8.32 45.32 9 46 C9 46.66 9 47.32 9 48 C7.418 48.196 5.834 48.382 4.25 48.562 C2.927 48.719 2.927 48.719 1.578 48.879 C-0.907 48.996 -2.629 48.654 -5 48 C-5 37.667 -5 27.333 -5 17 C-5.062 16.196 -5.124 15.391 -5.188 14.562 C-4.938 9.82 -3.805 5.398 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z M6 26 C7 28 7 28 7 28 Z M-4 32 C-4 34.97 -4 37.94 -4 41 C-3.67 41 -3.34 41 -3 41 C-3 38.03 -3 35.06 -3 32 C-3.33 32 -3.66 32 -4 32 Z M-4 42 C-3 44 -3 44 -3 44 Z M-4 45 C-3 47 -3 47 -3 47 Z " fill="#A391EC" transform="translate(421,863)"/>
<path d="M0 0 C1.578 0.014 1.578 0.014 3.188 0.027 C5.75 0.051 8.312 0.083 10.875 0.125 C11.37 1.115 11.37 1.115 11.875 2.125 C13.685 3.043 13.685 3.043 15.875 3.875 C20.976 6.036 25.369 8.929 29.875 12.125 C30.535 11.795 31.195 11.465 31.875 11.125 C32.37 13.105 32.37 13.105 32.875 15.125 C33.865 15.125 34.855 15.125 35.875 15.125 C36.266 16.208 36.266 16.208 36.664 17.312 C37.916 20.221 39.44 22.532 41.25 25.125 C48.733 36.678 50.064 49.202 49.938 62.625 C49.933 63.543 49.928 64.461 49.924 65.406 C49.912 67.646 49.896 69.885 49.875 72.125 C49.215 72.125 48.555 72.125 47.875 72.125 C47.793 73.404 47.71 74.683 47.625 76 C46.571 85.2 42.602 92.04 36.875 99.125 C36.215 100.115 35.555 101.105 34.875 102.125 C34.215 102.125 33.555 102.125 32.875 102.125 C34.256 99.191 35.871 96.638 37.75 94 C47.578 79.354 48.524 61.689 46.445 44.59 C43.46 30.9 36.51 19.236 25.055 11.129 C12 2.932 -3.173 0.907 -18.314 3.5 C-32.902 6.844 -43.429 15.612 -51.312 27.938 C-59.285 41.671 -60.707 61.44 -56.879 76.805 C-51.196 94.296 -42.933 104.44 -26.688 112.84 C-25.842 113.264 -24.996 113.688 -24.125 114.125 C-25.762 114.816 -25.762 114.816 -28.125 115.125 C-35.294 111.96 -41.319 107.3 -47.125 102.125 C-47.785 102.125 -48.445 102.125 -49.125 102.125 C-49.682 100.795 -49.682 100.795 -50.25 99.438 C-51.304 96.951 -52.418 94.516 -53.59 92.082 C-56.627 85.748 -58.879 80.036 -60.125 73.125 C-60.455 72.465 -60.785 71.805 -61.125 71.125 C-62.012 53.237 -59.873 38.816 -51.125 23.125 C-50.783 22.382 -50.442 21.64 -50.09 20.875 C-45.515 12.577 -33.038 5.532 -24.253 2.737 C-16.128 0.401 -8.429 -0.139 0 0 Z " fill="#5A55BE" transform="translate(365.125,465.875)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.732 0.516 1.464 1.031 1.188 1.562 C0.796 2.367 0.404 3.171 0 4 C-0.51 5.021 -1.021 6.042 -1.547 7.094 C-3.364 11.374 -3.48 15.336 -3.438 19.938 C-3.457 20.697 -3.477 21.457 -3.498 22.24 C-3.492 28.926 -1.488 33.895 2.684 39.102 C13.074 48.127 25.449 51.193 38.535 54.377 C54.711 58.42 54.711 58.42 60.125 64.312 C61.673 69.066 61.836 73.161 59.75 77.75 C59.173 78.493 58.595 79.235 58 80 C57.464 80.701 56.928 81.403 56.375 82.125 C48.847 88.068 39.176 87.886 30 87 C23.572 85.406 19.62 82.485 16 77 C15.298 75.012 14.625 73.013 14 71 C12.88 71.217 11.76 71.433 10.605 71.656 C9.133 71.938 7.66 72.219 6.188 72.5 C5.45 72.643 4.712 72.786 3.951 72.934 C0.922 73.509 -1.909 74 -5 74 C-1.049 85.467 2.43 93.374 13.047 99.676 C21.368 103.407 28.919 104.479 38 104.375 C39.016 104.387 40.032 104.398 41.078 104.41 C47.031 104.387 52.113 103.689 57.746 101.741 C60.144 100.953 62.519 100.452 65 100 C63.563 102.875 61.8 103.002 58.812 104.062 C57.87 104.404 56.928 104.746 55.957 105.098 C43.71 108.835 24.848 109.532 13 104 C10.945 102.098 10.945 102.098 9 100 C7.673 98.888 6.34 97.784 5 96.688 C1.244 93.448 -0.76 90.394 -3 86 C-3.66 85.34 -4.32 84.68 -5 84 C-6.23 80.311 -6.557 76.863 -7 73 C-2.373 70.833 2.311 70.149 7.312 69.375 C8.566 69.174 8.566 69.174 9.846 68.969 C11.896 68.641 13.948 68.32 16 68 C16.329 69.121 16.329 69.121 16.664 70.266 C18.084 74.675 19.214 78.173 22.438 81.562 C32.057 85.444 41.388 85.892 51.066 82.215 C54.491 80.564 56.056 78.911 58.062 75.688 C59.172 71.324 58.802 69.095 57 65 C56.01 64.67 55.02 64.34 54 64 C53.67 63.34 53.34 62.68 53 62 C50.66 60.869 50.66 60.869 47.938 59.875 C47.018 59.522 46.099 59.169 45.152 58.805 C44.442 58.539 43.732 58.274 43 58 C42.505 58.99 42.505 58.99 42 60 C41.67 59.34 41.34 58.68 41 58 C39.11 57.271 39.11 57.271 36.801 56.738 C35.927 56.51 35.054 56.282 34.154 56.047 C33.217 55.804 32.279 55.562 31.312 55.312 C17.6 51.581 5.467 47.136 -3 35 C-6.841 27.317 -7.35 19.152 -5.168 10.824 C-3.77 6.87 -2.331 3.497 0 0 Z " fill="#5E57C1" transform="translate(294,656)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 2.98 2 4.96 2 7 C2.99 7 3.98 7 5 7 C5 4.69 5 2.38 5 0 C5.33 0 5.66 0 6 0 C6.561 9.957 5.344 16.658 -1.312 24.312 C-4.623 27.789 -7.897 30.482 -12 33 C-12.908 33.908 -13.815 34.815 -14.75 35.75 C-15.492 36.492 -16.235 37.235 -17 38 C-17.66 38 -18.32 38 -19 38 C-19 38.66 -19 39.32 -19 40 C-10.42 40 -1.84 40 7 40 C7 41.65 7 43.3 7 45 C-4.22 45 -15.44 45 -27 45 C-27 39 -27 39 -25.044 36.426 C-24.148 35.587 -23.252 34.748 -22.328 33.883 C-21.849 33.422 -21.37 32.961 -20.877 32.486 C-19.348 31.017 -17.801 29.57 -16.25 28.125 C-4.895 18.025 -4.895 18.025 -0.25 4 C-0.193 3.237 -0.137 2.474 -0.078 1.688 C-0.052 1.131 -0.027 0.574 0 0 Z " fill="#F4ECFE" transform="translate(805,865)"/>
<path d="M0 0 C6.6 0 13.2 0 20 0 C20 36.96 20 73.92 20 112 C12.08 112 4.16 112 -4 112 C-4.99 110.35 -5.98 108.7 -7 107 C-6.505 106.01 -6.505 106.01 -6 105 C-5.562 105.819 -5.123 106.637 -4.672 107.48 C-1.122 110.828 1.09 110.521 5.816 110.439 C6.784 110.398 7.753 110.356 8.75 110.312 C9.734 110.29 10.717 110.267 11.73 110.244 C14.155 110.185 16.577 110.103 19 110 C18.837 109.431 18.675 108.863 18.508 108.277 C17.99 105.957 17.877 103.944 17.88 101.571 C17.878 100.69 17.877 99.81 17.876 98.902 C17.879 97.944 17.883 96.986 17.886 95.999 C17.886 94.984 17.886 93.969 17.886 92.923 C17.887 89.569 17.895 86.214 17.902 82.859 C17.904 80.533 17.906 78.207 17.907 75.881 C17.91 69.759 17.92 63.637 17.931 57.515 C17.942 51.268 17.946 45.021 17.951 38.773 C17.962 26.516 17.979 14.258 18 2 C10.08 2.495 10.08 2.495 2 3 C2 26.76 2 50.52 2 75 C1.01 75.495 1.01 75.495 0 76 C0 50.92 0 25.84 0 0 Z " fill="#F8F1FE" transform="translate(695,469)"/>
<path d="M0 0 C3.125 -0.188 3.125 -0.188 6 0 C6.33 1.65 6.66 3.3 7 5 C9.224 3.075 9.224 3.075 10 0 C11.604 -0.054 13.208 -0.093 14.812 -0.125 C15.706 -0.148 16.599 -0.171 17.52 -0.195 C20 0 20 0 23 2 C24.155 2.619 25.31 3.237 26.5 3.875 C29.44 5.534 31.171 7.112 33 10 C33.66 10.66 34.32 11.32 35 12 C35 13.32 35 14.64 35 16 C34.34 16 33.68 16 33 16 C33 24.25 33 32.5 33 41 C33.99 41.495 33.99 41.495 35 42 C35 43.32 35 44.64 35 46 C35.99 46.495 35.99 46.495 37 47 C32.477 48.508 27.749 48 23 48 C23.012 47.229 23.023 46.458 23.035 45.664 C23.044 44.661 23.053 43.658 23.062 42.625 C23.074 41.627 23.086 40.63 23.098 39.602 C23.002 37.046 22.816 35.816 21 34 C20.913 32.511 20.893 31.019 20.902 29.527 C20.906 28.628 20.909 27.729 20.912 26.803 C20.92 25.857 20.929 24.912 20.938 23.938 C20.942 22.988 20.947 22.039 20.951 21.061 C20.963 18.707 20.979 16.354 21 14 C21.66 14 22.32 14 23 14 C20.802 12.263 20.802 12.263 18 11 C15.218 11.695 15.218 11.695 13 13 C13 12.34 13 11.68 13 11 C11.35 11.66 9.7 12.32 8 13 C8.33 13.99 8.66 14.98 9 16 C8.34 16 7.68 16 7 16 C7.01 16.898 7.021 17.797 7.032 18.722 C7.066 22.051 7.091 25.379 7.11 28.708 C7.12 30.149 7.134 31.59 7.151 33.032 C7.175 35.102 7.186 37.172 7.195 39.242 C7.206 40.489 7.216 41.735 7.227 43.019 C7 46 7 46 5 48 C1.875 48.125 1.875 48.125 -1 48 C-2.428 44.162 -3.221 41.094 -3 37 C-3 36.01 -3 35.02 -3 34 C-3 25.625 -3 25.625 -2 22 C-2.66 22 -3.32 22 -4 22 C-4.33 21.01 -4.66 20.02 -5 19 C-4.01 17.515 -4.01 17.515 -3 16 C-2.755 13.841 -2.598 11.671 -2.5 9.5 C-2.222 3.333 -2.222 3.333 0 0 Z M6 8 C5.67 6.68 5.34 5.36 5 4 C3.02 4 1.04 4 -1 4 C-1 17.86 -1 31.72 -1 46 C0.98 46 2.96 46 5 46 C5.025 45.148 5.05 44.295 5.076 43.417 C5.171 40.24 5.271 37.062 5.372 33.885 C5.415 32.512 5.457 31.14 5.497 29.767 C5.555 27.789 5.619 25.81 5.684 23.832 C5.739 22.049 5.739 22.049 5.795 20.23 C5.977 17.363 6.219 14.763 7 12 C11.095 9.394 15.312 9.628 20 10 C23.236 11.743 23.824 12.472 25 16 C25.151 18.283 25.249 20.569 25.316 22.855 C25.337 23.517 25.358 24.179 25.379 24.86 C25.445 26.969 25.504 29.078 25.562 31.188 C25.606 32.619 25.649 34.051 25.693 35.482 C25.8 38.988 25.902 42.494 26 46 C27.65 46 29.3 46 31 46 C31.124 41.125 31.215 36.252 31.275 31.376 C31.3 29.719 31.334 28.063 31.377 26.406 C31.438 24.018 31.466 21.631 31.488 19.242 C31.514 18.508 31.54 17.773 31.566 17.016 C31.569 13.067 31.051 10.588 28.587 7.571 C25.114 4.119 23.556 3.147 18.688 2.688 C13.677 3.112 9.648 4.352 6 8 Z M23 17 C24 19 24 19 24 19 Z M23 20 C24 24 24 24 24 24 Z " fill="#8576DD" transform="translate(291,864)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 35.97 1 71.94 1 109 C6.28 109 11.56 109 17 109 C17 80.95 17 52.9 17 24 C17.66 24 18.32 24 19 24 C18.993 25.628 18.993 25.628 18.987 27.289 C18.946 37.502 18.916 47.714 18.896 57.927 C18.886 63.177 18.872 68.428 18.849 73.679 C18.827 78.743 18.815 83.806 18.81 88.87 C18.807 90.806 18.799 92.741 18.789 94.676 C18.774 97.38 18.772 100.083 18.773 102.787 C18.766 103.593 18.759 104.399 18.751 105.23 C18.597 109.581 18.597 109.581 21 113 C17.729 113.029 14.458 113.047 11.188 113.062 C10.259 113.071 9.33 113.079 8.373 113.088 C7.48 113.091 6.588 113.094 5.668 113.098 C4.846 113.103 4.024 113.108 3.177 113.114 C1 113 1 113 -2 112 C-2.495 112.99 -2.495 112.99 -3 114 C-2.988 113.313 -2.977 112.626 -2.965 111.918 C-2.956 111.017 -2.947 110.116 -2.938 109.188 C-2.926 108.294 -2.914 107.401 -2.902 106.48 C-2.89 103.866 -2.89 103.866 -4 101 C-3.34 101 -2.68 101 -2 101 C-1.985 99.898 -1.985 99.898 -1.969 98.775 C-1.837 89.433 -1.701 80.091 -1.562 70.75 C-1.551 70.009 -1.54 69.268 -1.529 68.504 C-1.188 45.664 -0.763 22.831 0 0 Z " fill="#F9F4FE" transform="translate(435,470)"/>
<path d="M0 0 C2.793 0.254 2.793 0.254 5 0 C6.333 5.333 7.667 10.667 9 16 C8.34 16 7.68 16 7 16 C6.959 16.969 6.918 17.939 6.875 18.938 C5.665 27.24 -0.697 33.084 -7 38 C-5.386 34.598 -3.414 31.897 -1 29 C-2.32 28.67 -3.64 28.34 -5 28 C-4.711 27.383 -4.423 26.765 -4.125 26.129 C-1.64 20.461 0.372 15.221 -1 9 C-2.75 5.75 -2.75 5.75 -6 4 C-10.66 3.221 -15 3.291 -19 6 C-20.333 8.157 -20.333 8.157 -21.25 10.625 C-21.585 11.442 -21.92 12.26 -22.266 13.102 C-22.508 13.728 -22.75 14.355 -23 15 C-24.65 15 -26.3 15 -28 15 C-27.882 10.184 -27.489 6.8 -24.938 2.625 C-17.127 -4.355 -9.311 -4.138 0 0 Z " fill="#E9DEFC" transform="translate(716,856)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.33 6.93 6.66 13.86 7 21 C8.65 19.68 10.3 18.36 12 17 C15.812 16.25 15.812 16.25 19 16 C18.67 16.66 18.34 17.32 18 18 C15.686 18.731 13.352 19.401 11 20 C12.32 20.66 13.64 21.32 15 22 C13.577 22.681 13.577 22.681 12.125 23.375 C8.997 24.802 8.997 24.802 7 27 C6.755 29.432 6.627 31.759 6.586 34.195 C6.567 34.903 6.547 35.61 6.527 36.339 C6.467 38.601 6.421 40.863 6.375 43.125 C6.337 44.658 6.298 46.19 6.258 47.723 C6.162 51.482 6.078 55.241 6 59 C4.02 59 2.04 59 0 59 C0 39.53 0 20.06 0 0 Z " fill="#E9E0FD" transform="translate(376,851)"/>
<path d="M0 0 C7.59 0 15.18 0 23 0 C23.66 1.32 24.32 2.64 25 4 C24.34 4 23.68 4 23 4 C23 3.34 23 2.68 23 2 C16.07 2 9.14 2 2 2 C2 37.64 2 73.28 2 110 C7.28 110 12.56 110 18 110 C18 83.93 18 57.86 18 31 C18.66 31.33 19.32 31.66 20 32 C19.67 58.4 19.34 84.8 19 112 C12.73 112 6.46 112 0 112 C0 75.04 0 38.08 0 0 Z " fill="#FAF5FE" transform="translate(627,469)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.495 0.99 2.495 0.99 3 2 C3.99 1.67 4.98 1.34 6 1 C7.125 6.625 7.125 6.625 6 10 C5.01 10 4.02 10 3 10 C2.67 10.99 2.34 11.98 2 13 C1.34 12.34 0.68 11.68 0 11 C-2.6 10.719 -2.6 10.719 -5.5 10.812 C-6.48 10.819 -7.459 10.825 -8.469 10.832 C-10.994 10.836 -10.994 10.836 -13 12 C-13 11.34 -13 10.68 -13 10 C-16.3 10 -19.6 10 -23 10 C-22.01 10.66 -21.02 11.32 -20 12 C-20 12.66 -20 13.32 -20 14 C-20.66 14 -21.32 14 -22 14 C-22.027 15.583 -22.046 17.167 -22.062 18.75 C-22.074 19.632 -22.086 20.513 -22.098 21.422 C-22.006 23.831 -21.884 25.757 -21 28 C-18.341 28.886 -16.519 29.18 -13.77 29.316 C-12.967 29.358 -12.165 29.4 -11.338 29.443 C-10.505 29.483 -9.671 29.522 -8.812 29.562 C-7.968 29.606 -7.123 29.649 -6.252 29.693 C-4.168 29.799 -2.084 29.9 0 30 C0 32.64 0 35.28 0 38 C-7.59 38 -15.18 38 -23 38 C-22.67 41.63 -22.34 45.26 -22 49 C-21.67 49 -21.34 49 -21 49 C-20.67 51.31 -20.34 53.62 -20 56 C-20.66 56 -21.32 56 -22 56 C-22.495 58.97 -22.495 58.97 -23 62 C-26.3 62 -29.6 62 -33 62 C-32.756 60.709 -32.756 60.709 -32.506 59.392 C-32.016 56.105 -31.886 53.047 -31.902 49.727 C-31.907 47.996 -31.907 47.996 -31.912 46.23 C-31.92 45.041 -31.929 43.851 -31.938 42.625 C-31.942 41.412 -31.947 40.199 -31.951 38.949 C-31.963 35.966 -31.979 32.983 -32 30 C-32.33 30 -32.66 30 -33 30 C-33.042 27.667 -33.041 25.333 -33 23 C-32.67 22.67 -32.34 22.34 -32 22 C-31.763 19.474 -31.578 16.969 -31.438 14.438 C-31.394 13.727 -31.351 13.016 -31.307 12.283 C-31.2 10.522 -31.1 8.761 -31 7 C-31.66 7 -32.32 7 -33 7 C-32.875 5.125 -32.875 5.125 -32 3 C-28.393 1.353 -26.815 0.728 -23 2 C-15.722 2.47 -8.218 1.963 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M-30 4 C-30 22.48 -30 40.96 -30 60 C-28.02 60 -26.04 60 -24 60 C-24 52.08 -24 44.16 -24 36 C-17.07 36 -10.14 36 -3 36 C-3 34.35 -3 32.7 -3 31 C-9.93 31 -16.86 31 -24 31 C-24 23.74 -24 16.48 -24 9 C-15.42 9 -6.84 9 2 9 C2 7.35 2 5.7 2 4 C-8.56 4 -19.12 4 -30 4 Z M-32 23 C-31 25 -31 25 -31 25 Z " fill="#8477DE" transform="translate(577,850)"/>
<path d="M0 0 C8.024 6.383 12.365 14.29 14.75 24.188 C15.984 35.586 15.51 47.294 8.832 56.965 C3.218 63.731 -3.516 68.482 -12.25 70.188 C-23.137 70.996 -32.593 69.119 -41.25 62.188 C-50.122 53.014 -52.271 43.001 -52.688 30.562 C-52.441 20.143 -49.001 11.167 -42.25 3.188 C-29.663 -8.747 -14.57 -8.125 0 0 Z M-23.25 -4.812 C-30.165 -2.687 -36.477 -0.514 -41.25 5.188 C-41.25 5.847 -41.25 6.508 -41.25 7.188 C-41.91 7.188 -42.57 7.188 -43.25 7.188 C-43.25 7.847 -43.25 8.508 -43.25 9.188 C-43.91 9.188 -44.57 9.188 -45.25 9.188 C-51.758 22.949 -52.394 34.635 -48.25 49.188 C-45.105 57.309 -39.786 62.032 -32.25 66.188 C-24.14 69.707 -13.532 69.438 -5.312 66.234 C3.683 61.669 8.707 55.56 11.93 46.082 C14.417 37.304 15.192 26.405 10.75 18.188 C10.441 17.136 10.131 16.084 9.812 15 C9.287 13.608 9.287 13.608 8.75 12.188 C7.76 11.857 6.77 11.528 5.75 11.188 C5.75 10.197 5.75 9.207 5.75 8.188 C1.788 1.82 -4.343 -1.19 -11.25 -3.812 C-11.91 -3.482 -12.57 -3.153 -13.25 -2.812 C-14.24 -3.472 -15.23 -4.133 -16.25 -4.812 C-18.875 -4.98 -18.875 -4.98 -21.25 -4.812 C-21.58 -3.822 -21.91 -2.832 -22.25 -1.812 C-22.58 -2.803 -22.91 -3.793 -23.25 -4.812 Z " fill="#5D56C0" transform="translate(590.25,670.8125)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-6.93 1 -13.86 1 -21 1 C-21 37.96 -21 74.92 -21 113 C-13.74 113 -6.48 113 1 113 C1 76.7 1 40.4 1 3 C1.33 3 1.66 3 2 3 C2 40.29 2 77.58 2 116 C-19 116 -19 116 -22 115 C-22 114.34 -22 113.68 -22 113 C-22.66 112.67 -23.32 112.34 -24 112 C-23.691 110.824 -23.691 110.824 -23.375 109.625 C-23.251 108.759 -23.128 107.893 -23 107 C-23.66 106.34 -24.32 105.68 -25 105 C-24.67 104.01 -24.34 103.02 -24 102 C-23.214 98.307 -22.76 95.596 -24 92 C-24.33 91.01 -24.66 90.02 -25 89 C-24.34 88.01 -23.68 87.02 -23 86 C-23.165 85.051 -23.33 84.102 -23.5 83.125 C-24.057 79.645 -23.55 78.1 -22 75 C-22.66 75 -23.32 75 -24 75 C-24.33 71.7 -24.66 68.4 -25 65 C-24.01 64.505 -24.01 64.505 -23 64 C-23.278 62.206 -23.278 62.206 -23.562 60.375 C-23.957 57.832 -23.993 55.964 -23.516 53.417 C-22.886 49.243 -22.857 45.202 -22.867 40.988 C-22.866 40.138 -22.865 39.288 -22.864 38.412 C-22.864 36.625 -22.865 34.837 -22.87 33.05 C-22.875 30.311 -22.87 27.573 -22.863 24.834 C-22.864 23.094 -22.865 21.354 -22.867 19.613 C-22.865 18.795 -22.863 17.976 -22.861 17.132 C-22.875 13.833 -22.95 11.151 -24 8 C-23.545 5.262 -22.747 2.68 -22 0 C-14.488 -0.945 -7.512 -0.945 0 0 Z " fill="#312DA6" transform="translate(601,468)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C4.02 3.954 4.04 4.907 4.06 5.89 C4.136 9.429 4.225 12.968 4.317 16.507 C4.356 18.039 4.391 19.57 4.422 21.102 C4.468 23.304 4.526 25.505 4.586 27.707 C4.597 28.392 4.609 29.076 4.621 29.781 C4.733 33.385 4.944 34.916 7 38 C7.99 38 8.98 38 10 38 C10.639 38.206 11.279 38.413 11.938 38.625 C14.043 39.173 14.043 39.173 16.188 38.312 C18.18 37.131 18.18 37.131 18.75 34.875 C18.832 34.256 18.915 33.638 19 33 C19.66 33 20.32 33 21 33 C20.861 29.895 20.713 26.791 20.562 23.688 C20.523 22.806 20.484 21.925 20.443 21.018 C20.401 20.17 20.36 19.322 20.316 18.449 C20.28 17.669 20.243 16.889 20.205 16.085 C20.059 13.807 20.059 13.807 19 11 C19.375 8.562 19.375 8.562 20 6 C20.186 5.237 20.371 4.474 20.562 3.688 C20.707 3.131 20.851 2.574 21 2 C23.333 1.958 25.667 1.959 28 2 C29.888 3.888 29.265 7.147 29.352 9.668 C29.375 10.34 29.398 11.013 29.422 11.706 C29.469 13.134 29.515 14.562 29.558 15.99 C29.624 18.161 29.702 20.332 29.781 22.502 C29.826 23.891 29.871 25.279 29.914 26.668 C29.955 27.929 29.996 29.19 30.038 30.489 C30.002 33.782 29.641 36.776 29 40 C29.99 40.495 29.99 40.495 31 41 C30.34 41 29.68 41 29 41 C29 42.32 29 43.64 29 45 C29.99 45.495 29.99 45.495 31 46 C30.34 46.66 29.68 47.32 29 48 C26.271 47.967 23.703 47.436 21 47 C20.67 46.01 20.34 45.02 20 44 C19.196 44.66 18.391 45.32 17.562 46 C13.53 48.669 9.694 48.826 5 48 C0.1 45.596 -2.434 42.775 -5 38 C-5.647 34.525 -5.745 31.091 -5.812 27.562 C-5.844 26.595 -5.876 25.627 -5.908 24.63 C-6.114 16.703 -5.799 8.889 -5 1 C-3.35 1.33 -1.7 1.66 0 2 C0 1.34 0 0.68 0 0 Z M-3 4 C-3.124 8.875 -3.215 13.748 -3.275 18.624 C-3.3 20.281 -3.334 21.937 -3.377 23.594 C-3.438 25.982 -3.466 28.369 -3.488 30.758 C-3.514 31.492 -3.54 32.227 -3.566 32.984 C-3.569 36.932 -3.046 39.425 -0.594 42.453 C3.587 46.558 6.608 46.33 12.289 46.293 C15.881 45.905 17.67 44.731 20 42 C20 41.34 20 40.68 20 40 C20.66 40 21.32 40 22 40 C22 41.98 22 43.96 22 46 C23.98 46 25.96 46 28 46 C28 32.14 28 18.28 28 4 C26.02 4 24.04 4 22 4 C21.985 4.883 21.971 5.765 21.956 6.675 C21.895 9.964 21.82 13.253 21.738 16.543 C21.704 17.964 21.676 19.384 21.654 20.805 C21.62 22.854 21.568 24.901 21.512 26.949 C21.486 28.18 21.459 29.411 21.432 30.678 C20.97 34.227 20.135 36.157 18 39 C15.673 40.163 14.153 40.199 11.562 40.25 C10.78 40.276 9.998 40.302 9.191 40.328 C6.463 39.92 5.656 39.167 4 37 C2.857 33.571 2.79 30.759 2.684 27.145 C2.663 26.483 2.642 25.821 2.621 25.14 C2.555 23.031 2.496 20.922 2.438 18.812 C2.394 17.381 2.351 15.949 2.307 14.518 C2.2 11.012 2.098 7.506 2 4 C0.35 4 -1.3 4 -3 4 Z " fill="#897CDC" transform="translate(252,864)"/>
<path d="M0 0 C7.393 5.398 12.219 13.538 14.641 22.324 C16.338 34.961 15.336 45.947 9.25 57.25 C2.13 65.952 -5.276 69.65 -16.449 71.379 C-24.439 71.824 -32.222 68.829 -38.312 63.75 C-39.571 62.53 -40.802 61.28 -42 60 C-42.567 59.403 -43.134 58.806 -43.719 58.191 C-51.579 48.925 -51.923 36.625 -51 25 C-49.451 15.668 -44.966 7.087 -37.625 1.062 C-25.807 -7.261 -12.556 -7.368 0 0 Z M-40.688 5.75 C-42.306 7.757 -43.668 9.793 -45 12 C-45.415 12.66 -45.83 13.32 -46.258 14 C-51.25 23.775 -50.762 38.866 -47.562 49.188 C-43.603 57.973 -36.918 64.523 -28 68 C-18.803 70.786 -9.461 69.203 -1 65 C5.965 60.72 10.617 53.572 12.559 45.75 C14.289 37.92 14.317 30.903 13 23 C12.01 23.495 12.01 23.495 11 24 C11.205 23.274 11.41 22.549 11.621 21.801 C12.295 16.817 9.647 13.091 7 9 C2.299 3.384 2.299 3.384 -4 0 C-5.011 -0.495 -6.021 -0.99 -7.062 -1.5 C-18.697 -6.437 -31.943 -3.318 -40.688 5.75 Z " fill="#524CBA" transform="translate(464,670)"/>
<path d="M0 0 C0.081 16.986 -0.153 33.956 -0.5 50.938 C-0.548 53.386 -0.597 55.835 -0.645 58.283 C-0.761 64.189 -0.879 70.094 -1 76 C-1.33 76 -1.66 76 -2 76 C-2 51.25 -2 26.5 -2 1 C-8.6 1 -15.2 1 -22 1 C-22 27.07 -22 53.14 -22 80 C-24.656 78.672 -25.058 77.319 -26.438 74.688 C-26.966 73.695 -27.495 72.702 -28.039 71.68 C-28.344 71.106 -28.648 70.532 -28.962 69.94 C-31.045 66.046 -33.183 62.181 -35.312 58.312 C-35.789 57.446 -36.266 56.58 -36.757 55.687 C-43.193 44.005 -49.695 32.375 -56.551 20.933 C-59.258 16.383 -61.811 11.825 -64 7 C-63.67 6.34 -63.34 5.68 -63 5 C-44.787 34.893 -44.787 34.893 -38.312 48.562 C-37.099 51.273 -37.099 51.273 -34 53 C-34 53.66 -34 54.32 -34 55 C-33.039 56.854 -32.032 58.684 -31 60.5 C-28 65.778 -28 65.778 -28 68 C-27.01 68.495 -27.01 68.495 -26 69 C-25.01 69.99 -25.01 69.99 -24 71 C-24.186 70.237 -24.371 69.474 -24.562 68.688 C-25.044 64.626 -24.689 61.072 -24.262 57.023 C-23.424 47.894 -23.424 47.894 -25 39 C-24.67 38.01 -24.34 37.02 -24 36 C-23.916 33.678 -23.893 31.354 -23.902 29.031 C-23.904 28.355 -23.905 27.678 -23.907 26.981 C-23.912 24.82 -23.925 22.66 -23.938 20.5 C-23.943 19.046 -23.947 17.591 -23.951 16.137 C-23.963 12.424 -23.981 8.712 -24 5 C-24 2 -24 2 -23 0 C-20.427 -0.345 -17.96 -0.561 -15.375 -0.688 C-14.675 -0.735 -13.975 -0.782 -13.254 -0.83 C-8.69 -1.088 -4.536 -0.704 0 0 Z M-24 71 C-24.33 71.66 -24.66 72.32 -25 73 C-24.34 73.66 -23.68 74.32 -23 75 C-23.33 73.68 -23.66 72.36 -24 71 Z " fill="#4D48B9" transform="translate(717,468)"/>
<path d="M0 0 C4.114 4.388 4.14 8.768 4.098 14.512 C4.096 15.24 4.095 15.968 4.093 16.718 C4.088 19.042 4.075 21.364 4.062 23.688 C4.057 25.264 4.053 26.84 4.049 28.416 C4.038 32.277 4.021 36.139 4 40 C2.35 40 0.7 40 -1 40 C-3.521 35.658 -2.699 32.872 -1.527 28.285 C-0.792 25.101 -0.669 22.245 -1 19 C-1.99 18.34 -2.98 17.68 -4 17 C-3.34 16.67 -2.68 16.34 -2 16 C-1.581 11.731 -1.52 9.67 -4.062 6.125 C-7.577 3.583 -9.675 3 -14 3 C-17.126 4.32 -18.566 5.277 -20.312 8.188 C-20.539 8.786 -20.766 9.384 -21 10 C-22.98 10 -24.96 10 -27 10 C-25.797 3.865 -24.192 1.531 -19 -2 C-12.644 -4.754 -5.782 -3.174 0 0 Z " fill="#EAE1FD" transform="translate(234,870)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-0.86 1.555 -0.86 1.555 -1.738 1.102 C-11.04 -3.429 -19.599 -4.455 -29.508 -1.168 C-36.194 1.966 -41.213 7.152 -44.246 13.848 C-46.463 20.177 -47.357 25.656 -47.312 32.312 C-47.309 33.151 -47.306 33.99 -47.302 34.854 C-47.155 43.218 -45.632 49.942 -41 57 C-41 57.66 -41 58.32 -41 59 C-40.34 59 -39.68 59 -39 59 C-37.713 60.287 -36.464 61.614 -35.266 62.984 C-32.548 65.403 -30.216 66.195 -26.75 67.25 C-25.672 67.585 -24.595 67.92 -23.484 68.266 C-22.255 68.629 -22.255 68.629 -21 69 C-20.67 68.34 -20.34 67.68 -20 67 C-20 67.66 -20 68.32 -20 69 C-8.375 67.396 -8.375 67.396 1 61 C2.614 58.428 3.828 55.795 5 53 C5.658 51.663 6.323 50.328 7 49 C20.463 48.636 20.463 48.636 27 51 C28.331 51.341 29.664 51.679 31 52 C30.722 59.365 28.056 64.879 24 71 C23.01 71.495 23.01 71.495 22 72 C23.504 68.213 25.055 64.448 26.625 60.688 C27.294 59.085 27.294 59.085 27.977 57.449 C28.314 56.641 28.652 55.833 29 55 C27.436 53.436 25.689 53.559 23.527 53.246 C22.628 53.114 21.729 52.982 20.803 52.846 C19.857 52.711 18.912 52.576 17.938 52.438 C16.988 52.299 16.039 52.16 15.061 52.018 C12.708 51.675 10.354 51.336 8 51 C7.869 51.764 7.737 52.529 7.602 53.316 C6.182 59.648 1.307 64.518 -4 68 C-11.814 71.299 -21.856 71.357 -30.051 69.195 C-38.408 65.426 -44.21 58.816 -47.438 50.312 C-51.687 37.45 -51.078 24.502 -46 12 C-42.692 5.739 -37.05 -0.942 -30.5 -4 C-20.148 -7.11 -8.948 -6.023 0 0 Z " fill="#645EC3" transform="translate(257,493)"/>
<path d="M0 0 C4.148 4.861 4.404 9.449 4.566 15.547 C4.59 16.317 4.613 17.087 4.637 17.881 C4.684 19.507 4.729 21.132 4.772 22.758 C4.84 25.255 4.917 27.753 4.996 30.25 C5.041 31.828 5.085 33.406 5.129 34.984 C5.153 35.735 5.177 36.486 5.201 37.26 C5.245 39.051 5.235 40.842 5.215 42.633 C4.215 43.633 4.215 43.633 1.34 43.695 C0.309 43.675 -0.723 43.654 -1.785 43.633 C-2.775 43.633 -3.765 43.633 -4.785 43.633 C-5.445 43.303 -6.105 42.973 -6.785 42.633 C-8.648 42.964 -10.501 43.347 -12.348 43.758 C-17.895 44.739 -22.984 44.981 -27.785 41.633 C-30.503 36.562 -30.373 31.243 -29.785 25.633 C-28.8 23.625 -27.804 21.623 -26.785 19.633 C-27.327 16.339 -27.327 16.339 -28.785 13.633 C-29.445 13.303 -30.105 12.973 -30.785 12.633 C-30.91 10.258 -30.91 10.258 -30.785 7.633 C-30.125 6.973 -29.465 6.313 -28.785 5.633 C-28.785 4.973 -28.785 4.313 -28.785 3.633 C-20.694 -2.396 -9.135 -6.996 0 0 Z M-25.16 4.008 C-26.794 6.647 -27.435 8.571 -27.785 11.633 C-24.912 12.05 -24.912 12.05 -21.785 11.633 C-20.104 9.275 -20.104 9.275 -18.785 6.633 C-16.052 4.485 -14.76 4.635 -11.223 4.883 C-7.785 5.633 -7.785 5.633 -4.785 7.633 C-4.055 9.797 -4.055 9.797 -3.598 12.258 C-3.439 13.075 -3.281 13.892 -3.117 14.734 C-3.008 15.361 -2.898 15.987 -2.785 16.633 C-3.608 16.659 -4.43 16.684 -5.277 16.711 C-12.618 17.084 -19.413 17.696 -25.785 21.633 C-28.607 25.099 -29.006 27.851 -28.848 32.195 C-28.334 35.621 -27.542 37.368 -24.91 39.633 C-20.341 42.557 -17.16 42.997 -11.785 42.633 C-8.513 41.275 -6.286 40.134 -3.785 37.633 C-3.125 38.953 -2.465 40.273 -1.785 41.633 C-0.135 41.633 1.515 41.633 3.215 41.633 C3.289 37.043 3.344 32.454 3.38 27.863 C3.395 26.304 3.415 24.744 3.441 23.185 C3.478 20.935 3.495 18.687 3.508 16.438 C3.523 15.747 3.539 15.056 3.555 14.344 C3.556 9.696 2.631 6.59 0.215 2.633 C-3.979 -1.29 -8.037 -1.677 -13.695 -1.582 C-18.444 -1.094 -21.825 0.672 -25.16 4.008 Z M-19.785 7.633 C-20.609 11.152 -20.609 11.152 -20.785 14.633 C-20.455 14.963 -20.125 15.293 -19.785 15.633 C-20.775 16.128 -20.775 16.128 -21.785 16.633 C-21.785 15.643 -21.785 14.653 -21.785 13.633 C-22.775 13.963 -23.765 14.293 -24.785 14.633 C-23.795 15.128 -23.795 15.128 -22.785 15.633 C-22.785 16.623 -22.785 17.613 -22.785 18.633 C-20.804 18.352 -18.826 18.058 -16.848 17.758 C-15.746 17.595 -14.643 17.433 -13.508 17.266 C-12.16 16.952 -12.16 16.952 -10.785 16.633 C-10.455 15.973 -10.125 15.313 -9.785 14.633 C-7.723 14.008 -7.723 14.008 -5.785 13.633 C-5.455 14.293 -5.125 14.953 -4.785 15.633 C-4.455 14.643 -4.125 13.653 -3.785 12.633 C-4.445 11.973 -5.105 11.313 -5.785 10.633 C-5.785 9.643 -5.785 8.653 -5.785 7.633 C-7.105 7.633 -8.425 7.633 -9.785 7.633 C-10.115 6.973 -10.445 6.313 -10.785 5.633 C-15.575 5.399 -15.575 5.399 -19.785 7.633 Z " fill="#796DD8" transform="translate(234.78515625,868.3671875)"/>
<path d="M0 0 C-0.492 1.922 -0.492 1.922 -2 4 C-4.804 4.628 -7.288 5.015 -10.125 5.25 C-10.879 5.327 -11.633 5.405 -12.41 5.484 C-14.272 5.673 -16.136 5.839 -18 6 C-18 5.34 -18 4.68 -18 4 C-19.65 4.66 -21.3 5.32 -23 6 C-22.67 6.99 -22.34 7.98 -22 9 C-21.01 8.67 -20.02 8.34 -19 8 C-19.098 10.018 -19.195 12.036 -19.293 14.055 C-19.196 14.697 -19.1 15.339 -19 16 C-15.89 18.073 -15.109 18.274 -11.562 18.25 C-10.801 18.255 -10.039 18.26 -9.254 18.266 C-6.686 17.963 -5.148 17.441 -3 16 C-0.998 13.029 -0.654 10.279 -0.375 6.75 C-0.3 5.858 -0.225 4.966 -0.148 4.047 C-0.099 3.371 -0.05 2.696 0 2 C3 4 3 4 3.73 7.477 C3.896 8.858 4.048 10.241 4.188 11.625 C4.31 12.685 4.31 12.685 4.436 13.766 C4.636 15.509 4.819 17.254 5 19 C5.33 19 5.66 19 6 19 C6 20.65 6 22.3 6 24 C4.02 24 2.04 24 0 24 C-0.33 22.35 -0.66 20.7 -1 19 C-1.825 19.804 -2.65 20.609 -3.5 21.438 C-7.111 24.42 -10.339 25.307 -15 25 C-20.105 23.468 -23.42 21.747 -26 17 C-26.902 12.32 -26.753 9.117 -24.062 5.125 C-16.443 -0.162 -9.081 -0.415 0 0 Z M-25 10 C-24 13 -24 13 -24 13 Z " fill="#EEE5FD" transform="translate(610,886)"/>
<path d="M0 0 C10.558 5.102 15.489 13.185 19.875 23.75 C20.871 29.624 20.648 35.496 20.5 41.438 C20.17 41.438 19.84 41.438 19.5 41.438 C19.402 40.482 19.304 39.527 19.203 38.543 C17.811 26.506 15.721 15.135 6.5 6.438 C-1.359 1.305 -9.642 -0.849 -18.957 0.898 C-26.977 2.993 -33.794 7.165 -38.402 14.195 C-42.214 21.982 -44.48 30.777 -43.5 39.438 C-43.394 40.406 -43.289 41.374 -43.18 42.371 C-42.29 48.851 -40.704 53.753 -37.5 59.438 C-37.17 60.428 -36.84 61.418 -36.5 62.438 C-35.84 62.438 -35.18 62.438 -34.5 62.438 C-33.861 63.18 -33.221 63.923 -32.562 64.688 C-27.696 69.712 -20.266 71.638 -13.438 71.875 C-4.066 71.401 4.063 68.453 10.5 61.438 C14.057 55.755 16.465 49.807 18.5 43.438 C20.085 47.982 18.676 51.139 16.996 55.48 C12.975 63.428 6.339 70.126 -2.188 73.008 C-10.463 74.546 -21.064 74.917 -28.582 70.758 C-36.651 65.203 -42.126 59.033 -44.812 49.438 C-47.102 36.909 -46.572 23.745 -39.812 12.75 C-30.346 -0.489 -15.649 -5.629 0 0 Z " fill="#4A45B4" transform="translate(372.5,489.5625)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.008 1.83 3.016 2.661 3.025 3.517 C3.102 11.345 3.19 19.172 3.288 27 C3.337 31.024 3.384 35.048 3.422 39.073 C3.459 42.957 3.505 46.841 3.558 50.724 C3.577 52.206 3.592 53.687 3.603 55.169 C3.62 57.245 3.649 59.32 3.681 61.396 C3.694 62.577 3.708 63.758 3.722 64.975 C3.993 67.919 4.599 69.463 6 72 C6 72.99 6 73.98 6 75 C7.485 75.495 7.485 75.495 9 76 C8.01 76.495 8.01 76.495 7 77 C7.797 77.364 7.797 77.364 8.609 77.734 C12.74 79.694 15.971 81.466 19 85 C18.67 85.66 18.34 86.32 18 87 C17.599 86.602 17.198 86.203 16.785 85.793 C12.709 81.994 9.21 80.066 3.82 78.641 C2 78 2 78 1 76 C0.908 73.753 0.883 71.504 0.886 69.255 C0.886 68.563 0.886 67.87 0.886 67.156 C0.887 64.862 0.895 62.568 0.902 60.273 C0.904 58.684 0.906 57.095 0.907 55.507 C0.91 51.321 0.92 47.135 0.931 42.95 C0.942 38.68 0.946 34.41 0.951 30.141 C0.962 21.76 0.979 13.38 1 5 C-1.64 5 -4.28 5 -7 5 C-6.978 5.901 -6.956 6.802 -6.933 7.731 C-6.732 16.235 -6.58 24.738 -6.482 33.243 C-6.43 37.616 -6.359 41.987 -6.246 46.358 C-6.137 50.58 -6.077 54.799 -6.051 59.021 C-6.033 60.63 -5.997 62.239 -5.943 63.846 C-5.785 68.736 -5.751 73.248 -7 78 C-9.384 80.425 -11.408 81.605 -14.542 82.891 C-18.441 84.65 -19.843 87.405 -22 91 C-22.33 90.01 -22.66 89.02 -23 88 C-21.062 84.812 -21.062 84.812 -19 82 C-19.33 81.34 -19.66 80.68 -20 80 C-19.278 80.082 -18.556 80.165 -17.812 80.25 C-14.165 79.926 -12.654 78.427 -10 76 C-10.33 74.68 -10.66 73.36 -11 72 C-10.01 72.33 -9.02 72.66 -8 73 C-8.002 72.138 -8.004 71.276 -8.007 70.387 C-8.027 62.279 -8.042 54.171 -8.052 46.062 C-8.057 41.893 -8.064 37.724 -8.075 33.555 C-8.086 29.535 -8.092 25.515 -8.095 21.495 C-8.097 19.958 -8.1 18.421 -8.106 16.884 C-8.113 14.739 -8.114 12.593 -8.114 10.448 C-8.116 9.224 -8.118 8 -8.12 6.74 C-8 4 -8 4 -7 3 C-4.667 2.959 -2.333 2.958 0 3 C0 2.01 0 1.02 0 0 Z " fill="#6E72E4" transform="translate(512,152)"/>
<path d="M0 0 C1.226 0.014 1.226 0.014 2.477 0.027 C3.416 0.045 3.416 0.045 4.375 0.062 C4.37 0.698 4.365 1.333 4.359 1.987 C4.338 4.887 4.325 7.787 4.312 10.688 C4.304 11.687 4.296 12.686 4.287 13.715 C4.284 14.688 4.281 15.661 4.277 16.664 C4.272 17.554 4.267 18.445 4.261 19.362 C4.321 22.116 4.321 22.116 4.875 25.008 C5.394 28.177 5.489 31.127 5.473 34.336 C5.469 35.49 5.466 36.643 5.463 37.832 C5.455 39.022 5.446 40.212 5.438 41.438 C5.433 42.651 5.428 43.864 5.424 45.113 C5.412 48.096 5.396 51.079 5.375 54.062 C7.685 54.393 9.995 54.722 12.375 55.062 C12.705 57.702 13.035 60.342 13.375 63.062 C3.462 64.628 3.462 64.628 -1.625 63.062 C-3.451 60.844 -4.392 58.712 -5.625 56.062 C-6.285 55.072 -6.945 54.082 -7.625 53.062 C-7.602 50.18 -7.602 50.18 -7.25 46.938 C-7.139 45.862 -7.028 44.787 -6.914 43.68 C-6.819 42.816 -6.723 41.952 -6.625 41.062 C-6.572 39.694 -6.572 39.694 -7.625 37.062 C-7.453 33.725 -7.111 30.419 -6.781 27.094 C-6.438 24.016 -6.438 24.016 -7.625 21.062 C-7.857 18.339 -7.756 15.812 -7.625 13.062 C-6.965 12.403 -6.305 11.742 -5.625 11.062 C-5.955 10.072 -6.285 9.082 -6.625 8.062 C-6.665 5.73 -6.669 3.395 -6.625 1.062 C-4.22 -0.14 -2.675 -0.038 0 0 Z M-3.625 3.062 C-3.701 10.358 -3.754 17.654 -3.79 24.95 C-3.805 27.431 -3.825 29.912 -3.851 32.393 C-3.888 35.963 -3.905 39.532 -3.918 43.102 C-3.933 44.208 -3.949 45.315 -3.965 46.454 C-3.966 52.037 -3.926 56.306 -0.625 61.062 C1.78 62.265 3.325 62.163 6 62.125 C6.817 62.116 7.635 62.107 8.477 62.098 C9.103 62.086 9.73 62.074 10.375 62.062 C10.375 60.413 10.375 58.763 10.375 57.062 C8.065 56.403 5.755 55.742 3.375 55.062 C3.045 37.903 2.715 20.742 2.375 3.062 C0.395 3.062 -1.585 3.062 -3.625 3.062 Z M-5.625 22.062 C-4.625 24.062 -4.625 24.062 -4.625 24.062 Z M-5.625 29.062 C-4.625 31.062 -4.625 31.062 -4.625 31.062 Z M-5.625 32.062 C-4.625 36.062 -4.625 36.062 -4.625 36.062 Z M-5.625 43.062 C-5.625 44.712 -5.625 46.362 -5.625 48.062 C-5.295 48.062 -4.965 48.062 -4.625 48.062 C-4.625 46.413 -4.625 44.763 -4.625 43.062 C-4.955 43.062 -5.285 43.062 -5.625 43.062 Z " fill="#9182E3" transform="translate(650.625,847.9375)"/>
<path d="M0 0 C2.5 0.25 2.5 0.25 4.5 2.25 C4.741 4.389 4.741 4.389 4.727 7.067 C4.727 8.068 4.727 9.069 4.727 10.1 C4.716 11.183 4.706 12.267 4.695 13.383 C4.692 14.49 4.69 15.597 4.687 16.737 C4.676 20.283 4.65 23.829 4.625 27.375 C4.615 29.775 4.606 32.174 4.598 34.574 C4.576 40.466 4.542 46.358 4.5 52.25 C5.181 52.245 5.862 52.24 6.564 52.234 C9.647 52.213 12.73 52.2 15.812 52.188 C16.884 52.179 17.956 52.171 19.061 52.162 C20.089 52.159 21.117 52.156 22.176 52.152 C23.124 52.147 24.072 52.142 25.048 52.136 C27.5 52.25 27.5 52.25 30.5 53.25 C30.005 50.28 30.005 50.28 29.5 47.25 C30.49 47.745 30.49 47.745 31.5 48.25 C31.893 50.573 32.007 52.868 32.156 55.219 C32.27 55.889 32.383 56.559 32.5 57.25 C33.49 57.745 33.49 57.745 34.5 58.25 C33.51 58.91 32.52 59.57 31.5 60.25 C31.5 59.59 31.5 58.93 31.5 58.25 C30.84 58.25 30.18 58.25 29.5 58.25 C29.17 59.24 28.84 60.23 28.5 61.25 C24.313 61.279 20.125 61.297 15.938 61.312 C14.756 61.321 13.575 61.329 12.357 61.338 C11.207 61.341 10.056 61.344 8.871 61.348 C7.818 61.353 6.766 61.358 5.681 61.364 C2.231 61.24 -1.092 60.793 -4.5 60.25 C-4.546 52.722 -4.582 45.195 -4.604 37.667 C-4.614 34.172 -4.628 30.676 -4.651 27.181 C-4.677 23.165 -4.686 19.149 -4.695 15.133 C-4.706 13.874 -4.716 12.616 -4.727 11.32 C-4.727 10.159 -4.727 8.998 -4.727 7.801 C-4.731 6.775 -4.736 5.749 -4.741 4.692 C-4.399 1.227 -3.453 0.345 0 0 Z M-3.5 3.25 C-3.5 21.73 -3.5 40.21 -3.5 59.25 C6.73 59.25 16.96 59.25 27.5 59.25 C27.5 57.6 27.5 55.95 27.5 54.25 C19.25 54.25 11 54.25 2.5 54.25 C2.5 37.42 2.5 20.59 2.5 3.25 C0.52 3.25 -1.46 3.25 -3.5 3.25 Z M29.5 54.25 C30.16 54.91 30.82 55.57 31.5 56.25 C31.5 55.59 31.5 54.93 31.5 54.25 C30.84 54.25 30.18 54.25 29.5 54.25 Z " fill="#8072D9" transform="translate(172.5,850.75)"/>
<path d="M0 0 C0.75 -0.004 1.5 -0.008 2.273 -0.012 C5.811 -0.005 8.973 0.172 12.375 1.125 C12.705 2.775 13.035 4.425 13.375 6.125 C12.99 5.641 12.605 5.156 12.208 4.657 C9.764 2.614 8.21 2.701 5.051 2.613 C3.509 2.56 3.509 2.56 1.936 2.506 C0.328 2.472 0.328 2.472 -1.312 2.438 C-2.396 2.403 -3.479 2.369 -4.596 2.334 C-7.272 2.251 -9.948 2.182 -12.625 2.125 C-12.618 2.706 -12.611 3.286 -12.604 3.884 C-12.434 17.996 -12.276 32.109 -12.13 46.221 C-12.059 53.046 -11.985 59.871 -11.901 66.695 C-11.821 73.276 -11.751 79.857 -11.687 86.439 C-11.661 88.955 -11.632 91.471 -11.599 93.986 C-11.553 97.499 -11.52 101.012 -11.49 104.525 C-11.473 105.574 -11.457 106.624 -11.44 107.705 C-11.434 108.659 -11.428 109.612 -11.422 110.595 C-11.413 111.426 -11.404 112.257 -11.395 113.114 C-11.625 115.125 -11.625 115.125 -13.625 117.125 C-14.285 117.125 -14.945 117.125 -15.625 117.125 C-15.648 102.443 -15.666 87.762 -15.677 73.08 C-15.682 66.264 -15.689 59.447 -15.7 52.63 C-15.711 46.055 -15.717 39.481 -15.72 32.906 C-15.722 30.394 -15.725 27.882 -15.731 25.369 C-15.738 21.859 -15.739 18.349 -15.739 14.839 C-15.742 13.793 -15.746 12.747 -15.749 11.669 C-15.748 10.714 -15.747 9.76 -15.745 8.776 C-15.746 7.945 -15.747 7.115 -15.748 6.259 C-15.625 4.125 -15.625 4.125 -14.625 1.125 C-9.696 0.139 -5.001 -0.026 0 0 Z " fill="#7B72CC" transform="translate(639.625,466.875)"/>
<path d="M0 0 C4.184 2.138 6.239 3.687 7.812 8.188 C9.103 12.758 9.625 17.273 10 22 C10.66 22 11.32 22 12 22 C11.67 22.99 11.34 23.98 11 25 C10.34 24.67 9.68 24.34 9 24 C9.012 24.675 9.023 25.351 9.035 26.047 C9.044 26.939 9.053 27.831 9.062 28.75 C9.074 29.632 9.086 30.513 9.098 31.422 C9.008 33.782 8.669 35.745 8 38 C7.34 38 6.68 38 6 38 C5.753 38.887 5.505 39.774 5.25 40.688 C3.961 44.104 2.4 46.28 0 49 C-0.66 48.67 -1.32 48.34 -2 48 C-0.35 45.69 1.3 43.38 3 41 C-0.465 43.475 -0.465 43.475 -4 46 C-3.732 45.361 -3.464 44.721 -3.188 44.062 C-2.6 42.547 -2.6 42.547 -2 41 C-1.736 40.415 -1.471 39.83 -1.199 39.227 C2.099 31.067 1.897 21.346 0.453 12.742 C-0.112 9.322 0.485 6.41 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CFC3F6" transform="translate(762,858)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 1.32 6 2.64 6 4 C6.928 3.319 7.856 2.639 8.812 1.938 C9.864 1.298 10.916 0.659 12 0 C12.99 0.33 13.98 0.66 15 1 C14.34 1.66 13.68 2.32 13 3 C13.33 3.66 13.66 4.32 14 5 C12.824 5.619 12.824 5.619 11.625 6.25 C8.759 8.16 8.168 8.849 7 12 C6.633 15.292 6.633 15.292 6.586 18.953 C6.567 19.613 6.547 20.274 6.527 20.954 C6.468 23.052 6.421 25.151 6.375 27.25 C6.337 28.677 6.298 30.104 6.258 31.531 C6.162 35.021 6.078 38.51 6 42 C4.02 42 2.04 42 0 42 C0 28.14 0 14.28 0 0 Z " fill="#F6EEFE" transform="translate(437,868)"/>
<path d="M0 0 C6.667 0 13.333 0 20 0 C22.222 2.222 22.926 4.085 24 7 C23.113 6.361 22.226 5.721 21.312 5.062 C18.308 3.192 16.449 2.406 13 2 C12.34 3.32 11.68 4.64 11 6 C12.671 6.588 12.671 6.588 14.375 7.188 C17.106 8.362 17.847 8.748 19.438 11.375 C20.001 14.007 20.04 16.319 20 19 C18.938 19.182 17.876 19.364 16.781 19.551 C9.578 20.82 2.741 22.051 -4 25 C-4 24.34 -4 23.68 -4 23 C1.625 20 1.625 20 5 20 C5.33 18.35 5.66 16.7 6 15 C6.577 15.186 7.155 15.371 7.75 15.562 C10.207 16.04 11.644 15.757 14 15 C14.33 15.66 14.66 16.32 15 17 C15.99 17.33 16.98 17.66 18 18 C17.34 17.01 16.68 16.02 16 15 C15.875 12.312 15.875 12.312 16 10 C15.01 10 14.02 10 13 10 C11.237 9.691 11.237 9.691 9.438 9.375 C8.303 9.251 7.169 9.128 6 9 C5.34 9.66 4.68 10.32 4 11 C3.01 11.495 3.01 11.495 2 12 C2.66 13.98 3.32 15.96 4 18 C2.375 18.75 2.375 18.75 0 19 C-3.188 17.438 -3.188 17.438 -6 15 C-6.875 11.75 -6.875 11.75 -7 9 C-6.34 9 -5.68 9 -5 9 C-4.732 8.423 -4.464 7.845 -4.188 7.25 C-2.984 4.969 -1.555 3.05 0 1 C0 0.67 0 0.34 0 0 Z M6 18 C10 19 10 19 10 19 Z " fill="#B4A7ED" transform="translate(590,866)"/>
<path d="M0 0 C2.698 2.59 2.329 5.877 2.562 9.5 C1.903 9.005 1.242 8.51 0.562 8 C-3.2 6.119 -6.353 5.699 -10.438 6.5 C-14.341 8.562 -15.796 9.91 -17.438 13.977 C-19.693 22.021 -20.108 30.193 -20.438 38.5 C-21.097 38.5 -21.758 38.5 -22.438 38.5 C-22.768 39.16 -23.097 39.82 -23.438 40.5 C-24.097 40.17 -24.758 39.84 -25.438 39.5 C-25.526 36.188 -25.578 32.876 -25.625 29.562 C-25.65 28.636 -25.675 27.71 -25.701 26.756 C-25.766 20.631 -25.233 15.05 -22.438 9.5 C-21.778 9.17 -21.117 8.84 -20.438 8.5 C-20.231 7.737 -20.025 6.974 -19.812 6.188 C-16.055 -1.157 -7.236 -1.484 0 0 Z " fill="#EEE5FD" transform="translate(757.4375,853.5)"/>
<path d="M0 0 C1.901 1.562 3.387 3.126 5 5 C1.984 3.869 -0.846 2.567 -3.688 1.062 C-11.752 -2.972 -19.456 -4.519 -28.438 -4.375 C-29.358 -4.365 -30.279 -4.355 -31.228 -4.344 C-44.457 -4.042 -55.877 -0.108 -65.688 9.062 C-79.535 23.902 -82.955 39.642 -82.383 59.262 C-81.783 70.831 -77.874 80.097 -72 90 C-71.321 91.327 -70.649 92.658 -70 94 C-74.386 90.978 -76.587 87.669 -78.812 82.938 C-79.124 82.313 -79.435 81.688 -79.756 81.045 C-81.148 78.178 -82 76.226 -82 73 C-82.99 73 -83.98 73 -85 73 C-84.814 72.464 -84.629 71.928 -84.438 71.375 C-83.85 68.955 -83.85 68.955 -84.461 66.344 C-85.067 62.585 -85.191 58.921 -85.188 55.125 C-85.188 54.03 -85.188 54.03 -85.189 52.912 C-85.117 45.188 -84.15 37.628 -83 30 C-82.34 30 -81.68 30 -81 30 C-80.731 29.117 -80.461 28.234 -80.184 27.324 C-75.872 15.215 -65.88 4.593 -55 -2 C-52.219 -3.23 -52.219 -3.23 -49.5 -4.188 C-48.603 -4.511 -47.706 -4.835 -46.781 -5.168 C-31.849 -9.635 -13.502 -7.881 0 0 Z " fill="#615AC2" transform="translate(600,650)"/>
<path d="M0 0 C0.33 2.31 0.66 4.62 1 7 C0.01 7.33 -0.98 7.66 -2 8 C-1.67 23.18 -1.34 38.36 -1 54 C-0.01 53.505 -0.01 53.505 1 53 C1 53.66 1 54.32 1 55 C2.98 54.505 2.98 54.505 5 54 C6.485 58.455 6.485 58.455 8 63 C2.001 64.071 -2.261 64.237 -8 62 C-10 58.875 -10 58.875 -11 56 C-11.33 56 -11.66 56 -12 56 C-11.967 54.079 -11.935 52.159 -11.902 50.238 C-11.925 48.01 -11.925 48.01 -12.514 45.951 C-13.199 41.791 -13.009 37.748 -12.879 33.543 C-12.855 32.64 -12.832 31.737 -12.807 30.807 C-12.731 27.933 -12.647 25.061 -12.562 22.188 C-12.509 20.235 -12.457 18.283 -12.404 16.33 C-12.275 11.553 -12.14 6.777 -12 2 C-7.78 0.499 -4.496 -0.214 0 0 Z M-9 3 C-9.076 10.456 -9.129 17.913 -9.165 25.369 C-9.18 27.906 -9.2 30.442 -9.226 32.979 C-9.263 36.625 -9.28 40.271 -9.293 43.918 C-9.308 45.051 -9.324 46.185 -9.34 47.353 C-9.34 48.414 -9.34 49.474 -9.341 50.567 C-9.347 51.498 -9.354 52.428 -9.361 53.387 C-8.925 56.547 -7.856 58.427 -6 61 C-3.595 62.203 -2.05 62.101 0.625 62.062 C1.442 62.053 2.26 62.044 3.102 62.035 C3.728 62.024 4.355 62.012 5 62 C5 60.35 5 58.7 5 57 C2.69 56.34 0.38 55.68 -2 55 C-2.33 37.84 -2.66 20.68 -3 3 C-4.98 3 -6.96 3 -9 3 Z M-2 3 C-1 7 -1 7 -1 7 Z M-11 9 C-10 11 -10 11 -10 11 Z M-11 17 C-10 19 -10 19 -10 19 Z M-11 25 C-10 28 -10 28 -10 28 Z M-11 33 C-10 36 -10 36 -10 36 Z M-11 41 C-10 43 -10 43 -10 43 Z M-11 50 C-10 52 -10 52 -10 52 Z " fill="#7A6BDA" transform="translate(636,848)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 37.95 1 75.9 1 115 C-7.25 115 -15.5 115 -24 115 C-28 109 -28 109 -28 107 C-29.485 106.505 -29.485 106.505 -31 106 C-31 105.34 -31 104.68 -31 104 C-32.294 101.592 -33.619 99.232 -35 96.875 C-35.382 96.216 -35.763 95.558 -36.156 94.879 C-37.1 93.25 -38.05 91.625 -39 90 C-39.66 90 -40.32 90 -41 90 C-42 87 -42 87 -42 84 C-42.93 82.115 -43.941 80.269 -44.992 78.449 C-45.615 77.358 -46.237 76.267 -46.879 75.143 C-47.538 74.002 -48.196 72.862 -48.875 71.688 C-50.169 69.444 -51.458 67.198 -52.742 64.949 C-53.318 63.952 -53.894 62.956 -54.488 61.928 C-55.997 59.005 -57.047 56.144 -58 53 C-57.01 53 -56.02 53 -55 53 C-54.888 53.723 -54.776 54.446 -54.661 55.191 C-53.962 58.163 -52.93 60.296 -51.43 62.949 C-50.889 63.911 -50.349 64.873 -49.792 65.865 C-48.905 67.417 -48.905 67.417 -48 69 C-47.385 70.085 -46.771 71.17 -46.138 72.288 C-44.142 75.801 -42.135 79.307 -40.125 82.812 C-39.449 83.993 -38.773 85.173 -38.098 86.354 C-37.763 86.938 -37.428 87.523 -37.083 88.126 C-36.411 89.299 -35.74 90.473 -35.069 91.646 C-33.294 94.749 -31.509 97.846 -29.71 100.935 C-29.339 101.576 -28.967 102.217 -28.585 102.877 C-27.87 104.109 -27.153 105.34 -26.431 106.568 C-24 110.775 -24 110.775 -24 113 C-16.08 113 -8.16 113 0 113 C0 75.71 0 38.42 0 0 Z " fill="#3D39AF" transform="translate(732,645)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.856 1.123 1.711 2.246 1.562 3.402 C0.022 14.667 0.022 14.667 0 26 C0.092 27.212 0.183 28.423 0.277 29.672 C1.719 45.348 7.336 58.885 19.574 69.211 C31.522 78.022 44.951 79.549 59.316 77.535 C71.803 74.784 80.074 68.903 88 59 C88 63.292 85.937 64.852 83.129 67.938 C77.227 73.655 69.398 79 61 79 C60.67 79.66 60.34 80.32 60 81 C56.979 81.027 53.958 81.047 50.938 81.062 C50.09 81.071 49.242 81.079 48.369 81.088 C36.129 81.135 25.21 78.01 15.652 70.043 C13.925 68.111 12.457 66.142 11 64 C10.399 63.216 9.799 62.433 9.18 61.625 C-0.815 48.243 -3.823 32.503 -3 16 C-2.946 14.729 -2.892 13.458 -2.836 12.148 C-2.294 2.294 -2.294 2.294 0 0 Z " fill="#645DC3" transform="translate(738,504)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 9.9 1 19.8 1 30 C-1.97 30.33 -4.94 30.66 -8 31 C-8 31.33 -8 31.66 -8 32 C-4.535 32.495 -4.535 32.495 -1 33 C-1 33.33 -1 33.66 -1 34 C-7.93 34 -14.86 34 -22 34 C-22.33 32.02 -22.66 30.04 -23 28 C-21.285 25.98 -21.285 25.98 -19 24 C-17.138 21.169 -15.416 18.276 -13.684 15.363 C-12 13 -12 13 -9 12 C-9 12.99 -9 13.98 -9 15 C-8.043 13.376 -8.043 13.376 -7.066 11.719 C-6.232 10.312 -5.397 8.906 -4.562 7.5 C-4.142 6.785 -3.721 6.069 -3.287 5.332 C-2.219 3.538 -1.113 1.767 0 0 Z M-1 4 C-1.66 5.32 -2.32 6.64 -3 8 C-2.01 7.67 -1.02 7.34 0 7 C-0.33 6.01 -0.66 5.02 -1 4 Z M-3 9 C-3 9.99 -3 10.98 -3 12 C-2.34 12 -1.68 12 -1 12 C-1.33 11.01 -1.66 10.02 -2 9 C-2.33 9 -2.66 9 -3 9 Z M-5 11 C-6.364 13.119 -7.716 15.244 -9.062 17.375 C-9.647 18.28 -9.647 18.28 -10.244 19.203 C-11.225 20.765 -12.119 22.38 -13 24 C-12.67 24.66 -12.34 25.32 -12 26 C-11.34 26 -10.68 26 -10 26 C-9.34 26.66 -8.68 27.32 -8 28 C-4.403 27.677 -4.403 27.677 -1 27 C-0.173 24.52 0.02 23.098 -0.5 20.5 C-1.02 17.902 -0.827 16.48 0 14 C-0.639 13.876 -1.279 13.753 -1.938 13.625 C-2.958 13.316 -2.958 13.316 -4 13 C-4.33 12.34 -4.66 11.68 -5 11 Z M-14 26 C-13 28 -13 28 -13 28 Z " fill="#E2D7FC" transform="translate(840,864)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 37.95 7 75.9 7 115 C3.218 116.261 -0.152 115.94 -4.062 115.688 C-5.162 115.634 -5.162 115.634 -6.283 115.58 C-11.757 115.243 -11.757 115.243 -14 113 C-7.73 113 -1.46 113 5 113 C5 76.04 5 39.08 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#605BBE" transform="translate(545,468)"/>
<path d="M0 0 C6.308 0.364 6.308 0.364 9.312 3.125 C12.499 8.554 13.251 13.806 13.195 20.027 C13.189 21.313 13.182 22.599 13.176 23.924 C13.159 25.262 13.142 26.6 13.125 27.938 C13.115 29.303 13.106 30.668 13.098 32.033 C13.074 35.356 13.041 38.678 13 42 C11.02 42 9.04 42 7 42 C6.975 41.162 6.95 40.325 6.924 39.461 C6.829 36.356 6.73 33.252 6.628 30.147 C6.563 28.141 6.503 26.135 6.443 24.129 C6.401 22.872 6.36 21.615 6.316 20.32 C6.28 19.158 6.243 17.995 6.205 16.797 C6.182 14.046 6.182 14.046 5 12 C4.918 11.361 4.835 10.721 4.75 10.062 C3.665 7.079 1.728 6.464 -1 5 C-1.66 4.34 -2.32 3.68 -3 3 C-0.03 3.495 -0.03 3.495 3 4 C3 3.34 3 2.68 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#EEE7FD" transform="translate(395,868)"/>
<path d="M0 0 C2 1 2 1 3 2 C5 2.041 7 2.043 9 2 C7.855 2.835 7.855 2.835 6.688 3.688 C3.875 6.108 2.564 7.654 1 11 C0.79 13.857 0.714 16.472 0.812 19.312 C0.826 20.053 0.84 20.794 0.854 21.557 C0.889 23.371 0.942 25.186 1 27 C1.99 27.33 2.98 27.66 4 28 C4.45 28.501 4.9 29.003 5.363 29.52 C7.588 31.532 9.231 31.496 12.188 31.688 C13.089 31.753 13.99 31.819 14.918 31.887 C15.605 31.924 16.292 31.961 17 32 C16.67 32.66 16.34 33.32 16 34 C13.668 34.339 11.334 34.672 9 35 C8.34 35.66 7.68 36.32 7 37 C3.125 36.205 -0.594 35.373 -3.488 32.547 C-7.637 25.607 -7.096 16.007 -5.422 8.359 C-4.206 4.866 -2.676 2.489 0 0 Z " fill="#DFD4FB" transform="translate(485,870)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.177 11.126 1.343 22.253 1.495 33.38 C1.566 38.547 1.64 43.713 1.724 48.88 C1.804 53.866 1.874 58.852 1.938 63.838 C1.963 65.74 1.993 67.642 2.026 69.545 C2.072 72.209 2.105 74.873 2.135 77.538 C2.152 78.324 2.168 79.11 2.185 79.92 C2.217 84.048 1.997 87.339 0 91 C-3.033 92.392 -6.138 92.043 -9.422 91.879 C-10.959 91.806 -10.959 91.806 -12.527 91.732 C-13.591 91.676 -14.654 91.62 -15.75 91.562 C-16.829 91.51 -17.908 91.458 -19.02 91.404 C-21.68 91.275 -24.34 91.14 -27 91 C-27.254 90.362 -27.508 89.724 -27.77 89.066 C-28.114 88.24 -28.458 87.414 -28.812 86.562 C-29.149 85.739 -29.485 84.915 -29.832 84.066 C-30.884 81.666 -30.884 81.666 -34 81 C-34.103 80.113 -34.206 79.226 -34.312 78.312 C-35.015 74.929 -36.16 72.881 -38 70 C-38 69.34 -38 68.68 -38 68 C-38.99 67.67 -39.98 67.34 -41 67 C-42.062 64.25 -42.062 64.25 -43 61 C-43.654 59.328 -44.317 57.66 -45 56 C-44.34 56 -43.68 56 -43 56 C-41.484 58.238 -41.484 58.238 -39.75 61.312 C-38.818 62.956 -38.818 62.956 -37.867 64.633 C-35.746 68.458 -33.63 72.286 -31.555 76.137 C-31.042 77.082 -30.529 78.026 -30 79 C-29.716 79.758 -29.433 80.516 -29.141 81.297 C-28.002 83.971 -27.001 85.882 -25 88 C-20.893 89.263 -16.956 89.17 -12.688 89.062 C-11.565 89.07 -10.443 89.077 -9.287 89.084 C-6.522 89.096 -3.764 89.066 -1 89 C-1.005 88.151 -1.01 87.303 -1.016 86.429 C-1.166 57.609 -0.634 28.812 0 0 Z " fill="#7F76CF" transform="translate(716,492)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.691 2.825 0.381 3.65 0.062 4.5 C-2.305 12.298 -1.532 20.645 2 28 C4.599 31.651 7.28 33.549 11.621 34.688 C17.85 35.517 22.424 34.891 28 32 C28.495 30.515 28.495 30.515 29 29 C28.34 29 27.68 29 27 29 C26.67 28.34 26.34 27.68 26 27 C25.67 27.99 25.34 28.98 25 30 C20.791 32.104 16.633 31.204 12.312 29.812 C8.408 27.676 5.784 25.486 4.414 21.141 C3.06 14.14 3.719 8.289 7 2 C9 5 9 5 8.75 7.625 C8.502 8.409 8.255 9.192 8 10 C7.01 10.495 7.01 10.495 6 11 C6.001 18.283 6.001 18.283 9.062 24.562 C9.702 25.037 10.341 25.511 11 26 C11 26.66 11 27.32 11 28 C15.921 28.167 19.503 28.248 24 26 C24 25.34 24 24.68 24 24 C25 21 25 21 27.062 19.812 C27.702 19.544 28.341 19.276 29 19 C29 19.66 29 20.32 29 21 C30.65 20.67 32.3 20.34 34 20 C34.797 22.32 35.114 23.62 34.398 26 C31.978 30.729 29.62 34.714 24.328 36.594 C20.973 37.179 17.643 37.104 14.25 37.062 C13.553 37.058 12.855 37.053 12.137 37.049 C10.424 37.037 8.712 37.019 7 37 C7 36.34 7 35.68 7 35 C5.541 33.56 5.541 33.56 3.688 32.125 C1.031 29.935 -1.011 27.826 -3 25 C-3.66 24.67 -4.32 24.34 -5 24 C-4.67 23.34 -4.34 22.68 -4 22 C-3.34 22.33 -2.68 22.66 -2 23 C-2.33 22.34 -2.66 21.68 -3 21 C-3.509 13.446 -3.82 6.686 0 0 Z M6 6 C7 9 7 9 7 9 Z " fill="#AC9EEA" transform="translate(333,875)"/>
<path d="M0 0 C4.927 0.118 8.077 0.25 12 3.438 C15.239 7.588 15.203 10.745 15.316 15.852 C15.337 16.626 15.358 17.4 15.379 18.197 C15.445 20.673 15.504 23.149 15.562 25.625 C15.606 27.302 15.649 28.979 15.693 30.656 C15.801 34.771 15.902 38.885 16 43 C14.02 43 12.04 43 10 43 C9.98 42.087 9.96 41.174 9.94 40.234 C9.862 36.832 9.775 33.431 9.683 30.03 C9.644 28.561 9.61 27.091 9.578 25.622 C9.532 23.504 9.474 21.387 9.414 19.27 C9.383 17.997 9.351 16.724 9.319 15.413 C9.015 12.16 8.502 9.886 7 7 C4.983 5.656 4.983 5.656 3 5 C4.32 4.34 5.64 3.68 7 3 C4.69 2.34 2.38 1.68 0 1 C0 0.67 0 0.34 0 0 Z " fill="#EDE5FE" transform="translate(453,867)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.452 6.326 0.655 10.644 -3 16 C-7.027 20.422 -11.387 24.496 -15.745 28.587 C-17.251 30.009 -18.741 31.445 -20.23 32.883 C-21.121 33.722 -22.012 34.561 -22.93 35.426 C-25.545 38.247 -25.545 38.247 -26 44 C-14.78 44 -3.56 44 8 44 C8 42.35 8 40.7 8 39 C-0.58 39 -9.16 39 -18 39 C-18 37 -18 37 -16.355 35.309 C-15.282 34.382 -15.282 34.382 -14.188 33.438 C-13.126 32.507 -13.126 32.507 -12.043 31.559 C-10 30 -10 30 -7 29 C-6.67 29.99 -6.34 30.98 -6 32 C-6.928 32.155 -6.928 32.155 -7.875 32.312 C-10.365 32.875 -10.365 32.875 -12 36 C-11.374 35.995 -10.749 35.99 -10.104 35.984 C-7.278 35.963 -4.451 35.95 -1.625 35.938 C-0.64 35.929 0.345 35.921 1.359 35.912 C2.771 35.907 2.771 35.907 4.211 35.902 C5.515 35.894 5.515 35.894 6.845 35.886 C9 36 9 36 11 37 C10.67 39.64 10.34 42.28 10 45 C4.258 46.748 -1.872 46.184 -7.812 46.188 C-9.701 46.206 -9.701 46.206 -11.627 46.225 C-17.784 46.234 -23.209 46.19 -29 44 C-27.79 38.48 -26.348 35.754 -22.133 31.785 C-21.419 31.096 -21.419 31.096 -20.69 30.393 C-19.18 28.937 -17.655 27.498 -16.125 26.062 C-4.421 15.075 -4.421 15.075 0 0 Z " fill="#7E70D9" transform="translate(804,866)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.32 2 3.64 2 5 2 C4.967 3.986 4.935 5.971 4.902 7.957 C4.917 10.203 4.917 10.203 6 13 C5.34 13 4.68 13 4 13 C4.023 13.739 4.046 14.477 4.07 15.238 C4.256 25.667 3.941 38.617 -3.629 46.75 C-4.081 47.163 -4.534 47.575 -5 48 C-4.818 47.478 -4.636 46.956 -4.449 46.418 C-0.841 35.853 1.473 25.986 2.188 14.812 C2.456 10.914 2.724 7.016 3 3 C-11.19 3 -25.38 3 -40 3 C-40 8.28 -40 13.56 -40 19 C-32.08 19 -24.16 19 -16 19 C-17.75 30.75 -17.75 30.75 -20 33 C-19.858 31.562 -19.711 30.125 -19.562 28.688 C-19.481 27.887 -19.4 27.086 -19.316 26.262 C-19 24 -19 24 -18 21 C-18.648 21.171 -19.297 21.343 -19.965 21.52 C-23.901 22.143 -27.598 21.936 -31.562 21.688 C-32.317 21.652 -33.072 21.617 -33.85 21.58 C-37.65 21.356 -40.762 21.104 -44 19 C-43.34 19 -42.68 19 -42 19 C-42.165 18.319 -42.33 17.639 -42.5 16.938 C-43.483 11.163 -43.198 7.496 -41 2 C-27.47 2 -13.94 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#4A45B9" transform="translate(832,515)"/>
<path d="M0 0 C1.794 2.692 2.333 3.987 3 7 C3.892 9.625 4.802 12.24 5.73 14.852 C6.003 15.624 6.276 16.397 6.557 17.194 C7.432 19.671 8.31 22.148 9.188 24.625 C11.185 30.262 13.174 35.901 15.143 41.547 C15.725 43.214 16.311 44.879 16.9 46.542 C17.773 49.012 18.632 51.486 19.488 53.961 C19.757 54.711 20.025 55.461 20.301 56.234 C21.568 59.944 22.183 62.261 21 66 C20.798 65.43 20.597 64.859 20.389 64.272 C19.475 61.699 18.55 59.131 17.625 56.562 C17.308 55.665 16.991 54.767 16.664 53.842 C16.2 52.561 16.2 52.561 15.727 51.254 C15.302 50.068 15.302 50.068 14.87 48.858 C14.148 46.815 14.148 46.815 12 46 C11.81 44.188 11.622 42.375 11.438 40.562 C10.517 35.175 8.346 30.102 6.438 25 C6.013 23.844 5.588 22.687 5.15 21.496 C4.108 18.661 3.058 15.829 2 13 C2.002 13.868 2.004 14.736 2.007 15.631 C2.027 23.811 2.042 31.992 2.052 40.173 C2.057 44.379 2.064 48.585 2.075 52.791 C2.086 56.85 2.092 60.909 2.095 64.968 C2.097 66.516 2.1 68.065 2.106 69.613 C2.113 71.782 2.114 73.951 2.114 76.12 C2.116 77.355 2.118 78.589 2.12 79.862 C2 83 2 83 1 87 C-3.95 87 -8.9 87 -14 87 C-14 86.67 -14 86.34 -14 86 C-9.38 86 -4.76 86 0 86 C0 57.62 0 29.24 0 0 Z " fill="#4A45B4" transform="translate(454,495)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.835 17.648 -1.603 33.54 -12.75 47.812 C-20.54 56.162 -32.501 64.586 -44.238 65.098 C-44.923 65.086 -45.607 65.074 -46.312 65.062 C-48.138 65.032 -48.138 65.032 -50 65 C-50.33 65.66 -50.66 66.32 -51 67 C-60.397 65.964 -69.726 64.883 -79 63 C-79 62.34 -79 61.68 -79 61 C-79.869 60.762 -80.738 60.523 -81.633 60.277 C-85.36 58.864 -87.808 57.093 -90.875 54.562 C-91.842 53.78 -92.809 52.998 -93.805 52.191 C-94.529 51.468 -95.254 50.745 -96 50 C-96 49.01 -96 48.02 -96 47 C-95.585 47.424 -95.17 47.848 -94.742 48.285 C-83.875 58.63 -70.527 63.419 -55.688 63.312 C-54.859 63.307 -54.03 63.301 -53.176 63.295 C-39.066 63.018 -27.654 58.652 -17.25 49 C-4.244 35.325 -1.69 18.072 0 0 Z " fill="#554EBC" transform="translate(627,697)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.65 2 4.3 2 6 2 C6.816 4.562 7.628 7.124 8.438 9.688 C8.668 10.409 8.898 11.13 9.135 11.873 C10.297 15.562 11.344 19.18 12 23 C11.34 23 10.68 23 10 23 C8.994 20.231 7.995 17.46 7 14.688 C6.714 13.902 6.428 13.116 6.133 12.307 C5.862 11.549 5.591 10.792 5.312 10.012 C5.061 9.315 4.81 8.619 4.551 7.901 C4 6 4 6 4 3 C-6.23 3 -16.46 3 -27 3 C-26.979 7.826 -26.959 12.652 -26.938 17.625 C-26.901 36.191 -27.187 54.75 -27.5 73.312 C-27.512 74.022 -27.524 74.732 -27.536 75.463 C-27.686 84.309 -27.84 93.154 -28 102 C-28.33 102 -28.66 102 -29 102 C-29.779 88.252 -30.161 74.551 -30.13 60.782 C-30.125 57.814 -30.13 54.847 -30.137 51.879 C-30.136 49.979 -30.135 48.079 -30.133 46.18 C-30.135 45.299 -30.137 44.418 -30.139 43.51 C-30.136 42.697 -30.133 41.884 -30.129 41.046 C-30.129 40.335 -30.128 39.623 -30.127 38.89 C-30 37 -30 37 -29 34 C-29.66 33.67 -30.32 33.34 -31 33 C-30.34 33 -29.68 33 -29 33 C-29.33 22.44 -29.66 11.88 -30 1 C-26.349 0.979 -22.699 0.959 -18.938 0.938 C-17.232 0.924 -17.232 0.924 -15.492 0.91 C-10.215 0.896 -5.197 1.064 0 2 C0 1.34 0 0.68 0 0 Z " fill="#4F4CB8" transform="translate(462,466)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.251 1.039 -1.251 1.039 -2.527 1.078 C-11.667 1.539 -19.587 2.992 -26.59 9.332 C-35.707 20.116 -36.9 32.407 -36 46 C-34.717 54.537 -31.527 62.267 -24.75 67.855 C-17.255 73.045 -11.057 73.651 -2 73 C4.694 71.51 9.361 68.303 13.312 62.75 C13.869 61.842 14.426 60.935 15 60 C15.33 60.99 15.66 61.98 16 63 C11.887 69.789 5.933 73.52 -1.5 75.75 C-9.996 76.6 -17.554 76.143 -24.719 71.078 C-29.863 66.69 -33.809 62.453 -36 56 C-36.352 54.984 -36.704 53.968 -37.066 52.922 C-40.179 41.708 -39.522 27.235 -34.605 16.699 C-26.131 2.451 -15.934 -0.956 0 0 Z " fill="#4A46B5" transform="translate(796,488)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.698 2.879 0.397 3.758 0.086 4.664 C-2.201 11.69 -2.466 20.85 0.625 27.688 C2.847 31.229 5.128 33.279 9 35 C13.669 35.652 17.587 35.676 21.875 33.625 C22.576 33.089 23.277 32.553 24 32 C25.324 31.316 26.657 30.646 28 30 C28.384 34.804 28.48 38.09 26.188 42.438 C23.791 45.244 22.199 46.84 18.477 47.336 C12.476 47.418 9.512 47.254 5 43 C1.812 42.203 1.812 42.203 -1 42 C-0.34 43.98 0.32 45.96 1 48 C-1.392 46.128 -2.008 44.974 -2.875 42 C-3 39 -3 39 -1.688 37.125 C0 36 0 36 2 36 C2 36.66 2 37.32 2 38 C2.66 38 3.32 38 4 38 C3.34 37.01 2.68 36.02 2 35 C1.01 35 0.02 35 -1 35 C-0.814 34.423 -0.629 33.845 -0.438 33.25 C0.047 30.758 -0.105 29.36 -1 27 C-1.66 26.67 -2.32 26.34 -3 26 C-5.215 22.252 -6.609 19.388 -6 15 C-5.34 14.34 -4.68 13.68 -4 13 C-4.66 12.67 -5.32 12.34 -6 12 C-5.34 12 -4.68 12 -4 12 C-3.963 11.301 -3.925 10.603 -3.887 9.883 C-3.788 8.518 -3.788 8.518 -3.688 7.125 C-3.6 5.768 -3.6 5.768 -3.512 4.383 C-3 2 -3 2 0 0 Z M26 32 C27 34 27 34 27 34 Z M25 34 C24.113 34.454 23.226 34.908 22.312 35.375 C17.991 37.495 14.141 38.459 9.375 37.438 C6.352 36.881 4.719 37.679 2 39 C2 39.66 2 40.32 2 41 C3.65 41 5.3 41 7 41 C7.289 41.639 7.577 42.279 7.875 42.938 C8.828 45.152 8.828 45.152 11 46 C15.446 46.533 18.941 46.563 22.938 44.438 C25.472 41.442 26.195 38.806 27 35 C26.34 34.67 25.68 34.34 25 34 Z " fill="#9787E4" transform="translate(481,873)"/>
<path d="M0 0 C1.915 -0.108 3.833 -0.186 5.75 -0.25 C7.351 -0.32 7.351 -0.32 8.984 -0.391 C12.675 0.087 13.644 1.219 16 4 C16.66 4.33 17.32 4.66 18 5 C18 5.66 18 6.32 18 7 C17.432 6.783 16.863 6.567 16.277 6.344 C8.985 3.714 3.083 2.975 -4.207 6.035 C-7.096 7.59 -8.387 9.146 -10 12 C-10.869 14.972 -11.494 17.946 -12 21 C-10.35 21 -8.7 21 -7 21 C-6.753 19.928 -6.505 18.855 -6.25 17.75 C-4.991 13.972 -4.032 12.464 -1 10 C2.457 8.271 6.22 8.51 10 9 C12.87 10.794 14.491 11.983 16 15 C17.06 22.161 15.225 27.238 11 33 C8.789 35.481 6.474 37.783 4 40 C4 35.604 6.537 34.009 9.469 30.938 C11.31 29.019 11.31 29.019 11 26 C13 23 13 23 14 22 C13.713 19.661 13.381 17.326 13 15 C7.495 13.05 3.743 12.735 -2 14 C-3.96 16.941 -4.378 18.623 -5 22 C-6 23 -6 23 -9.5 23.125 C-13 23 -13 23 -14 22 C-13.713 19.661 -13.381 17.326 -13 15 C-13.66 14.67 -14.32 14.34 -15 14 C-14.01 14 -13.02 14 -12 14 C-11.918 13.443 -11.835 12.886 -11.75 12.312 C-10.761 9.263 -9.152 7.348 -7 5 C-6.34 5 -5.68 5 -5 5 C-4.67 4.34 -4.34 3.68 -4 3 C-1.938 1.875 -1.938 1.875 0 1 C0 0.67 0 0.34 0 0 Z M6 1 C10 2 10 2 10 2 Z M2 10 C2 10.66 2 11.32 2 12 C2.99 11.67 3.98 11.34 5 11 C4.01 10.67 3.02 10.34 2 10 Z " fill="#8E80E0" transform="translate(700,850)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 13.86 5 27.72 5 42 C3.35 42 1.7 42 0 42 C0 28.14 0 14.28 0 0 Z " fill="#EFE8FE" transform="translate(420,868)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 35.31 1 70.62 1 107 C7.27 107 13.54 107 20 107 C20 89.18 20 71.36 20 53 C20.33 53 20.66 53 21 53 C21.33 71.48 21.66 89.96 22 109 C14.74 109 7.48 109 0 109 C0 73.03 0 37.06 0 0 Z " fill="#FCF6FE" transform="translate(580,472)"/>
<path d="M0 0 C1.675 0.286 3.344 0.618 5 1 C4.781 4.835 4.06 5.942 1.25 8.688 C-4.096 12.491 -8.411 13.789 -15 13 C-19.911 11.779 -23.867 10.135 -27 6 C-27.75 3.75 -27.75 3.75 -28 2 C-25.312 1.75 -25.312 1.75 -22 2 C-20.917 2.99 -20.917 2.99 -19.812 4 C-16.028 6.691 -13.16 6.616 -8.602 6.469 C-4.844 5.792 -3.209 4.019 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#ECE2FD" transform="translate(508,913)"/>
<path d="M0 0 C0 12.21 0 24.42 0 37 C-0.66 36.67 -1.32 36.34 -2 36 C-2 35.01 -2 34.02 -2 33 C-2.33 34.32 -2.66 35.64 -3 37 C-3.66 37 -4.32 37 -5 37 C-5.33 27.76 -5.66 18.52 -6 9 C-6.99 9 -7.98 9 -9 9 C-8.088 2.436 -6.949 0 0 0 Z " fill="#E9DFFE" transform="translate(846,856)"/>
<path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.062 C3.933 3.033 3.933 3.033 4.438 6.062 C4.768 6.062 5.097 6.062 5.438 6.062 C6.688 13.688 6.688 13.688 4.438 17.062 C4.271 20.188 4.271 20.188 4.438 23.062 C4.768 23.062 5.097 23.062 5.438 23.062 C5.933 26.528 5.933 26.528 6.438 30.062 C5.447 30.062 4.457 30.062 3.438 30.062 C3.438 31.383 3.438 32.702 3.438 34.062 C4.097 34.062 4.758 34.062 5.438 34.062 C5.107 35.053 4.778 36.043 4.438 37.062 C4.095 38.394 3.76 39.727 3.438 41.062 C4.097 41.062 4.758 41.062 5.438 41.062 C5.438 42.712 5.438 44.362 5.438 46.062 C4.778 46.062 4.117 46.062 3.438 46.062 C2.984 47.279 2.53 48.496 2.062 49.75 C0.893 52.871 -0.308 55.975 -1.562 59.062 C-2.553 59.062 -3.543 59.062 -4.562 59.062 C-4.233 60.053 -3.902 61.043 -3.562 62.062 C-4.553 62.062 -5.543 62.062 -6.562 62.062 C-6.562 61.403 -6.562 60.742 -6.562 60.062 C-7.192 60.218 -7.821 60.374 -8.469 60.535 C-14.619 61.934 -20.52 63.196 -26.391 60.098 C-27.828 59.154 -29.203 58.116 -30.562 57.062 C-30.233 56.403 -29.902 55.742 -29.562 55.062 C-28.655 55.537 -27.748 56.011 -26.812 56.5 C-21.193 59.202 -15.494 60.309 -9.406 58.379 C-4.621 56.227 -1.89 53.905 0.438 49.062 C1.359 43.288 1.634 37.59 1.754 31.75 C1.775 30.896 1.795 30.041 1.817 29.161 C1.882 26.462 1.941 23.762 2 21.062 C2.043 19.22 2.087 17.378 2.131 15.535 C2.237 11.044 2.339 6.553 2.438 2.062 C0.457 2.062 -1.522 2.062 -3.562 2.062 C-3.562 4.043 -3.562 6.023 -3.562 8.062 C-4.223 8.062 -4.882 8.062 -5.562 8.062 C-6.952 6.101 -8.289 4.101 -9.562 2.062 C-8.356 1.846 -8.356 1.846 -7.125 1.625 C-3.542 0.838 -4.05 0.071 0 0 Z " fill="#9987E9" transform="translate(512.5625,865.9375)"/>
<path d="M0 0 C2.308 2.957 4.217 6.024 6.125 9.25 C13.298 20.496 23.858 27.585 36.883 30.625 C40.506 31.267 44.077 31.34 47.75 31.312 C48.536 31.307 49.323 31.301 50.133 31.295 C63.384 31.022 75.148 27.098 84.688 17.625 C85.805 16.429 86.915 15.225 88 14 C88 17 88 17 86.062 19.25 C73.527 31 73.527 31 66 31 C65.67 31.66 65.34 32.32 65 33 C49.872 35.991 33.442 34.793 20.062 26.75 C15.775 23.798 12.158 20.444 8.656 16.602 C6.985 14.755 6.985 14.755 4 14 C3.897 13.299 3.794 12.597 3.688 11.875 C3.085 8.874 3.085 8.874 1.438 5.875 C0 3 0 3 0 0 Z " fill="#5E58C1" transform="translate(190,551)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 2.176 1.01 2.176 0 3.375 C-2.202 5.715 -2.202 5.715 -2 8 C5.26 8.33 12.52 8.66 20 9 C19.34 9.33 18.68 9.66 18 10 C18 11.65 18 13.3 18 15 C18.99 15.33 19.98 15.66 21 16 C17.133 18.574 13.058 18.306 8.586 18.301 C7.775 18.305 6.964 18.309 6.129 18.314 C4.419 18.319 2.71 18.32 1 18.316 C-1.623 18.313 -4.245 18.336 -6.867 18.361 C-8.529 18.364 -10.19 18.364 -11.852 18.363 C-12.638 18.372 -13.424 18.382 -14.235 18.391 C-16.44 18.372 -16.44 18.372 -20 18 C-20.66 17.01 -21.32 16.02 -22 15 C-21.312 11.875 -21.312 11.875 -20 9 C-19.01 8.67 -18.02 8.34 -17 8 C-17 8.66 -17 9.32 -17 10 C-17.66 10 -18.32 10 -19 10 C-19 11.98 -19 13.96 -19 16 C-1.675 15.505 -1.675 15.505 16 15 C16 13.68 16 12.36 16 11 C7.42 11 -1.16 11 -10 11 C-10 9 -10 9 -8.32 7.273 C-7.596 6.647 -6.871 6.02 -6.125 5.375 C-5.046 4.428 -5.046 4.428 -3.945 3.461 C-2 2 -2 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9383E2" transform="translate(707,894)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C0.061 7.595 -2.309 14.914 -5.062 22.25 C-8.411 31.006 -8.411 31.006 -11 40 C-10.67 40.66 -10.34 41.32 -10 42 C-11.333 44 -12.667 46 -14 48 C-14.519 50.272 -14.882 52.542 -15.242 54.844 C-16.29 57.825 -17.156 58.587 -20 60 C-22.928 60.351 -25.806 60.283 -28.75 60.188 C-29.543 60.174 -30.336 60.16 -31.152 60.146 C-33.102 60.111 -35.051 60.057 -37 60 C-44.23 44.146 -44.23 44.146 -45 39 C-44.34 38.01 -43.68 37.02 -43 36 C-41.99 38.913 -40.994 41.831 -40 44.75 C-39.714 45.575 -39.428 46.4 -39.133 47.25 C-38.727 48.449 -38.727 48.449 -38.312 49.672 C-38.061 50.405 -37.81 51.138 -37.551 51.894 C-37 54 -37 54 -37 58 C-31.06 58 -25.12 58 -19 58 C-18.67 55.69 -18.34 53.38 -18 51 C-17.403 48.921 -17.403 48.921 -16.723 47.18 C-16.461 46.493 -16.2 45.807 -15.93 45.1 C-15.644 44.365 -15.358 43.631 -15.062 42.875 C-11.747 34.14 -8.657 25.336 -5.625 16.5 C-5.402 15.852 -5.18 15.204 -4.95 14.537 C-2.453 7.276 -2.453 7.276 0 0 Z " fill="#675FC3" transform="translate(520,523)"/>
<path d="M0 0 C6.152 -0.098 6.152 -0.098 8 0 C9.888 1.888 9.265 5.147 9.352 7.668 C9.375 8.34 9.398 9.013 9.422 9.706 C9.469 11.134 9.515 12.562 9.558 13.99 C9.624 16.161 9.702 18.332 9.781 20.502 C9.826 21.891 9.871 23.279 9.914 24.668 C9.955 25.929 9.996 27.19 10.038 28.489 C10.002 31.782 9.641 34.776 9 38 C9.99 38.495 9.99 38.495 11 39 C10.34 39 9.68 39 9 39 C9 40.32 9 41.64 9 43 C9.99 43.495 9.99 43.495 11 44 C10.34 44.66 9.68 45.32 9 46 C6.271 45.967 3.703 45.436 1 45 C0.67 44.01 0.34 43.02 0 42 C-0.804 42.66 -1.609 43.32 -2.438 44 C-6.463 46.664 -10.313 46.84 -15 46 C-18.85 44.004 -21.947 41.878 -24 38 C-24.188 34.75 -24.188 34.75 -24 32 C-23.67 32 -23.34 32 -23 32 C-22.773 33.072 -22.546 34.145 -22.312 35.25 C-20.79 39.599 -20.093 40.912 -16 43 C-12.224 43.602 -8.768 43.686 -5 43 C-1.933 40.687 -1.933 40.687 0 38 C0.66 38 1.32 38 2 38 C2 39.98 2 41.96 2 44 C3.98 44 5.96 44 8 44 C8 30.14 8 16.28 8 2 C6.02 2 4.04 2 2 2 C2 11.57 2 21.14 2 31 C1.67 31 1.34 31 1 31 C0.975 30.44 0.95 29.88 0.924 29.303 C0.808 26.765 0.685 24.226 0.562 21.688 C0.523 20.806 0.484 19.925 0.443 19.018 C0.401 18.17 0.36 17.322 0.316 16.449 C0.28 15.669 0.243 14.889 0.205 14.085 C0.059 11.807 0.059 11.807 -1 9 C-0.7 7.327 -0.364 5.66 0 4 C0 2.68 0 1.36 0 0 Z " fill="#7F71D9" transform="translate(272,866)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C26 0.33 26 0.66 26 1 C25.085 1.061 24.17 1.121 23.227 1.184 C22.038 1.267 20.85 1.351 19.625 1.438 C18.442 1.519 17.258 1.6 16.039 1.684 C13.085 1.811 13.085 1.811 11 3 C11 2.34 11 1.68 11 1 C7.7 1 4.4 1 1 1 C2.485 1.99 2.485 1.99 4 3 C4 3.66 4 4.32 4 5 C3.34 5 2.68 5 2 5 C1.973 6.583 1.954 8.167 1.938 9.75 C1.926 10.632 1.914 11.513 1.902 12.422 C1.994 14.831 2.116 16.757 3 19 C5.659 19.886 7.481 20.18 10.23 20.316 C11.033 20.358 11.835 20.4 12.662 20.443 C13.495 20.483 14.329 20.522 15.188 20.562 C16.032 20.606 16.877 20.649 17.748 20.693 C19.832 20.799 21.916 20.9 24 21 C24 23.64 24 26.28 24 29 C16.41 29 8.82 29 1 29 C1.33 32.63 1.66 36.26 2 40 C2.33 40 2.66 40 3 40 C3.33 42.31 3.66 44.62 4 47 C2.68 47 1.36 47 0 47 C0 40.4 0 33.8 0 27 C6.93 27 13.86 27 21 27 C21 25.35 21 23.7 21 22 C14.07 22 7.14 22 0 22 C0 14.74 0 7.48 0 0 Z " fill="#7F70DB" transform="translate(553,859)"/>
<path d="M0 0 C2.658 1.329 2.681 2.386 3.621 5.164 C3.915 6.032 4.21 6.899 4.513 7.793 C4.818 8.728 5.123 9.662 5.438 10.625 C5.75 11.577 6.063 12.529 6.385 13.51 C10.119 25.14 12.495 36.766 13 49 C13.053 50.176 13.106 51.351 13.16 52.562 C13.622 72.471 10.656 95.326 1 113 C-0.29 109.13 0.592 108.071 2.188 104.375 C4.322 99.059 5.947 93.827 7.25 88.25 C7.413 87.566 7.575 86.882 7.743 86.177 C9.974 76.304 10.225 66.538 10.25 56.438 C10.252 55.775 10.255 55.113 10.257 54.431 C10.271 44.284 9.634 34.815 7 25 C6.34 25 5.68 25 5 25 C5.214 24.33 5.428 23.659 5.648 22.969 C6.11 19.073 4.962 16.093 3.625 12.5 C2.136 8.369 0.719 4.349 0 0 Z M11 55 C12 57 12 57 12 57 Z " fill="#696EE1" transform="translate(630,199)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 16.5 3 33 3 50 C5.97 50.66 8.94 51.32 12 52 C12 52.99 12 53.98 12 55 C8.7 55 5.4 55 2 55 C-0.125 50.75 -0.276 47.8 -0.23 43.207 C-0.23 42.46 -0.23 41.714 -0.229 40.945 C-0.226 39.372 -0.218 37.799 -0.206 36.227 C-0.187 33.811 -0.185 31.395 -0.186 28.979 C-0.181 27.451 -0.175 25.923 -0.168 24.395 C-0.166 23.306 -0.166 23.306 -0.165 22.195 C-0.116 17.116 -0.116 17.116 1 16 C1.072 14.481 1.084 12.958 1.062 11.438 C1.053 10.611 1.044 9.785 1.035 8.934 C1.018 7.976 1.018 7.976 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#E4D9FE" transform="translate(649,853)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C-0.52 2.273 -3.041 3.544 -5.562 4.812 C-6.267 5.169 -6.971 5.525 -7.697 5.893 C-15.613 9.869 -23.166 12.692 -31.812 14.625 C-33.694 15.053 -35.576 15.482 -37.457 15.914 C-38.282 16.099 -39.107 16.284 -39.956 16.475 C-42.046 16.935 -42.046 16.935 -44 18 C-46 18 -48 18 -50 18 C-50.99 18.33 -51.98 18.66 -53 19 C-56.391 19.234 -59.789 19.19 -63.188 19.188 C-64.166 19.188 -65.145 19.189 -66.153 19.189 C-74.565 19.129 -82.697 18.332 -91 17 C-91 17.33 -91 17.66 -91 18 C-92.666 18.043 -94.334 18.041 -96 18 C-97 17 -98 16 -99 15 C-100.984 14.286 -102.983 13.614 -105 13 C-101.16 11.581 -98.28 12.431 -94.375 13.375 C-64.908 20.46 -31.376 15.881 -4.491 2.097 C-3.019 1.347 -1.512 0.666 0 0 Z " fill="#696CDF" transform="translate(577,381)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 2.98 2 4.96 2 7 C2.99 7 3.98 7 5 7 C5 4.69 5 2.38 5 0 C5.33 0 5.66 0 6 0 C6.57 10.117 5.274 16.746 -1.5 24.5 C-5.518 28.839 -5.518 28.839 -9 30 C-9.99 29.67 -10.98 29.34 -12 29 C-10.715 26.133 -9.429 24.024 -7 22 C-6.34 22 -5.68 22 -5 22 C-4.897 20.969 -4.794 19.938 -4.688 18.875 C-4 15 -4 15 -2.5 12.125 C-0.573 8.111 -0.284 4.4 0 0 Z " fill="#D5CAF6" transform="translate(805,865)"/>
<path d="M0 0 C3.741 3.282 5.801 7.055 8 11.438 C12.117 19.438 16.516 27.251 21.014 35.043 C21.503 35.891 21.992 36.739 22.496 37.613 C22.931 38.364 23.366 39.115 23.814 39.888 C25 42 25 42 25.969 44.149 C26.999 46.329 26.999 46.329 30 48 C30 48.66 30 49.32 30 50 C30.961 51.854 31.968 53.684 33 55.5 C36 60.778 36 60.778 36 63 C36.99 63.495 36.99 63.495 38 64 C38.99 64.99 38.99 64.99 40 66 C39.814 65.113 39.629 64.226 39.438 63.312 C39.02 60.148 39.192 58.052 40 55 C40.66 55 41.32 55 42 55 C42 61.6 42 68.2 42 75 C39.344 73.672 38.942 72.319 37.562 69.688 C37.034 68.695 36.505 67.702 35.961 66.68 C35.656 66.106 35.352 65.532 35.038 64.94 C32.955 61.046 30.817 57.181 28.688 53.312 C28.211 52.446 27.734 51.58 27.243 50.687 C20.807 39.005 14.305 27.375 7.449 15.933 C0 3.414 0 3.414 0 0 Z M40 66 C39.67 66.66 39.34 67.32 39 68 C39.66 68.66 40.32 69.32 41 70 C40.67 68.68 40.34 67.36 40 66 Z " fill="#504BB7" transform="translate(653,473)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-2.191 4.441 -5.202 5.25 -8.625 5.875 C-12.111 6.626 -12.111 6.626 -14.938 8.75 C-16.209 11.442 -17.157 14.146 -18 17 C-19.98 17 -21.96 17 -24 17 C-23.067 10.732 -21.851 6.429 -17 2 C-11.344 -1.372 -6.32 -1.214 0 0 Z " fill="#ECE2FC" transform="translate(801,854)"/>
<path d="M0 0 C1.77 -0.054 3.541 -0.093 5.312 -0.125 C6.299 -0.148 7.285 -0.171 8.301 -0.195 C11 0 11 0 14 2 C15.733 2.928 15.733 2.928 17.5 3.875 C20.44 5.534 22.171 7.112 24 10 C24.66 10.66 25.32 11.32 26 12 C26 13.32 26 14.64 26 16 C25.34 16 24.68 16 24 16 C24 24.25 24 32.5 24 41 C24.66 41.33 25.32 41.66 26 42 C26 43.32 26 44.64 26 46 C26.66 46.33 27.32 46.66 28 47 C23.477 48.508 18.749 48 14 48 C14 45.69 14 43.38 14 41 C14.99 40.505 14.99 40.505 16 40 C16.33 41.65 16.66 43.3 17 45 C18.65 45.33 20.3 45.66 22 46 C21.956 41.106 21.886 36.214 21.792 31.321 C21.764 29.658 21.743 27.994 21.729 26.331 C21.708 23.935 21.661 21.54 21.609 19.145 C21.608 18.405 21.607 17.665 21.606 16.903 C21.467 11.995 20.48 9.46 17 6 C14.085 4.062 11.96 3.789 8.5 3.938 C5.44 4.058 2.946 3.864 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9685E5" transform="translate(300,864)"/>
<path d="M0 0 C16.364 -0.106 32.666 -0.152 49 1 C49 1.66 49 2.32 49 3 C49.536 3.014 50.072 3.028 50.625 3.043 C63.017 3.797 74.979 8.518 86 14 C86.661 14.324 87.323 14.647 88.004 14.98 C89.342 15.642 90.672 16.318 92 17 C89 18 89 18 86.785 16.996 C85.887 16.482 84.988 15.967 84.062 15.438 C59.359 2.67 27.852 -0.354 0.707 4.562 C-1.915 4.986 -4.349 5.078 -7 5 C-6.34 4.01 -5.68 3.02 -5 2 C-2.375 1.812 -2.375 1.812 0 2 C0 1.34 0 0.68 0 0 Z M1 1 C1 1.33 1 1.66 1 2 C3.31 2 5.62 2 8 2 C8 1.67 8 1.34 8 1 C5.69 1 3.38 1 1 1 Z " fill="#6E73E2" transform="translate(484,112)"/>
<path d="M0 0 C7.598 0.584 13.731 4.273 18.812 9.812 C27.785 20.862 29.151 33.229 28 47 C26.448 56.303 22.074 62.842 15 69 C12.296 70.848 10.12 71.87 7 73 C6.01 72.67 5.02 72.34 4 72 C4.724 71.648 5.449 71.296 6.195 70.934 C15.952 65.951 20.814 61.318 24.25 50.824 C26.307 43.08 26.644 34.876 25 27 C24.34 26.67 23.68 26.34 23 26 C22.661 23.668 22.328 21.334 22 19 C21.34 18.34 20.68 17.68 20 17 C19.34 16.34 18.68 15.68 18 15 C18 14.34 18 13.68 18 13 C14.983 8.345 11.625 6.432 6.75 4.188 C6.1 3.876 5.451 3.565 4.781 3.244 C3.193 2.485 1.597 1.741 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5851BB" transform="translate(577,666)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.098 0.773 1.196 1.547 1.297 2.344 C2.26 9.049 3.679 14.107 7 20 C7.33 20.99 7.66 21.98 8 23 C8.66 23 9.32 23 10 23 C10.639 23.742 11.279 24.485 11.938 25.25 C16.804 30.274 24.234 32.201 31.062 32.438 C40.434 31.963 48.563 29.015 55 22 C58.557 16.317 60.965 10.37 63 4 C64.585 8.544 63.176 11.702 61.496 16.043 C57.475 23.991 50.839 30.689 42.312 33.57 C34.037 35.108 23.436 35.479 15.918 31.32 C8.105 25.942 2.541 20.256 0.219 10.828 C-1.283 2.567 -1.283 2.567 0 0 Z " fill="#5C56BF" transform="translate(328,529)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 16.5 3 33 3 50 C2.34 50 1.68 50 1 50 C0.67 50.99 0.34 51.98 0 53 C0 35.51 0 18.02 0 0 Z " fill="#E1D6FD" transform="translate(171,855)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.33 17.16 6.66 34.32 7 52 C9.31 52.66 11.62 53.32 14 54 C14 55.65 14 57.3 14 59 C5.891 59.468 5.891 59.468 2.5 57.688 C-0.211 54.637 -0.123 51.686 -0.114 47.794 C-0.113 46.203 -0.113 46.203 -0.113 44.579 C-0.108 43.436 -0.103 42.292 -0.098 41.113 C-0.096 39.941 -0.095 38.769 -0.093 37.561 C-0.088 33.812 -0.075 30.062 -0.062 26.312 C-0.057 23.773 -0.053 21.233 -0.049 18.693 C-0.038 12.462 -0.021 6.231 0 0 Z M1 1 C0.924 8.332 0.871 15.664 0.835 22.997 C0.82 25.492 0.8 27.987 0.774 30.481 C0.737 34.066 0.72 37.65 0.707 41.234 C0.692 42.351 0.676 43.468 0.66 44.619 C0.66 45.659 0.66 46.7 0.659 47.772 C0.653 48.687 0.646 49.602 0.639 50.545 C1.071 53.484 1.973 54.858 4 57 C6.049 57.481 6.049 57.481 8.188 57.25 C9.446 57.168 10.704 57.085 12 57 C11.01 56.34 10.02 55.68 9 55 C8.01 54.34 7.02 53.68 6 53 C5.601 50.785 5.601 50.785 5.568 48 C5.548 46.959 5.528 45.918 5.508 44.845 C5.501 43.718 5.495 42.591 5.488 41.43 C5.47 40.278 5.452 39.127 5.434 37.941 C5.38 34.252 5.346 30.564 5.312 26.875 C5.279 24.379 5.245 21.883 5.209 19.387 C5.124 13.258 5.056 7.129 5 1 C3.68 1 2.36 1 1 1 Z " fill="#FAF2FE" transform="translate(627,851)"/>
<path d="M0 0 C1 3 1 3 0.109 4.902 C-1.252 6.941 -2.614 8.978 -4 11 C-4.495 11.804 -4.99 12.609 -5.5 13.438 C-8.505 16.568 -12.706 16.117 -16.812 16.312 C-21.969 16.259 -25.796 14.984 -30 12 C-32 9.75 -32 9.75 -33 8 C-32.67 7.34 -32.34 6.68 -32 6 C-31.227 7.145 -31.227 7.145 -30.438 8.312 C-27.434 11.624 -25.288 12.735 -21 14 C-17.891 13.946 -15.083 13.522 -12 13 C-14.97 13 -17.94 13 -21 13 C-18.537 10.537 -16.293 10.002 -13 9 C-14.825 9.093 -14.825 9.093 -16.688 9.188 C-20.582 9.181 -22.949 8.589 -26 6 C-26.812 3.812 -26.812 3.812 -27 2 C-25.062 2.312 -25.062 2.312 -23 3 C-22.67 3.99 -22.34 4.98 -22 6 C-19.92 6.277 -17.836 6.52 -15.75 6.75 C-14.59 6.889 -13.43 7.028 -12.234 7.172 C-8.903 7.326 -8.903 7.326 -6.703 5.016 C-6.141 4.35 -5.579 3.685 -5 3 C-4.34 3 -3.68 3 -3 3 C-3.33 3.804 -3.66 4.609 -4 5.438 C-5.061 7.697 -5.061 7.697 -5 9 C-3.35 6.03 -1.7 3.06 0 0 Z " fill="#A698E6" transform="translate(768,896)"/>
<path d="M0 0 C1.226 0.014 1.226 0.014 2.477 0.027 C3.416 0.045 3.416 0.045 4.375 0.062 C3.055 0.393 1.735 0.722 0.375 1.062 C0.375 1.722 0.375 2.383 0.375 3.062 C-0.945 3.062 -2.265 3.062 -3.625 3.062 C-3.618 3.682 -3.612 4.302 -3.605 4.941 C-3.539 11.38 -3.493 17.82 -3.46 24.26 C-3.445 26.665 -3.425 29.069 -3.399 31.474 C-3.362 34.927 -3.345 38.379 -3.332 41.832 C-3.317 42.91 -3.301 43.988 -3.285 45.099 C-3.285 46.601 -3.285 46.601 -3.284 48.132 C-3.278 49.014 -3.271 49.895 -3.264 50.804 C-3.696 53.504 -4.53 54.374 -6.625 56.062 C-7.763 52.649 -7.535 50.499 -7.188 46.938 C-7.086 45.862 -6.984 44.787 -6.879 43.68 C-6.795 42.816 -6.711 41.952 -6.625 41.062 C-6.572 39.694 -6.572 39.694 -7.625 37.062 C-7.453 33.725 -7.111 30.419 -6.781 27.094 C-6.438 24.016 -6.438 24.016 -7.625 21.062 C-7.857 18.339 -7.756 15.812 -7.625 13.062 C-6.965 12.403 -6.305 11.742 -5.625 11.062 C-5.955 10.072 -6.285 9.082 -6.625 8.062 C-6.665 5.73 -6.669 3.395 -6.625 1.062 C-4.22 -0.14 -2.675 -0.038 0 0 Z M-5.625 22.062 C-4.625 24.062 -4.625 24.062 -4.625 24.062 Z M-5.625 29.062 C-4.625 31.062 -4.625 31.062 -4.625 31.062 Z M-5.625 32.062 C-4.625 36.062 -4.625 36.062 -4.625 36.062 Z M-5.625 43.062 C-5.625 44.712 -5.625 46.362 -5.625 48.062 C-5.295 48.062 -4.965 48.062 -4.625 48.062 C-4.625 46.413 -4.625 44.763 -4.625 43.062 C-4.955 43.062 -5.285 43.062 -5.625 43.062 Z " fill="#9382E5" transform="translate(650.625,847.9375)"/>
<path d="M0 0 C11.019 0.848 21.079 4.03 29 12 C35.247 19.641 36.928 28.309 36 38 C34.79 44.494 33.044 49.693 29 55 C28.526 55.887 28.051 56.774 27.562 57.688 C25.694 60.453 24.032 61.636 21 63 C19.515 62.505 19.515 62.505 18 62 C18.675 61.443 19.351 60.886 20.047 60.312 C27.118 54.263 31.512 49.29 33 40 C33.711 30.457 33.646 21.696 27.375 14.008 C19.969 6.817 9.623 4.047 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5A54BE" transform="translate(341,693)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 13.86 2 27.72 2 42 C3.32 42 4.64 42 6 42 C6 42.66 6 43.32 6 44 C0.25 44.125 0.25 44.125 -2 43 C-2 32.667 -2 22.333 -2 12 C-2.041 11.113 -2.082 10.226 -2.125 9.312 C-1.994 5.845 -1.229 3.227 0 0 Z M-1 27 C-1 29.97 -1 32.94 -1 36 C-0.67 36 -0.34 36 0 36 C0 33.03 0 30.06 0 27 C-0.33 27 -0.66 27 -1 27 Z M-1 37 C0 39 0 39 0 39 Z M-1 40 C0 42 0 42 0 42 Z " fill="#9B8BE6" transform="translate(418,868)"/>
<path d="M0 0 C-0.812 1.938 -0.812 1.938 -2 4 C-2.99 4.33 -3.98 4.66 -5 5 C-3.35 5.66 -1.7 6.32 0 7 C-0.99 7.66 -1.98 8.32 -3 9 C-3 8.34 -3 7.68 -3 7 C-4.44 7.257 -5.877 7.531 -7.312 7.812 C-8.513 8.039 -8.513 8.039 -9.738 8.27 C-12.29 9.094 -13.353 9.907 -15 12 C-16.206 15.619 -16.108 18.925 -16.062 22.688 C-16.058 23.389 -16.053 24.091 -16.049 24.814 C-16.037 26.543 -16.019 28.272 -16 30 C-18.844 27.156 -18.3 25.15 -18.312 21.188 C-18.329 19.964 -18.346 18.741 -18.363 17.48 C-17.837 12.434 -15.705 9.352 -12 6 C-13.98 6 -15.96 6 -18 6 C-18 5.34 -18 4.68 -18 4 C-18.99 4.66 -19.98 5.32 -21 6 C-19.959 2.982 -19.062 2.039 -16.312 0.312 C-10.29 -2.074 -6.034 -2.502 0 0 Z " fill="#AEA2EA" transform="translate(503,867)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C3.035 4.663 3.117 8.168 3.098 11.957 C3.094 13.069 3.091 14.181 3.088 15.326 C3.075 17.052 3.075 17.052 3.062 18.812 C3.058 19.982 3.053 21.152 3.049 22.357 C3.037 25.238 3.021 28.119 3 31 C3.66 31 4.32 31 5 31 C3.125 37.875 3.125 37.875 2 39 C-1.5 38.688 -1.5 38.688 -5 38 C-5.33 37.34 -5.66 36.68 -6 36 C-6 36.66 -6 37.32 -6 38 C-12.519 39.337 -19.993 40.432 -26 37 C-29.385 34.273 -30.52 32.32 -31 28 C-31.66 28 -32.32 28 -33 28 C-32.333 24.667 -31.667 21.333 -31 18 C-30.01 18 -29.02 18 -28 18 C-28.33 18.99 -28.66 19.98 -29 21 C-29.513 25.491 -29.593 28.928 -27.375 32.938 C-23.37 36.416 -20.33 37.021 -15.062 36.938 C-10.434 36.224 -7.585 33.913 -4 31 C-4 32.98 -4 34.96 -4 37 C-2.02 37 -0.04 37 2 37 C1.881 33.016 1.756 29.033 1.628 25.049 C1.564 23.037 1.503 21.024 1.443 19.012 C1.234 12.614 0.961 6.331 0 0 Z " fill="#897ADE" transform="translate(614,873)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.81 2.918 2.81 2.918 5 3.75 C10.101 5.911 14.494 8.804 19 12 C19.66 11.67 20.32 11.34 21 11 C21.495 12.98 21.495 12.98 22 15 C22.99 15 23.98 15 25 15 C24.67 16.32 24.34 17.64 24 19 C23.092 18.154 22.185 17.309 21.25 16.438 C7.568 4.82 -7.012 1.67 -24.538 2.724 C-32.099 3.572 -39.299 6.485 -46 10 C-45 7 -45 7 -41.812 5 C-28.447 -1.096 -14.346 -0.362 0 0 Z " fill="#635EC5" transform="translate(376,466)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.617 5.712 4.388 10.882 4.375 16.812 C4.373 17.991 4.373 17.991 4.37 19.192 C4.142 30.06 0.299 38.937 -7.438 46.625 C-14.896 52.901 -24.496 54.644 -34 54 C-31.342 51.342 -29.577 51.546 -25.875 51.125 C-15.693 49.675 -10.547 46.197 -3.812 38.5 C-0.943 34.542 0 31.91 0 27 C0.66 27 1.32 27 2 27 C2.33 23.04 2.66 19.08 3 15 C2.01 14.67 1.02 14.34 0 14 C0.66 13.67 1.32 13.34 2 13 C1.691 12.423 1.381 11.845 1.062 11.25 C0.074 9.157 -0.517 7.255 -1 5 C-0.34 5 0.32 5 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#6960C6" transform="translate(475,687)"/>
<path d="M0 0 C0 1.667 0 3.333 0 5 C2.312 5.027 4.625 5.046 6.938 5.062 C8.869 5.08 8.869 5.08 10.84 5.098 C13.863 5.365 13.863 5.365 15 4 C12.03 3.67 9.06 3.34 6 3 C6 2.67 6 2.34 6 2 C14.91 2 23.82 2 33 2 C33.495 4.475 33.495 4.475 34 7 C22.12 7 10.24 7 -2 7 C-2 5.02 -2 3.04 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#FDFAFE" transform="translate(690,903)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.32 2 2.64 2 4 C3.32 3.67 4.64 3.34 6 3 C5.679 4.006 5.357 5.011 5.026 6.047 C3.957 9.802 3.687 13.334 3.586 17.246 C3.567 17.902 3.547 18.558 3.527 19.234 C3.468 21.302 3.421 23.369 3.375 25.438 C3.337 26.851 3.298 28.264 3.258 29.678 C3.162 33.118 3.078 36.559 3 40 C2.01 40 1.02 40 0 40 C-0.167 34.464 -0.328 28.928 -0.482 23.392 C-0.536 21.508 -0.591 19.623 -0.648 17.738 C-0.73 15.035 -0.805 12.332 -0.879 9.629 C-0.906 8.781 -0.933 7.933 -0.961 7.059 C-1.013 5.04 -1.012 3.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E3D8FE" transform="translate(439,869)"/>
<path d="M0 0 C0.386 4.829 0.458 8.115 -1.812 12.5 C-4.205 15.234 -5.545 15.861 -9.121 16.203 C-12.638 16.274 -15.31 16.303 -18.562 14.875 C-20.614 12.199 -20.349 10.282 -20 7 C-19.67 6.34 -19.34 5.68 -19 5 C-20.65 5.66 -22.3 6.32 -24 7 C-23.812 5.125 -23.812 5.125 -23 3 C-20.438 1.75 -20.438 1.75 -18 1 C-18 1.66 -18 2.32 -18 3 C-16.868 2.783 -15.736 2.567 -14.57 2.344 C-13.089 2.062 -11.607 1.781 -10.125 1.5 C-9.379 1.357 -8.632 1.214 -7.863 1.066 C-2.227 0 -2.227 0 0 0 Z M-12 3 C-12.33 3.99 -12.66 4.98 -13 6 C-13.66 5.34 -14.32 4.68 -15 4 C-18.143 5.67 -18.143 5.67 -18.875 8.375 C-19.225 11.021 -19.225 11.021 -17.852 13.16 C-16.122 15.25 -16.122 15.25 -13.406 15.348 C-7.494 14.963 -7.494 14.963 -2.812 11.75 C-1.998 8.993 -1.755 6.856 -2 4 C-4.227 5.062 -4.227 5.062 -7 7 C-6.67 5.68 -6.34 4.36 -6 3 C-7.98 3 -9.96 3 -12 3 Z " fill="#BDB2EF" transform="translate(610,889)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.33 7 0.66 7 1 C5.02 1 3.04 1 1 1 C1 14.86 1 28.72 1 43 C2.98 43 4.96 43 7 43 C6.99 42.102 6.979 41.203 6.968 40.278 C6.934 36.949 6.909 33.621 6.89 30.292 C6.88 28.851 6.866 27.41 6.849 25.968 C6.825 23.898 6.814 21.828 6.805 19.758 C6.794 18.511 6.784 17.265 6.773 15.981 C7 13 7 13 9 11 C8.984 12.15 8.984 12.15 8.968 13.323 C8.927 16.799 8.901 20.274 8.875 23.75 C8.858 24.957 8.841 26.163 8.824 27.406 C8.818 28.566 8.811 29.727 8.805 30.922 C8.794 31.99 8.784 33.058 8.773 34.159 C9.001 37.011 9.685 38.52 11 41 C10.125 43.25 10.125 43.25 9 45 C5.333 45 1.667 45 -2 45 C-3.333 42.333 -2.671 40.833 -2 38 C-1.34 38 -0.68 38 0 38 C-0.036 36.955 -0.071 35.911 -0.108 34.835 C-0.238 30.96 -0.361 27.086 -0.482 23.211 C-0.536 21.534 -0.591 19.857 -0.648 18.18 C-0.73 15.77 -0.805 13.36 -0.879 10.949 C-0.906 10.199 -0.933 9.449 -0.961 8.676 C-1.053 5.478 -1.021 3.063 0 0 Z M-1 39 C0 43 0 43 0 43 Z " fill="#7A6CDB" transform="translate(436,867)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 9.9 1 19.8 1 30 C-4.61 30 -10.22 30 -16 30 C-15.67 28.35 -15.34 26.7 -15 25 C-12 25 -12 25 -10.25 26.125 C-6.818 27.46 -4.371 26.167 -1 25 C-1.278 23.391 -1.278 23.391 -1.562 21.75 C-1.93 18.596 -2.045 16.902 -1 14 C-3.475 13.01 -3.475 13.01 -6 12 C-4.683 7.391 -2.848 3.859 0 0 Z M-1 4 C-1.66 5.32 -2.32 6.64 -3 8 C-2.01 7.67 -1.02 7.34 0 7 C-0.33 6.01 -0.66 5.02 -1 4 Z M-3 9 C-3 9.99 -3 10.98 -3 12 C-2.34 12 -1.68 12 -1 12 C-1.33 11.01 -1.66 10.02 -2 9 C-2.33 9 -2.66 9 -3 9 Z M-14 26 C-13 28 -13 28 -13 28 Z " fill="#8777DB" transform="translate(840,864)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 24.75 1 49.5 1 75 C7.93 75.33 14.86 75.66 22 76 C22 76.33 22 76.66 22 77 C18.729 77.029 15.458 77.047 12.188 77.062 C11.259 77.071 10.33 77.079 9.373 77.088 C8.48 77.091 7.588 77.094 6.668 77.098 C5.846 77.103 5.024 77.108 4.177 77.114 C2 77 2 77 -1 76 C-1.495 76.99 -1.495 76.99 -2 78 C-1.988 77.313 -1.977 76.626 -1.965 75.918 C-1.956 75.017 -1.947 74.116 -1.938 73.188 C-1.926 72.294 -1.914 71.401 -1.902 70.48 C-1.89 67.866 -1.89 67.866 -3 65 C-2.34 65 -1.68 65 -1 65 C-0.67 43.55 -0.34 22.1 0 0 Z " fill="#A5A0E4" transform="translate(434,506)"/>
<path d="M0 0 C2.522 -0.341 2.522 -0.341 5.664 -0.293 C6.786 -0.283 7.907 -0.274 9.062 -0.264 C10.238 -0.239 11.414 -0.213 12.625 -0.188 C14.4 -0.167 14.4 -0.167 16.211 -0.146 C19.141 -0.111 22.07 -0.062 25 0 C25.495 0.99 25.495 0.99 26 2 C13.625 2.495 13.625 2.495 1 3 C-4.722 18.981 -10.422 34.968 -16 51 C-16.66 51 -17.32 51 -18 51 C-15.397 41.961 -12.371 33.074 -9.329 24.175 C-8.432 21.546 -7.541 18.915 -6.65 16.283 C-6.079 14.607 -5.506 12.93 -4.934 11.254 C-4.669 10.47 -4.404 9.685 -4.132 8.877 C-3.007 5.607 -1.929 2.893 0 0 Z " fill="#504BB7" transform="translate(519,467)"/>
<path d="M0 0 C13.86 0 27.72 0 42 0 C42 16.315 42 16.315 41.125 22.812 C41.046 23.504 40.968 24.196 40.887 24.908 C40.668 26.617 40.344 28.312 40 30 C39.01 30.495 39.01 30.495 38 31 C38.66 21.43 39.32 11.86 40 2 C37.278 2.021 34.555 2.041 31.75 2.062 C21.477 2.1 11.253 1.605 1 1 C1 4.96 1 8.92 1 13 C0.67 13 0.34 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#F7F1FD" transform="translate(793,518)"/>
<path d="M0 0 C3 3 3 3 3.341 6.186 C3.325 7.469 3.309 8.752 3.293 10.074 C3.289 10.758 3.284 11.442 3.28 12.146 C3.263 14.327 3.226 16.507 3.188 18.688 C3.172 20.167 3.159 21.647 3.146 23.127 C3.113 26.752 3.062 30.376 3 34 C4.98 34 6.96 34 9 34 C9 24.1 9 14.2 9 4 C9.33 4 9.66 4 10 4 C10.33 12.25 10.66 20.5 11 29 C11.33 29 11.66 29 12 29 C12 30.98 12 32.96 12 35 C10.398 35.222 8.793 35.427 7.188 35.625 C6.294 35.741 5.401 35.857 4.48 35.977 C2 36 2 36 -1 34 C-0.505 32.36 -0.505 32.36 0 30.688 C1.131 27.154 1.131 27.154 1 24 C1.012 23.288 1.023 22.577 1.035 21.844 C1.044 21.07 1.053 20.297 1.062 19.5 C1.074 18.727 1.086 17.953 1.098 17.156 C1.09 14.815 1.09 14.815 0 12 C0.66 12 1.32 12 2 12 C1.34 8.04 0.68 4.08 0 0 Z " fill="#8071DB" transform="translate(460,876)"/>
<path d="M0 0 C4.421 3.816 7.173 7.132 8 13 C9.321 12.783 9.321 12.783 10.668 12.562 C11.829 12.377 12.991 12.191 14.188 12 C15.336 11.814 16.485 11.629 17.668 11.438 C20.504 11.065 23.147 10.914 26 11 C26.495 11.99 26.495 11.99 27 13 C25.105 13.338 23.209 13.671 21.312 14 C20.257 14.186 19.201 14.371 18.113 14.562 C15.351 14.951 12.783 15.083 10 15 C9.67 15.99 9.34 16.98 9 18 C7 16 7 16 6.5 12.938 C6.335 11.968 6.17 10.999 6 10 C5.34 9.67 4.68 9.34 4 9 C3.732 8.34 3.464 7.68 3.188 7 C2.14 4.715 2.14 4.715 -0.625 4.25 C-1.409 4.168 -2.192 4.085 -3 4 C-2.67 3.01 -2.34 2.02 -2 1 C-2.66 1.66 -3.32 2.32 -4 3 C-4.99 3.495 -4.99 3.495 -6 4 C-6.66 2.35 -7.32 0.7 -8 -1 C-9.98 -0.67 -11.96 -0.34 -14 0 C-14 -0.99 -14 -1.98 -14 -3 C-8.778 -4.813 -4.606 -2.6 0 0 Z " fill="#655EC0" transform="translate(806,492)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.072 1.99 1.072 1.99 0.125 3 C-2.499 6.704 -3.101 9.579 -4 14 C-2.35 14.33 -0.7 14.66 1 15 C1.227 13.763 1.454 12.525 1.688 11.25 C2.464 8.249 3.034 6.959 5.062 4.5 C9.104 2.436 12.546 2.162 17 3 C20.062 5 20.062 5 22 7 C21.67 7.99 21.34 8.98 21 10 C21 9.34 21 8.68 21 8 C19.907 8 18.814 8 17.688 8 C15.458 8 13.229 8 11 8 C10.34 7.01 9.68 6.02 9 5 C5.607 7.857 3.774 9.942 2.875 14.25 C2.586 15.487 2.298 16.725 2 18 C1.01 18.33 0.02 18.66 -1 19 C-1 18.34 -1 17.68 -1 17 C-1.99 17.33 -2.98 17.66 -4 18 C-4.66 17.34 -5.32 16.68 -6 16 C-6 15.01 -6 14.02 -6 13 C-6.66 12.67 -7.32 12.34 -8 12 C-7.01 11.67 -6.02 11.34 -5 11 C-5.33 9.68 -5.66 8.36 -6 7 C-5.34 7 -4.68 7 -4 7 C-3.711 6.216 -3.423 5.433 -3.125 4.625 C-2 2 -2 2 0 0 Z M14 4 C15 6 15 6 15 6 Z " fill="#8F80E1" transform="translate(782,856)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.61 1 11.22 1 17 C8.92 17 16.84 17 25 17 C23.25 28.75 23.25 28.75 21 31 C21.142 29.562 21.289 28.125 21.438 26.688 C21.519 25.887 21.6 25.086 21.684 24.262 C22 22 22 22 23 19 C22.352 19.171 21.703 19.343 21.035 19.52 C17.099 20.143 13.402 19.936 9.438 19.688 C8.683 19.652 7.928 19.617 7.15 19.58 C3.35 19.356 0.238 19.104 -3 17 C-2.34 17 -1.68 17 -1 17 C-1.165 16.319 -1.33 15.639 -1.5 14.938 C-2.429 9.478 -2.441 5.035 0 0 Z " fill="#6F67C7" transform="translate(791,517)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-0.86 1.555 -0.86 1.555 -1.738 1.102 C-11.04 -3.429 -19.599 -4.455 -29.508 -1.168 C-36.084 1.914 -41.327 7.107 -44.227 13.77 C-45.733 18.114 -46.916 22.533 -48 27 C-48.33 27 -48.66 27 -49 27 C-49.929 18.827 -46.742 11.48 -41.723 5.109 C-30.419 -6.804 -14.127 -9.509 0 0 Z " fill="#544FBA" transform="translate(257,493)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.258 2.083 1.258 2.083 0.5 3.188 C-1.392 6.204 -1.392 6.204 -1 11 C6.92 11 14.84 11 23 11 C23 11.33 23 11.66 23 12 C15.08 12.33 7.16 12.66 -1 13 C5.821 12.96 5.821 12.96 12.642 12.91 C13.49 12.907 14.338 12.905 15.211 12.902 C16.08 12.897 16.95 12.892 17.845 12.886 C20 13 20 13 22 14 C22 16.97 22 19.94 22 23 C21.67 23 21.34 23 21 23 C21 20.36 21 17.72 21 15 C20.363 15.159 19.726 15.317 19.07 15.48 C14.993 16.17 11.004 16.263 6.875 16.312 C6.064 16.342 5.253 16.371 4.418 16.4 C-1.452 16.463 -1.452 16.463 -4.074 14.246 C-5.604 10.535 -4.715 7.898 -4 4 C-3.01 3.67 -2.02 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#AF9AF0" transform="translate(818,888)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 13.2 2 26.4 2 40 C1.01 39.67 0.02 39.34 -1 39 C-0.67 37.68 -0.34 36.36 0 35 C-0.99 35 -1.98 35 -3 35 C-4.125 31.25 -4.125 31.25 -3 29 C-2.34 29 -1.68 29 -1 29 C-0.67 19.43 -0.34 9.86 0 0 Z " fill="#E1D6FE" transform="translate(276,869)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.691 2.825 0.381 3.65 0.062 4.5 C-2.305 12.298 -1.532 20.645 2 28 C4.599 31.651 7.28 33.549 11.621 34.688 C17.395 35.456 23.108 35.498 28 32 C30.024 29.492 31.575 26.885 33 24 C33.495 25.485 33.495 25.485 34 27 C31.592 31.282 29.137 34.909 24.359 36.57 C20.994 37.183 17.657 37.104 14.25 37.062 C13.553 37.058 12.855 37.053 12.137 37.049 C10.424 37.037 8.712 37.019 7 37 C7 36.34 7 35.68 7 35 C5.541 33.56 5.541 33.56 3.688 32.125 C1.031 29.935 -1.011 27.826 -3 25 C-3.66 24.67 -4.32 24.34 -5 24 C-4.67 23.34 -4.34 22.68 -4 22 C-3.34 22.33 -2.68 22.66 -2 23 C-2.33 22.34 -2.66 21.68 -3 21 C-3.509 13.446 -3.82 6.686 0 0 Z " fill="#8375DA" transform="translate(333,875)"/>
<path d="M0 0 C2 3 2 3 1.812 5.5 C0.956 8.137 0.11 9.257 -2 11 C-4.875 10.375 -4.875 10.375 -8 9 C-9.375 5.875 -9.375 5.875 -10 3 C-9.34 3 -8.68 3 -8 3 C-7.67 2.01 -7.34 1.02 -7 0 C-4.276 -1.362 -2.872 -0.862 0 0 Z " fill="#AFA1EB" transform="translate(426,852)"/>
<path d="M0 0 C0.619 0.19 1.239 0.381 1.877 0.577 C-0.892 3.395 -3.667 6.206 -6.448 9.012 C-7.857 10.435 -9.26 11.863 -10.662 13.292 C-11.552 14.188 -12.441 15.084 -13.358 16.007 C-14.175 16.834 -14.992 17.661 -15.833 18.514 C-17.909 20.384 -19.578 21.495 -22.123 22.577 C-22.783 22.907 -23.443 23.237 -24.123 23.577 C-22.507 19.698 -19.889 16.861 -17.061 13.827 C-16.33 13.041 -16.33 13.041 -15.584 12.238 C-3.433 -0.684 -3.433 -0.684 0 0 Z " fill="#858AED" transform="translate(421.123291015625,143.423095703125)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-1.3 4.3 -4.6 7.6 -8 11 C-6.02 11 -4.04 11 -2 11 C-2 11.66 -2 12.32 -2 13 C-2.66 13 -3.32 13 -4 13 C-4 13.66 -4 14.32 -4 15 C-0.37 15.33 3.26 15.66 7 16 C2.574 19.319 0.224 19.44 -5 19 C-7.007 18.713 -9.012 18.398 -11 18 C-11.33 17.01 -11.66 16.02 -12 15 C-11.34 15 -10.68 15 -10 15 C-10.495 12.525 -10.495 12.525 -11 10 C-10.457 9.529 -9.915 9.059 -9.355 8.574 C-8.64 7.952 -7.925 7.329 -7.188 6.688 C-6.48 6.073 -5.772 5.458 -5.043 4.824 C-3.299 3.267 -1.632 1.673 0 0 Z " fill="#CCC1F4" transform="translate(701,890)"/>
<path d="M0 0 C2 1 2 1 3 2 C5 2.041 7 2.043 9 2 C7.855 2.835 7.855 2.835 6.688 3.688 C0.741 8.804 0.11 14.542 -1 22 C-1.33 22 -1.66 22 -2 22 C-2.027 20.583 -2.047 19.167 -2.062 17.75 C-2.074 16.961 -2.086 16.172 -2.098 15.359 C-2.004 13.093 -1.632 11.171 -1 9 C-1.99 9.495 -1.99 9.495 -3 10 C-4.254 13.763 -4.107 17.272 -4.062 21.188 C-4.058 21.937 -4.053 22.687 -4.049 23.459 C-4.037 25.306 -4.019 27.153 -4 29 C-7.49 23.766 -6.419 17.106 -6 11 C-5.027 6.464 -3.417 3.178 0 0 Z " fill="#F3EBFE" transform="translate(485,870)"/>
<path d="M0 0 C1 3 1 3 0.312 5.438 C-1.721 9.408 -3.749 11.583 -8 13 C-12.919 13.516 -17.399 13.854 -21.625 11 C-22.409 10.34 -23.192 9.68 -24 9 C-20.888 9 -18.058 9.456 -15 10 C-15.33 9.34 -15.66 8.68 -16 8 C-15.42 7.986 -14.84 7.972 -14.242 7.957 C-8.141 7.585 -5.277 6.384 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#ECE2FD" transform="translate(508,896)"/>
<path d="M0 0 C-3 2 -3 2 -6 3 C-6.082 3.639 -6.165 4.279 -6.25 4.938 C-7.295 9.205 -9.141 13.031 -11 17 C-12.65 17 -14.3 17 -16 17 C-15.836 10.296 -14.909 6.681 -10 2 C-6.738 -0.315 -3.932 -0.179 0 0 Z " fill="#E7DCFD" transform="translate(704,854)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4 4 4 4 3.578 6.465 C1.438 19.55 1.856 34.37 6 47 C5.01 47 4.02 47 3 47 C2.505 45.515 2.505 45.515 2 44 C1.464 43.464 0.928 42.928 0.375 42.375 C-1.406 39.299 -0.789 37.295 -0.254 33.887 C0.067 30.236 -0.25 26.701 -0.562 23.062 C-1.078 16.734 -0.779 11.116 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z M1 37 C2 41 2 41 2 41 Z " fill="#756ECD" transform="translate(185,504)"/>
<path d="M0 0 C0.621 7.919 0.621 7.919 -1.688 11.938 C-5.033 14.921 -7.582 15.823 -12 16.375 C-15.857 15.893 -17.416 14.888 -20 12 C-20.588 7.887 -20.33 6.496 -18 3 C-12.132 0.35 -6.362 -0.353 0 0 Z M-18 5 C-19.211 8.633 -18.669 9.662 -17 13 C-13.873 14.564 -10.42 14.4 -7 14 C-6.01 13.34 -5.02 12.68 -4 12 C-4 11.34 -4 10.68 -4 10 C-3.34 9.67 -2.68 9.34 -2 9 C-2 8.34 -2 7.68 -2 7 C-2.66 7 -3.32 7 -4 7 C-3.67 5.35 -3.34 3.7 -3 2 C-8.934 1.688 -12.759 2.112 -18 5 Z M-2 3 C-1 5 -1 5 -1 5 Z " fill="#8879DE" transform="translate(232,890)"/>
<path d="M0 0 C2.622 3.806 3.131 6.478 3 11 C1.938 11.182 0.876 11.364 -0.219 11.551 C-7.422 12.82 -14.259 14.051 -21 17 C-21 16.34 -21 15.68 -21 15 C-15.375 12 -15.375 12 -12 12 C-11.67 10.35 -11.34 8.7 -11 7 C-10.423 7.186 -9.845 7.371 -9.25 7.562 C-6.793 8.04 -5.356 7.757 -3 7 C-2.67 7.66 -2.34 8.32 -2 9 C-1.01 9.33 -0.02 9.66 1 10 C0.34 9.01 -0.32 8.02 -1 7 C-1.125 4.312 -1.125 4.312 -1 2 C-1.99 2 -2.98 2 -4 2 C-5.564 1.849 -7.127 1.681 -8.688 1.5 C-9.496 1.407 -10.304 1.314 -11.137 1.219 C-11.752 1.147 -12.366 1.074 -13 1 C-12 -1 -12 -1 -9.875 -1.75 C-6.038 -2.084 -3.545 -1.51 0 0 Z M-11 10 C-7 11 -7 11 -7 11 Z " fill="#8476DD" transform="translate(607,874)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 2.98 0.02 4.96 -1 7 C-1.66 7 -2.32 7 -3 7 C-4.698 13.632 -5.31 19.667 -5.25 26.5 C-5.258 27.386 -5.265 28.271 -5.273 29.184 C-5.256 35.074 -4.461 40.293 -3 46 C-5 45 -5 45 -5.816 42.566 C-6.186 41.079 -6.186 41.079 -6.562 39.562 C-7.502 35.677 -7.502 35.677 -9 32 C-9.07 29.237 -8.997 26.489 -8.938 23.727 C-8.884 21.06 -8.884 21.06 -9.621 18.82 C-9.746 18.22 -9.871 17.619 -10 17 C-8.062 14.25 -8.062 14.25 -6 12 C-7.32 11.67 -8.64 11.34 -10 11 C-9.407 10.939 -8.814 10.879 -8.203 10.816 C-5.056 9.65 -4.359 7.826 -2.75 4.938 C-2.229 4.018 -1.708 3.099 -1.172 2.152 C-0.785 1.442 -0.398 0.732 0 0 Z M-8 16 C-7 18 -7 18 -7 18 Z " fill="#8E81DF" transform="translate(738,856)"/>
<path d="M0 0 C3.571 3.333 5.816 5.771 6.227 10.749 C6.217 11.775 6.206 12.802 6.195 13.859 C6.189 14.974 6.182 16.089 6.176 17.238 C6.159 18.397 6.142 19.556 6.125 20.75 C6.116 21.924 6.107 23.099 6.098 24.309 C6.074 27.206 6.041 30.103 6 33 C5.01 33 4.02 33 3 33 C2.67 24.09 2.34 15.18 2 6 C1.01 6.495 1.01 6.495 0 7 C0 4.69 0 2.38 0 0 Z " fill="#E1D6FD" transform="translate(400,875)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.139 1.073 -1.139 1.073 -2.301 1.148 C-3.792 1.261 -3.792 1.261 -5.312 1.375 C-6.792 1.479 -6.792 1.479 -8.301 1.586 C-11.215 1.839 -11.215 1.839 -14 4 C-14.489 3.263 -14.489 3.263 -14.988 2.512 C-18.112 0.164 -21.447 0.685 -25.188 0.812 C-26.312 0.833 -26.312 0.833 -27.459 0.854 C-29.306 0.889 -31.153 0.943 -33 1 C-33.33 2.32 -33.66 3.64 -34 5 C-35.437 5.081 -36.874 5.139 -38.312 5.188 C-39.513 5.24 -39.513 5.24 -40.738 5.293 C-43.512 4.934 -44.158 4.009 -46 2 C-30.515 -0.69 -15.662 -2.278 0 0 Z M-40 2 C-40.33 2.66 -40.66 3.32 -41 4 C-37.535 3.505 -37.535 3.505 -34 3 C-34 2.67 -34 2.34 -34 2 C-35.98 2 -37.96 2 -40 2 Z " fill="#797BEA" transform="translate(527,123)"/>
<path d="M0 0 C0.946 0.303 1.892 0.606 2.867 0.918 C4.203 1.342 5.539 1.765 6.875 2.188 C7.586 2.412 8.297 2.637 9.029 2.869 C12.931 4.089 16.84 5.246 20.789 6.305 C21.576 6.516 22.362 6.727 23.172 6.945 C24.682 7.345 26.193 7.739 27.707 8.124 C32.684 9.446 37.741 11.024 42 14 C42.495 15.485 42.495 15.485 43 17 C42.01 17 41.02 17 40 17 C39.505 16.01 39.505 16.01 39 15 C36.66 13.869 36.66 13.869 33.938 12.875 C33.018 12.522 32.099 12.169 31.152 11.805 C30.442 11.539 29.732 11.274 29 11 C28.67 11.66 28.34 12.32 28 13 C27.67 12.34 27.34 11.68 27 11 C24.996 10.205 24.996 10.205 22.555 9.598 C21.642 9.347 20.729 9.096 19.789 8.838 C18.827 8.582 17.866 8.326 16.875 8.062 C10.698 6.404 4.81 4.696 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#6860C6" transform="translate(308,703)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.393 3.323 2.507 5.618 2.656 7.969 C2.77 8.639 2.883 9.309 3 10 C3.66 10.33 4.32 10.66 5 11 C4.01 11.66 3.02 12.32 2 13 C2 12.34 2 11.68 2 11 C1.34 11 0.68 11 0 11 C-0.33 11.99 -0.66 12.98 -1 14 C-1.33 14 -1.66 14 -2 14 C-2 11.69 -2 9.38 -2 7 C-10.25 7 -18.5 7 -27 7 C-25 5 -25 5 -22.709 4.757 C-21.775 4.761 -20.841 4.765 -19.879 4.77 C-18.352 4.772 -18.352 4.772 -16.795 4.775 C-15.728 4.788 -14.662 4.8 -13.562 4.812 C-12.5 4.813 -11.437 4.814 -10.342 4.814 C-2.421 4.86 -2.421 4.86 1 6 C0.67 4.02 0.34 2.04 0 0 Z M0 7 C0.66 7.66 1.32 8.32 2 9 C2 8.34 2 7.68 2 7 C1.34 7 0.68 7 0 7 Z " fill="#887ADF" transform="translate(202,898)"/>
<path d="M0 0 C2.965 2.445 3.556 4.446 4.188 8.188 C4.346 9.089 4.505 9.99 4.668 10.918 C4.778 11.605 4.887 12.292 5 13 C4.34 13 3.68 13 3 13 C2.959 13.969 2.918 14.939 2.875 15.938 C1.665 24.24 -4.697 30.084 -11 35 C-9.386 31.598 -7.414 28.897 -5 26 C-6.32 25.67 -7.64 25.34 -9 25 C-5.554 19.215 -5.554 19.215 -2.75 18.125 C-2.173 18.084 -1.595 18.043 -1 18 C-1 18.99 -1 19.98 -1 21 C0.789 17.161 1.246 14.16 1.188 9.938 C1.181 8.915 1.175 7.893 1.168 6.84 C1.024 4.399 0.699 2.332 0 0 Z " fill="#A699E8" transform="translate(720,859)"/>
<path d="M0 0 C3.59 1.538 6.038 3.715 8.875 6.375 C11.618 8.905 14.093 11.059 17.25 13.062 C23.421 17.411 28.115 25.082 31 32 C30.67 32.66 30.34 33.32 30 34 C29.343 33.035 29.343 33.035 28.672 32.051 C23.067 24.065 16.743 17.032 10 10 C9.429 9.399 8.858 8.799 8.27 8.18 C6.998 6.868 6.998 6.868 5 6.125 C2.316 4.615 1.411 2.681 0 0 Z " fill="#7577E7" transform="translate(592,152)"/>
<path d="M0 0 C6.484 0.335 9.438 1.326 14 6 C15.02 9.059 15.115 11.124 15.098 14.324 C15.094 15.352 15.091 16.38 15.088 17.439 C15.08 18.511 15.071 19.583 15.062 20.688 C15.058 21.771 15.053 22.854 15.049 23.971 C15.037 26.647 15.021 29.324 15 32 C14.67 32 14.34 32 14 32 C13.98 31.355 13.96 30.709 13.94 30.044 C13.845 27.112 13.735 24.181 13.625 21.25 C13.594 20.234 13.563 19.218 13.531 18.172 C13.493 17.192 13.454 16.212 13.414 15.203 C13.383 14.302 13.351 13.402 13.319 12.474 C12.989 9.912 12.29 8.219 11 6 C10.01 6 9.02 6 8 6 C8.66 7.65 9.32 9.3 10 11 C9.278 10.381 8.556 9.763 7.812 9.125 C5.216 7.163 3.053 6.047 0 5 C0.99 3.68 1.98 2.36 3 1 C2.01 0.67 1.02 0.34 0 0 Z " fill="#F2E9FE" transform="translate(600,867)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.62 1 9.24 1 14 C8.59 14 16.18 14 24 14 C24 14.99 24 15.98 24 17 C20.563 17.029 17.125 17.047 13.688 17.062 C12.711 17.071 11.735 17.079 10.729 17.088 C9.791 17.091 8.853 17.094 7.887 17.098 C7.023 17.103 6.159 17.108 5.268 17.114 C3 17 3 17 0 16 C0 15.34 0 14.68 0 14 C-0.66 13.67 -1.32 13.34 -2 13 C-1.691 11.824 -1.691 11.824 -1.375 10.625 C-1.251 9.759 -1.128 8.893 -1 8 C-1.66 7.34 -2.32 6.68 -3 6 C-2 3 -2 3 0 0 Z " fill="#847AD1" transform="translate(579,567)"/>
<path d="M0 0 C0.33 2.31 0.66 4.62 1 7 C0.34 6.505 -0.32 6.01 -1 5.5 C-4.811 3.595 -7.811 3.341 -12 4 C-14.925 5.351 -14.925 5.351 -17 7 C-17.66 5.68 -18.32 4.36 -19 3 C-13.244 -1.847 -7.138 -2.379 0 0 Z " fill="#EDE1FD" transform="translate(759,856)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.75 1.159 -1.5 1.317 -2.273 1.48 C-9.447 3.116 -15.636 5.302 -22 9 C-22.625 9.357 -23.25 9.714 -23.895 10.082 C-31.899 15.196 -37.211 23.475 -41 32 C-42 29 -42 29 -41.031 26.801 C-32.114 12.31 -18.339 -1.358 0 0 Z " fill="#514CBA" transform="translate(439,644)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 9.24 3 18.48 3 28 C2.01 27.67 1.02 27.34 0 27 C0 18.09 0 9.18 0 0 Z " fill="#DDD2FD" transform="translate(250,869)"/>
<path d="M0 0 C2.234 3.351 2.238 4.418 2.195 8.324 C2.189 9.352 2.182 10.38 2.176 11.439 C2.159 12.511 2.142 13.583 2.125 14.688 C2.116 15.771 2.107 16.854 2.098 17.971 C2.074 20.647 2.041 23.324 2 26 C2.99 26 3.98 26 5 26 C4.47 30.507 2.804 33.454 0 37 C-0.99 37 -1.98 37 -3 37 C-1.35 34.69 0.3 32.38 2 30 C-1.465 32.475 -1.465 32.475 -5 35 C-4.732 34.361 -4.464 33.721 -4.188 33.062 C-3.6 31.547 -3.6 31.547 -3 30 C-2.747 29.38 -2.495 28.76 -2.234 28.121 C0.082 21.956 0.365 16.476 0.188 9.938 C0.167 8.496 0.167 8.496 0.146 7.025 C0.112 4.683 0.062 2.342 0 0 Z " fill="#F2EAFD" transform="translate(763,869)"/>
<path d="M0 0 C2.308 2.308 2.797 3.962 4 7 C5.46 9.99 7.01 12.932 8.562 15.875 C9 16.706 9.437 17.537 9.888 18.394 C13.983 26.1 18.409 33.581 23 41 C22.34 41.66 21.68 42.32 21 43 C20.546 42.175 20.092 41.35 19.625 40.5 C19.089 39.675 18.553 38.85 18 38 C17.34 38 16.68 38 16 38 C15 35 15 35 15 32 C14.07 30.115 13.059 28.269 12.008 26.449 C11.385 25.358 10.763 24.267 10.121 23.143 C9.133 21.432 9.133 21.432 8.125 19.688 C6.831 17.444 5.542 15.198 4.258 12.949 C3.682 11.952 3.106 10.956 2.512 9.928 C1.003 7.005 -0.047 4.144 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#645CC1" transform="translate(675,697)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-6.93 1 -13.86 1 -21 1 C-21 15.85 -21 30.7 -21 46 C-21.33 46 -21.66 46 -22 46 C-23.091 36.938 -23.072 27.95 -22.963 18.836 C-22.955 17.868 -22.946 16.9 -22.938 15.902 C-22.926 15.031 -22.914 14.159 -22.902 13.261 C-22.903 10.831 -22.903 10.831 -24 8 C-23.545 5.262 -22.747 2.68 -22 0 C-14.488 -0.945 -7.512 -0.945 0 0 Z " fill="#322DA5" transform="translate(601,468)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.351 4.912 0.375 7.726 -2 12 C-2.33 12.99 -2.66 13.98 -3 15 C-3.99 15 -4.98 15 -6 15 C-5.67 15.99 -5.34 16.98 -5 18 C-5.99 18 -6.98 18 -8 18 C-8 17.34 -8 16.68 -8 16 C-8.629 16.156 -9.258 16.312 -9.906 16.473 C-16.057 17.871 -21.957 19.134 -27.828 16.035 C-29.266 15.091 -30.641 14.053 -32 13 C-31.67 12.34 -31.34 11.68 -31 11 C-29.639 11.712 -29.639 11.712 -28.25 12.438 C-22.63 15.139 -16.931 16.246 -10.844 14.316 C-6.048 12.16 -3.442 9.787 -1 5 C-0.218 2.272 -0.218 2.272 0 0 Z " fill="#8A7ADD" transform="translate(514,910)"/>
<path d="M0 0 C1.126 0.005 2.252 0.01 3.412 0.016 C4.627 0.019 5.842 0.022 7.094 0.026 C8.373 0.034 9.652 0.042 10.97 0.051 C12.254 0.056 13.537 0.06 14.859 0.065 C18.042 0.077 21.225 0.093 24.408 0.114 C24.078 1.104 23.748 2.094 23.408 3.114 C19.658 3.172 15.908 3.207 12.158 3.239 C10.563 3.264 10.563 3.264 8.935 3.289 C7.398 3.299 7.398 3.299 5.83 3.309 C4.416 3.325 4.416 3.325 2.973 3.341 C0.308 3.105 -1.311 2.47 -3.592 1.114 C-2.592 0.114 -2.592 0.114 0 0 Z " fill="#E6DBFD" transform="translate(786.592041015625,905.886474609375)"/>
<path d="M0 0 C2.388 2.388 2.252 2.892 2.259 6.143 C2.265 6.997 2.271 7.851 2.278 8.731 C2.274 9.655 2.27 10.579 2.266 11.531 C2.268 12.478 2.269 13.425 2.271 14.401 C2.273 16.405 2.269 18.409 2.261 20.413 C2.25 23.488 2.261 26.562 2.273 29.637 C2.272 31.581 2.27 33.525 2.266 35.469 C2.27 36.393 2.274 37.317 2.278 38.269 C2.272 39.123 2.265 39.977 2.259 40.857 C2.257 41.611 2.256 42.364 2.254 43.141 C2 45 2 45 0 47 C0.033 45.079 0.065 43.159 0.098 41.238 C0.071 38.996 0.071 38.996 -0.514 36.855 C-1.131 33.233 -1.016 29.728 -0.879 26.074 C-0.855 25.327 -0.832 24.579 -0.807 23.809 C-0.731 21.435 -0.647 19.061 -0.562 16.688 C-0.509 15.072 -0.456 13.457 -0.404 11.842 C-0.276 7.894 -0.14 3.947 0 0 Z " fill="#B8A8F4" transform="translate(624,857)"/>
<path d="M0 0 C3.818 0.786 5.596 2.67 8.258 5.465 C8.588 6.125 8.918 6.785 9.258 7.465 C11.785 8.12 11.785 8.12 14.258 8.465 C13.598 5.825 12.938 3.185 12.258 0.465 C14.858 2.387 15.31 3.654 16.195 6.84 C16.258 10.465 16.258 10.465 14.32 13.402 C13.299 14.423 13.299 14.423 12.258 15.465 C9.053 13.862 8.21 10.632 6.672 7.496 C4.975 5.058 4.086 4.977 1.258 4.465 C0.928 3.805 0.598 3.145 0.258 2.465 C-5.293 3.208 -5.293 3.208 -10.742 4.465 C-7.696 0.227 -4.939 -0.204 0 0 Z " fill="#A290EA" transform="translate(351.7421875,872.53515625)"/>
<path d="M0 0 C6.048 0.269 10.238 0.918 15 5 C17.577 8.436 18 9.601 18 14 C15.688 14.25 15.688 14.25 13 14 C11.125 12.312 11.125 12.312 10 10 C10.312 7.25 10.312 7.25 11 5 C7.911 2.941 7.291 2.761 3.812 2.875 C2.554 2.916 1.296 2.957 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EDE2FD" transform="translate(348,867)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.742 2.247 1.485 2.495 2.25 2.75 C5.201 4.091 6.848 5.609 9 8 C8.67 8.66 8.34 9.32 8 10 C7.67 10 7.34 10 7 10 C6.67 12.31 6.34 14.62 6 17 C3.035 14.555 2.444 12.554 1.812 8.812 C1.654 7.911 1.495 7.01 1.332 6.082 C1.222 5.395 1.113 4.708 1 4 C-5.299 1.9 -10.888 2.963 -17 5 C-12.913 -0.289 -6.165 -0.252 0 0 Z " fill="#A89DE6" transform="translate(757,851)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.02 0.686 1.04 1.373 1.06 2.08 C1.155 5.178 1.265 8.277 1.375 11.375 C1.406 12.455 1.437 13.535 1.469 14.648 C1.507 15.68 1.546 16.711 1.586 17.773 C1.617 18.727 1.649 19.68 1.681 20.662 C1.786 23.189 1.786 23.189 4 25 C4 26.32 4 27.64 4 29 C4.66 29.33 5.32 29.66 6 30 C1.477 31.508 -3.251 31 -8 31 C-8 28.69 -8 26.38 -8 24 C-7.34 23.67 -6.68 23.34 -6 23 C-5.67 24.65 -5.34 26.3 -5 28 C-3.35 28.33 -1.7 28.66 0 29 C0 19.43 0 9.86 0 0 Z " fill="#A290EA" transform="translate(322,881)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 10.89 1 21.78 1 33 C-0.65 33 -2.3 33 -4 33 C-4 23.1 -4 13.2 -4 3 C-3.67 3 -3.34 3 -3 3 C-2.67 12.24 -2.34 21.48 -2 31 C-1.34 31 -0.68 31 0 31 C0 20.77 0 10.54 0 0 Z " fill="#FCF7FE" transform="translate(321,877)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.333 1 6.667 1 10 C1.33 10.33 1.66 10.66 2 11 C2.523 17.14 0.957 22.21 -1 28 C-1.99 28 -2.98 28 -4 28 C-3.288 26.546 -3.288 26.546 -2.562 25.062 C-0.69 20.752 -0.359 16.664 0 12 C-2.97 12 -5.94 12 -9 12 C-9 10.667 -9 9.333 -9 8 C-4.545 7.505 -4.545 7.505 0 7 C0 4.69 0 2.38 0 0 Z " fill="#F3EBFE" transform="translate(232,878)"/>
<path d="M0 0 C1.938 0.812 1.938 0.812 4 2 C5.596 6.789 5.832 11.17 3.75 15.75 C2.884 16.864 2.884 16.864 2 18 C1.464 18.701 0.928 19.403 0.375 20.125 C-6.808 25.796 -15.242 25.365 -24 25 C-24 24.67 -24 24.34 -24 24 C-23.01 23.902 -22.02 23.804 -21 23.703 C-9.002 22.493 -9.002 22.493 0.938 16.062 C2.876 12.3 3.28 9.999 2.125 5.875 C1.754 4.926 1.382 3.977 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#554FBE" transform="translate(350,718)"/>
<path d="M0 0 C1.033 2.788 1.045 3.868 0.062 6.75 C-0.288 7.492 -0.639 8.235 -1 9 C0.98 9 2.96 9 5 9 C5.33 7.68 5.66 6.36 6 5 C7.001 8.004 7.514 10.877 8 14 C7.01 14 6.02 14 5 14 C5 13.01 5 12.02 5 11 C4.01 11.495 4.01 11.495 3 12 C3.33 13.32 3.66 14.64 4 16 C3.34 16 2.68 16 2 16 C1.67 16.66 1.34 17.32 1 18 C0.546 16.866 0.093 15.731 -0.375 14.562 C-1.571 10.953 -1.571 10.953 -4 10 C-4.125 7.625 -4.125 7.625 -4 5 C-3.34 4.34 -2.68 3.68 -2 3 C-2 2.34 -2 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#9888E6" transform="translate(208,871)"/>
<path d="M0 0 C5.669 5.371 5.669 5.371 7 9 C7 9.99 7 10.98 7 12 C8.181 11.783 9.362 11.567 10.578 11.344 C12.135 11.062 13.693 10.781 15.25 10.5 C16.027 10.357 16.805 10.214 17.605 10.066 C18.36 9.931 19.114 9.796 19.891 9.656 C20.582 9.531 21.273 9.405 21.985 9.275 C24 9 24 9 28 9 C28.66 7.68 29.32 6.36 30 5 C30 6.65 30 8.3 30 10 C12.571 14 12.571 14 6 14 C5.34 12.35 4.68 10.7 4 9 C3.34 9 2.68 9 2 9 C1.783 7.886 1.783 7.886 1.562 6.75 C1.098 4.48 0.581 2.242 0 0 Z " fill="#736BCF" transform="translate(257,494)"/>
<path d="M0 0 C3 1 3 1 3 1 Z M4 2 C3.67 2.99 3.34 3.98 3 5 C1.68 5 0.36 5 -1 5 C-0.216 5.454 0.567 5.908 1.375 6.375 C4 8 4 8 6 10 C5.67 10.99 5.34 11.98 5 13 C4.01 12.01 3.02 11.02 2 10 C1.01 10.495 1.01 10.495 0 11 C-0.99 10.34 -1.98 9.68 -3 9 C-3 8.34 -3 7.68 -3 7 C-5.31 7.66 -7.62 8.32 -10 9 C-9.01 8.01 -8.02 7.02 -7 6 C-7.66 5.34 -8.32 4.68 -9 4 C-4.474 0.983 -1.235 0.654 4 2 Z M-1 7 C-1 7.66 -1 8.32 -1 9 C-0.01 8.67 0.98 8.34 2 8 C1.01 7.67 0.02 7.34 -1 7 Z " fill="#B3A7ED" transform="translate(395,867)"/>
<path d="M0 0 C0.625 1.812 0.625 1.812 1 4 C0.01 5.485 0.01 5.485 -1 7 C-1 5.35 -1 3.7 -1 2 C-3.31 1.67 -5.62 1.34 -8 1 C-7.67 1.99 -7.34 2.98 -7 4 C-7.66 4 -8.32 4 -9 4 C-9.33 3.34 -9.66 2.68 -10 2 C-16.638 4.114 -22.025 6.025 -27 11 C-27.33 11.66 -27.66 12.32 -28 13 C-28.66 13 -29.32 13 -30 13 C-30.33 13.66 -30.66 14.32 -31 15 C-30.669 11.946 -30.339 10.364 -28.215 8.086 C-18.603 0.865 -12.066 -0.497 0 0 Z " fill="#6E65C7" transform="translate(577,665)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C0.34 10 -0.32 10 -1 10 C-0.67 12.64 -0.34 15.28 0 18 C-0.619 18.041 -1.237 18.082 -1.875 18.125 C-2.576 18.414 -3.278 18.702 -4 19 C-5.305 21.996 -5.305 21.996 -6 25 C-5.34 25.33 -4.68 25.66 -4 26 C-4.99 26 -5.98 26 -7 26 C-7.33 26.99 -7.66 27.98 -8 29 C-9 27 -9 27 -8.287 24.294 C-7.905 23.174 -7.523 22.053 -7.129 20.898 C-6.72 19.687 -6.31 18.475 -5.889 17.227 C-5.447 15.943 -5.005 14.659 -4.562 13.375 C-4.124 12.084 -3.686 10.792 -3.248 9.5 C-2.172 6.331 -1.089 3.165 0 0 Z " fill="#7C72C9" transform="translate(530,494)"/>
<path d="M0 0 C1.98 0.99 3.96 1.98 6 3 C5.01 3.33 4.02 3.66 3 4 C2.67 20.83 2.34 37.66 2 55 C1.67 55 1.34 55 1 55 C1 37.84 1 20.68 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#ECE2FD" transform="translate(600,465)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.691 2.722 0.381 3.444 0.062 4.188 C-1.784 9.074 -1.387 12.84 0.25 17.75 C2.347 21.73 2.347 21.73 6 24 C10.585 25.12 13.927 25.417 18.375 23.688 C20.261 22.475 22.135 21.244 24 20 C23.02 22.585 22.061 23.948 19.938 25.75 C14.729 27.966 9.972 27.707 4.688 25.812 C0.755 23.16 -1.499 20.503 -3 16 C-3.544 9.924 -3.345 5.296 0 0 Z " fill="#676CE2" transform="translate(499,244)"/>
<path d="M0 0 C0.495 1.485 0.495 1.485 1 3 C-3.113 9.789 -9.067 13.52 -16.5 15.75 C-21.782 16.278 -26.82 16.295 -32 15 C-31.67 14.34 -31.34 13.68 -31 13 C-30.082 13.035 -29.164 13.07 -28.219 13.105 C-19.559 13.296 -13.597 13.03 -6.25 8.188 C-3.684 5.693 -1.868 3.044 0 0 Z " fill="#615AC2" transform="translate(811,548)"/>
<path d="M0 0 C3 3.625 3 3.625 3 7 C1.812 9.125 1.812 9.125 0 11 C-4.044 12.092 -8.145 12.29 -12.312 12.562 C-13.567 12.646 -14.821 12.73 -16.113 12.816 C-17.066 12.877 -18.019 12.938 -19 13 C-19.33 12.01 -19.66 11.02 -20 10 C-19.227 9.939 -18.453 9.879 -17.656 9.816 C-11.748 9.341 -5.875 8.791 0 8 C-0.186 7.423 -0.371 6.845 -0.562 6.25 C-1.04 3.793 -0.757 2.356 0 0 Z " fill="#7B72D2" transform="translate(368,667)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.995 0.796 0.99 1.592 0.984 2.412 C0.963 6.066 0.95 9.721 0.938 13.375 C0.929 14.627 0.921 15.878 0.912 17.168 C0.704 27.643 0.704 27.643 2 38 C2.66 38.66 3.32 39.32 4 40 C3.34 40 2.68 40 2 40 C2.165 40.516 2.33 41.031 2.5 41.562 C3.15 44.734 3.062 47.772 3 51 C0.84 47.415 0.341 43.774 -0.125 39.688 C-0.209 38.961 -0.293 38.235 -0.38 37.487 C-1.052 30.952 -1.182 24.441 -1.188 17.875 C-1.188 16.904 -1.189 15.933 -1.19 14.932 C-1.15 9.855 -0.866 5.004 0 0 Z " fill="#676DE1" transform="translate(378,235)"/>
<path d="M0 0 C2.375 1 2.375 1 4 3 C4.627 5.992 4.872 8.949 5 12 C1.71 13.097 0.287 12.8 -3 12 C-2.67 11.01 -2.34 10.02 -2 9 C-0.35 9 1.3 9 3 9 C2.34 8.01 1.68 7.02 1 6 C1 5.01 1 4.02 1 3 C-0.32 3.33 -1.64 3.66 -3 4 C-3.33 3.34 -3.66 2.68 -4 2 C-4.763 2 -5.526 2 -6.312 2 C-8.208 2 -10.104 2 -12 2 C-7.732 -1.233 -5.146 -1.31 0 0 Z " fill="#9182E1" transform="translate(227,873)"/>
<path d="M0 0 C2.067 3.101 2.245 3.729 2.195 7.23 C2.189 8.033 2.182 8.835 2.176 9.662 C2.159 10.495 2.142 11.329 2.125 12.188 C2.116 13.032 2.107 13.877 2.098 14.748 C2.074 16.832 2.038 18.916 2 21 C2.797 20.571 2.797 20.571 3.609 20.134 C4.316 19.765 5.022 19.396 5.75 19.016 C6.446 18.647 7.142 18.279 7.859 17.899 C11.064 16.553 13.567 16.714 17 17 C17 17.66 17 18.32 17 19 C18.32 19.66 19.64 20.32 21 21 C20.34 20.835 19.68 20.67 19 20.5 C13.338 19.556 9.615 19.073 4.625 21.938 C3.326 22.958 3.326 22.958 2 24 C1.34 24.33 0.68 24.66 0 25 C0 17.74 0 10.48 0 3 C-1.65 3 -3.3 3 -5 3 C-2 1 -2 1 0 0 Z " fill="#8577DE" transform="translate(382,848)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-1.75 5 -1.75 5 -4 5 C-4.278 5.58 -4.557 6.16 -4.844 6.758 C-6.298 9.577 -8.028 12.164 -9.781 14.805 C-12.865 20.359 -13.372 25.667 -13.625 31.875 C-13.664 32.659 -13.702 33.442 -13.742 34.25 C-13.836 36.166 -13.919 38.083 -14 40 C-14.33 40 -14.66 40 -15 40 C-16.605 26.584 -15.192 16.986 -7 6 C-4.772 3.754 -2.528 1.921 0 0 Z " fill="#6961C5" transform="translate(429,669)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5 1.66 5 2.32 5 3 C5.904 3.061 6.807 3.121 7.738 3.184 C8.918 3.267 10.097 3.351 11.312 3.438 C12.484 3.519 13.656 3.6 14.863 3.684 C18 4 18 4 21 5 C20.67 5.66 20.34 6.32 20 7 C4.818 7.442 4.818 7.442 0.125 3.375 C-0.246 2.921 -0.618 2.467 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#DFD3FC" transform="translate(482,917)"/>
<path d="M0 0 C2.71 0.994 3.792 1.589 5.117 4.207 C6.089 7.283 6.266 9.779 6.25 13 C6.258 14.516 6.258 14.516 6.266 16.062 C5.845 20.71 4.547 23.936 1 27 C0.34 26.67 -0.32 26.34 -1 26 C-0.041 24.979 -0.041 24.979 0.938 23.938 C3.326 20.536 3.679 18.096 4 14 C3.34 14 2.68 14 2 14 C1.67 12.02 1.34 10.04 1 8 C1.99 7.34 2.98 6.68 4 6 C3.34 5.67 2.68 5.34 2 5 C1.278 3.356 0.606 1.689 0 0 Z " fill="#8C7EE2" transform="translate(503,875)"/>
<path d="M0 0 C-0.624 0.224 -1.248 0.449 -1.891 0.68 C-4.723 2.453 -4.933 3.937 -5.75 7.125 C-5.992 8.035 -6.235 8.945 -6.484 9.883 C-6.655 10.581 -6.825 11.28 -7 12 C-8.98 12 -10.96 12 -13 12 C-12.72 7.987 -12.242 6.28 -9.562 3.188 C-6.075 0.21 -4.498 -0.118 0 0 Z " fill="#EDE3FD" transform="translate(598,868)"/>
<path d="M0 0 C3.63 0.99 7.26 1.98 11 3 C9.68 3.99 8.36 4.98 7 6 C7.66 6.33 8.32 6.66 9 7 C8.67 7.66 8.34 8.32 8 9 C7.34 8.67 6.68 8.34 6 8 C0.974 7.621 -1.899 8.017 -6 11 C-4.848 8.532 -3.952 6.952 -2 5 C-1.34 3.35 -0.68 1.7 0 0 Z " fill="#C0B3F0" transform="translate(451,867)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C-0.663 5.982 -5.124 9.758 -11.102 12.34 C-17.57 14.468 -23.21 15.46 -30 15 C-27.342 12.342 -25.577 12.546 -21.875 12.125 C-13.26 10.898 -7.861 8.55 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#5E57BF" transform="translate(471,726)"/>
<path d="M0 0 C1.459 0.114 2.917 0.242 4.375 0.375 C5.593 0.479 5.593 0.479 6.836 0.586 C9 1 9 1 11 3 C12.381 5.763 12.191 7.947 12 11 C11.34 11.66 10.68 12.32 10 13 C7 9.25 7 9.25 7 7 C5.68 6.34 4.36 5.68 3 5 C4.32 4.34 5.64 3.68 7 3 C4.69 2.34 2.38 1.68 0 1 C0 0.67 0 0.34 0 0 Z " fill="#ECE1FD" transform="translate(453,867)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 0.66 2.66 1.32 3 2 C3.99 1.67 4.98 1.34 6 1 C7.125 6.625 7.125 6.625 6 10 C5.01 10 4.02 10 3 10 C2.67 10.99 2.34 11.98 2 13 C1.34 12.34 0.68 11.68 0 11 C0.66 11 1.32 11 2 11 C2 8.69 2 6.38 2 4 C-2.95 3.67 -7.9 3.34 -13 3 C-13 2.67 -13 2.34 -13 2 C-12.145 1.939 -11.291 1.879 -10.41 1.816 C-8.753 1.691 -8.753 1.691 -7.062 1.562 C-5.96 1.481 -4.858 1.4 -3.723 1.316 C-1.089 1.296 -1.089 1.296 0 0 Z " fill="#A08FEA" transform="translate(577,850)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.01 9.57 0.02 19.14 -1 29 C-1.66 29 -2.32 29 -3 29 C-3.275 10.623 -3.275 10.623 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#8077D1" transform="translate(738,504)"/>
<path d="M0 0 C1.173 0.951 2.338 1.911 3.5 2.875 C4.15 3.409 4.799 3.942 5.469 4.492 C5.974 4.99 6.479 5.487 7 6 C7 6.66 7 7.32 7 8 C7.66 8 8.32 8 9 8 C10.158 9.124 11.31 10.256 12.426 11.422 C15.454 14.457 18.752 17.204 22 20 C18.262 20 17.238 18.612 14.539 16.219 C12.963 14.834 12.963 14.834 10.5 14.125 C5.519 11.884 2.608 7.52 0.625 2.562 C0.419 1.717 0.212 0.871 0 0 Z " fill="#777CE7" transform="translate(408,341)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 0.99 19 1.98 19 3 C12.73 3 6.46 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#E3D6FC" transform="translate(560,855)"/>
<path d="M0 0 C1.178 3.534 0.754 4.449 -0.434 7.887 C-0.753 8.825 -1.072 9.762 -1.4 10.729 C-1.743 11.705 -2.085 12.681 -2.438 13.688 C-2.776 14.676 -3.114 15.664 -3.463 16.682 C-4.301 19.124 -5.147 21.563 -6 24 C-6.66 24 -7.32 24 -8 24 C-8.217 23.103 -8.217 23.103 -8.438 22.188 C-8.887 20.438 -9.429 18.713 -10 17 C-8.68 17.33 -7.36 17.66 -6 18 C-5.939 17.374 -5.879 16.747 -5.816 16.102 C-5.733 15.284 -5.649 14.467 -5.562 13.625 C-5.481 12.813 -5.4 12.001 -5.316 11.164 C-5 9 -5 9 -4 7 C-4 6.01 -4 5.02 -4 4 C-2 1.812 -2 1.812 0 0 Z " fill="#7E75CF" transform="translate(501,518)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.05 4.199 1.086 8.398 1.11 12.598 C1.12 14.025 1.134 15.453 1.151 16.881 C1.175 18.936 1.187 20.991 1.195 23.047 C1.206 24.283 1.216 25.519 1.227 26.792 C1 30 1 30 -1 34 C-1.33 26.74 -1.66 19.48 -2 12 C-1.34 12 -0.68 12 0 12 C0 8.04 0 4.08 0 0 Z " fill="#A091E9" transform="translate(248,868)"/>
<path d="M0 0 C1.967 2.536 3.666 5.051 5.188 7.875 C6.769 10.786 8.384 13.67 10.066 16.523 C10.426 17.138 10.787 17.754 11.157 18.387 C11.873 19.608 12.594 20.825 13.321 22.039 C15.247 25.341 16.514 28.196 17 32 C13.926 29.335 12.135 26.009 10.188 22.5 C9.841 21.881 9.494 21.263 9.137 20.625 C8.429 19.361 7.724 18.096 7.021 16.829 C6.076 15.136 5.111 13.454 4.145 11.773 C3.602 10.817 3.059 9.86 2.5 8.875 C2.015 8.027 1.531 7.179 1.031 6.305 C0 4 0 4 0 0 Z " fill="#544DB9" transform="translate(694,691)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 1.66 4 2.32 4 3 C11.26 3 18.52 3 26 3 C24 5 24 5 21.662 5.227 C20.709 5.217 19.755 5.206 18.773 5.195 C17.227 5.186 17.227 5.186 15.648 5.176 C14.568 5.159 13.488 5.142 12.375 5.125 C11.287 5.116 10.199 5.107 9.078 5.098 C6.385 5.074 3.693 5.041 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#7C74D3" transform="translate(644,641)"/>
<path d="M0 0 C7.92 0 15.84 0 24 0 C24.33 0.99 24.66 1.98 25 3 C19.72 3 14.44 3 9 3 C9 2.67 9 2.34 9 2 C6.03 1.67 3.06 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E2D8FD" transform="translate(175,906)"/>
<path d="M0 0 C1.584 4.539 0.173 7.693 -1.488 12.035 C-4.581 18.1 -9.349 23.233 -15 27 C-15.99 26.67 -16.98 26.34 -18 26 C-17.022 25.343 -17.022 25.343 -16.023 24.672 C-6.982 18.111 -3.35 10.452 0 0 Z " fill="#4D47B6" transform="translate(391,533)"/>
<path d="M0 0 C0.384 4.804 0.48 8.09 -1.812 12.438 C-4.242 15.284 -5.811 16.862 -9.594 17.301 C-11.032 17.307 -11.032 17.307 -12.5 17.312 C-13.459 17.329 -14.418 17.346 -15.406 17.363 C-18.348 16.951 -19.204 16.299 -21 14 C-20.432 14.157 -19.863 14.315 -19.277 14.477 C-15.483 15.349 -11.856 15.612 -8 15 C-4.713 12.604 -3.316 11.095 -2.188 7.188 C-2.095 6.105 -2.095 6.105 -2 5 C-3.32 4.67 -4.64 4.34 -6 4 C-2.25 0 -2.25 0 0 0 Z M-2 2 C-1 4 -1 4 -1 4 Z " fill="#8578DC" transform="translate(509,903)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2.682 3.667 3.27 6.304 3.812 9 C5.118 15.1 6.638 20.18 10.145 25.434 C10.427 25.951 10.709 26.467 11 27 C10.67 27.66 10.34 28.32 10 29 C3.57 20.705 -0.995 10.695 0 0 Z " fill="#5A54BD" transform="translate(391,711)"/>
<path d="M0 0 C2 2 2 2 2 4 C1.01 4 0.02 4 -1 4 C-0.67 14.23 -0.34 24.46 0 35 C-3.069 36.534 -5.701 35.55 -9 35 C-8.67 34.34 -8.34 33.68 -8 33 C-6.02 33.33 -4.04 33.66 -2 34 C-2.01 33.041 -2.021 32.082 -2.032 31.094 C-2.068 27.542 -2.091 23.99 -2.11 20.438 C-2.12 18.9 -2.134 17.361 -2.151 15.823 C-2.175 13.614 -2.186 11.405 -2.195 9.195 C-2.206 8.506 -2.216 7.816 -2.227 7.105 C-2.227 5.402 -2.122 3.699 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#7E70DA" transform="translate(384,876)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 2.98 2 4.96 2 7 C2.99 7 3.98 7 5 7 C5 4.69 5 2.38 5 0 C5.33 0 5.66 0 6 0 C6.054 1.583 6.093 3.166 6.125 4.75 C6.148 5.632 6.171 6.513 6.195 7.422 C5.991 10.114 5.366 11.699 4 14 C3.67 13.01 3.34 12.02 3 11 C3.33 10.34 3.66 9.68 4 9 C1.126 11.716 1.126 11.716 -1 15 C-1.66 14.67 -2.32 14.34 -3 14 C-2.536 13.041 -2.536 13.041 -2.062 12.062 C-0.681 8.08 -0.346 4.183 0 0 Z " fill="#F4EBFE" transform="translate(805,865)"/>
<path d="M0 0 C8.156 -0.549 15.234 -0.161 21.633 5.426 C24.315 8.391 25.649 11.025 26 15 C25.567 14.381 25.134 13.763 24.688 13.125 C23.003 10.78 23.003 10.78 20 9 C20 8.34 20 7.68 20 7 C13.469 3.232 7.451 1.944 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5851BD" transform="translate(323,662)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.67 2.66 3.34 3.32 3 4 C1.68 4 0.36 4 -1 4 C-1 4.66 -1 5.32 -1 6 C0.65 6 2.3 6 4 6 C4.33 6.99 4.66 7.98 5 9 C0.545 8.01 0.545 8.01 -4 7 C-3.34 8.98 -2.68 10.96 -2 13 C-4.392 11.128 -5.008 9.974 -5.875 7 C-6 4 -6 4 -4.688 2.125 C-3 1 -3 1 -1 1 C-1 1.66 -1 2.32 -1 3 C-0.34 3 0.32 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#9A89E6" transform="translate(484,908)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2 -0.32 2 -1 2 C-1 3.98 -1 5.96 -1 8 C8.57 8 18.14 8 28 8 C28 8.33 28 8.66 28 9 C23.765 9.217 19.529 9.414 15.292 9.592 C13.851 9.656 12.409 9.725 10.968 9.799 C8.898 9.905 6.829 9.992 4.758 10.074 C2.888 10.161 2.888 10.161 0.981 10.249 C-0.003 10.167 -0.986 10.085 -2 10 C-2.66 9.01 -3.32 8.02 -4 7 C-3.25 3.875 -3.25 3.875 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#7968D9" transform="translate(689,902)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.011 4.696 0.34 5.806 -2.188 7.25 C-5.542 8.145 -8.543 8.136 -12 8 C-12.33 7.34 -12.66 6.68 -13 6 C-10.418 3.418 -8.416 3.695 -4.91 3.352 C-2.492 2.906 -1.396 2.006 0 0 Z " fill="#E7DCFC" transform="translate(228,903)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.704 2.869 0.407 3.738 0.102 4.633 C-1.131 8.4 -1.442 11.951 -1.625 15.875 C-1.664 16.563 -1.702 17.252 -1.742 17.961 C-1.835 19.64 -1.919 21.32 -2 23 C-2.99 23 -3.98 23 -5 23 C-5.977 16.945 -5.977 16.945 -6 15 C-5.34 14.34 -4.68 13.68 -4 13 C-4.66 12.67 -5.32 12.34 -6 12 C-5.34 12 -4.68 12 -4 12 C-3.963 11.301 -3.925 10.603 -3.887 9.883 C-3.788 8.518 -3.788 8.518 -3.688 7.125 C-3.6 5.768 -3.6 5.768 -3.512 4.383 C-3 2 -3 2 0 0 Z " fill="#8C7DE1" transform="translate(481,873)"/>
<path d="M0 0 C1.272 0.009 2.545 0.018 3.855 0.027 C5.319 0.045 5.319 0.045 6.812 0.062 C6.812 1.053 6.812 2.043 6.812 3.062 C0.872 3.062 -5.067 3.062 -11.188 3.062 C-9.077 -1.159 -4.124 -0.038 0 0 Z " fill="#E7DDFE" transform="translate(715.1875,905.9375)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.258 2.083 1.258 2.083 0.5 3.188 C-1.392 6.204 -1.392 6.204 -1 11 C6.92 11 14.84 11 23 11 C23 11.33 23 11.66 23 12 C14.42 12 5.84 12 -3 12 C-3.33 9.36 -3.66 6.72 -4 4 C-3.01 3.67 -2.02 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#9F8FE6" transform="translate(818,888)"/>
<path d="M0 0 C-1.98 0.99 -1.98 0.99 -4 2 C-4.33 8.27 -4.66 14.54 -5 21 C-5.99 20.67 -6.98 20.34 -8 20 C-8 18.02 -8 16.04 -8 14 C-7.34 14 -6.68 14 -6 14 C-6.351 12.907 -6.701 11.814 -7.062 10.688 C-8 7 -8 7 -7 4 C-7.389 1.857 -7.389 1.857 -8 0 C-4.947 -0.981 -3.053 -0.981 0 0 Z " fill="#9D8DE7" transform="translate(381,849)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.185 9.24 3.562 16.591 -1.688 24.562 C-2.121 25.037 -2.554 25.511 -3 26 C-3.66 25.67 -4.32 25.34 -5 25 C-4.381 23.989 -3.763 22.979 -3.125 21.938 C-1.071 18.368 0.023 14.991 1 11 C0.34 10.67 -0.32 10.34 -1 10 C-0.814 9.051 -0.629 8.102 -0.438 7.125 C0.287 4.101 0.287 4.101 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#8477DB" transform="translate(761,877)"/>
<path d="M0 0 C-3 2 -3 2 -6 3 C-6.082 3.639 -6.165 4.279 -6.25 4.938 C-7.295 9.205 -9.141 13.031 -11 17 C-11.66 16.67 -12.32 16.34 -13 16 C-12.546 14.948 -12.092 13.896 -11.625 12.812 C-10.372 9.892 -9.165 6.957 -8 4 C-10.97 6.475 -10.97 6.475 -14 9 C-13.026 5.492 -12.078 4.066 -9.25 1.688 C-5.788 -0.11 -3.834 -0.365 0 0 Z " fill="#F4EBFE" transform="translate(704,854)"/>
<path d="M0 0 C0.33 2.31 0.66 4.62 1 7 C0.01 7 -0.98 7 -2 7 C-1.67 5.68 -1.34 4.36 -1 3 C-3.64 3 -6.28 3 -9 3 C-9 5.64 -9 8.28 -9 11 C-11.762 8.238 -11.579 5.793 -12 2 C-7.823 0.409 -4.468 -0.213 0 0 Z " fill="#9B8DE8" transform="translate(636,848)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C15 0.33 15 0.66 15 1 C10.38 1 5.76 1 1 1 C1 9.91 1 18.82 1 28 C0.67 28 0.34 28 0 28 C-0.168 24.229 -0.334 20.458 -0.5 16.688 C-0.572 15.08 -0.572 15.08 -0.645 13.439 C-0.69 12.411 -0.735 11.383 -0.781 10.324 C-0.823 9.376 -0.865 8.428 -0.908 7.452 C-1.104 2.209 -1.104 2.209 0 0 Z " fill="#2320A2" transform="translate(694,468)"/>
<path d="M0 0 C0.978 0.433 0.978 0.433 1.977 0.875 C6.792 2.921 10.659 4.562 16 4 C15.67 4.66 15.34 5.32 15 6 C12.668 6.339 10.334 6.672 8 7 C7.34 7.66 6.68 8.32 6 9 C3.375 8.625 3.375 8.625 1 8 C0.67 5.36 0.34 2.72 0 0 Z " fill="#C0B4EF" transform="translate(486,898)"/>
<path d="M0 0 C1 2 1 2 0.062 5.125 C-0.288 6.074 -0.639 7.023 -1 8 C0.98 8 2.96 8 5 8 C5.33 6.68 5.66 5.36 6 4 C6.66 4.66 7.32 5.32 8 6 C7.34 6 6.68 6 6 6 C6.66 7.98 7.32 9.96 8 12 C6.375 12.75 6.375 12.75 4 13 C0.812 11.438 0.812 11.438 -2 9 C-2.875 5.75 -2.875 5.75 -3 3 C-2.34 3 -1.68 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#9786E5" transform="translate(586,872)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 6.6 2 13.2 2 20 C-1.247 18.376 -1.702 15.246 -3 12 C-2.01 11.67 -1.02 11.34 0 11 C-0.186 10.113 -0.371 9.226 -0.562 8.312 C-0.98 5.148 -0.808 3.052 0 0 Z M0 11 C-0.33 11.66 -0.66 12.32 -1 13 C-0.34 13.66 0.32 14.32 1 15 C0.67 13.68 0.34 12.36 0 11 Z " fill="#635ABF" transform="translate(693,528)"/>
<path d="M0 0 C-0.866 2.669 -1.722 4.677 -3.562 6.812 C-7.285 8.626 -10.931 8.235 -15 8 C-15 7.67 -15 7.34 -15 7 C-13.02 7 -11.04 7 -9 7 C-9 6.34 -9 5.68 -9 5 C-9.66 4.67 -10.32 4.34 -11 4 C-9.544 3.329 -8.085 2.663 -6.625 2 C-5.813 1.629 -5.001 1.258 -4.164 0.875 C-2 0 -2 0 0 0 Z " fill="#F3ECFE" transform="translate(273,902)"/>
<path d="M0 0 C0.386 4.829 0.458 8.115 -1.812 12.5 C-4 15 -4 15 -7 16 C-8.561 16.067 -10.125 16.085 -11.688 16.062 C-12.9 16.049 -12.9 16.049 -14.137 16.035 C-14.752 16.024 -15.366 16.012 -16 16 C-16 15.67 -16 15.34 -16 15 C-15.229 14.914 -14.458 14.827 -13.664 14.738 C-12.661 14.598 -11.658 14.457 -10.625 14.312 C-9.627 14.185 -8.63 14.057 -7.602 13.926 C-4.795 13.254 -4.795 13.254 -3.719 10.625 C-3.482 9.759 -3.244 8.893 -3 8 C-2.629 6.907 -2.258 5.814 -1.875 4.688 C-1.586 3.801 -1.298 2.914 -1 2 C-2.65 2 -4.3 2 -6 2 C-2.25 0 -2.25 0 0 0 Z " fill="#7E71D8" transform="translate(610,889)"/>
<path d="M0 0 C2.64 1.98 5.28 3.96 8 6 C7.34 7.32 6.68 8.64 6 10 C6.351 10.825 6.701 11.65 7.062 12.5 C7.372 13.325 7.681 14.15 8 15 C6.75 17.062 6.75 17.062 5 19 C4.304 20.318 3.628 21.648 3 23 C2.67 22.34 2.34 21.68 2 21 C2.33 20.01 2.66 19.02 3 18 C3.144 16.024 3.221 14.043 3.25 12.062 C3.276 11.022 3.302 9.982 3.328 8.91 C2.96 5.649 2.085 4.452 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9182E0" transform="translate(807,857)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C2.32 3.67 3.64 3.34 5 3 C5.778 6.406 6.1 9.508 6 13 C3.36 12.34 0.72 11.68 -2 11 C-2 10.67 -2 10.34 -2 10 C-0.35 10 1.3 10 3 10 C3 8.35 3 6.7 3 5 C1.02 5 -0.96 5 -3 5 C-2.01 4.67 -1.02 4.34 0 4 C-0.66 3.67 -1.32 3.34 -2 3 C-2 2.34 -2 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#A190E7" transform="translate(850,889)"/>
<path d="M0 0 C7.25 0.803 12.322 6.721 16.758 12.152 C17.889 13.733 18.954 15.361 20 17 C19.34 17.33 18.68 17.66 18 18 C17.622 17.443 17.245 16.886 16.855 16.312 C12.652 10.484 8.099 6.56 2.16 2.531 C1.447 2.026 0.734 1.521 0 1 C0 0.67 0 0.34 0 0 Z " fill="#F4EFFC" transform="translate(597,652)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 10.24 3 19.48 3 29 C2.67 29 2.34 29 2 29 C2 20.75 2 12.5 2 4 C-0.97 4 -3.94 4 -7 4 C-7 3.67 -7 3.34 -7 3 C-4.69 3 -2.38 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#7074E6" transform="translate(512,152)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.99 2.01 2.98 1.02 4 0 C4 4 4 4 2.688 5.438 C2.131 5.953 1.574 6.469 1 7 C-0.034 8.646 -1.041 10.31 -2 12 C-3.37 9.261 -3.816 7.063 -4 4 C-2.062 1.625 -2.062 1.625 0 0 Z " fill="#C0B4EF" transform="translate(830,876)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.355 3.074 2.537 5.941 2 9 C-1 11.562 -1 11.562 -4 13 C-2.125 8.25 -2.125 8.25 -1 6 C-1.66 6 -2.32 6 -3 6 C-1.125 1.125 -1.125 1.125 0 0 Z M-6 6 C-3 7 -3 7 -3 7 Z " fill="#D8CDF7" transform="translate(807,874)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.31 1.66 4.62 2 7 C1.01 7 0.02 7 -1 7 C-1 8.32 -1 9.64 -1 11 C-0.34 11 0.32 11 1 11 C0.67 11.99 0.34 12.98 0 14 C-0.342 15.331 -0.678 16.664 -1 18 C-0.34 18 0.32 18 1 18 C1 19.65 1 21.3 1 23 C-0.32 22.34 -1.64 21.68 -3 21 C-2.67 16.05 -2.34 11.1 -2 6 C-1.34 6 -0.68 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#A491EC" transform="translate(517,889)"/>
<path d="M0 0 C-0.75 1.938 -0.75 1.938 -2 4 C-4.125 4.75 -4.125 4.75 -6 5 C-6 4.34 -6 3.68 -6 3 C-6.99 2.67 -7.98 2.34 -9 2 C-9.66 1.67 -10.32 1.34 -11 1 C-11.33 2.32 -11.66 3.64 -12 5 C-13.32 4.01 -14.64 3.02 -16 2 C-10.541 -1.639 -6.229 -0.95 0 0 Z " fill="#9A8AE6" transform="translate(609,891)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 6.93 2 13.86 2 21 C1.34 20.67 0.68 20.34 0 20 C0.041 19.258 0.082 18.515 0.125 17.75 C-0.004 14.912 -0.571 13.412 -2 11 C-1.34 10.01 -0.68 9.02 0 8 C-0.287 5.993 -0.619 3.991 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#756CC6" transform="translate(578,546)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.895 2.338 -2.791 2.671 -4.688 3 C-5.743 3.186 -6.799 3.371 -7.887 3.562 C-10.649 3.951 -13.217 4.083 -16 4 C-16.33 4.99 -16.66 5.98 -17 7 C-17.66 6.34 -18.32 5.68 -19 5 C-18.67 4.01 -18.34 3.02 -18 2 C-15.938 1.662 -13.876 1.33 -11.812 1 C-10.664 0.814 -9.515 0.629 -8.332 0.438 C-5.496 0.065 -2.853 -0.086 0 0 Z " fill="#766DCF" transform="translate(832,503)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C1.134 2.124 2.269 2.247 3.438 2.375 C4.613 2.581 5.789 2.788 7 3 C7.33 3.66 7.66 4.32 8 5 C2.06 5.495 2.06 5.495 -4 6 C-4 5.34 -4 4.68 -4 4 C-5.458 2.375 -5.458 2.375 -7 1 C-4.537 -0.231 -2.72 -0.072 0 0 Z " fill="#E3D8FD" transform="translate(346,904)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.329 6.748 1.457 12.073 -2 18 C-4.188 19.938 -4.188 19.938 -6 21 C-6 20.34 -6 19.68 -6 19 C-6.66 18.34 -7.32 17.68 -8 17 C-7.216 16.587 -6.433 16.175 -5.625 15.75 C-2.795 13.863 -2.213 13.077 -1 10 C-0.549 7.415 -0.549 7.415 -0.375 4.75 C-0.263 3.412 -0.263 3.412 -0.148 2.047 C-0.099 1.371 -0.05 0.696 0 0 Z " fill="#F5EEFE" transform="translate(610,888)"/>
<path d="M0 0 C-3 2 -3 2 -6.5 3.438 C-9.457 4.757 -11.016 5.549 -13 8 C-13.66 6.68 -14.32 5.36 -15 4 C-9.98 -0.119 -6.401 -0.512 0 0 Z " fill="#E0D3FC" transform="translate(755,855)"/>
<path d="M0 0 C1 3 1 3 0.312 5.562 C-1.161 8.299 -2.135 8.95 -5 10 C-5.33 9.67 -5.66 9.34 -6 9 C-7.686 8.928 -9.375 8.916 -11.062 8.938 C-11.982 8.947 -12.901 8.956 -13.848 8.965 C-14.558 8.976 -15.268 8.988 -16 9 C-16 8.67 -16 8.34 -16 8 C-15.42 7.986 -14.84 7.972 -14.242 7.957 C-8.141 7.585 -5.277 6.384 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#F3EBFE" transform="translate(508,896)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 3.31 3 5.62 3 8 C-4.59 8 -12.18 8 -20 8 C-20 7.67 -20 7.34 -20 7 C-13.4 7 -6.8 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#A193E6" transform="translate(574,880)"/>
<path d="M0 0 C2 2 2 2 2.195 4.82 C2.172 5.911 2.149 7.001 2.125 8.125 C2.107 9.221 2.089 10.316 2.07 11.445 C2.047 12.288 2.024 13.131 2 14 C1.01 14.33 0.02 14.66 -1 15 C-1.221 12.876 -1.427 10.751 -1.625 8.625 C-1.741 7.442 -1.857 6.258 -1.977 5.039 C-1.984 4.036 -1.992 3.033 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#E1D6FD" transform="translate(734,870)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.393 3.323 2.507 5.618 2.656 7.969 C2.77 8.639 2.883 9.309 3 10 C3.66 10.33 4.32 10.66 5 11 C4.01 11.66 3.02 12.32 2 13 C2 12.34 2 11.68 2 11 C1.34 11 0.68 11 0 11 C-0.33 11.99 -0.66 12.98 -1 14 C-1.33 14 -1.66 14 -2 14 C-2.33 11.36 -2.66 8.72 -3 6 C-1.68 6 -0.36 6 1 6 C0.67 4.02 0.34 2.04 0 0 Z M0 7 C0.66 7.66 1.32 8.32 2 9 C2 8.34 2 7.68 2 7 C1.34 7 0.68 7 0 7 Z " fill="#B09EF0" transform="translate(202,898)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2 2.32 2 3 2 C4.625 3.5 4.625 3.5 6 5 C6.743 4.649 7.485 4.299 8.25 3.938 C11.132 2.955 12.212 2.967 15 4 C14.67 4.99 14.34 5.98 14 7 C7.089 8.549 7.089 8.549 4.133 7.059 C2.5 5.75 2.5 5.75 0 3 C0 2.01 0 1.02 0 0 Z " fill="#E9E0FD" transform="translate(250,902)"/>
<path d="M0 0 C1.212 0.027 1.212 0.027 2.449 0.055 C3.372 0.089 3.372 0.089 4.312 0.125 C4.312 1.115 4.312 2.105 4.312 3.125 C-4.259 4.554 -4.259 4.554 -7.688 2.125 C-4.484 -0.011 -3.668 -0.106 0 0 Z " fill="#E5D8FB" transform="translate(796.6875,854.875)"/>
<path d="M0 0 C7.507 0.577 13.711 4.158 18.562 9.812 C19.39 10.864 20.208 11.922 21 13 C20.01 13 19.02 13 18 13 C17.428 12.254 16.855 11.507 16.266 10.738 C13.464 7.352 10.679 5.996 6.75 4.188 C6.1 3.876 5.451 3.565 4.781 3.244 C3.193 2.485 1.597 1.741 0 1 C0 0.67 0 0.34 0 0 Z " fill="#4C46B6" transform="translate(577,666)"/>
<path d="M0 0 C1.052 0.507 1.052 0.507 2.125 1.023 C7.717 3.652 12.948 5.744 19 7 C19 7.66 19 8.32 19 9 C11.549 7.938 5.563 5.659 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#6760C4" transform="translate(210,575)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.97 1 5.94 1 9 C1.99 9 2.98 9 4 9 C4 6.36 4 3.72 4 1 C4.33 1 4.66 1 5 1 C5 4.3 5 7.6 5 11 C3.02 11 1.04 11 -1 11 C-1.027 9.542 -1.046 8.083 -1.062 6.625 C-1.074 5.813 -1.086 5.001 -1.098 4.164 C-1 2 -1 2 0 0 Z " fill="#F7F0FE" transform="translate(842,899)"/>
<path d="M0 0 C3.763 0.786 5.614 2.757 8.262 5.473 C7.932 6.793 7.602 8.113 7.262 9.473 C6.829 8.648 6.395 7.823 5.949 6.973 C5.392 6.148 4.835 5.323 4.262 4.473 C3.272 4.473 2.282 4.473 1.262 4.473 C0.932 3.813 0.602 3.153 0.262 2.473 C-5.289 3.216 -5.289 3.216 -10.738 4.473 C-7.692 0.234 -4.938 -0.188 0 0 Z " fill="#8576DE" transform="translate(351.73828125,872.52734375)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.75 5.875 -0.75 5.875 -3 9 C-3.474 9.887 -3.949 10.774 -4.438 11.688 C-6.306 14.453 -7.968 15.636 -11 17 C-11.99 16.67 -12.98 16.34 -14 16 C-13.325 15.432 -12.649 14.863 -11.953 14.277 C-6.856 9.865 -2.87 6.131 0 0 Z " fill="#4844B8" transform="translate(373,739)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-2.64 1.33 -5.28 1.66 -8 2 C-7.34 4.31 -6.68 6.62 -6 9 C-6.99 9.99 -7.98 10.98 -9 12 C-9.33 8.37 -9.66 4.74 -10 1 C-4.375 -1.25 -4.375 -1.25 0 0 Z " fill="#766FCE" transform="translate(297,728)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C-5.298 10.483 -5.298 10.483 -9 13 C-8.67 11.68 -8.34 10.36 -8 9 C-7.34 9 -6.68 9 -6 9 C-5.907 8.165 -5.907 8.165 -5.812 7.312 C-4.666 4.05 -2.506 2.313 0 0 Z " fill="#E2D5FC" transform="translate(792,892)"/>
<path d="M0 0 C-0.66 1.98 -1.32 3.96 -2 6 C-1.34 5.67 -0.68 5.34 0 5 C-1.37 8.161 -1.989 8.993 -5 11 C-6.221 5.628 -6.221 5.628 -5.188 2.688 C-3.641 0.49 -2.686 0 0 0 Z " fill="#C7BBF2" transform="translate(806,880)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-3.334 6.018 -7.297 8.881 -14.875 9.125 C-15.906 9.084 -16.938 9.043 -18 9 C-15.162 7.108 -13.359 6.46 -10.125 5.625 C-5.975 4.442 -3.154 3.049 0 0 Z " fill="#504BB8" transform="translate(258,554)"/>
<path d="M0 0 C4.894 0.419 6.731 2.486 10 6 C10.928 6.701 11.856 7.402 12.812 8.125 C13.534 8.744 14.256 9.363 15 10 C15 10.99 15 11.98 15 13 C11.264 11.755 9.774 9.774 7 7 C5.928 6.299 4.855 5.598 3.75 4.875 C1 3 1 3 0 0 Z " fill="#6E69C8" transform="translate(811,471)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 5.28 2 10.56 2 16 C1.34 16 0.68 16 0 16 C-0.194 13.521 -0.38 11.042 -0.562 8.562 C-0.619 7.855 -0.675 7.148 -0.732 6.42 C-0.862 4.616 -0.935 2.808 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M0 6 C1 8 1 8 1 8 Z " fill="#7F82F0" transform="translate(641,248)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 1.66 2.34 2.32 2 3 C2.66 3 3.32 3 4 3 C3.551 4.482 3.09 5.961 2.625 7.438 C2.37 8.261 2.115 9.085 1.852 9.934 C1 12 1 12 -1 13 C-2.317 9.048 -1.191 6.923 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D8CEF8" transform="translate(335,872)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1.66 1.68 2.32 1 3 C1.33 3.66 1.66 4.32 2 5 C1.216 5.454 0.433 5.908 -0.375 6.375 C-3.037 7.884 -3.037 7.884 -5 10 C-5.75 8.312 -5.75 8.312 -6 6 C-4.215 3.353 -2.88 1.44 0 0 Z " fill="#F1E7FE" transform="translate(449,868)"/>
<path d="M0 0 C5.539 0.462 10.396 1.744 15 5 C15.33 5.99 15.66 6.98 16 8 C15.01 8 14.02 8 13 8 C12.67 7.34 12.34 6.68 12 6 C9.66 4.869 9.66 4.869 6.938 3.875 C6.018 3.522 5.099 3.169 4.152 2.805 C3.442 2.539 2.732 2.274 2 2 C1.67 2.66 1.34 3.32 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#7168CA" transform="translate(335,712)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.664 1.098 -1.328 1.196 -2.012 1.297 C-7.373 2.205 -11.02 3.02 -15 7 C-14.67 7.66 -14.34 8.32 -14 9 C-15.32 8.67 -16.64 8.34 -18 8 C-15.995 3.489 -13.477 1.656 -9.141 -0.352 C-5.769 -1.373 -3.385 -0.846 0 0 Z " fill="#7069CA" transform="translate(328,663)"/>
<path d="M0 0 C1.98 0.99 3.96 1.98 6 3 C5.01 3.33 4.02 3.66 3 4 C2.67 9.61 2.34 15.22 2 21 C1.67 21 1.34 21 1 21 C1 15.06 1 9.12 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C7BEEE" transform="translate(600,465)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 0.99 2.66 1.98 3 3 C2.072 3.155 2.072 3.155 1.125 3.312 C-1.365 3.875 -1.365 3.875 -3 7 C-3 7.99 -3 8.98 -3 10 C-4.98 10 -6.96 10 -9 10 C-9 9.34 -9 8.68 -9 8 C-7.285 6.309 -7.285 6.309 -5.062 4.438 C-3.966 3.507 -3.966 3.507 -2.848 2.559 C-2.238 2.044 -1.628 1.53 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8677D9" transform="translate(795,895)"/>
<path d="M0 0 C3 3 3 3 3.328 5.742 C3.302 6.776 3.277 7.81 3.25 8.875 C3.227 10.418 3.227 10.418 3.203 11.992 C3.017 14.751 2.616 17.309 2 20 C1.34 19.67 0.68 19.34 0 19 C0 12.73 0 6.46 0 0 Z " fill="#B9A6F5" transform="translate(312,878)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-1.512 5.829 -5.125 9.306 -11 11 C-13.246 10.598 -13.246 10.598 -15 10 C-14.336 9.697 -13.672 9.394 -12.988 9.082 C-7.774 6.608 -3.768 4.395 0 0 Z " fill="#F7F2FE" transform="translate(810,553)"/>
<path d="M0 0 C1 3 1 3 0 6 C-0.66 6 -1.32 6 -2 6 C-2.66 4.35 -3.32 2.7 -4 1 C-5.98 1.33 -7.96 1.66 -10 2 C-10 1.01 -10 0.02 -10 -1 C-6.194 -2.464 -3.653 -1.623 0 0 Z " fill="#A598E2" transform="translate(802,490)"/>
<path d="M0 0 C0 3.338 -0.581 4.357 -2.312 7.125 C-2.742 7.829 -3.171 8.533 -3.613 9.258 C-5 11 -5 11 -8 12 C-6.72 6.996 -4.827 4.434 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8386ED" transform="translate(400,178)"/>
<path d="M0 0 C6.625 -0.25 6.625 -0.25 10 2 C8.938 3.562 8.938 3.562 7 5 C3.312 4.688 3.312 4.688 0 4 C0 2.68 0 1.36 0 0 Z " fill="#DED2FB" transform="translate(595,905)"/>
<path d="M0 0 C-3.225 1.737 -6.513 2.891 -10 4 C-10.33 5.65 -10.66 7.3 -11 9 C-13.36 6.64 -13.491 5.221 -14 2 C-9.158 0.664 -5.066 -0.253 0 0 Z " fill="#F6F2FD" transform="translate(303,728)"/>
<path d="M0 0 C2.529 2.2 3.367 3.729 4 7 C4.33 7.66 4.66 8.32 5 9 C1 9 1 9 -1.25 7.125 C-3 5 -3 5 -3 3 C-2.01 3 -1.02 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#E1D6FC" transform="translate(737,895)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-1.848 2.683 -2.696 2.366 -3.57 2.039 C-7.095 0.971 -10.207 0.6 -13.875 0.375 C-15.027 0.3 -16.18 0.225 -17.367 0.148 C-18.67 0.075 -18.67 0.075 -20 0 C-20 -0.33 -20 -0.66 -20 -1 C-12.882 -2.121 -6.976 -1.796 0 0 Z " fill="#DBD2F4" transform="translate(804,488)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.625 2.875 1.625 2.875 2 6 C0 8 0 8 -3.625 8.125 C-4.739 8.084 -5.852 8.043 -7 8 C-7.33 7.34 -7.66 6.68 -8 6 C-7.34 5.01 -6.68 4.02 -6 3 C-6 3.99 -6 4.98 -6 6 C-4.35 6 -2.7 6 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#9E8FE7" transform="translate(694,865)"/>
<path d="M0 0 C2.5 0.125 2.5 0.125 3.5 1.125 C3.541 2.791 3.543 4.459 3.5 6.125 C3.17 4.805 2.84 3.485 2.5 2.125 C0.515 2.392 0.515 2.392 -1.5 3.125 C-1.83 4.115 -2.16 5.105 -2.5 6.125 C-1.84 6.455 -1.18 6.785 -0.5 7.125 C-1.82 6.795 -3.14 6.465 -4.5 6.125 C-3.431 0.172 -3.431 0.172 0 0 Z " fill="#F2E9FC" transform="translate(422.5,852.875)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.986 1.967 -2.971 1.935 -4.957 1.902 C-7.203 1.917 -7.203 1.917 -10 3 C-12.341 2.722 -14.676 2.395 -17 2 C-17 1.67 -17 1.34 -17 1 C-14.918 0.636 -12.835 0.284 -10.75 -0.062 C-9.01 -0.358 -9.01 -0.358 -7.234 -0.66 C-4.375 -0.961 -2.627 -1.094 0 0 Z " fill="#7C7CE8" transform="translate(534,397)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.98 1 -3.96 1 -6 1 C-6 2.98 -6 4.96 -6 7 C-6.66 7 -7.32 7 -8 7 C-9.389 5.039 -10.726 3.038 -12 1 C-7.947 0.018 -4.161 -0.082 0 0 Z " fill="#887BDD" transform="translate(515,867)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.28 1 10.56 1 16 C0.01 16.33 -0.98 16.66 -2 17 C-2 14.36 -2 11.72 -2 9 C-1.34 9 -0.68 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#E2DBF9" transform="translate(394,695)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.054 1.625 1.093 3.25 1.125 4.875 C1.148 5.78 1.171 6.685 1.195 7.617 C1 10 1 10 -1 12 C-1.99 11.67 -2.98 11.34 -4 11 C-3.505 10.103 -3.505 10.103 -3 9.188 C-1.792 6.946 -1.792 6.946 -2 4 C-1.34 4 -0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#9786E6" transform="translate(375,899)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 2.65 1.34 4.3 1 6 C-0.65 6.33 -2.3 6.66 -4 7 C-4 4 -4 4 -2 1.812 C-1.34 1.214 -0.68 0.616 0 0 Z " fill="#E2D5FB" transform="translate(700,895)"/>
<path d="M0 0 C4.609 0.112 6.729 1.385 9.875 4.562 C10.929 5.697 11.975 6.839 13 8 C12.34 8.66 11.68 9.32 11 10 C10.299 9.072 9.598 8.144 8.875 7.188 C6.327 4.099 3.716 2.556 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5D55BC" transform="translate(373,492)"/>
<path d="M0 0 C5.846 5.538 5.846 5.538 7 9 C6.67 9.99 6.34 10.98 6 12 C3.44 9.774 2.677 8.287 2 5 C1.353 3.326 0.69 1.657 0 0 Z " fill="#6C65C7" transform="translate(257,494)"/>
<path d="M0 0 C10.334 1.186 10.334 1.186 12.688 4.062 C13.121 4.702 13.554 5.341 14 6 C12.334 6.043 10.666 6.041 9 6 C8 5 7 4 6 3 C4.016 2.286 2.017 1.614 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7E7DE9" transform="translate(472,393)"/>
<path d="M0 0 C0 1.667 0 3.333 0 5 C1.98 5 3.96 5 6 5 C6 5.66 6 6.32 6 7 C3.36 7 0.72 7 -2 7 C-2 5.02 -2 3.04 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#F3EBFE" transform="translate(690,903)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 2.31 2 4.62 2 7 C3.32 7.33 4.64 7.66 6 8 C3.69 7.67 1.38 7.34 -1 7 C-2 4 -2 4 -1.062 1.812 C-0.537 0.915 -0.537 0.915 0 0 Z " fill="#9C8CE7" transform="translate(461,904)"/>
<path d="M0 0 C5.38 0.34 9.533 0.686 14 4 C13.01 4.495 13.01 4.495 12 5 C11.464 4.691 10.928 4.381 10.375 4.062 C6.864 2.492 3.827 2.872 0 3 C0 2.01 0 1.02 0 0 Z " fill="#F4EBFE" transform="translate(348,867)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-0.64 4.3 -3.28 7.6 -6 11 C-6.99 10.67 -7.98 10.34 -9 10 C-8.277 9.294 -8.277 9.294 -7.539 8.574 C-6.907 7.952 -6.276 7.329 -5.625 6.688 C-4.685 5.765 -4.685 5.765 -3.727 4.824 C-1.819 2.912 -1.819 2.912 0 0 Z " fill="#5C56C0" transform="translate(617,737)"/>
<path d="M0 0 C0.082 0.949 0.165 1.898 0.25 2.875 C0.651 6.105 0.651 6.105 3.062 7.375 C3.702 7.581 4.341 7.788 5 8 C2.36 8 -0.28 8 -3 8 C-4.349 5.302 -3.803 3.848 -3 1 C-1 0 -1 0 0 0 Z M-2 2 C-1 6 -1 6 -1 6 Z " fill="#A08EEA" transform="translate(437,904)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.94 5.297 1.988 10.087 1.562 15.438 C1.51 16.169 1.458 16.901 1.404 17.654 C1.276 19.437 1.139 21.218 1 23 C0.67 23 0.34 23 0 23 C0 15.41 0 7.82 0 0 Z " fill="#B9A5F5" transform="translate(325,881)"/>
<path d="M0 0 C5.28 0.99 10.56 1.98 16 3 C16 3.33 16 3.66 16 4 C9.559 5.645 5.782 4.055 0 1 C0 0.67 0 0.34 0 0 Z " fill="#514BBD" transform="translate(310,758)"/>
<path d="M0 0 C2 2 2 2 2.266 4.531 C2.26 5.511 2.255 6.491 2.25 7.5 C2.255 8.48 2.26 9.459 2.266 10.469 C2 13 2 13 0 15 C-1.142 11.573 -0.909 9.389 -0.562 5.812 C-0.461 4.726 -0.359 3.639 -0.254 2.52 C-0.17 1.688 -0.086 0.857 0 0 Z " fill="#BAA5F4" transform="translate(644,889)"/>
<path d="M0 0 C-5.143 4.429 -5.143 4.429 -9 5 C-9.33 4.01 -9.66 3.02 -10 2 C-6.598 -0.094 -3.947 -0.179 0 0 Z " fill="#EADFFD" transform="translate(494,872)"/>
<path d="M0 0 C2.5 2.174 3.436 3.73 4 7 C3.01 7.66 2.02 8.32 1 9 C0.34 8.67 -0.32 8.34 -1 8 C-0.67 5.36 -0.34 2.72 0 0 Z " fill="#E1D5FC" transform="translate(763,862)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.125 5.625 3.125 5.625 2 9 C1.01 9 0.02 9 -1 9 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#B4A1F3" transform="translate(581,851)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-1.986 3.354 -3.988 4.685 -6 6 C-7.369 7.297 -8.717 8.618 -10 10 C-9.439 6.463 -8.304 5.007 -5.562 2.75 C-4.945 2.229 -4.328 1.708 -3.691 1.172 C-2 0 -2 0 0 0 Z " fill="#6C66C7" transform="translate(416,652)"/>
<path d="M0 0 C1.255 3.766 0.371 5.372 -1 9 C-1.66 9 -2.32 9 -3 9 C-3.217 8.103 -3.217 8.103 -3.438 7.188 C-3.887 5.438 -4.429 3.713 -5 2 C-3.68 2.33 -2.36 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#887ED3" transform="translate(496,533)"/>
<path d="M0 0 C-1.32 0.33 -2.64 0.66 -4 1 C-4 1.66 -4 2.32 -4 3 C-6.64 3.33 -9.28 3.66 -12 4 C-8.078 -0.1 -5.684 -0.105 0 0 Z " fill="#F1E9FE" transform="translate(222,887)"/>
<path d="M0 0 C-5.445 2.475 -5.445 2.475 -11 5 C-11 4.01 -11 3.02 -11 2 C-4.5 -1.125 -4.5 -1.125 0 0 Z " fill="#615CC0" transform="translate(554,647)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 1.66 0.68 2.32 0 3 C-0.719 5.318 -1.391 7.651 -2 10 C-2.66 9.67 -3.32 9.34 -4 9 C-2.25 2.25 -2.25 2.25 0 0 Z " fill="#9688E2" transform="translate(779,901)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.382 2.656 2.714 4.325 3 6 C3.33 6.66 3.66 7.32 4 8 C1.03 7.505 1.03 7.505 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z " fill="#E2D8FD" transform="translate(586,896)"/>
<path d="M0 0 C1.707 1.281 3.374 2.618 5 4 C5 4.66 5 5.32 5 6 C6.32 6.66 7.64 7.32 9 8 C8.01 8.33 7.02 8.66 6 9 C4.994 7.88 3.995 6.754 3 5.625 C2.443 4.999 1.886 4.372 1.312 3.727 C0 2 0 2 0 0 Z " fill="#655DC2" transform="translate(402,740)"/>
<path d="M0 0 C7 9.1 7 9.1 7 12 C3.337 10.779 2.65 9.133 0.77 5.887 C0 4 0 4 0 0 Z " fill="#675EC1" transform="translate(694,691)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C-0.97 5.475 -0.97 5.475 -4 8 C-4 4.092 -2.494 2.884 0 0 Z " fill="#7E83EA" transform="translate(396,167)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.688 3.062 3.688 3.062 4 5 C3.34 5 2.68 5 2 5 C1.67 5.66 1.34 6.32 1 7 C0.34 5.02 -0.32 3.04 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B6A2F4" transform="translate(208,882)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.222 1.79 1.427 3.582 1.625 5.375 C1.741 6.373 1.857 7.37 1.977 8.398 C1.984 9.257 1.992 10.115 2 11 C1.34 11.66 0.68 12.32 0 13 C-1.297 9.108 -1.13 6.106 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#BAA6F6" transform="translate(644,859)"/>
<path d="M0 0 C4.29 0.33 8.58 0.66 13 1 C13 1.33 13 1.66 13 2 C7.06 2.495 7.06 2.495 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#9185D9" transform="translate(348,677)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C2.99 4 3.98 4 5 4 C4.67 5.32 4.34 6.64 4 8 C1.69 6.02 -0.62 4.04 -3 2 C-2.01 1.34 -1.02 0.68 0 0 Z " fill="#7770CE" transform="translate(396,477)"/>
<path d="M0 0 C5.875 1.875 5.875 1.875 7 3 C7.041 4.666 7.043 6.334 7 8 C5.68 7.34 4.36 6.68 3 6 C3.66 5.34 4.32 4.68 5 4 C4.175 3.526 3.35 3.051 2.5 2.562 C1.675 2.047 0.85 1.531 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8181EF" transform="translate(433,376)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.67 2.66 3.34 3.32 3 4 C0.69 4 -1.62 4 -4 4 C-3.67 3.01 -3.34 2.02 -3 1 C-2.34 1 -1.68 1 -1 1 C-1 1.66 -1 2.32 -1 3 C-0.34 3 0.32 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#BAA5F6" transform="translate(484,908)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C3.97 2 6.94 2 10 2 C10 2.33 10 2.66 10 3 C3.565 3.495 3.565 3.495 -3 4 C-2.34 3.67 -1.68 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#E9E0FD" transform="translate(822,892)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 3.3 2 6.6 2 10 C1.01 10 0.02 10 -1 10 C-0.814 9.051 -0.629 8.102 -0.438 7.125 C0.287 4.101 0.287 4.101 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#B3A1F0" transform="translate(761,877)"/>
<path d="M0 0 C0.866 0.278 0.866 0.278 1.75 0.562 C4.207 1.04 5.644 0.757 8 0 C8.33 0.66 8.66 1.32 9 2 C8.34 2 7.68 2 7 2 C6.67 2.66 6.34 3.32 6 4 C3.03 3.505 3.03 3.505 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BFAAF7" transform="translate(596,881)"/>
<path d="M0 0 C7.385 0.492 7.385 0.492 10.5 3.062 C10.995 3.702 11.49 4.341 12 5 C8.367 5 7.029 3.921 4 2 C2.674 1.319 1.343 0.647 0 0 Z " fill="#F2EAFD" transform="translate(707,854)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C1.02 2.33 -0.96 2.66 -3 3 C-3.33 3.99 -3.66 4.98 -4 6 C-4.66 6 -5.32 6 -6 6 C-5.812 4.125 -5.812 4.125 -5 2 C-2.625 0.625 -2.625 0.625 0 0 Z " fill="#9E90E8" transform="translate(550,851)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C2.66 3.67 3.32 3.34 4 3 C4 3.66 4 4.32 4 5 C4.99 5.33 5.98 5.66 7 6 C7 6.33 7 6.66 7 7 C4.625 7.125 4.625 7.125 2 7 C0 5 0 5 -0.125 2.375 C-0.084 1.591 -0.043 0.808 0 0 Z " fill="#7C6CDC" transform="translate(633,898)"/>
<path d="M0 0 C-1.29 1.376 -2.627 2.708 -4 4 C-4.66 4 -5.32 4 -6 4 C-6 3.34 -6 2.68 -6 2 C-6.99 1.67 -7.98 1.34 -9 1 C-5.574 -0.618 -3.619 -1.341 0 0 Z " fill="#BCA7F5" transform="translate(609,892)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.99 3 2.98 3 4 C2.34 4 1.68 4 1 4 C0.67 4.99 0.34 5.98 0 7 C-0.934 3.99 -1.044 3.133 0 0 Z " fill="#EDE4FD" transform="translate(297,872)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.33 0.99 5.66 1.98 6 3 C3.36 3.33 0.72 3.66 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#867BDE" transform="translate(170,851)"/>
<path d="M0 0 C1.65 1.65 3.3 3.3 5 5 C4.67 5.66 4.34 6.32 4 7 C3.01 7 2.02 7 1 7 C0.67 4.69 0.34 2.38 0 0 Z " fill="#7870CC" transform="translate(315,561)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C0.34 10 -0.32 10 -1 10 C-2.467 6.185 -1.433 3.71 0 0 Z " fill="#6961C1" transform="translate(530,494)"/>
<path d="M0 0 C1.461 2.647 2 3.894 2 7 C2.99 7.33 3.98 7.66 5 8 C3.35 8.33 1.7 8.66 0 9 C-1.108 5.675 -0.845 3.378 0 0 Z " fill="#9D9BFE" transform="translate(516,220)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.99 3 2.98 3 4 C-0.3 4 -3.6 4 -7 4 C-7 3.67 -7 3.34 -7 3 C-4.69 3 -2.38 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9191F7" transform="translate(512,152)"/>
<path d="M0 0 C3.3 0.33 6.6 0.66 10 1 C8.68 1.99 7.36 2.98 6 4 C3.532 2.848 1.952 1.952 0 0 Z " fill="#7678E7" transform="translate(528,124)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-2.312 4.75 -2.312 4.75 -5 6 C-5.99 5.67 -6.98 5.34 -8 5 C-2.25 0 -2.25 0 0 0 Z " fill="#615AC3" transform="translate(367,750)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 2.31 3 4.62 3 7 C2.01 7 1.02 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#E7DDF9" transform="translate(497,707)"/>
<path d="M0 0 C3.784 0.925 5.567 1.394 7.875 4.625 C8.432 5.801 8.432 5.801 9 7 C5.183 5.445 2.808 2.964 0 0 Z " fill="#F3EDFC" transform="translate(803,490)"/>
<path d="M0 0 C3.653 1.25 4.781 1.671 7 5 C4.03 4.505 4.03 4.505 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#7779E6" transform="translate(585,147)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#BBA9F6" transform="translate(434,873)"/>
<path d="M0 0 C4.548 -0.178 7.879 0.002 12 2 C8.703 3.099 7.595 2.871 4.312 2.062 C3.504 1.868 2.696 1.673 1.863 1.473 C0.941 1.239 0.941 1.239 0 1 C0 0.67 0 0.34 0 0 Z " fill="#9896FB" transform="translate(519,125)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.62 1 9.24 1 14 C0.67 14 0.34 14 0 14 C0 9.38 0 4.76 0 0 Z " fill="#B7A5F3" transform="translate(471,882)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 4.63 1.34 8.26 1 12 C0.67 12 0.34 12 0 12 C0 8.04 0 4.08 0 0 Z " fill="#BCA9F7" transform="translate(445,886)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.97 1.66 5.94 2 9 C1.01 8.67 0.02 8.34 -1 8 C-1.042 5.667 -1.041 3.333 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D0C1FA" transform="translate(513,706)"/>
<path d="M0 0 C2.236 3.353 2.173 3.907 1.625 7.688 C1.514 8.496 1.403 9.304 1.289 10.137 C1.194 10.752 1.098 11.366 1 12 C0.67 12 0.34 12 0 12 C0 8.04 0 4.08 0 0 Z " fill="#9896FC" transform="translate(516,154)"/>
<path d="M0 0 C2.962 0.613 4.381 1.254 7 3 C4.69 2.67 2.38 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BFABF7" transform="translate(490,898)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.32 2.67 -2.64 2.34 -4 2 C-2.68 1.34 -1.36 0.68 0 0 Z " fill="#B9A5F2" transform="translate(221,893)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C-0.062 5.188 -0.062 5.188 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#BBA6F6" transform="translate(373,875)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 2.31 2 4.62 2 7 C1.67 7 1.34 7 1 7 C0.67 4.69 0.34 2.38 0 0 Z " fill="#BBA8F5" transform="translate(727,877)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 0.66 5.34 1.32 5 2 C3.35 1.67 1.7 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BFB0F5" transform="translate(330,764)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#9794FC" transform="translate(597,251)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C1.68 1.67 0.36 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BBA6F4" transform="translate(501,910)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#BDAAF6" transform="translate(544,889)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.67 2.99 1.34 3.98 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BBA7F6" transform="translate(772,883)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.01 -0.32 4.02 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#D1C0F6" transform="translate(692,474)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C1.02 2 -0.96 2 -3 2 C-2.01 1.34 -1.02 0.68 0 0 Z " fill="#9F9CFA" transform="translate(557,248)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 1.33 5 1.66 5 2 C3.35 2 1.7 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9895FC" transform="translate(551,132)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.99 2.34 1.98 2 3 C1.34 2.01 0.68 1.02 0 0 Z " fill="#BAA7F6" transform="translate(223,893)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 3.68 -0.32 2.36 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BDAAF8" transform="translate(729,890)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.68 1.33 1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BDA8F8" transform="translate(500,878)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.01 3.495 0.01 3.495 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#BAA7F6" transform="translate(813,876)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#BBA8F8" transform="translate(555,865)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#BCA9F4" transform="translate(544,858)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.01 3.33 -0.98 3.66 -2 4 C-2 3.34 -2 2.68 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B5A2F3" transform="translate(743,849)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.01 2.67 -0.98 2.34 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C7B7F7" transform="translate(610,749)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#CDBDF8" transform="translate(641,722)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.99 3 1.98 3 3 C1 2 1 2 0 0 Z " fill="#CBBCF9" transform="translate(313,709)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2 -0.32 2 -1 2 C-1.33 2.66 -1.66 3.32 -2 4 C-2 3.01 -2 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#C1B6F5" transform="translate(202,572)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.67 2.99 1.34 3.98 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#D9C8F9" transform="translate(650,513)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.67 0.66 3.34 1.32 3 2 C2.01 1.67 1.02 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CABBF9" transform="translate(239,490)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.68 1.33 1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C2B4F5" transform="translate(464,465)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.68 1.33 1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9D99FB" transform="translate(563,262)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#9998FB" transform="translate(381,230)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 1.66 2.34 2.32 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9697FA" transform="translate(445,141)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-1.32 1.67 -2.64 1.34 -4 1 C-2 0 -2 0 0 0 Z " fill="#9596FA" transform="translate(444,142)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#B29DF4" transform="translate(287,908)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BBA7F6" transform="translate(445,899)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#BDA9F8" transform="translate(635,896)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#BFABF7" transform="translate(328,890)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BEABF8" transform="translate(434,889)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#B8A4F6" transform="translate(517,884)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.01 3.495 1.01 3.495 0 4 C0 2.68 0 1.36 0 0 Z " fill="#BDAAF7" transform="translate(740,876)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.34 -0.32 2.68 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C1AFF8" transform="translate(713,867)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#BCA9F8" transform="translate(635,859)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BBA8F6" transform="translate(722,857)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C6B6F8" transform="translate(452,763)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C7B5F6" transform="translate(699,747)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.34 2.34 -0.32 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CCBAF9" transform="translate(524,740)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#CEC0F7" transform="translate(601,708)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.01 3.495 1.01 3.495 0 4 C0 2.68 0 1.36 0 0 Z " fill="#CCBDF8" transform="translate(541,705)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C2.68 2 1.36 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CFC0F8" transform="translate(434,670)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C1.68 1.67 0.36 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CCBBF9" transform="translate(586,642)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C2.68 1.67 1.36 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CABCF8" transform="translate(338,641)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.34 -0.32 2.68 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D5C4F7" transform="translate(692,483)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 1.33 2.02 1.66 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BFB0F1" transform="translate(712,465)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.99 2.34 1.98 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#C0B1F3" transform="translate(548,464)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9593F9" transform="translate(596,352)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.01 3.495 0.01 3.495 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#9695FA" transform="translate(633,296)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#9E9CFC" transform="translate(570,249)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#9697FA" transform="translate(382,224)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.01 3.495 1.01 3.495 0 4 C0 2.68 0 1.36 0 0 Z " fill="#9B97FD" transform="translate(516,212)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#9B99FE" transform="translate(516,203)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#9A98FC" transform="translate(516,171)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2.33 2.32 2.66 3 3 C2.01 3 1.02 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9693FB" transform="translate(600,161)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9798FA" transform="translate(420,158)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C1.68 1.67 0.36 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9696FA" transform="translate(508,152)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#9895FC" transform="translate(503,125)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.68 1.33 0.36 1.66 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#9998FC" transform="translate(497,125)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#BCA9F5" transform="translate(505,911)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B6A0F4" transform="translate(644,907)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#B9A3F5" transform="translate(625,906)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BAA5F6" transform="translate(428,904)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#B3A0F3" transform="translate(313,901)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BCA9F4" transform="translate(544,897)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BAA7F4" transform="translate(709,895)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BCA7F6" transform="translate(635,883)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C1ACF7" transform="translate(544,881)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#BDA7F8" transform="translate(586,883)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BCA8F5" transform="translate(301,877)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#BBA6F5" transform="translate(603,876)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BBA7F5" transform="translate(491,876)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#B9A5F6" transform="translate(477,876)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BAA6F6" transform="translate(387,876)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BBA8F3" transform="translate(227,876)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BDA9F9" transform="translate(772,873)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BCA8F5" transform="translate(494,875)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BBA7F5" transform="translate(348,875)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#B7A3F3" transform="translate(219,875)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BEAAF7" transform="translate(724,873)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B2A0F3" transform="translate(238,870)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BEAAF5" transform="translate(741,869)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BFAAF8" transform="translate(635,867)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B19FF1" transform="translate(236,868)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B8A4F4" transform="translate(400,865)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BAA7F7" transform="translate(724,861)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CDBCF9" transform="translate(484,748)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#D1C0FA" transform="translate(336,739)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CABAF9" transform="translate(394,733)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CBBDF8" transform="translate(318,710)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CCBCF7" transform="translate(689,680)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CBBCF8" transform="translate(460,642)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C6B7F9" transform="translate(328,581)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D1C0FA" transform="translate(384,536)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D6C6FB" transform="translate(490,532)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#DECDFB" transform="translate(692,523)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D4C3FA" transform="translate(669,496)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BCAFF1" transform="translate(801,466)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#9794FA" transform="translate(604,344)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9695FA" transform="translate(397,321)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#9898FC" transform="translate(511,267)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9C9AFC" transform="translate(560,262)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#9999FC" transform="translate(483,253)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#9898FE" transform="translate(523,251)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#9D9CFD" transform="translate(540,248)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#9A9AFC" transform="translate(501,245)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#9793FA" transform="translate(637,228)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#9797FD" transform="translate(388,204)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#928EF6" transform="translate(628,172)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9997FC" transform="translate(516,167)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9696FA" transform="translate(413,165)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#AD9AEF" transform="translate(486,926)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B69EF4" transform="translate(480,922)"/>
<path d="" fill="#B4A0F3" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B6A0F5" transform="translate(363,910)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B29CF3" transform="translate(283,910)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BAA7F4" transform="translate(726,906)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B6A1F5" transform="translate(364,907)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B49EF0" transform="translate(248,906)"/>
<path d="" fill="#BCAAF6" transform="translate(0,0)"/>
<path d="" fill="#BDA7F7" transform="translate(0,0)"/>
<path d="" fill="#B7A4F3" transform="translate(0,0)"/>
<path d="" fill="#BBA7F7" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B8A6F6" transform="translate(503,896)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BEA9F8" transform="translate(496,897)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BEABF6" transform="translate(341,896)"/>
<path d="" fill="#BBA6F8" transform="translate(0,0)"/>
<path d="" fill="#BDAAF6" transform="translate(0,0)"/>
<path d="" fill="#B6A3F8" transform="translate(0,0)"/>
<path d="" fill="#BDA9F7" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BEABF7" transform="translate(817,886)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BDA8F8" transform="translate(340,886)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BCA7F9" transform="translate(587,885)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BFA9F5" transform="translate(460,884)"/>
<path d="" fill="#BDABF9" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B9A5F5" transform="translate(216,883)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BBA8F5" transform="translate(818,882)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BFADF7" transform="translate(706,882)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BEABF8" transform="translate(356,878)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BFAAF5" transform="translate(457,876)"/>
<path d="" fill="#BCA9F7" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B8A4F4" transform="translate(310,876)"/>
<path d="" fill="#BAA7F4" transform="translate(0,0)"/>
<path d="" fill="#BCA7F5" transform="translate(0,0)"/>
<path d="" fill="#B9A7F7" transform="translate(0,0)"/>
<path d="" fill="#BBA6F7" transform="translate(0,0)"/>
<path d="" fill="#B7A3F5" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B9A6F6" transform="translate(613,864)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B09EF1" transform="translate(212,865)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BCA8F5" transform="translate(730,864)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BFAAF9" transform="translate(416,864)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BAA7F4" transform="translate(395,864)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B8A5F4" transform="translate(341,864)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B9A5F4" transform="translate(749,861)"/>
<path d="" fill="#B9A5F6" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BFACF6" transform="translate(571,861)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BEACF5" transform="translate(567,861)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B9A6F5" transform="translate(696,851)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B6A3F2" transform="translate(796,849)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B6A3F3" transform="translate(745,850)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B6A4F6" transform="translate(580,849)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A798EC" transform="translate(173,849)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B6A3F4" transform="translate(756,848)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B5A2F5" transform="translate(739,848)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BFAFF6" transform="translate(325,766)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C1B2F7" transform="translate(568,764)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C5B4F8" transform="translate(582,763)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C0B1F6" transform="translate(314,762)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C1B1F5" transform="translate(294,749)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CFBEFA" transform="translate(516,724)"/>
<path d="" fill="#D0C0FB" transform="translate(0,0)"/>
<path d="" fill="#D1C0F8" transform="translate(0,0)"/>
<path d="" fill="#CFC2F8" transform="translate(0,0)"/>
<path d="" fill="#D4C4FB" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D2C2F9" transform="translate(473,689)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D1BFF9" transform="translate(581,670)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D1BEFA" transform="translate(578,669)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CDBCF9" transform="translate(561,669)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CABAF8" transform="translate(541,648)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CDBCF9" transform="translate(552,643)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CBBCF7" transform="translate(432,642)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C9B8F6" transform="translate(443,641)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C7B8F5" transform="translate(717,582)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CABCF8" transform="translate(331,579)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CEBFF9" transform="translate(476,571)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C9BAF9" transform="translate(405,566)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D7C8FB" transform="translate(577,564)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D3C2FD" transform="translate(673,556)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CDBCF9" transform="translate(739,556)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CBBCF8" transform="translate(308,554)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C2B5F7" transform="translate(187,549)"/>
<path d="" fill="#D2C3F9" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D5C4FA" transform="translate(466,542)"/>
<path d="" fill="#D1C2F8" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CEBEF7" transform="translate(812,537)"/>
<path d="" fill="#D1C1F8" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CABAF5" transform="translate(797,514)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CBBCF9" transform="translate(380,502)"/>
<path d="" fill="#C6B8F9" transform="translate(0,0)"/>
<path d="" fill="#D4C4FB" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C0B2F2" transform="translate(692,468)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BDB3F5" transform="translate(258,466)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C0B2F8" transform="translate(512,466)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BFB3F4" transform="translate(254,466)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C1B4F3" transform="translate(778,465)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9F97FB" transform="translate(544,397)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A29AFA" transform="translate(551,394)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9D98FB" transform="translate(470,395)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9C98FB" transform="translate(453,389)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9997F8" transform="translate(533,384)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9997FB" transform="translate(436,363)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9692F8" transform="translate(607,362)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9696F9" transform="translate(421,352)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9897FB" transform="translate(385,296)"/>
<path d="" fill="#9796F8" transform="translate(0,0)"/>
<path d="" fill="#9895FB" transform="translate(0,0)"/>
<path d="" fill="#9395F8" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9898F9" transform="translate(502,264)"/>
<path d="" fill="#9392F8" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9B99FD" transform="translate(576,263)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9C9AFD" transform="translate(553,262)"/>
<path d="" fill="#9C9CFD" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9B99FB" transform="translate(588,249)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9E9DFC" transform="translate(565,249)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9F9DFC" transform="translate(548,249)"/>
<path d="" fill="#9898FB" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9A99FC" transform="translate(521,248)"/>
<path d="" fill="#999AFC" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9B99FD" transform="translate(500,227)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9391FA" transform="translate(650,224)"/>
<path d="" fill="#9794FB" transform="translate(0,0)"/>
<path d="" fill="#8F8EF5" transform="translate(0,0)"/>
<path d="" fill="#908EF6" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9692FA" transform="translate(628,202)"/>
<path d="" fill="#9B99FD" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9594FA" transform="translate(398,184)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9795FA" transform="translate(592,154)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9895FE" transform="translate(549,134)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9797F9" transform="translate(456,135)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9693FA" transform="translate(556,134)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9793FB" transform="translate(548,131)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9795FB" transform="translate(468,131)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9595F8" transform="translate(438,128)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9495F8" transform="translate(454,120)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9596F9" transform="translate(471,115)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9494F7" transform="translate(542,114)"/>
<path d="" fill="#B49EF3" transform="translate(0,0)"/>
<path d="" fill="#BBA6F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8A3F5" transform="translate(502,915)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7A0F5" transform="translate(489,915)"/>
<path d="" fill="#BAA5F7" transform="translate(0,0)"/>
<path d="" fill="#B49EF3" transform="translate(0,0)"/>
<path d="" fill="#B29CF3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AC98EB" transform="translate(268,911)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBA7F2" transform="translate(504,909)"/>
<path d="" fill="#B4A0F3" transform="translate(0,0)"/>
<path d="" fill="#B39DF4" transform="translate(0,0)"/>
<path d="" fill="#BAA5F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AA94EF" transform="translate(854,903)"/>
<path d="" fill="#BBA9F5" transform="translate(0,0)"/>
<path d="" fill="#BBAAF6" transform="translate(0,0)"/>
<path d="" fill="#BAA7F6" transform="translate(0,0)"/>
<path d="" fill="#B6A4F4" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9A5F8" transform="translate(348,902)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7A4F4" transform="translate(792,901)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BAA6F4" transform="translate(603,901)"/>
<path d="" fill="#B6A4F6" transform="translate(0,0)"/>
<path d="" fill="#B6A3F2" transform="translate(0,0)"/>
<path d="" fill="#B7A2F6" transform="translate(0,0)"/>
<path d="" fill="#BAA9F4" transform="translate(0,0)"/>
<path d="" fill="#BDAAF5" transform="translate(0,0)"/>
<path d="" fill="#BCA8F1" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9A7F5" transform="translate(757,897)"/>
<path d="" fill="#BBA9F3" transform="translate(0,0)"/>
<path d="" fill="#BCA9F4" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEABF5" transform="translate(593,895)"/>
<path d="" fill="#BCA8F7" transform="translate(0,0)"/>
<path d="" fill="#B6A4F4" transform="translate(0,0)"/>
<path d="" fill="#BEABF6" transform="translate(0,0)"/>
<path d="" fill="#C5B3F9" transform="translate(0,0)"/>
<path d="" fill="#BBA7F8" transform="translate(0,0)"/>
<path d="" fill="#BAA6F9" transform="translate(0,0)"/>
<path d="" fill="#C0AEF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9A4F9" transform="translate(286,887)"/>
<path d="" fill="#BCA7F6" transform="translate(0,0)"/>
<path d="" fill="#B4A0F7" transform="translate(0,0)"/>
<path d="" fill="#C1ADF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BAA8F6" transform="translate(740,881)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDA9F8" transform="translate(428,881)"/>
<path d="" fill="#BEAAF7" transform="translate(0,0)"/>
<path d="" fill="#BDABF7" transform="translate(0,0)"/>
<path d="" fill="#B9A7F2" transform="translate(0,0)"/>
<path d="" fill="#BAA7F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDAAF4" transform="translate(397,879)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBA7FA" transform="translate(563,878)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8A6F4" transform="translate(342,878)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BCA8F7" transform="translate(495,877)"/>
<path d="" fill="#BDA8F8" transform="translate(0,0)"/>
<path d="" fill="#BBA7F9" transform="translate(0,0)"/>
<path d="" fill="#BAA8F5" transform="translate(0,0)"/>
<path d="" fill="#B7A4F2" transform="translate(0,0)"/>
<path d="" fill="#B8A3F8" transform="translate(0,0)"/>
<path d="" fill="#B9A6F7" transform="translate(0,0)"/>
<path d="" fill="#BFAEF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8A5F6" transform="translate(365,871)"/>
<path d="" fill="#B6A4F3" transform="translate(0,0)"/>
<path d="" fill="#B9A5F6" transform="translate(0,0)"/>
<path d="" fill="#B7A4F2" transform="translate(0,0)"/>
<path d="" fill="#B3A1F3" transform="translate(0,0)"/>
<path d="" fill="#B7A3F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDAAF7" transform="translate(724,865)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3A1F3" transform="translate(236,865)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6A1F4" transform="translate(288,864)"/>
<path d="" fill="#BAA9F6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8A5F2" transform="translate(789,862)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFADF6" transform="translate(560,861)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7A2F4" transform="translate(734,858)"/>
<path d="" fill="#B29EF1" transform="translate(0,0)"/>
<path d="" fill="#B5A2F5" transform="translate(0,0)"/>
<path d="" fill="#B3A1F3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8A4F6" transform="translate(739,853)"/>
<path d="" fill="#B6A4F6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6A1F1" transform="translate(792,850)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B4A0F2" transform="translate(789,850)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3B2F8" transform="translate(572,764)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFAEF4" transform="translate(324,764)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7B6F8" transform="translate(559,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6B6F8" transform="translate(342,763)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D2C0FC" transform="translate(557,762)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CEBFF7" transform="translate(705,759)"/>
<path d="" fill="#C3B3F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2B3F5" transform="translate(301,756)"/>
<path d="" fill="#C2B1F5" transform="translate(0,0)"/>
<path d="" fill="#C8B9F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9B8FA" transform="translate(484,751)"/>
<path d="" fill="#CCBDF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6B7F8" transform="translate(403,749)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8B8F8" transform="translate(405,748)"/>
<path d="" fill="#CAB9F8" transform="translate(0,0)"/>
<path d="" fill="#C4B5F5" transform="translate(0,0)"/>
<path d="" fill="#C8B8F5" transform="translate(0,0)"/>
<path d="" fill="#CBBCF8" transform="translate(0,0)"/>
<path d="" fill="#CFC1F6" transform="translate(0,0)"/>
<path d="" fill="#C7B6FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CEBEF8" transform="translate(341,738)"/>
<path d="" fill="#CCBDF7" transform="translate(0,0)"/>
<path d="" fill="#CDBDF7" transform="translate(0,0)"/>
<path d="" fill="#CDBDF9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D2C0FB" transform="translate(516,727)"/>
<path d="" fill="#D0BEF9" transform="translate(0,0)"/>
<path d="" fill="#C7B6F7" transform="translate(0,0)"/>
<path d="" fill="#D3C2FA" transform="translate(0,0)"/>
<path d="" fill="#D4C1F9" transform="translate(0,0)"/>
<path d="" fill="#CDBEF8" transform="translate(0,0)"/>
<path d="" fill="#D0C1F8" transform="translate(0,0)"/>
<path d="" fill="#CCBCF8" transform="translate(0,0)"/>
<path d="" fill="#D6C8F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3B6F8" transform="translate(296,703)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CABAF8" transform="translate(298,701)"/>
<path d="" fill="#D1C5F7" transform="translate(0,0)"/>
<path d="" fill="#CBBCF9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D3C3F5" transform="translate(358,696)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFBFFA" transform="translate(350,693)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDBBF7" transform="translate(347,692)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFBFF9" transform="translate(340,690)"/>
<path d="" fill="#CEBFF9" transform="translate(0,0)"/>
<path d="" fill="#CCBBF8" transform="translate(0,0)"/>
<path d="" fill="#CFC1F8" transform="translate(0,0)"/>
<path d="" fill="#D1BFFC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D5C3F8" transform="translate(556,671)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D3C1F8" transform="translate(558,670)"/>
<path d="" fill="#CFBFF9" transform="translate(0,0)"/>
<path d="" fill="#C6B7F6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBBBF8" transform="translate(441,668)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D4C4FA" transform="translate(446,667)"/>
<path d="" fill="#D2C1FB" transform="translate(0,0)"/>
<path d="" fill="#CDBDF9" transform="translate(0,0)"/>
<path d="" fill="#CABCF8" transform="translate(0,0)"/>
<path d="" fill="#CEBDFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9BAFA" transform="translate(547,645)"/>
<path d="" fill="#CBBCFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CABBF7" transform="translate(347,644)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C5B6F6" transform="translate(308,644)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7BAF8" transform="translate(344,643)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9B9F8" transform="translate(457,642)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2B1F2" transform="translate(804,583)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CAB9F8" transform="translate(766,582)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8BAF7" transform="translate(605,582)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6B8F4" transform="translate(502,582)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CCBCF8" transform="translate(381,582)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7BBF7" transform="translate(337,582)"/>
<path d="" fill="#CCBBF9" transform="translate(0,0)"/>
<path d="" fill="#CDBDF9" transform="translate(0,0)"/>
<path d="" fill="#CABCF9" transform="translate(0,0)"/>
<path d="" fill="#D4C5FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDB1F5" transform="translate(192,565)"/>
<path d="" fill="#C4B5F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CEBEFB" transform="translate(312,563)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D4C4FA" transform="translate(509,562)"/>
<path d="" fill="#BDAEF1" transform="translate(0,0)"/>
<path d="" fill="#CBBCF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDBCF4" transform="translate(789,560)"/>
<path d="" fill="#CBBCF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D3C3F4" transform="translate(361,560)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D3C3F5" transform="translate(238,560)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFC0FC" transform="translate(672,559)"/>
<path d="" fill="#D7C7FD" transform="translate(0,0)"/>
<path d="" fill="#D8C3F8" transform="translate(0,0)"/>
<path d="" fill="#CEBFF7" transform="translate(0,0)"/>
<path d="" fill="#D1C2FA" transform="translate(0,0)"/>
<path d="" fill="#D7C7F8" transform="translate(0,0)"/>
<path d="" fill="#CABAF8" transform="translate(0,0)"/>
<path d="" fill="#D7C7FD" transform="translate(0,0)"/>
<path d="" fill="#D4C2F9" transform="translate(0,0)"/>
<path d="" fill="#BFB1F2" transform="translate(0,0)"/>
<path d="" fill="#C7B8F8" transform="translate(0,0)"/>
<path d="" fill="#C5B9F3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D1BFFB" transform="translate(659,534)"/>
<path d="" fill="#C4B6F5" transform="translate(0,0)"/>
<path d="" fill="#D7C7FD" transform="translate(0,0)"/>
<path d="" fill="#D2C1F9" transform="translate(0,0)"/>
<path d="" fill="#D5C4F9" transform="translate(0,0)"/>
<path d="" fill="#CFBEF7" transform="translate(0,0)"/>
<path d="" fill="#D2C2F6" transform="translate(0,0)"/>
<path d="" fill="#D9C8FE" transform="translate(0,0)"/>
<path d="" fill="#D6C5FA" transform="translate(0,0)"/>
<path d="" fill="#D5C6F9" transform="translate(0,0)"/>
<path d="" fill="#D2C2FA" transform="translate(0,0)"/>
<path d="" fill="#DED0FE" transform="translate(0,0)"/>
<path d="" fill="#CEBDF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDC0FB" transform="translate(486,511)"/>
<path d="" fill="#D3C3FD" transform="translate(0,0)"/>
<path d="" fill="#CDBEFC" transform="translate(0,0)"/>
<path d="" fill="#DBCDFD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7B9F6" transform="translate(264,509)"/>
<path d="" fill="#C9BBFA" transform="translate(0,0)"/>
<path d="" fill="#D2C3FA" transform="translate(0,0)"/>
<path d="" fill="#D1C1F8" transform="translate(0,0)"/>
<path d="" fill="#CBBBF9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CCBDF7" transform="translate(738,498)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFBEFB" transform="translate(348,495)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CEBEF6" transform="translate(222,495)"/>
<path d="" fill="#CCBCF8" transform="translate(0,0)"/>
<path d="" fill="#D0C1F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CCBDF8" transform="translate(371,493)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CCBFF7" transform="translate(251,493)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDBEF9" transform="translate(369,492)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CCBCF8" transform="translate(248,492)"/>
<path d="" fill="#D2C2FB" transform="translate(0,0)"/>
<path d="" fill="#CCBDF7" transform="translate(0,0)"/>
<path d="" fill="#CDBEF9" transform="translate(0,0)"/>
<path d="" fill="#C7B8F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BAB0F1" transform="translate(198,481)"/>
<path d="" fill="#CBBBF5" transform="translate(0,0)"/>
<path d="" fill="#C1B4F5" transform="translate(0,0)"/>
<path d="" fill="#CCBDF7" transform="translate(0,0)"/>
<path d="" fill="#CEBFF9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CABAF9" transform="translate(390,473)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CCBEF6" transform="translate(576,471)"/>
<path d="" fill="#CBBBF6" transform="translate(0,0)"/>
<path d="" fill="#BDB2F3" transform="translate(0,0)"/>
<path d="" fill="#C0B2F5" transform="translate(0,0)"/>
<path d="" fill="#C6B6F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6B3F5" transform="translate(545,465)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEB3F2" transform="translate(229,465)"/>
<path d="" fill="#A098FC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A19DF8" transform="translate(528,399)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9E9AFA" transform="translate(536,398)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9C97F9" transform="translate(541,397)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A29DFC" transform="translate(468,394)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9A96FC" transform="translate(512,386)"/>
<path d="" fill="#9A99FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9A96FA" transform="translate(514,385)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9A98F9" transform="translate(434,379)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9C97FC" transform="translate(464,378)"/>
<path d="" fill="#9A95FD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9A98FA" transform="translate(444,369)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9797FA" transform="translate(442,368)"/>
<path d="" fill="#9794FA" transform="translate(0,0)"/>
<path d="" fill="#9595F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9793F9" transform="translate(619,350)"/>
<path d="" fill="#9690F9" transform="translate(0,0)"/>
<path d="" fill="#9694FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9494F8" transform="translate(384,329)"/>
<path d="" fill="#9793FA" transform="translate(0,0)"/>
<path d="" fill="#9796FA" transform="translate(0,0)"/>
<path d="" fill="#9491F5" transform="translate(0,0)"/>
<path d="" fill="#9595F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9897FA" transform="translate(382,283)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9D9AFC" transform="translate(510,282)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9B99FA" transform="translate(507,282)"/>
<path d="" fill="#9692FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9A98FC" transform="translate(527,279)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9896FB" transform="translate(533,270)"/>
<path d="" fill="#9A97FC" transform="translate(0,0)"/>
<path d="" fill="#9B9AFE" transform="translate(0,0)"/>
<path d="" fill="#9C9AFD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9B98FC" transform="translate(549,263)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9B9AFC" transform="translate(544,263)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9B98FB" transform="translate(540,263)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9D9CFD" transform="translate(579,262)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9B9AFD" transform="translate(571,262)"/>
<path d="" fill="#9898FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9F9DFC" transform="translate(583,249)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9E9EFC" transform="translate(580,249)"/>
<path d="" fill="#9B9CFD" transform="translate(0,0)"/>
<path d="" fill="#9796F7" transform="translate(0,0)"/>
<path d="" fill="#9797FB" transform="translate(0,0)"/>
<path d="" fill="#9797FB" transform="translate(0,0)"/>
<path d="" fill="#9A9BFC" transform="translate(0,0)"/>
<path d="" fill="#9998FD" transform="translate(0,0)"/>
<path d="" fill="#9793FB" transform="translate(0,0)"/>
<path d="" fill="#9797FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9692FB" transform="translate(635,225)"/>
<path d="" fill="#9C99FE" transform="translate(0,0)"/>
<path d="" fill="#9695FB" transform="translate(0,0)"/>
<path d="" fill="#9192F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9394F8" transform="translate(368,211)"/>
<path d="" fill="#9494F7" transform="translate(0,0)"/>
<path d="" fill="#9595FB" transform="translate(0,0)"/>
<path d="" fill="#9596F9" transform="translate(0,0)"/>
<path d="" fill="#9998FC" transform="translate(0,0)"/>
<path d="" fill="#9696FC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9190F8" transform="translate(636,185)"/>
<path d="" fill="#9A97FA" transform="translate(0,0)"/>
<path d="" fill="#9B98FE" transform="translate(0,0)"/>
<path d="" fill="#8E8DF3" transform="translate(0,0)"/>
<path d="" fill="#9997FB" transform="translate(0,0)"/>
<path d="" fill="#9193F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9693FB" transform="translate(609,172)"/>
<path d="" fill="#928EF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9393F9" transform="translate(604,166)"/>
<path d="" fill="#938FF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9793FB" transform="translate(580,150)"/>
<path d="" fill="#9999FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9693FD" transform="translate(569,140)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9998F8" transform="translate(560,135)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9697F9" transform="translate(459,134)"/>
<path d="" fill="#9496F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9696FD" transform="translate(504,127)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9594F8" transform="translate(451,120)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9695FA" transform="translate(464,117)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9593FA" transform="translate(547,116)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9695F9" transform="translate(475,114)"/>
<path d="" fill="#8B75DB" transform="translate(0,0)"/>
<path d="" fill="#AD98F1" transform="translate(0,0)"/>
<path d="" fill="#B59DF2" transform="translate(0,0)"/>
<path d="" fill="#B6A0F3" transform="translate(0,0)"/>
<path d="" fill="#B19DF6" transform="translate(0,0)"/>
<path d="" fill="#B29CF3" transform="translate(0,0)"/>
<path d="" fill="#B9A4F5" transform="translate(0,0)"/>
<path d="" fill="#BAA4F5" transform="translate(0,0)"/>
<path d="" fill="#B59FF2" transform="translate(0,0)"/>
<path d="" fill="#B49CF3" transform="translate(0,0)"/>
<path d="" fill="#B29CF5" transform="translate(0,0)"/>
<path d="" fill="#BAA4F6" transform="translate(0,0)"/>
<path d="" fill="#B59FF5" transform="translate(0,0)"/>
<path d="" fill="#BBA4F7" transform="translate(0,0)"/>
<path d="" fill="#B09BF5" transform="translate(0,0)"/>
<path d="" fill="#B19EF5" transform="translate(0,0)"/>
<path d="" fill="#B69EF3" transform="translate(0,0)"/>
<path d="" fill="#B4A1EF" transform="translate(0,0)"/>
<path d="" fill="#B39EF4" transform="translate(0,0)"/>
<path d="" fill="#B59EF5" transform="translate(0,0)"/>
<path d="" fill="#B9A2F3" transform="translate(0,0)"/>
<path d="" fill="#BBA6F3" transform="translate(0,0)"/>
<path d="" fill="#B29CF3" transform="translate(0,0)"/>
<path d="" fill="#B2A1F0" transform="translate(0,0)"/>
<path d="" fill="#AA99F0" transform="translate(0,0)"/>
<path d="" fill="#AB95F0" transform="translate(0,0)"/>
<path d="" fill="#B4A0F5" transform="translate(0,0)"/>
<path d="" fill="#B7A3F4" transform="translate(0,0)"/>
<path d="" fill="#B7A4F5" transform="translate(0,0)"/>
<path d="" fill="#B59EF5" transform="translate(0,0)"/>
<path d="" fill="#AE9BEE" transform="translate(0,0)"/>
<path d="" fill="#B6A0F4" transform="translate(0,0)"/>
<path d="" fill="#B9A3F4" transform="translate(0,0)"/>
<path d="" fill="#BBA5F4" transform="translate(0,0)"/>
<path d="" fill="#B5A0F6" transform="translate(0,0)"/>
<path d="" fill="#B7A2F5" transform="translate(0,0)"/>
<path d="" fill="#BBA8F1" transform="translate(0,0)"/>
<path d="" fill="#B9A3F6" transform="translate(0,0)"/>
<path d="" fill="#B7A1F7" transform="translate(0,0)"/>
<path d="" fill="#B6A1F5" transform="translate(0,0)"/>
<path d="" fill="#AB9AEF" transform="translate(0,0)"/>
<path d="" fill="#BAA3F6" transform="translate(0,0)"/>
<path d="" fill="#BBA7F3" transform="translate(0,0)"/>
<path d="" fill="#B6A2F5" transform="translate(0,0)"/>
<path d="" fill="#B5A1F4" transform="translate(0,0)"/>
<path d="" fill="#B39DF3" transform="translate(0,0)"/>
<path d="" fill="#B7A4F3" transform="translate(0,0)"/>
<path d="" fill="#B5A1F6" transform="translate(0,0)"/>
<path d="" fill="#B3A0F4" transform="translate(0,0)"/>
<path d="" fill="#B49DF5" transform="translate(0,0)"/>
<path d="" fill="#AB9AF2" transform="translate(0,0)"/>
<path d="" fill="#B6A1F6" transform="translate(0,0)"/>
<path d="" fill="#B29FF6" transform="translate(0,0)"/>
<path d="" fill="#B8A2F5" transform="translate(0,0)"/>
<path d="" fill="#BBA8FA" transform="translate(0,0)"/>
<path d="" fill="#B6A3F5" transform="translate(0,0)"/>
<path d="" fill="#B8A6F5" transform="translate(0,0)"/>
<path d="" fill="#B7A3F7" transform="translate(0,0)"/>
<path d="" fill="#B9A5F5" transform="translate(0,0)"/>
<path d="" fill="#B6A1F6" transform="translate(0,0)"/>
<path d="" fill="#BAA6F6" transform="translate(0,0)"/>
<path d="" fill="#B3A0F5" transform="translate(0,0)"/>
<path d="" fill="#BAA5F5" transform="translate(0,0)"/>
<path d="" fill="#BAA6F7" transform="translate(0,0)"/>
<path d="" fill="#BCA9FB" transform="translate(0,0)"/>
<path d="" fill="#B6A0F6" transform="translate(0,0)"/>
<path d="" fill="#BAA9F4" transform="translate(0,0)"/>
<path d="" fill="#B6A1F7" transform="translate(0,0)"/>
<path d="" fill="#BDA7F0" transform="translate(0,0)"/>
<path d="" fill="#B4A2F8" transform="translate(0,0)"/>
<path d="" fill="#B4A1F8" transform="translate(0,0)"/>
<path d="" fill="#C1ABF4" transform="translate(0,0)"/>
<path d="" fill="#BCA8F7" transform="translate(0,0)"/>
<path d="" fill="#BDA9F5" transform="translate(0,0)"/>
<path d="" fill="#C0ABF9" transform="translate(0,0)"/>
<path d="" fill="#BAA4F7" transform="translate(0,0)"/>
<path d="" fill="#BCA6F5" transform="translate(0,0)"/>
<path d="" fill="#B4A2F2" transform="translate(0,0)"/>
<path d="" fill="#BCA7F7" transform="translate(0,0)"/>
<path d="" fill="#BDA9F8" transform="translate(0,0)"/>
<path d="" fill="#B9A4FC" transform="translate(0,0)"/>
<path d="" fill="#BCA6F8" transform="translate(0,0)"/>
<path d="" fill="#BBA8F5" transform="translate(0,0)"/>
<path d="" fill="#BEA7F7" transform="translate(0,0)"/>
<path d="" fill="#BAA8F6" transform="translate(0,0)"/>
<path d="" fill="#BCA7FA" transform="translate(0,0)"/>
<path d="" fill="#B8A3F7" transform="translate(0,0)"/>
<path d="" fill="#B9A5F8" transform="translate(0,0)"/>
<path d="" fill="#BCA9F6" transform="translate(0,0)"/>
<path d="" fill="#C0AAF4" transform="translate(0,0)"/>
<path d="" fill="#B7A3F4" transform="translate(0,0)"/>
<path d="" fill="#BEAAF9" transform="translate(0,0)"/>
<path d="" fill="#C0ABF9" transform="translate(0,0)"/>
<path d="" fill="#B8A4F7" transform="translate(0,0)"/>
<path d="" fill="#BAA4F8" transform="translate(0,0)"/>
<path d="" fill="#BBA7F7" transform="translate(0,0)"/>
<path d="" fill="#BCA7F9" transform="translate(0,0)"/>
<path d="" fill="#B8A3F8" transform="translate(0,0)"/>
<path d="" fill="#BFABF4" transform="translate(0,0)"/>
<path d="" fill="#BCA8F4" transform="translate(0,0)"/>
<path d="" fill="#BBA6F6" transform="translate(0,0)"/>
<path d="" fill="#BBAAF3" transform="translate(0,0)"/>
<path d="" fill="#C0ACF7" transform="translate(0,0)"/>
<path d="" fill="#B7A3F6" transform="translate(0,0)"/>
<path d="" fill="#B5A5F7" transform="translate(0,0)"/>
<path d="" fill="#BCA9F6" transform="translate(0,0)"/>
<path d="" fill="#B9A5F5" transform="translate(0,0)"/>
<path d="" fill="#B8A5F1" transform="translate(0,0)"/>
<path d="" fill="#BDA9F6" transform="translate(0,0)"/>
<path d="" fill="#BBA5FA" transform="translate(0,0)"/>
<path d="" fill="#B5A0F7" transform="translate(0,0)"/>
<path d="" fill="#BAA6F9" transform="translate(0,0)"/>
<path d="" fill="#BBA9F6" transform="translate(0,0)"/>
<path d="" fill="#BAA6F3" transform="translate(0,0)"/>
<path d="" fill="#AE9CF4" transform="translate(0,0)"/>
<path d="" fill="#B3A0F4" transform="translate(0,0)"/>
<path d="" fill="#B7A1F4" transform="translate(0,0)"/>
<path d="" fill="#BBA8F2" transform="translate(0,0)"/>
<path d="" fill="#BDA8F8" transform="translate(0,0)"/>
<path d="" fill="#BBA7F7" transform="translate(0,0)"/>
<path d="" fill="#B7A4F6" transform="translate(0,0)"/>
<path d="" fill="#B9A2F8" transform="translate(0,0)"/>
<path d="" fill="#BEA9F8" transform="translate(0,0)"/>
<path d="" fill="#AC96EC" transform="translate(0,0)"/>
<path d="" fill="#BCA6F7" transform="translate(0,0)"/>
<path d="" fill="#BBA8F6" transform="translate(0,0)"/>
<path d="" fill="#BEAAF9" transform="translate(0,0)"/>
<path d="" fill="#B9A4FA" transform="translate(0,0)"/>
<path d="" fill="#BCA7F9" transform="translate(0,0)"/>
<path d="" fill="#B6A3F9" transform="translate(0,0)"/>
<path d="" fill="#B8A4F8" transform="translate(0,0)"/>
<path d="" fill="#C0ADF7" transform="translate(0,0)"/>
<path d="" fill="#B5A3F3" transform="translate(0,0)"/>
<path d="" fill="#B6A2F6" transform="translate(0,0)"/>
<path d="" fill="#BCA7F5" transform="translate(0,0)"/>
<path d="" fill="#BBAAF7" transform="translate(0,0)"/>
<path d="" fill="#BBA8F7" transform="translate(0,0)"/>
<path d="" fill="#BEA8FA" transform="translate(0,0)"/>
<path d="" fill="#BBA5F8" transform="translate(0,0)"/>
<path d="" fill="#BEA9F8" transform="translate(0,0)"/>
<path d="" fill="#B8A3F6" transform="translate(0,0)"/>
<path d="" fill="#BBA7F8" transform="translate(0,0)"/>
<path d="" fill="#B9A2F9" transform="translate(0,0)"/>
<path d="" fill="#BFACF8" transform="translate(0,0)"/>
<path d="" fill="#B7A4F5" transform="translate(0,0)"/>
<path d="" fill="#BCABF9" transform="translate(0,0)"/>
<path d="" fill="#BDA9F4" transform="translate(0,0)"/>
<path d="" fill="#BDA8F9" transform="translate(0,0)"/>
<path d="" fill="#B8A5F9" transform="translate(0,0)"/>
<path d="" fill="#B7A4F6" transform="translate(0,0)"/>
<path d="" fill="#C0AEF7" transform="translate(0,0)"/>
<path d="" fill="#B8A5F2" transform="translate(0,0)"/>
<path d="" fill="#B2A0EF" transform="translate(0,0)"/>
<path d="" fill="#B6A5F3" transform="translate(0,0)"/>
<path d="" fill="#BBA6F7" transform="translate(0,0)"/>
<path d="" fill="#BBA9F6" transform="translate(0,0)"/>
<path d="" fill="#BCA8F6" transform="translate(0,0)"/>
<path d="" fill="#B7A2F6" transform="translate(0,0)"/>
<path d="" fill="#B9A7F7" transform="translate(0,0)"/>
<path d="" fill="#BBA9F8" transform="translate(0,0)"/>
<path d="" fill="#BAA5FA" transform="translate(0,0)"/>
<path d="" fill="#BFACF7" transform="translate(0,0)"/>
<path d="" fill="#B9A4F8" transform="translate(0,0)"/>
<path d="" fill="#BAA7F2" transform="translate(0,0)"/>
<path d="" fill="#BEACF5" transform="translate(0,0)"/>
<path d="" fill="#BFABF7" transform="translate(0,0)"/>
<path d="" fill="#B3A1F8" transform="translate(0,0)"/>
<path d="" fill="#BDAAF8" transform="translate(0,0)"/>
<path d="" fill="#BDAAF7" transform="translate(0,0)"/>
<path d="" fill="#C0ABFA" transform="translate(0,0)"/>
<path d="" fill="#BFA8FA" transform="translate(0,0)"/>
<path d="" fill="#BCA8F8" transform="translate(0,0)"/>
<path d="" fill="#BBA7F9" transform="translate(0,0)"/>
<path d="" fill="#BDAAF9" transform="translate(0,0)"/>
<path d="" fill="#BEABFA" transform="translate(0,0)"/>
<path d="" fill="#BFAAF9" transform="translate(0,0)"/>
<path d="" fill="#B6A2F2" transform="translate(0,0)"/>
<path d="" fill="#BAA4F9" transform="translate(0,0)"/>
<path d="" fill="#C1ADF9" transform="translate(0,0)"/>
<path d="" fill="#C7B3F9" transform="translate(0,0)"/>
<path d="" fill="#BEA8F8" transform="translate(0,0)"/>
<path d="" fill="#BAA8F8" transform="translate(0,0)"/>
<path d="" fill="#BDAAF8" transform="translate(0,0)"/>
<path d="" fill="#B7A1F5" transform="translate(0,0)"/>
<path d="" fill="#BAA7F6" transform="translate(0,0)"/>
<path d="" fill="#C0ABF7" transform="translate(0,0)"/>
<path d="" fill="#C4B0FA" transform="translate(0,0)"/>
<path d="" fill="#BFADF8" transform="translate(0,0)"/>
<path d="" fill="#BCA7F6" transform="translate(0,0)"/>
<path d="" fill="#C1ACF9" transform="translate(0,0)"/>
<path d="" fill="#C0AAFA" transform="translate(0,0)"/>
<path d="" fill="#B6A4F9" transform="translate(0,0)"/>
<path d="" fill="#BDA9F8" transform="translate(0,0)"/>
<path d="" fill="#BFABF8" transform="translate(0,0)"/>
<path d="" fill="#BBA8F9" transform="translate(0,0)"/>
<path d="" fill="#BDA8F8" transform="translate(0,0)"/>
<path d="" fill="#B3A3F2" transform="translate(0,0)"/>
<path d="" fill="#BDAAF8" transform="translate(0,0)"/>
<path d="" fill="#BBA8F7" transform="translate(0,0)"/>
<path d="" fill="#B9A6F5" transform="translate(0,0)"/>
<path d="" fill="#B6A2F5" transform="translate(0,0)"/>
<path d="" fill="#BCA8F7" transform="translate(0,0)"/>
<path d="" fill="#BAA5F9" transform="translate(0,0)"/>
<path d="" fill="#BCA9F8" transform="translate(0,0)"/>
<path d="" fill="#BBA8F8" transform="translate(0,0)"/>
<path d="" fill="#B9A9F4" transform="translate(0,0)"/>
<path d="" fill="#C1ADF9" transform="translate(0,0)"/>
<path d="" fill="#BCA9F5" transform="translate(0,0)"/>
<path d="" fill="#C1ACF8" transform="translate(0,0)"/>
<path d="" fill="#BDA9F7" transform="translate(0,0)"/>
<path d="" fill="#B6A3F9" transform="translate(0,0)"/>
<path d="" fill="#B8A5F7" transform="translate(0,0)"/>
<path d="" fill="#BEAAF9" transform="translate(0,0)"/>
<path d="" fill="#BDA7F8" transform="translate(0,0)"/>
<path d="" fill="#B8A5F1" transform="translate(0,0)"/>
<path d="" fill="#BAA7F6" transform="translate(0,0)"/>
<path d="" fill="#B9A6F7" transform="translate(0,0)"/>
<path d="" fill="#BBA7F6" transform="translate(0,0)"/>
<path d="" fill="#BCA9F7" transform="translate(0,0)"/>
<path d="" fill="#BBAAF7" transform="translate(0,0)"/>
<path d="" fill="#BBACF7" transform="translate(0,0)"/>
<path d="" fill="#B4A2F4" transform="translate(0,0)"/>
<path d="" fill="#B6A2F7" transform="translate(0,0)"/>
<path d="" fill="#B9A3F5" transform="translate(0,0)"/>
<path d="" fill="#BDA9F6" transform="translate(0,0)"/>
<path d="" fill="#B8A7F3" transform="translate(0,0)"/>
<path d="" fill="#B2A1F5" transform="translate(0,0)"/>
<path d="" fill="#B9A6F5" transform="translate(0,0)"/>
<path d="" fill="#BDA9F5" transform="translate(0,0)"/>
<path d="" fill="#B5A0F7" transform="translate(0,0)"/>
<path d="" fill="#BEA9F8" transform="translate(0,0)"/>
<path d="" fill="#BCA8F7" transform="translate(0,0)"/>
<path d="" fill="#BAA5F9" transform="translate(0,0)"/>
<path d="" fill="#BDAAF9" transform="translate(0,0)"/>
<path d="" fill="#B9A5F7" transform="translate(0,0)"/>
<path d="" fill="#BDA9F9" transform="translate(0,0)"/>
<path d="" fill="#B8A4F8" transform="translate(0,0)"/>
<path d="" fill="#BCA8F4" transform="translate(0,0)"/>
<path d="" fill="#BDAAF9" transform="translate(0,0)"/>
<path d="" fill="#BEA7F7" transform="translate(0,0)"/>
<path d="" fill="#BAA9F6" transform="translate(0,0)"/>
<path d="" fill="#BBA7F8" transform="translate(0,0)"/>
<path d="" fill="#BBA9F7" transform="translate(0,0)"/>
<path d="" fill="#BBA7F6" transform="translate(0,0)"/>
<path d="" fill="#B29DF5" transform="translate(0,0)"/>
<path d="" fill="#BFAAF8" transform="translate(0,0)"/>
<path d="" fill="#B8A7F8" transform="translate(0,0)"/>
<path d="" fill="#B3A0F5" transform="translate(0,0)"/>
<path d="" fill="#B6A0F6" transform="translate(0,0)"/>
<path d="" fill="#B7A5F3" transform="translate(0,0)"/>
<path d="" fill="#BDAAF7" transform="translate(0,0)"/>
<path d="" fill="#B7A4F4" transform="translate(0,0)"/>
<path d="" fill="#BBA5F5" transform="translate(0,0)"/>
<path d="" fill="#B9A4F8" transform="translate(0,0)"/>
<path d="" fill="#B8A4F8" transform="translate(0,0)"/>
<path d="" fill="#BDAAF5" transform="translate(0,0)"/>
<path d="" fill="#BAA6F8" transform="translate(0,0)"/>
<path d="" fill="#BBAAF8" transform="translate(0,0)"/>
<path d="" fill="#BAA6F7" transform="translate(0,0)"/>
<path d="" fill="#B8A6F4" transform="translate(0,0)"/>
<path d="" fill="#B4A1F7" transform="translate(0,0)"/>
<path d="" fill="#C2AAF2" transform="translate(0,0)"/>
<path d="" fill="#B8A3F7" transform="translate(0,0)"/>
<path d="" fill="#BDAAF5" transform="translate(0,0)"/>
<path d="" fill="#B9A5F5" transform="translate(0,0)"/>
<path d="" fill="#BBA6F8" transform="translate(0,0)"/>
<path d="" fill="#BAA6F8" transform="translate(0,0)"/>
<path d="" fill="#BCA6F6" transform="translate(0,0)"/>
<path d="" fill="#B8A4F2" transform="translate(0,0)"/>
<path d="" fill="#B9A4F6" transform="translate(0,0)"/>
<path d="" fill="#B7A5F7" transform="translate(0,0)"/>
<path d="" fill="#B7A4F4" transform="translate(0,0)"/>
<path d="" fill="#BAA7F5" transform="translate(0,0)"/>
<path d="" fill="#BBA6F6" transform="translate(0,0)"/>
<path d="" fill="#B8A4F6" transform="translate(0,0)"/>
<path d="" fill="#B7A6F8" transform="translate(0,0)"/>
<path d="" fill="#BAA6F3" transform="translate(0,0)"/>
<path d="" fill="#BEAAF8" transform="translate(0,0)"/>
<path d="" fill="#BEACF5" transform="translate(0,0)"/>
<path d="" fill="#C0AEF7" transform="translate(0,0)"/>
<path d="" fill="#B7A5F3" transform="translate(0,0)"/>
<path d="" fill="#B8A5F6" transform="translate(0,0)"/>
<path d="" fill="#BBA7F7" transform="translate(0,0)"/>
<path d="" fill="#BAA7F8" transform="translate(0,0)"/>
<path d="" fill="#B9A5F8" transform="translate(0,0)"/>
<path d="" fill="#BEAAF5" transform="translate(0,0)"/>
<path d="" fill="#B7A3F5" transform="translate(0,0)"/>
<path d="" fill="#BAA7F1" transform="translate(0,0)"/>
<path d="" fill="#B6A4F3" transform="translate(0,0)"/>
<path d="" fill="#B7A6F6" transform="translate(0,0)"/>
<path d="" fill="#B6A2F3" transform="translate(0,0)"/>
<path d="" fill="#B9A6F2" transform="translate(0,0)"/>
<path d="" fill="#B7A4F7" transform="translate(0,0)"/>
<path d="" fill="#B6A5F4" transform="translate(0,0)"/>
<path d="" fill="#B6A4F0" transform="translate(0,0)"/>
<path d="" fill="#B7A5F3" transform="translate(0,0)"/>
<path d="" fill="#B9A5F6" transform="translate(0,0)"/>
<path d="" fill="#BAA5F5" transform="translate(0,0)"/>
<path d="" fill="#B9A6F3" transform="translate(0,0)"/>
<path d="" fill="#AD9BED" transform="translate(0,0)"/>
<path d="" fill="#B2A1F0" transform="translate(0,0)"/>
<path d="" fill="#BAA6F1" transform="translate(0,0)"/>
<path d="" fill="#BBA7F2" transform="translate(0,0)"/>
<path d="" fill="#B8A6F3" transform="translate(0,0)"/>
<path d="" fill="#BCAAF7" transform="translate(0,0)"/>
<path d="" fill="#B7A4F6" transform="translate(0,0)"/>
<path d="" fill="#B8A3F4" transform="translate(0,0)"/>
<path d="" fill="#B8A5F6" transform="translate(0,0)"/>
<path d="" fill="#AB9CF1" transform="translate(0,0)"/>
<path d="" fill="#B7A5F7" transform="translate(0,0)"/>
<path d="" fill="#BEADF6" transform="translate(0,0)"/>
<path d="" fill="#BBA9F4" transform="translate(0,0)"/>
<path d="" fill="#BFADF6" transform="translate(0,0)"/>
<path d="" fill="#BBA7F6" transform="translate(0,0)"/>
<path d="" fill="#BDABF6" transform="translate(0,0)"/>
<path d="" fill="#BAA6F6" transform="translate(0,0)"/>
<path d="" fill="#B9A7F7" transform="translate(0,0)"/>
<path d="" fill="#B9A5F6" transform="translate(0,0)"/>
<path d="" fill="#BCA7F7" transform="translate(0,0)"/>
<path d="" fill="#BDA6F8" transform="translate(0,0)"/>
<path d="" fill="#AB9AF5" transform="translate(0,0)"/>
<path d="" fill="#B5A1F1" transform="translate(0,0)"/>
<path d="" fill="#B6A4F4" transform="translate(0,0)"/>
<path d="" fill="#BDA8F8" transform="translate(0,0)"/>
<path d="" fill="#BCAAF8" transform="translate(0,0)"/>
<path d="" fill="#B9A6F5" transform="translate(0,0)"/>
<path d="" fill="#C1ADF7" transform="translate(0,0)"/>
<path d="" fill="#B1A1F0" transform="translate(0,0)"/>
<path d="" fill="#B9A6F6" transform="translate(0,0)"/>
<path d="" fill="#B7A4F4" transform="translate(0,0)"/>
<path d="" fill="#B9A5F4" transform="translate(0,0)"/>
<path d="" fill="#BEABF7" transform="translate(0,0)"/>
<path d="" fill="#B5A4F6" transform="translate(0,0)"/>
<path d="" fill="#B4A1F2" transform="translate(0,0)"/>
<path d="" fill="#BBA6F2" transform="translate(0,0)"/>
<path d="" fill="#B7A3F3" transform="translate(0,0)"/>
<path d="" fill="#B7A4F5" transform="translate(0,0)"/>
<path d="" fill="#B9A5F5" transform="translate(0,0)"/>
<path d="" fill="#B6A4F2" transform="translate(0,0)"/>
<path d="" fill="#B4A1F4" transform="translate(0,0)"/>
<path d="" fill="#B5A1F8" transform="translate(0,0)"/>
<path d="" fill="#B2A1F0" transform="translate(0,0)"/>
<path d="" fill="#B7A3F3" transform="translate(0,0)"/>
<path d="" fill="#B9A6F7" transform="translate(0,0)"/>
<path d="" fill="#B5A2F5" transform="translate(0,0)"/>
<path d="" fill="#B3A2F1" transform="translate(0,0)"/>
<path d="" fill="#B4A2F5" transform="translate(0,0)"/>
<path d="" fill="#A596EA" transform="translate(0,0)"/>
<path d="" fill="#A696EA" transform="translate(0,0)"/>
<path d="" fill="#B9A5F6" transform="translate(0,0)"/>
<path d="" fill="#B7A3F8" transform="translate(0,0)"/>
<path d="" fill="#B6A4F4" transform="translate(0,0)"/>
<path d="" fill="#B7A5F5" transform="translate(0,0)"/>
<path d="" fill="#B5A3F7" transform="translate(0,0)"/>
<path d="" fill="#B3A0F6" transform="translate(0,0)"/>
<path d="" fill="#C6B3F7" transform="translate(0,0)"/>
<path d="" fill="#C3B4F5" transform="translate(0,0)"/>
<path d="" fill="#BFAFF3" transform="translate(0,0)"/>
<path d="" fill="#C2B3F6" transform="translate(0,0)"/>
<path d="" fill="#C8B6FB" transform="translate(0,0)"/>
<path d="" fill="#C5B4F9" transform="translate(0,0)"/>
<path d="" fill="#C4B3F6" transform="translate(0,0)"/>
<path d="" fill="#C5B4F8" transform="translate(0,0)"/>
<path d="" fill="#BFAFF6" transform="translate(0,0)"/>
<path d="" fill="#C0B2F6" transform="translate(0,0)"/>
<path d="" fill="#C8B7F9" transform="translate(0,0)"/>
<path d="" fill="#C5B3F8" transform="translate(0,0)"/>
<path d="" fill="#C5B4FB" transform="translate(0,0)"/>
<path d="" fill="#C1B0F7" transform="translate(0,0)"/>
<path d="" fill="#B7A8F5" transform="translate(0,0)"/>
<path d="" fill="#BCADF5" transform="translate(0,0)"/>
<path d="" fill="#BFAFF7" transform="translate(0,0)"/>
<path d="" fill="#BCABF5" transform="translate(0,0)"/>
<path d="" fill="#BFAEF8" transform="translate(0,0)"/>
<path d="" fill="#BDADF5" transform="translate(0,0)"/>
<path d="" fill="#C3B3F3" transform="translate(0,0)"/>
<path d="" fill="#C5B5F9" transform="translate(0,0)"/>
<path d="" fill="#C7B6FA" transform="translate(0,0)"/>
<path d="" fill="#C9B8F6" transform="translate(0,0)"/>
<path d="" fill="#C2B2F9" transform="translate(0,0)"/>
<path d="" fill="#C3B4F6" transform="translate(0,0)"/>
<path d="" fill="#C0B1F6" transform="translate(0,0)"/>
<path d="" fill="#BCAEF2" transform="translate(0,0)"/>
<path d="" fill="#C6B5F3" transform="translate(0,0)"/>
<path d="" fill="#CBB8F6" transform="translate(0,0)"/>
<path d="" fill="#C8BAF7" transform="translate(0,0)"/>
<path d="" fill="#C9BBF7" transform="translate(0,0)"/>
<path d="" fill="#C0B5F5" transform="translate(0,0)"/>
<path d="" fill="#C4B5F9" transform="translate(0,0)"/>
<path d="" fill="#C6B6FA" transform="translate(0,0)"/>
<path d="" fill="#CBBCF8" transform="translate(0,0)"/>
<path d="" fill="#C6B6F6" transform="translate(0,0)"/>
<path d="" fill="#CBBBF7" transform="translate(0,0)"/>
<path d="" fill="#CDBEF9" transform="translate(0,0)"/>
<path d="" fill="#C8BAF7" transform="translate(0,0)"/>
<path d="" fill="#C6B5F7" transform="translate(0,0)"/>
<path d="" fill="#C3B2F8" transform="translate(0,0)"/>
<path d="" fill="#C8B7F9" transform="translate(0,0)"/>
<path d="" fill="#C9BEF9" transform="translate(0,0)"/>
<path d="" fill="#C7B9FA" transform="translate(0,0)"/>
<path d="" fill="#BAACED" transform="translate(0,0)"/>
<path d="" fill="#BDAFF6" transform="translate(0,0)"/>
<path d="" fill="#D4C2F9" transform="translate(0,0)"/>
<path d="" fill="#C7B7F7" transform="translate(0,0)"/>
<path d="" fill="#C4B6F8" transform="translate(0,0)"/>
<path d="" fill="#C2B4F7" transform="translate(0,0)"/>
<path d="" fill="#BFB0F5" transform="translate(0,0)"/>
<path d="" fill="#CBBCF9" transform="translate(0,0)"/>
<path d="" fill="#CDBDFB" transform="translate(0,0)"/>
<path d="" fill="#CABAF7" transform="translate(0,0)"/>
<path d="" fill="#CAB7F9" transform="translate(0,0)"/>
<path d="" fill="#CBB9F7" transform="translate(0,0)"/>
<path d="" fill="#CCBCF9" transform="translate(0,0)"/>
<path d="" fill="#D0BFFA" transform="translate(0,0)"/>
<path d="" fill="#BFB2F3" transform="translate(0,0)"/>
<path d="" fill="#C4B5EB" transform="translate(0,0)"/>
<path d="" fill="#CDBEFA" transform="translate(0,0)"/>
<path d="" fill="#C9B8FB" transform="translate(0,0)"/>
<path d="" fill="#C8B8FA" transform="translate(0,0)"/>
<path d="" fill="#CCC0F9" transform="translate(0,0)"/>
<path d="" fill="#D6C5F5" transform="translate(0,0)"/>
<path d="" fill="#C9BAFA" transform="translate(0,0)"/>
<path d="" fill="#C9BBF6" transform="translate(0,0)"/>
<path d="" fill="#CCBDF6" transform="translate(0,0)"/>
<path d="" fill="#CEBDFC" transform="translate(0,0)"/>
<path d="" fill="#C8B9F8" transform="translate(0,0)"/>
<path d="" fill="#CDBDF8" transform="translate(0,0)"/>
<path d="" fill="#D7C6FB" transform="translate(0,0)"/>
<path d="" fill="#CBBAF6" transform="translate(0,0)"/>
<path d="" fill="#C8B8F8" transform="translate(0,0)"/>
<path d="" fill="#C5B8F6" transform="translate(0,0)"/>
<path d="" fill="#CDBEFA" transform="translate(0,0)"/>
<path d="" fill="#D2C4F7" transform="translate(0,0)"/>
<path d="" fill="#CCBDF9" transform="translate(0,0)"/>
<path d="" fill="#C9BAF8" transform="translate(0,0)"/>
<path d="" fill="#CCB8F4" transform="translate(0,0)"/>
<path d="" fill="#CAB8F6" transform="translate(0,0)"/>
<path d="" fill="#CBBBF6" transform="translate(0,0)"/>
<path d="" fill="#CEBFFB" transform="translate(0,0)"/>
<path d="" fill="#C9B8FA" transform="translate(0,0)"/>
<path d="" fill="#D0C3F8" transform="translate(0,0)"/>
<path d="" fill="#CFBEF9" transform="translate(0,0)"/>
<path d="" fill="#CBBBF2" transform="translate(0,0)"/>
<path d="" fill="#D4C1F6" transform="translate(0,0)"/>
<path d="" fill="#D5C4F9" transform="translate(0,0)"/>
<path d="" fill="#D0C1FC" transform="translate(0,0)"/>
<path d="" fill="#CFC0F9" transform="translate(0,0)"/>
<path d="" fill="#D6C6FD" transform="translate(0,0)"/>
<path d="" fill="#C9B7F5" transform="translate(0,0)"/>
<path d="" fill="#CCBEFC" transform="translate(0,0)"/>
<path d="" fill="#D4C5FD" transform="translate(0,0)"/>
<path d="" fill="#CFC0FA" transform="translate(0,0)"/>
<path d="" fill="#CFBFF9" transform="translate(0,0)"/>
<path d="" fill="#D4C3F7" transform="translate(0,0)"/>
<path d="" fill="#C9B9F8" transform="translate(0,0)"/>
<path d="" fill="#CFC0FA" transform="translate(0,0)"/>
<path d="" fill="#CFBEF4" transform="translate(0,0)"/>
<path d="" fill="#CFBEF9" transform="translate(0,0)"/>
<path d="" fill="#D4C3F8" transform="translate(0,0)"/>
<path d="" fill="#D4C4F9" transform="translate(0,0)"/>
<path d="" fill="#CBBBFB" transform="translate(0,0)"/>
<path d="" fill="#D2BFF8" transform="translate(0,0)"/>
<path d="" fill="#CBBCFC" transform="translate(0,0)"/>
<path d="" fill="#CDBCFC" transform="translate(0,0)"/>
<path d="" fill="#CDBDF8" transform="translate(0,0)"/>
<path d="" fill="#DBCBF7" transform="translate(0,0)"/>
<path d="" fill="#D1C2FA" transform="translate(0,0)"/>
<path d="" fill="#D4C1F8" transform="translate(0,0)"/>
<path d="" fill="#D1C0FA" transform="translate(0,0)"/>
<path d="" fill="#CFBDFA" transform="translate(0,0)"/>
<path d="" fill="#D3C3F9" transform="translate(0,0)"/>
<path d="" fill="#BAACF4" transform="translate(0,0)"/>
<path d="" fill="#C7B7F7" transform="translate(0,0)"/>
<path d="" fill="#CBBAFC" transform="translate(0,0)"/>
<path d="" fill="#CFC0F8" transform="translate(0,0)"/>
<path d="" fill="#CDBCFA" transform="translate(0,0)"/>
<path d="" fill="#CCBBFB" transform="translate(0,0)"/>
<path d="" fill="#CDBCF9" transform="translate(0,0)"/>
<path d="" fill="#C2B4F7" transform="translate(0,0)"/>
<path d="" fill="#C8B6F7" transform="translate(0,0)"/>
<path d="" fill="#CABEF9" transform="translate(0,0)"/>
<path d="" fill="#C7B6F9" transform="translate(0,0)"/>
<path d="" fill="#D9CAF7" transform="translate(0,0)"/>
<path d="" fill="#C5B5F8" transform="translate(0,0)"/>
<path d="" fill="#CABBF6" transform="translate(0,0)"/>
<path d="" fill="#C6B8F7" transform="translate(0,0)"/>
<path d="" fill="#CCBDF7" transform="translate(0,0)"/>
<path d="" fill="#C4B7F9" transform="translate(0,0)"/>
<path d="" fill="#CFC0FB" transform="translate(0,0)"/>
<path d="" fill="#C8BAF9" transform="translate(0,0)"/>
<path d="" fill="#CEBEFC" transform="translate(0,0)"/>
<path d="" fill="#CEBDFC" transform="translate(0,0)"/>
<path d="" fill="#CCBCFA" transform="translate(0,0)"/>
<path d="" fill="#D3C2F9" transform="translate(0,0)"/>
<path d="" fill="#CBBBF7" transform="translate(0,0)"/>
<path d="" fill="#D3C0F9" transform="translate(0,0)"/>
<path d="" fill="#D7C4FB" transform="translate(0,0)"/>
<path d="" fill="#D0BFF8" transform="translate(0,0)"/>
<path d="" fill="#D5BFF9" transform="translate(0,0)"/>
<path d="" fill="#D7C6FC" transform="translate(0,0)"/>
<path d="" fill="#D9C9FB" transform="translate(0,0)"/>
<path d="" fill="#D2C3F7" transform="translate(0,0)"/>
<path d="" fill="#D1C0F9" transform="translate(0,0)"/>
<path d="" fill="#CFC0FA" transform="translate(0,0)"/>
<path d="" fill="#CBBDFA" transform="translate(0,0)"/>
<path d="" fill="#C4B5F8" transform="translate(0,0)"/>
<path d="" fill="#D0BEF8" transform="translate(0,0)"/>
<path d="" fill="#E2CEFC" transform="translate(0,0)"/>
<path d="" fill="#CDBFF8" transform="translate(0,0)"/>
<path d="" fill="#C9BBF6" transform="translate(0,0)"/>
<path d="" fill="#CFC0FA" transform="translate(0,0)"/>
<path d="" fill="#CFC0F9" transform="translate(0,0)"/>
<path d="" fill="#CFBCFA" transform="translate(0,0)"/>
<path d="" fill="#CDBFF7" transform="translate(0,0)"/>
<path d="" fill="#D0BFF9" transform="translate(0,0)"/>
<path d="" fill="#D6C6F8" transform="translate(0,0)"/>
<path d="" fill="#D2C2F9" transform="translate(0,0)"/>
<path d="" fill="#D4C0F9" transform="translate(0,0)"/>
<path d="" fill="#D4C6FA" transform="translate(0,0)"/>
<path d="" fill="#DBCCFE" transform="translate(0,0)"/>
<path d="" fill="#C8B8F7" transform="translate(0,0)"/>
<path d="" fill="#CDBDF9" transform="translate(0,0)"/>
<path d="" fill="#CDBDF9" transform="translate(0,0)"/>
<path d="" fill="#D1C2FA" transform="translate(0,0)"/>
<path d="" fill="#CAB8F8" transform="translate(0,0)"/>
<path d="" fill="#CABBF8" transform="translate(0,0)"/>
<path d="" fill="#D1C1FA" transform="translate(0,0)"/>
<path d="" fill="#CEBCFA" transform="translate(0,0)"/>
<path d="" fill="#D1C1F6" transform="translate(0,0)"/>
<path d="" fill="#CEBDFA" transform="translate(0,0)"/>
<path d="" fill="#CFBEF8" transform="translate(0,0)"/>
<path d="" fill="#D0C0F9" transform="translate(0,0)"/>
<path d="" fill="#CBBBF7" transform="translate(0,0)"/>
<path d="" fill="#CDBCF9" transform="translate(0,0)"/>
<path d="" fill="#CDBDFA" transform="translate(0,0)"/>
<path d="" fill="#D2C2F8" transform="translate(0,0)"/>
<path d="" fill="#D4C5FA" transform="translate(0,0)"/>
<path d="" fill="#DCCCFC" transform="translate(0,0)"/>
<path d="" fill="#D2C1F6" transform="translate(0,0)"/>
<path d="" fill="#CDC1FB" transform="translate(0,0)"/>
<path d="" fill="#C5B8F8" transform="translate(0,0)"/>
<path d="" fill="#D8C3FA" transform="translate(0,0)"/>
<path d="" fill="#CDBDF9" transform="translate(0,0)"/>
<path d="" fill="#CCBDF8" transform="translate(0,0)"/>
<path d="" fill="#CFBFFA" transform="translate(0,0)"/>
<path d="" fill="#CDBEF7" transform="translate(0,0)"/>
<path d="" fill="#D9C9FD" transform="translate(0,0)"/>
<path d="" fill="#CEBFFB" transform="translate(0,0)"/>
<path d="" fill="#D4C3FB" transform="translate(0,0)"/>
<path d="" fill="#CDBBF8" transform="translate(0,0)"/>
<path d="" fill="#CEBDF9" transform="translate(0,0)"/>
<path d="" fill="#D8C6F9" transform="translate(0,0)"/>
<path d="" fill="#CFBDF9" transform="translate(0,0)"/>
<path d="" fill="#CFBDFA" transform="translate(0,0)"/>
<path d="" fill="#CFBDF9" transform="translate(0,0)"/>
<path d="" fill="#CDBCFA" transform="translate(0,0)"/>
<path d="" fill="#CFBEFC" transform="translate(0,0)"/>
<path d="" fill="#CDBEF9" transform="translate(0,0)"/>
<path d="" fill="#CCBBF8" transform="translate(0,0)"/>
<path d="" fill="#CFC0F7" transform="translate(0,0)"/>
<path d="" fill="#CCBCF9" transform="translate(0,0)"/>
<path d="" fill="#D3C2FC" transform="translate(0,0)"/>
<path d="" fill="#D0C2F7" transform="translate(0,0)"/>
<path d="" fill="#D8C4FB" transform="translate(0,0)"/>
<path d="" fill="#C9BCFB" transform="translate(0,0)"/>
<path d="" fill="#CFC1FB" transform="translate(0,0)"/>
<path d="" fill="#D6C5FE" transform="translate(0,0)"/>
<path d="" fill="#D8C7F9" transform="translate(0,0)"/>
<path d="" fill="#D3C3FC" transform="translate(0,0)"/>
<path d="" fill="#D7C9FB" transform="translate(0,0)"/>
<path d="" fill="#CCBCFA" transform="translate(0,0)"/>
<path d="" fill="#D1C1FD" transform="translate(0,0)"/>
<path d="" fill="#D0BDFB" transform="translate(0,0)"/>
<path d="" fill="#D0C0F7" transform="translate(0,0)"/>
<path d="" fill="#CCBAF7" transform="translate(0,0)"/>
<path d="" fill="#CDBBFD" transform="translate(0,0)"/>
<path d="" fill="#CEBFFC" transform="translate(0,0)"/>
<path d="" fill="#CDBEFA" transform="translate(0,0)"/>
<path d="" fill="#BEAEF5" transform="translate(0,0)"/>
<path d="" fill="#D0BFF9" transform="translate(0,0)"/>
<path d="" fill="#D4C3FD" transform="translate(0,0)"/>
<path d="" fill="#D2C1F8" transform="translate(0,0)"/>
<path d="" fill="#CEBDFA" transform="translate(0,0)"/>
<path d="" fill="#C8B8F7" transform="translate(0,0)"/>
<path d="" fill="#CDBFF6" transform="translate(0,0)"/>
<path d="" fill="#C8BAF9" transform="translate(0,0)"/>
<path d="" fill="#CEBCF8" transform="translate(0,0)"/>
<path d="" fill="#CEBEFB" transform="translate(0,0)"/>
<path d="" fill="#D0C1FA" transform="translate(0,0)"/>
<path d="" fill="#D5C3FB" transform="translate(0,0)"/>
<path d="" fill="#CBBBF6" transform="translate(0,0)"/>
<path d="" fill="#CFC0FA" transform="translate(0,0)"/>
<path d="" fill="#CDBCF7" transform="translate(0,0)"/>
<path d="" fill="#CFBDFB" transform="translate(0,0)"/>
<path d="" fill="#CBBBFA" transform="translate(0,0)"/>
<path d="" fill="#CEBEFA" transform="translate(0,0)"/>
<path d="" fill="#CDBDFC" transform="translate(0,0)"/>
<path d="" fill="#CDBBFA" transform="translate(0,0)"/>
<path d="" fill="#D0BFFB" transform="translate(0,0)"/>
<path d="" fill="#CDBEFB" transform="translate(0,0)"/>
<path d="" fill="#DCD1FD" transform="translate(0,0)"/>
<path d="" fill="#CEBEF8" transform="translate(0,0)"/>
<path d="" fill="#C2B6F5" transform="translate(0,0)"/>
<path d="" fill="#D6C3F8" transform="translate(0,0)"/>
<path d="" fill="#D0BEFB" transform="translate(0,0)"/>
<path d="" fill="#C9BAFB" transform="translate(0,0)"/>
<path d="" fill="#D1C1FC" transform="translate(0,0)"/>
<path d="" fill="#CABCF8" transform="translate(0,0)"/>
<path d="" fill="#CDBCF6" transform="translate(0,0)"/>
<path d="" fill="#D0C1F7" transform="translate(0,0)"/>
<path d="" fill="#CFBFF9" transform="translate(0,0)"/>
<path d="" fill="#CFBEFA" transform="translate(0,0)"/>
<path d="" fill="#CCBDF8" transform="translate(0,0)"/>
<path d="" fill="#CBBBFA" transform="translate(0,0)"/>
<path d="" fill="#CABAF9" transform="translate(0,0)"/>
<path d="" fill="#CABAFA" transform="translate(0,0)"/>
<path d="" fill="#CDBBF7" transform="translate(0,0)"/>
<path d="" fill="#CBBAF5" transform="translate(0,0)"/>
<path d="" fill="#CBBDF9" transform="translate(0,0)"/>
<path d="" fill="#CDBEF9" transform="translate(0,0)"/>
<path d="" fill="#C1B3F6" transform="translate(0,0)"/>
<path d="" fill="#D2C1FC" transform="translate(0,0)"/>
<path d="" fill="#CFC0FA" transform="translate(0,0)"/>
<path d="" fill="#C5B5F7" transform="translate(0,0)"/>
<path d="" fill="#C2B5F8" transform="translate(0,0)"/>
<path d="" fill="#CBBBF9" transform="translate(0,0)"/>
<path d="" fill="#D7C7F9" transform="translate(0,0)"/>
<path d="" fill="#D7C6FB" transform="translate(0,0)"/>
<path d="" fill="#D3C1FA" transform="translate(0,0)"/>
<path d="" fill="#D3C4FB" transform="translate(0,0)"/>
<path d="" fill="#CBBAFA" transform="translate(0,0)"/>
<path d="" fill="#C6B7F6" transform="translate(0,0)"/>
<path d="" fill="#D4C2F6" transform="translate(0,0)"/>
<path d="" fill="#D1C2FA" transform="translate(0,0)"/>
<path d="" fill="#CABAFA" transform="translate(0,0)"/>
<path d="" fill="#C8B8F8" transform="translate(0,0)"/>
<path d="" fill="#C8B7F8" transform="translate(0,0)"/>
<path d="" fill="#DBCAFD" transform="translate(0,0)"/>
<path d="" fill="#C8BBFA" transform="translate(0,0)"/>
<path d="" fill="#BFAFF2" transform="translate(0,0)"/>
<path d="" fill="#CBB8F8" transform="translate(0,0)"/>
<path d="" fill="#CABDF5" transform="translate(0,0)"/>
<path d="" fill="#C6B7F7" transform="translate(0,0)"/>
<path d="" fill="#C5B7F6" transform="translate(0,0)"/>
<path d="" fill="#C8BAF7" transform="translate(0,0)"/>
<path d="" fill="#C8B6F6" transform="translate(0,0)"/>
<path d="" fill="#C8B5F5" transform="translate(0,0)"/>
<path d="" fill="#CCBDF8" transform="translate(0,0)"/>
<path d="" fill="#CFC0F9" transform="translate(0,0)"/>
<path d="" fill="#CCBDF9" transform="translate(0,0)"/>
<path d="" fill="#C4B6F7" transform="translate(0,0)"/>
<path d="" fill="#CCBBF6" transform="translate(0,0)"/>
<path d="" fill="#CAB9F7" transform="translate(0,0)"/>
<path d="" fill="#CDBDF8" transform="translate(0,0)"/>
<path d="" fill="#C9B8F9" transform="translate(0,0)"/>
<path d="" fill="#CCBCF8" transform="translate(0,0)"/>
<path d="" fill="#CBBCFA" transform="translate(0,0)"/>
<path d="" fill="#C8BBF7" transform="translate(0,0)"/>
<path d="" fill="#CBBBF8" transform="translate(0,0)"/>
<path d="" fill="#C6B6F6" transform="translate(0,0)"/>
<path d="" fill="#CAB9F9" transform="translate(0,0)"/>
<path d="" fill="#CBBCFB" transform="translate(0,0)"/>
<path d="" fill="#6662C3" transform="translate(0,0)"/>
<path d="" fill="#C3B3F6" transform="translate(0,0)"/>
<path d="" fill="#BEAEF4" transform="translate(0,0)"/>
<path d="" fill="#C8BBF6" transform="translate(0,0)"/>
<path d="" fill="#C5B8F7" transform="translate(0,0)"/>
<path d="" fill="#CEBCF2" transform="translate(0,0)"/>
<path d="" fill="#D4C6FC" transform="translate(0,0)"/>
<path d="" fill="#CCBBFA" transform="translate(0,0)"/>
<path d="" fill="#C1B6F8" transform="translate(0,0)"/>
<path d="" fill="#C7B7F2" transform="translate(0,0)"/>
<path d="" fill="#CABDF3" transform="translate(0,0)"/>
<path d="" fill="#C4B7F3" transform="translate(0,0)"/>
<path d="" fill="#C2B3F6" transform="translate(0,0)"/>
<path d="" fill="#BFB1F7" transform="translate(0,0)"/>
<path d="" fill="#BCAFF7" transform="translate(0,0)"/>
<path d="" fill="#C3B2F1" transform="translate(0,0)"/>
<path d="" fill="#CAB8F6" transform="translate(0,0)"/>
<path d="" fill="#C0B4F6" transform="translate(0,0)"/>
<path d="" fill="#C0B2F8" transform="translate(0,0)"/>
<path d="" fill="#C0B4F7" transform="translate(0,0)"/>
<path d="" fill="#CAB7F8" transform="translate(0,0)"/>
<path d="" fill="#C9BAF9" transform="translate(0,0)"/>
<path d="" fill="#C1B2F4" transform="translate(0,0)"/>
<path d="" fill="#C8B8F7" transform="translate(0,0)"/>
<path d="" fill="#CDBEFA" transform="translate(0,0)"/>
<path d="" fill="#C9B8F9" transform="translate(0,0)"/>
<path d="" fill="#C6B7F6" transform="translate(0,0)"/>
<path d="" fill="#C3B3F5" transform="translate(0,0)"/>
<path d="" fill="#DAC3FE" transform="translate(0,0)"/>
<path d="" fill="#CBBDF8" transform="translate(0,0)"/>
<path d="" fill="#C5B7F5" transform="translate(0,0)"/>
<path d="" fill="#CFBFF9" transform="translate(0,0)"/>
<path d="" fill="#D6C3FD" transform="translate(0,0)"/>
<path d="" fill="#D1C0F9" transform="translate(0,0)"/>
<path d="" fill="#C1B4F4" transform="translate(0,0)"/>
<path d="" fill="#CCBAF8" transform="translate(0,0)"/>
<path d="" fill="#C0B0F3" transform="translate(0,0)"/>
<path d="" fill="#CBBDFA" transform="translate(0,0)"/>
<path d="" fill="#C8B7FA" transform="translate(0,0)"/>
<path d="" fill="#CABAF7" transform="translate(0,0)"/>
<path d="" fill="#C9BAF2" transform="translate(0,0)"/>
<path d="" fill="#C7B9F5" transform="translate(0,0)"/>
<path d="" fill="#CDBFF9" transform="translate(0,0)"/>
<path d="" fill="#CCBBF9" transform="translate(0,0)"/>
<path d="" fill="#CCBBFC" transform="translate(0,0)"/>
<path d="" fill="#CFC0F9" transform="translate(0,0)"/>
<path d="" fill="#C9B7F6" transform="translate(0,0)"/>
<path d="" fill="#C1B1F2" transform="translate(0,0)"/>
<path d="" fill="#BBADF0" transform="translate(0,0)"/>
<path d="" fill="#D3C5F6" transform="translate(0,0)"/>
<path d="" fill="#CBBBF8" transform="translate(0,0)"/>
<path d="" fill="#D3C4F9" transform="translate(0,0)"/>
<path d="" fill="#C3B5F8" transform="translate(0,0)"/>
<path d="" fill="#C9BAFA" transform="translate(0,0)"/>
<path d="" fill="#CEBFFA" transform="translate(0,0)"/>
<path d="" fill="#CFBEFB" transform="translate(0,0)"/>
<path d="" fill="#CFBFFB" transform="translate(0,0)"/>
<path d="" fill="#C3B5F7" transform="translate(0,0)"/>
<path d="" fill="#C8BBF5" transform="translate(0,0)"/>
<path d="" fill="#CABBF5" transform="translate(0,0)"/>
<path d="" fill="#CEBFF7" transform="translate(0,0)"/>
<path d="" fill="#BCADE8" transform="translate(0,0)"/>
<path d="" fill="#CCBFFA" transform="translate(0,0)"/>
<path d="" fill="#D0C1FC" transform="translate(0,0)"/>
<path d="" fill="#BAB0F5" transform="translate(0,0)"/>
<path d="" fill="#C3B4F4" transform="translate(0,0)"/>
<path d="" fill="#D1C1F9" transform="translate(0,0)"/>
<path d="" fill="#CEBEFA" transform="translate(0,0)"/>
<path d="" fill="#CDBDFA" transform="translate(0,0)"/>
<path d="" fill="#CEBFF9" transform="translate(0,0)"/>
<path d="" fill="#D0C0F9" transform="translate(0,0)"/>
<path d="" fill="#D1C2FB" transform="translate(0,0)"/>
<path d="" fill="#D1C0FB" transform="translate(0,0)"/>
<path d="" fill="#D5C4FB" transform="translate(0,0)"/>
<path d="" fill="#BFB0F3" transform="translate(0,0)"/>
<path d="" fill="#D8C9F9" transform="translate(0,0)"/>
<path d="" fill="#BCB1F4" transform="translate(0,0)"/>
<path d="" fill="#D0C0F7" transform="translate(0,0)"/>
<path d="" fill="#C9B9F7" transform="translate(0,0)"/>
<path d="" fill="#E1D1FB" transform="translate(0,0)"/>
<path d="" fill="#CDBDF7" transform="translate(0,0)"/>
<path d="" fill="#D4C5F9" transform="translate(0,0)"/>
<path d="" fill="#C3B6F1" transform="translate(0,0)"/>
<path d="" fill="#D3C2FA" transform="translate(0,0)"/>
<path d="" fill="#D5C7FC" transform="translate(0,0)"/>
<path d="" fill="#CBBCF9" transform="translate(0,0)"/>
<path d="" fill="#CEBFFA" transform="translate(0,0)"/>
<path d="" fill="#D4C4F7" transform="translate(0,0)"/>
<path d="" fill="#C9BAF9" transform="translate(0,0)"/>
<path d="" fill="#CEC0F9" transform="translate(0,0)"/>
<path d="" fill="#D2C5FB" transform="translate(0,0)"/>
<path d="" fill="#C4B6F5" transform="translate(0,0)"/>
<path d="" fill="#BCAFF5" transform="translate(0,0)"/>
<path d="" fill="#D3C3FD" transform="translate(0,0)"/>
<path d="" fill="#D5C0FB" transform="translate(0,0)"/>
<path d="" fill="#D1C3F9" transform="translate(0,0)"/>
<path d="" fill="#CEC1F8" transform="translate(0,0)"/>
<path d="" fill="#CDBDFB" transform="translate(0,0)"/>
<path d="" fill="#D4C3F9" transform="translate(0,0)"/>
<path d="" fill="#C7B9FA" transform="translate(0,0)"/>
<path d="" fill="#CBBBF8" transform="translate(0,0)"/>
<path d="" fill="#D5C6F9" transform="translate(0,0)"/>
<path d="" fill="#CFBFFA" transform="translate(0,0)"/>
<path d="" fill="#D2C3FA" transform="translate(0,0)"/>
<path d="" fill="#C9BAFA" transform="translate(0,0)"/>
<path d="" fill="#D2C5FD" transform="translate(0,0)"/>
<path d="" fill="#D0C2F8" transform="translate(0,0)"/>
<path d="" fill="#D0C1FA" transform="translate(0,0)"/>
<path d="" fill="#CDBFF8" transform="translate(0,0)"/>
<path d="" fill="#BEAFF5" transform="translate(0,0)"/>
<path d="" fill="#CFC2FB" transform="translate(0,0)"/>
<path d="" fill="#D2C4F9" transform="translate(0,0)"/>
<path d="" fill="#D3C2F7" transform="translate(0,0)"/>
<path d="" fill="#CCBCF6" transform="translate(0,0)"/>
<path d="" fill="#CFC0FA" transform="translate(0,0)"/>
<path d="" fill="#CDBFF9" transform="translate(0,0)"/>
<path d="" fill="#D1C0FB" transform="translate(0,0)"/>
<path d="" fill="#CCBCF8" transform="translate(0,0)"/>
<path d="" fill="#C7BAFB" transform="translate(0,0)"/>
<path d="" fill="#C8BBF7" transform="translate(0,0)"/>
<path d="" fill="#D4C4FA" transform="translate(0,0)"/>
<path d="" fill="#CDBEF8" transform="translate(0,0)"/>
<path d="" fill="#BCAFF3" transform="translate(0,0)"/>
<path d="" fill="#D6C5F9" transform="translate(0,0)"/>
<path d="" fill="#BDAFF5" transform="translate(0,0)"/>
<path d="" fill="#D2C3F8" transform="translate(0,0)"/>
<path d="" fill="#D3C4FD" transform="translate(0,0)"/>
<path d="" fill="#D4C5FA" transform="translate(0,0)"/>
<path d="" fill="#CBBCFA" transform="translate(0,0)"/>
<path d="" fill="#C4B7FA" transform="translate(0,0)"/>
<path d="" fill="#C3B6F9" transform="translate(0,0)"/>
<path d="" fill="#CBBBF9" transform="translate(0,0)"/>
<path d="" fill="#D8C8F9" transform="translate(0,0)"/>
<path d="" fill="#C2B4F9" transform="translate(0,0)"/>
<path d="" fill="#D2C4FB" transform="translate(0,0)"/>
<path d="" fill="#B8ABF1" transform="translate(0,0)"/>
<path d="" fill="#D3C2F7" transform="translate(0,0)"/>
<path d="" fill="#D4C4FA" transform="translate(0,0)"/>
<path d="" fill="#DBC9FB" transform="translate(0,0)"/>
<path d="" fill="#D4C6FB" transform="translate(0,0)"/>
<path d="" fill="#D1C3FA" transform="translate(0,0)"/>
<path d="" fill="#DCC9FD" transform="translate(0,0)"/>
<path d="" fill="#D2C1F7" transform="translate(0,0)"/>
<path d="" fill="#CEBFFC" transform="translate(0,0)"/>
<path d="" fill="#C1B4F5" transform="translate(0,0)"/>
<path d="" fill="#D3C3FB" transform="translate(0,0)"/>
<path d="" fill="#CEC1F9" transform="translate(0,0)"/>
<path d="" fill="#DECFFE" transform="translate(0,0)"/>
<path d="" fill="#CCBBFA" transform="translate(0,0)"/>
<path d="" fill="#C8B9F8" transform="translate(0,0)"/>
<path d="" fill="#CFBDFB" transform="translate(0,0)"/>
<path d="" fill="#D1C2FA" transform="translate(0,0)"/>
<path d="" fill="#D1C0FB" transform="translate(0,0)"/>
<path d="" fill="#C9BBFB" transform="translate(0,0)"/>
<path d="" fill="#CDBEF9" transform="translate(0,0)"/>
<path d="" fill="#D3C3FD" transform="translate(0,0)"/>
<path d="" fill="#D5C3F9" transform="translate(0,0)"/>
<path d="" fill="#DACAFA" transform="translate(0,0)"/>
<path d="" fill="#D1C2FB" transform="translate(0,0)"/>
<path d="" fill="#D5C5F8" transform="translate(0,0)"/>
<path d="" fill="#D2C2FC" transform="translate(0,0)"/>
<path d="" fill="#CFC0F7" transform="translate(0,0)"/>
<path d="" fill="#D0C0FA" transform="translate(0,0)"/>
<path d="" fill="#CABCFA" transform="translate(0,0)"/>
<path d="" fill="#CFC0F9" transform="translate(0,0)"/>
<path d="" fill="#D0C0F7" transform="translate(0,0)"/>
<path d="" fill="#B8AAF1" transform="translate(0,0)"/>
<path d="" fill="#CBBDF9" transform="translate(0,0)"/>
<path d="" fill="#DBC9FA" transform="translate(0,0)"/>
<path d="" fill="#D8CAFB" transform="translate(0,0)"/>
<path d="" fill="#D0C0FC" transform="translate(0,0)"/>
<path d="" fill="#D3C3FA" transform="translate(0,0)"/>
<path d="" fill="#D0C0FA" transform="translate(0,0)"/>
<path d="" fill="#CBBCF3" transform="translate(0,0)"/>
<path d="" fill="#D3C3F6" transform="translate(0,0)"/>
<path d="" fill="#D0BEF6" transform="translate(0,0)"/>
<path d="" fill="#CBBCF7" transform="translate(0,0)"/>
<path d="" fill="#D3C2F8" transform="translate(0,0)"/>
<path d="" fill="#CEBFFB" transform="translate(0,0)"/>
<path d="" fill="#BDADF0" transform="translate(0,0)"/>
<path d="" fill="#C8B8F5" transform="translate(0,0)"/>
<path d="" fill="#D0BFF9" transform="translate(0,0)"/>
<path d="" fill="#D2C4FA" transform="translate(0,0)"/>
<path d="" fill="#DDCAF9" transform="translate(0,0)"/>
<path d="" fill="#D1C3FA" transform="translate(0,0)"/>
<path d="" fill="#CDC1FD" transform="translate(0,0)"/>
<path d="" fill="#BDB2F4" transform="translate(0,0)"/>
<path d="" fill="#CBBBFB" transform="translate(0,0)"/>
<path d="" fill="#CCBEFA" transform="translate(0,0)"/>
<path d="" fill="#D3C4FA" transform="translate(0,0)"/>
<path d="" fill="#CFBFFB" transform="translate(0,0)"/>
<path d="" fill="#D3C4FD" transform="translate(0,0)"/>
<path d="" fill="#CEC3FA" transform="translate(0,0)"/>
<path d="" fill="#D5C1FB" transform="translate(0,0)"/>
<path d="" fill="#BEB2F5" transform="translate(0,0)"/>
<path d="" fill="#CEBCFA" transform="translate(0,0)"/>
<path d="" fill="#CFC2FB" transform="translate(0,0)"/>
<path d="" fill="#CABBF9" transform="translate(0,0)"/>
<path d="" fill="#C8BBF7" transform="translate(0,0)"/>
<path d="" fill="#C6B5F6" transform="translate(0,0)"/>
<path d="" fill="#D8C9FB" transform="translate(0,0)"/>
<path d="" fill="#D7C7FB" transform="translate(0,0)"/>
<path d="" fill="#D6C7F8" transform="translate(0,0)"/>
<path d="" fill="#D1BFFA" transform="translate(0,0)"/>
<path d="" fill="#CFBFFB" transform="translate(0,0)"/>
<path d="" fill="#CEBDF7" transform="translate(0,0)"/>
<path d="" fill="#CCBDFB" transform="translate(0,0)"/>
<path d="" fill="#CBBBF8" transform="translate(0,0)"/>
<path d="" fill="#C4B5F9" transform="translate(0,0)"/>
<path d="" fill="#C7B9FA" transform="translate(0,0)"/>
<path d="" fill="#C4B9F9" transform="translate(0,0)"/>
<path d="" fill="#CDC0F9" transform="translate(0,0)"/>
<path d="" fill="#D9C9FF" transform="translate(0,0)"/>
<path d="" fill="#D7C7F9" transform="translate(0,0)"/>
<path d="" fill="#CEBFF6" transform="translate(0,0)"/>
<path d="" fill="#C9BDF8" transform="translate(0,0)"/>
<path d="" fill="#D2C2FA" transform="translate(0,0)"/>
<path d="" fill="#D2C3FA" transform="translate(0,0)"/>
<path d="" fill="#DBCAF9" transform="translate(0,0)"/>
<path d="" fill="#DACAFD" transform="translate(0,0)"/>
<path d="" fill="#CDC0FB" transform="translate(0,0)"/>
<path d="" fill="#D6C5FC" transform="translate(0,0)"/>
<path d="" fill="#CDBEFC" transform="translate(0,0)"/>
<path d="" fill="#D7C8FB" transform="translate(0,0)"/>
<path d="" fill="#CEBFF8" transform="translate(0,0)"/>
<path d="" fill="#CBBBF8" transform="translate(0,0)"/>
<path d="" fill="#D3C1FA" transform="translate(0,0)"/>
<path d="" fill="#D3C8FB" transform="translate(0,0)"/>
<path d="" fill="#C9BAFB" transform="translate(0,0)"/>
<path d="" fill="#D1C1F7" transform="translate(0,0)"/>
<path d="" fill="#D8C9F9" transform="translate(0,0)"/>
<path d="" fill="#CABCF9" transform="translate(0,0)"/>
<path d="" fill="#D0C1F9" transform="translate(0,0)"/>
<path d="" fill="#CDBCF8" transform="translate(0,0)"/>
<path d="" fill="#D1C1F7" transform="translate(0,0)"/>
<path d="" fill="#C5B9F4" transform="translate(0,0)"/>
<path d="" fill="#CDBDFA" transform="translate(0,0)"/>
<path d="" fill="#CEBDF7" transform="translate(0,0)"/>
<path d="" fill="#CABCF8" transform="translate(0,0)"/>
<path d="" fill="#DBCEFA" transform="translate(0,0)"/>
<path d="" fill="#CEC0FA" transform="translate(0,0)"/>
<path d="" fill="#D6C8FA" transform="translate(0,0)"/>
<path d="" fill="#D5C6FC" transform="translate(0,0)"/>
<path d="" fill="#CABDFB" transform="translate(0,0)"/>
<path d="" fill="#CDBEF8" transform="translate(0,0)"/>
<path d="" fill="#CCBBFA" transform="translate(0,0)"/>
<path d="" fill="#CABBFB" transform="translate(0,0)"/>
<path d="" fill="#CEC1FA" transform="translate(0,0)"/>
<path d="" fill="#CEBDF9" transform="translate(0,0)"/>
<path d="" fill="#D9C5FA" transform="translate(0,0)"/>
<path d="" fill="#D9CBFB" transform="translate(0,0)"/>
<path d="" fill="#D1C0F9" transform="translate(0,0)"/>
<path d="" fill="#C9BDF9" transform="translate(0,0)"/>
<path d="" fill="#CDBEFB" transform="translate(0,0)"/>
<path d="" fill="#CDBBF7" transform="translate(0,0)"/>
<path d="" fill="#D0C1FA" transform="translate(0,0)"/>
<path d="" fill="#D0C0F9" transform="translate(0,0)"/>
<path d="" fill="#C6B9FA" transform="translate(0,0)"/>
<path d="" fill="#CABAFB" transform="translate(0,0)"/>
<path d="" fill="#DDCEFB" transform="translate(0,0)"/>
<path d="" fill="#D8C5F7" transform="translate(0,0)"/>
<path d="" fill="#CEBFF9" transform="translate(0,0)"/>
<path d="" fill="#CBBAF9" transform="translate(0,0)"/>
<path d="" fill="#C8B9F6" transform="translate(0,0)"/>
<path d="" fill="#BCB1F2" transform="translate(0,0)"/>
<path d="" fill="#BBAEF1" transform="translate(0,0)"/>
<path d="" fill="#CEBDF9" transform="translate(0,0)"/>
<path d="" fill="#D4C9F9" transform="translate(0,0)"/>
<path d="" fill="#CABDF7" transform="translate(0,0)"/>
<path d="" fill="#C5B7FA" transform="translate(0,0)"/>
<path d="" fill="#C4B6F7" transform="translate(0,0)"/>
<path d="" fill="#D5C5FE" transform="translate(0,0)"/>
<path d="" fill="#D7C8FA" transform="translate(0,0)"/>
<path d="" fill="#B8ABF0" transform="translate(0,0)"/>
<path d="" fill="#C6B7FA" transform="translate(0,0)"/>
<path d="" fill="#C4B7F8" transform="translate(0,0)"/>
<path d="" fill="#B7ADF3" transform="translate(0,0)"/>
<path d="" fill="#C9BBFA" transform="translate(0,0)"/>
<path d="" fill="#C3B5F1" transform="translate(0,0)"/>
<path d="" fill="#AFA5EB" transform="translate(0,0)"/>
<path d="" fill="#C4B7F4" transform="translate(0,0)"/>
<path d="" fill="#C5B9F8" transform="translate(0,0)"/>
<path d="" fill="#C3B5F9" transform="translate(0,0)"/>
<path d="" fill="#BDAFF0" transform="translate(0,0)"/>
<path d="" fill="#C4B7F7" transform="translate(0,0)"/>
<path d="" fill="#C2B5F8" transform="translate(0,0)"/>
<path d="" fill="#C5B7F6" transform="translate(0,0)"/>
<path d="" fill="#BEB3F5" transform="translate(0,0)"/>
<path d="" fill="#CCBDF6" transform="translate(0,0)"/>
<path d="" fill="#CDBEF8" transform="translate(0,0)"/>
<path d="" fill="#CCBFF8" transform="translate(0,0)"/>
<path d="" fill="#C2B6F7" transform="translate(0,0)"/>
<path d="" fill="#C4B5F5" transform="translate(0,0)"/>
<path d="" fill="#CEBFFA" transform="translate(0,0)"/>
<path d="" fill="#CEBFF7" transform="translate(0,0)"/>
<path d="" fill="#C5B5F6" transform="translate(0,0)"/>
<path d="" fill="#C8BAF7" transform="translate(0,0)"/>
<path d="" fill="#D0BCF9" transform="translate(0,0)"/>
<path d="" fill="#D0C1FD" transform="translate(0,0)"/>
<path d="" fill="#C1B4F3" transform="translate(0,0)"/>
<path d="" fill="#CEC1F8" transform="translate(0,0)"/>
<path d="" fill="#BEB3F3" transform="translate(0,0)"/>
<path d="" fill="#B9ACEF" transform="translate(0,0)"/>
<path d="" fill="#BDB0F1" transform="translate(0,0)"/>
<path d="" fill="#BFB1F7" transform="translate(0,0)"/>
<path d="" fill="#C8BAF5" transform="translate(0,0)"/>
<path d="" fill="#C8BBF9" transform="translate(0,0)"/>
<path d="" fill="#C0B3F3" transform="translate(0,0)"/>
<path d="" fill="#C0B0F4" transform="translate(0,0)"/>
<path d="" fill="#B8A9EF" transform="translate(0,0)"/>
<path d="" fill="#BDB3EF" transform="translate(0,0)"/>
<path d="" fill="#BAADF5" transform="translate(0,0)"/>
<path d="" fill="#CFBAF5" transform="translate(0,0)"/>
<path d="" fill="#D0BEF8" transform="translate(0,0)"/>
<path d="" fill="#C2B4F6" transform="translate(0,0)"/>
<path d="" fill="#C3B8F8" transform="translate(0,0)"/>
<path d="" fill="#BDB1F3" transform="translate(0,0)"/>
<path d="" fill="#BEB2F6" transform="translate(0,0)"/>
<path d="" fill="#C0B2F2" transform="translate(0,0)"/>
<path d="" fill="#C6BAF5" transform="translate(0,0)"/>
<path d="" fill="#BEB1F7" transform="translate(0,0)"/>
<path d="" fill="#BEB1F6" transform="translate(0,0)"/>
<path d="" fill="#C0B0F5" transform="translate(0,0)"/>
<path d="" fill="#BFB2F6" transform="translate(0,0)"/>
<path d="" fill="#BEB2F7" transform="translate(0,0)"/>
<path d="" fill="#C6B9F8" transform="translate(0,0)"/>
<path d="" fill="#C6B9F7" transform="translate(0,0)"/>
<path d="" fill="#C0B4F6" transform="translate(0,0)"/>
<path d="" fill="#BDB1F1" transform="translate(0,0)"/>
<path d="" fill="#B9AFF5" transform="translate(0,0)"/>
<path d="" fill="#B9AEF1" transform="translate(0,0)"/>
<path d="" fill="#BAAEF0" transform="translate(0,0)"/>
<path d="" fill="#C2B5F6" transform="translate(0,0)"/>
<path d="" fill="#C4B9F6" transform="translate(0,0)"/>
<path d="" fill="#C3B4F5" transform="translate(0,0)"/>
<path d="" fill="#C0B3F4" transform="translate(0,0)"/>
<path d="" fill="#BDB1F4" transform="translate(0,0)"/>
<path d="" fill="#BBAFF1" transform="translate(0,0)"/>
<path d="" fill="#BDB2F4" transform="translate(0,0)"/>
<path d="" fill="#BBB1F6" transform="translate(0,0)"/>
<path d="" fill="#9C9AF6" transform="translate(0,0)"/>
<path d="" fill="#A5A2FD" transform="translate(0,0)"/>
<path d="" fill="#A19CFB" transform="translate(0,0)"/>
<path d="" fill="#9E98F9" transform="translate(0,0)"/>
<path d="" fill="#9F9AFB" transform="translate(0,0)"/>
<path d="" fill="#9D96FA" transform="translate(0,0)"/>
<path d="" fill="#A099FA" transform="translate(0,0)"/>
<path d="" fill="#9D98FB" transform="translate(0,0)"/>
<path d="" fill="#9D98FC" transform="translate(0,0)"/>
<path d="" fill="#9D96FD" transform="translate(0,0)"/>
<path d="" fill="#9C98F6" transform="translate(0,0)"/>
<path d="" fill="#9B9CFE" transform="translate(0,0)"/>
<path d="" fill="#9E9BFD" transform="translate(0,0)"/>
<path d="" fill="#A098FC" transform="translate(0,0)"/>
<path d="" fill="#9D99FB" transform="translate(0,0)"/>
<path d="" fill="#9A98FB" transform="translate(0,0)"/>
<path d="" fill="#9C9BFA" transform="translate(0,0)"/>
<path d="" fill="#9D9CFB" transform="translate(0,0)"/>
<path d="" fill="#9695FA" transform="translate(0,0)"/>
<path d="" fill="#9694FA" transform="translate(0,0)"/>
<path d="" fill="#9896F9" transform="translate(0,0)"/>
<path d="" fill="#9997FC" transform="translate(0,0)"/>
<path d="" fill="#9B9AF8" transform="translate(0,0)"/>
<path d="" fill="#9996F9" transform="translate(0,0)"/>
<path d="" fill="#9894F5" transform="translate(0,0)"/>
<path d="" fill="#9999F9" transform="translate(0,0)"/>
<path d="" fill="#9C98FC" transform="translate(0,0)"/>
<path d="" fill="#9C9AFD" transform="translate(0,0)"/>
<path d="" fill="#9C96FB" transform="translate(0,0)"/>
<path d="" fill="#9A96FC" transform="translate(0,0)"/>
<path d="" fill="#9796FB" transform="translate(0,0)"/>
<path d="" fill="#9B98FC" transform="translate(0,0)"/>
<path d="" fill="#9D98FD" transform="translate(0,0)"/>
<path d="" fill="#9998FB" transform="translate(0,0)"/>
<path d="" fill="#9A95FB" transform="translate(0,0)"/>
<path d="" fill="#9C97F8" transform="translate(0,0)"/>
<path d="" fill="#989AF8" transform="translate(0,0)"/>
<path d="" fill="#9C96FB" transform="translate(0,0)"/>
<path d="" fill="#9B95FA" transform="translate(0,0)"/>
<path d="" fill="#9E9BFD" transform="translate(0,0)"/>
<path d="" fill="#9C95FA" transform="translate(0,0)"/>
<path d="" fill="#9B98F6" transform="translate(0,0)"/>
<path d="" fill="#9C98FB" transform="translate(0,0)"/>
<path d="" fill="#9A96F8" transform="translate(0,0)"/>
<path d="" fill="#9B98FE" transform="translate(0,0)"/>
<path d="" fill="#9897FB" transform="translate(0,0)"/>
<path d="" fill="#9A96FB" transform="translate(0,0)"/>
<path d="" fill="#9090F3" transform="translate(0,0)"/>
<path d="" fill="#9A97FD" transform="translate(0,0)"/>
<path d="" fill="#9798FA" transform="translate(0,0)"/>
<path d="" fill="#9693F8" transform="translate(0,0)"/>
<path d="" fill="#9593F8" transform="translate(0,0)"/>
<path d="" fill="#9A97F9" transform="translate(0,0)"/>
<path d="" fill="#9895F9" transform="translate(0,0)"/>
<path d="" fill="#9792F9" transform="translate(0,0)"/>
<path d="" fill="#9A96F8" transform="translate(0,0)"/>
<path d="" fill="#9996FA" transform="translate(0,0)"/>
<path d="" fill="#9B97FA" transform="translate(0,0)"/>
<path d="" fill="#9997FB" transform="translate(0,0)"/>
<path d="" fill="#9695F7" transform="translate(0,0)"/>
<path d="" fill="#9796F8" transform="translate(0,0)"/>
<path d="" fill="#9493FA" transform="translate(0,0)"/>
<path d="" fill="#9495F8" transform="translate(0,0)"/>
<path d="" fill="#9996FC" transform="translate(0,0)"/>
<path d="" fill="#9596F9" transform="translate(0,0)"/>
<path d="" fill="#9C96FC" transform="translate(0,0)"/>
<path d="" fill="#9996F9" transform="translate(0,0)"/>
<path d="" fill="#9994FB" transform="translate(0,0)"/>
<path d="" fill="#9695F8" transform="translate(0,0)"/>
<path d="" fill="#9694F9" transform="translate(0,0)"/>
<path d="" fill="#9899FA" transform="translate(0,0)"/>
<path d="" fill="#9691F9" transform="translate(0,0)"/>
<path d="" fill="#9691F8" transform="translate(0,0)"/>
<path d="" fill="#9B98FA" transform="translate(0,0)"/>
<path d="" fill="#9696FB" transform="translate(0,0)"/>
<path d="" fill="#9697F5" transform="translate(0,0)"/>
<path d="" fill="#9E9AFB" transform="translate(0,0)"/>
<path d="" fill="#9594F9" transform="translate(0,0)"/>
<path d="" fill="#9794FA" transform="translate(0,0)"/>
<path d="" fill="#9896FA" transform="translate(0,0)"/>
<path d="" fill="#9695FA" transform="translate(0,0)"/>
<path d="" fill="#9695F9" transform="translate(0,0)"/>
<path d="" fill="#9592F9" transform="translate(0,0)"/>
<path d="" fill="#9894F7" transform="translate(0,0)"/>
<path d="" fill="#9C96FA" transform="translate(0,0)"/>
<path d="" fill="#9392F7" transform="translate(0,0)"/>
<path d="" fill="#9794FB" transform="translate(0,0)"/>
<path d="" fill="#9793F9" transform="translate(0,0)"/>
<path d="" fill="#9793FA" transform="translate(0,0)"/>
<path d="" fill="#9593FC" transform="translate(0,0)"/>
<path d="" fill="#9793F9" transform="translate(0,0)"/>
<path d="" fill="#9794F9" transform="translate(0,0)"/>
<path d="" fill="#9996F9" transform="translate(0,0)"/>
<path d="" fill="#9591F7" transform="translate(0,0)"/>
<path d="" fill="#9D9AF9" transform="translate(0,0)"/>
<path d="" fill="#9C99FA" transform="translate(0,0)"/>
<path d="" fill="#9895FD" transform="translate(0,0)"/>
<path d="" fill="#9796F7" transform="translate(0,0)"/>
<path d="" fill="#9694F9" transform="translate(0,0)"/>
<path d="" fill="#9491F7" transform="translate(0,0)"/>
<path d="" fill="#9796FA" transform="translate(0,0)"/>
<path d="" fill="#9995FB" transform="translate(0,0)"/>
<path d="" fill="#9696F8" transform="translate(0,0)"/>
<path d="" fill="#9D9CFA" transform="translate(0,0)"/>
<path d="" fill="#8E92F4" transform="translate(0,0)"/>
<path d="" fill="#9393F9" transform="translate(0,0)"/>
<path d="" fill="#9794FB" transform="translate(0,0)"/>
<path d="" fill="#9192F7" transform="translate(0,0)"/>
<path d="" fill="#9692FB" transform="translate(0,0)"/>
<path d="" fill="#9792F9" transform="translate(0,0)"/>
<path d="" fill="#9792FA" transform="translate(0,0)"/>
<path d="" fill="#9593F9" transform="translate(0,0)"/>
<path d="" fill="#9A9AFC" transform="translate(0,0)"/>
<path d="" fill="#9B9AFE" transform="translate(0,0)"/>
<path d="" fill="#9795FD" transform="translate(0,0)"/>
<path d="" fill="#9A9BFD" transform="translate(0,0)"/>
<path d="" fill="#9C9AFC" transform="translate(0,0)"/>
<path d="" fill="#9997FC" transform="translate(0,0)"/>
<path d="" fill="#989AFB" transform="translate(0,0)"/>
<path d="" fill="#989BFC" transform="translate(0,0)"/>
<path d="" fill="#9A98FC" transform="translate(0,0)"/>
<path d="" fill="#9391F9" transform="translate(0,0)"/>
<path d="" fill="#9997FE" transform="translate(0,0)"/>
<path d="" fill="#9B99FD" transform="translate(0,0)"/>
<path d="" fill="#9697FA" transform="translate(0,0)"/>
<path d="" fill="#9593F9" transform="translate(0,0)"/>
<path d="" fill="#9693FB" transform="translate(0,0)"/>
<path d="" fill="#9A98FB" transform="translate(0,0)"/>
<path d="" fill="#9A99FB" transform="translate(0,0)"/>
<path d="" fill="#9999FD" transform="translate(0,0)"/>
<path d="" fill="#9B97FD" transform="translate(0,0)"/>
<path d="" fill="#9D9BFE" transform="translate(0,0)"/>
<path d="" fill="#9C99FF" transform="translate(0,0)"/>
<path d="" fill="#9F9CFE" transform="translate(0,0)"/>
<path d="" fill="#9C9CFF" transform="translate(0,0)"/>
<path d="" fill="#9698FE" transform="translate(0,0)"/>
<path d="" fill="#9A94FB" transform="translate(0,0)"/>
<path d="" fill="#9B9AFF" transform="translate(0,0)"/>
<path d="" fill="#9897FA" transform="translate(0,0)"/>
<path d="" fill="#9796FD" transform="translate(0,0)"/>
<path d="" fill="#9594FA" transform="translate(0,0)"/>
<path d="" fill="#9D97FD" transform="translate(0,0)"/>
<path d="" fill="#9D98FD" transform="translate(0,0)"/>
<path d="" fill="#9D9CFE" transform="translate(0,0)"/>
<path d="" fill="#9F9CFD" transform="translate(0,0)"/>
<path d="" fill="#948EF9" transform="translate(0,0)"/>
<path d="" fill="#9A9AFD" transform="translate(0,0)"/>
<path d="" fill="#9696FC" transform="translate(0,0)"/>
<path d="" fill="#999AFD" transform="translate(0,0)"/>
<path d="" fill="#9B9AFE" transform="translate(0,0)"/>
<path d="" fill="#9D9CFE" transform="translate(0,0)"/>
<path d="" fill="#9A9CFF" transform="translate(0,0)"/>
<path d="" fill="#9C99FE" transform="translate(0,0)"/>
<path d="" fill="#9691FC" transform="translate(0,0)"/>
<path d="" fill="#9391F9" transform="translate(0,0)"/>
<path d="" fill="#9997FB" transform="translate(0,0)"/>
<path d="" fill="#9E9BFB" transform="translate(0,0)"/>
<path d="" fill="#9E9CFD" transform="translate(0,0)"/>
<path d="" fill="#9E9DFD" transform="translate(0,0)"/>
<path d="" fill="#A19EFE" transform="translate(0,0)"/>
<path d="" fill="#9C9AFF" transform="translate(0,0)"/>
<path d="" fill="#9B9AFE" transform="translate(0,0)"/>
<path d="" fill="#9B9AFD" transform="translate(0,0)"/>
<path d="" fill="#9E9EFD" transform="translate(0,0)"/>
<path d="" fill="#9493F8" transform="translate(0,0)"/>
<path d="" fill="#9C9AFB" transform="translate(0,0)"/>
<path d="" fill="#9A98FD" transform="translate(0,0)"/>
<path d="" fill="#9390F8" transform="translate(0,0)"/>
<path d="" fill="#9791FA" transform="translate(0,0)"/>
<path d="" fill="#9492F8" transform="translate(0,0)"/>
<path d="" fill="#9896FB" transform="translate(0,0)"/>
<path d="" fill="#9391F9" transform="translate(0,0)"/>
<path d="" fill="#9B99FD" transform="translate(0,0)"/>
<path d="" fill="#9696FB" transform="translate(0,0)"/>
<path d="" fill="#9A97FC" transform="translate(0,0)"/>
<path d="" fill="#9899FB" transform="translate(0,0)"/>
<path d="" fill="#9A95FB" transform="translate(0,0)"/>
<path d="" fill="#9997FC" transform="translate(0,0)"/>
<path d="" fill="#9897FC" transform="translate(0,0)"/>
<path d="" fill="#9B97FC" transform="translate(0,0)"/>
<path d="" fill="#989BFD" transform="translate(0,0)"/>
<path d="" fill="#9693F9" transform="translate(0,0)"/>
<path d="" fill="#9A96FD" transform="translate(0,0)"/>
<path d="" fill="#9E9AFB" transform="translate(0,0)"/>
<path d="" fill="#9A99FC" transform="translate(0,0)"/>
<path d="" fill="#9A98FD" transform="translate(0,0)"/>
<path d="" fill="#9798F8" transform="translate(0,0)"/>
<path d="" fill="#9C9CFF" transform="translate(0,0)"/>
<path d="" fill="#9B98FE" transform="translate(0,0)"/>
<path d="" fill="#938EF9" transform="translate(0,0)"/>
<path d="" fill="#9492F8" transform="translate(0,0)"/>
<path d="" fill="#9799FB" transform="translate(0,0)"/>
<path d="" fill="#9998FC" transform="translate(0,0)"/>
<path d="" fill="#938EF9" transform="translate(0,0)"/>
<path d="" fill="#9898F8" transform="translate(0,0)"/>
<path d="" fill="#9B9BFC" transform="translate(0,0)"/>
<path d="" fill="#9092F8" transform="translate(0,0)"/>
<path d="" fill="#9898F9" transform="translate(0,0)"/>
<path d="" fill="#9592FD" transform="translate(0,0)"/>
<path d="" fill="#9695FC" transform="translate(0,0)"/>
<path d="" fill="#9794FA" transform="translate(0,0)"/>
<path d="" fill="#9B99FC" transform="translate(0,0)"/>
<path d="" fill="#9A98FE" transform="translate(0,0)"/>
<path d="" fill="#9496F7" transform="translate(0,0)"/>
<path d="" fill="#9B98FC" transform="translate(0,0)"/>
<path d="" fill="#938FF9" transform="translate(0,0)"/>
<path d="" fill="#9692F9" transform="translate(0,0)"/>
<path d="" fill="#9495F9" transform="translate(0,0)"/>
<path d="" fill="#928FF7" transform="translate(0,0)"/>
<path d="" fill="#918DF5" transform="translate(0,0)"/>
<path d="" fill="#9A97FE" transform="translate(0,0)"/>
<path d="" fill="#9495FA" transform="translate(0,0)"/>
<path d="" fill="#9A98FD" transform="translate(0,0)"/>
<path d="" fill="#9693FB" transform="translate(0,0)"/>
<path d="" fill="#9694FB" transform="translate(0,0)"/>
<path d="" fill="#9494FC" transform="translate(0,0)"/>
<path d="" fill="#9596F9" transform="translate(0,0)"/>
<path d="" fill="#9591FA" transform="translate(0,0)"/>
<path d="" fill="#9997FD" transform="translate(0,0)"/>
<path d="" fill="#9592FA" transform="translate(0,0)"/>
<path d="" fill="#9595F9" transform="translate(0,0)"/>
<path d="" fill="#9394F7" transform="translate(0,0)"/>
<path d="" fill="#9A99FC" transform="translate(0,0)"/>
<path d="" fill="#9694F9" transform="translate(0,0)"/>
<path d="" fill="#9696F9" transform="translate(0,0)"/>
<path d="" fill="#9A99FD" transform="translate(0,0)"/>
<path d="" fill="#9392F8" transform="translate(0,0)"/>
<path d="" fill="#9796FB" transform="translate(0,0)"/>
<path d="" fill="#9394F9" transform="translate(0,0)"/>
<path d="" fill="#9796FB" transform="translate(0,0)"/>
<path d="" fill="#9695FA" transform="translate(0,0)"/>
<path d="" fill="#9B98FF" transform="translate(0,0)"/>
<path d="" fill="#9999FC" transform="translate(0,0)"/>
<path d="" fill="#9395F9" transform="translate(0,0)"/>
<path d="" fill="#9295F7" transform="translate(0,0)"/>
<path d="" fill="#9695FA" transform="translate(0,0)"/>
<path d="" fill="#9492F5" transform="translate(0,0)"/>
<path d="" fill="#9998F9" transform="translate(0,0)"/>
<path d="" fill="#9595FA" transform="translate(0,0)"/>
<path d="" fill="#9793FA" transform="translate(0,0)"/>
<path d="" fill="#8F93F5" transform="translate(0,0)"/>
<path d="" fill="#9B99FC" transform="translate(0,0)"/>
<path d="" fill="#9390F8" transform="translate(0,0)"/>
<path d="" fill="#9590FB" transform="translate(0,0)"/>
<path d="" fill="#9295F5" transform="translate(0,0)"/>
<path d="" fill="#9494F8" transform="translate(0,0)"/>
<path d="" fill="#9694FC" transform="translate(0,0)"/>
<path d="" fill="#9490F9" transform="translate(0,0)"/>
<path d="" fill="#9597FE" transform="translate(0,0)"/>
<path d="" fill="#9694FA" transform="translate(0,0)"/>
<path d="" fill="#9192F7" transform="translate(0,0)"/>
<path d="" fill="#9490FA" transform="translate(0,0)"/>
<path d="" fill="#9897FB" transform="translate(0,0)"/>
<path d="" fill="#9594F8" transform="translate(0,0)"/>
<path d="" fill="#928CF7" transform="translate(0,0)"/>
<path d="" fill="#9390F9" transform="translate(0,0)"/>
<path d="" fill="#9694F9" transform="translate(0,0)"/>
<path d="" fill="#9794FC" transform="translate(0,0)"/>
<path d="" fill="#9495FA" transform="translate(0,0)"/>
<path d="" fill="#918FF9" transform="translate(0,0)"/>
<path d="" fill="#9893FD" transform="translate(0,0)"/>
<path d="" fill="#9998FA" transform="translate(0,0)"/>
<path d="" fill="#9392F9" transform="translate(0,0)"/>
<path d="" fill="#9898FC" transform="translate(0,0)"/>
<path d="" fill="#9596F7" transform="translate(0,0)"/>
<path d="" fill="#9494F8" transform="translate(0,0)"/>
<path d="" fill="#9897FC" transform="translate(0,0)"/>
<path d="" fill="#9D98F8" transform="translate(0,0)"/>
<path d="" fill="#9794FC" transform="translate(0,0)"/>
<path d="" fill="#9995FB" transform="translate(0,0)"/>
<path d="" fill="#918FF9" transform="translate(0,0)"/>
<path d="" fill="#9793FB" transform="translate(0,0)"/>
<path d="" fill="#9390F7" transform="translate(0,0)"/>
<path d="" fill="#9392F8" transform="translate(0,0)"/>
<path d="" fill="#9396F8" transform="translate(0,0)"/>
<path d="" fill="#938FF7" transform="translate(0,0)"/>
<path d="" fill="#9493FA" transform="translate(0,0)"/>
<path d="" fill="#9792FB" transform="translate(0,0)"/>
<path d="" fill="#9895FD" transform="translate(0,0)"/>
<path d="" fill="#9490F8" transform="translate(0,0)"/>
<path d="" fill="#9794FC" transform="translate(0,0)"/>
<path d="" fill="#9596F9" transform="translate(0,0)"/>
<path d="" fill="#9993F9" transform="translate(0,0)"/>
<path d="" fill="#9290F8" transform="translate(0,0)"/>
<path d="" fill="#9C97FC" transform="translate(0,0)"/>
<path d="" fill="#948FF8" transform="translate(0,0)"/>
<path d="" fill="#9695FC" transform="translate(0,0)"/>
<path d="" fill="#9999FB" transform="translate(0,0)"/>
<path d="" fill="#9795FE" transform="translate(0,0)"/>
<path d="" fill="#9796FC" transform="translate(0,0)"/>
<path d="" fill="#9899F9" transform="translate(0,0)"/>
<path d="" fill="#9996FD" transform="translate(0,0)"/>
<path d="" fill="#9390F6" transform="translate(0,0)"/>
<path d="" fill="#9B99F9" transform="translate(0,0)"/>
<path d="" fill="#9796FB" transform="translate(0,0)"/>
<path d="" fill="#9996FB" transform="translate(0,0)"/>
<path d="" fill="#9997FA" transform="translate(0,0)"/>
<path d="" fill="#9495F9" transform="translate(0,0)"/>
<path d="" fill="#9897FE" transform="translate(0,0)"/>
<path d="" fill="#9997FD" transform="translate(0,0)"/>
<path d="" fill="#9695F9" transform="translate(0,0)"/>
<path d="" fill="#9995FC" transform="translate(0,0)"/>
<path d="" fill="#9494F9" transform="translate(0,0)"/>
<path d="" fill="#9999FB" transform="translate(0,0)"/>
<path d="" fill="#9795F9" transform="translate(0,0)"/>
<path d="" fill="#9396F5" transform="translate(0,0)"/>
<path d="" fill="#9799F8" transform="translate(0,0)"/>
<path d="" fill="#9692FB" transform="translate(0,0)"/>
<path d="" fill="#9792F9" transform="translate(0,0)"/>
<path d="" fill="#9594F6" transform="translate(0,0)"/>
<path d="" fill="#9898FA" transform="translate(0,0)"/>
<path d="" fill="#9695FB" transform="translate(0,0)"/>
<path d="" fill="#9693F9" transform="translate(0,0)"/>
<path d="" fill="#9494F7" transform="translate(0,0)"/>
<path d="" fill="#9297F8" transform="translate(0,0)"/>
<path d="" fill="#9294F6" transform="translate(0,0)"/>
<path d="" fill="#9698F6" transform="translate(0,0)"/>
<path d="" fill="#9595F9" transform="translate(0,0)"/>
<path d="" fill="#5054BF" transform="translate(0,0)"/>
<path d="" fill="#4D53C0" transform="translate(0,0)"/>
</svg>
