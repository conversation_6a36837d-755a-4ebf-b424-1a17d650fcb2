<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C1.301 -0.037 2.603 -0.075 3.943 -0.113 C5.188 -0.125 6.433 -0.136 7.715 -0.148 C8.859 -0.168 10.004 -0.188 11.183 -0.208 C14.062 0.375 14.062 0.375 15.804 2.631 C16.219 3.537 16.635 4.442 17.062 5.375 C17.685 6.474 17.685 6.474 18.321 7.595 C18.926 8.687 18.926 8.687 19.543 9.801 C20.205 10.995 20.205 10.995 20.881 12.213 C21.333 13.03 21.784 13.846 22.25 14.688 C22.713 15.523 23.177 16.359 23.654 17.221 C24.791 19.272 25.927 21.323 27.062 23.375 C27.722 23.375 28.382 23.375 29.062 23.375 C31.062 25.375 31.062 25.375 31.062 28.375 C32.238 31.902 33.816 34.445 36.062 37.375 C36.392 38.365 36.722 39.355 37.062 40.375 C38.05 42.049 39.051 43.716 40.062 45.375 C40.341 45.952 40.619 46.53 40.906 47.125 C42.784 50.78 45.036 54.213 47.229 57.686 C48.822 60.239 50.106 62.506 51.062 65.375 C51.351 64.571 51.64 63.766 51.938 62.938 C53.062 60.375 53.062 60.375 55.062 59.375 C55.718 56.848 55.718 56.848 56.062 54.375 C56.722 54.375 57.382 54.375 58.062 54.375 C58.351 53.488 58.64 52.601 58.938 51.688 C59.894 48.871 60.93 46.124 62.062 43.375 C62.722 43.375 63.382 43.375 64.062 43.375 C64.722 41.065 65.382 38.755 66.062 36.375 C66.722 36.375 67.382 36.375 68.062 36.375 C68.329 35.676 68.596 34.978 68.871 34.258 C70.148 31.169 71.607 28.227 73.125 25.25 C74.635 22.288 76.011 19.53 77.062 16.375 C77.578 15.88 78.094 15.385 78.625 14.875 C80.408 13.36 80.408 13.36 80.062 10.375 C81.062 9.375 82.062 8.375 83.062 7.375 C84.382 5.065 85.702 2.755 87.062 0.375 C95.643 0.375 104.222 0.375 113.062 0.375 C113.062 43.275 113.062 86.175 113.062 130.375 C109.675 131.504 107.068 131.508 103.5 131.5 C102.344 131.503 101.187 131.505 99.996 131.508 C95.339 131.351 90.699 130.839 86.062 130.375 C85.732 106.945 85.403 83.515 85.062 59.375 C81.904 64.428 78.781 69.464 75.812 74.625 C75.462 75.223 75.111 75.821 74.75 76.438 C73.066 79.365 71.627 82.022 71.062 85.375 C70.403 85.375 69.743 85.375 69.062 85.375 C68.825 86.031 68.588 86.687 68.344 87.363 C66.686 91.261 64.648 94.881 62.562 98.562 C62.159 99.286 61.756 100.01 61.34 100.756 C60.949 101.449 60.559 102.142 60.156 102.855 C59.625 103.797 59.625 103.797 59.084 104.759 C58.062 106.375 58.062 106.375 56.062 108.375 C53.242 108.57 53.242 108.57 49.938 108.5 C48.842 108.482 47.746 108.464 46.617 108.445 C45.774 108.422 44.931 108.399 44.062 108.375 C40.062 101.625 40.062 101.625 40.062 99.375 C39.403 99.375 38.743 99.375 38.062 99.375 C37.508 98.243 36.954 97.111 36.383 95.945 C35.651 94.463 34.919 92.982 34.188 91.5 C33.823 90.754 33.458 90.007 33.082 89.238 C32.728 88.523 32.373 87.807 32.008 87.07 C31.521 86.081 31.521 86.081 31.024 85.071 C29.723 82.777 28.418 81.553 26.062 80.375 C25.875 79.06 25.875 79.06 25.684 77.719 C24.938 73.703 23.364 71.276 21.125 67.875 C18.428 63.764 16.425 60.111 15.062 55.375 C14.402 55.375 13.743 55.375 13.062 55.375 C13.062 79.795 13.062 104.215 13.062 129.375 C8.641 131.144 6.826 131.618 2.324 131.57 C0.724 131.561 0.724 131.561 -0.908 131.551 C-2.562 131.526 -2.562 131.526 -4.25 131.5 C-5.372 131.491 -6.494 131.482 -7.65 131.473 C-10.413 131.449 -13.175 131.416 -15.938 131.375 C-16.062 124.75 -16.062 124.75 -14.938 121.375 C-15.598 121.045 -16.257 120.715 -16.938 120.375 C-16.775 119.867 -16.612 119.358 -16.444 118.835 C-15.764 115.531 -15.826 112.392 -15.84 109.02 C-15.841 108.317 -15.843 107.615 -15.844 106.892 C-15.85 104.657 -15.862 102.422 -15.875 100.188 C-15.88 98.673 -15.885 97.158 -15.889 95.643 C-15.9 91.887 -15.918 88.131 -15.938 84.375 C-15.938 84.045 -15.938 83.715 -15.938 83.375 C-16.267 82.385 -16.598 81.395 -16.938 80.375 C-16.615 79.039 -16.28 77.706 -15.938 76.375 C-15.899 74.375 -15.892 72.374 -15.938 70.375 C-15.277 70.375 -14.618 70.375 -13.938 70.375 C-13.938 69.385 -13.938 68.395 -13.938 67.375 C-14.598 67.045 -15.257 66.715 -15.938 66.375 C-15.938 56.375 -15.938 56.375 -15.375 54 C-14.9 51.147 -15.223 49.155 -15.938 46.375 C-15.277 46.375 -14.618 46.375 -13.938 46.375 C-14.288 45.303 -14.639 44.23 -15 43.125 C-15.91 39.483 -15.906 38.709 -14.938 35.375 C-15.224 33.368 -15.556 31.366 -15.938 29.375 C-15.938 26.042 -15.938 22.708 -15.938 19.375 C-16.164 18.591 -16.391 17.808 -16.625 17 C-16.728 16.134 -16.831 15.268 -16.938 14.375 C-15.5 12.562 -15.5 12.562 -13.938 11.375 C-14.598 11.375 -15.257 11.375 -15.938 11.375 C-15.277 8.405 -14.618 5.435 -13.938 2.375 C-14.598 2.045 -15.257 1.715 -15.938 1.375 C-11.346 -0.921 -5.041 0.055 0 0 Z M-14.938 59.375 C-13.938 61.375 -13.938 61.375 -13.938 61.375 Z " fill="#FBFBFE" transform="translate(447.9375,188.625)"/>
<path d="M0 0 C3.545 -0.217 7.077 -0.375 10.625 -0.5 C12.124 -0.601 12.124 -0.601 13.652 -0.703 C20.933 -0.896 20.933 -0.896 23.922 1.614 C25.484 3.671 26.761 5.737 28 8 C28.652 9.005 29.305 10.011 29.977 11.047 C31.082 12.859 32.186 14.671 33.289 16.484 C34.902 19.038 34.902 19.038 37.035 20.977 C39.34 23.351 40.438 25.622 41.812 28.625 C44.183 33.525 46.822 37.847 50.016 42.242 C52.424 45.589 54.716 49.005 57 52.438 C57.427 53.078 57.854 53.719 58.294 54.38 C61.418 59.082 64.455 63.834 67.43 68.633 C69.145 71.218 71.03 73.604 73 76 C74.918 74.082 74.129 71.239 74.139 68.687 C74.137 67.802 74.135 66.916 74.133 66.004 C74.134 64.648 74.134 64.648 74.136 63.265 C74.136 61.349 74.135 59.433 74.13 57.517 C74.125 54.579 74.13 51.64 74.137 48.701 C74.136 46.844 74.135 44.986 74.133 43.129 C74.136 41.804 74.136 41.804 74.139 40.452 C74.258 36.089 74.258 36.089 73 32 C73.66 32 74.32 32 75 32 C74.959 31.279 74.918 30.559 74.876 29.816 C74.697 26.524 74.536 23.231 74.375 19.938 C74.311 18.804 74.246 17.67 74.18 16.502 C74.128 15.397 74.077 14.291 74.023 13.152 C73.971 12.142 73.919 11.131 73.865 10.09 C74 7 74 7 76 0 C84.58 0 93.16 0 102 0 C102 42.9 102 85.8 102 130 C98.582 131.139 95.965 131.113 92.363 131.098 C91.065 131.094 89.766 131.091 88.428 131.088 C87.056 131.08 85.684 131.071 84.312 131.062 C82.929 131.057 81.546 131.053 80.162 131.049 C76.775 131.037 73.387 131.021 70 131 C70.165 130.216 70.33 129.433 70.5 128.625 C71.062 126.118 71.062 126.118 71 124 C71 123.34 71 122.68 71 122 C69.68 122.33 68.36 122.66 67 123 C67.495 121.02 67.495 121.02 68 119 C66.68 118.01 65.36 117.02 64 116 C64.66 116 65.32 116 66 116 C66 115.34 66 114.68 66 114 C65.34 114 64.68 114 64 114 C61 109.615 61 109.615 61 107 C60.01 106.67 59.02 106.34 58 106 C56 103 56 103 55.184 100.797 C53.569 96.98 51.358 93.689 49.062 90.25 C48.62 89.566 48.177 88.881 47.721 88.176 C47.288 87.525 46.856 86.874 46.41 86.203 C46.02 85.612 45.629 85.02 45.227 84.411 C43.95 82.701 43.95 82.701 41 82 C40.353 79.923 39.711 77.844 39.094 75.758 C37.3 71.234 34.446 67.457 31.645 63.504 C30 61 30 61 30 59 C28.68 59 27.36 59 26 59 C26.107 67.44 26.22 75.881 26.339 84.321 C26.395 88.24 26.448 92.159 26.497 96.078 C26.545 99.858 26.597 103.638 26.653 107.418 C26.673 108.863 26.692 110.307 26.709 111.751 C26.733 113.769 26.764 115.788 26.795 117.806 C26.818 119.532 26.818 119.532 26.842 121.292 C26.82 123.968 26.82 123.968 28 126 C27.562 128.062 27.562 128.062 26 130 C19.326 131.607 12.766 130.733 6 130 C5.505 130.99 5.505 130.99 5 132 C4.402 131.835 3.804 131.67 3.188 131.5 C0.887 130.889 0.887 130.889 -2 131 C-2 127.37 -2 123.74 -2 120 C-1.67 120 -1.34 120 -1 120 C-1 94.26 -1 68.52 -1 42 C-1.33 42 -1.66 42 -2 42 C-2 34.41 -2 26.82 -2 19 C-1.67 19 -1.34 19 -1 19 C-0.67 12.73 -0.34 6.46 0 0 Z M-1 27 C-1 28.98 -1 30.96 -1 33 C-0.67 33 -0.34 33 0 33 C0 31.02 0 29.04 0 27 C-0.33 27 -0.66 27 -1 27 Z M74 77 C74.66 78.32 75.32 79.64 76 81 C75.67 79.68 75.34 78.36 75 77 C74.67 77 74.34 77 74 77 Z " fill="#FDFDFE" transform="translate(634,189)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.536 2.206 1.072 2.413 1.625 2.625 C4.92 4.533 7.594 7.048 10 10 C10 10.66 10 11.32 10 12 C10.66 12 11.32 12 12 12 C13.508 14.117 13.508 14.117 15.125 16.875 C15.933 18.232 15.933 18.232 16.758 19.617 C18 22 18 22 18 24 C19.32 23.67 20.64 23.34 22 23 C22 23.99 22 24.98 22 26 C21.34 26 20.68 26 20 26 C20.277 26.869 20.554 27.738 20.84 28.633 C21.202 29.785 21.564 30.938 21.938 32.125 C22.477 33.83 22.477 33.83 23.027 35.57 C28.061 53.319 25.763 72.204 17.141 88.258 C8.106 104.402 -6.57 114.911 -24.188 119.938 C-26.787 120.647 -29.392 121.325 -32 122 C-32.99 122.33 -33.98 122.66 -35 123 C-35 123.33 -35 123.66 -35 124 C-36.98 124.33 -38.96 124.66 -41 125 C-41 124.01 -41 123.02 -41 122 C-41.608 122.035 -42.217 122.07 -42.844 122.105 C-48.935 122.294 -54.652 121.721 -60.625 120.625 C-61.336 120.499 -62.047 120.373 -62.779 120.243 C-64.535 119.901 -66.271 119.459 -68 119 C-68.33 118.34 -68.66 117.68 -69 117 C-70.052 116.711 -71.104 116.423 -72.188 116.125 C-76.109 114.968 -78.651 113.283 -82 111 C-82.99 111 -83.98 111 -85 111 C-85.248 110.361 -85.495 109.721 -85.75 109.062 C-86.163 108.382 -86.575 107.701 -87 107 C-87.625 106.878 -88.25 106.755 -88.895 106.629 C-91.785 105.766 -92.383 104.416 -93.938 101.875 C-94.448 101.068 -94.958 100.261 -95.484 99.43 C-95.985 98.628 -96.485 97.826 -97 97 C-100.067 91.921 -100.067 91.921 -105 89 C-104.67 88.01 -104.34 87.02 -104 86 C-104.695 83.931 -105.481 81.893 -106.312 79.875 C-109.04 72.872 -109.97 66.067 -110.641 58.617 C-110.894 55.172 -110.894 55.172 -111.609 52.57 C-111.738 52.052 -111.867 51.534 -112 51 C-111.505 50.505 -111.505 50.505 -111 50 C-110.687 48.161 -110.379 46.321 -110.101 44.477 C-107.754 29.15 -101.783 15.399 -91 4 C-90.34 4 -89.68 4 -89 4 C-89 3.34 -89 2.68 -89 2 C-84.031 -2.134 -75.744 -9 -69 -9 C-68.67 -9.66 -68.34 -10.32 -68 -11 C-65.069 -11.657 -62.216 -12.185 -59.25 -12.625 C-57.948 -12.818 -57.948 -12.818 -56.619 -13.016 C-35.726 -15.957 -17.169 -12.619 0 0 Z M-52 12 C-52 12.33 -52 12.66 -52 13 C-49.69 13 -47.38 13 -45 13 C-45 12.67 -45 12.34 -45 12 C-47.31 12 -49.62 12 -52 12 Z M-40 12 C-40 12.33 -40 12.66 -40 13 C-38.02 13 -36.04 13 -34 13 C-34 12.67 -34 12.34 -34 12 C-35.98 12 -37.96 12 -40 12 Z M-44 12 C-44 12.33 -44 12.66 -44 13 C-46.64 13.66 -49.28 14.32 -52 15 C-52 14.34 -52 13.68 -52 13 C-58.914 13.532 -65.089 16.926 -70.062 21.688 C-72.214 23.753 -72.214 23.753 -72 26 C-72.99 26.33 -73.98 26.66 -75 27 C-82.916 39.261 -84.191 53.488 -81.859 67.633 C-79.747 75.91 -74.795 84.719 -67.625 89.625 C-66.759 90.079 -65.892 90.533 -65 91 C-64.01 91.681 -63.02 92.361 -62 93.062 C-53.278 97.971 -41.574 97.815 -32 96 C-25.597 93.974 -20.014 91.55 -15 87 C-15 86.34 -15 85.68 -15 85 C-14.34 85 -13.68 85 -13 85 C-3.544 73.014 -1.617 60.826 -3 46 C-4.583 36.662 -8.538 28.953 -15 22 C-15.99 21.67 -16.98 21.34 -18 21 C-18.474 20.464 -18.949 19.928 -19.438 19.375 C-20.211 18.694 -20.211 18.694 -21 18 C-23.163 18.228 -23.163 18.228 -25 19 C-24.67 18.01 -24.34 17.02 -24 16 C-28.353 13.655 -28.353 13.655 -33.062 13.312 C-33.702 13.539 -34.341 13.766 -35 14 C-37 14.038 -39.001 14.045 -41 14 C-41 13.34 -41 12.68 -41 12 C-41.99 12 -42.98 12 -44 12 Z " fill="#FCFCFE" transform="translate(473,377)"/>
<path d="M0 0 C14.159 -0.407 27.64 2.647 39.664 10.129 C39.994 9.469 40.324 8.809 40.664 8.129 C42.664 10.129 42.664 10.129 43.559 12.051 C44.843 14.464 46.233 15.664 48.352 17.379 C52.532 20.917 56.41 24.706 59.664 29.129 C59.664 29.789 59.664 30.449 59.664 31.129 C60.324 31.459 60.984 31.789 61.664 32.129 C71.433 50.715 73.793 71.965 67.664 92.129 C61.813 107.36 51.323 120.967 36.379 128.059 C34.323 129.088 34.323 129.088 33.664 132.129 C32.674 131.799 31.684 131.469 30.664 131.129 C27.604 131.857 27.604 131.857 24.289 133.004 C5.311 139.243 -14.323 136.873 -32.199 128.277 C-34.336 127.129 -34.336 127.129 -37.336 125.129 C-37.996 124.799 -38.656 124.469 -39.336 124.129 C-40.161 123.283 -40.986 122.438 -41.836 121.566 C-43.993 118.896 -43.993 118.896 -46.336 119.129 C-47.382 117.491 -48.376 115.819 -49.336 114.129 C-51.256 111.329 -52.445 110.056 -55.336 108.129 C-56.336 107.129 -56.336 107.129 -56.77 105.348 C-57.587 102.145 -59.067 99.317 -60.496 96.348 C-61.336 94.129 -61.336 94.129 -61.336 90.129 C-61.996 89.799 -62.656 89.469 -63.336 89.129 C-63.553 87.427 -63.553 87.427 -63.773 85.691 C-63.959 84.516 -64.145 83.34 -64.336 82.129 C-65.326 81.469 -66.316 80.809 -67.336 80.129 C-66.346 74.189 -66.346 74.189 -65.336 68.129 C-65.996 68.129 -66.656 68.129 -67.336 68.129 C-67.033 67.46 -66.73 66.791 -66.418 66.102 C-65.157 62.637 -64.599 59.188 -64.023 55.566 C-62.071 44.416 -57.936 35.311 -51.336 26.129 C-50.581 24.986 -49.831 23.841 -49.086 22.691 C-42.641 13.255 -30.003 6.497 -19.336 3.129 C-18.346 3.129 -17.356 3.129 -16.336 3.129 C-16.006 2.469 -15.676 1.809 -15.336 1.129 C-10.271 0.33 -5.116 0.282 0 0 Z M-18.336 31.129 C-18.336 31.789 -18.336 32.449 -18.336 33.129 C-19.171 33.222 -19.171 33.222 -20.023 33.316 C-23.327 34.477 -25.055 36.531 -27.336 39.129 C-27.336 39.789 -27.336 40.449 -27.336 41.129 C-27.996 41.129 -28.656 41.129 -29.336 41.129 C-37.681 55.222 -38.713 69.405 -35.336 85.129 C-33.563 89.909 -31.151 93.906 -28.336 98.129 C-27.676 98.129 -27.016 98.129 -26.336 98.129 C-26.13 98.706 -25.923 99.284 -25.711 99.879 C-22.192 105.637 -14.544 108.283 -8.336 110.129 C-4.671 110.69 -1.039 110.988 2.664 111.129 C2.664 110.469 2.664 109.809 2.664 109.129 C3.324 109.129 3.984 109.129 4.664 109.129 C4.664 109.789 4.664 110.449 4.664 111.129 C11.504 110.613 17.303 108.643 23.664 106.129 C22.674 105.799 21.684 105.469 20.664 105.129 C21.242 105.005 21.819 104.881 22.414 104.754 C24.738 104.108 26.614 103.402 28.664 102.129 C29.159 100.644 29.159 100.644 29.664 99.129 C30.997 98.461 32.33 97.795 33.664 97.129 C40.835 88.994 43.177 79.735 42.664 69.129 C42.004 69.129 41.344 69.129 40.664 69.129 C40.664 68.469 40.664 67.809 40.664 67.129 C41.324 67.129 41.984 67.129 42.664 67.129 C42.664 65.479 42.664 63.829 42.664 62.129 C41.179 61.634 41.179 61.634 39.664 61.129 C40.654 60.139 40.654 60.139 41.664 59.129 C41.338 50.046 35.756 41.516 29.828 34.988 C19.568 26.172 7.845 25.288 -5.015 25.876 C-9.972 26.416 -14.164 28.504 -18.336 31.129 Z " fill="#FCFCFE" transform="translate(579.3359375,362.87109375)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 0.66 19 1.32 19 2 C20.32 1.67 21.64 1.34 23 1 C23.104 1.726 23.209 2.451 23.316 3.199 C24.063 6.26 25.175 8.215 26.938 10.812 C27.462 11.603 27.987 12.393 28.527 13.207 C29.013 13.799 29.499 14.39 30 15 C30.66 15 31.32 15 32 15 C32.495 18.465 32.495 18.465 33 22 C33.99 21.67 34.98 21.34 36 21 C36.268 21.969 36.536 22.939 36.812 23.938 C37.4 25.453 37.4 25.453 38 27 C38.99 27.33 39.98 27.66 41 28 C41.103 28.897 41.206 29.793 41.312 30.717 C42.031 34.148 42.922 35.772 45.016 38.531 C47.909 42.474 50.657 46.477 53.297 50.594 C54.035 51.737 54.773 52.88 55.512 54.023 C56.642 55.777 57.771 57.532 58.895 59.29 C60.003 61.02 61.118 62.745 62.234 64.469 C63.212 65.991 63.212 65.991 64.21 67.545 C65.933 70.205 65.933 70.205 69 72 C69 72.99 69 73.98 69 75 C70.084 77.097 70.084 77.097 71.5 79.188 C71.964 79.903 72.428 80.618 72.906 81.355 C73.267 81.898 73.628 82.441 74 83 C74 81.02 74 79.04 74 77 C73.01 77 72.02 77 71 77 C71.66 76.67 72.32 76.34 73 76 C74.336 72.47 74.12 68.85 74.114 65.12 C74.114 64.437 74.114 63.753 74.114 63.049 C74.113 60.799 74.106 58.548 74.098 56.297 C74.096 54.733 74.094 53.169 74.093 51.606 C74.09 47.496 74.08 43.386 74.069 39.276 C74.058 35.08 74.054 30.884 74.049 26.688 C74.038 18.458 74.021 10.229 74 2 C78.597 0.59 81.339 -0.119 86 1 C88.274 0.917 90.546 0.769 92.812 0.562 C93.974 0.461 95.135 0.359 96.332 0.254 C97.212 0.17 98.093 0.086 99 0 C100.595 3.53 101.25 6.275 101.247 10.141 C101.251 11.154 101.256 12.168 101.26 13.212 C101.254 14.313 101.249 15.414 101.243 16.548 C101.245 17.716 101.247 18.884 101.249 20.088 C101.252 23.95 101.242 27.813 101.23 31.676 C101.228 34.362 101.229 37.049 101.229 39.735 C101.228 45.368 101.219 51.001 101.206 56.635 C101.19 63.132 101.185 69.63 101.186 76.128 C101.186 83.062 101.179 89.996 101.17 96.929 C101.168 98.919 101.167 100.909 101.167 102.898 C101.165 106.62 101.156 110.342 101.145 114.064 C101.146 115.159 101.146 116.253 101.147 117.381 C101.142 118.403 101.138 119.425 101.134 120.478 C101.132 121.356 101.13 122.235 101.129 123.14 C100.965 126.776 100.451 130.389 100 134 C94.06 134 88.12 134 82 134 C81.67 134.66 81.34 135.32 81 136 C81 135.34 81 134.68 81 134 C79.886 134.155 79.886 134.155 78.75 134.312 C76 134 76 134 74.035 131.836 C72.053 129.074 70.373 126.262 68.688 123.312 C65.826 118.619 65.826 118.619 61.062 116.312 C60.052 116.209 59.041 116.106 58 116 C58.99 115.67 59.98 115.34 61 115 C60.375 111.948 59.369 109.673 57.742 107.023 C57.056 105.9 57.056 105.9 56.355 104.754 C55.623 103.576 55.623 103.576 54.875 102.375 C54.379 101.573 53.882 100.77 53.371 99.944 C47.722 89.175 47.722 89.175 39 82 C38.918 81.299 38.835 80.597 38.75 79.875 C37.832 76.354 36.052 73.978 34 71 C34 70.34 34 69.68 34 69 C31.925 67.776 31.925 67.776 29 68 C28.34 67.34 27.68 66.68 27 66 C27.008 66.905 27.008 66.905 27.016 67.828 C27.177 89.899 26.602 111.939 26 134 C17.09 134 8.18 134 -1 134 C-1.023 118.362 -1.041 102.723 -1.052 87.085 C-1.057 79.824 -1.064 72.562 -1.075 65.3 C-1.085 58.969 -1.092 52.637 -1.094 46.306 C-1.095 42.955 -1.099 39.605 -1.106 36.255 C-1.113 32.508 -1.114 28.762 -1.114 25.016 C-1.119 23.362 -1.119 23.362 -1.124 21.674 C-1.117 16.018 -0.849 10.594 0 5 C-0.66 4.67 -1.32 4.34 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z M27 60 C27 61.32 27 62.64 27 64 C27.66 63.67 28.32 63.34 29 63 C28.67 62.01 28.34 61.02 28 60 C27.67 60 27.34 60 27 60 Z " fill="#FDFDFE" transform="translate(670,363)"/>
<path d="M0 0 C15.551 11.782 26.024 26.296 29.375 46.008 C31.736 65.029 27.918 83.93 16.062 99.32 C15.127 100.538 15.127 100.538 14.172 101.781 C3.791 114.276 -10.888 121.91 -26.938 124.32 C-29.374 124.387 -31.813 124.406 -34.25 124.383 C-35.504 124.374 -36.759 124.365 -38.051 124.355 C-39.48 124.338 -39.48 124.338 -40.938 124.32 C-41.267 124.98 -41.597 125.64 -41.938 126.32 C-41.938 125.66 -41.938 125 -41.938 124.32 C-42.866 124.382 -43.794 124.444 -44.75 124.508 C-50.021 124.54 -54.937 122.839 -59.938 121.32 C-60.267 121.98 -60.597 122.64 -60.938 123.32 C-61.639 122.722 -62.34 122.124 -63.062 121.508 C-67.411 118.199 -71.512 116.118 -76.938 115.32 C-77.267 114.33 -77.598 113.34 -77.938 112.32 C-79.461 110.913 -79.461 110.913 -81.312 109.695 C-81.918 109.27 -82.524 108.845 -83.148 108.406 C-85.057 107.157 -85.057 107.157 -87.938 106.32 C-88.267 104.67 -88.598 103.02 -88.938 101.32 C-90.423 100.825 -90.423 100.825 -91.938 100.32 C-93.619 97.553 -95.002 94.69 -96.438 91.789 C-97.754 89.022 -97.754 89.022 -100.938 88.32 C-100.938 85.35 -100.938 82.38 -100.938 79.32 C-101.598 78.99 -102.257 78.66 -102.938 78.32 C-103.84 74.402 -104.166 70.374 -104.625 66.383 C-104.767 65.279 -104.91 64.175 -105.057 63.037 C-106.236 53.1 -105.949 44.636 -102.188 35.32 C-101.92 34.637 -101.653 33.953 -101.377 33.249 C-95.402 18.219 -95.402 18.219 -89.938 13.32 C-89.497 12.369 -89.497 12.369 -89.047 11.398 C-87.046 7.65 -83.362 5.702 -79.938 3.32 C-78.86 2.539 -78.86 2.539 -77.762 1.742 C-72.431 -2.007 -67.132 -4.657 -60.938 -6.68 C-60.018 -6.98 -59.099 -7.28 -58.152 -7.59 C-39.147 -12.862 -16.529 -11.198 0 0 Z M-44.938 15.32 C-41.502 17.61 -39.952 17.499 -35.938 17.32 C-38.323 17.32 -40.678 17.913 -43.027 18.297 C-43.658 18.305 -44.288 18.312 -44.938 18.32 C-45.597 17.66 -46.257 17 -46.938 16.32 C-54.061 16.169 -59.493 20.027 -64.938 24.32 C-69.305 28.812 -72.375 33.2 -73.938 39.32 C-74.44 40.508 -74.943 41.695 -75.461 42.918 C-77.212 47.552 -77.399 51.781 -77.312 56.695 C-77.302 57.533 -77.292 58.371 -77.282 59.234 C-77.002 70.4 -73.75 80.155 -65.945 88.434 C-62.522 91.553 -59.434 93.958 -54.938 95.32 C-54.442 95.815 -54.442 95.815 -53.938 96.32 C-49.292 97.231 -45.562 97.336 -40.938 96.32 C-40.938 97.31 -40.938 98.3 -40.938 99.32 C-34.829 99.613 -34.829 99.613 -28.938 98.32 C-28.608 97.66 -28.278 97 -27.938 96.32 C-27.113 96.403 -26.288 96.485 -25.438 96.57 C-16.856 95.957 -11.405 90.532 -5.938 84.32 C2.503 74.238 3.093 60.831 2.062 48.32 C1.461 45.045 0.532 42.3 -0.938 39.32 C-1.313 38.53 -1.688 37.74 -2.074 36.926 C-4.376 32.432 -6.502 28.981 -10.938 26.32 C-13.623 25.531 -13.623 25.531 -15.938 25.32 C-14.938 24.32 -14.938 24.32 -11.938 23.32 C-13.656 22.282 -15.388 21.265 -17.125 20.258 C-18.088 19.689 -19.051 19.121 -20.043 18.535 C-23.343 17.15 -24.561 17.509 -27.938 18.32 C-28.928 17.66 -29.918 17 -30.938 16.32 C-30.938 15.99 -30.938 15.66 -30.938 15.32 C-35.558 15.32 -40.178 15.32 -44.938 15.32 Z M-46.938 97.32 C-42.938 98.32 -42.938 98.32 -42.938 98.32 Z " fill="#FCFCFE" transform="translate(384.9375,196.6796875)"/>
<path d="M0 0 C3.524 3.278 6.326 6.988 9.07 10.926 C9.73 11.854 10.39 12.782 11.07 13.738 C12.07 15.926 12.07 15.926 12.008 18.051 C9.746 22.574 4.347 24.455 0.008 26.613 C-0.617 26.934 -1.241 27.254 -1.885 27.584 C-4.762 29.024 -6.668 29.926 -9.93 29.926 C-10.874 28.168 -11.818 26.41 -12.762 24.652 C-13.977 22.621 -13.977 22.621 -16.93 20.926 C-16.93 20.266 -16.93 19.606 -16.93 18.926 C-17.804 18.493 -17.804 18.493 -18.695 18.051 C-20.069 17.359 -21.435 16.654 -22.789 15.926 C-33.019 10.515 -43.962 9.163 -55.355 11.684 C-65.593 14.922 -73.244 20.161 -78.449 29.715 C-83.669 40.877 -85.074 54.368 -81.805 66.426 C-79.553 72.602 -76.472 79.231 -70.93 82.926 C-70.93 83.586 -70.93 84.246 -70.93 84.926 C-70.27 85.173 -69.61 85.421 -68.93 85.676 C-65.845 86.961 -62.938 88.45 -59.992 90.023 C-54.953 92.228 -49.531 92.211 -44.117 92.488 C-43.329 92.531 -42.541 92.575 -41.729 92.619 C-39.796 92.725 -37.863 92.826 -35.93 92.926 C-35.435 91.441 -35.435 91.441 -34.93 89.926 C-34.6 90.586 -34.27 91.246 -33.93 91.926 C-25.752 89.438 -19.473 85.503 -13.93 78.926 C-12.456 75.979 -11.526 73.162 -10.93 69.926 C-12.58 69.266 -14.23 68.606 -15.93 67.926 C-15.93 67.266 -15.93 66.606 -15.93 65.926 C-18.18 65.899 -20.43 65.879 -22.68 65.863 C-24.559 65.846 -24.559 65.846 -26.477 65.828 C-29.215 65.906 -31.348 66.098 -33.93 66.926 C-36.477 62.082 -36.158 57.434 -36.055 52.113 C-36.046 51.23 -36.037 50.346 -36.027 49.436 C-36.004 47.266 -35.968 45.096 -35.93 42.926 C-28.663 42.856 -21.396 42.803 -14.129 42.77 C-10.755 42.755 -7.38 42.733 -4.006 42.699 C-0.129 42.663 3.748 42.646 7.625 42.633 C8.84 42.617 10.054 42.602 11.306 42.586 C12.987 42.586 12.987 42.586 14.703 42.585 C16.188 42.575 16.188 42.575 17.704 42.565 C18.485 42.684 19.266 42.803 20.07 42.926 C20.73 43.916 21.39 44.906 22.07 45.926 C21.41 45.926 20.75 45.926 20.07 45.926 C20.105 46.979 20.14 48.032 20.176 49.117 C20.592 66.952 17.59 85.283 5.07 98.926 C4.41 98.926 3.75 98.926 3.07 98.926 C2.843 99.505 2.617 100.083 2.383 100.68 C-2.704 109.385 -16.034 115.042 -25.449 117.531 C-31.898 119.084 -38.064 119.465 -44.68 119.426 C-46.383 119.416 -46.383 119.416 -48.12 119.406 C-66.476 119.009 -80.797 111.866 -94.418 99.59 C-95.93 97.926 -95.93 97.926 -95.93 95.926 C-96.59 95.926 -97.25 95.926 -97.93 95.926 C-98.59 94.606 -99.25 93.286 -99.93 91.926 C-100.92 91.926 -101.91 91.926 -102.93 91.926 C-102.93 90.936 -102.93 89.946 -102.93 88.926 C-103.556 87.243 -104.217 85.573 -104.93 83.926 C-106.91 84.421 -106.91 84.421 -108.93 84.926 C-109.555 83.176 -109.555 83.176 -109.93 80.926 C-109.582 80.313 -109.234 79.701 -108.875 79.07 C-107.634 76.254 -108.167 75.232 -109.18 72.363 C-111.715 64.091 -112.174 55.505 -112.93 46.926 C-112.27 46.926 -111.61 46.926 -110.93 46.926 C-110.93 43.956 -110.93 40.986 -110.93 37.926 C-111.26 37.926 -111.59 37.926 -111.93 37.926 C-112.215 30.497 -112.215 30.497 -109.93 26.926 C-109.6 27.916 -109.27 28.906 -108.93 29.926 C-108.147 28.294 -108.147 28.294 -107.348 26.629 C-98.36 8.46 -86.116 -4.896 -66.617 -11.887 C-43.452 -19.347 -18.903 -15.278 0 0 Z M-109.93 31.926 C-108.93 33.926 -108.93 33.926 -108.93 33.926 Z M-9.93 65.926 C-8.93 68.926 -8.93 68.926 -8.93 68.926 Z " fill="#FCFCFE" transform="translate(863.9296875,202.07421875)"/>
<path d="M0 0 C0.775 -0.006 1.549 -0.011 2.348 -0.017 C6.161 0.033 8.007 0.151 11.25 2.312 C13.113 2.953 14.99 3.553 16.875 4.125 C23.595 6.303 29.26 9.404 35.188 13.188 C35.868 13.559 36.549 13.93 37.25 14.312 C37.91 13.983 38.57 13.652 39.25 13.312 C39.706 19.846 37.46 22.95 33.375 27.75 C32.333 28.938 31.292 30.125 30.25 31.312 C28.573 33.304 26.912 35.308 25.25 37.312 C21.891 36.694 19.803 35.444 17.062 33.438 C8.05 27.307 -2.352 25.108 -13.125 27.125 C-16.469 27.908 -19.628 28.856 -22.75 30.312 C-22.75 30.973 -22.75 31.632 -22.75 32.312 C-23.41 32.312 -24.07 32.312 -24.75 32.312 C-25.685 39.088 -25.685 39.088 -23.367 45.191 C-20.381 47.46 -18.606 48.367 -14.812 48.125 C-14.132 47.857 -13.451 47.589 -12.75 47.312 C-12.42 48.303 -12.09 49.293 -11.75 50.312 C-9.096 51.468 -9.096 51.468 -5.801 52.328 C-4.566 52.689 -3.331 53.049 -2.059 53.421 C-1.405 53.607 -0.751 53.793 -0.078 53.985 C32.827 63.41 32.827 63.41 41.926 77.715 C47.15 87.963 46.835 97.677 44.199 108.543 C43.237 111.35 42.095 113.022 40.25 115.312 C39.92 115.996 39.59 116.679 39.25 117.383 C34.983 125.617 22.519 131.359 14.25 134.312 C6.432 136.363 -1.327 136.722 -9.375 136.938 C-10.57 136.976 -11.765 137.015 -12.996 137.055 C-15.914 137.148 -18.832 137.234 -21.75 137.312 C-21.75 136.653 -21.75 135.993 -21.75 135.312 C-23.663 134.78 -25.581 134.262 -27.5 133.75 C-28.567 133.46 -29.635 133.17 -30.734 132.871 C-33.641 132.333 -35.018 132.384 -37.75 133.312 C-38.41 133.312 -39.07 133.312 -39.75 133.312 C-40.75 130.312 -40.75 130.312 -40.75 128.312 C-43.06 126.993 -45.37 125.673 -47.75 124.312 C-47.75 123.653 -47.75 122.993 -47.75 122.312 C-48.585 121.941 -48.585 121.941 -49.438 121.562 C-52.15 120.096 -54.386 118.283 -56.75 116.312 C-56.437 113.391 -55.93 112.461 -53.625 110.562 C-52.676 109.82 -51.727 109.077 -50.75 108.312 C-47.165 104.507 -43.85 100.521 -40.75 96.312 C-40.75 96.972 -40.75 97.632 -40.75 98.312 C-40.171 98.588 -39.592 98.864 -38.996 99.148 C-36.811 100.281 -34.848 101.559 -32.812 102.938 C-29.661 104.947 -27.578 105.911 -23.75 105.312 C-24.245 106.798 -24.245 106.798 -24.75 108.312 C-12.603 110.889 2.148 113.226 13.25 106.312 C16.841 102.984 18.046 100.203 18.25 95.312 C17.17 90.777 15.119 88.811 11.25 86.312 C6.324 83.884 1.676 82.186 -3.812 81.688 C-5.112 81.564 -6.411 81.44 -7.75 81.312 C-8.08 80.653 -8.41 79.993 -8.75 79.312 C-10.526 78.566 -10.526 78.566 -12.68 78.023 C-13.475 77.804 -14.27 77.585 -15.09 77.359 C-15.926 77.138 -16.763 76.916 -17.625 76.688 C-18.454 76.458 -19.283 76.229 -20.137 75.992 C-24.874 74.701 -24.874 74.701 -29.75 74.312 C-30.774 73.337 -31.77 72.332 -32.75 71.312 C-34.351 70.295 -35.979 69.318 -37.625 68.375 C-47.753 62.396 -47.753 62.396 -49.75 57.312 C-50.41 57.312 -51.07 57.312 -51.75 57.312 C-52.75 54.312 -52.75 54.312 -52.75 52.312 C-53.41 51.322 -54.07 50.332 -54.75 49.312 C-54.09 49.312 -53.43 49.312 -52.75 49.312 C-53.245 47.827 -53.245 47.827 -53.75 46.312 C-54.514 38.079 -53.52 31.653 -49.75 24.312 C-49.411 23.614 -49.072 22.915 -48.723 22.195 C-43.784 13.162 -34.254 6.812 -24.75 3.312 C-16.511 0.917 -8.536 0.052 0 0 Z " fill="#FCFCFE" transform="translate(303.75,362.6875)"/>
<path d="M0 0 C2.646 -0.312 2.646 -0.312 5 -1 C5.495 0.485 5.495 0.485 6 2 C8.24 3.264 10.419 4.373 12.75 5.438 C17.285 7.531 21.139 9.803 25 13 C26.048 13.516 26.048 13.516 27.117 14.043 C29.325 15.165 30.529 16.429 32.125 18.312 C32.623 18.886 33.12 19.46 33.633 20.051 C35.184 22.263 36.084 24.466 37 27 C27.444 34.749 27.444 34.749 22.938 37.875 C19.968 39.678 19.968 39.678 19 42 C17.35 41.67 15.7 41.34 14 41 C13.505 41.99 13.505 41.99 13 43 C13.076 42.242 13.152 41.484 13.23 40.703 C13.267 37.945 13.267 37.945 11.426 36.234 C6.199 32.459 6.199 32.459 0 31 C0.495 30.01 0.495 30.01 1 29 C-0.727 28.35 -2.457 27.705 -4.188 27.062 C-5.15 26.703 -6.113 26.343 -7.105 25.973 C-12.197 24.262 -16.628 23.895 -22 24 C-20.515 24.495 -20.515 24.495 -19 25 C-19.33 25.66 -19.66 26.32 -20 27 C-21.65 27 -23.3 27 -25 27 C-25 26.34 -25 25.68 -25 25 C-31.658 23.071 -37.084 25.941 -43 29 C-52.98 36.078 -58.137 44.239 -61 56 C-62.035 68.154 -61.539 81.559 -53.465 91.449 C-47.864 97.379 -42.197 102.26 -34 104 C-31.28 103.743 -29.54 103.104 -27 102 C-27.33 102.66 -27.66 103.32 -28 104 C-28.66 104 -29.32 104 -30 104 C-30 104.66 -30 105.32 -30 106 C-22.039 106.144 -13.876 106.277 -6 105 C-5.67 104.34 -5.34 103.68 -5 103 C-4.34 103.33 -3.68 103.66 -3 104 C-2.67 103.34 -2.34 102.68 -2 102 C-1.154 101.752 -0.309 101.505 0.562 101.25 C7.291 98.803 13.042 93.936 17 88 C20.243 88.559 21.931 89.413 24 92 C24 92.66 24 93.32 24 94 C24.572 94.262 25.145 94.523 25.734 94.793 C28.166 96.089 30.136 97.547 32.25 99.312 C32.956 99.886 33.663 100.46 34.391 101.051 C36 103 36 103 36.547 105.418 C35.76 109.132 33.821 110.528 31 113 C30.207 113.73 29.414 114.459 28.598 115.211 C12.633 128.839 -5.634 134.19 -26.414 132.75 C-35.34 131.936 -43.508 129.906 -52 127 C-52.757 126.836 -53.513 126.673 -54.293 126.504 C-55.138 126.254 -55.138 126.254 -56 126 C-56.33 125.34 -56.66 124.68 -57 124 C-59.276 122.429 -61.617 120.99 -63.969 119.535 C-66 118 -66 118 -67 115 C-67.66 115 -68.32 115 -69 115 C-69.495 114.01 -69.495 114.01 -70 113 C-71.65 113 -73.3 113 -75 113 C-74.654 112.141 -74.654 112.141 -74.301 111.266 C-73.745 108.919 -73.745 108.919 -75.293 106.922 C-75.918 106.205 -76.543 105.488 -77.188 104.75 C-80.565 100.68 -82.743 96.765 -85 92 C-85.66 91.34 -86.32 90.68 -87 90 C-88 89 -88 89 -89 86 C-88.34 86 -87.68 86 -87 86 C-87.311 84.25 -87.624 82.5 -87.938 80.75 C-88.112 79.775 -88.286 78.801 -88.465 77.797 C-88.928 75.13 -88.928 75.13 -89.562 73.094 C-90.215 69.973 -90.092 66.932 -90.062 63.75 C-90.053 62.487 -90.044 61.223 -90.035 59.922 C-90.024 58.958 -90.012 57.993 -90 57 C-89.34 57 -88.68 57 -88 57 C-88.083 56.113 -88.165 55.226 -88.25 54.312 C-88.133 39.775 -78.964 26.392 -69.348 16.215 C-65.535 12.618 -61.473 9.717 -57 7 C-55.882 6.308 -55.882 6.308 -54.742 5.602 C-41.868 -1.599 -12.841 -8.561 0 0 Z " fill="#FCFCFE" transform="translate(235,189)"/>
<path d="M0 0 C8.91 0 17.82 0 27 0 C27.198 16.637 27.384 33.274 27.547 49.912 C27.623 57.637 27.704 65.362 27.799 73.086 C27.882 79.818 27.954 86.55 28.013 93.282 C28.045 96.847 28.082 100.412 28.132 103.978 C28.187 107.955 28.219 111.932 28.249 115.909 C28.269 117.096 28.289 118.282 28.31 119.504 C28.314 120.585 28.318 121.666 28.323 122.78 C28.338 124.192 28.338 124.192 28.354 125.632 C28 128 28 128 26.645 129.805 C24.193 131.586 22.235 131.339 19.238 131.293 C17.561 131.278 17.561 131.278 15.85 131.264 C14.682 131.239 13.515 131.213 12.312 131.188 C11.134 131.174 9.955 131.16 8.74 131.146 C5.826 131.111 2.913 131.062 0 131 C-1.045 99.963 -0.987 68.984 -0.562 37.938 C-0.543 36.53 -0.543 36.53 -0.524 35.093 C-0.363 23.395 -0.187 11.698 0 0 Z " fill="#FDFDFE" transform="translate(583,189)"/>
<path d="M0 0 C3.545 -0.217 7.077 -0.375 10.625 -0.5 C12.124 -0.601 12.124 -0.601 13.652 -0.703 C20.933 -0.896 20.933 -0.896 23.922 1.614 C25.484 3.671 26.761 5.737 28 8 C28.652 9.005 29.305 10.011 29.977 11.047 C31.082 12.859 32.186 14.671 33.289 16.484 C34.902 19.038 34.902 19.038 37.035 20.977 C39.34 23.351 40.438 25.622 41.812 28.625 C44.183 33.525 46.822 37.847 50.016 42.242 C52.424 45.589 54.716 49.005 57 52.438 C57.427 53.078 57.854 53.719 58.294 54.38 C61.418 59.082 64.455 63.834 67.43 68.633 C69.145 71.218 71.03 73.604 73 76 C74.918 74.082 74.129 71.239 74.139 68.687 C74.137 67.802 74.135 66.916 74.133 66.004 C74.134 64.648 74.134 64.648 74.136 63.265 C74.136 61.349 74.135 59.433 74.13 57.517 C74.125 54.579 74.13 51.64 74.137 48.701 C74.136 46.844 74.135 44.986 74.133 43.129 C74.136 41.804 74.136 41.804 74.139 40.452 C74.258 36.089 74.258 36.089 73 32 C73.66 32 74.32 32 75 32 C74.959 31.279 74.918 30.559 74.876 29.816 C74.697 26.524 74.536 23.231 74.375 19.938 C74.311 18.804 74.246 17.67 74.18 16.502 C74.128 15.397 74.077 14.291 74.023 13.152 C73.971 12.142 73.919 11.131 73.865 10.09 C74 7 74 7 76 0 C84.58 0 93.16 0 102 0 C102 42.9 102 85.8 102 130 C98.582 131.139 95.965 131.113 92.363 131.098 C91.065 131.094 89.766 131.091 88.428 131.088 C87.056 131.08 85.684 131.071 84.312 131.062 C82.929 131.057 81.546 131.053 80.162 131.049 C76.775 131.037 73.387 131.021 70 131 C70.165 130.216 70.33 129.433 70.5 128.625 C71.062 126.118 71.062 126.118 71 124 C71 123.34 71 122.68 71 122 C69.68 122.33 68.36 122.66 67 123 C67.495 121.02 67.495 121.02 68 119 C66.68 118.01 65.36 117.02 64 116 C64.66 116 65.32 116 66 116 C66 115.34 66 114.68 66 114 C65.34 114 64.68 114 64 114 C61 109.615 61 109.615 61 107 C60.01 106.67 59.02 106.34 58 106 C56 103 56 103 55.184 100.797 C53.569 96.98 51.358 93.689 49.062 90.25 C48.62 89.566 48.177 88.881 47.721 88.176 C47.288 87.525 46.856 86.874 46.41 86.203 C46.02 85.612 45.629 85.02 45.227 84.411 C43.95 82.701 43.95 82.701 41 82 C40.353 79.923 39.711 77.844 39.094 75.758 C37.3 71.234 34.446 67.457 31.645 63.504 C30 61 30 61 30 59 C28.68 59 27.36 59 26 59 C26.107 67.44 26.22 75.881 26.339 84.321 C26.395 88.24 26.448 92.159 26.497 96.078 C26.545 99.858 26.597 103.638 26.653 107.418 C26.673 108.863 26.692 110.307 26.709 111.751 C26.733 113.769 26.764 115.788 26.795 117.806 C26.818 119.532 26.818 119.532 26.842 121.292 C26.82 123.968 26.82 123.968 28 126 C27.562 128.062 27.562 128.062 26 130 C19.326 131.607 12.766 130.733 6 130 C5.505 130.99 5.505 130.99 5 132 C4.402 131.835 3.804 131.67 3.188 131.5 C0.887 130.889 0.887 130.889 -2 131 C-2 127.37 -2 123.74 -2 120 C-1.67 120 -1.34 120 -1 120 C-1 94.26 -1 68.52 -1 42 C-1.33 42 -1.66 42 -2 42 C-2 34.41 -2 26.82 -2 19 C-1.67 19 -1.34 19 -1 19 C-0.67 12.73 -0.34 6.46 0 0 Z M2 2 C2 43.58 2 85.16 2 128 C9.26 128 16.52 128 24 128 C24.33 101.93 24.66 75.86 25 49 C27.64 53.62 30.28 58.24 33 63 C34.925 65.935 36.852 68.87 38.824 71.773 C41.311 75.462 43.72 79.196 46.125 82.938 C49.208 87.722 52.315 92.489 55.465 97.23 C56.055 98.119 56.645 99.008 57.253 99.923 C58.457 101.722 59.673 103.512 60.899 105.295 C64.079 109.943 66.9 114.457 69.113 119.645 C70.795 122.707 70.795 122.707 76 128 C83.92 128 91.84 128 100 128 C100 86.42 100 44.84 100 2 C92.41 2 84.82 2 77 2 C76.67 29.39 76.34 56.78 76 85 C71.05 77.08 71.05 77.08 66 69 C63.039 64.506 60.067 60.026 57.062 55.562 C55.485 53.209 53.91 50.855 52.336 48.5 C51.956 47.932 51.576 47.364 51.185 46.779 C46.848 40.288 42.616 33.734 38.396 27.167 C35.59 22.811 32.762 18.469 29.938 14.125 C29.096 12.83 29.096 12.83 28.237 11.508 C26.171 8.33 24.103 5.154 22 2 C15.4 2 8.8 2 2 2 Z M-1 27 C-1 28.98 -1 30.96 -1 33 C-0.67 33 -0.34 33 0 33 C0 31.02 0 29.04 0 27 C-0.33 27 -0.66 27 -1 27 Z M74 77 C74.66 78.32 75.32 79.64 76 81 C75.67 79.68 75.34 78.36 75 77 C74.67 77 74.34 77 74 77 Z " fill="#969DDB" transform="translate(634,189)"/>
<path d="M0 0 C3 0 3 0 6 2 C6.961 4.883 7.117 6.677 7.098 9.668 C7.094 10.561 7.091 11.453 7.088 12.373 C7.075 13.766 7.075 13.766 7.062 15.188 C7.058 16.128 7.053 17.068 7.049 18.037 C7.037 20.358 7.021 22.679 7 25 C7.66 25.33 8.32 25.66 9 26 C8.505 26.495 8.505 26.495 8 27 C8.495 27.99 8.495 27.99 9 29 C8.34 29 7.68 29 7 29 C7.495 30.485 7.495 30.485 8 32 C7.676 33.336 7.343 34.669 7 36 C7.33 36.66 7.66 37.32 8 38 C8.07 39.707 8.084 41.417 8.062 43.125 C8.053 44.035 8.044 44.945 8.035 45.883 C8.024 46.581 8.012 47.28 8 48 C8.33 48 8.66 48 9 48 C9.66 50.64 10.32 53.28 11 56 C9.68 55.67 8.36 55.34 7 55 C5.391 55.247 5.391 55.247 3.75 55.5 C0.65 55.913 -1.308 55.936 -4.312 55.5 C-7.815 54.673 -7.815 54.673 -10 56 C-15.351 56.282 -19.926 55.74 -25.117 54.438 C-27.003 53.925 -27.003 53.925 -29 54 C-29.268 52.989 -29.536 51.979 -29.812 50.938 C-30.894 47.127 -32.379 43.611 -34 40 C-35.65 40 -37.3 40 -39 40 C-38.258 39.412 -38.258 39.412 -37.5 38.812 C-35.544 36.449 -35.758 34.986 -36 32 C-35.34 32 -34.68 32 -34 32 C-33.709 31.336 -33.417 30.672 -33.117 29.988 C-30.03 23.337 -27.413 19.241 -20.738 15.852 C-17.334 14.793 -14.724 15.018 -11.188 15.375 C-10.026 15.486 -8.865 15.597 -7.668 15.711 C-6.788 15.806 -5.907 15.902 -5 16 C-5.179 15.251 -5.358 14.502 -5.543 13.73 C-6.17 9.982 -5.389 7.505 -4 4 C-1.875 2.562 -1.875 2.562 0 2 C0 1.34 0 0.68 0 0 Z M-5 16 C-5.33 16.66 -5.66 17.32 -6 18 C-5.34 18 -4.68 18 -4 18 C-4.33 17.34 -4.66 16.68 -5 16 Z M-22 29 C-23.182 33.087 -23.3 36.746 -22.312 40.875 C-20.803 43.318 -19.523 44.624 -17 46 C-16.01 45.67 -15.02 45.34 -14 45 C-13.67 45.33 -13.34 45.66 -13 46 C-12.34 46.33 -11.68 46.66 -11 47 C-5.88 43.962 -5.88 43.962 -3 39 C-2.97 36.627 -2.97 36.627 -3.375 34.25 C-3.486 33.451 -3.597 32.652 -3.711 31.828 C-3.806 31.225 -3.902 30.622 -4 30 C-4.66 30 -5.32 30 -6 30 C-6 29.01 -6 28.02 -6 27 C-12.558 24.56 -16.983 23.286 -22 29 Z M-28 50 C-27 52 -27 52 -27 52 Z " fill="#F9F8FD" transform="translate(693,616)"/>
<path d="M0 0 C0 1.65 0 3.3 0 5 C-0.66 5 -1.32 5 -2 5 C-2.66 6.65 -3.32 8.3 -4 10 C-5.093 9.67 -6.186 9.34 -7.312 9 C-10.64 8.098 -12.696 7.633 -16 8.562 C-18 9 -18 9 -21 8 C-23.009 8.276 -25.012 8.602 -27 9 C-27 8.34 -27 7.68 -27 7 C-28.247 8.051 -28.247 8.051 -28.375 10.375 C-28.251 11.241 -28.127 12.107 -28 13 C-25.591 14.862 -25.591 14.862 -23 16 C-23.33 15.01 -23.66 14.02 -24 13 C-23.34 12.67 -22.68 12.34 -22 12 C-21.67 12.99 -21.34 13.98 -21 15 C-18.413 16.111 -18.413 16.111 -15.312 16.938 C-9.397 18.792 -5.292 20.402 -1.125 25.125 C0.556 29.421 0.563 33.447 0 38 C-1.789 42.004 -4.111 45.185 -8.199 46.949 C-18.12 49.893 -26.864 49.677 -36.438 45.5 C-39 44 -39 44 -41 42 C-40.01 39.03 -39.02 36.06 -38 33 C-37.361 33.268 -36.721 33.536 -36.062 33.812 C-35.052 34.204 -34.041 34.596 -33 35 C-32.322 35.264 -31.644 35.529 -30.945 35.801 C-25.73 37.705 -21.554 38.479 -16 38 C-15.01 37.34 -14.02 36.68 -13 36 C-11.515 35.505 -11.515 35.505 -10 35 C-11.522 31.956 -12.408 30.296 -15.5 28.75 C-16.325 28.503 -17.15 28.255 -18 28 C-19.485 27.505 -19.485 27.505 -21 27 C-21.33 27.66 -21.66 28.32 -22 29 C-22.33 28.01 -22.66 27.02 -23 26 C-24.299 25.814 -24.299 25.814 -25.625 25.625 C-27.296 25.316 -27.296 25.316 -29 25 C-30.011 24.814 -31.021 24.629 -32.062 24.438 C-35.852 22.583 -36.975 20.326 -38.922 16.68 C-39.278 16.125 -39.633 15.571 -40 15 C-40.66 15 -41.32 15 -42 15 C-42.125 12.625 -42.125 12.625 -42 10 C-41.34 9.34 -40.68 8.68 -40 8 C-39.415 6.718 -38.855 5.425 -38.312 4.125 C-36.063 -0.517 -33.667 -1.917 -29 -4 C-18.653 -7.104 -9.601 -3.924 0 0 Z " fill="#F7F7FD" transform="translate(358,623)"/>
<path d="M0 0 C3.524 3.278 6.326 6.988 9.07 10.926 C9.73 11.854 10.39 12.782 11.07 13.738 C12.07 15.926 12.07 15.926 12.008 18.051 C9.746 22.574 4.347 24.455 0.008 26.613 C-0.617 26.934 -1.241 27.254 -1.885 27.584 C-4.762 29.024 -6.668 29.926 -9.93 29.926 C-10.874 28.168 -11.818 26.41 -12.762 24.652 C-13.977 22.621 -13.977 22.621 -16.93 20.926 C-16.93 20.266 -16.93 19.606 -16.93 18.926 C-17.804 18.493 -17.804 18.493 -18.695 18.051 C-20.069 17.359 -21.435 16.654 -22.789 15.926 C-33.019 10.515 -43.962 9.163 -55.355 11.684 C-65.593 14.922 -73.244 20.161 -78.449 29.715 C-83.669 40.877 -85.074 54.368 -81.805 66.426 C-79.553 72.602 -76.472 79.231 -70.93 82.926 C-70.93 83.586 -70.93 84.246 -70.93 84.926 C-70.27 85.173 -69.61 85.421 -68.93 85.676 C-65.845 86.961 -62.938 88.45 -59.992 90.023 C-54.953 92.228 -49.531 92.211 -44.117 92.488 C-43.329 92.531 -42.541 92.575 -41.729 92.619 C-39.796 92.725 -37.863 92.826 -35.93 92.926 C-35.435 91.441 -35.435 91.441 -34.93 89.926 C-34.6 90.586 -34.27 91.246 -33.93 91.926 C-25.752 89.438 -19.473 85.503 -13.93 78.926 C-12.456 75.979 -11.526 73.162 -10.93 69.926 C-12.58 69.266 -14.23 68.606 -15.93 67.926 C-15.93 67.266 -15.93 66.606 -15.93 65.926 C-18.18 65.899 -20.43 65.879 -22.68 65.863 C-24.559 65.846 -24.559 65.846 -26.477 65.828 C-29.215 65.906 -31.348 66.098 -33.93 66.926 C-36.477 62.082 -36.158 57.434 -36.055 52.113 C-36.046 51.23 -36.037 50.346 -36.027 49.436 C-36.004 47.266 -35.968 45.096 -35.93 42.926 C-28.663 42.856 -21.396 42.803 -14.129 42.77 C-10.755 42.755 -7.38 42.733 -4.006 42.699 C-0.129 42.663 3.748 42.646 7.625 42.633 C8.84 42.617 10.054 42.602 11.306 42.586 C12.987 42.586 12.987 42.586 14.703 42.585 C16.188 42.575 16.188 42.575 17.704 42.565 C18.485 42.684 19.266 42.803 20.07 42.926 C20.73 43.916 21.39 44.906 22.07 45.926 C21.41 45.926 20.75 45.926 20.07 45.926 C20.105 46.979 20.14 48.032 20.176 49.117 C20.592 66.952 17.59 85.283 5.07 98.926 C4.41 98.926 3.75 98.926 3.07 98.926 C2.843 99.505 2.617 100.083 2.383 100.68 C-2.704 109.385 -16.034 115.042 -25.449 117.531 C-31.898 119.084 -38.064 119.465 -44.68 119.426 C-46.383 119.416 -46.383 119.416 -48.12 119.406 C-66.476 119.009 -80.797 111.866 -94.418 99.59 C-95.93 97.926 -95.93 97.926 -95.93 95.926 C-96.59 95.926 -97.25 95.926 -97.93 95.926 C-98.59 94.606 -99.25 93.286 -99.93 91.926 C-100.92 91.926 -101.91 91.926 -102.93 91.926 C-102.93 90.936 -102.93 89.946 -102.93 88.926 C-103.556 87.243 -104.217 85.573 -104.93 83.926 C-106.91 84.421 -106.91 84.421 -108.93 84.926 C-109.555 83.176 -109.555 83.176 -109.93 80.926 C-109.582 80.313 -109.234 79.701 -108.875 79.07 C-107.634 76.254 -108.167 75.232 -109.18 72.363 C-111.715 64.091 -112.174 55.505 -112.93 46.926 C-112.27 46.926 -111.61 46.926 -110.93 46.926 C-110.93 43.956 -110.93 40.986 -110.93 37.926 C-111.26 37.926 -111.59 37.926 -111.93 37.926 C-112.215 30.497 -112.215 30.497 -109.93 26.926 C-109.6 27.916 -109.27 28.906 -108.93 29.926 C-108.147 28.294 -108.147 28.294 -107.348 26.629 C-98.36 8.46 -86.116 -4.896 -66.617 -11.887 C-43.452 -19.347 -18.903 -15.278 0 0 Z M-93.93 7.926 C-106.592 22.085 -110.957 39.3 -110.082 57.875 C-109.018 72.196 -104.539 85.205 -94.93 95.926 C-94.284 96.676 -93.638 97.426 -92.973 98.199 C-81.128 111.077 -65.001 116.895 -47.836 118.152 C-29.939 118.64 -13.96 113.896 -0.176 102.148 C10.056 92.024 16.052 79.051 18.07 64.926 C18.07 57.996 18.07 51.066 18.07 43.926 C0.91 43.926 -16.25 43.926 -33.93 43.926 C-33.93 50.526 -33.93 57.126 -33.93 63.926 C-25.02 63.926 -16.11 63.926 -6.93 63.926 C-10.263 75.037 -10.263 75.037 -13.746 80.137 C-14.999 81.915 -14.999 81.915 -15.832 84.027 C-17.223 86.433 -18.39 86.865 -20.93 87.926 C-21.711 88.34 -22.492 88.753 -23.297 89.18 C-24.538 89.827 -24.538 89.827 -25.805 90.488 C-27.038 91.14 -27.038 91.14 -28.297 91.805 C-38.568 96.178 -51.701 94.827 -61.98 91.262 C-72.512 86.491 -78.506 78.32 -82.93 67.926 C-87.02 56.159 -85.942 42.107 -80.805 30.863 C-75.043 20.195 -66.724 13.12 -55.055 9.551 C-42.583 7.112 -30.186 9.148 -19.555 16.176 C-15.049 19.57 -11.403 23.498 -7.93 27.926 C-5.302 26.639 -2.678 25.347 -0.055 24.051 C0.692 23.686 1.438 23.321 2.207 22.945 C2.922 22.591 3.638 22.236 4.375 21.871 C5.365 21.384 5.365 21.384 6.375 20.887 C8.251 19.923 8.251 19.923 10.07 17.926 C9.343 10.58 1.588 3.994 -3.781 -0.449 C-31.484 -21.403 -70.162 -16.24 -93.93 7.926 Z M-109.93 31.926 C-108.93 33.926 -108.93 33.926 -108.93 33.926 Z M-9.93 65.926 C-8.93 68.926 -8.93 68.926 -8.93 68.926 Z " fill="#4E59BA" transform="translate(863.9296875,202.07421875)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 0.66 19 1.32 19 2 C20.32 1.67 21.64 1.34 23 1 C23.104 1.726 23.209 2.451 23.316 3.199 C24.063 6.26 25.175 8.215 26.938 10.812 C27.462 11.603 27.987 12.393 28.527 13.207 C29.013 13.799 29.499 14.39 30 15 C30.66 15 31.32 15 32 15 C32.495 18.465 32.495 18.465 33 22 C33.99 21.67 34.98 21.34 36 21 C36.268 21.969 36.536 22.939 36.812 23.938 C37.4 25.453 37.4 25.453 38 27 C38.99 27.33 39.98 27.66 41 28 C41.103 28.897 41.206 29.793 41.312 30.717 C42.031 34.148 42.922 35.772 45.016 38.531 C47.909 42.474 50.657 46.477 53.297 50.594 C54.035 51.737 54.773 52.88 55.512 54.023 C56.642 55.777 57.771 57.532 58.895 59.29 C60.003 61.02 61.118 62.745 62.234 64.469 C63.212 65.991 63.212 65.991 64.21 67.545 C65.933 70.205 65.933 70.205 69 72 C69 72.99 69 73.98 69 75 C70.084 77.097 70.084 77.097 71.5 79.188 C71.964 79.903 72.428 80.618 72.906 81.355 C73.267 81.898 73.628 82.441 74 83 C74 81.02 74 79.04 74 77 C73.01 77 72.02 77 71 77 C71.66 76.67 72.32 76.34 73 76 C74.336 72.47 74.12 68.85 74.114 65.12 C74.114 64.437 74.114 63.753 74.114 63.049 C74.113 60.799 74.106 58.548 74.098 56.297 C74.096 54.733 74.094 53.169 74.093 51.606 C74.09 47.496 74.08 43.386 74.069 39.276 C74.058 35.08 74.054 30.884 74.049 26.688 C74.038 18.458 74.021 10.229 74 2 C78.597 0.59 81.339 -0.119 86 1 C88.274 0.917 90.546 0.769 92.812 0.562 C93.974 0.461 95.135 0.359 96.332 0.254 C97.212 0.17 98.093 0.086 99 0 C100.595 3.53 101.25 6.275 101.247 10.141 C101.251 11.154 101.256 12.168 101.26 13.212 C101.254 14.313 101.249 15.414 101.243 16.548 C101.245 17.716 101.247 18.884 101.249 20.088 C101.252 23.95 101.242 27.813 101.23 31.676 C101.228 34.362 101.229 37.049 101.229 39.735 C101.228 45.368 101.219 51.001 101.206 56.635 C101.19 63.132 101.185 69.63 101.186 76.128 C101.186 83.062 101.179 89.996 101.17 96.929 C101.168 98.919 101.167 100.909 101.167 102.898 C101.165 106.62 101.156 110.342 101.145 114.064 C101.146 115.159 101.146 116.253 101.147 117.381 C101.142 118.403 101.138 119.425 101.134 120.478 C101.132 121.356 101.13 122.235 101.129 123.14 C100.965 126.776 100.451 130.389 100 134 C94.06 134 88.12 134 82 134 C81.67 134.66 81.34 135.32 81 136 C81 135.34 81 134.68 81 134 C79.886 134.155 79.886 134.155 78.75 134.312 C76 134 76 134 74.035 131.836 C72.053 129.074 70.373 126.262 68.688 123.312 C65.826 118.619 65.826 118.619 61.062 116.312 C60.052 116.209 59.041 116.106 58 116 C58.99 115.67 59.98 115.34 61 115 C60.375 111.948 59.369 109.673 57.742 107.023 C57.056 105.9 57.056 105.9 56.355 104.754 C55.623 103.576 55.623 103.576 54.875 102.375 C54.379 101.573 53.882 100.77 53.371 99.944 C47.722 89.175 47.722 89.175 39 82 C38.918 81.299 38.835 80.597 38.75 79.875 C37.832 76.354 36.052 73.978 34 71 C34 70.34 34 69.68 34 69 C31.925 67.776 31.925 67.776 29 68 C28.34 67.34 27.68 66.68 27 66 C27.008 66.905 27.008 66.905 27.016 67.828 C27.177 89.899 26.602 111.939 26 134 C17.09 134 8.18 134 -1 134 C-1.023 118.362 -1.041 102.723 -1.052 87.085 C-1.057 79.824 -1.064 72.562 -1.075 65.3 C-1.085 58.969 -1.092 52.637 -1.094 46.306 C-1.095 42.955 -1.099 39.605 -1.106 36.255 C-1.113 32.508 -1.114 28.762 -1.114 25.016 C-1.119 23.362 -1.119 23.362 -1.124 21.674 C-1.117 16.018 -0.849 10.594 0 5 C-0.66 4.67 -1.32 4.34 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z M1 3 C1 45.57 1 88.14 1 132 C8.59 132 16.18 132 24 132 C24.33 105.93 24.66 79.86 25 53 C28.465 58.94 28.465 58.94 32 65 C34.223 68.42 36.461 71.813 38.75 75.188 C39.983 77.02 41.214 78.854 42.445 80.688 C43.048 81.585 43.65 82.482 44.271 83.406 C46.761 87.142 49.198 90.911 51.625 94.688 C54.421 99.035 57.235 103.367 60.102 107.668 C60.621 108.448 61.141 109.229 61.677 110.033 C62.716 111.58 63.765 113.12 64.825 114.653 C67.075 117.931 68.956 120.895 70.504 124.578 C71.829 128.153 71.829 128.153 76 132 C83.59 132 91.18 132 99 132 C99 89.43 99 46.86 99 3 C91.74 3 84.48 3 77 3 C76.67 31.38 76.34 59.76 76 89 C72.04 82.73 68.08 76.46 64 70 C61.157 65.617 58.309 61.24 55.438 56.875 C53.976 54.647 52.515 52.419 51.055 50.191 C50.361 49.133 49.667 48.075 48.952 46.985 C46.306 42.938 43.682 38.877 41.062 34.812 C38.503 30.844 35.931 26.884 33.32 22.949 C32.883 22.29 32.445 21.63 31.994 20.95 C31.137 19.671 30.272 18.398 29.396 17.132 C27.588 14.502 25.997 11.993 24.711 9.066 C23.537 6.124 23.537 6.124 20 3 C13.73 3 7.46 3 1 3 Z M27 60 C27 61.32 27 62.64 27 64 C27.66 63.67 28.32 63.34 29 63 C28.67 62.01 28.34 61.02 28 60 C27.67 60 27.34 60 27 60 Z " fill="#5868C7" transform="translate(670,363)"/>
<path d="M0 0 C0.775 -0.006 1.549 -0.011 2.348 -0.017 C6.161 0.033 8.007 0.151 11.25 2.312 C13.113 2.953 14.99 3.553 16.875 4.125 C22.448 5.936 27.284 8.227 32.25 11.312 C32.873 11.688 33.495 12.063 34.137 12.449 C35.552 13.337 36.909 14.316 38.25 15.312 C38.25 15.973 38.25 16.632 38.25 17.312 C35.1 16.23 32.493 14.848 29.75 13 C15.123 3.672 -2.346 1.109 -19.195 4.625 C-29.432 7.38 -39.906 13.259 -45.75 22.312 C-50.753 31.313 -52.488 39.992 -50.086 50.109 C-46.676 59.895 -40.065 64.722 -31.062 69.258 C-23.039 72.975 -14.494 75.157 -5.95 77.347 C2.953 79.686 12.485 82.376 19 89.188 C20.833 93.771 21.178 97.604 19.59 102.238 C17.302 106.863 14.072 109.474 9.25 111.312 C-3.054 114.987 -16.602 114.331 -28.172 108.625 C-35.841 104.222 -35.841 104.222 -38.75 101.312 C-41.484 101.503 -42.582 102.152 -44.566 104.055 C-45.431 105.11 -45.431 105.11 -46.312 106.188 C-49.682 110.255 -49.682 110.255 -51.527 111.82 C-52.981 113.251 -52.981 113.251 -52.637 115.324 C-49.175 123.087 -37.114 127.157 -29.75 130.312 C-15.039 135.781 4.868 135.787 19.25 129.312 C20.221 128.888 21.191 128.464 22.191 128.027 C31.48 123.491 37.669 116.932 41.559 107.355 C44.827 97.698 44.145 88.154 39.773 78.965 C31.724 64.95 13.775 60.879 -0.75 56.625 C-3.093 55.935 -5.434 55.242 -7.772 54.536 C-9.21 54.102 -10.65 53.677 -12.093 53.263 C-18.618 51.298 -24.161 48.248 -27.75 42.312 C-28.364 38.091 -28.066 35.933 -26.125 32.125 C-22.671 28.035 -19.048 25.285 -13.75 24.312 C0.586 23.171 11.175 26.338 23.25 34.312 C23.91 34.312 24.57 34.312 25.25 34.312 C26.665 32.798 26.665 32.798 28.07 30.812 C28.873 29.73 28.873 29.73 29.691 28.625 C30.525 27.48 30.525 27.48 31.375 26.312 C32.22 25.168 32.22 25.168 33.082 24 C34.477 22.108 35.866 20.212 37.25 18.312 C38.25 20.312 38.25 20.312 37.625 22.625 C35.96 25.879 33.893 27.789 31.25 30.312 C29.21 32.612 27.225 34.957 25.25 37.312 C21.891 36.694 19.803 35.444 17.062 33.438 C8.05 27.307 -2.352 25.108 -13.125 27.125 C-16.469 27.908 -19.628 28.856 -22.75 30.312 C-22.75 30.973 -22.75 31.632 -22.75 32.312 C-23.41 32.312 -24.07 32.312 -24.75 32.312 C-25.685 39.088 -25.685 39.088 -23.367 45.191 C-20.381 47.46 -18.606 48.367 -14.812 48.125 C-14.132 47.857 -13.451 47.589 -12.75 47.312 C-12.42 48.303 -12.09 49.293 -11.75 50.312 C-9.096 51.468 -9.096 51.468 -5.801 52.328 C-4.566 52.689 -3.331 53.049 -2.059 53.421 C-1.405 53.607 -0.751 53.793 -0.078 53.985 C32.827 63.41 32.827 63.41 41.926 77.715 C47.15 87.963 46.835 97.677 44.199 108.543 C43.237 111.35 42.095 113.022 40.25 115.312 C39.92 115.996 39.59 116.679 39.25 117.383 C34.983 125.617 22.519 131.359 14.25 134.312 C6.432 136.363 -1.327 136.722 -9.375 136.938 C-10.57 136.976 -11.765 137.015 -12.996 137.055 C-15.914 137.148 -18.832 137.234 -21.75 137.312 C-21.75 136.653 -21.75 135.993 -21.75 135.312 C-23.663 134.78 -25.581 134.262 -27.5 133.75 C-28.567 133.46 -29.635 133.17 -30.734 132.871 C-33.641 132.333 -35.018 132.384 -37.75 133.312 C-38.41 133.312 -39.07 133.312 -39.75 133.312 C-40.75 130.312 -40.75 130.312 -40.75 128.312 C-43.06 126.993 -45.37 125.673 -47.75 124.312 C-47.75 123.653 -47.75 122.993 -47.75 122.312 C-48.585 121.941 -48.585 121.941 -49.438 121.562 C-52.15 120.096 -54.386 118.283 -56.75 116.312 C-56.437 113.391 -55.93 112.461 -53.625 110.562 C-52.676 109.82 -51.727 109.077 -50.75 108.312 C-47.165 104.507 -43.85 100.521 -40.75 96.312 C-40.75 96.972 -40.75 97.632 -40.75 98.312 C-40.171 98.588 -39.592 98.864 -38.996 99.148 C-36.811 100.281 -34.848 101.559 -32.812 102.938 C-29.661 104.947 -27.578 105.911 -23.75 105.312 C-24.245 106.798 -24.245 106.798 -24.75 108.312 C-12.603 110.889 2.148 113.226 13.25 106.312 C16.841 102.984 18.046 100.203 18.25 95.312 C17.17 90.777 15.119 88.811 11.25 86.312 C6.324 83.884 1.676 82.186 -3.812 81.688 C-5.112 81.564 -6.411 81.44 -7.75 81.312 C-8.08 80.653 -8.41 79.993 -8.75 79.312 C-10.526 78.566 -10.526 78.566 -12.68 78.023 C-13.475 77.804 -14.27 77.585 -15.09 77.359 C-15.926 77.138 -16.763 76.916 -17.625 76.688 C-18.454 76.458 -19.283 76.229 -20.137 75.992 C-24.874 74.701 -24.874 74.701 -29.75 74.312 C-30.774 73.337 -31.77 72.332 -32.75 71.312 C-34.351 70.295 -35.979 69.318 -37.625 68.375 C-47.753 62.396 -47.753 62.396 -49.75 57.312 C-50.41 57.312 -51.07 57.312 -51.75 57.312 C-52.75 54.312 -52.75 54.312 -52.75 52.312 C-53.41 51.322 -54.07 50.332 -54.75 49.312 C-54.09 49.312 -53.43 49.312 -52.75 49.312 C-53.245 47.827 -53.245 47.827 -53.75 46.312 C-54.514 38.079 -53.52 31.653 -49.75 24.312 C-49.411 23.614 -49.072 22.915 -48.723 22.195 C-43.784 13.162 -34.254 6.812 -24.75 3.312 C-16.511 0.917 -8.536 0.052 0 0 Z " fill="#5663C2" transform="translate(303.75,362.6875)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C1.052 2.062 2.104 2.124 3.188 2.188 C7.675 3.144 9.071 4.569 12 8 C12.66 8.66 13.32 9.32 14 10 C14.414 12.984 14.414 12.984 14.625 16.75 C14.682 17.739 14.682 17.739 14.74 18.748 C15.094 25.498 15.094 32.242 15 39 C15.33 38.67 15.66 38.34 16 38 C16.33 38.99 16.66 39.98 17 41 C12.039 42.203 7.27 42.464 2.25 41.438 C-0.155 40.779 -0.155 40.779 -3 42 C-11.217 42.261 -11.217 42.261 -15 41 C-15 40.34 -15 39.68 -15 39 C-16.485 39.495 -16.485 39.495 -18 40 C-18.66 39.34 -19.32 38.68 -20 38 C-20 37.01 -20 36.02 -20 35 C-20.306 33.266 -20.642 31.537 -21 29.812 C-21.186 28.911 -21.371 28.01 -21.562 27.082 C-21.707 26.395 -21.851 25.708 -22 25 C-21.34 25 -20.68 25 -20 25 C-19.732 24.464 -19.464 23.928 -19.188 23.375 C-18 21 -18 21 -16.188 18.062 C-15.796 17.052 -15.404 16.041 -15 15 C-16.156 12.268 -16.156 12.268 -18 10 C-18.33 9.01 -18.66 8.02 -19 7 C-14.588 1.891 -6.55 0 0 0 Z M-4 11 C-4.99 12.485 -4.99 12.485 -6 14 C-5.67 14.66 -5.34 15.32 -5 16 C-3.35 16 -1.7 16 0 16 C-0.33 15.34 -0.66 14.68 -1 14 C-0.67 13.01 -0.34 12.02 0 11 C-1.32 11 -2.64 11 -4 11 Z M-8 11 C-9.65 11.66 -11.3 12.32 -13 13 C-12.01 13.495 -12.01 13.495 -11 14 C-10.67 14.66 -10.34 15.32 -10 16 C-9.01 15.01 -8.02 14.02 -7 13 C-7.33 12.34 -7.66 11.68 -8 11 Z M3 14 C4 16 4 16 4 16 Z M-10 26 C-9.814 26.619 -9.629 27.237 -9.438 27.875 C-9 30 -9 30 -10 32 C-7.291 33.354 -4.991 33.065 -2 33 C-2.33 32.34 -2.66 31.68 -3 31 C-2.01 31.33 -1.02 31.66 0 32 C0.66 30.35 1.32 28.7 2 27 C2.99 28.485 2.99 28.485 4 30 C4.33 28.35 4.66 26.7 5 25 C3.063 24.973 1.125 24.954 -0.812 24.938 C-2.431 24.92 -2.431 24.92 -4.082 24.902 C-7.065 24.864 -7.065 24.864 -10 26 Z " fill="#F7F7FD" transform="translate(415,630)"/>
<path d="M0 0 C2.646 -0.312 2.646 -0.312 5 -1 C5.495 0.485 5.495 0.485 6 2 C8.24 3.264 10.419 4.373 12.75 5.438 C17.285 7.531 21.139 9.803 25 13 C26.048 13.516 26.048 13.516 27.117 14.043 C29.325 15.165 30.529 16.429 32.125 18.312 C32.623 18.886 33.12 19.46 33.633 20.051 C35.184 22.263 36.084 24.466 37 27 C31.444 31.505 25.873 35.914 20 40 C19.34 39.67 18.68 39.34 18 39 C23.28 34.71 28.56 30.42 34 26 C28.129 18.172 22.992 14.001 15 9 C14.321 8.566 13.641 8.131 12.941 7.684 C-1.671 -0.615 -22.409 -1.431 -38.469 2.254 C-48.686 5.265 -56.697 9.307 -65 16 C-65.825 16.598 -66.65 17.196 -67.5 17.812 C-70.612 20.535 -72.738 23.499 -75 26.938 C-75.435 27.595 -75.87 28.252 -76.318 28.929 C-83.704 40.445 -86.18 51.629 -86.25 65.188 C-86.271 66.165 -86.291 67.143 -86.312 68.15 C-86.392 83.186 -81.008 97.773 -71 109 C-70.49 109.603 -69.979 110.207 -69.453 110.828 C-58.982 122.336 -44.016 128.704 -28.715 129.477 C-5.936 130.235 11.656 127.05 28.871 111.051 C30.6 109.385 32.313 107.708 34 106 C32.458 101.599 28.986 99.404 25.375 96.75 C24.764 96.291 24.153 95.832 23.523 95.359 C22.021 94.232 20.511 93.115 19 92 C16.367 93.212 14.907 94.099 12.875 96.25 C2.109 106.548 -10.097 108.523 -24.383 108.27 C-35.504 107.806 -44.949 103.691 -53 96 C-62.051 84.761 -64.93 72.208 -64 58 C-62.779 49.711 -60.096 43.599 -55 37 C-54.423 36.134 -53.845 35.267 -53.25 34.375 C-44.997 25.664 -34.477 20.906 -22.5 20.562 C-6.643 21.131 4.812 27.074 16 38 C15.505 38.99 15.505 38.99 15 40 C14.42 39.434 13.84 38.868 13.242 38.285 C7.565 32.683 7.565 32.683 0 31 C0.495 30.01 0.495 30.01 1 29 C-0.727 28.35 -2.457 27.705 -4.188 27.062 C-5.15 26.703 -6.113 26.343 -7.105 25.973 C-12.197 24.262 -16.628 23.895 -22 24 C-20.515 24.495 -20.515 24.495 -19 25 C-19.33 25.66 -19.66 26.32 -20 27 C-21.65 27 -23.3 27 -25 27 C-25 26.34 -25 25.68 -25 25 C-31.658 23.071 -37.084 25.941 -43 29 C-52.98 36.078 -58.137 44.239 -61 56 C-62.035 68.154 -61.539 81.559 -53.465 91.449 C-47.864 97.379 -42.197 102.26 -34 104 C-31.28 103.743 -29.54 103.104 -27 102 C-27.33 102.66 -27.66 103.32 -28 104 C-28.66 104 -29.32 104 -30 104 C-30 104.66 -30 105.32 -30 106 C-22.039 106.144 -13.876 106.277 -6 105 C-5.67 104.34 -5.34 103.68 -5 103 C-4.34 103.33 -3.68 103.66 -3 104 C-2.67 103.34 -2.34 102.68 -2 102 C-1.154 101.752 -0.309 101.505 0.562 101.25 C7.291 98.803 13.042 93.936 17 88 C20.243 88.559 21.931 89.413 24 92 C24 92.66 24 93.32 24 94 C24.572 94.262 25.145 94.523 25.734 94.793 C28.166 96.089 30.136 97.547 32.25 99.312 C32.956 99.886 33.663 100.46 34.391 101.051 C36 103 36 103 36.547 105.418 C35.76 109.132 33.821 110.528 31 113 C30.207 113.73 29.414 114.459 28.598 115.211 C12.633 128.839 -5.634 134.19 -26.414 132.75 C-35.34 131.936 -43.508 129.906 -52 127 C-52.757 126.836 -53.513 126.673 -54.293 126.504 C-55.138 126.254 -55.138 126.254 -56 126 C-56.33 125.34 -56.66 124.68 -57 124 C-59.276 122.429 -61.617 120.99 -63.969 119.535 C-66 118 -66 118 -67 115 C-67.66 115 -68.32 115 -69 115 C-69.495 114.01 -69.495 114.01 -70 113 C-71.65 113 -73.3 113 -75 113 C-74.654 112.141 -74.654 112.141 -74.301 111.266 C-73.745 108.919 -73.745 108.919 -75.293 106.922 C-75.918 106.205 -76.543 105.488 -77.188 104.75 C-80.565 100.68 -82.743 96.765 -85 92 C-85.66 91.34 -86.32 90.68 -87 90 C-88 89 -88 89 -89 86 C-88.34 86 -87.68 86 -87 86 C-87.311 84.25 -87.624 82.5 -87.938 80.75 C-88.112 79.775 -88.286 78.801 -88.465 77.797 C-88.928 75.13 -88.928 75.13 -89.562 73.094 C-90.215 69.973 -90.092 66.932 -90.062 63.75 C-90.053 62.487 -90.044 61.223 -90.035 59.922 C-90.024 58.958 -90.012 57.993 -90 57 C-89.34 57 -88.68 57 -88 57 C-88.083 56.113 -88.165 55.226 -88.25 54.312 C-88.133 39.775 -78.964 26.392 -69.348 16.215 C-65.535 12.618 -61.473 9.717 -57 7 C-55.882 6.308 -55.882 6.308 -54.742 5.602 C-41.868 -1.599 -12.841 -8.561 0 0 Z " fill="#5866C2" transform="translate(235,189)"/>
<path d="M0 0 C4.45 3.56 7.456 8.367 8.5 14 C8.539 16.333 8.545 18.667 8.5 21 C6.52 21.99 6.52 21.99 4.5 23 C4.851 23.887 5.201 24.774 5.562 25.688 C6.536 29.127 6.753 30.689 5.5 34 C1.588 38.063 -4.036 39.08 -9.5 39.312 C-13.635 39.208 -16.801 37.849 -20.5 36 C-21.263 35.691 -22.026 35.381 -22.812 35.062 C-24.961 33.71 -25.472 32.277 -26.5 30 C-27.16 29.67 -27.82 29.34 -28.5 29 C-28.5 28.34 -28.5 27.68 -28.5 27 C-29.16 27 -29.82 27 -30.5 27 C-30.314 26.113 -30.129 25.226 -29.938 24.312 C-29.52 21.148 -29.692 19.052 -30.5 16 C-29.84 16 -29.18 16 -28.5 16 C-28.397 14.948 -28.294 13.896 -28.188 12.812 C-27.074 6.636 -24.601 3.571 -19.5 0 C-13.372 -2.043 -6.17 -1.763 0 0 Z M-18.5 12 C-18.5 12.66 -18.5 13.32 -18.5 14 C-17.51 14 -16.52 14 -15.5 14 C-15.17 13.67 -14.84 13.34 -14.5 13 C-14.5 13.33 -14.5 13.66 -14.5 14 C-10.21 14 -5.92 14 -1.5 14 C-1.83 13.34 -2.16 12.68 -2.5 12 C-3.49 11.67 -4.48 11.34 -5.5 11 C-6.16 10.34 -6.82 9.68 -7.5 9 C-7.5 8.67 -7.5 8.34 -7.5 8 C-12.594 7.526 -14.873 8.373 -18.5 12 Z M-18.5 22 C-17.024 27.06 -17.024 27.06 -13.5 29 C-12.51 28.67 -11.52 28.34 -10.5 28 C-7.721 28 -6.229 28.09 -3.5 29 C-2.84 28.34 -2.18 27.68 -1.5 27 C-1.819 24.892 -1.819 24.892 -2.5 23 C-1.84 22.67 -1.18 22.34 -0.5 22 C-6.44 22 -12.38 22 -18.5 22 Z M0.5 23 C1.5 25 1.5 25 1.5 25 Z M-0.5 25 C-0.83 25.66 -1.16 26.32 -1.5 27 C-0.84 27 -0.18 27 0.5 27 C0.17 26.34 -0.16 25.68 -0.5 25 Z " fill="#F8F7FD" transform="translate(646.5,633)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C14.667 6 17.333 12 20 18 C21.65 17.67 23.3 17.34 25 17 C25.182 16.359 25.364 15.719 25.551 15.059 C27.063 9.856 28.75 4.929 31 0 C34.3 0 37.6 0 41 0 C40.451 5.152 39.215 9.532 37.188 14.312 C36.921 14.964 36.654 15.616 36.378 16.287 C35.184 19.198 33.959 22.094 32.715 24.984 C30.78 29.561 29.62 32.935 30 38 C29.67 38.66 29.34 39.32 29 40 C27.515 40.495 27.515 40.495 26 41 C24.538 42.995 24.538 42.995 23.188 45.375 C20.607 49.453 18.509 51.112 14 53 C13.67 53.99 13.34 54.98 13 56 C12.031 55.67 11.061 55.34 10.062 55 C7.379 53.948 7.379 53.948 6 54 C3 52.5 3 52.5 0 51 C0.33 50.01 0.66 49.02 1 48 C1.165 47.175 1.33 46.35 1.5 45.5 C2 43 2 43 3 40 C5 41 5 41 6 42 C9.458 42.25 9.458 42.25 13 42 C15.411 40.044 15.411 40.044 15.125 36.375 C15.084 35.261 15.043 34.148 15 33 C13.02 32.34 11.04 31.68 9 31 C9.33 30.34 9.66 29.68 10 29 C10.99 29.33 11.98 29.66 13 30 C12.639 29.157 12.278 28.314 11.906 27.445 C11.442 26.35 10.978 25.254 10.5 24.125 C9.804 22.489 9.804 22.489 9.094 20.82 C8 18 8 18 8 16 C7.34 15.67 6.68 15.34 6 15 C5.148 12.934 5.148 12.934 4.375 10.438 C4.115 9.611 3.854 8.785 3.586 7.934 C3.393 7.296 3.199 6.657 3 6 C2.67 6.66 2.34 7.32 2 8 C1.34 7.67 0.68 7.34 0 7 C0.33 6.01 0.66 5.02 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z M20 19 C20.33 20.65 20.66 22.3 21 24 C21.66 23.01 22.32 22.02 23 21 C22.34 21 21.68 21 21 21 C20.67 20.34 20.34 19.68 20 19 Z M28 34 C29 36 29 36 29 36 Z M27 37 C28 39 28 39 28 39 Z " fill="#F5F5FC" transform="translate(432,632)"/>
<path d="M0 0 C2.225 1.52 3.352 2.803 5 5 C5 5.66 5 6.32 5 7 C7.475 7.495 7.475 7.495 10 8 C9.34 8.33 8.68 8.66 8 9 C6.845 12.192 6.884 15.274 6.902 18.641 C6.906 19.711 6.909 20.78 6.912 21.883 C6.92 22.994 6.929 24.105 6.938 25.25 C6.942 26.377 6.947 27.503 6.951 28.664 C6.963 31.443 6.979 34.221 7 37 C2.265 39.208 -0.062 38.743 -5 37 C-4.99 36.38 -4.979 35.759 -4.968 35.12 C-4.927 32.309 -4.901 29.498 -4.875 26.688 C-4.858 25.711 -4.841 24.735 -4.824 23.729 C-4.818 22.791 -4.811 21.853 -4.805 20.887 C-4.794 20.023 -4.784 19.159 -4.773 18.268 C-4.849 15.718 -4.849 15.718 -7 13 C-7 11.68 -7 10.36 -7 9 C-8.258 8.938 -9.516 8.876 -10.812 8.812 C-11.52 8.778 -12.228 8.743 -12.957 8.707 C-15.676 9.097 -16.348 9.817 -18 12 C-18.506 14.803 -18.506 14.803 -18.512 17.98 C-18.547 19.144 -18.583 20.307 -18.619 21.506 C-18.642 22.721 -18.664 23.936 -18.688 25.188 C-18.722 26.414 -18.756 27.641 -18.791 28.904 C-18.874 31.936 -18.943 34.968 -19 38 C-22.019 38.805 -24.619 39.109 -27.75 39.062 C-28.549 39.053 -29.348 39.044 -30.172 39.035 C-30.775 39.024 -31.378 39.012 -32 39 C-33.097 35.71 -32.8 34.287 -32 31 C-31.67 31 -31.34 31 -31 31 C-30.67 20.44 -30.34 9.88 -30 -1 C-26.7 -1 -23.4 -1 -20 -1 C-20 -0.34 -20 0.32 -20 1 C-19.394 0.76 -18.788 0.52 -18.164 0.273 C-11.661 -2.028 -6.517 -2.584 0 0 Z " fill="#FBFAFE" transform="translate(604,633)"/>
<path d="M0 0 C1.625 -0.054 3.25 -0.093 4.875 -0.125 C6.232 -0.16 6.232 -0.16 7.617 -0.195 C10 0 10 0 12 2 C11.34 2 10.68 2 10 2 C10.165 2.825 10.33 3.65 10.5 4.5 C11.209 9.463 11.607 14.178 10 19 C10.13 24.514 10.13 24.514 13 29 C15.85 30.222 17.116 29.97 20.188 29.188 C23.116 28.31 23.116 28.31 24 26 C24.142 24.127 24.226 22.249 24.281 20.371 C24.319 19.254 24.356 18.137 24.395 16.986 C24.465 14.617 24.535 12.247 24.605 9.877 C24.643 8.761 24.68 7.646 24.719 6.496 C24.749 5.467 24.779 4.437 24.811 3.377 C25 1 25 1 26 0 C28.97 0 31.94 0 35 0 C35.33 12.21 35.66 24.42 36 37 C36.99 37.495 36.99 37.495 38 38 C37.505 38.99 37.505 38.99 37 40 C35.396 40.054 33.792 40.093 32.188 40.125 C31.294 40.148 30.401 40.171 29.48 40.195 C27 40 27 40 24 38 C22.339 38.498 20.68 39.001 19.031 39.539 C14.304 40.612 9.65 39.623 5.188 37.938 C1.135 34.348 -0.946 29.259 -2 24 C-2 21.667 -2 19.333 -2 17 C-2.195 15.665 -2.403 14.331 -2.625 13 C-3.288 8.265 -2.13 4.259 0 0 Z " fill="#F9F9FD" transform="translate(530,632)"/>
<path d="M0 0 C1.301 -0.037 2.603 -0.075 3.943 -0.113 C5.188 -0.125 6.433 -0.136 7.715 -0.148 C8.859 -0.168 10.004 -0.188 11.183 -0.208 C14.062 0.375 14.062 0.375 15.804 2.631 C16.219 3.537 16.635 4.442 17.062 5.375 C17.685 6.474 17.685 6.474 18.321 7.595 C18.926 8.687 18.926 8.687 19.543 9.801 C20.205 10.995 20.205 10.995 20.881 12.213 C21.333 13.03 21.784 13.846 22.25 14.688 C22.713 15.523 23.177 16.359 23.654 17.221 C24.791 19.272 25.927 21.323 27.062 23.375 C27.722 23.375 28.382 23.375 29.062 23.375 C31.062 25.375 31.062 25.375 31.062 28.375 C32.238 31.902 33.816 34.445 36.062 37.375 C36.392 38.365 36.722 39.355 37.062 40.375 C38.05 42.049 39.051 43.716 40.062 45.375 C40.342 45.956 40.622 46.538 40.91 47.137 C43.046 51.286 45.682 55.153 48.188 59.086 C49.938 62.157 51.081 64.991 52.062 68.375 C49.062 67.375 49.062 67.375 47.738 65.109 C47.288 64.125 46.839 63.14 46.375 62.125 C44.306 57.811 42.123 53.729 39.562 49.688 C34.84 42.171 30.557 34.419 26.25 26.659 C25.682 25.636 25.113 24.613 24.527 23.559 C23.965 22.545 23.403 21.532 22.824 20.488 C20.414 16.228 17.89 12.039 15.35 7.856 C14.062 5.375 14.062 5.375 14.062 2.375 C5.152 2.375 -3.757 2.375 -12.938 2.375 C-12.938 43.955 -12.938 85.535 -12.938 128.375 C-5.017 128.375 2.902 128.375 11.062 128.375 C11.062 101.315 11.062 74.255 11.062 46.375 C13.754 49.74 15.705 52.272 17.656 55.938 C18.123 56.807 18.59 57.676 19.072 58.571 C19.564 59.497 20.056 60.422 20.562 61.375 C24.764 69.178 29.034 76.934 33.438 84.625 C33.913 85.456 34.388 86.288 34.877 87.145 C37.814 92.263 40.839 97.318 43.948 102.335 C45.062 104.375 45.062 104.375 45.062 106.375 C48.965 106.689 52.316 106.571 56.062 105.375 C59.177 102.09 60.874 98.275 62.75 94.188 C63.848 91.981 64.947 89.775 66.047 87.57 C66.821 85.966 66.821 85.966 67.611 84.329 C69.422 80.643 71.413 77.097 73.5 73.562 C76.782 67.883 79.951 62.15 83.062 56.375 C84.075 58.997 84.196 60.058 83.09 62.693 C82.589 63.527 82.087 64.36 81.57 65.219 C81.03 66.135 80.49 67.052 79.934 67.996 C79.357 68.946 78.781 69.896 78.188 70.875 C77.061 72.758 75.938 74.643 74.82 76.531 C74.316 77.365 73.813 78.198 73.293 79.057 C72.158 81.195 71.519 83.01 71.062 85.375 C70.403 85.375 69.743 85.375 69.062 85.375 C68.825 86.031 68.588 86.687 68.344 87.363 C66.686 91.261 64.648 94.881 62.562 98.562 C62.159 99.286 61.756 100.01 61.34 100.756 C60.949 101.449 60.559 102.142 60.156 102.855 C59.625 103.797 59.625 103.797 59.084 104.759 C58.062 106.375 58.062 106.375 56.062 108.375 C53.242 108.57 53.242 108.57 49.938 108.5 C48.842 108.482 47.746 108.464 46.617 108.445 C45.774 108.422 44.931 108.399 44.062 108.375 C40.062 101.625 40.062 101.625 40.062 99.375 C39.403 99.375 38.743 99.375 38.062 99.375 C37.508 98.243 36.954 97.111 36.383 95.945 C35.651 94.463 34.919 92.982 34.188 91.5 C33.823 90.754 33.458 90.007 33.082 89.238 C32.728 88.523 32.373 87.807 32.008 87.07 C31.521 86.081 31.521 86.081 31.024 85.071 C29.723 82.777 28.418 81.553 26.062 80.375 C25.875 79.06 25.875 79.06 25.684 77.719 C24.938 73.703 23.364 71.276 21.125 67.875 C18.428 63.764 16.425 60.111 15.062 55.375 C14.402 55.375 13.743 55.375 13.062 55.375 C13.062 79.795 13.062 104.215 13.062 129.375 C8.641 131.144 6.826 131.618 2.324 131.57 C0.724 131.561 0.724 131.561 -0.908 131.551 C-2.562 131.526 -2.562 131.526 -4.25 131.5 C-5.372 131.491 -6.494 131.482 -7.65 131.473 C-10.413 131.449 -13.175 131.416 -15.938 131.375 C-16.062 124.75 -16.062 124.75 -14.938 121.375 C-15.598 121.045 -16.257 120.715 -16.938 120.375 C-16.775 119.867 -16.612 119.358 -16.444 118.835 C-15.764 115.531 -15.826 112.392 -15.84 109.02 C-15.841 108.317 -15.843 107.615 -15.844 106.892 C-15.85 104.657 -15.862 102.422 -15.875 100.188 C-15.88 98.673 -15.885 97.158 -15.889 95.643 C-15.9 91.887 -15.918 88.131 -15.938 84.375 C-15.938 84.045 -15.938 83.715 -15.938 83.375 C-16.267 82.385 -16.598 81.395 -16.938 80.375 C-16.615 79.039 -16.28 77.706 -15.938 76.375 C-15.899 74.375 -15.892 72.374 -15.938 70.375 C-15.277 70.375 -14.618 70.375 -13.938 70.375 C-13.938 69.385 -13.938 68.395 -13.938 67.375 C-14.598 67.045 -15.257 66.715 -15.938 66.375 C-15.938 56.375 -15.938 56.375 -15.375 54 C-14.9 51.147 -15.223 49.155 -15.938 46.375 C-15.277 46.375 -14.618 46.375 -13.938 46.375 C-14.288 45.303 -14.639 44.23 -15 43.125 C-15.91 39.483 -15.906 38.709 -14.938 35.375 C-15.224 33.368 -15.556 31.366 -15.938 29.375 C-15.938 26.042 -15.938 22.708 -15.938 19.375 C-16.164 18.591 -16.391 17.808 -16.625 17 C-16.728 16.134 -16.831 15.268 -16.938 14.375 C-15.5 12.562 -15.5 12.562 -13.938 11.375 C-14.598 11.375 -15.257 11.375 -15.938 11.375 C-15.277 8.405 -14.618 5.435 -13.938 2.375 C-14.598 2.045 -15.257 1.715 -15.938 1.375 C-11.346 -0.921 -5.041 0.055 0 0 Z M-14.938 59.375 C-13.938 61.375 -13.938 61.375 -13.938 61.375 Z " fill="#7E85D0" transform="translate(447.9375,188.625)"/>
<path d="M0 0 C2 2 2 2 2.125 5.625 C2.084 6.739 2.043 7.852 2 9 C2.638 9.061 3.276 9.121 3.934 9.184 C4.76 9.267 5.586 9.351 6.438 9.438 C7.673 9.559 7.673 9.559 8.934 9.684 C11 10 11 10 12 11 C12.072 12.853 12.084 14.708 12.062 16.562 C12.053 17.574 12.044 18.586 12.035 19.629 C12.024 20.411 12.012 21.194 12 22 C10.68 21.34 9.36 20.68 8 20 C8.33 20.99 8.66 21.98 9 23 C6.063 22.371 3.662 21.376 1 20 C1.142 22.229 1.289 24.459 1.438 26.688 C1.519 27.929 1.6 29.17 1.684 30.449 C1.945 33.382 2.348 36.134 3 39 C4.98 38.01 6.96 37.02 9 36 C9.33 36.66 9.66 37.32 10 38 C11.979 38.727 13.98 39.398 16 40 C15.01 40.495 15.01 40.495 14 41 C13.593 43.322 13.256 45.657 13 48 C7.609 50.331 1.798 50.881 -4 50 C-4.66 49.01 -5.32 48.02 -6 47 C-6.99 47.99 -7.98 48.98 -9 50 C-9 46.7 -9 43.4 -9 40 C-9.66 39.67 -10.32 39.34 -11 39 C-10.835 38.01 -10.67 37.02 -10.5 36 C-9.915 31.973 -9.929 28.065 -10 24 C-11.32 24.33 -12.64 24.66 -14 25 C-14.217 23.886 -14.217 23.886 -14.438 22.75 C-14.912 20.105 -14.912 20.105 -15.688 18.062 C-16.145 15.041 -15.083 12.802 -14 10 C-12.68 10 -11.36 10 -10 10 C-9.67 7.03 -9.34 4.06 -9 1 C-2.164 0.023 -2.164 0.023 0 0 Z " fill="#F5F5FC" transform="translate(377,622)"/>
<path d="M0 0 C1 1 1 1 1.098 3.066 C1.065 5.378 1.033 7.689 1 10 C4.3 9.67 7.6 9.34 11 9 C12.603 12.118 13.19 13.39 12.125 16.812 C11.754 17.534 11.383 18.256 11 19 C9.36 19.217 9.36 19.217 7.688 19.438 C4.069 19.883 4.069 19.883 1 21 C1.114 23.084 1.242 25.167 1.375 27.25 C1.445 28.41 1.514 29.57 1.586 30.766 C1.962 33.707 2.477 35.499 4 38 C7.465 37.505 7.465 37.505 11 37 C11.33 37.66 11.66 38.32 12 39 C12.99 39 13.98 39 15 39 C15.99 39 16.98 39 18 39 C18 39.66 18 40.32 18 41 C17.01 41.66 16.02 42.32 15 43 C14.67 43.66 14.34 44.32 14 45 C13.34 45 12.68 45 12 45 C12 45.66 12 46.32 12 47 C6.576 48.618 2.62 49.485 -3 48 C-5.642 46.18 -7.739 44.261 -10 42 C-9.505 41.505 -9.505 41.505 -9 41 C-8.959 39.334 -8.957 37.666 -9 36 C-9.66 35.67 -10.32 35.34 -11 35 C-10.67 33.68 -10.34 32.36 -10 31 C-10.66 30.67 -11.32 30.34 -12 30 C-11.34 28.02 -10.68 26.04 -10 24 C-10.66 24 -11.32 24 -12 24 C-12.66 22.68 -13.32 21.36 -14 20 C-12.68 20.33 -11.36 20.66 -10 21 C-11.641 19.359 -13.778 19.399 -16 19 C-17.231 15.306 -16.644 12.794 -16 9 C-14.02 9 -12.04 9 -10 9 C-10 6.36 -10 3.72 -10 1 C-6.747 -0.627 -3.608 -0.115 0 0 Z " fill="#FAFAFE" transform="translate(511,623)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.536 2.206 1.072 2.413 1.625 2.625 C4.92 4.533 7.594 7.048 10 10 C10 10.66 10 11.32 10 12 C10.66 12 11.32 12 12 12 C13.508 14.117 13.508 14.117 15.125 16.875 C15.933 18.232 15.933 18.232 16.758 19.617 C18 22 18 22 18 24 C19.32 23.67 20.64 23.34 22 23 C22 23.99 22 24.98 22 26 C21.34 26 20.68 26 20 26 C20.277 26.869 20.554 27.738 20.84 28.633 C21.202 29.785 21.564 30.938 21.938 32.125 C22.477 33.83 22.477 33.83 23.027 35.57 C28.061 53.319 25.763 72.204 17.141 88.258 C8.106 104.402 -6.57 114.911 -24.188 119.938 C-26.787 120.647 -29.392 121.325 -32 122 C-32.99 122.33 -33.98 122.66 -35 123 C-35 123.33 -35 123.66 -35 124 C-36.98 124.33 -38.96 124.66 -41 125 C-41 124.01 -41 123.02 -41 122 C-41.608 122.035 -42.217 122.07 -42.844 122.105 C-48.935 122.294 -54.652 121.721 -60.625 120.625 C-61.336 120.499 -62.047 120.373 -62.779 120.243 C-64.535 119.901 -66.271 119.459 -68 119 C-68.33 118.34 -68.66 117.68 -69 117 C-70.052 116.711 -71.104 116.423 -72.188 116.125 C-76.109 114.968 -78.651 113.283 -82 111 C-82.99 111 -83.98 111 -85 111 C-85.248 110.361 -85.495 109.721 -85.75 109.062 C-86.163 108.382 -86.575 107.701 -87 107 C-87.625 106.878 -88.25 106.755 -88.895 106.629 C-91.785 105.766 -92.383 104.416 -93.938 101.875 C-94.448 101.068 -94.958 100.261 -95.484 99.43 C-95.985 98.628 -96.485 97.826 -97 97 C-100.067 91.921 -100.067 91.921 -105 89 C-104.67 88.01 -104.34 87.02 -104 86 C-104.695 83.931 -105.481 81.893 -106.312 79.875 C-109.04 72.872 -109.97 66.067 -110.641 58.617 C-110.894 55.172 -110.894 55.172 -111.609 52.57 C-111.738 52.052 -111.867 51.534 -112 51 C-111.505 50.505 -111.505 50.505 -111 50 C-110.687 48.161 -110.379 46.321 -110.101 44.477 C-107.754 29.15 -101.783 15.399 -91 4 C-90.34 4 -89.68 4 -89 4 C-89 3.34 -89 2.68 -89 2 C-84.031 -2.134 -75.744 -9 -69 -9 C-68.67 -9.66 -68.34 -10.32 -68 -11 C-65.069 -11.657 -62.216 -12.185 -59.25 -12.625 C-57.948 -12.818 -57.948 -12.818 -56.619 -13.016 C-35.726 -15.957 -17.169 -12.619 0 0 Z M-94.41 10.871 C-97.289 14.726 -99.713 18.771 -102 23 C-102.382 23.696 -102.763 24.392 -103.156 25.109 C-111.923 42.775 -110.191 63.645 -104.258 81.863 C-102.173 87.064 -99.306 91.498 -96 96 C-95.267 97.044 -95.267 97.044 -94.52 98.109 C-85.195 110.095 -69.646 117.402 -55 120 C-35.478 121.481 -16.243 119.375 -0.703 106.312 C6.701 99.783 12.801 93.109 17.188 84.25 C17.53 83.598 17.872 82.945 18.225 82.273 C25.932 66.889 25.03 46.035 19.745 29.962 C13.055 12.342 -0.084 0.487 -16.93 -7.301 C-43.58 -18.859 -75.894 -11.392 -94.41 10.871 Z " fill="#5C68C1" transform="translate(473,377)"/>
<path d="M0 0 C15.551 11.782 26.024 26.296 29.375 46.008 C31.736 65.029 27.918 83.93 16.062 99.32 C15.127 100.538 15.127 100.538 14.172 101.781 C3.791 114.276 -10.888 121.91 -26.938 124.32 C-29.374 124.387 -31.813 124.406 -34.25 124.383 C-35.504 124.374 -36.759 124.365 -38.051 124.355 C-39.48 124.338 -39.48 124.338 -40.938 124.32 C-41.267 124.98 -41.597 125.64 -41.938 126.32 C-41.938 125.66 -41.938 125 -41.938 124.32 C-42.866 124.382 -43.794 124.444 -44.75 124.508 C-50.021 124.54 -54.937 122.839 -59.938 121.32 C-60.267 121.98 -60.597 122.64 -60.938 123.32 C-61.639 122.722 -62.34 122.124 -63.062 121.508 C-67.411 118.199 -71.512 116.118 -76.938 115.32 C-77.267 114.33 -77.598 113.34 -77.938 112.32 C-79.461 110.913 -79.461 110.913 -81.312 109.695 C-81.918 109.27 -82.524 108.845 -83.148 108.406 C-85.057 107.157 -85.057 107.157 -87.938 106.32 C-88.267 104.67 -88.598 103.02 -88.938 101.32 C-90.423 100.825 -90.423 100.825 -91.938 100.32 C-93.619 97.553 -95.002 94.69 -96.438 91.789 C-97.754 89.022 -97.754 89.022 -100.938 88.32 C-100.938 85.35 -100.938 82.38 -100.938 79.32 C-101.598 78.99 -102.257 78.66 -102.938 78.32 C-103.864 74.361 -104.212 70.293 -104.688 66.258 C-104.838 65.137 -104.989 64.016 -105.145 62.861 C-105.923 56.429 -106.018 51.48 -103.938 45.32 C-103.608 45.32 -103.277 45.32 -102.938 45.32 C-102.928 45.938 -102.919 46.557 -102.909 47.194 C-102.48 69.873 -99.344 88.412 -83.031 105.293 C-67.32 119.942 -48.344 122.114 -27.874 121.5 C-22.109 121.148 -17.209 119.707 -11.938 117.32 C-10.931 116.896 -9.924 116.472 -8.887 116.035 C6.403 109.061 16.696 97.768 23.062 82.32 C28.823 64.944 29.397 45.219 21.227 28.473 C12.313 11.992 1.249 1.679 -16.672 -4.645 C-34.187 -9.672 -54.467 -8.4 -70.522 0.157 C-85.871 8.759 -94.17 20.428 -99.746 36.887 C-100.661 39.522 -101.604 41.883 -102.938 44.32 C-103.471 37.983 -101.717 33.737 -99 28.07 C-98.261 26.482 -97.523 24.894 -96.785 23.305 C-96.455 22.611 -96.125 21.917 -95.785 21.202 C-94.83 19.166 -94.83 19.166 -93.938 16.32 C-93.298 15.846 -92.659 15.372 -92 14.883 C-89.84 13.497 -89.84 13.497 -89.031 11.402 C-87.056 7.643 -83.359 5.699 -79.938 3.32 C-78.86 2.539 -78.86 2.539 -77.762 1.742 C-72.431 -2.007 -67.132 -4.657 -60.938 -6.68 C-60.018 -6.98 -59.099 -7.28 -58.152 -7.59 C-39.147 -12.862 -16.529 -11.198 0 0 Z " fill="#4C59BB" transform="translate(384.9375,196.6796875)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C26 42.9 26 85.8 26 130 C22.596 131.135 19.96 131.133 16.375 131.125 C15.207 131.128 14.039 131.13 12.836 131.133 C9.927 131.032 7.144 130.824 4.26 130.492 C3.46 130.4 2.659 130.308 1.834 130.214 C0.926 130.108 0.926 130.108 0 130 C-0.01 129.052 -0.021 128.105 -0.031 127.128 C-0.129 118.211 -0.232 109.293 -0.339 100.376 C-0.395 95.791 -0.448 91.206 -0.497 86.622 C-0.545 82.2 -0.597 77.778 -0.653 73.356 C-0.673 71.666 -0.692 69.976 -0.709 68.286 C-0.733 65.925 -0.763 63.565 -0.795 61.204 C-0.803 60.148 -0.803 60.148 -0.812 59.072 C-0.825 55.412 -0.825 55.412 -2 52 C-1.01 50.68 -0.02 49.36 1 48 C1 74.4 1 100.8 1 128 C8.59 128 16.18 128 24 128 C24 86.42 24 44.84 24 2 C2.521 0.303 2.521 0.303 -3.852 7.977 C-5.395 11.173 -5.395 11.173 -6.625 14.625 C-7.699 17.009 -8.778 19.392 -9.859 21.773 C-10.373 22.982 -10.887 24.191 -11.416 25.437 C-13.858 30.929 -16.848 36.064 -19.875 41.25 C-20.941 43.12 -22.004 44.991 -23.066 46.863 C-24.044 48.576 -25.022 50.288 -26 52 C-26.863 53.514 -27.727 55.029 -28.59 56.543 C-30.389 59.698 -32.193 62.85 -34 66 C-35.333 63.333 -34.671 61.833 -34 59 C-33.34 59 -32.68 59 -32 59 C-31.67 57.35 -31.34 55.7 -31 54 C-30.34 54 -29.68 54 -29 54 C-28.711 53.113 -28.423 52.226 -28.125 51.312 C-27.169 48.496 -26.132 45.749 -25 43 C-24.34 43 -23.68 43 -23 43 C-22.34 40.69 -21.68 38.38 -21 36 C-20.34 36 -19.68 36 -19 36 C-18.6 34.952 -18.6 34.952 -18.191 33.883 C-16.915 30.794 -15.456 27.852 -13.938 24.875 C-12.427 21.913 -11.052 19.155 -10 16 C-9.484 15.505 -8.969 15.01 -8.438 14.5 C-6.654 12.985 -6.654 12.985 -7 10 C-6 9 -5 8 -4 7 C-2.68 4.69 -1.36 2.38 0 0 Z " fill="#4352B9" transform="translate(535,189)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 42.24 1 84.48 1 128 C8.59 128 16.18 128 24 128 C24 101.27 24 74.54 24 47 C26.747 50.663 29.168 54.42 31.562 58.312 C35.326 64.383 39.198 70.362 43.182 76.289 C46.299 80.931 49.349 85.611 52.375 90.312 C57.681 98.549 63.105 106.703 68.557 114.844 C71.043 118.559 73.521 122.28 76 126 C75.67 126.66 75.34 127.32 75 128 C72.188 125.502 70.603 122.689 68.75 119.438 C65.874 114.664 65.874 114.664 61.062 112.312 C60.052 112.209 59.041 112.106 58 112 C58.99 111.67 59.98 111.34 61 111 C60.375 107.948 59.369 105.673 57.742 103.023 C57.056 101.9 57.056 101.9 56.355 100.754 C55.623 99.576 55.623 99.576 54.875 98.375 C54.379 97.573 53.882 96.77 53.371 95.944 C47.722 85.175 47.722 85.175 39 78 C38.918 77.299 38.835 76.597 38.75 75.875 C37.832 72.354 36.052 69.978 34 67 C34 66.34 34 65.68 34 65 C31.925 63.776 31.925 63.776 29 64 C28.34 63.34 27.68 62.68 27 62 C27.008 62.905 27.008 62.905 27.016 63.828 C27.177 85.899 26.602 107.939 26 130 C17.09 130 8.18 130 -1 130 C-1.023 114.379 -1.041 98.758 -1.052 83.137 C-1.057 75.883 -1.064 68.629 -1.075 61.376 C-1.085 55.05 -1.092 48.725 -1.094 42.4 C-1.095 39.053 -1.099 35.707 -1.106 32.36 C-1.113 28.617 -1.114 24.873 -1.114 21.129 C-1.117 20.03 -1.121 18.931 -1.124 17.799 C-1.116 11.781 -0.786 5.967 0 0 Z M27 56 C27 57.32 27 58.64 27 60 C27.66 59.67 28.32 59.34 29 59 C28.67 58.01 28.34 57.02 28 56 C27.67 56 27.34 56 27 56 Z " fill="#1E2AA8" transform="translate(670,367)"/>
<path d="M0 0 C8.91 0 17.82 0 27 0 C27.198 16.637 27.384 33.274 27.547 49.912 C27.623 57.637 27.704 65.362 27.799 73.086 C27.882 79.818 27.954 86.55 28.013 93.282 C28.045 96.847 28.082 100.412 28.132 103.978 C28.187 107.955 28.219 111.932 28.249 115.909 C28.269 117.096 28.289 118.282 28.31 119.504 C28.314 120.585 28.318 121.666 28.323 122.78 C28.338 124.192 28.338 124.192 28.354 125.632 C28 128 28 128 26.645 129.805 C24.193 131.586 22.235 131.339 19.238 131.293 C17.561 131.278 17.561 131.278 15.85 131.264 C14.682 131.239 13.515 131.213 12.312 131.188 C11.134 131.174 9.955 131.16 8.74 131.146 C5.826 131.111 2.913 131.062 0 131 C-1.045 99.963 -0.987 68.984 -0.562 37.938 C-0.543 36.53 -0.543 36.53 -0.524 35.093 C-0.363 23.395 -0.187 11.698 0 0 Z M2 2 C2 43.58 2 85.16 2 128 C9.92 128 17.84 128 26 128 C26 86.42 26 44.84 26 2 C18.08 2 10.16 2 2 2 Z " fill="#2833AD" transform="translate(583,189)"/>
<path d="M0 0 C19.55 -1.626 19.55 -1.626 24.773 1.84 C26.551 4.481 27.767 7.068 29 10 C30.238 12.041 31.509 14.063 32.812 16.062 C33.376 16.982 33.939 17.901 34.52 18.848 C35.822 21.112 35.822 21.112 38 22 C38.95 23.785 39.835 25.604 40.688 27.438 C43.107 32.392 45.78 36.79 49.016 41.242 C51.424 44.589 53.716 48.005 56 51.438 C56.427 52.078 56.854 52.719 57.294 53.38 C60.418 58.082 63.455 62.834 66.43 67.633 C68.145 70.218 70.03 72.604 72 75 C73.918 73.082 73.129 70.239 73.139 67.687 C73.137 66.802 73.135 65.916 73.133 65.004 C73.134 63.648 73.134 63.648 73.136 62.265 C73.136 60.349 73.135 58.433 73.13 56.517 C73.125 53.579 73.13 50.64 73.137 47.701 C73.136 45.844 73.135 43.986 73.133 42.129 C73.136 40.804 73.136 40.804 73.139 39.452 C73.258 35.089 73.258 35.089 72 31 C72.66 31 73.32 31 74 31 C73.964 30.274 73.929 29.548 73.892 28.8 C73.734 25.492 73.586 22.184 73.438 18.875 C73.353 17.162 73.353 17.162 73.268 15.414 C73.219 14.305 73.171 13.197 73.121 12.055 C73.05 10.531 73.05 10.531 72.978 8.976 C73 6 73 6 74 0 C82.58 0 91.16 0 100 0 C100 42.24 100 84.48 100 128 C99.67 128 99.34 128 99 128 C99 86.09 99 44.18 99 1 C91.41 1 83.82 1 76 1 C76 28.72 76 56.44 76 85 C73.682 82.682 72.113 80.845 70.355 78.166 C69.851 77.4 69.346 76.634 68.826 75.845 C68.015 74.603 68.015 74.603 67.188 73.336 C66.619 72.47 66.05 71.605 65.463 70.713 C63.639 67.936 61.819 65.156 60 62.375 C58.806 60.557 57.612 58.739 56.418 56.922 C50.043 47.214 43.708 37.481 37.407 27.725 C35.318 24.49 33.22 21.261 31.117 18.035 C30.571 17.197 30.024 16.358 29.461 15.494 C28.365 13.814 27.269 12.134 26.172 10.455 C25.638 9.636 25.105 8.817 24.555 7.973 C24.085 7.253 23.616 6.534 23.132 5.793 C22 4 22 4 21 2 C14.07 1.67 7.14 1.34 0 1 C0 0.67 0 0.34 0 0 Z M73 76 C73.66 77.32 74.32 78.64 75 80 C74.67 78.68 74.34 77.36 74 76 C73.67 76 73.34 76 73 76 Z " fill="#747BCC" transform="translate(635,190)"/>
<path d="M0 0 C6.268 2.79 11.065 8.491 15 14 C15 14.66 15 15.32 15 16 C15.66 16.33 16.32 16.66 17 17 C26.769 35.586 29.129 56.836 23 77 C17.149 92.231 6.659 105.839 -8.285 112.93 C-10.341 113.959 -10.341 113.959 -11 117 C-12.485 116.505 -12.485 116.505 -14 116 C-17.06 116.728 -17.06 116.728 -20.375 117.875 C-39.353 124.114 -58.987 121.744 -76.863 113.148 C-79 112 -79 112 -82 110 C-82.66 109.67 -83.32 109.34 -84 109 C-84.825 108.154 -85.65 107.309 -86.5 106.438 C-88.657 103.767 -88.657 103.767 -91 104 C-92.046 102.362 -93.04 100.69 -94 99 C-95.92 96.2 -97.109 94.927 -100 93 C-101 92 -101 92 -101.434 90.219 C-102.251 87.016 -103.731 84.189 -105.16 81.219 C-106 79 -106 79 -106 75 C-106.66 74.67 -107.32 74.34 -108 74 C-108.217 72.298 -108.217 72.298 -108.438 70.562 C-108.623 69.387 -108.809 68.211 -109 67 C-109.99 66.34 -110.98 65.68 -112 65 C-111.34 61.04 -110.68 57.08 -110 53 C-110.66 53 -111.32 53 -112 53 C-111.01 50.03 -110.02 47.06 -109 44 C-108.67 44 -108.34 44 -108 44 C-107.944 45.58 -107.944 45.58 -107.887 47.191 C-107.042 67.119 -103.447 86.869 -88.531 101.406 C-82.23 106.839 -75.655 110.779 -68 114 C-67.074 114.414 -66.149 114.828 -65.195 115.254 C-48.657 121.549 -28.91 119.119 -12.848 112.75 C2.704 105.705 12.798 94.217 19.438 78.75 C19.991 77.178 20.514 75.594 21 74 C21.223 73.296 21.446 72.592 21.676 71.867 C26.165 54.338 23.201 34.541 14.707 18.758 C11.248 13.169 7.446 7.921 2.254 3.844 C1.51 3.235 0.766 2.627 0 2 C0 1.34 0 0.68 0 0 Z " fill="#4E5BBE" transform="translate(624,378)"/>
<path d="M0 0 C9.545 7.572 15.961 17.783 17.812 29.938 C18.548 43.889 17.517 55.412 8.812 66.938 C1.164 75.352 -7.685 80.484 -19.129 81.176 C-32.189 81.49 -42.901 79.669 -53.188 70.938 C-63.83 59.958 -65.691 47.395 -65.465 32.75 C-65.049 22.153 -60.566 11.645 -53.27 3.902 C-38.397 -9.647 -16.619 -11.627 0 0 Z M-33.188 -5.062 C-33.188 -4.733 -33.188 -4.402 -33.188 -4.062 C-30.877 -4.062 -28.567 -4.062 -26.188 -4.062 C-26.188 -4.392 -26.188 -4.723 -26.188 -5.062 C-28.498 -5.062 -30.808 -5.062 -33.188 -5.062 Z M-21.188 -5.062 C-21.188 -4.733 -21.188 -4.402 -21.188 -4.062 C-19.207 -4.062 -17.228 -4.062 -15.188 -4.062 C-15.188 -4.392 -15.188 -4.723 -15.188 -5.062 C-17.168 -5.062 -19.147 -5.062 -21.188 -5.062 Z M-25.188 -5.062 C-25.188 -4.733 -25.188 -4.402 -25.188 -4.062 C-27.827 -3.402 -30.467 -2.743 -33.188 -2.062 C-33.188 -2.723 -33.188 -3.382 -33.188 -4.062 C-40.102 -3.531 -46.277 -0.136 -51.25 4.625 C-53.402 6.69 -53.402 6.69 -53.188 8.938 C-54.178 9.267 -55.168 9.598 -56.188 9.938 C-64.103 22.199 -65.378 36.426 -63.047 50.57 C-60.934 58.848 -55.982 67.657 -48.812 72.562 C-47.946 73.016 -47.08 73.47 -46.188 73.938 C-45.197 74.618 -44.207 75.299 -43.188 76 C-34.466 80.908 -22.762 80.752 -13.188 78.938 C-6.784 76.911 -1.201 74.488 3.812 69.938 C3.812 69.278 3.812 68.618 3.812 67.938 C4.472 67.938 5.132 67.938 5.812 67.938 C15.269 55.952 17.195 43.763 15.812 28.938 C14.23 19.599 10.274 11.89 3.812 4.938 C2.822 4.608 1.832 4.277 0.812 3.938 C0.338 3.401 -0.136 2.865 -0.625 2.312 C-1.398 1.632 -1.398 1.632 -2.188 0.938 C-4.35 1.165 -4.35 1.165 -6.188 1.938 C-5.858 0.947 -5.528 -0.043 -5.188 -1.062 C-9.54 -3.408 -9.54 -3.408 -14.25 -3.75 C-14.889 -3.523 -15.529 -3.296 -16.188 -3.062 C-18.187 -3.024 -20.188 -3.017 -22.188 -3.062 C-22.188 -3.723 -22.188 -4.382 -22.188 -5.062 C-23.178 -5.062 -24.168 -5.062 -25.188 -5.062 Z " fill="#4D59BA" transform="translate(454.1875,394.0625)"/>
<path d="M0 0 C10.227 7.354 16.765 17.516 19.098 29.934 C20.656 43.65 18.929 56.19 10.809 67.57 C4.467 75.31 -3.632 80.865 -13.625 82.375 C-26.872 83.5 -37.96 82.824 -48.938 74.688 C-57.732 67.105 -62.644 57.688 -63.869 46.062 C-64.867 31.876 -63.265 19.973 -54.375 8.375 C-50.282 3.767 -46.101 0.21 -40.625 -2.625 C-39.975 -2.963 -39.326 -3.3 -38.656 -3.648 C-26.691 -8.665 -10.816 -6.6 0 0 Z M-43.625 1.375 C-43.625 2.035 -43.625 2.695 -43.625 3.375 C-44.46 3.468 -44.46 3.468 -45.312 3.562 C-48.616 4.723 -50.344 6.777 -52.625 9.375 C-52.625 10.035 -52.625 10.695 -52.625 11.375 C-53.285 11.375 -53.945 11.375 -54.625 11.375 C-62.97 25.468 -64.002 39.651 -60.625 55.375 C-58.852 60.155 -56.44 64.152 -53.625 68.375 C-52.965 68.375 -52.305 68.375 -51.625 68.375 C-51.419 68.952 -51.212 69.53 -51 70.125 C-47.481 75.883 -39.834 78.529 -33.625 80.375 C-29.96 80.936 -26.329 81.234 -22.625 81.375 C-22.625 80.715 -22.625 80.055 -22.625 79.375 C-21.965 79.375 -21.305 79.375 -20.625 79.375 C-20.625 80.035 -20.625 80.695 -20.625 81.375 C-13.785 80.859 -7.986 78.889 -1.625 76.375 C-2.615 76.045 -3.605 75.715 -4.625 75.375 C-4.048 75.251 -3.47 75.127 -2.875 75 C-0.551 74.354 1.324 73.648 3.375 72.375 C3.87 70.89 3.87 70.89 4.375 69.375 C5.708 68.707 7.041 68.041 8.375 67.375 C15.546 59.24 17.888 49.981 17.375 39.375 C16.715 39.375 16.055 39.375 15.375 39.375 C15.375 38.715 15.375 38.055 15.375 37.375 C16.035 37.375 16.695 37.375 17.375 37.375 C17.375 35.725 17.375 34.075 17.375 32.375 C15.89 31.88 15.89 31.88 14.375 31.375 C15.365 30.385 15.365 30.385 16.375 29.375 C16.049 20.292 10.466 11.762 4.539 5.234 C-5.722 -3.582 -17.444 -4.466 -30.304 -3.878 C-35.262 -3.338 -39.453 -1.25 -43.625 1.375 Z " fill="#5964BE" transform="translate(604.625,392.625)"/>
<path d="M0 0 C0 1.65 0 3.3 0 5 C-0.66 5 -1.32 5 -2 5 C-2.66 6.65 -3.32 8.3 -4 10 C-5.093 9.67 -6.186 9.34 -7.312 9 C-10.64 8.098 -12.696 7.633 -16 8.562 C-18 9 -18 9 -21 8 C-23.009 8.276 -25.012 8.602 -27 9 C-27 8.34 -27 7.68 -27 7 C-28.247 8.051 -28.247 8.051 -28.375 10.375 C-28.251 11.241 -28.127 12.107 -28 13 C-25.591 14.862 -25.591 14.862 -23 16 C-23.33 15.01 -23.66 14.02 -24 13 C-23.34 12.67 -22.68 12.34 -22 12 C-21.67 12.99 -21.34 13.98 -21 15 C-18.413 16.111 -18.413 16.111 -15.312 16.938 C-9.397 18.792 -5.292 20.402 -1.125 25.125 C0.556 29.421 0.563 33.447 0 38 C-1.789 42.004 -4.111 45.185 -8.199 46.949 C-18.12 49.893 -26.864 49.677 -36.438 45.5 C-39 44 -39 44 -41 42 C-40.01 39.03 -39.02 36.06 -38 33 C-37.361 33.268 -36.721 33.536 -36.062 33.812 C-35.052 34.204 -34.041 34.596 -33 35 C-32.322 35.264 -31.644 35.529 -30.945 35.801 C-25.73 37.705 -21.554 38.479 -16 38 C-15.01 37.34 -14.02 36.68 -13 36 C-11.515 35.505 -11.515 35.505 -10 35 C-11.522 31.956 -12.408 30.296 -15.5 28.75 C-16.325 28.503 -17.15 28.255 -18 28 C-19.485 27.505 -19.485 27.505 -21 27 C-21.33 27.66 -21.66 28.32 -22 29 C-22.33 28.01 -22.66 27.02 -23 26 C-24.299 25.814 -24.299 25.814 -25.625 25.625 C-27.296 25.316 -27.296 25.316 -29 25 C-30.011 24.814 -31.021 24.629 -32.062 24.438 C-35.852 22.583 -36.975 20.326 -38.922 16.68 C-39.278 16.125 -39.633 15.571 -40 15 C-40.66 15 -41.32 15 -42 15 C-42.125 12.625 -42.125 12.625 -42 10 C-41.34 9.34 -40.68 8.68 -40 8 C-39.415 6.718 -38.855 5.425 -38.312 4.125 C-36.063 -0.517 -33.667 -1.917 -29 -4 C-18.653 -7.104 -9.601 -3.924 0 0 Z M-36.5 3.75 C-38.473 8.025 -38.521 10.313 -38 15 C-36.464 18.977 -34.196 21.026 -30.438 23.027 C-26.486 24.604 -22.374 25.616 -18.243 26.606 C-14.996 27.393 -12.818 28.121 -10 30 C-9.812 33.625 -9.812 33.625 -10 37 C-15.415 40.17 -19.39 40.87 -25.527 39.391 C-29.554 38.232 -33.289 36.962 -37 35 C-37.33 37.31 -37.66 39.62 -38 42 C-36.391 42.959 -36.391 42.959 -34.75 43.938 C-34.147 44.297 -33.543 44.657 -32.922 45.027 C-26.601 48.226 -18.305 47.858 -11.512 46.504 C-6.947 44.982 -4.593 42.97 -2 39 C-1.298 35.832 -1.298 35.832 -1.375 32.5 C-1.341 31.397 -1.308 30.293 -1.273 29.156 C-2.073 25.682 -3.174 24.188 -6.043 22.081 C-9.875 19.964 -14.034 18.962 -18.25 17.875 C-20.05 17.403 -21.847 16.922 -23.641 16.43 C-24.43 16.226 -25.219 16.022 -26.032 15.812 C-28 15 -28 15 -30 12 C-29.918 8.711 -29.438 7.424 -27.062 5.125 C-19.755 2.441 -11.975 5.321 -5 8 C-2.63 5.046 -2.63 5.046 -3 1 C-14.551 -4.093 -27.217 -6.462 -36.5 3.75 Z " fill="#5262C3" transform="translate(358,623)"/>
<path d="M0 0 C7.26 0 14.52 0 22 0 C22 0.33 22 0.66 22 1 C15.4 1.33 8.8 1.66 2 2 C2 43.58 2 85.16 2 128 C9.26 128 16.52 128 24 128 C24 101.6 24 75.2 24 48 C24.33 48 24.66 48 25 48 C25 74.73 25 101.46 25 129 C18.73 129.33 12.46 129.66 6 130 C5.505 130.99 5.505 130.99 5 132 C4.402 131.835 3.804 131.67 3.188 131.5 C0.887 130.889 0.887 130.889 -2 131 C-2 127.37 -2 123.74 -2 120 C-1.67 120 -1.34 120 -1 120 C-1 94.26 -1 68.52 -1 42 C-1.33 42 -1.66 42 -2 42 C-2 34.41 -2 26.82 -2 19 C-1.67 19 -1.34 19 -1 19 C-0.67 12.73 -0.34 6.46 0 0 Z M-1 27 C-1 28.98 -1 30.96 -1 33 C-0.67 33 -0.34 33 0 33 C0 31.02 0 29.04 0 27 C-0.33 27 -0.66 27 -1 27 Z " fill="#6E78CB" transform="translate(634,189)"/>
<path d="M0 0 C3.487 2.85 6.779 5.854 10 9 C9.67 9.66 9.34 10.32 9 11 C8.42 10.434 7.84 9.868 7.242 9.285 C1.565 3.683 1.565 3.683 -6 2 C-5.67 1.34 -5.34 0.68 -5 0 C-6.727 -0.65 -8.457 -1.295 -10.188 -1.938 C-11.15 -2.297 -12.113 -2.657 -13.105 -3.027 C-18.197 -4.738 -22.628 -5.105 -28 -5 C-27.01 -4.67 -26.02 -4.34 -25 -4 C-25.33 -3.34 -25.66 -2.68 -26 -2 C-27.65 -2 -29.3 -2 -31 -2 C-31 -2.66 -31 -3.32 -31 -4 C-37.658 -5.929 -43.084 -3.059 -49 0 C-58.98 7.078 -64.137 15.239 -67 27 C-68.035 39.154 -67.539 52.559 -59.465 62.449 C-53.864 68.379 -48.197 73.26 -40 75 C-37.28 74.743 -35.54 74.104 -33 73 C-33.33 73.66 -33.66 74.32 -34 75 C-34.66 75 -35.32 75 -36 75 C-36 75.66 -36 76.32 -36 77 C-28.039 77.144 -19.876 77.277 -12 76 C-11.67 75.34 -11.34 74.68 -11 74 C-10.01 74.495 -10.01 74.495 -9 75 C-8.67 74.34 -8.34 73.68 -8 73 C-7.154 72.752 -6.309 72.505 -5.438 72.25 C1.291 69.803 7.042 64.936 11 59 C12.98 59.495 12.98 59.495 15 60 C14.505 61.485 14.505 61.485 14 63 C13.257 63.289 12.515 63.577 11.75 63.875 C8.861 64.843 8.861 64.843 6.812 67.25 C-3.116 76.958 -14.598 79.312 -27.965 79.312 C-40.033 79.098 -50.181 75.425 -59 67 C-68.051 55.761 -70.93 43.208 -70 29 C-68.779 20.711 -66.096 14.599 -61 8 C-60.134 6.701 -60.134 6.701 -59.25 5.375 C-43.518 -11.231 -18.79 -12.219 0 0 Z " fill="#525FBF" transform="translate(241,218)"/>
<path d="M0 0 C2.225 1.52 3.352 2.803 5 5 C5 5.66 5 6.32 5 7 C7.475 7.495 7.475 7.495 10 8 C9.34 8.33 8.68 8.66 8 9 C6.845 12.192 6.884 15.274 6.902 18.641 C6.906 19.711 6.909 20.78 6.912 21.883 C6.92 22.994 6.929 24.105 6.938 25.25 C6.942 26.377 6.947 27.503 6.951 28.664 C6.963 31.443 6.979 34.221 7 37 C2.265 39.208 -0.062 38.743 -5 37 C-4.99 36.38 -4.979 35.759 -4.968 35.12 C-4.927 32.309 -4.901 29.498 -4.875 26.688 C-4.858 25.711 -4.841 24.735 -4.824 23.729 C-4.818 22.791 -4.811 21.853 -4.805 20.887 C-4.794 20.023 -4.784 19.159 -4.773 18.268 C-4.849 15.718 -4.849 15.718 -7 13 C-7 11.68 -7 10.36 -7 9 C-8.258 8.938 -9.516 8.876 -10.812 8.812 C-11.52 8.778 -12.228 8.743 -12.957 8.707 C-15.676 9.097 -16.348 9.817 -18 12 C-18.506 14.803 -18.506 14.803 -18.512 17.98 C-18.547 19.144 -18.583 20.307 -18.619 21.506 C-18.642 22.721 -18.664 23.936 -18.688 25.188 C-18.722 26.414 -18.756 27.641 -18.791 28.904 C-18.874 31.936 -18.943 34.968 -19 38 C-22.019 38.805 -24.619 39.109 -27.75 39.062 C-28.549 39.053 -29.348 39.044 -30.172 39.035 C-30.775 39.024 -31.378 39.012 -32 39 C-33.097 35.71 -32.8 34.287 -32 31 C-31.67 31 -31.34 31 -31 31 C-30.67 20.44 -30.34 9.88 -30 -1 C-26.7 -1 -23.4 -1 -20 -1 C-20 -0.34 -20 0.32 -20 1 C-19.394 0.76 -18.788 0.52 -18.164 0.273 C-11.661 -2.028 -6.517 -2.584 0 0 Z M-20 4 C-20.33 3.01 -20.66 2.02 -21 1 C-23.31 1 -25.62 1 -28 1 C-28 12.55 -28 24.1 -28 36 C-25.69 36 -23.38 36 -21 36 C-20.98 35.324 -20.96 34.648 -20.94 33.952 C-20.845 30.884 -20.735 27.817 -20.625 24.75 C-20.594 23.687 -20.563 22.623 -20.531 21.527 C-20.473 19.99 -20.473 19.99 -20.414 18.422 C-20.383 17.479 -20.351 16.537 -20.319 15.565 C-19.99 12.919 -19.403 11.252 -18 9 C-14.78 6.7 -11.874 6.447 -8 7 C-5.5 8.664 -4.44 9.458 -3 12 C-2.238 16.099 -2.209 20.152 -2.188 24.312 C-2.162 25.435 -2.137 26.557 -2.111 27.713 C-2.053 30.476 -2.016 33.237 -2 36 C0.31 36 2.62 36 5 36 C5.087 31.604 5.14 27.209 5.188 22.812 C5.213 21.567 5.238 20.321 5.264 19.037 C5.278 17.234 5.278 17.234 5.293 15.395 C5.317 13.737 5.317 13.737 5.341 12.046 C4.821 7.401 2.569 3.974 -1 1 C-7.388 -1.129 -14.93 -1.07 -20 4 Z " fill="#5D68C6" transform="translate(604,633)"/>
<path d="M0 0 C1.625 -0.054 3.25 -0.093 4.875 -0.125 C6.232 -0.16 6.232 -0.16 7.617 -0.195 C10 0 10 0 12 2 C11.34 2 10.68 2 10 2 C10.165 2.825 10.33 3.65 10.5 4.5 C11.209 9.463 11.607 14.178 10 19 C10.13 24.514 10.13 24.514 13 29 C15.85 30.222 17.116 29.97 20.188 29.188 C23.116 28.31 23.116 28.31 24 26 C24.142 24.127 24.226 22.249 24.281 20.371 C24.319 19.254 24.356 18.137 24.395 16.986 C24.465 14.617 24.535 12.247 24.605 9.877 C24.643 8.761 24.68 7.646 24.719 6.496 C24.749 5.467 24.779 4.437 24.811 3.377 C25 1 25 1 26 0 C28.97 0 31.94 0 35 0 C35.33 12.21 35.66 24.42 36 37 C36.99 37.495 36.99 37.495 38 38 C37.505 38.99 37.505 38.99 37 40 C35.396 40.054 33.792 40.093 32.188 40.125 C31.294 40.148 30.401 40.171 29.48 40.195 C27 40 27 40 24 38 C22.339 38.498 20.68 39.001 19.031 39.539 C14.304 40.612 9.65 39.623 5.188 37.938 C1.135 34.348 -0.946 29.259 -2 24 C-2 21.667 -2 19.333 -2 17 C-2.195 15.665 -2.403 14.331 -2.625 13 C-3.288 8.265 -2.13 4.259 0 0 Z M1 2 C0.913 6.375 0.859 10.75 0.812 15.125 C0.787 16.362 0.762 17.6 0.736 18.875 C0.727 20.074 0.717 21.273 0.707 22.508 C0.691 23.608 0.676 24.707 0.659 25.84 C1.151 30.402 2.657 33.779 6.062 36.875 C10.998 38.765 16.242 39.13 21.203 37.098 C23.329 35.921 23.329 35.921 26 34 C26.33 34.99 26.66 35.98 27 37 C29.31 37 31.62 37 34 37 C33.505 19.675 33.505 19.675 33 2 C31.02 2 29.04 2 27 2 C26.985 2.671 26.971 3.341 26.956 4.032 C26.881 7.084 26.785 10.136 26.688 13.188 C26.665 14.243 26.642 15.298 26.619 16.385 C26.584 17.406 26.548 18.428 26.512 19.48 C26.486 20.418 26.459 21.355 26.432 22.321 C25.923 25.48 24.858 27.414 23 30 C19.857 31.572 16.442 31.232 13 31 C12.01 30.67 11.02 30.34 10 30 C9.212 26.465 8.825 23.098 8.684 19.48 C8.621 17.948 8.621 17.948 8.557 16.385 C8.517 15.33 8.478 14.275 8.438 13.188 C8.394 12.113 8.351 11.039 8.307 9.932 C8.201 7.288 8.099 4.644 8 2 C5.69 2 3.38 2 1 2 Z " fill="#5B66C6" transform="translate(530,632)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C14.667 6 17.333 12 20 18 C21.65 17.67 23.3 17.34 25 17 C25.182 16.359 25.364 15.719 25.551 15.059 C27.063 9.856 28.75 4.929 31 0 C34.3 0 37.6 0 41 0 C40.451 5.152 39.215 9.532 37.188 14.312 C36.921 14.964 36.654 15.616 36.378 16.287 C35.184 19.198 33.959 22.094 32.715 24.984 C30.78 29.561 29.62 32.935 30 38 C29.67 38.66 29.34 39.32 29 40 C27.515 40.495 27.515 40.495 26 41 C24.538 42.995 24.538 42.995 23.188 45.375 C20.607 49.453 18.509 51.112 14 53 C13.67 53.99 13.34 54.98 13 56 C12.031 55.67 11.061 55.34 10.062 55 C7.379 53.948 7.379 53.948 6 54 C3 52.5 3 52.5 0 51 C0.33 50.01 0.66 49.02 1 48 C1.165 47.175 1.33 46.35 1.5 45.5 C2 43 2 43 3 40 C5 41 5 41 6 42 C9.458 42.25 9.458 42.25 13 42 C15.411 40.044 15.411 40.044 15.125 36.375 C15.084 35.261 15.043 34.148 15 33 C13.02 32.34 11.04 31.68 9 31 C9.33 30.34 9.66 29.68 10 29 C10.99 29.33 11.98 29.66 13 30 C12.639 29.157 12.278 28.314 11.906 27.445 C11.442 26.35 10.978 25.254 10.5 24.125 C9.804 22.489 9.804 22.489 9.094 20.82 C8 18 8 18 8 16 C7.34 15.67 6.68 15.34 6 15 C5.148 12.934 5.148 12.934 4.375 10.438 C4.115 9.611 3.854 8.785 3.586 7.934 C3.393 7.296 3.199 6.657 3 6 C2.67 6.66 2.34 7.32 2 8 C1.34 7.67 0.68 7.34 0 7 C0.33 6.01 0.66 5.02 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z M4 2 C5.633 7.161 7.44 12.172 9.516 17.172 C9.962 18.248 10.408 19.325 10.867 20.434 C11.797 22.663 12.735 24.89 13.68 27.113 C14.121 28.184 14.561 29.256 15.016 30.359 C15.421 31.323 15.826 32.287 16.243 33.28 C17.094 36.338 16.987 38.005 16 41 C13.411 43.589 11.973 43.374 8.375 43.625 C7.372 43.7 6.369 43.775 5.336 43.852 C4.565 43.901 3.794 43.95 3 44 C3 45.65 3 47.3 3 49 C7.051 51.222 10.275 51.071 14.719 50.68 C18.418 49.577 20.973 47.689 23 44.356 C23.997 42.327 24.885 40.276 25.75 38.188 C26.08 37.417 26.41 36.647 26.75 35.853 C27.856 33.244 28.932 30.625 30 28 C30.409 26.995 30.819 25.991 31.24 24.956 C32.506 21.83 33.757 18.698 35 15.562 C35.398 14.562 35.796 13.561 36.206 12.53 C36.571 11.593 36.936 10.657 37.312 9.691 C37.802 8.441 37.802 8.441 38.301 7.166 C39.154 4.904 39.154 4.904 39 2 C36.69 2 34.38 2 32 2 C30.788 4.891 29.58 7.784 28.379 10.68 C27.519 12.75 26.653 14.819 25.777 16.883 C25.418 17.746 25.058 18.61 24.688 19.5 C24.361 20.273 24.035 21.047 23.699 21.844 C22.794 24.256 22.794 24.256 23 28 C22.34 28 21.68 28 21 28 C20.853 27.464 20.706 26.928 20.555 26.375 C18.178 18.072 15.089 10.058 12 2 C9.36 2 6.72 2 4 2 Z M20 19 C20.33 20.65 20.66 22.3 21 24 C21.66 23.01 22.32 22.02 23 21 C22.34 21 21.68 21 21 21 C20.67 20.34 20.34 19.68 20 19 Z M28 34 C29 36 29 36 29 36 Z M27 37 C28 39 28 39 28 39 Z " fill="#5662C2" transform="translate(432,632)"/>
<path d="M0 0 C3 0 3 0 6 2 C6.961 4.883 7.117 6.677 7.098 9.668 C7.094 10.561 7.091 11.453 7.088 12.373 C7.075 13.766 7.075 13.766 7.062 15.188 C7.058 16.128 7.053 17.068 7.049 18.037 C7.037 20.358 7.021 22.679 7 25 C7.66 25.33 8.32 25.66 9 26 C8.505 26.495 8.505 26.495 8 27 C8.495 27.99 8.495 27.99 9 29 C8.34 29 7.68 29 7 29 C7.495 30.485 7.495 30.485 8 32 C7.676 33.336 7.343 34.669 7 36 C7.33 36.66 7.66 37.32 8 38 C8.07 39.707 8.084 41.417 8.062 43.125 C8.053 44.035 8.044 44.945 8.035 45.883 C8.024 46.581 8.012 47.28 8 48 C8.33 48 8.66 48 9 48 C9.66 50.64 10.32 53.28 11 56 C9.68 55.67 8.36 55.34 7 55 C5.391 55.247 5.391 55.247 3.75 55.5 C0.65 55.913 -1.308 55.936 -4.312 55.5 C-7.815 54.673 -7.815 54.673 -10 56 C-15.351 56.282 -19.926 55.74 -25.117 54.438 C-27.003 53.925 -27.003 53.925 -29 54 C-29.268 52.989 -29.536 51.979 -29.812 50.938 C-30.894 47.127 -32.379 43.611 -34 40 C-35.65 40 -37.3 40 -39 40 C-38.258 39.412 -38.258 39.412 -37.5 38.812 C-35.544 36.449 -35.758 34.986 -36 32 C-35.34 32 -34.68 32 -34 32 C-33.709 31.336 -33.417 30.672 -33.117 29.988 C-30.03 23.337 -27.413 19.241 -20.738 15.852 C-17.334 14.793 -14.724 15.018 -11.188 15.375 C-10.026 15.486 -8.865 15.597 -7.668 15.711 C-6.788 15.806 -5.907 15.902 -5 16 C-5.179 15.251 -5.358 14.502 -5.543 13.73 C-6.17 9.982 -5.389 7.505 -4 4 C-1.875 2.562 -1.875 2.562 0 2 C0 1.34 0 0.68 0 0 Z M-2 5 C-2.33 10.28 -2.66 15.56 -3 21 C-4.176 20.34 -5.351 19.68 -6.562 19 C-11.987 16.316 -15.904 16.325 -21.688 18.188 C-26.34 20.733 -29.026 24.12 -31 29 C-32.048 38.153 -32.048 38.153 -29.25 46.625 C-28.508 47.409 -27.765 48.192 -27 49 C-26.319 49.763 -25.639 50.526 -24.938 51.312 C-21.051 54.697 -16.647 54.32 -11.742 54.301 C-7.816 53.87 -5.95 52.552 -3 50 C-2.67 50.99 -2.34 51.98 -2 53 C0.31 53 2.62 53 5 53 C5 37.16 5 21.32 5 5 C2.69 5 0.38 5 -2 5 Z M-5 16 C-5.33 16.66 -5.66 17.32 -6 18 C-5.34 18 -4.68 18 -4 18 C-4.33 17.34 -4.66 16.68 -5 16 Z M-28 50 C-27 52 -27 52 -27 52 Z " fill="#5B65C2" transform="translate(693,616)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C1.052 2.062 2.104 2.124 3.188 2.188 C7.675 3.144 9.071 4.569 12 8 C12.66 8.66 13.32 9.32 14 10 C14.414 12.984 14.414 12.984 14.625 16.75 C14.682 17.739 14.682 17.739 14.74 18.748 C15.094 25.498 15.094 32.242 15 39 C15.33 38.67 15.66 38.34 16 38 C16.33 38.99 16.66 39.98 17 41 C12.039 42.203 7.27 42.464 2.25 41.438 C-0.155 40.779 -0.155 40.779 -3 42 C-11.217 42.261 -11.217 42.261 -15 41 C-15 40.34 -15 39.68 -15 39 C-16.485 39.495 -16.485 39.495 -18 40 C-18.66 39.34 -19.32 38.68 -20 38 C-20 37.01 -20 36.02 -20 35 C-20.306 33.266 -20.642 31.537 -21 29.812 C-21.186 28.911 -21.371 28.01 -21.562 27.082 C-21.707 26.395 -21.851 25.708 -22 25 C-21.34 25 -20.68 25 -20 25 C-19.732 24.464 -19.464 23.928 -19.188 23.375 C-18 21 -18 21 -16.188 18.062 C-15.796 17.052 -15.404 16.041 -15 15 C-16.156 12.268 -16.156 12.268 -18 10 C-18.33 9.01 -18.66 8.02 -19 7 C-14.588 1.891 -6.55 0 0 0 Z M-16 6 C-16 7.65 -16 9.3 -16 11 C-12.036 12.321 -9.972 11.144 -6 10 C-1.874 9.67 1.438 9.761 5 12 C5.637 15.086 5.637 15.086 6 18 C5.082 17.965 4.164 17.93 3.219 17.895 C-8.144 17.551 -8.144 17.551 -18 22.625 C-19.593 26.409 -19.782 29.974 -19 34 C-16.47 37.282 -14.923 38.692 -11 40 C-6.081 40.516 -1.601 40.854 2.625 38 C3.409 37.34 4.192 36.68 5 36 C5.33 36.99 5.66 37.98 6 39 C8.31 39 10.62 39 13 39 C13.117 34.625 13.187 30.251 13.25 25.875 C13.284 24.638 13.317 23.4 13.352 22.125 C13.555 13.218 13.555 13.218 9 6 C1.437 0.466 -8.033 2.393 -16 6 Z M-4 11 C-4.99 12.485 -4.99 12.485 -6 14 C-5.67 14.66 -5.34 15.32 -5 16 C-3.35 16 -1.7 16 0 16 C-0.33 15.34 -0.66 14.68 -1 14 C-0.67 13.01 -0.34 12.02 0 11 C-1.32 11 -2.64 11 -4 11 Z M-8 11 C-9.65 11.66 -11.3 12.32 -13 13 C-12.01 13.495 -12.01 13.495 -11 14 C-10.67 14.66 -10.34 15.32 -10 16 C-9.01 15.01 -8.02 14.02 -7 13 C-7.33 12.34 -7.66 11.68 -8 11 Z M3 14 C4 16 4 16 4 16 Z " fill="#5461C5" transform="translate(415,630)"/>
<path d="M0 0 C2.692 3.365 4.643 5.897 6.594 9.562 C7.061 10.432 7.528 11.301 8.009 12.196 C8.501 13.122 8.993 14.047 9.5 15 C13.702 22.803 17.971 30.559 22.375 38.25 C22.85 39.081 23.325 39.913 23.814 40.77 C26.752 45.888 29.777 50.943 32.885 55.96 C34 58 34 58 34 60 C37.902 60.314 41.253 60.196 45 59 C48.114 55.715 49.811 51.9 51.688 47.812 C52.785 45.606 53.884 43.4 54.984 41.195 C55.501 40.126 56.017 39.056 56.549 37.954 C58.359 34.268 60.351 30.722 62.438 27.188 C65.72 21.508 68.889 15.775 72 10 C73.013 12.622 73.133 13.683 72.028 16.318 C71.526 17.152 71.025 17.985 70.508 18.844 C69.698 20.219 69.698 20.219 68.871 21.621 C68.295 22.571 67.719 23.521 67.125 24.5 C65.998 26.383 64.876 28.268 63.758 30.156 C63.002 31.406 63.002 31.406 62.231 32.682 C61.095 34.82 60.457 36.635 60 39 C59.34 39 58.68 39 58 39 C57.763 39.656 57.526 40.312 57.281 40.988 C55.623 44.886 53.586 48.506 51.5 52.188 C51.097 52.911 50.693 53.635 50.277 54.381 C49.887 55.074 49.496 55.767 49.094 56.48 C48.74 57.108 48.386 57.736 48.021 58.384 C47 60 47 60 45 62 C42.18 62.195 42.18 62.195 38.875 62.125 C37.779 62.107 36.684 62.089 35.555 62.07 C34.712 62.047 33.869 62.024 33 62 C29 55.25 29 55.25 29 53 C28.34 53 27.68 53 27 53 C26.446 51.868 25.891 50.736 25.32 49.57 C24.589 48.088 23.857 46.607 23.125 45.125 C22.76 44.379 22.395 43.632 22.02 42.863 C21.665 42.148 21.311 41.432 20.945 40.695 C20.621 40.035 20.296 39.376 19.961 38.696 C18.661 36.402 17.356 35.178 15 34 C14.875 33.123 14.75 32.247 14.621 31.344 C13.875 27.328 12.302 24.901 10.062 21.5 C7.366 17.389 5.362 13.736 4 9 C3.34 9 2.68 9 2 9 C2.002 9.616 2.004 10.232 2.007 10.866 C2.029 17.357 2.044 23.847 2.055 30.338 C2.06 32.751 2.067 35.163 2.075 37.576 C2.122 50.757 2.116 63.859 1 77 C0.67 77 0.34 77 0 77 C0 51.59 0 26.18 0 0 Z " fill="#4C59BD" transform="translate(459,235)"/>
<path d="M0 0 C2 2 2 2 2.125 5.625 C2.084 6.739 2.043 7.852 2 9 C2.638 9.061 3.276 9.121 3.934 9.184 C4.76 9.267 5.586 9.351 6.438 9.438 C7.673 9.559 7.673 9.559 8.934 9.684 C11 10 11 10 12 11 C12.072 12.853 12.084 14.708 12.062 16.562 C12.053 17.574 12.044 18.586 12.035 19.629 C12.024 20.411 12.012 21.194 12 22 C10.68 21.34 9.36 20.68 8 20 C8.33 20.99 8.66 21.98 9 23 C6.063 22.371 3.662 21.376 1 20 C1.142 22.229 1.289 24.459 1.438 26.688 C1.519 27.929 1.6 29.17 1.684 30.449 C1.945 33.382 2.348 36.134 3 39 C4.98 38.01 6.96 37.02 9 36 C9.33 36.66 9.66 37.32 10 38 C11.979 38.727 13.98 39.398 16 40 C15.01 40.495 15.01 40.495 14 41 C13.593 43.322 13.256 45.657 13 48 C7.609 50.331 1.798 50.881 -4 50 C-4.66 49.01 -5.32 48.02 -6 47 C-6.99 47.99 -7.98 48.98 -9 50 C-9 46.7 -9 43.4 -9 40 C-9.66 39.67 -10.32 39.34 -11 39 C-11 38.34 -11 37.68 -11 37 C-9.515 36.505 -9.515 36.505 -8 36 C-7.608 37.258 -7.216 38.516 -6.812 39.812 C-5.765 42.515 -5.079 43.921 -3 46 C1.621 47.54 6.174 47.228 11 47 C10.67 45.35 10.34 43.7 10 42 C8.907 42.062 7.814 42.124 6.688 42.188 C3 42 3 42 1.125 40.812 C-1.684 36.287 -1.122 30.835 -1.062 25.688 C-1.058 24.947 -1.053 24.206 -1.049 23.443 C-1.037 21.629 -1.019 19.814 -1 18 C2.63 18 6.26 18 10 18 C10 16.02 10 14.04 10 12 C6.7 12 3.4 12 0 12 C0 9.03 0 6.06 0 3 C-2.31 3.33 -4.62 3.66 -7 4 C-7.33 6.64 -7.66 9.28 -8 12 C-9.65 12 -11.3 12 -13 12 C-13 13.98 -13 15.96 -13 18 C-11.35 18 -9.7 18 -8 18 C-8 23.61 -8 29.22 -8 35 C-10.679 30.981 -10.156 28.758 -10 24 C-11.32 24.33 -12.64 24.66 -14 25 C-14.217 23.886 -14.217 23.886 -14.438 22.75 C-14.912 20.105 -14.912 20.105 -15.688 18.062 C-16.145 15.041 -15.083 12.802 -14 10 C-12.68 10 -11.36 10 -10 10 C-9.67 7.03 -9.34 4.06 -9 1 C-2.164 0.023 -2.164 0.023 0 0 Z " fill="#7A83D6" transform="translate(377,622)"/>
<path d="M0 0 C7.267 -0.068 14.534 -0.123 21.801 -0.155 C25.175 -0.171 28.549 -0.192 31.923 -0.226 C35.801 -0.263 39.677 -0.28 43.555 -0.293 C44.769 -0.308 45.984 -0.324 47.235 -0.34 C48.917 -0.34 48.917 -0.34 50.632 -0.341 C51.623 -0.347 52.613 -0.354 53.634 -0.361 C54.414 -0.242 55.195 -0.123 56 0 C56.99 1.485 56.99 1.485 58 3 C57.34 3 56.68 3 56 3 C56.052 4.58 56.052 4.58 56.105 6.191 C56.522 24.027 53.519 42.357 41 56 C40.34 56 39.68 56 39 56 C38.773 56.579 38.546 57.158 38.312 57.754 C33.226 66.459 19.896 72.116 10.48 74.605 C4.032 76.158 -2.134 76.54 -8.75 76.5 C-9.885 76.494 -11.021 76.487 -12.19 76.48 C-27.21 76.156 -38.755 71.557 -51 63 C-51.779 62.466 -52.557 61.933 -53.359 61.383 C-53.901 60.926 -54.442 60.47 -55 60 C-55 59.34 -55 58.68 -55 58 C-51.801 59.5 -48.984 61.331 -46.062 63.312 C-29.044 74.175 -9.536 76.4 9.996 72.105 C19.312 69.747 27.711 65.047 34.547 58.285 C36 57 36 57 38 57 C38.272 56.423 38.544 55.845 38.824 55.25 C39.998 53.003 41.336 51.058 42.812 49 C52.814 34.108 53.143 18.989 54 1 C36.84 1 19.68 1 2 1 C2 5.62 2 10.24 2 15 C1.67 15 1.34 15 1 15 C0.505 7.575 0.505 7.575 0 0 Z " fill="#363FAF" transform="translate(828,245)"/>
<path d="M0 0 C1 1 1 1 1.098 3.066 C1.065 5.378 1.033 7.689 1 10 C4.3 9.67 7.6 9.34 11 9 C12.603 12.118 13.19 13.39 12.125 16.812 C11.754 17.534 11.383 18.256 11 19 C9.36 19.217 9.36 19.217 7.688 19.438 C4.069 19.883 4.069 19.883 1 21 C1.114 23.084 1.242 25.167 1.375 27.25 C1.445 28.41 1.514 29.57 1.586 30.766 C1.962 33.707 2.477 35.499 4 38 C7.465 37.505 7.465 37.505 11 37 C11.33 37.66 11.66 38.32 12 39 C12.99 39 13.98 39 15 39 C15.99 39 16.98 39 18 39 C18 39.66 18 40.32 18 41 C17.01 41.66 16.02 42.32 15 43 C14.67 43.66 14.34 44.32 14 45 C13.34 45 12.68 45 12 45 C12 45.66 12 46.32 12 47 C6.576 48.618 2.62 49.485 -3 48 C-5.642 46.18 -7.739 44.261 -10 42 C-9.505 41.505 -9.505 41.505 -9 41 C-8.959 39.334 -8.957 37.666 -9 36 C-9.66 35.67 -10.32 35.34 -11 35 C-10.67 33.68 -10.34 32.36 -10 31 C-10.66 30.67 -11.32 30.34 -12 30 C-11.34 28.02 -10.68 26.04 -10 24 C-10.66 24 -11.32 24 -12 24 C-12.66 22.68 -13.32 21.36 -14 20 C-12.68 20.33 -11.36 20.66 -10 21 C-11.641 19.359 -13.778 19.399 -16 19 C-17.231 15.306 -16.644 12.794 -16 9 C-14.02 9 -12.04 9 -10 9 C-10 6.36 -10 3.72 -10 1 C-6.747 -0.627 -3.608 -0.115 0 0 Z M-8 3 C-8 5.64 -8 8.28 -8 11 C-9.98 11 -11.96 11 -14 11 C-14 12.98 -14 14.96 -14 17 C-12.02 17 -10.04 17 -8 17 C-8.016 17.569 -8.031 18.138 -8.048 18.725 C-8.11 21.337 -8.149 23.95 -8.188 26.562 C-8.213 27.458 -8.238 28.353 -8.264 29.275 C-8.335 35.809 -7.902 41.013 -3.133 45.922 C-0.538 47.234 1.229 47.336 4.125 47.25 C4.994 47.235 5.863 47.219 6.758 47.203 C9.036 47.134 9.036 47.134 11 46 C11 44.35 11 42.7 11 41 C8.043 39.522 5.258 39.94 2 40 C0.333 37.082 -0.243 35.024 -0.195 31.672 C-0.189 30.873 -0.182 30.073 -0.176 29.25 C-0.159 28.425 -0.142 27.6 -0.125 26.75 C-0.116 25.91 -0.107 25.069 -0.098 24.203 C-0.074 22.135 -0.038 20.068 0 18 C3.3 17.67 6.6 17.34 10 17 C10 15.02 10 13.04 10 11 C6.37 11 2.74 11 -1 11 C-1 8.36 -1 5.72 -1 3 C-3.31 3 -5.62 3 -8 3 Z " fill="#6B74CD" transform="translate(511,623)"/>
<path d="M0 0 C1.263 0.92 2.518 1.851 3.75 2.812 C3.09 3.143 2.43 3.472 1.75 3.812 C-0.548 2.621 -2.682 1.236 -4.863 -0.156 C-7.801 -1.426 -9.206 -0.951 -12.25 -0.188 C-15.062 -1.125 -15.062 -1.125 -17.25 -2.188 C-17.25 -2.518 -17.25 -2.847 -17.25 -3.188 C-18.24 -3.188 -19.23 -3.188 -20.25 -3.188 C-20.25 -2.197 -20.25 -1.208 -20.25 -0.188 C-20.745 -1.178 -20.745 -1.178 -21.25 -2.188 C-22.57 -2.188 -23.89 -2.188 -25.25 -2.188 C-25.58 -1.528 -25.91 -0.868 -26.25 -0.188 C-26.807 -0.332 -27.364 -0.476 -27.938 -0.625 C-28.701 -0.811 -29.464 -0.996 -30.25 -1.188 C-31.404 -1.518 -31.404 -1.518 -32.582 -1.855 C-37.427 -2.458 -41.228 0.333 -45.25 2.812 C-48.084 5.146 -48.084 5.146 -50.25 7.812 C-51.137 8.782 -52.024 9.751 -52.938 10.75 C-55.672 14.372 -56.724 17.578 -57.75 21.938 C-58.25 23.812 -58.25 23.812 -59.262 25.667 C-60.934 29.298 -60.589 32.998 -60.562 36.938 C-60.557 37.793 -60.551 38.649 -60.545 39.531 C-60.345 49.936 -57.71 58.49 -51.25 66.812 C-46.021 72.028 -46.021 72.028 -39.25 73.812 C-36.25 75.812 -36.25 75.812 -34.25 78.812 C-42.205 78.405 -48.144 73.237 -53.5 67.75 C-62.588 57.066 -64.014 43.386 -63.425 29.925 C-62.537 19.194 -56.651 9.596 -48.812 2.438 C-34.992 -8.508 -14.831 -9.802 0 0 Z " fill="#5F6AC4" transform="translate(369.25,216.1875)"/>
<path d="M0 0 C2.413 3.62 2.154 5.094 2 9.375 C1.795 26.026 5.21 43.332 16.785 55.945 C18.178 57.309 19.587 58.656 21 60 C21 60.66 21 61.32 21 62 C21.536 62.25 22.072 62.5 22.625 62.758 C25.544 64.285 28.237 66.08 31 67.875 C46.021 77.366 64.43 79.537 81.793 76.211 C86.836 75.057 91.603 73.745 96.188 71.301 C99 70 99 70 101.375 70.312 C101.911 70.539 102.447 70.766 103 71 C96.156 75.103 89.373 77.087 81.627 78.655 C79.739 79.048 77.867 79.518 76 80 C75.67 80.66 75.34 81.32 75 82 C71.937 82.625 71.937 82.625 69 83 C69 82.01 69 81.02 69 80 C68.392 80.035 67.783 80.07 67.156 80.105 C61.065 80.294 55.348 79.721 49.375 78.625 C48.309 78.436 48.309 78.436 47.221 78.243 C45.465 77.901 43.729 77.459 42 77 C41.67 76.34 41.34 75.68 41 75 C39.948 74.711 38.896 74.423 37.812 74.125 C33.891 72.968 31.349 71.283 28 69 C27.01 69 26.02 69 25 69 C24.752 68.361 24.505 67.721 24.25 67.062 C23.837 66.382 23.425 65.701 23 65 C22.375 64.878 21.75 64.755 21.105 64.629 C18.215 63.766 17.617 62.416 16.062 59.875 C15.552 59.068 15.042 58.261 14.516 57.43 C13.765 56.227 13.765 56.227 13 55 C9.933 49.921 9.933 49.921 5 47 C5.33 46.01 5.66 45.02 6 44 C5.305 41.931 4.519 39.893 3.688 37.875 C0.96 30.872 0.03 24.067 -0.641 16.617 C-0.894 13.172 -0.894 13.172 -1.609 10.57 C-1.738 10.052 -1.867 9.534 -2 9 C-1.67 8.67 -1.34 8.34 -1 8 C-0.769 6.653 -0.588 5.296 -0.438 3.938 C-0.293 2.638 -0.149 1.339 0 0 Z " fill="#5763C2" transform="translate(363,419)"/>
<path d="M0 0 C8.91 0 17.82 0 27 0 C27 0.33 27 0.66 27 1 C18.42 1 9.84 1 1 1 C1.33 42.91 1.66 84.82 2 128 C9.92 128 17.84 128 26 128 C26 123.71 26 119.42 26 115 C26.66 115 27.32 115 28 115 C28.081 117.104 28.139 119.208 28.188 121.312 C28.222 122.484 28.257 123.656 28.293 124.863 C28 128 28 128 26.842 129.678 C24.25 131.538 22.406 131.341 19.238 131.293 C17.561 131.278 17.561 131.278 15.85 131.264 C14.682 131.239 13.515 131.213 12.312 131.188 C11.134 131.174 9.955 131.16 8.74 131.146 C5.826 131.111 2.913 131.062 0 131 C-1.045 99.963 -0.987 68.984 -0.562 37.938 C-0.543 36.53 -0.543 36.53 -0.524 35.093 C-0.363 23.395 -0.187 11.698 0 0 Z " fill="#606DCD" transform="translate(583,189)"/>
<path d="M0 0 C5.284 2.114 5.945 2.756 8.82 7.285 C9.515 8.365 10.209 9.444 10.925 10.557 C11.659 11.725 12.392 12.894 13.125 14.062 C13.888 15.257 14.652 16.45 15.417 17.643 C16.933 20.009 18.442 22.379 19.944 24.754 C22.541 28.853 25.173 32.929 27.812 37 C28.236 37.657 28.66 38.314 29.097 38.99 C33.837 46.328 38.696 53.586 43.557 60.844 C46.043 64.559 48.521 68.28 51 72 C50.67 72.66 50.34 73.32 50 74 C47.188 71.502 45.603 68.689 43.75 65.438 C40.874 60.664 40.874 60.664 36.062 58.312 C34.547 58.158 34.547 58.158 33 58 C33.99 57.67 34.98 57.34 36 57 C35.375 53.948 34.369 51.673 32.742 49.023 C32.285 48.274 31.827 47.525 31.355 46.754 C30.867 45.969 30.378 45.184 29.875 44.375 C29.379 43.573 28.882 42.77 28.371 41.944 C22.722 31.175 22.722 31.175 14 24 C13.918 23.299 13.835 22.598 13.75 21.875 C12.832 18.354 11.052 15.978 9 13 C9 12.34 9 11.68 9 11 C6.925 9.776 6.925 9.776 4 10 C3.34 9.34 2.68 8.68 2 8 C2.005 8.603 2.01 9.206 2.016 9.828 C2.177 31.898 1.607 53.94 1 76 C0.67 76 0.34 76 0 76 C0 50.92 0 25.84 0 0 Z M2 2 C2 3.32 2 4.64 2 6 C2.66 5.67 3.32 5.34 4 5 C3.67 4.01 3.34 3.02 3 2 C2.67 2 2.34 2 2 2 Z " fill="#5D65C0" transform="translate(695,421)"/>
<path d="M0 0 C8.25 0 16.5 0 25 0 C25 42.24 25 84.48 25 128 C24.67 128 24.34 128 24 128 C24 86.09 24 44.18 24 1 C16.41 1 8.82 1 1 1 C1 28.72 1 56.44 1 85 C-1.833 82.167 -2.719 80.646 -4 77 C-3.01 77.33 -2.02 77.66 -1 78 C-0.67 52.26 -0.34 26.52 0 0 Z " fill="#081392" transform="translate(710,190)"/>
<path d="M0 0 C14.159 -0.407 27.64 2.647 39.664 10.129 C39.994 9.469 40.324 8.809 40.664 8.129 C42.886 10.351 43.59 12.214 44.664 15.129 C40.966 13.724 37.533 12.042 34.039 10.191 C17.301 1.75 -2.425 0.74 -20.336 6.129 C-37.351 12.369 -49.733 24.4 -57.344 40.75 C-59.716 46.438 -61.549 51.992 -62.336 58.129 C-62.666 58.129 -62.996 58.129 -63.336 58.129 C-64.72 44.011 -55.454 30.953 -47.336 20.129 C-39.668 12.461 -29.649 6.386 -19.336 3.129 C-18.346 3.129 -17.356 3.129 -16.336 3.129 C-16.006 2.469 -15.676 1.809 -15.336 1.129 C-10.271 0.33 -5.116 0.282 0 0 Z " fill="#4956BB" transform="translate(579.3359375,362.87109375)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 0.66 19 1.32 19 2 C20.32 1.67 21.64 1.34 23 1 C23.104 1.726 23.209 2.451 23.316 3.199 C24.063 6.26 25.175 8.215 26.938 10.812 C27.462 11.603 27.987 12.393 28.527 13.207 C29.013 13.799 29.499 14.39 30 15 C30.66 15 31.32 15 32 15 C32.495 18.465 32.495 18.465 33 22 C33.99 21.67 34.98 21.34 36 21 C36.268 21.969 36.536 22.939 36.812 23.938 C37.4 25.453 37.4 25.453 38 27 C38.99 27.33 39.98 27.66 41 28 C41.103 28.897 41.206 29.793 41.312 30.717 C42.031 34.148 42.922 35.772 45.016 38.531 C47.909 42.474 50.657 46.477 53.297 50.594 C54.035 51.737 54.773 52.88 55.512 54.023 C56.642 55.777 57.771 57.532 58.895 59.29 C60.003 61.02 61.118 62.745 62.234 64.469 C63.212 65.991 63.212 65.991 64.21 67.545 C65.933 70.205 65.933 70.205 69 72 C69 72.99 69 73.98 69 75 C70.087 77.028 70.087 77.028 71.5 79.062 C74 82.792 74 82.792 74 85 C74.66 85 75.32 85 76 85 C75.505 86.98 75.505 86.98 75 89 C74.331 87.93 73.662 86.86 72.973 85.758 C65.475 73.809 57.79 61.987 50.055 50.191 C46.702 45.079 43.374 39.951 40.062 34.812 C36.245 28.891 32.37 23.012 28.434 17.169 C28.056 16.607 27.678 16.044 27.289 15.465 C26.908 14.912 26.528 14.36 26.135 13.791 C25 12 25 12 23.939 9.807 C22.79 7.554 21.78 5.802 20 4 C16.741 3.03 13.738 3.235 10.375 3.5 C9.087 3.554 9.087 3.554 7.773 3.609 C6.183 3.679 4.593 3.765 3.005 3.873 C1.34 3.978 -0.331 4 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#505BB9" transform="translate(670,363)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.009 0.618 1.019 1.236 1.028 1.873 C1.458 24.553 4.594 43.092 20.906 59.973 C32.609 70.884 47.639 75.808 63.312 76.562 C64.148 76.606 64.984 76.649 65.846 76.693 C67.897 76.799 69.948 76.9 72 77 C72 77.33 72 77.66 72 78 C69.03 78 66.06 78 63 78 C62.505 79.485 62.505 79.485 62 81 C62 80.34 62 79.68 62 79 C61.072 79.062 60.144 79.124 59.188 79.188 C53.916 79.22 49 77.519 44 76 C43.67 76.66 43.34 77.32 43 78 C42.299 77.402 41.597 76.804 40.875 76.188 C36.526 72.879 32.426 70.798 27 70 C26.67 69.01 26.34 68.02 26 67 C24.476 65.592 24.476 65.592 22.625 64.375 C22.019 63.95 21.413 63.524 20.789 63.086 C18.88 61.837 18.88 61.837 16 61 C15.67 59.35 15.34 57.7 15 56 C13.515 55.505 13.515 55.505 12 55 C10.319 52.233 8.936 49.37 7.5 46.469 C6.183 43.702 6.183 43.702 3 43 C3 40.03 3 37.06 3 34 C2.34 33.67 1.68 33.34 1 33 C0.073 29.041 -0.274 24.973 -0.75 20.938 C-0.976 19.256 -0.976 19.256 -1.207 17.541 C-1.985 11.108 -2.081 6.16 0 0 Z " fill="#606AC2" transform="translate(281,242)"/>
<path d="M0 0 C-0.66 0.66 -1.32 1.32 -2 2 C-2.834 1.662 -3.668 1.325 -4.527 0.977 C-5.653 0.531 -6.778 0.085 -7.938 -0.375 C-9.04 -0.816 -10.142 -1.257 -11.277 -1.711 C-27.598 -7.363 -48.098 -7.71 -63.819 -0.122 C-80.986 8.588 -90.668 20.444 -96.809 38.566 C-97.723 41.202 -98.667 43.563 -100 46 C-100.534 39.663 -98.78 35.416 -96.062 29.75 C-95.323 28.162 -94.585 26.573 -93.848 24.984 C-93.518 24.291 -93.188 23.597 -92.848 22.882 C-91.892 20.846 -91.892 20.846 -91 18 C-90.361 17.526 -89.721 17.051 -89.062 16.562 C-86.902 15.176 -86.902 15.176 -86.094 13.082 C-84.119 9.323 -80.421 7.379 -77 5 C-75.923 4.219 -75.923 4.219 -74.824 3.422 C-69.494 -0.328 -64.194 -2.977 -58 -5 C-57.081 -5.3 -56.162 -5.601 -55.215 -5.91 C-37.499 -10.825 -15.855 -9.751 0 0 Z " fill="#4F5EC2" transform="translate(382,195)"/>
<path d="M0 0 C2.625 -0.438 2.625 -0.438 6 0 C13.674 5.726 18.335 17.34 19.754 26.512 C21.138 40.502 18.42 53.424 9.625 64.562 C2.276 72.533 -8.05 76.69 -18.762 77.168 C-20.705 77.178 -20.705 77.178 -22.688 77.188 C-23.991 77.202 -25.294 77.216 -26.637 77.23 C-30 77 -30 77 -33 75 C-31 73 -31 73 -27.875 72.875 C-26.926 72.916 -25.978 72.957 -25 73 C-25 73.99 -25 74.98 -25 76 C-23.762 75.835 -22.525 75.67 -21.25 75.5 C-18.422 75.123 -15.884 75 -13 75 C-12.505 73.515 -12.505 73.515 -12 72 C-11.175 72.082 -10.35 72.165 -9.5 72.25 C-2.304 71.736 2.675 67.944 7.473 62.766 C15.909 53.013 17.809 41.411 17.32 28.891 C16.927 24.118 16.115 20.281 14 16 C13.42 14.797 13.42 14.797 12.828 13.57 C10.343 8.768 8.206 5.132 2.875 3.25 C0.913 2.81 0.913 2.81 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#4D5ABA" transform="translate(369,220)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.63 1.66 7.26 2 11 C10.91 11 19.82 11 29 11 C27.711 20.022 24.486 28.878 16.926 34.559 C6.409 41.807 -3.341 42.769 -16 42 C-19.772 41.259 -23.373 40.267 -27 39 C-26.67 38.34 -26.34 37.68 -26 37 C-24.866 37.309 -23.731 37.619 -22.562 37.938 C-15.173 39.613 -7.544 39.671 0 40 C0.33 39.01 0.66 38.02 1 37 C1.33 37.66 1.66 38.32 2 39 C10.178 36.512 16.457 32.577 22 26 C23.474 23.053 24.403 20.236 25 17 C22.525 16.01 22.525 16.01 20 15 C20 14.34 20 13.68 20 13 C17.75 12.973 15.5 12.954 13.25 12.938 C11.371 12.92 11.371 12.92 9.453 12.902 C6.715 12.98 4.581 13.172 2 14 C-0.399 9.352 -0.2 5.089 0 0 Z M26 13 C27 16 27 16 27 16 Z " fill="#4F59BA" transform="translate(828,255)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 16.5 1 33 1 50 C8.92 50 16.84 50 25 50 C25.33 45.05 25.66 40.1 26 35 C26.33 35 26.66 35 27 35 C27 40.28 27 45.56 27 51 C22.579 52.769 20.763 53.243 16.262 53.195 C14.662 53.186 14.662 53.186 13.029 53.176 C11.927 53.159 10.824 53.142 9.688 53.125 C8.565 53.116 7.443 53.107 6.287 53.098 C3.525 53.074 0.762 53.041 -2 53 C-2.125 46.375 -2.125 46.375 -1 43 C-1.66 42.67 -2.32 42.34 -3 42 C-2.756 41.239 -2.756 41.239 -2.506 40.462 C-1.824 37.146 -1.889 33.994 -1.902 30.609 C-1.904 29.905 -1.905 29.2 -1.907 28.474 C-1.912 26.233 -1.925 23.991 -1.938 21.75 C-1.943 20.227 -1.947 18.703 -1.951 17.18 C-1.962 13.453 -1.979 9.727 -2 6 C-1.67 6 -1.34 6 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#848BD2" transform="translate(434,267)"/>
<path d="M0 0 C2.17 3.254 2.25 4.142 2.247 7.901 C2.251 8.911 2.256 9.922 2.26 10.963 C2.254 12.071 2.249 13.179 2.243 14.321 C2.245 15.489 2.247 16.657 2.249 17.86 C2.252 21.733 2.242 25.607 2.23 29.48 C2.228 32.17 2.229 34.859 2.229 37.549 C2.228 43.194 2.219 48.839 2.206 54.484 C2.19 60.996 2.185 67.509 2.186 74.021 C2.186 80.966 2.179 87.91 2.17 94.855 C2.168 96.849 2.167 98.843 2.167 100.837 C2.165 104.569 2.156 108.3 2.145 112.032 C2.146 113.679 2.146 113.679 2.147 115.359 C2.142 116.384 2.138 117.409 2.134 118.465 C2.132 119.346 2.13 120.227 2.129 121.134 C1.965 124.772 1.452 128.387 1 132 C-4.94 132 -10.88 132 -17 132 C-17.495 132.99 -17.495 132.99 -18 134 C-18 133.34 -18 132.68 -18 132 C-19.65 132 -21.3 132 -23 132 C-23 131.34 -23 130.68 -23 130 C-15.41 130 -7.82 130 0 130 C0 87.1 0 44.2 0 0 Z " fill="#121E9D" transform="translate(769,365)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.318 2.697 3.654 3.359 5 4 C6.319 4.865 7.631 5.74 8.938 6.625 C12.089 8.634 14.172 9.598 18 9 C17.67 9.99 17.34 10.98 17 12 C22.94 12.99 22.94 12.99 29 14 C24.065 16.468 22.779 15.829 17.801 14.199 C13.693 12.441 10.113 10.058 6.445 7.523 C4.21 6.131 2.569 5.442 0 5 C-0.626 5.699 -1.253 6.397 -1.898 7.117 C-2.716 8.027 -3.533 8.937 -4.375 9.875 C-5.187 10.78 -5.999 11.685 -6.836 12.617 C-8.191 14.109 -9.575 15.575 -11 17 C-10.781 19.433 -10.314 20.663 -8.645 22.457 C1.696 30.108 12.356 34.795 25 37 C24.505 37.99 24.505 37.99 24 39 C19.793 38.549 15.828 38.009 11.75 36.875 C8.166 36.039 7.282 36.037 4 37 C3.34 37 2.68 37 2 37 C1 34 1 34 1 32 C-1.31 30.68 -3.62 29.36 -6 28 C-6 27.34 -6 26.68 -6 26 C-6.557 25.752 -7.114 25.505 -7.688 25.25 C-10.4 23.784 -12.636 21.97 -15 20 C-14.687 17.078 -14.18 16.149 -11.875 14.25 C-10.926 13.507 -9.977 12.765 -9 12 C-5.429 8.283 -2.142 4.728 0 0 Z " fill="#606DC5" transform="translate(262,459)"/>
<path d="M0 0 C1.625 0.828 1.625 0.828 3.812 2.328 C3.482 2.988 3.153 3.648 2.812 4.328 C1.822 3.833 0.832 3.338 -0.188 2.828 C-5.752 0.472 -10.227 0.707 -15.938 2.516 C-20.172 4.877 -22.117 7.401 -24.438 11.578 C-25.911 16.981 -25.667 21.731 -24.062 27.078 C-21.384 31.72 -18.386 34.595 -13.188 36.328 C-6.765 36.852 -1.811 36.789 3.812 33.328 C4.472 33.328 5.133 33.328 5.812 33.328 C5.317 30.358 5.317 30.358 4.812 27.328 C3.822 27.328 2.832 27.328 1.812 27.328 C2.143 26.008 2.472 24.688 2.812 23.328 C3.803 23.328 4.793 23.328 5.812 23.328 C7.412 27.373 8.389 30.163 6.812 34.328 C2.9 38.391 -2.723 39.408 -8.188 39.641 C-12.323 39.536 -15.489 38.177 -19.188 36.328 C-19.951 36.019 -20.714 35.709 -21.5 35.391 C-23.648 34.038 -24.159 32.606 -25.188 30.328 C-25.847 29.998 -26.508 29.668 -27.188 29.328 C-27.188 28.668 -27.188 28.008 -27.188 27.328 C-27.847 27.328 -28.508 27.328 -29.188 27.328 C-29.002 26.441 -28.816 25.554 -28.625 24.641 C-28.207 21.476 -28.38 19.38 -29.188 16.328 C-28.528 16.328 -27.867 16.328 -27.188 16.328 C-27.084 15.276 -26.981 14.224 -26.875 13.141 C-25.761 6.964 -23.289 3.899 -18.188 0.328 C-12.675 -1.509 -5.586 -1.716 0 0 Z " fill="#535EC0" transform="translate(645.1875,632.671875)"/>
<path d="M0 0 C2.24 1.967 3.912 3.792 5.566 6.254 C5.236 6.914 4.906 7.574 4.566 8.254 C4.01 7.45 3.453 6.645 2.879 5.816 C0.348 3.012 -0.759 2.56 -4.434 2.254 C-4.104 1.594 -3.774 0.934 -3.434 0.254 C-5.414 -0.076 -7.394 -0.406 -9.434 -0.746 C-9.434 -1.736 -9.434 -2.726 -9.434 -3.746 C-10.723 -4.109 -12.015 -4.461 -13.309 -4.809 C-14.028 -5.006 -14.747 -5.203 -15.488 -5.406 C-17.506 -5.957 -17.506 -5.957 -19.434 -4.746 C-25.131 -5.444 -25.131 -5.444 -26.434 -6.746 C-26.434 -6.416 -26.434 -6.086 -26.434 -5.746 C-29.734 -5.416 -33.034 -5.086 -36.434 -4.746 C-36.434 -5.406 -36.434 -6.066 -36.434 -6.746 C-44.269 -3.904 -44.269 -3.904 -51.434 0.254 C-51.434 0.914 -51.434 1.574 -51.434 2.254 C-52.073 2.543 -52.712 2.831 -53.371 3.129 C-55.586 4.082 -55.586 4.082 -56.434 6.254 C-57.094 6.254 -57.754 6.254 -58.434 6.254 C-57.581 1.184 -54.391 -0.808 -50.434 -3.746 C-34.48 -14.788 -14.452 -11.136 0 0 Z M-36.434 -7.746 C-36.434 -7.416 -36.434 -7.086 -36.434 -6.746 C-34.124 -6.746 -31.814 -6.746 -29.434 -6.746 C-29.434 -7.076 -29.434 -7.406 -29.434 -7.746 C-31.744 -7.746 -34.054 -7.746 -36.434 -7.746 Z M-24.434 -7.746 C-24.434 -7.416 -24.434 -7.086 -24.434 -6.746 C-22.454 -6.746 -20.474 -6.746 -18.434 -6.746 C-18.434 -7.076 -18.434 -7.406 -18.434 -7.746 C-20.414 -7.746 -22.394 -7.746 -24.434 -7.746 Z " fill="#7780CE" transform="translate(457.43359375,396.74609375)"/>
<path d="M0 0 C6.268 2.79 11.065 8.491 15 14 C15 14.66 15 15.32 15 16 C15.66 16.33 16.32 16.66 17 17 C26.75 35.55 29.173 56.872 23 77 C17.755 90.123 17.755 90.123 14 92 C14.679 88.734 15.895 86.045 17.375 83.062 C25.291 65.625 25.242 45.951 19 28 C15.336 18.828 10.111 10.013 2.254 3.844 C1.51 3.235 0.766 2.627 0 2 C0 1.34 0 0.68 0 0 Z " fill="#515CBD" transform="translate(624,378)"/>
<path d="M0 0 C2.248 2.052 3.002 3.007 4 6 C3.75 7.574 3.426 9.136 3.062 10.688 C-0.226 27.785 1.806 47.902 11.738 62.566 C13.434 64.77 15.186 66.894 17 69 C17.804 70.011 18.609 71.021 19.438 72.062 C20.211 73.022 20.211 73.022 21 74 C18 74 18 74 16.312 72.625 C15 71 15 71 15 69 C14.34 69 13.68 69 13 69 C12.34 67.68 11.68 66.36 11 65 C10.01 65 9.02 65 8 65 C8 64.01 8 63.02 8 62 C7.374 60.318 6.712 58.648 6 57 C4.02 57.495 4.02 57.495 2 58 C1.375 56.25 1.375 56.25 1 54 C1.348 53.388 1.696 52.775 2.055 52.145 C3.296 49.329 2.763 48.306 1.75 45.438 C-0.786 37.166 -1.244 28.579 -2 20 C-1.34 20 -0.68 20 0 20 C0 17.03 0 14.06 0 11 C-0.33 11 -0.66 11 -1 11 C-1.143 3.571 -1.143 3.571 0 0 Z M1 5 C2 7 2 7 2 7 Z " fill="#515CBA" transform="translate(753,229)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.75 0.984 -1.5 0.969 -2.272 0.952 C-5.744 0.889 -9.215 0.851 -12.688 0.812 C-14.456 0.775 -14.456 0.775 -16.26 0.736 C-35.712 0.576 -51.709 7.198 -65.64 20.612 C-72.223 27.33 -75.751 35.287 -79 44 C-79.66 43.67 -80.32 43.34 -81 43 C-76.783 27.398 -64.807 14.023 -51 6 C-35.812 -1.655 -16.538 -5.513 0 0 Z " fill="#4A55BA" transform="translate(837,189)"/>
<path d="M0 0 C8.91 0 17.82 0 27 0 C30.75 7.063 30.75 7.063 31.905 9.242 C34.193 13.542 36.565 17.782 39 22 C39.66 22 40.32 22 41 22 C43 24 43 24 43 27 C44.176 30.527 45.753 33.07 48 36 C48.33 36.99 48.66 37.98 49 39 C49.987 40.674 50.988 42.341 52 44 C52.28 44.581 52.559 45.163 52.848 45.762 C54.984 49.911 57.62 53.778 60.125 57.711 C61.876 60.782 63.019 63.616 64 67 C61 66 61 66 59.676 63.734 C59.226 62.75 58.776 61.765 58.312 60.75 C56.243 56.436 54.061 52.354 51.5 48.312 C46.777 40.796 42.494 33.044 38.188 25.284 C37.619 24.261 37.051 23.238 36.465 22.184 C35.903 21.17 35.34 20.157 34.761 19.113 C32.351 14.853 29.828 10.664 27.287 6.481 C26 4 26 4 26 1 C17.42 1 8.84 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#515DBC" transform="translate(436,190)"/>
<path d="M0 0 C3.199 1.5 6.016 3.331 8.938 5.312 C25.962 16.179 45.449 18.384 64.988 14.113 C72.73 12.156 80.214 8.475 86.273 3.273 C89 1 89 1 91 1 C91 1.66 91 2.32 91 3 C77.943 14.029 63.157 18.601 46.25 18.5 C44.547 18.49 44.547 18.49 42.81 18.48 C27.79 18.156 16.245 13.557 4 5 C3.221 4.466 2.443 3.933 1.641 3.383 C1.099 2.926 0.558 2.47 0 2 C0 1.34 0 0.68 0 0 Z " fill="#4550B5" transform="translate(773,303)"/>
<path d="M0 0 C5.639 0.403 8.073 2.165 12 6 C13.742 11.226 13.95 16.873 11.688 21.938 C7.078 27.571 2.126 29.376 -5.117 30.258 C-12.515 30.466 -19.627 28.731 -26 25 C-26.66 24.34 -27.32 23.68 -28 23 C-27.01 20.03 -26.02 17.06 -25 14 C-24.361 14.268 -23.721 14.536 -23.062 14.812 C-17.134 17.111 -11.338 19.187 -5 20 C-5 20.33 -5 20.66 -5 21 C-12.245 21.534 -16.729 20.716 -23 17 C-23.33 18.98 -23.66 20.96 -24 23 C-15.7 27.197 -7.674 28.45 1.5 26.457 C5.544 25.112 7.89 23.671 10 20 C10.549 16.866 10.549 16.866 10.5 13.5 C10.531 12.376 10.562 11.252 10.594 10.094 C10.269 6.827 10.269 6.827 7.645 4.672 C5.143 3.09 2.795 1.958 0 1 C0 0.67 0 0.34 0 0 Z " fill="#4F5DBE" transform="translate(345,642)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.122 3.76 -0.83 5.483 -1.812 7.188 C-7.958 19.221 -8.174 33.007 -5 46 C-3.636 49.571 -1.95 52.726 0 56 C0.33 56.99 0.66 57.98 1 59 C1.66 59 2.32 59 3 59 C3.681 59.743 4.361 60.485 5.062 61.25 C7.947 64.308 11.239 65.6 15.109 67.105 C17 68 17 68 18 70 C20.81 70.692 20.81 70.692 24.062 71.125 C25.167 71.293 26.272 71.46 27.41 71.633 C28.265 71.754 29.119 71.875 30 72 C30 72.33 30 72.66 30 73 C20.697 73.702 13.12 70.912 5.688 65.312 C-3.107 57.73 -8.019 48.313 -9.244 36.687 C-10.188 23.277 -9.136 10.6 0 0 Z " fill="#4551B5" transform="translate(550,402)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.056 1.58 1.056 1.58 1.113 3.191 C1.913 22.059 5.092 40.29 18 55 C21.532 58.19 25.23 61.099 29 64 C29.99 64.99 29.99 64.99 31 66 C30.34 66.66 29.68 67.32 29 68 C28.34 67.34 27.68 66.68 27 66 C26.34 65.67 25.68 65.34 25 65 C24.175 64.154 23.35 63.309 22.5 62.438 C20.343 59.767 20.343 59.767 18 60 C16.954 58.362 15.96 56.69 15 55 C13.08 52.2 11.891 50.927 9 49 C8 48 8 48 7.566 46.219 C6.749 43.016 5.269 40.189 3.84 37.219 C3 35 3 35 3 31 C2.34 30.67 1.68 30.34 1 30 C0.856 28.866 0.711 27.731 0.562 26.562 C0.377 25.387 0.191 24.211 0 23 C-0.99 22.34 -1.98 21.68 -3 21 C-2.34 17.04 -1.68 13.08 -1 9 C-1.66 9 -2.32 9 -3 9 C-2.01 6.03 -1.02 3.06 0 0 Z " fill="#5560BE" transform="translate(515,422)"/>
<path d="M0 0 C4.847 0.591 8.607 1.979 13 4 C13 5.65 13 7.3 13 9 C12.34 9 11.68 9 11 9 C10.34 10.65 9.68 12.3 9 14 C7.907 13.67 6.814 13.34 5.688 13 C2.36 12.098 0.304 11.633 -3 12.562 C-5 13 -5 13 -8 12 C-10.009 12.276 -12.012 12.602 -14 13 C-14 12.34 -14 11.68 -14 11 C-15.247 12.051 -15.247 12.051 -15.375 14.375 C-15.189 15.674 -15.189 15.674 -15 17 C-12.591 18.862 -12.591 18.862 -10 20 C-10.33 19.01 -10.66 18.02 -11 17 C-10.34 16.67 -9.68 16.34 -9 16 C-8.67 16.99 -8.34 17.98 -8 19 C-5.671 20.027 -5.671 20.027 -2.938 20.688 C-2.018 20.939 -1.099 21.19 -0.152 21.449 C0.558 21.631 1.268 21.813 2 22 C-1 24 -1 24 -4.25 23.461 C-5.488 23.103 -6.725 22.744 -8 22.375 C-9.238 22.032 -10.475 21.689 -11.75 21.336 C-15.1 19.959 -16.324 19.155 -18 16 C-18.062 13.5 -18.062 13.5 -17 11 C-14.38 8.447 -12.415 7.079 -8.695 6.871 C-2.833 7.319 2.531 8.9 8 11 C8.66 9.02 9.32 7.04 10 5 C6.37 4.01 2.74 3.02 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#606DC7" transform="translate(345,619)"/>
<path d="M0 0 C3.504 3.037 4.938 5.485 6 10 C6.075 12.332 6.093 14.669 6 17 C4.886 17.268 3.773 17.536 2.625 17.812 C-1.218 18.658 -1.218 18.658 -3 22 C-3 22.99 -3 23.98 -3 25 C-7.039 27.692 -10.255 27.672 -15 27 C-18.803 24.995 -20.618 23.573 -23 20 C-23 18.68 -23 17.36 -23 16 C-14.09 16 -5.18 16 4 16 C2.531 5.847 2.531 5.847 0 0 Z M-21 18 C-19.524 23.06 -19.524 23.06 -16 25 C-15.01 24.67 -14.02 24.34 -13 24 C-10.221 24 -8.729 24.09 -6 25 C-5.34 24.34 -4.68 23.68 -4 23 C-4.319 20.892 -4.319 20.892 -5 19 C-4.34 18.67 -3.68 18.34 -3 18 C-8.94 18 -14.88 18 -21 18 Z " fill="#858DD7" transform="translate(649,637)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.039 1.231 -2.078 1.461 -3.148 1.699 C-20.953 5.796 -34.506 13.483 -45 29 C-50.167 37.849 -53.215 46.919 -55 57 C-55.33 57 -55.66 57 -56 57 C-57.14 42.186 -48.661 28.925 -39.734 17.801 C-39.162 17.207 -38.59 16.612 -38 16 C-37.34 16 -36.68 16 -36 16 C-36 15.34 -36 14.68 -36 14 C-31.031 9.866 -22.744 3 -16 3 C-15.67 2.34 -15.34 1.68 -15 1 C-10.039 -0.24 -5.076 -0.102 0 0 Z " fill="#4A57BA" transform="translate(420,365)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.506 1.238 -1.506 1.238 -3.043 1.48 C-17.254 3.848 -28.628 7.833 -40 17 C-40.825 17.598 -41.65 18.196 -42.5 18.812 C-45.612 21.535 -47.738 24.499 -50 27.938 C-50.435 28.595 -50.87 29.252 -51.318 29.929 C-56.311 37.715 -59.673 45.792 -61 55 C-61.66 55 -62.32 55 -63 55 C-61.861 39.625 -53.041 25.426 -42 15 C-29.806 5.026 -15.913 -0.951 0 0 Z " fill="#4656BA" transform="translate(210,188)"/>
<path d="M0 0 C2.858 2.45 2.729 4.237 3.188 7.875 C2.27 7.912 1.352 7.95 0.406 7.988 C-6.554 8.361 -12.534 8.736 -18.812 11.875 C-20.836 14.91 -21.134 16.069 -21.438 19.562 C-21.512 20.371 -21.587 21.179 -21.664 22.012 C-21.713 22.627 -21.762 23.241 -21.812 23.875 C-24.575 21.113 -24.391 18.668 -24.812 14.875 C-24.152 14.875 -23.493 14.875 -22.812 14.875 C-22.544 14.339 -22.276 13.803 -22 13.25 C-21.608 12.466 -21.216 11.683 -20.812 10.875 C-20.359 9.823 -19.905 8.771 -19.438 7.688 C-17.812 4.875 -17.812 4.875 -15.812 3.938 C-13.812 3.875 -13.812 3.875 -10.812 4.875 C-10.483 3.555 -10.152 2.235 -9.812 0.875 C-10.473 0.545 -11.132 0.215 -11.812 -0.125 C-7.483 -1.568 -4.272 -1.709 0 0 Z M-6.812 0.875 C-7.803 2.36 -7.803 2.36 -8.812 3.875 C-8.483 4.535 -8.152 5.195 -7.812 5.875 C-6.163 5.875 -4.512 5.875 -2.812 5.875 C-3.142 5.215 -3.473 4.555 -3.812 3.875 C-3.483 2.885 -3.152 1.895 -2.812 0.875 C-4.132 0.875 -5.452 0.875 -6.812 0.875 Z M0.188 3.875 C1.188 5.875 1.188 5.875 1.188 5.875 Z " fill="#515EC1" transform="translate(417.8125,640.125)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.268 3.73 4.043 4.874 3.062 7.75 C2.712 8.492 2.361 9.235 2 10 C0.36 10.217 0.36 10.217 -1.312 10.438 C-4.931 10.883 -4.931 10.883 -8 12 C-7.886 14.084 -7.758 16.167 -7.625 18.25 C-7.555 19.41 -7.486 20.57 -7.414 21.766 C-7.038 24.707 -6.523 26.499 -5 29 C-1.535 28.505 -1.535 28.505 2 28 C2.33 28.66 2.66 29.32 3 30 C3.99 30 4.98 30 6 30 C6.99 30 7.98 30 9 30 C9 30.66 9 31.32 9 32 C6 34 6 34 3 34 C3 33.34 3 32.68 3 32 C2.313 32.058 1.626 32.116 0.918 32.176 C0.017 32.221 -0.884 32.266 -1.812 32.312 C-2.706 32.371 -3.599 32.429 -4.52 32.488 C-7 32 -7 32 -8.809 30.059 C-10.437 25.878 -10.363 22.192 -10.25 17.75 C-10.245 16.922 -10.24 16.095 -10.234 15.242 C-10.155 9.155 -10.155 9.155 -9 8 C-7.314 7.928 -5.625 7.916 -3.938 7.938 C-3.018 7.947 -2.099 7.956 -1.152 7.965 C-0.442 7.976 0.268 7.988 1 8 C0.67 5.36 0.34 2.72 0 0 Z " fill="#7A83D5" transform="translate(520,632)"/>
<path d="M0 0 C2.236 1.512 3.349 2.799 5 5 C5 5.66 5 6.32 5 7 C7.475 7.495 7.475 7.495 10 8 C9.34 8.33 8.68 8.66 8 9 C6.686 11.627 6.787 13.726 6.684 16.664 C6.621 18.346 6.621 18.346 6.557 20.062 C6.517 21.238 6.478 22.414 6.438 23.625 C6.373 25.4 6.373 25.4 6.307 27.211 C6.2 30.141 6.098 33.07 6 36 C5.67 36 5.34 36 5 36 C4.985 35.208 4.971 34.417 4.956 33.601 C4.881 30.004 4.785 26.409 4.688 22.812 C4.654 20.944 4.654 20.944 4.619 19.037 C4.584 17.835 4.548 16.633 4.512 15.395 C4.486 14.29 4.459 13.185 4.432 12.046 C3.814 7.689 2.202 5.025 -1.125 2.188 C-5.152 0.524 -8.701 0.417 -13 1 C-16.369 2.307 -16.369 2.307 -19 4 C-19.99 4.495 -19.99 4.495 -21 5 C-21 3.68 -21 2.36 -21 1 C-23.31 1 -25.62 1 -28 1 C-28 12.55 -28 24.1 -28 36 C-28.33 36 -28.66 36 -29 36 C-29 24.12 -29 12.24 -29 0 C-25.04 0 -21.08 0 -17 0 C-15.804 -0.371 -14.607 -0.742 -13.375 -1.125 C-8.581 -2.368 -4.629 -1.681 0 0 Z " fill="#4350BC" transform="translate(604,633)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.67 1.99 4.34 2.98 4 4 C3.257 4.289 2.515 4.577 1.75 4.875 C-1.139 5.843 -1.139 5.843 -3.188 8.25 C-13.904 18.728 -26.194 20.386 -40.473 20.234 C-45.917 20.057 -50.896 18.889 -56 17 C-55.34 16.34 -54.68 15.68 -54 15 C-53.031 15.165 -52.061 15.33 -51.062 15.5 C-47.837 16.384 -47.837 16.384 -45 14 C-44.34 14.33 -43.68 14.66 -43 15 C-43.99 15.33 -44.98 15.66 -46 16 C-46 16.66 -46 17.32 -46 18 C-38.039 18.144 -29.876 18.277 -22 17 C-21.67 16.34 -21.34 15.68 -21 15 C-20.34 15.33 -19.68 15.66 -19 16 C-18.67 15.34 -18.34 14.68 -18 14 C-17.154 13.752 -16.309 13.505 -15.438 13.25 C-9.884 11.231 -4.926 7.436 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#6671C7" transform="translate(251,277)"/>
<path d="M0 0 C4.613 3 8.542 6.771 12.469 10.605 C12.139 11.265 11.809 11.925 11.469 12.605 C10.889 12.04 10.309 11.474 9.711 10.891 C4.033 5.288 4.033 5.288 -3.531 3.605 C-3.201 2.945 -2.871 2.285 -2.531 1.605 C-4.259 0.955 -5.988 0.31 -7.719 -0.332 C-8.682 -0.692 -9.645 -1.051 -10.637 -1.422 C-15.728 -3.133 -20.16 -3.5 -25.531 -3.395 C-24.541 -3.065 -23.551 -2.735 -22.531 -2.395 C-22.861 -1.735 -23.191 -1.075 -23.531 -0.395 C-25.181 -0.395 -26.831 -0.395 -28.531 -0.395 C-28.531 -1.055 -28.531 -1.715 -28.531 -2.395 C-33.376 -3.909 -36.79 -2.761 -41.531 -1.395 C-41.861 -2.055 -42.191 -2.715 -42.531 -3.395 C-28.233 -8.817 -13.443 -7.092 0 0 Z " fill="#6672C8" transform="translate(238.53125,216.39453125)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.977 4.164 1.977 4.164 1.625 6.625 C1.459 7.851 1.459 7.851 1.289 9.102 C1.146 10.041 1.146 10.041 1 11 C2.98 11 4.96 11 7 11 C7 11.33 7 11.66 7 12 C4.69 12 2.38 12 0 12 C0 9.03 0 6.06 0 3 C-2.31 3.33 -4.62 3.66 -7 4 C-7.33 6.64 -7.66 9.28 -8 12 C-9.65 12 -11.3 12 -13 12 C-13 13.98 -13 15.96 -13 18 C-11.35 18 -9.7 18 -8 18 C-8 23.61 -8 29.22 -8 35 C-10.679 30.981 -10.156 28.758 -10 24 C-11.32 24.33 -12.64 24.66 -14 25 C-14.217 23.886 -14.217 23.886 -14.438 22.75 C-14.912 20.105 -14.912 20.105 -15.688 18.062 C-16.145 15.041 -15.083 12.802 -14 10 C-12.68 10 -11.36 10 -10 10 C-9.67 7.03 -9.34 4.06 -9 1 C-2.164 0.023 -2.164 0.023 0 0 Z " fill="#616CCB" transform="translate(377,622)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.28 1 10.56 1 16 C-4 15 -4 15 -7 14 C-12.47 13.579 -16.254 14.187 -21 17 C-25.431 21.762 -26.574 26.515 -26.762 32.898 C-27 35 -27 35 -29 37 C-31.263 36.646 -32.951 36.025 -35 35 C-34.01 34.34 -33.02 33.68 -32 33 C-31.75 30.416 -31.75 30.416 -32 28 C-31.34 28 -30.68 28 -30 28 C-29.709 27.336 -29.417 26.672 -29.117 25.988 C-26.03 19.337 -23.413 15.241 -16.738 11.852 C-13.334 10.793 -10.724 11.018 -7.188 11.375 C-6.026 11.486 -4.865 11.597 -3.668 11.711 C-2.788 11.806 -1.907 11.902 -1 12 C-1.184 11.254 -1.369 10.507 -1.559 9.738 C-2.146 6.095 -1.153 3.459 0 0 Z M-1 12 C-1.33 12.66 -1.66 13.32 -2 14 C-1.34 14 -0.68 14 0 14 C-0.33 13.34 -0.66 12.68 -1 12 Z " fill="#5964C3" transform="translate(689,620)"/>
<path d="M0 0 C3.658 2.04 5.669 3.504 8 7 C8.582 11.882 8.617 16.488 6.75 21.062 C3.652 24.493 1.767 24.913 -2.875 25.438 C-7.667 25.339 -9.722 23.358 -13 20 C-14.97 16.06 -14.453 11.295 -14 7 C-10.683 1.195 -6.606 -0.793 0 0 Z M-12 6 C-13.182 10.087 -13.3 13.746 -12.312 17.875 C-10.803 20.318 -9.523 21.624 -7 23 C-6.01 22.67 -5.02 22.34 -4 22 C-3.67 22.33 -3.34 22.66 -3 23 C-2.34 23.33 -1.68 23.66 -1 24 C4.12 20.962 4.12 20.962 7 16 C7.03 13.627 7.03 13.627 6.625 11.25 C6.514 10.451 6.403 9.652 6.289 8.828 C6.194 8.225 6.098 7.622 6 7 C5.34 7 4.68 7 4 7 C4 6.01 4 5.02 4 4 C-2.558 1.56 -6.983 0.286 -12 6 Z " fill="#414EB7" transform="translate(683,639)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 1.66 4 2.32 4 3 C4.572 3.262 5.145 3.523 5.734 3.793 C8.166 5.089 10.136 6.547 12.25 8.312 C12.956 8.886 13.663 9.46 14.391 10.051 C16 12 16 12 16.547 14.418 C15.76 18.132 13.821 19.528 11 22 C10.207 22.73 9.414 23.459 8.598 24.211 C-2.16 33.394 -14.514 40.508 -29 40 C-25.95 38.28 -22.825 37.311 -19.467 36.353 C-5.57 32.35 3.861 25.269 14 15 C12.452 10.941 9.811 9.005 6.438 6.375 C1.097 2.194 1.097 2.194 0 0 Z " fill="#4B59BD" transform="translate(255,280)"/>
<path d="M0 0 C7.92 0 15.84 0 24 0 C24 0.33 24 0.66 24 1 C16.41 1 8.82 1 1 1 C1 28.72 1 56.44 1 85 C-1.833 82.167 -2.719 80.646 -4 77 C-3.01 77.33 -2.02 77.66 -1 78 C-0.67 52.26 -0.34 26.52 0 0 Z " fill="#1928AC" transform="translate(710,190)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 0.66 19 1.32 19 2 C20.32 1.67 21.64 1.34 23 1 C23.104 1.726 23.209 2.451 23.316 3.199 C24.063 6.26 25.175 8.215 26.938 10.812 C27.462 11.603 27.987 12.393 28.527 13.207 C29.013 13.799 29.499 14.39 30 15 C30.66 15 31.32 15 32 15 C32.495 18.465 32.495 18.465 33 22 C33.99 21.67 34.98 21.34 36 21 C36.268 21.969 36.536 22.939 36.812 23.938 C37.4 25.453 37.4 25.453 38 27 C38.99 27.33 39.98 27.66 41 28 C41 28.99 41 29.98 41 31 C37.432 30.398 37.071 30.092 34.668 27.008 C33.831 25.72 33.005 24.425 32.188 23.125 C31.74 22.453 31.293 21.782 30.832 21.09 C28.769 17.963 26.995 14.988 25.461 11.559 C23.91 8.235 22.903 6.271 20 4 C14.405 2.79 8.63 3.287 2.974 3.825 C1 4 1 4 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#636CC3" transform="translate(670,363)"/>
<path d="M0 0 C3 0 3 0 6 2 C6.961 4.883 7.117 6.677 7.098 9.668 C7.094 10.561 7.091 11.453 7.088 12.373 C7.08 13.302 7.071 14.231 7.062 15.188 C7.058 16.128 7.053 17.068 7.049 18.037 C7.037 20.358 7.021 22.679 7 25 C7.66 25.33 8.32 25.66 9 26 C8.67 26.33 8.34 26.66 8 27 C8.33 27.66 8.66 28.32 9 29 C8.34 29 7.68 29 7 29 C7.33 29.99 7.66 30.98 8 32 C7.67 32.99 7.34 33.98 7 35 C6.855 36.407 6.754 37.818 6.684 39.23 C6.642 40.033 6.6 40.835 6.557 41.662 C6.498 42.912 6.498 42.912 6.438 44.188 C6.394 45.032 6.351 45.877 6.307 46.748 C6.201 48.832 6.1 50.916 6 53 C5.67 53 5.34 53 5 53 C5 37.16 5 21.32 5 5 C2.69 5 0.38 5 -2 5 C-2 10.61 -2 16.22 -2 22 C-2.66 21.34 -3.32 20.68 -4 20 C-3.983 17.08 -3.861 14.277 -3.625 11.375 C-3.568 10.573 -3.512 9.771 -3.453 8.945 C-3.312 6.963 -3.157 4.981 -3 3 C-2.01 2.67 -1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#4B57BD" transform="translate(693,616)"/>
<path d="M0 0 C6.167 1.773 11.308 3.411 15.312 8.75 C16.819 13.68 16.736 18.162 14.438 22.75 C11.011 27.992 6.122 29.899 0.25 31.25 C-6.871 32.478 -13.94 32.584 -21 31 C-21 30.67 -21 30.34 -21 30 C-20.447 29.991 -19.893 29.981 -19.323 29.972 C-2.67 30.125 -2.67 30.125 11.562 22.625 C13.702 18.718 14.415 15.745 13.375 11.375 C10.396 6.229 5.057 3.776 0 1 C0 0.67 0 0.34 0 0 Z " fill="#4E5BBA" transform="translate(308,444)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.324 1.815 3.647 2.629 3.98 3.469 C5.538 7.381 6.698 9.312 10 12 C17.786 13.79 26.063 13.314 34 13 C34 13.99 34 14.98 34 16 C24.1 16 14.2 16 4 16 C4.33 14.35 4.66 12.7 5 11 C5 10.34 5 9.68 5 9 C5 8.34 5 7.68 5 7 C3.68 7.33 2.36 7.66 1 8 C1.33 6.68 1.66 5.36 2 4 C0.68 3.01 -0.64 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#8188CF" transform="translate(700,304)"/>
<path d="M0 0 C2.143 4.286 2.239 6.238 2.211 10.94 C2.21 11.63 2.209 12.32 2.207 13.03 C2.201 15.304 2.18 17.578 2.16 19.852 C2.154 21.433 2.149 23.014 2.144 24.596 C2.131 28.754 2.107 32.913 2.079 37.071 C2.036 43.734 2.006 50.398 1.983 57.061 C1.973 59.391 1.956 61.721 1.938 64.051 C1.931 65.474 1.925 66.896 1.918 68.319 C1.91 69.564 1.902 70.808 1.893 72.091 C1.797 74.938 1.797 74.938 3 77 C2.562 79.062 2.562 79.062 1 81 C-4.835 82.331 -10.122 81.884 -16 81 C-16 80.67 -16 80.34 -16 80 C-10.72 80 -5.44 80 0 80 C0 53.6 0 27.2 0 0 Z " fill="#1D2CA9" transform="translate(659,238)"/>
<path d="M0 0 C1.263 0.92 2.518 1.851 3.75 2.812 C3.09 3.143 2.43 3.472 1.75 3.812 C-0.548 2.621 -2.682 1.236 -4.863 -0.156 C-7.801 -1.426 -9.206 -0.951 -12.25 -0.188 C-15.062 -1.125 -15.062 -1.125 -17.25 -2.188 C-17.25 -2.518 -17.25 -2.847 -17.25 -3.188 C-18.24 -3.188 -19.23 -3.188 -20.25 -3.188 C-20.25 -2.197 -20.25 -1.208 -20.25 -0.188 C-20.58 -0.847 -20.91 -1.507 -21.25 -2.188 C-22.57 -2.188 -23.89 -2.188 -25.25 -2.188 C-25.58 -1.528 -25.91 -0.868 -26.25 -0.188 C-29.66 -0.974 -32.939 -2.059 -36.25 -3.188 C-28.013 -11.425 -8.385 -5.542 0 0 Z " fill="#5863BF" transform="translate(369.25,216.1875)"/>
<path d="M0 0 C-1.394 0.762 -1.394 0.762 -2.816 1.539 C-19.383 10.702 -28.695 21.521 -34.809 39.566 C-35.723 42.202 -36.667 44.563 -38 47 C-38.534 40.663 -36.78 36.416 -34.062 30.75 C-33.323 29.162 -32.585 27.573 -31.848 25.984 C-31.518 25.291 -31.188 24.597 -30.848 23.882 C-29.892 21.846 -29.892 21.846 -29 19 C-28.361 18.526 -27.721 18.051 -27.062 17.562 C-24.902 16.176 -24.902 16.176 -24.094 14.082 C-22.835 11.685 -21.628 10.691 -19.438 9.125 C-18.38 8.365 -18.38 8.365 -17.301 7.59 C-4.405 -1.321 -4.405 -1.321 0 0 Z " fill="#4D5ABA" transform="translate(320,194)"/>
<path d="M0 0 C2.53 1.983 4.418 4.202 6 7 C6 7.99 6 8.98 6 10 C-0.93 10 -7.86 10 -15 10 C-13.993 4.964 -13.66 4.105 -10 1 C-6.726 -0.637 -3.51 -0.812 0 0 Z M-13 6 C-13 6.66 -13 7.32 -13 8 C-12.01 8 -11.02 8 -10 8 C-9.67 7.67 -9.34 7.34 -9 7 C-9 7.33 -9 7.66 -9 8 C-4.71 8 -0.42 8 4 8 C3.67 7.34 3.34 6.68 3 6 C2.01 5.67 1.02 5.34 0 5 C-0.66 4.34 -1.32 3.68 -2 3 C-2 2.67 -2 2.34 -2 2 C-7.094 1.526 -9.373 2.373 -13 6 Z " fill="#777ED1" transform="translate(641,639)"/>
<path d="M0 0 C-3.726 1.134 -3.726 1.134 -6.191 -0.074 C-6.974 -0.607 -7.756 -1.139 -8.562 -1.688 C-20.309 -9.122 -32.943 -11.187 -46.625 -11.625 C-47.629 -11.664 -48.633 -11.702 -49.668 -11.742 C-52.112 -11.835 -54.556 -11.921 -57 -12 C-57 -12.33 -57 -12.66 -57 -13 C-35.512 -16.371 -17.859 -11.982 0 0 Z " fill="#5763C4" transform="translate(473,377)"/>
<path d="M0 0 C0.763 0.639 1.526 1.279 2.312 1.938 C7.134 5.637 12.126 5.683 18 5 C20.661 3.924 22.624 2.653 25 1 C25 2.32 25 3.64 25 5 C27.31 5 29.62 5 32 5 C32 4.34 32 3.68 32 3 C32.66 3 33.32 3 34 3 C35 4 35 4 36 7 C31.039 8.203 26.27 8.464 21.25 7.438 C18.845 6.779 18.845 6.779 16 8 C7.783 8.261 7.783 8.261 4 7 C4 6.34 4 5.68 4 5 C3.01 5.33 2.02 5.66 1 6 C0.34 5.34 -0.32 4.68 -1 4 C-1 1 -1 1 0 0 Z " fill="#9298DE" transform="translate(396,664)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.33 10 0.66 10 1 C7.03 1 4.06 1 1 1 C1 12.88 1 24.76 1 37 C3.64 37 6.28 37 9 37 C9.33 30.07 9.66 23.14 10 16 C10.33 16 10.66 16 11 16 C11 23.59 11 31.18 11 39 C7.731 39.817 5.53 40.111 2.25 40.062 C1.451 40.053 0.652 40.044 -0.172 40.035 C-0.775 40.024 -1.378 40.012 -2 40 C-3.097 36.71 -2.8 35.287 -2 32 C-1.67 32 -1.34 32 -1 32 C-0.67 21.44 -0.34 10.88 0 0 Z " fill="#A0A4E8" transform="translate(574,632)"/>
<path d="M0 0 C0.33 0.33 0.66 0.66 1 1 C0.647 7.35 0.647 7.35 -1.699 9.707 C-4.531 11.298 -6.394 11.369 -9.625 11.312 C-10.587 11.309 -11.548 11.305 -12.539 11.301 C-15 11 -15 11 -17 9 C-17.25 6 -17.25 6 -17 3 C-12.755 -1.245 -5.669 -0.357 0 0 Z M-15 3 C-14.814 3.619 -14.629 4.237 -14.438 4.875 C-14 7 -14 7 -15 9 C-12.291 10.354 -9.991 10.065 -7 10 C-7.33 9.34 -7.66 8.68 -8 8 C-7.01 8.33 -6.02 8.66 -5 9 C-4.34 7.35 -3.68 5.7 -3 4 C-2.01 5.485 -2.01 5.485 -1 7 C-0.67 5.35 -0.34 3.7 0 2 C-1.937 1.973 -3.875 1.954 -5.812 1.938 C-7.431 1.92 -7.431 1.92 -9.082 1.902 C-12.065 1.864 -12.065 1.864 -15 3 Z " fill="#777FD2" transform="translate(420,653)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 2.32 2.34 3.64 2 5 C3.32 5.66 4.64 6.32 6 7 C6 8.32 6 9.64 6 11 C4 10 4 10 2 8 C2.005 8.603 2.01 9.206 2.016 9.828 C2.177 31.898 1.607 53.94 1 76 C0.67 76 0.34 76 0 76 C0 50.92 0 25.84 0 0 Z " fill="#A2ABE2" transform="translate(695,421)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.026 0.788 1.052 1.575 1.078 2.387 C1.749 15.266 4.584 28.94 14.281 38.172 C18.614 41.932 18.614 41.932 24 43 C27 45 27 45 29 48 C21.045 47.592 15.106 42.425 9.75 36.938 C2.516 28.433 -0.165 17.623 -0.062 6.688 C-0.053 5.433 -0.044 4.179 -0.035 2.887 C-0.024 1.934 -0.012 0.981 0 0 Z " fill="#3E4CB6" transform="translate(306,247)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.711 1.571 1.423 2.142 1.125 2.73 C-2.5 10.647 -3.499 18.371 -1.234 26.875 C0.506 31.516 2.586 35.342 6 39 C3 39 3 39 1 38 C-0.125 35.938 -0.125 35.938 -1 34 C-1.66 34 -2.32 34 -3 34 C-4 31 -4 31 -4 29 C-4.66 28.01 -5.32 27.02 -6 26 C-5.34 26 -4.68 26 -4 26 C-4.33 25.01 -4.66 24.02 -5 23 C-5.757 14.85 -4.799 8.277 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#5664C2" transform="translate(255,386)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.002 12.675 -7.838 23.501 -17 32 C-20.172 34.6 -23.322 37.161 -27 39 C-27.99 38.67 -28.98 38.34 -30 38 C-29.31 37.527 -28.621 37.054 -27.91 36.566 C-14.09 26.841 -4.991 16.329 0 0 Z " fill="#4F5BBA" transform="translate(494,450)"/>
<path d="M0 0 C4.437 -0.058 8.875 -0.094 13.312 -0.125 C14.575 -0.142 15.838 -0.159 17.139 -0.176 C18.951 -0.185 18.951 -0.185 20.801 -0.195 C21.916 -0.206 23.032 -0.216 24.181 -0.227 C27 0 27 0 30 2 C32.646 1.688 32.646 1.688 35 1 C35.495 2.485 35.495 2.485 36 4 C39.503 5.716 39.503 5.716 43 7 C42.34 7.66 41.68 8.32 41 9 C40.31 8.734 39.621 8.469 38.91 8.195 C29.358 4.605 20.872 2.814 10.671 2.341 C7.022 2.168 3.56 1.835 0 1 C0 0.67 0 0.34 0 0 Z " fill="#606EC9" transform="translate(205,187)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-7.348 10.85 -16.293 15.371 -29.398 15.137 C-32.322 14.983 -35.138 14.619 -38 14 C-38 13.34 -38 12.68 -38 12 C-36.907 12.186 -35.814 12.371 -34.688 12.562 C-31.064 13.258 -31.064 13.258 -28 12 C-28 12.66 -28 13.32 -28 14 C-25.541 13.357 -23.083 12.711 -20.625 12.062 C-19.926 11.88 -19.228 11.698 -18.508 11.51 C-14.66 10.515 -14.66 10.515 -11 9 C-11.33 7.68 -11.66 6.36 -12 5 C-11.113 5.268 -10.226 5.536 -9.312 5.812 C-5.81 6.391 -5.81 6.391 -3.188 3.625 C-2.466 2.759 -1.744 1.892 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#7077C6" transform="translate(612,460)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.257 0.534 2.513 1.067 2.777 1.617 C9.253 14.22 19.603 23.406 32 30 C28.804 31.065 28.03 31.194 25 30 C23.667 28.667 22.333 27.333 21 26 C18.368 24.746 18.368 24.746 16 24 C16 23.01 16 22.02 16 21 C15.34 21 14.68 21 14 21 C13.67 20.34 13.34 19.68 13 19 C11.35 19 9.7 19 8 19 C8.233 18.43 8.467 17.86 8.707 17.273 C9.259 14.902 9.259 14.902 7.637 12.852 C6.993 12.117 6.35 11.382 5.688 10.625 C2.84 7.193 1.021 4.424 0 0 Z " fill="#4C5BBC" transform="translate(152,283)"/>
<path d="M0 0 C4.337 1.321 8.67 2.655 13 4 C14.384 4.427 14.384 4.427 15.797 4.863 C16.586 5.115 17.375 5.366 18.188 5.625 C18.882 5.844 19.577 6.063 20.293 6.289 C22 7 22 7 24 9 C24.375 12.438 24.375 12.438 24 16 C21.746 18.331 20.088 18.971 17 20 C14.667 20.04 12.333 20.044 10 20 C10 19.67 10 19.34 10 19 C13.3 18.01 16.6 17.02 20 16 C20 15.34 20 14.68 20 14 C20.99 14 21.98 14 23 14 C21.478 10.956 20.592 9.296 17.5 7.75 C16.675 7.503 15.85 7.255 15 7 C14.01 6.67 13.02 6.34 12 6 C11.67 6.66 11.34 7.32 11 8 C10.67 7.01 10.34 6.02 10 5 C8.701 4.814 8.701 4.814 7.375 4.625 C4.105 4.019 1.127 3.117 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#6873CB" transform="translate(325,644)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.63 1.66 7.26 2 11 C3.81 10.983 3.81 10.983 5.656 10.965 C7.271 10.955 8.885 10.946 10.5 10.938 C11.687 10.925 11.687 10.925 12.898 10.912 C18.028 10.891 22.923 11.261 28 12 C27.34 13.32 26.68 14.64 26 16 C23 16 23 16 20 15 C20 14.34 20 13.68 20 13 C17.75 12.973 15.5 12.954 13.25 12.938 C11.371 12.92 11.371 12.92 9.453 12.902 C6.715 12.98 4.581 13.172 2 14 C-0.399 9.352 -0.2 5.089 0 0 Z " fill="#5357B8" transform="translate(828,255)"/>
<path d="M0 0 C3.199 1.5 6.016 3.331 8.938 5.312 C18.636 11.503 28.625 14.627 40 16 C39.67 16.66 39.34 17.32 39 18 C24.352 17.352 11.163 11.472 0 2 C0 1.34 0 0.68 0 0 Z " fill="#4752B5" transform="translate(773,303)"/>
<path d="M0 0 C2.728 1.364 2.715 2.649 3.688 5.5 C5.863 11.194 9.086 15.387 13 20 C14.207 21.516 14.207 21.516 15.438 23.062 C15.953 23.702 16.469 24.341 17 25 C14 25 14 25 12.312 23.625 C11 22 11 22 11 20 C10.34 20 9.68 20 9 20 C8.34 18.68 7.68 17.36 7 16 C6.01 16 5.02 16 4 16 C4 15.01 4 14.02 4 13 C3.374 11.318 2.712 9.648 2 8 C0.68 8.33 -0.64 8.66 -2 9 C-2.688 7.312 -2.688 7.312 -3 5 C-1.562 2.25 -1.562 2.25 0 0 Z " fill="#5963C0" transform="translate(757,278)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.99 2 2.98 2 4 C1.34 4 0.68 4 0 4 C0 5.65 0 7.3 0 9 C6.697 10.118 10.975 10.263 17 7 C17.66 6.67 18.32 6.34 19 6 C17.515 10.306 14.931 11.076 11 13 C10.67 13.99 10.34 14.98 10 16 C9.031 15.67 8.061 15.34 7.062 15 C4.379 13.948 4.379 13.948 3 14 C0 12.5 0 12.5 -3 11 C-2.67 10.01 -2.34 9.02 -2 8 C-1.835 7.175 -1.67 6.35 -1.5 5.5 C-1 3 -1 3 0 0 Z " fill="#757FD2" transform="translate(435,672)"/>
<path d="M0 0 C9.555 0.948 17.874 4.058 26.004 9.117 C26.334 8.457 26.664 7.797 27.004 7.117 C29.226 9.339 29.93 11.202 31.004 14.117 C27.306 12.712 23.873 11.03 20.379 9.18 C12.569 5.241 4.675 3.081 -3.996 2.117 C-1.996 0.117 -1.996 0.117 0 0 Z " fill="#616BC6" transform="translate(592.99609375,363.8828125)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.081 2.104 2.139 4.208 2.188 6.312 C2.24 8.07 2.24 8.07 2.293 9.863 C2 13 2 13 0.871 14.902 C-1.84 16.493 -4.009 16.325 -7.125 16.25 C-8.2 16.235 -9.275 16.219 -10.383 16.203 C-13 16 -13 16 -14 15 C-15.682 14.767 -17.371 14.587 -19.062 14.438 C-19.982 14.354 -20.901 14.27 -21.848 14.184 C-22.558 14.123 -23.268 14.062 -24 14 C-24 13.67 -24 13.34 -24 13 C-16.08 13 -8.16 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#7B83CE" transform="translate(609,304)"/>
<path d="M0 0 C1.836 3.324 2.804 6.66 3.812 10.312 C6.987 20.72 12.883 27.049 22 32.938 C22.99 33.463 22.99 33.463 24 34 C22.515 34.495 22.515 34.495 21 35 C11.036 29.859 4.892 21.609 1.062 11.25 C-0.078 7.392 -0.256 4.003 0 0 Z " fill="#4856B8" transform="translate(172,258)"/>
<path d="M0 0 C1.257 0.003 2.514 0.006 3.809 0.01 C5.127 0.018 6.446 0.027 7.805 0.035 C9.145 0.04 10.484 0.045 11.824 0.049 C15.109 0.061 18.395 0.077 21.68 0.098 C21.68 0.428 21.68 0.758 21.68 1.098 C13.1 1.428 4.52 1.758 -4.32 2.098 C-4.65 8.698 -4.98 15.298 -5.32 22.098 C-5.65 21.108 -5.98 20.118 -6.32 19.098 C-6.98 19.098 -7.64 19.098 -8.32 19.098 C-9.008 16.785 -9.008 16.785 -9.32 14.098 C-7.883 12.285 -7.883 12.285 -6.32 11.098 C-6.98 11.098 -7.64 11.098 -8.32 11.098 C-7.66 8.128 -7 5.158 -6.32 2.098 C-6.98 1.768 -7.64 1.438 -8.32 1.098 C-5.502 -0.311 -3.148 -0.014 0 0 Z " fill="#7C86D2" transform="translate(440.3203125,188.90234375)"/>
<path d="M0 0 C4.472 0.54 7.644 1.684 11.562 3.875 C17.844 7.153 24.062 8.724 31 10 C30.67 10.66 30.34 11.32 30 12 C25.793 11.549 21.828 11.009 17.75 9.875 C14.166 9.039 13.282 9.037 10 10 C9.34 10 8.68 10 8 10 C7 7 7 7 7 5 C4.69 3.68 2.38 2.36 0 1 C0 0.67 0 0.34 0 0 Z " fill="#6673CB" transform="translate(256,486)"/>
<path d="M0 0 C3.747 1.249 3.973 2.297 5.812 5.688 C8.413 10.243 11.224 13.404 15 17 C15 17.66 15 18.32 15 19 C15.557 19.248 16.114 19.495 16.688 19.75 C19.4 21.216 21.636 23.03 24 25 C21.525 25.495 21.525 25.495 19 26 C18.752 25.361 18.505 24.721 18.25 24.062 C17.837 23.382 17.425 22.701 17 22 C16.375 21.878 15.75 21.755 15.105 21.629 C12.215 20.766 11.617 19.416 10.062 16.875 C9.297 15.665 9.297 15.665 8.516 14.43 C8.015 13.628 7.515 12.826 7 12 C3.933 6.921 3.933 6.921 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#6872C7" transform="translate(369,462)"/>
<path d="M0 0 C3.504 3.037 4.938 5.485 6 10 C6.075 12.332 6.093 14.669 6 17 C4.907 17.247 3.814 17.495 2.688 17.75 C-1.059 19.02 -2.106 19.658 -4 23 C-4.99 22.67 -5.98 22.34 -7 22 C-6.34 22 -5.68 22 -5 22 C-5 21.01 -5 20.02 -5 19 C-6.338 19.035 -6.338 19.035 -7.703 19.07 C-8.874 19.088 -10.044 19.106 -11.25 19.125 C-12.41 19.148 -13.57 19.171 -14.766 19.195 C-18 19 -18 19 -22 17 C-13.42 17 -4.84 17 4 17 C2.605 5.997 2.605 5.997 0 0 Z " fill="#6A73C9" transform="translate(649,637)"/>
<path d="M0 0 C1.895 0.014 1.895 0.014 3.828 0.027 C4.792 0.039 5.757 0.051 6.75 0.062 C6.75 0.392 6.75 0.723 6.75 1.062 C5.737 1.172 4.724 1.282 3.68 1.395 C2.328 1.554 0.976 1.714 -0.375 1.875 C-1.373 1.98 -1.373 1.98 -2.391 2.088 C-6.91 2.642 -10.268 3.906 -14.25 6.062 C-14.91 6.062 -15.57 6.062 -16.25 6.062 C-16.25 6.723 -16.25 7.382 -16.25 8.062 C-16.91 8.062 -17.57 8.062 -18.25 8.062 C-18.304 9.812 -18.343 11.562 -18.375 13.312 C-18.41 14.774 -18.41 14.774 -18.445 16.266 C-18.249 19.082 -17.696 20.672 -16.25 23.062 C-19.546 20.343 -21.11 18.915 -21.812 14.625 C-20.931 9.04 -18.684 6.168 -14.25 2.75 C-9.372 0.006 -5.594 -0.052 0 0 Z " fill="#4753B6" transform="translate(297.25,386.9375)"/>
<path d="M0 0 C0.959 0.402 0.959 0.402 1.938 0.812 C7.866 3.111 13.662 5.187 20 6 C20 6.33 20 6.66 20 7 C12.755 7.534 8.271 6.716 2 3 C1.67 4.98 1.34 6.96 1 9 C5.95 10.98 5.95 10.98 11 13 C11 13.66 11 14.32 11 15 C5.528 14.349 0.965 12.965 -3 9 C-2.01 6.03 -1.02 3.06 0 0 Z " fill="#5564C3" transform="translate(320,656)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.967 1.026 -1.934 1.052 -2.93 1.078 C-10.817 1.422 -18.008 1.883 -25 6 C-25.33 6.66 -25.66 7.32 -26 8 C-26.742 8.433 -27.485 8.866 -28.25 9.312 C-31.311 11.191 -33.539 13.399 -36 16 C-35.68 12.832 -35.266 11.293 -33.102 8.902 C-22.173 0.232 -13.855 -1.861 0 0 Z " fill="#6F78CB" transform="translate(587,388)"/>
<path d="M0 0 C3.016 2.838 5.292 5.732 7.531 9.211 C8.5 10.708 8.5 10.708 9.488 12.234 C10.152 13.271 10.816 14.307 11.5 15.375 C12.823 17.425 14.146 19.474 15.469 21.523 C16.081 22.481 16.693 23.438 17.324 24.425 C18.679 26.506 20.082 28.524 21.531 30.539 C22.016 31.351 22.501 32.163 23 33 C22.67 33.99 22.34 34.98 22 36 C21.457 35.145 20.915 34.291 20.355 33.41 C19.64 32.305 18.925 31.201 18.188 30.062 C17.48 28.96 16.772 27.858 16.043 26.723 C14.222 23.758 14.222 23.758 11 23 C10.918 22.299 10.835 21.598 10.75 20.875 C9.832 17.354 8.052 14.978 6 12 C6 11.34 6 10.68 6 10 C5.34 9.67 4.68 9.34 4 9 C2.93 7.441 2.93 7.441 1.875 5.562 C1.522 4.945 1.169 4.328 0.805 3.691 C0 2 0 2 0 0 Z " fill="#4750B3" transform="translate(698,422)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-6.102 11.59 -13.769 17.846 -23 24 C-23.33 24.66 -23.66 25.32 -24 26 C-24.66 24.68 -25.32 23.36 -26 22 C-25.154 21.504 -24.309 21.007 -23.438 20.496 C-13.989 14.872 -6.105 9.348 0 0 Z " fill="#5B65C3" transform="translate(637,469)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.268 3.73 4.043 4.874 3.062 7.75 C2.712 8.492 2.361 9.235 2 10 C0.36 10.186 0.36 10.186 -1.312 10.375 C-5.074 10.755 -5.074 10.755 -8 13 C-8.587 16.346 -8.587 16.346 -8.688 20.125 C-8.753 21.406 -8.819 22.688 -8.887 24.008 C-8.924 24.995 -8.961 25.983 -9 27 C-9.33 27 -9.66 27 -10 27 C-10.027 24.021 -10.047 21.042 -10.062 18.062 C-10.071 17.212 -10.079 16.362 -10.088 15.486 C-10.091 14.677 -10.094 13.869 -10.098 13.035 C-10.103 12.286 -10.108 11.537 -10.114 10.766 C-10 9 -10 9 -9 8 C-7.314 7.928 -5.625 7.916 -3.938 7.938 C-3.018 7.947 -2.099 7.956 -1.152 7.965 C-0.442 7.976 0.268 7.988 1 8 C0.67 5.36 0.34 2.72 0 0 Z " fill="#848BD8" transform="translate(520,632)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.188 4.625 3.188 4.625 3 7 C3.66 7.33 4.32 7.66 5 8 C0.38 8 -4.24 8 -9 8 C-9.33 7.34 -9.66 6.68 -10 6 C-9.34 6 -8.68 6 -8 6 C-7.67 4.68 -7.34 3.36 -7 2 C-7.66 1.67 -8.32 1.34 -9 1 C-5.745 -0.085 -3.41 -0.213 0 0 Z M-4 2 C-4.99 3.485 -4.99 3.485 -6 5 C-5.67 5.66 -5.34 6.32 -5 7 C-3.35 7 -1.7 7 0 7 C-0.33 6.34 -0.66 5.68 -1 5 C-0.67 4.01 -0.34 3.02 0 2 C-1.32 2 -2.64 2 -4 2 Z " fill="#5F6AC6" transform="translate(415,639)"/>
<path d="M0 0 C-2.972 1.583 -5.924 2.656 -9.125 3.688 C-18.77 7.173 -25.721 12.995 -32 21 C-32.33 20.34 -32.66 19.68 -33 19 C-28.683 10.103 -18.861 4.622 -10 1 C-6.6 -0.019 -3.549 -0.081 0 0 Z " fill="#4152B8" transform="translate(289,365)"/>
<path d="M0 0 C4.575 4.118 7.467 8.711 10.562 14 C14.159 20.16 14.159 20.16 18.402 25.875 C19.193 26.927 19.193 26.927 20 28 C19.67 28.99 19.34 29.98 19 31 C16.285 27.073 13.58 23.141 10.882 19.202 C9.963 17.863 9.042 16.525 8.118 15.189 C6.791 13.27 5.473 11.345 4.156 9.418 C3.741 8.821 3.325 8.224 2.897 7.609 C0 3.342 0 3.342 0 0 Z " fill="#4D58B9" transform="translate(686,236)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.015 0.623 1.029 1.247 1.044 1.889 C1.357 13.9 1.792 25.357 5 37 C4.34 37 3.68 37 3 37 C3 36.01 3 35.02 3 34 C2.34 33.67 1.68 33.34 1 33 C0.073 29.041 -0.274 24.973 -0.75 20.938 C-0.976 19.256 -0.976 19.256 -1.207 17.541 C-1.985 11.108 -2.081 6.16 0 0 Z " fill="#5461BF" transform="translate(281,242)"/>
<path d="M0 0 C2 2 2 2 2.195 4.383 C2.172 5.288 2.149 6.193 2.125 7.125 C2.107 8.035 2.089 8.945 2.07 9.883 C2.047 10.581 2.024 11.28 2 12 C2.33 12 2.66 12 3 12 C3.66 14.64 4.32 17.28 5 20 C3.68 19.67 2.36 19.34 1 19 C-0.114 19.186 -1.227 19.371 -2.375 19.562 C-6.266 20.032 -8.423 19.483 -12 18 C-11.34 17.34 -10.68 16.68 -10 16 C-6.619 16.033 -3.36 16.569 0 17 C0 11.39 0 5.78 0 0 Z " fill="#A0A2E7" transform="translate(699,652)"/>
<path d="M0 0 C6.268 2.79 11.065 8.491 15 14 C15 14.66 15 15.32 15 16 C15.66 16.33 16.32 16.66 17 17 C18.324 19.582 18.324 19.582 19.688 22.812 C20.145 23.871 20.603 24.929 21.074 26.02 C21.995 28.984 22.129 30.175 21 33 C20.671 32.129 20.343 31.257 20.004 30.359 C15.498 19.077 9.8 9.539 0 2 C0 1.34 0 0.68 0 0 Z " fill="#3642AD" transform="translate(624,378)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.694 2.471 5.27 4.937 6.812 7.5 C7.239 8.196 7.665 8.892 8.104 9.609 C8.967 11.021 9.827 12.435 10.683 13.852 C11.581 15.316 12.496 16.768 13.428 18.211 C13.882 18.915 14.337 19.619 14.805 20.344 C15.218 20.975 15.632 21.606 16.058 22.256 C17.308 24.571 17.403 26.421 17 29 C16.34 29 15.68 29 15 29 C14.67 27.762 14.34 26.525 14 25.25 C12.185 19.681 9.009 15.251 5.645 10.504 C4 8 4 8 4 6 C2.68 6 1.36 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#5E67C0" transform="translate(660,242)"/>
<path d="M0 0 C0.908 0.351 1.815 0.701 2.75 1.062 C5.878 1.965 7.052 2.134 10 1 C10.33 1.99 10.66 2.98 11 4 C13.678 4.99 16.255 5.815 19 6.562 C21.7 7.312 24.338 8.116 27 9 C27.33 9.66 27.66 10.32 28 11 C22.289 10.382 17.176 8.917 11.75 7.062 C10.948 6.798 10.146 6.533 9.32 6.26 C5.828 5.083 3.097 4.065 0 2 C0 1.34 0 0.68 0 0 Z " fill="#5461BF" transform="translate(281,409)"/>
<path d="M0 0 C2 2 2 2 2.812 4.938 C3.752 8.079 3.752 8.079 6.125 9.812 C9.812 11.335 13.028 11.663 17 12 C16.67 12.66 16.34 13.32 16 14 C14.209 14.081 12.417 14.139 10.625 14.188 C9.128 14.24 9.128 14.24 7.602 14.293 C6.743 14.196 5.885 14.1 5 14 C4.34 13.01 3.68 12.02 3 11 C2.01 11.99 1.02 12.98 0 14 C0 10.7 0 7.4 0 4 C-0.66 3.67 -1.32 3.34 -2 3 C-2 2.34 -2 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#6C78CE" transform="translate(368,658)"/>
<path d="M0 0 C8.544 2.579 16.933 7.475 22 15 C21.67 15.66 21.34 16.32 21 17 C20.443 16.196 19.886 15.391 19.312 14.562 C16.781 11.758 15.674 11.306 12 11 C12.33 10.34 12.66 9.68 13 9 C10.03 8.505 10.03 8.505 7 8 C7 7.01 7 6.02 7 5 C4.03 4.01 1.06 3.02 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#626BC3" transform="translate(441,388)"/>
<path d="M0 0 C1.48 0.14 2.959 0.288 4.438 0.438 C5.673 0.559 5.673 0.559 6.934 0.684 C9 1 9 1 10 2 C10.072 3.853 10.084 5.708 10.062 7.562 C10.053 8.574 10.044 9.586 10.035 10.629 C10.024 11.411 10.012 12.194 10 13 C8.68 12.34 7.36 11.68 6 11 C6.33 11.99 6.66 12.98 7 14 C5.68 13.67 4.36 13.34 3 13 C3.66 12.67 4.32 12.34 5 12 C5 11.34 5 10.68 5 10 C2.36 10 -0.28 10 -3 10 C-3 9.67 -3 9.34 -3 9 C0.63 9 4.26 9 8 9 C8 7.02 8 5.04 8 3 C5.03 2.67 2.06 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#8A92DD" transform="translate(379,631)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.385 11.244 0.38 21.395 -6 31 C-8.164 32.785 -8.164 32.785 -10 34 C-8.886 30.936 -7.562 28.214 -5.875 25.438 C-3.681 22.067 -3.681 22.067 -4 18 C-3.34 18 -2.68 18 -2 18 C-1.34 12.06 -0.68 6.12 0 0 Z " fill="#4653B7" transform="translate(623,427)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 14.19 1 28.38 1 43 C-1.833 40.167 -2.719 38.646 -4 35 C-3.01 35.33 -2.02 35.66 -1 36 C-0.67 24.12 -0.34 12.24 0 0 Z " fill="#1C2EB3" transform="translate(710,232)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 1.66 5 2.32 5 3 C-2.495 5.447 -10.175 5.631 -18 5 C-18.99 4.34 -19.98 3.68 -21 3 C-19 1 -19 1 -15.875 0.875 C-14.926 0.916 -13.978 0.957 -13 1 C-13 1.99 -13 2.98 -13 4 C-11.762 3.835 -10.525 3.67 -9.25 3.5 C-6.422 3.123 -3.884 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#727BCD" transform="translate(357,292)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.027 3.063 3.047 6.125 3.062 9.188 C3.071 10.052 3.079 10.916 3.088 11.807 C3.104 16.003 3.076 19.909 2 24 C1.34 24 0.68 24 0 24 C-1.103 20.692 -0.821 20.192 0 17 C0.038 15 0.045 12.999 0 11 C0.66 11 1.32 11 2 11 C2 10.01 2 9.02 2 8 C1.34 7.67 0.68 7.34 0 7 C0 4.69 0 2.38 0 0 Z " fill="#999EDB" transform="translate(432,248)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.977 4.164 1.977 4.164 1.625 6.625 C1.459 7.851 1.459 7.851 1.289 9.102 C1.146 10.041 1.146 10.041 1 11 C2.98 11 4.96 11 7 11 C7 11.33 7 11.66 7 12 C4.69 12 2.38 12 0 12 C0 9.03 0 6.06 0 3 C-2.31 3.33 -4.62 3.66 -7 4 C-7.33 6.64 -7.66 9.28 -8 12 C-9.65 12 -11.3 12 -13 12 C-12.01 11.34 -11.02 10.68 -10 10 C-9.42 7.805 -9.42 7.805 -9.312 5.375 C-9.214 4.149 -9.214 4.149 -9.113 2.898 C-9.057 1.959 -9.057 1.959 -9 1 C-7.544 0.778 -6.085 0.573 -4.625 0.375 C-3.407 0.201 -3.407 0.201 -2.164 0.023 C-1.45 0.016 -0.736 0.008 0 0 Z " fill="#515FC4" transform="translate(377,622)"/>
<path d="M0 0 C1 1 1 1 1.098 3.066 C1.086 3.89 1.074 4.714 1.062 5.562 C1.053 6.389 1.044 7.215 1.035 8.066 C1.024 8.704 1.012 9.343 1 10 C4.465 9.505 4.465 9.505 8 9 C8.33 9.66 8.66 10.32 9 11 C5.7 11 2.4 11 -1 11 C-1 8.36 -1 5.72 -1 3 C-3.97 3 -6.94 3 -10 3 C-10 2.34 -10 1.68 -10 1 C-6.747 -0.627 -3.608 -0.115 0 0 Z " fill="#636FC7" transform="translate(511,623)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C-0.98 8 -2.96 8 -5 8 C-5 9.98 -5 11.96 -5 14 C-3.35 14 -1.7 14 0 14 C-0.33 14.99 -0.66 15.98 -1 17 C-2.98 16.67 -4.96 16.34 -7 16 C-8.231 12.306 -7.644 9.794 -7 6 C-5.02 6 -3.04 6 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#737BD1" transform="translate(502,626)"/>
<path d="M0 0 C2.858 0.569 5.71 1.154 8.562 1.75 C9.362 1.907 10.162 2.065 10.986 2.227 C16.345 3.364 20.379 5.048 25 8 C24.34 8.66 23.68 9.32 23 10 C22.166 9.662 21.332 9.325 20.473 8.977 C12.767 5.912 12.767 5.912 4.75 3.875 C3.512 3.586 2.275 3.298 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#606EC6" transform="translate(357,187)"/>
<path d="M0 0 C7.59 0 15.18 0 23 0 C23.33 0.66 23.66 1.32 24 2 C18.06 2 12.12 2 6 2 C5.67 2.66 5.34 3.32 5 4 C5 3.34 5 2.68 5 2 C3.35 2 1.7 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#505CBF" transform="translate(746,495)"/>
<path d="M0 0 C4.841 1.139 6.921 3.896 9.641 7.801 C13.962 14.792 14.404 20.857 14 29 C13.67 29 13.34 29 13 29 C12.914 28.25 12.827 27.5 12.738 26.727 C11.232 15.757 7.537 8.063 0 0 Z " fill="#3B4BB5" transform="translate(335,431)"/>
<path d="M0 0 C0.763 0.639 1.526 1.279 2.312 1.938 C6.351 5.036 9.234 5.116 14.188 5.062 C15.274 5.053 16.361 5.044 17.48 5.035 C18.312 5.024 19.143 5.012 20 5 C19.67 5.66 19.34 6.32 19 7 C14.197 8.601 8.835 8.612 4 7 C4 6.34 4 5.68 4 5 C3.01 5.33 2.02 5.66 1 6 C0.34 5.34 -0.32 4.68 -1 4 C-1 1 -1 1 0 0 Z " fill="#6A75CB" transform="translate(396,664)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.129 3.825 -0.824 5.611 -1.812 7.375 C-5.01 13.504 -6.297 19.163 -7 26 C-9.249 22.626 -9.155 21.223 -8.543 17.32 C-7.022 10.823 -4.592 4.921 0 0 Z " fill="#5762BD" transform="translate(550,402)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19.33 0.99 19.66 1.98 20 3 C16.896 3.168 13.792 3.334 10.688 3.5 C9.806 3.548 8.925 3.595 8.018 3.645 C6.746 3.712 6.746 3.712 5.449 3.781 C4.669 3.823 3.889 3.865 3.085 3.908 C1.391 3.983 -0.305 4 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#7479CF" transform="translate(670,363)"/>
<path d="M0 0 C3 0 3 0 6 2 C6 2.99 6 3.98 6 5 C3.36 5 0.72 5 -2 5 C-2 10.61 -2 16.22 -2 22 C-2.66 21.34 -3.32 20.68 -4 20 C-3.983 17.08 -3.861 14.277 -3.625 11.375 C-3.568 10.573 -3.512 9.771 -3.453 8.945 C-3.312 6.963 -3.157 4.981 -3 3 C-2.01 2.67 -1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6069C8" transform="translate(693,616)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 7.59 1 15.18 1 23 C0.67 23 0.34 23 0 23 C-0.66 18.71 -1.32 14.42 -2 10 C-2.99 10 -3.98 10 -5 10 C-4.34 9.67 -3.68 9.34 -3 9 C-2.622 7.679 -2.298 6.342 -2 5 C-1.353 3.326 -0.69 1.657 0 0 Z " fill="#848FD8" transform="translate(746,430)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-0.918 2.436 -1.836 2.871 -2.781 3.32 C-9.693 6.656 -16.105 10.002 -22 15 C-22 14.01 -22 13.02 -22 12 C-16.552 7.04 -7.798 0 0 0 Z " fill="#5A64BE" transform="translate(797,191)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C4.599 4.045 5.576 6.834 4 11 C1.265 13.735 -1.215 14.474 -5 15 C-5 14.34 -5 13.68 -5 13 C0.75 10 0.75 10 3 10 C2.67 8.02 2.34 6.04 2 4 C1.01 4 0.02 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#7179D0" transform="translate(648,656)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 7.92 1 15.84 1 24 C-4.625 26.25 -4.625 26.25 -9 25 C-8.67 24.34 -8.34 23.68 -8 23 C-5.36 23 -2.72 23 0 23 C0 15.41 0 7.82 0 0 Z " fill="#8E93E0" transform="translate(610,646)"/>
<path d="M0 0 C4.847 0.591 8.607 1.979 13 4 C13 5.65 13 7.3 13 9 C12.34 9 11.68 9 11 9 C10.34 10.65 9.68 12.3 9 14 C8.34 14 7.68 14 7 14 C7.99 11.03 8.98 8.06 10 5 C6.37 4.01 2.74 3.02 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#6977D1" transform="translate(345,619)"/>
<path d="M0 0 C2.805 2.478 4.33 5.203 6.062 8.5 C7.618 11.444 9.151 14.227 11 17 C10.34 17.66 9.68 18.32 9 19 C8.529 18.145 8.059 17.291 7.574 16.41 C6.952 15.305 6.329 14.201 5.688 13.062 C4.765 11.409 4.765 11.409 3.824 9.723 C2.225 6.735 2.225 6.735 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#6A75C9" transform="translate(285,279)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.67 2.639 1.34 3.279 1 3.938 C-0.733 9.246 -0.517 14.646 1 20 C0.01 20.66 -0.98 21.32 -2 22 C-2 21.34 -2 20.68 -2 20 C-2.66 20 -3.32 20 -4 20 C-3.814 19.113 -3.629 18.226 -3.438 17.312 C-3.02 14.148 -3.192 12.052 -4 9 C-3.34 9 -2.68 9 -2 9 C-1.783 7.329 -1.783 7.329 -1.562 5.625 C-1 2 -1 2 0 0 Z " fill="#6D74CC" transform="translate(620,640)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.483 1.459 0.966 1.918 0.434 2.391 C-0.554 3.311 -0.554 3.311 -1.562 4.25 C-2.224 4.853 -2.885 5.457 -3.566 6.078 C-5.224 7.877 -5.224 7.877 -4.906 10.062 C-3.873 12.271 -2.863 13.275 -0.938 14.75 C0.517 15.864 0.517 15.864 2 17 C1.34 17.33 0.68 17.66 0 18 C-2.97 15.69 -5.94 13.38 -9 11 C-7.831 6.326 -6.742 5.762 -3 3 C-1.983 2.018 -0.974 1.025 0 0 Z " fill="#4D5DC0" transform="translate(256,468)"/>
<path d="M0 0 C0.699 0.137 1.397 0.273 2.117 0.414 C7.216 1.203 11.905 0.729 16.992 0.039 C16.992 1.029 16.992 2.019 16.992 3.039 C9.402 3.039 1.812 3.039 -6.008 3.039 C-2.008 0.039 -2.008 0.039 0 0 Z " fill="#6167C8" transform="translate(752.0078125,362.9609375)"/>
<path d="M0 0 C0.639 0.701 1.279 1.402 1.938 2.125 C5.498 5.716 9.748 8.304 14 11 C14.66 10.67 15.32 10.34 16 10 C18 12 18 12 20 15 C12.776 14.63 7.07 10.109 1.938 5.312 C0 3 0 3 0 0 Z " fill="#5A65C0" transform="translate(315,280)"/>
<path d="M0 0 C1.549 2.324 2.744 4.44 3.938 6.938 C5.382 9.875 6.894 12.473 9 15 C6 15 6 15 4 14 C2.875 11.938 2.875 11.938 2 10 C1.34 10 0.68 10 0 10 C-1 7 -1 7 -1 5 C-1.66 4.01 -2.32 3.02 -3 2 C-2.01 1.34 -1.02 0.68 0 0 Z " fill="#6A78CA" transform="translate(252,410)"/>
<path d="M0 0 C2.248 2.052 3.002 3.007 4 6 C3.73 8.664 3.73 8.664 3.188 11.625 C3.016 12.607 2.845 13.59 2.668 14.602 C2.448 15.393 2.227 16.185 2 17 C1.34 17.33 0.68 17.66 0 18 C0 15.69 0 13.38 0 11 C-0.33 11 -0.66 11 -1 11 C-1.143 3.571 -1.143 3.571 0 0 Z M1 5 C2 7 2 7 2 7 Z " fill="#3E49B1" transform="translate(753,229)"/>
<path d="M0 0 C7.623 0.968 13.45 4.107 20 8 C20 8.99 20 9.98 20 11 C19.082 10.518 18.164 10.036 17.219 9.539 C10.905 6.272 5.061 3.271 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#5E67C1" transform="translate(837,189)"/>
<path d="M0 0 C7.269 1.42 14.554 6.117 20 11 C15.515 10.367 11.757 8.637 7.75 6.625 C7.113 6.321 6.476 6.017 5.82 5.703 C4.171 4.882 2.58 3.947 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#6974C9" transform="translate(453,367)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.228 2.018 1.456 4.036 1.684 6.055 C1.913 8.074 1.913 8.074 3 10 C2.562 12.062 2.562 12.062 1 14 C-4.835 15.331 -10.122 14.884 -16 14 C-16 13.67 -16 13.34 -16 13 C-10.72 13 -5.44 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#4A55BA" transform="translate(659,305)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 1.66 4 2.32 4 3 C4.572 3.262 5.145 3.523 5.734 3.793 C8.166 5.089 10.136 6.547 12.25 8.312 C12.956 8.886 13.663 9.46 14.391 10.051 C16 12 16 12 16.234 14.73 C16.118 15.854 16.118 15.854 16 17 C15.737 16.401 15.474 15.803 15.204 15.186 C13.937 12.886 12.755 11.68 10.742 10.02 C10.117 9.496 9.492 8.972 8.848 8.432 C8.197 7.897 7.546 7.363 6.875 6.812 C4.563 4.896 2.274 2.962 0 1 C0 0.67 0 0.34 0 0 Z " fill="#525EBD" transform="translate(255,280)"/>
<path d="M0 0 C4.342 0.595 7.481 2.175 11.25 4.375 C12.307 4.981 13.364 5.587 14.453 6.211 C17 8 17 8 18 11 C13.931 9.601 10.534 7.547 6.938 5.238 C5.316 4.202 3.662 3.219 2 2.25 C1.34 1.837 0.68 1.425 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5260BE" transform="translate(324,369)"/>
<path d="M0 0 C2.449 3.673 2.315 5.664 2 10 C1.34 10.66 0.68 11.32 0 12 C-2.263 11.646 -3.951 11.025 -6 10 C-5.01 9.34 -4.02 8.68 -3 8 C-2.75 5.416 -2.75 5.416 -3 3 C-2.34 3 -1.68 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#969CE3" transform="translate(660,645)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.99 2 2.98 2 4 C1.34 4 0.68 4 0 4 C0 5.65 0 7.3 0 9 C1.32 9.66 2.64 10.32 4 11 C3.67 11.99 3.34 12.98 3 14 C0 12.5 0 12.5 -3 11 C-2.67 10.01 -2.34 9.02 -2 8 C-1.835 7.175 -1.67 6.35 -1.5 5.5 C-1 3 -1 3 0 0 Z " fill="#7C85D8" transform="translate(435,672)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C0.68 5.63 -0.64 9.26 -2 13 C-2.66 13 -3.32 13 -4 13 C-4.33 12.01 -4.66 11.02 -5 10 C-4.34 9.01 -3.68 8.02 -3 7 C-3.66 5.68 -4.32 4.36 -5 3 C-3.35 2.67 -1.7 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7A83D3" transform="translate(457,647)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 6.27 1 12.54 1 19 C0.34 19.33 -0.32 19.66 -1 20 C-0.979 19.01 -0.959 18.02 -0.938 17 C-0.921 14.217 -1.181 11.62 -1.625 8.875 C-1.994 5.057 -1.767 3.325 0 0 Z " fill="#BBBFF2" transform="translate(529,636)"/>
<path d="M0 0 C3.634 1.781 7.189 3.702 10.75 5.625 C11.352 5.95 11.954 6.275 12.574 6.609 C14.05 7.406 15.525 8.203 17 9 C14.45 9.95 13.349 10.14 10.781 9.109 C10.028 8.661 9.276 8.212 8.5 7.75 C7.747 7.312 6.994 6.873 6.219 6.422 C4.048 5.031 1.97 3.665 0 2 C0 1.34 0 0.68 0 0 Z " fill="#4352B7" transform="translate(260,425)"/>
<path d="M0 0 C3.329 3.049 5.504 6.219 7.75 10.125 C8.67 11.707 8.67 11.707 9.609 13.32 C11 16 11 16 11 18 C8 17 8 17 6.465 14.453 C5.961 13.396 5.457 12.339 4.938 11.25 C4.431 10.203 3.924 9.157 3.402 8.078 C2.187 5.41 1.058 2.734 0 0 Z " fill="#3D49B1" transform="translate(489,239)"/>
<path d="M0 0 C5.286 -0.503 7.91 0.653 12 4 C13.375 6.25 13.375 6.25 14 8 C13.34 8.66 12.68 9.32 12 10 C12 9.01 12 8.02 12 7 C11.34 7 10.68 7 10 7 C10 6.01 10 5.02 10 4 C9.362 3.879 8.724 3.758 8.066 3.633 C7.24 3.465 6.414 3.298 5.562 3.125 C4.739 2.963 3.915 2.8 3.066 2.633 C2.384 2.424 1.703 2.215 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#6971CA" transform="translate(677,639)"/>
<path d="M0 0 C2.406 2.185 3.795 4.259 5.188 7.188 C5.532 7.903 5.876 8.618 6.23 9.355 C6.484 9.898 6.738 10.441 7 11 C6.01 11 5.02 11 4 11 C3.34 9.02 2.68 7.04 2 5 C1.34 5 0.68 5 0 5 C-0.495 6.98 -0.495 6.98 -1 9 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#7780CA" transform="translate(461,239)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.31 1.66 4.62 2 7 C3.98 6.67 5.96 6.34 8 6 C8.33 6.66 8.66 7.32 9 8 C6.517 9.173 4.579 10 1.812 10 C0 9 0 9 -1.375 6.125 C-1.684 4.578 -1.684 4.578 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#6B78D0" transform="translate(398,634)"/>
<path d="M0 0 C4.122 3.768 7.631 7.889 10 13 C9.67 13.66 9.34 14.32 9 15 C6.556 12.71 4.685 10.273 2.812 7.5 C2.283 6.727 1.753 5.953 1.207 5.156 C0 3 0 3 0 0 Z " fill="#4550B5" transform="translate(736,480)"/>
<path d="M0 0 C2.75 -0.25 2.75 -0.25 6 0 C8.375 2 8.375 2 10 4 C9.67 4.99 9.34 5.98 9 7 C8.423 6.526 7.845 6.051 7.25 5.562 C5.051 4.036 3.629 3.351 1 3 C0.01 3.495 0.01 3.495 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#9198D9" transform="translate(369,220)"/>
<path d="M0 0 C5.17 1.41 8.753 4.863 12 9 C12.703 11.227 12.703 11.227 13 13 C12.34 12.319 11.68 11.639 11 10.938 C8.011 7.931 4.803 5.198 1.574 2.453 C1.055 1.974 0.535 1.494 0 1 C0 0.67 0 0.34 0 0 Z " fill="#4C57B8" transform="translate(471,378)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 9.57 1 19.14 1 29 C0.67 29 0.34 29 0 29 C0 19.43 0 9.86 0 0 Z " fill="#CECFF7" transform="translate(632,249)"/>
<path d="M0 0 C3.375 4.5 3.375 4.5 4 8 C3.01 7.67 2.02 7.34 1 7 C1 10.63 1 14.26 1 18 C0.67 18 0.34 18 0 18 C0 12.06 0 6.12 0 0 Z " fill="#27309D" transform="translate(694,414)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.491 5.98 -2.557 9.243 -6 13 C-6.66 12.67 -7.32 12.34 -8 12 C-7.506 11.385 -7.013 10.77 -6.504 10.137 C-5.543 8.924 -5.543 8.924 -4.562 7.688 C-3.924 6.887 -3.286 6.086 -2.629 5.262 C-0.839 3.013 -0.839 3.013 0 0 Z " fill="#606BC2" transform="translate(533,381)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C12 0.99 12 1.98 12 3 C10.376 3.081 8.75 3.139 7.125 3.188 C5.768 3.24 5.768 3.24 4.383 3.293 C3.203 3.148 3.203 3.148 2 3 C1.34 2.01 0.68 1.02 0 0 Z " fill="#A5A8E7" transform="translate(555,669)"/>
<path d="M0 0 C1.858 0.09 3.712 0.246 5.562 0.438 C7.08 0.59 7.08 0.59 8.629 0.746 C9.803 0.872 9.803 0.872 11 1 C10.67 1.99 10.34 2.98 10 4 C8.541 3.886 7.083 3.758 5.625 3.625 C4.407 3.521 4.407 3.521 3.164 3.414 C1 3 1 3 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8B91D8" transform="translate(688,668)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.28 1 10.56 1 16 C-1.852 13.148 -1.739 10.98 -2 7 C-1.529 4.547 -0.822 2.37 0 0 Z " fill="#B1B3EB" transform="translate(689,620)"/>
<path d="M0 0 C1.114 1.083 1.114 1.083 2.25 2.188 C2.25 2.847 2.25 3.508 2.25 4.188 C1.26 4.518 0.27 4.847 -0.75 5.188 C-3.608 3.455 -4.867 2.013 -6.75 -0.812 C-3.603 -2.211 -2.966 -1.955 0 0 Z " fill="#7872DA" transform="translate(830.75,931.8125)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3 2.99 3 3.98 3 5 C3.66 5.33 4.32 5.66 5 6 C1.7 6 -1.6 6 -5 6 C-4.34 5.01 -3.68 4.02 -3 3 C-2.34 3 -1.68 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#BFC1F5" transform="translate(415,641)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.099 6.111 1.071 11.974 0 18 C-0.66 17.67 -1.32 17.34 -2 17 C-1.34 13.04 -0.68 9.08 0 5 C-0.66 5 -1.32 5 -2 5 C-1.34 3.35 -0.68 1.7 0 0 Z " fill="#D0D1F7" transform="translate(514,426)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.217 2.172 0.423 3.338 -0.375 4.5 C-0.816 5.15 -1.257 5.799 -1.711 6.469 C-2.136 6.974 -2.562 7.479 -3 8 C-3.66 8 -4.32 8 -5 8 C-5.66 9.32 -6.32 10.64 -7 12 C-7.66 11.34 -8.32 10.68 -9 10 C-8.01 9.67 -7.02 9.34 -6 9 C-5.296 7.686 -4.637 6.348 -4 5 C-2.695 3.311 -1.366 1.64 0 0 Z " fill="#5760BF" transform="translate(872,293)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C9.34 1.32 8.68 2.64 8 4 C5 4 5 4 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#D9D9FA" transform="translate(846,267)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-2.01 3.934 -2.867 4.044 -6 3 C-8.009 3.276 -10.012 3.602 -12 4 C-8.777 -0.834 -5.447 -0.103 0 0 Z " fill="#BCBFF4" transform="translate(343,628)"/>
<path d="M0 0 C4.29 0.33 8.58 0.66 13 1 C13 1.99 13 2.98 13 4 C12.67 3.34 12.34 2.68 12 2 C10.68 2 9.36 2 8 2 C7.67 2.66 7.34 3.32 7 4 C3.381 3.376 2.829 2.829 0 0 Z " fill="#CDCEF9" transform="translate(336,212)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-2.082 3.762 -2.082 3.762 -4.812 5.688 C-5.706 6.331 -6.599 6.974 -7.52 7.637 C-8.338 8.087 -9.157 8.536 -10 9 C-10.99 8.67 -11.98 8.34 -13 8 C-11.213 6.661 -9.42 5.329 -7.625 4 C-6.627 3.257 -5.63 2.515 -4.602 1.75 C-2 0 -2 0 0 0 Z " fill="#3D4AB4" transform="translate(477,480)"/>
<path d="M0 0 C1.175 0.927 2.34 1.867 3.5 2.812 C4.15 3.335 4.799 3.857 5.469 4.395 C7 6 7 6 7 9 C6.402 8.691 5.804 8.381 5.188 8.062 C2.91 6.898 2.91 6.898 0 6 C0 4 0 2 0 0 Z " fill="#6D7ACC" transform="translate(160,295)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.026 0.771 1.052 1.542 1.078 2.336 C1.135 3.339 1.192 4.342 1.25 5.375 C1.296 6.373 1.343 7.37 1.391 8.398 C1.722 11.153 1.722 11.153 4.047 12.352 C4.691 12.566 5.336 12.78 6 13 C4.68 13.33 3.36 13.66 2 14 C-0.399 9.352 -0.2 5.089 0 0 Z " fill="#898FD6" transform="translate(828,255)"/>
<path d="M0 0 C0 1.32 0 2.64 0 4 C-0.639 4.289 -1.279 4.577 -1.938 4.875 C-4.152 5.828 -4.152 5.828 -5 8 C-5.66 8 -6.32 8 -7 8 C-6.444 4.109 -4.341 0 0 0 Z " fill="#6D76C8" transform="translate(406,395)"/>
<path d="M0 0 C0 3.977 -1.38 5.089 -4 8 C-6.25 9.812 -6.25 9.812 -8 11 C-8.66 10.67 -9.32 10.34 -10 10 C-6.7 6.7 -3.4 3.4 0 0 Z " fill="#3142B2" transform="translate(269,295)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.63 1 7.26 1 11 C-1.833 8.167 -2.719 6.646 -4 3 C-3.01 3.33 -2.02 3.66 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#3C45AD" transform="translate(710,264)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C9.67 1.32 9.34 2.64 9 4 C8.67 3.67 8.34 3.34 8 3 C6.845 2.856 5.69 2.711 4.5 2.562 C3.345 2.377 2.19 2.191 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#707ACD" transform="translate(463,632)"/>
<path d="M0 0 C0.908 0.351 1.815 0.701 2.75 1.062 C5.878 1.965 7.052 2.134 10 1 C10 1.66 10 2.32 10 3 C9.34 3 8.68 3 8 3 C8 3.66 8 4.32 8 5 C4.625 4.453 2.918 3.945 0 2 C0 1.34 0 0.68 0 0 Z " fill="#767FCE" transform="translate(281,409)"/>
<path d="M0 0 C1.875 0.062 1.875 0.062 4 1 C6.25 3.562 6.25 3.562 8 6 C7.67 6.66 7.34 7.32 7 8 C6.443 7.196 5.886 6.391 5.312 5.562 C2.781 2.758 1.674 2.306 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#6C74C9" transform="translate(455,397)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-1.3 3.64 -4.6 6.28 -8 9 C-8 8.01 -8 7.02 -8 6 C-6.25 4.395 -6.25 4.395 -4 2.812 C-3.258 2.283 -2.515 1.753 -1.75 1.207 C-1.173 0.809 -0.595 0.41 0 0 Z " fill="#4350B4" transform="translate(544,374)"/>
<path d="M0 0 C4.427 1.391 6.994 3.479 10 7 C10 7.99 10 8.98 10 10 C8.33 8.524 6.664 7.044 5 5.562 C4.072 4.739 3.144 3.915 2.188 3.066 C1.466 2.384 0.744 1.703 0 1 C0 0.67 0 0.34 0 0 Z " fill="#3F4BB3" transform="translate(857,199)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.32 1.34 3.64 0.68 5 0 C5.99 1.32 6.98 2.64 8 4 C1.477 4.369 1.477 4.369 -0.938 2.5 C-1.463 1.758 -1.463 1.758 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#6D74CC" transform="translate(675,660)"/>
<path d="M0 0 C2.051 0.033 4.102 0.065 6.152 0.098 C6.152 0.758 6.152 1.418 6.152 2.098 C3.34 2.723 3.34 2.723 0.152 3.098 C-0.838 2.438 -1.828 1.778 -2.848 1.098 C-1.848 0.098 -1.848 0.098 0 0 Z " fill="#7D84D4" transform="translate(603.84765625,668.90234375)"/>
<path d="M0 0 C3 1 3 1 5 4 C5 4.99 5 5.98 5 7 C3.68 7.33 2.36 7.66 1 8 C1.33 6.68 1.66 5.36 2 4 C0.68 3.01 -0.64 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#787FCE" transform="translate(700,304)"/>
<path d="M0 0 C0.99 1.32 1.98 2.64 3 4 C2.01 4.495 2.01 4.495 1 5 C1.33 6.32 1.66 7.64 2 9 C1.01 9.66 0.02 10.32 -1 11 C-1.143 3.571 -1.143 3.571 0 0 Z " fill="#D1D0F8" transform="translate(753,229)"/>
<path d="M0 0 C2.164 0.242 2.164 0.242 4.625 0.875 C5.442 1.079 6.26 1.282 7.102 1.492 C8.041 1.744 8.041 1.744 9 2 C9 2.99 9 3.98 9 5 C5.37 4.01 1.74 3.02 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#6670C6" transform="translate(837,189)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C-1.64 5.66 -4.28 6.32 -7 7 C-7 6.34 -7 5.68 -7 5 C-6.031 4.546 -5.061 4.092 -4.062 3.625 C-0.929 2.342 -0.929 2.342 0 0 Z " fill="#5864C3" transform="translate(556,664)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.67 1.32 3.34 2.64 3 4 C1.68 4.33 0.36 4.66 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#BCBFF8" transform="translate(364,642)"/>
<path d="M0 0 C4.752 0.432 6.92 1.328 10 5 C5.754 5 3.615 3.182 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5D6BC5" transform="translate(256,486)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2.33 2.32 2.66 3 3 C-1.625 7.875 -1.625 7.875 -5 9 C-3.502 5.888 -1.801 2.947 0 0 Z " fill="#6572C8" transform="translate(262,459)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-0.97 4.475 -0.97 4.475 -4 7 C-4.66 6.67 -5.32 6.34 -6 6 C-4 4 -2 2 0 0 Z " fill="#6973C4" transform="translate(612,460)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-4.3 1.67 -7.6 1.34 -11 1 C-6.82 -1.786 -4.853 -0.796 0 0 Z " fill="#6E76C8" transform="translate(813,319)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.906 1.867 0.799 2.718 -0.312 3.562 C-0.927 4.038 -1.542 4.514 -2.176 5.004 C-2.778 5.333 -3.38 5.661 -4 6 C-4.99 5.67 -5.98 5.34 -7 5 C-5.906 4.133 -4.799 3.282 -3.688 2.438 C-3.073 1.962 -2.458 1.486 -1.824 0.996 C-1.222 0.667 -0.62 0.339 0 0 Z " fill="#4856B9" transform="translate(194,214)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.99 2.31 3.98 4.62 5 7 C4.01 7.66 3.02 8.32 2 9 C2 8.01 2 7.02 2 6 C1.34 5.01 0.68 4.02 0 3 C0 2.01 0 1.02 0 0 Z " fill="#6F7CCB" transform="translate(267,209)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.99 8 1.98 8 3 C5.657 2.744 3.322 2.407 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#7A81D1" transform="translate(575,669)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C4.01 1.66 3.02 2.32 2 3 C2 3.66 2 4.32 2 5 C0.68 5.33 -0.64 5.66 -2 6 C-2.33 5.34 -2.66 4.68 -3 4 C-2.34 4 -1.68 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#C0C1F7" transform="translate(408,641)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.99 5 1.98 5 3 C3.02 3.33 1.04 3.66 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#BCBDF7" transform="translate(592,642)"/>
<path d="M0 0 C2.31 0.66 4.62 1.32 7 2 C4.948 4.248 3.993 5.002 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#7F87D2" transform="translate(600,465)"/>
<path d="M0 0 C2.648 2.578 4.944 4.916 7 8 C6.01 8 5.02 8 4 8 C0 3.771 0 3.771 0 0 Z " fill="#5563BD" transform="translate(395,455)"/>
<path d="M0 0 C1 2 1 2 0.312 4.5 C-1.156 7.297 -2.093 7.934 -5 9 C-4.426 5.129 -2.513 2.918 0 0 Z " fill="#5664C5" transform="translate(341,381)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 1.32 2.66 2.64 3 4 C1.02 4.99 1.02 4.99 -1 6 C-1.625 4.125 -1.625 4.125 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#D0CFFA" transform="translate(756,281)"/>
<path d="M0 0 C2.341 2.128 3.707 4.116 5 7 C4.01 7.495 4.01 7.495 3 8 C2.34 7.34 1.68 6.68 1 6 C0.34 5.67 -0.32 5.34 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#6F7ACB" transform="translate(475,264)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C4 3 4 3 4 6 C2.68 6 1.36 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#9196D3" transform="translate(660,242)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C6.461 2.647 7 3.894 7 7 C5.83 6.024 4.664 5.044 3.5 4.062 C2.85 3.517 2.201 2.972 1.531 2.41 C1.026 1.945 0.521 1.479 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5967C2" transform="translate(259,203)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 2.65 3 4.3 3 6 C1.68 6.33 0.36 6.66 -1 7 C-1 6.34 -1 5.68 -1 5 C-0.34 5 0.32 5 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#EAEAF9" transform="translate(385,663)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C-0.32 3.67 -1.64 3.34 -3 3 C-3.33 3.99 -3.66 4.98 -4 6 C-4.66 5.34 -5.32 4.68 -6 4 C-2.25 0 -2.25 0 0 0 Z " fill="#BABCF3" transform="translate(404,644)"/>
<path d="M0 0 C2 1.375 2 1.375 4 3 C4 3.66 4 4.32 4 5 C4.66 5.33 5.32 5.66 6 6 C6 6.66 6 7.32 6 8 C2.246 6.749 1.759 5.435 0 2 C0 1.34 0 0.68 0 0 Z " fill="#555FBC" transform="translate(635,389)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-0.812 4.062 -0.812 4.062 -4 6 C-4.99 5.67 -5.98 5.34 -7 5 C-4.69 3.35 -2.38 1.7 0 0 Z " fill="#6772C5" transform="translate(391,374)"/>
<path d="M0 0 C3.367 1.393 4.986 2.979 7 6 C6.01 6 5.02 6 4 6 C0 1.543 0 1.543 0 0 Z " fill="#6B74C6" transform="translate(767,297)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.66 2 2.32 2 3 C4.31 3.33 6.62 3.66 9 4 C9 4.33 9 4.66 9 5 C6.36 5 3.72 5 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#6670C9" transform="translate(491,292)"/>
<path d="M0 0 C4.875 4.625 4.875 4.625 6 8 C5.01 8.495 5.01 8.495 4 9 C0 3.375 0 3.375 0 0 Z " fill="#5E69C2" transform="translate(686,236)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.28 1 10.56 1 16 C0.67 16 0.34 16 0 16 C0 10.72 0 5.44 0 0 Z " fill="#CCCDF5" transform="translate(632,232)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C4 2.99 4 3.98 4 5 C2.68 5.33 1.36 5.66 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C7C7F8" transform="translate(784,313)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#CCCEF6" transform="translate(632,280)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.67 3.32 2.34 4.64 2 6 C1.34 6 0.68 6 0 6 C-1 3 -1 3 0 0 Z " fill="#7772DA" transform="translate(830,926)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.99 3 1.98 3 3 C3.66 3.33 4.32 3.66 5 4 C3.35 4 1.7 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#B9B9EE" transform="translate(880,240)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 3.31 1.34 5.62 1 8 C0.34 7.67 -0.32 7.34 -1 7 C-1.043 5 -1.041 3 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D1D3FA" transform="translate(513,416)"/>
<path d="M0 0 C-4.75 2 -4.75 2 -7 2 C-7 1.34 -7 0.68 -7 0 C-3.99 -0.934 -3.133 -1.044 0 0 Z " fill="#D2D4FD" transform="translate(299,390)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.96 1 7.92 1 12 C0.67 12 0.34 12 0 12 C0 8.04 0 4.08 0 0 Z " fill="#CACBF4" transform="translate(632,296)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 2.32 1.34 3.64 1 5 C0.34 5 -0.32 5 -1 5 C-1.33 4.01 -1.66 3.02 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#766ED7" transform="translate(884,937)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 2.32 3.34 3.64 3 5 C0 1.125 0 1.125 0 0 Z " fill="#7672DA" transform="translate(822,915)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C2.99 2.934 2.133 3.044 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C5C9F8" transform="translate(397,493)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C7 1.33 7 1.66 7 2 C5.02 2 3.04 2 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CCCDF9" transform="translate(594,362)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C7 1.33 7 1.66 7 2 C4.69 2.33 2.38 2.66 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D1D1F9" transform="translate(824,213)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.66 3 2.32 3 3 C2.01 3.33 1.02 3.66 0 4 C0 2.68 0 1.36 0 0 Z " fill="#7871DA" transform="translate(837,937)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.99 3.33 2.98 3.66 4 4 C2.02 4 0.04 4 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#7871D9" transform="translate(857,928)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 0.99 2.66 1.98 3 3 C1.35 3 -0.3 3 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#C6C9F9" transform="translate(243,317)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2.33 2.32 2.66 3 3 C2.01 3.33 1.02 3.66 0 4 C-0.99 3.34 -1.98 2.68 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CBCEF8" transform="translate(389,308)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.67 1.99 4.34 2.98 4 4 C2.667 2.667 1.333 1.333 0 0 Z " fill="#776FD8" transform="translate(877,929)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.34 1.66 3.68 2.32 3 3 C2.01 3 1.02 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D1D2F8" transform="translate(360,289)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.98 2 3.96 2 6 C1.34 5.67 0.68 5.34 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D1D3FA" transform="translate(384,249)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C-0.062 5.188 -0.062 5.188 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#B6B9F3" transform="translate(553,638)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#DEDCFB" transform="translate(668,392)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.34 0.66 4.68 1.32 4 2 C2.68 1.34 1.36 0.68 0 0 Z " fill="#CCCEF9" transform="translate(584,390)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.625 4.125 1.625 4.125 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#D5D7FB" transform="translate(310,263)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.34 0.66 3.68 1.32 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D1D3F9" transform="translate(202,214)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.99 2 2.98 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#A7B1ED" transform="translate(320,668)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.34 -0.32 4.68 -1 4 C-0.625 1.875 -0.625 1.875 0 0 Z " fill="#BBBBF0" transform="translate(688,649)"/>
<path d="M0 0 C1.32 0.99 2.64 1.98 4 3 C2.68 3 1.36 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C7CAF8" transform="translate(392,489)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.67 3.99 1.34 4.98 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#CFD1FB" transform="translate(363,450)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.01 -0.32 4.02 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#DBD9FA" transform="translate(668,426)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.01 -0.32 4.02 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#DAD7F9" transform="translate(668,410)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C2C3F5" transform="translate(743,385)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.99 1.02 2.98 0 4 C0 2.68 0 1.36 0 0 Z " fill="#D0D1F6" transform="translate(528,384)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.33 6 0.66 6 1 C4.02 1.33 2.04 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C5C4F6" transform="translate(810,322)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1 2 1 2 -1.562 2.062 C-2.769 2.032 -2.769 2.032 -4 2 C-2.125 0.938 -2.125 0.938 0 0 Z " fill="#C0C1F1" transform="translate(845,318)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.02 1.99 2.02 1.99 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CBCEF7" transform="translate(304,309)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C1.01 3.33 0.02 3.66 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#CDCFFA" transform="translate(289,292)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.98 2 3.96 2 6 C1.67 6 1.34 6 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#BAC2F4" transform="translate(144,265)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.99 2 2.98 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D7D9F9" transform="translate(498,248)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.34 -0.32 3.68 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#BEC6F6" transform="translate(145,240)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1.66 1.68 2.32 1 3 C0.34 2.34 -0.32 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CCCBF7" transform="translate(842,220)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C4.02 0.99 4.02 0.99 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#CECEF9" transform="translate(815,213)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.67 0.99 3.34 1.98 3 3 C2.67 2.34 2.34 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#CDD0F9" transform="translate(216,213)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C4C5F2" transform="translate(632,193)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.01 3.495 1.01 3.495 0 4 C-0.33 3.01 -0.66 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#7771DA" transform="translate(822,943)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.66 0.02 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#7872DA" transform="translate(830,938)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 0.99 2.66 1.98 3 3 C2.01 2.67 1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#776FD7" transform="translate(886,929)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.01 2.67 -0.98 2.34 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#7772DA" transform="translate(826,927)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#7771D9" transform="translate(847,920)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.01 4.495 1.01 4.495 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BFBFF4" transform="translate(566,663)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3.66 0.68 4.32 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BDBEF2" transform="translate(541,654)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C2.35 2 0.7 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#BCBEF5" transform="translate(338,651)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.33 6 0.66 6 1 C4.02 1 2.04 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BABFF5" transform="translate(296,500)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1 1.68 1 1 1 C0.67 1.66 0.34 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CED0FB" transform="translate(562,465)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.01 3.495 2.01 3.495 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#D4D6FB" transform="translate(704,435)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 2.485 2.01 2.485 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#CED0FA" transform="translate(600,395)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.68 1.33 1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CFD0FB" transform="translate(579,390)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C1.35 2 -0.3 2 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#CACDF7" transform="translate(306,361)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C8C9F7" transform="translate(532,313)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.01 1.33 3.02 1.66 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#CED1FA" transform="translate(196,290)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.34 -0.32 3.68 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#D5D6FB" transform="translate(412,275)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.32 2.33 -1.64 2.66 -3 3 C-2.01 2.01 -1.02 1.02 0 0 Z " fill="#D6D7FA" transform="translate(527,261)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.34 1.32 0.68 2.64 0 4 C0 2.68 0 1.36 0 0 Z " fill="#D3D4FA" transform="translate(309,256)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.67 2.99 1.34 3.98 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#D9DAFC" transform="translate(493,240)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.01 3 1.02 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C9CBF9" transform="translate(512,217)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.01 2.67 -0.98 2.34 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#B5B8EF" transform="translate(730,185)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C1.35 2 -0.3 2 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#BFC3F4" transform="translate(340,184)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#7771D9" transform="translate(810,954)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.34 -0.32 2.68 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#7771DA" transform="translate(822,951)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 1.66 2.34 2.32 2 3 C1.34 2.67 0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7771DA" transform="translate(829,948)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7871DA" transform="translate(833,936)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#7772DA" transform="translate(848,926)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#7772DA" transform="translate(824,910)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 2.33 -0.98 2.66 -2 3 C-2 2.34 -2 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#AEB1F1" transform="translate(533,669)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.99 2 1.98 2 3 C1.34 2.67 0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B8BAF5" transform="translate(445,668)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#B2B3F2" transform="translate(529,664)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B3B6F2" transform="translate(500,660)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.34 2.34 -0.32 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BDBEF4" transform="translate(543,658)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#BCBBF4" transform="translate(612,649)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#B8BAF1" transform="translate(541,648)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.33 0.02 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#BCBCF4" transform="translate(656,642)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#AAB2EB" transform="translate(338,616)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BCBEF6" transform="translate(584,500)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C1.68 1.67 0.36 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C5C6F5" transform="translate(460,493)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.01 2.67 1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C7CAF5" transform="translate(736,485)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 1.66 2.34 2.32 2 3 C1.34 2.67 0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C6C9F5" transform="translate(536,484)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D5D6F9" transform="translate(448,466)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D6D6FB" transform="translate(668,460)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#CDD0F7" transform="translate(465,448)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.99 1.34 2.98 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#CFD0FA" transform="translate(617,416)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C3C6F7" transform="translate(743,409)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D0D1FA" transform="translate(612,405)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#DCDAF9" transform="translate(668,403)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C2C3F7" transform="translate(743,393)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C0C2F6" transform="translate(743,369)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#CBCCFA" transform="translate(560,361)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.99 2 1.98 2 3 C1.34 2.67 0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CCCEFA" transform="translate(319,317)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CCCEF8" transform="translate(696,301)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D0D2FB" transform="translate(406,292)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 1.67 1.02 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CFD1F7" transform="translate(268,288)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.68 1.33 0.36 1.66 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D2D4F9" transform="translate(236,288)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D3D1F8" transform="translate(532,254)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D2D4FA" transform="translate(280,233)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.66 2 2.32 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CCCDFB" transform="translate(752,225)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.68 1.33 0.36 1.66 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#CCCEF8" transform="translate(221,214)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.34 2.34 -0.32 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CCCDF6" transform="translate(521,208)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B7BAED" transform="translate(552,187)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BABCF0" transform="translate(542,187)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B9B9ED" transform="translate(818,185)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#7771DA" transform="translate(837,945)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#7871DA" transform="translate(830,944)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#7772DA" transform="translate(822,926)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#7672D9" transform="translate(828,915)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#7672D9" transform="translate(823,891)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#AFB4EE" transform="translate(350,670)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#B1B3EF" transform="translate(525,668)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BFC1F5" transform="translate(392,656)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C7C4F3" transform="translate(629,656)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#AEAFED" transform="translate(701,656)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BCBCF3" transform="translate(674,657)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C3C4F6" transform="translate(441,657)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BCC0F2" transform="translate(344,653)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#BDC0F5" transform="translate(357,647)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B7B8F3" transform="translate(675,645)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B8B9F4" transform="translate(679,643)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#BBBDF5" transform="translate(653,639)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#B5B7F3" transform="translate(525,632)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#C4C6F6" transform="translate(567,499)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C1C2F8" transform="translate(562,498)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BBC0F7" transform="translate(279,498)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D5D3F9" transform="translate(668,482)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CBCEF7" transform="translate(529,477)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CCCDF7" transform="translate(724,466)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CED0F8" transform="translate(605,464)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CFD1FC" transform="translate(316,451)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D8D5FA" transform="translate(668,444)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#DAD8FB" transform="translate(668,436)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CDCEF8" transform="translate(620,433)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CDD0F9" transform="translate(468,432)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D9D8F9" transform="translate(668,420)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C7C8F7" transform="translate(743,418)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D2D4FB" transform="translate(466,417)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D5D5FB" transform="translate(616,412)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D3D4F9" transform="translate(557,398)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CCCEF9" transform="translate(317,395)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C3C4F7" transform="translate(743,377)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CED0F6" transform="translate(536,376)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CED1F8" transform="translate(550,368)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CCCEF9" transform="translate(603,364)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#CBCEF9" transform="translate(346,322)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C5C4F9" transform="translate(788,318)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CBCDF8" transform="translate(371,318)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C1C6F5" transform="translate(184,317)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CDCEFA" transform="translate(312,313)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#D5D5FC" transform="translate(404,292)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CECFFA" transform="translate(840,281)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#C9CCF9" transform="translate(241,282)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D3D4F9" transform="translate(481,280)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D3D1FA" transform="translate(752,273)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D1D4F9" transform="translate(178,273)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#D1CFF8" transform="translate(532,270)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D2D3FA" transform="translate(668,258)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#DAD7FD" transform="translate(464,250)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#CBCAF2" transform="translate(874,242)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D1D1F8" transform="translate(310,240)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C3CBFA" transform="translate(266,221)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C6CBF7" transform="translate(269,208)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BABCF0" transform="translate(535,187)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BABEF3" transform="translate(456,187)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#766ED8" transform="translate(871,979)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7772DB" transform="translate(815,955)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7872DA" transform="translate(838,943)"/>
<path d="" fill="#766ED8" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#7872DA" transform="translate(834,940)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#776ED7" transform="translate(894,931)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#776FD9" transform="translate(876,931)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#7871DA" transform="translate(871,931)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#7871D9" transform="translate(859,925)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#756DD6" transform="translate(896,923)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#766FD7" transform="translate(887,914)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#736CD4" transform="translate(922,893)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B1B3F3" transform="translate(528,669)"/>
<path d="" fill="#B6B7F3" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BCBBF3" transform="translate(682,659)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B9BBF4" transform="translate(546,659)"/>
<path d="" fill="#BBBAF4" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BFBFF7" transform="translate(414,657)"/>
<path d="" fill="#B7BDF4" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B8B8F4" transform="translate(640,657)"/>
<path d="" fill="#BCBDF4" transform="translate(0,0)"/>
<path d="" fill="#BDBDF4" transform="translate(0,0)"/>
<path d="" fill="#BCBDF1" transform="translate(0,0)"/>
<path d="" fill="#BEBEF3" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BABCF4" transform="translate(587,645)"/>
<path d="" fill="#BCBEF6" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BCBEF7" transform="translate(397,643)"/>
<path d="" fill="#C0C0F7" transform="translate(0,0)"/>
<path d="" fill="#BFC0F4" transform="translate(0,0)"/>
<path d="" fill="#BBBFF6" transform="translate(0,0)"/>
<path d="" fill="#BABCF7" transform="translate(0,0)"/>
<path d="" fill="#BEC2F5" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B2B5EE" transform="translate(399,632)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BABAF1" transform="translate(605,632)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BBC1F7" transform="translate(335,631)"/>
<path d="" fill="#B3B8F1" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BEBFF5" transform="translate(580,500)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BDC1F7" transform="translate(424,500)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BCC0F5" transform="translate(289,500)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B2B3F1" transform="translate(693,498)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BFC2F8" transform="translate(410,498)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BFC2F7" transform="translate(549,494)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C4C6F5" transform="translate(384,486)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C6C8F6" transform="translate(473,485)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C8CAF8" transform="translate(635,477)"/>
<path d="" fill="#D6D4FB" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CFD1FA" transform="translate(565,466)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CDCFFC" transform="translate(412,465)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D8D7FB" transform="translate(405,464)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CDCEF8" transform="translate(646,462)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CFD0FC" transform="translate(549,450)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CED0F9" transform="translate(307,447)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CCCFFB" transform="translate(513,445)"/>
<path d="" fill="#BFC0F2" transform="translate(0,0)"/>
<path d="" fill="#DCDBF8" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CDCEFA" transform="translate(619,442)"/>
<path d="" fill="#CFD3FA" transform="translate(0,0)"/>
<path d="" fill="#C8C9F7" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D0D2FB" transform="translate(319,419)"/>
<path d="" fill="#CFD1FC" transform="translate(0,0)"/>
<path d="" fill="#D2D2FA" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D5D8FD" transform="translate(283,408)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D1D1F9" transform="translate(458,402)"/>
<path d="" fill="#C4C6F7" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D0CFF9" transform="translate(451,396)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D1D3FD" transform="translate(520,395)"/>
<path d="" fill="#C1C7F7" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D0CFF9" transform="translate(667,380)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C9CEF5" transform="translate(384,376)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C7C8FA" transform="translate(623,372)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CFD1F9" transform="translate(614,368)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C8CBF9" transform="translate(451,364)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C7C9F8" transform="translate(313,363)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CBCDF5" transform="translate(589,362)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C9CBF8" transform="translate(444,362)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C8CAF7" transform="translate(440,362)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CBCDF7" transform="translate(416,362)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CBCCF9" transform="translate(350,322)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C9C9F7" transform="translate(792,318)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C3C7F6" transform="translate(188,318)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C9C9F5" transform="translate(661,317)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C7CAF5" transform="translate(462,318)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C9C8F8" transform="translate(612,317)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C6C9F9" transform="translate(246,316)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CBCDF7" transform="translate(776,309)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C9CEFA" transform="translate(688,294)"/>
<path d="" fill="#CFCEF9" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D0D1FB" transform="translate(811,293)"/>
<path d="" fill="#CECEF8" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C9CDFA" transform="translate(246,281)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D1D2F8" transform="translate(661,270)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D3D5FC" transform="translate(472,269)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D0D1FB" transform="translate(472,266)"/>
<path d="" fill="#D3D6FB" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D4D6FB" transform="translate(665,253)"/>
<path d="" fill="#BFC7F4" transform="translate(0,0)"/>
<path d="" fill="#BFC5F6" transform="translate(0,0)"/>
<path d="" fill="#D5D4F9" transform="translate(0,0)"/>
<path d="" fill="#D2D4F9" transform="translate(0,0)"/>
<path d="" fill="#D5D7FB" transform="translate(0,0)"/>
<path d="" fill="#DAD9FA" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CECDF9" transform="translate(754,228)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C8CCF8" transform="translate(283,226)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D0D2F9" transform="translate(187,222)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D3D5FB" transform="translate(324,219)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CACBF6" transform="translate(520,211)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D0D1F9" transform="translate(398,207)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B8B8EE" transform="translate(852,192)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BCC3F2" transform="translate(180,192)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BBBBF1" transform="translate(800,187)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B7BAF0" transform="translate(648,187)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B8BAF0" transform="translate(592,187)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C1C6F4" transform="translate(328,187)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#786FD9" transform="translate(847,995)"/>
<path d="" fill="#766FD8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#766ED8" transform="translate(869,983)"/>
<path d="" fill="#7670D9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7671D9" transform="translate(798,963)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#746ED6" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7870D9" transform="translate(854,955)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7671DA" transform="translate(815,953)"/>
<path d="" fill="#7672DA" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7872DB" transform="translate(830,947)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7871DA" transform="translate(792,947)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7672DA" transform="translate(788,945)"/>
<path d="" fill="#766FD8" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7970DB" transform="translate(838,941)"/>
<path d="" fill="#766FD7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#766ED7" transform="translate(886,936)"/>
<path d="" fill="#7870DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7772DB" transform="translate(845,935)"/>
<path d="" fill="#7873DB" transform="translate(0,0)"/>
<path d="" fill="#7773DB" transform="translate(0,0)"/>
<path d="" fill="#756DD6" transform="translate(0,0)"/>
<path d="" fill="#7971DC" transform="translate(0,0)"/>
<path d="" fill="#7973DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#776FD8" transform="translate(884,932)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7971DA" transform="translate(847,931)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7773DA" transform="translate(833,931)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7872DA" transform="translate(798,931)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#766FD8" transform="translate(883,930)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#766FD7" transform="translate(886,927)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#776FD9" transform="translate(877,927)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7872DA" transform="translate(853,927)"/>
<path d="" fill="#766ED6" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7670D8" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7671D9" transform="translate(824,925)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7872DA" transform="translate(854,923)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7772DA" transform="translate(838,923)"/>
<path d="" fill="#7570D9" transform="translate(0,0)"/>
<path d="" fill="#766DD7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7670D8" transform="translate(862,912)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7672DA" transform="translate(822,907)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7771D9" transform="translate(863,905)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#766FD8" transform="translate(887,903)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#776FD9" transform="translate(879,889)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#AEB0EF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBBBED" transform="translate(645,671)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B4B5F0" transform="translate(628,671)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B2ED" transform="translate(324,671)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEAFEF" transform="translate(654,669)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEB3F3" transform="translate(354,669)"/>
<path d="" fill="#A9AAEA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5B7F2" transform="translate(614,667)"/>
<path d="" fill="#BCBEF5" transform="translate(0,0)"/>
<path d="" fill="#C4C3F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8B9F5" transform="translate(569,664)"/>
<path d="" fill="#AEAEEE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BAF3" transform="translate(549,660)"/>
<path d="" fill="#B7BBF4" transform="translate(0,0)"/>
<path d="" fill="#B9BBF3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6C4F5" transform="translate(631,659)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BAF3" transform="translate(343,659)"/>
<path d="" fill="#B6B8F0" transform="translate(0,0)"/>
<path d="" fill="#BBBBF4" transform="translate(0,0)"/>
<path d="" fill="#BEBEF2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9BBF5" transform="translate(518,658)"/>
<path d="" fill="#BFBFF8" transform="translate(0,0)"/>
<path d="" fill="#BCBDF5" transform="translate(0,0)"/>
<path d="" fill="#B2B6F0" transform="translate(0,0)"/>
<path d="" fill="#BCC0F2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BDF6" transform="translate(339,655)"/>
<path d="" fill="#C0C3F7" transform="translate(0,0)"/>
<path d="" fill="#B6B8F2" transform="translate(0,0)"/>
<path d="" fill="#B7BAF1" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEC0FA" transform="translate(381,647)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BCBDF6" transform="translate(612,644)"/>
<path d="" fill="#BEBEF5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8B8F4" transform="translate(631,643)"/>
<path d="" fill="#B7BCF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9B9F3" transform="translate(658,642)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BCBDF5" transform="translate(635,642)"/>
<path d="" fill="#BEBFF3" transform="translate(0,0)"/>
<path d="" fill="#B6B5F1" transform="translate(0,0)"/>
<path d="" fill="#B8B8F2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9BAF1" transform="translate(664,635)"/>
<path d="" fill="#BCBEF3" transform="translate(0,0)"/>
<path d="" fill="#B7BAF2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BBEC" transform="translate(585,632)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BAF4" transform="translate(553,632)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9BCEF" transform="translate(422,632)"/>
<path d="" fill="#B9BDF5" transform="translate(0,0)"/>
<path d="" fill="#B5BAF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BABEF6" transform="translate(358,630)"/>
<path d="" fill="#B4B5F1" transform="translate(0,0)"/>
<path d="" fill="#B8BBF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ADADED" transform="translate(700,623)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3B7F3" transform="translate(351,619)"/>
<path d="" fill="#A5A8E9" transform="translate(0,0)"/>
<path d="" fill="#BEC3F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBBDF5" transform="translate(588,500)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BCBFF5" transform="translate(575,500)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDC0F4" transform="translate(421,500)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC0F2" transform="translate(293,500)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3C5F8" transform="translate(416,499)"/>
<path d="" fill="#BDC0F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEC3F8" transform="translate(311,499)"/>
<path d="" fill="#BABBF3" transform="translate(0,0)"/>
<path d="" fill="#C2C5F8" transform="translate(0,0)"/>
<path d="" fill="#C9C9F8" transform="translate(0,0)"/>
<path d="" fill="#B8BDF4" transform="translate(0,0)"/>
<path d="" fill="#C5C4F4" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C5C4F5" transform="translate(667,490)"/>
<path d="" fill="#D3D1F9" transform="translate(0,0)"/>
<path d="" fill="#D4D2F8" transform="translate(0,0)"/>
<path d="" fill="#CCCCF5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D7D8F5" transform="translate(585,472)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D9D9F7" transform="translate(301,472)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBCBF8" transform="translate(487,471)"/>
<path d="" fill="#CACEFB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7C7F8" transform="translate(723,469)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D2D2FC" transform="translate(414,469)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CED0F9" transform="translate(564,468)"/>
<path d="" fill="#C7C9FB" transform="translate(0,0)"/>
<path d="" fill="#CCD0F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CED0FC" transform="translate(275,467)"/>
<path d="" fill="#D0D2FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0BEF2" transform="translate(772,465)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D9DBFB" transform="translate(454,464)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDCFFB" transform="translate(494,462)"/>
<path d="" fill="#CFD2F8" transform="translate(0,0)"/>
<path d="" fill="#CDCFFC" transform="translate(0,0)"/>
<path d="" fill="#D1D3FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6C8F8" transform="translate(517,460)"/>
<path d="" fill="#CED2FB" transform="translate(0,0)"/>
<path d="" fill="#D4D4F9" transform="translate(0,0)"/>
<path d="" fill="#CBCEF9" transform="translate(0,0)"/>
<path d="" fill="#D2D4FA" transform="translate(0,0)"/>
<path d="" fill="#D5D7FD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CCCEF9" transform="translate(298,444)"/>
<path d="" fill="#C8CCF8" transform="translate(0,0)"/>
<path d="" fill="#CECFF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C5C8F9" transform="translate(270,435)"/>
<path d="" fill="#CECFFA" transform="translate(0,0)"/>
<path d="" fill="#CCCEF8" transform="translate(0,0)"/>
<path d="" fill="#CCCEF8" transform="translate(0,0)"/>
<path d="" fill="#D2D3FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C1C6F6" transform="translate(256,425)"/>
<path d="" fill="#D0D1FB" transform="translate(0,0)"/>
<path d="" fill="#CACDFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D1D3F9" transform="translate(313,418)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D1D3FA" transform="translate(305,416)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD2FA" transform="translate(292,412)"/>
<path d="" fill="#CDCDF9" transform="translate(0,0)"/>
<path d="" fill="#D1D2F6" transform="translate(0,0)"/>
<path d="" fill="#D5D7F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D0D3FB" transform="translate(516,406)"/>
<path d="" fill="#D2D2FA" transform="translate(0,0)"/>
<path d="" fill="#D0D3FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDCEFA" transform="translate(451,399)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDCDF9" transform="translate(605,398)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFCFFB" transform="translate(603,396)"/>
<path d="" fill="#D1D3F9" transform="translate(0,0)"/>
<path d="" fill="#CBCEF9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDD0FA" transform="translate(315,394)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD1FB" transform="translate(576,390)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D2D2F9" transform="translate(573,390)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D1D4FA" transform="translate(303,390)"/>
<path d="" fill="#DEDBFD" transform="translate(0,0)"/>
<path d="" fill="#C6CAF6" transform="translate(0,0)"/>
<path d="" fill="#CBCDF9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C5C8F7" transform="translate(465,371)"/>
<path d="" fill="#C7C9F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7CAF4" transform="translate(316,364)"/>
<path d="" fill="#C4C9FA" transform="translate(0,0)"/>
<path d="" fill="#C4C2F4" transform="translate(0,0)"/>
<path d="" fill="#C4C6F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7CAF9" transform="translate(358,322)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDBDF1" transform="translate(847,319)"/>
<path d="" fill="#BAC0F7" transform="translate(0,0)"/>
<path d="" fill="#CCCDF7" transform="translate(0,0)"/>
<path d="" fill="#CBCBF9" transform="translate(0,0)"/>
<path d="" fill="#CCCBF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D1D2FA" transform="translate(302,307)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8C9F7" transform="translate(262,306)"/>
<path d="" fill="#C0BFF0" transform="translate(0,0)"/>
<path d="" fill="#D0D1F8" transform="translate(0,0)"/>
<path d="" fill="#D3D2FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C5C7F7" transform="translate(497,298)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CACAF9" transform="translate(825,293)"/>
<path d="" fill="#CDCDF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD3FB" transform="translate(353,293)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D6D5FC" transform="translate(832,291)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDCEFC" transform="translate(823,291)"/>
<path d="" fill="#D8D9FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D5D5FB" transform="translate(835,290)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD0F9" transform="translate(802,290)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD2F7" transform="translate(233,290)"/>
<path d="" fill="#D4D4FA" transform="translate(0,0)"/>
<path d="" fill="#CFD1F9" transform="translate(0,0)"/>
<path d="" fill="#D3D4FA" transform="translate(0,0)"/>
<path d="" fill="#CFCFF9" transform="translate(0,0)"/>
<path d="" fill="#CED0FA" transform="translate(0,0)"/>
<path d="" fill="#D4D4FB" transform="translate(0,0)"/>
<path d="" fill="#D2D4FA" transform="translate(0,0)"/>
<path d="" fill="#CCCFFB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBD0FB" transform="translate(259,280)"/>
<path d="" fill="#BEBEF3" transform="translate(0,0)"/>
<path d="" fill="#D4D4FD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D3D3FB" transform="translate(518,278)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D3D4FB" transform="translate(518,275)"/>
<path d="" fill="#CDD1F9" transform="translate(0,0)"/>
<path d="" fill="#C3C1F2" transform="translate(0,0)"/>
<path d="" fill="#C1C1F2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D0CFFA" transform="translate(848,271)"/>
<path d="" fill="#D3D4F9" transform="translate(0,0)"/>
<path d="" fill="#D0D3FB" transform="translate(0,0)"/>
<path d="" fill="#CFCEFA" transform="translate(0,0)"/>
<path d="" fill="#D1D0F9" transform="translate(0,0)"/>
<path d="" fill="#DADDFD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D1D2FB" transform="translate(469,260)"/>
<path d="" fill="#D0D1F9" transform="translate(0,0)"/>
<path d="" fill="#D4D2FA" transform="translate(0,0)"/>
<path d="" fill="#D4D3F9" transform="translate(0,0)"/>
<path d="" fill="#D3D2FA" transform="translate(0,0)"/>
<path d="" fill="#D8D7FF" transform="translate(0,0)"/>
<path d="" fill="#D8D4FA" transform="translate(0,0)"/>
<path d="" fill="#B9B8EC" transform="translate(0,0)"/>
<path d="" fill="#CFD0F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D4D7FB" transform="translate(309,247)"/>
<path d="" fill="#D4D6F9" transform="translate(0,0)"/>
<path d="" fill="#D0D4FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CACBF3" transform="translate(866,243)"/>
<path d="" fill="#D1D1F7" transform="translate(0,0)"/>
<path d="" fill="#B5B4ED" transform="translate(0,0)"/>
<path d="" fill="#BEC5F5" transform="translate(0,0)"/>
<path d="" fill="#D1D4F9" transform="translate(0,0)"/>
<path d="" fill="#D6D4FB" transform="translate(0,0)"/>
<path d="" fill="#D1D3F8" transform="translate(0,0)"/>
<path d="" fill="#CDCCF8" transform="translate(0,0)"/>
<path d="" fill="#CED1F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CECDF9" transform="translate(851,229)"/>
<path d="" fill="#D1D3F9" transform="translate(0,0)"/>
<path d="" fill="#CCCFF9" transform="translate(0,0)"/>
<path d="" fill="#C8CCF7" transform="translate(0,0)"/>
<path d="" fill="#B7BFF4" transform="translate(0,0)"/>
<path d="" fill="#C4C9FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D0D1F7" transform="translate(189,222)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBCBF9" transform="translate(840,219)"/>
<path d="" fill="#D1D2F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD0FB" transform="translate(367,219)"/>
<path d="" fill="#CFD2F9" transform="translate(0,0)"/>
<path d="" fill="#CED0F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D4D5FA" transform="translate(332,215)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBCEF9" transform="translate(336,214)"/>
<path d="" fill="#C2C7F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9CDF7" transform="translate(396,205)"/>
<path d="" fill="#C6CBF7" transform="translate(0,0)"/>
<path d="" fill="#C7C7F3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6CAF5" transform="translate(393,202)"/>
<path d="" fill="#C4C6F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C3F3" transform="translate(775,200)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3C7F5" transform="translate(253,196)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4C7F5" transform="translate(528,195)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFC5F6" transform="translate(309,194)"/>
<path d="" fill="#BEC2F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2C6F4" transform="translate(312,193)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C3F3" transform="translate(314,192)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7C8F2" transform="translate(246,192)"/>
<path d="" fill="#BCBDEE" transform="translate(0,0)"/>
<path d="" fill="#BCC0F3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFBFF2" transform="translate(835,187)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3B4EB" transform="translate(734,187)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9BAF1" transform="translate(720,187)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3B4EF" transform="translate(710,187)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1B4EF" transform="translate(608,187)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7B7F0" transform="translate(584,187)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7BEF1" transform="translate(360,186)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEC7F6" transform="translate(201,186)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B4B6EF" transform="translate(732,185)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BCC2F1" transform="translate(214,185)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDC6F6" transform="translate(206,185)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDC3F4" transform="translate(222,184)"/>
<path d="" fill="#766FDB" transform="translate(0,0)"/>
<path d="" fill="#6267D6" transform="translate(0,0)"/>
<path d="" fill="#7670DA" transform="translate(0,0)"/>
<path d="" fill="#7670D8" transform="translate(0,0)"/>
<path d="" fill="#7670DA" transform="translate(0,0)"/>
<path d="" fill="#786FD9" transform="translate(0,0)"/>
<path d="" fill="#6669D8" transform="translate(0,0)"/>
<path d="" fill="#7870DB" transform="translate(0,0)"/>
<path d="" fill="#7670D9" transform="translate(0,0)"/>
<path d="" fill="#776FD8" transform="translate(0,0)"/>
<path d="" fill="#766ED8" transform="translate(0,0)"/>
<path d="" fill="#766ED8" transform="translate(0,0)"/>
<path d="" fill="#736AD5" transform="translate(0,0)"/>
<path d="" fill="#786ED9" transform="translate(0,0)"/>
<path d="" fill="#7770DA" transform="translate(0,0)"/>
<path d="" fill="#786FDA" transform="translate(0,0)"/>
<path d="" fill="#756FDA" transform="translate(0,0)"/>
<path d="" fill="#7870DA" transform="translate(0,0)"/>
<path d="" fill="#5766D5" transform="translate(0,0)"/>
<path d="" fill="#786FDA" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#786FD9" transform="translate(0,0)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="" fill="#756FDB" transform="translate(0,0)"/>
<path d="" fill="#756FD7" transform="translate(0,0)"/>
<path d="" fill="#756FD9" transform="translate(0,0)"/>
<path d="" fill="#7571D9" transform="translate(0,0)"/>
<path d="" fill="#746CD7" transform="translate(0,0)"/>
<path d="" fill="#756FD8" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#776FDA" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#7670DA" transform="translate(0,0)"/>
<path d="" fill="#7570DA" transform="translate(0,0)"/>
<path d="" fill="#766ED7" transform="translate(0,0)"/>
<path d="" fill="#776DD7" transform="translate(0,0)"/>
<path d="" fill="#786FD9" transform="translate(0,0)"/>
<path d="" fill="#756ED7" transform="translate(0,0)"/>
<path d="" fill="#7670DA" transform="translate(0,0)"/>
<path d="" fill="#746CD5" transform="translate(0,0)"/>
<path d="" fill="#746CD7" transform="translate(0,0)"/>
<path d="" fill="#746ED6" transform="translate(0,0)"/>
<path d="" fill="#746DD6" transform="translate(0,0)"/>
<path d="" fill="#766ED8" transform="translate(0,0)"/>
<path d="" fill="#766FD8" transform="translate(0,0)"/>
<path d="" fill="#776FD8" transform="translate(0,0)"/>
<path d="" fill="#716BD3" transform="translate(0,0)"/>
<path d="" fill="#7971DB" transform="translate(0,0)"/>
<path d="" fill="#7670D9" transform="translate(0,0)"/>
<path d="" fill="#736AD4" transform="translate(0,0)"/>
<path d="" fill="#736BD5" transform="translate(0,0)"/>
<path d="" fill="#7770DA" transform="translate(0,0)"/>
<path d="" fill="#7870DA" transform="translate(0,0)"/>
<path d="" fill="#7770DA" transform="translate(0,0)"/>
<path d="" fill="#756ED7" transform="translate(0,0)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#786FD9" transform="translate(0,0)"/>
<path d="" fill="#766ED7" transform="translate(0,0)"/>
<path d="" fill="#766DD8" transform="translate(0,0)"/>
<path d="" fill="#766ED8" transform="translate(0,0)"/>
<path d="" fill="#7670DA" transform="translate(0,0)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#756CD7" transform="translate(0,0)"/>
<path d="" fill="#756ED6" transform="translate(0,0)"/>
<path d="" fill="#7670D9" transform="translate(0,0)"/>
<path d="" fill="#766ED8" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7870D9" transform="translate(0,0)"/>
<path d="" fill="#7772D9" transform="translate(0,0)"/>
<path d="" fill="#716FD9" transform="translate(0,0)"/>
<path d="" fill="#716AD3" transform="translate(0,0)"/>
<path d="" fill="#756BD6" transform="translate(0,0)"/>
<path d="" fill="#756DD7" transform="translate(0,0)"/>
<path d="" fill="#766FD8" transform="translate(0,0)"/>
<path d="" fill="#7771D9" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7870DB" transform="translate(0,0)"/>
<path d="" fill="#7672DB" transform="translate(0,0)"/>
<path d="" fill="#7670DB" transform="translate(0,0)"/>
<path d="" fill="#766ED8" transform="translate(0,0)"/>
<path d="" fill="#7770DA" transform="translate(0,0)"/>
<path d="" fill="#7570DB" transform="translate(0,0)"/>
<path d="" fill="#7670DB" transform="translate(0,0)"/>
<path d="" fill="#7470DA" transform="translate(0,0)"/>
<path d="" fill="#756DD7" transform="translate(0,0)"/>
<path d="" fill="#776FD7" transform="translate(0,0)"/>
<path d="" fill="#766FDA" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#756FD8" transform="translate(0,0)"/>
<path d="" fill="#756BD5" transform="translate(0,0)"/>
<path d="" fill="#766ED7" transform="translate(0,0)"/>
<path d="" fill="#7671DB" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7671DA" transform="translate(0,0)"/>
<path d="" fill="#7771D9" transform="translate(0,0)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="" fill="#746DD7" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7672DA" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#7670DA" transform="translate(0,0)"/>
<path d="" fill="#7971DA" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#706DDA" transform="translate(0,0)"/>
<path d="" fill="#7067D0" transform="translate(0,0)"/>
<path d="" fill="#756BD6" transform="translate(0,0)"/>
<path d="" fill="#766CD7" transform="translate(0,0)"/>
<path d="" fill="#776FD7" transform="translate(0,0)"/>
<path d="" fill="#7870D9" transform="translate(0,0)"/>
<path d="" fill="#7773DA" transform="translate(0,0)"/>
<path d="" fill="#7870DB" transform="translate(0,0)"/>
<path d="" fill="#7870DB" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7871DC" transform="translate(0,0)"/>
<path d="" fill="#7671DB" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#706DD9" transform="translate(0,0)"/>
<path d="" fill="#6180E7" transform="translate(0,0)"/>
<path d="" fill="#756ED8" transform="translate(0,0)"/>
<path d="" fill="#7670D9" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#7770DA" transform="translate(0,0)"/>
<path d="" fill="#7770DA" transform="translate(0,0)"/>
<path d="" fill="#776ED8" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7971DA" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7671DC" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#766FD7" transform="translate(0,0)"/>
<path d="" fill="#7870D9" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7672DA" transform="translate(0,0)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="" fill="#756FD8" transform="translate(0,0)"/>
<path d="" fill="#7670D9" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7571DA" transform="translate(0,0)"/>
<path d="" fill="#7971DA" transform="translate(0,0)"/>
<path d="" fill="#7972DA" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7971DA" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7A72DB" transform="translate(0,0)"/>
<path d="" fill="#7770DB" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7971DC" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7670DB" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#7A71DB" transform="translate(0,0)"/>
<path d="" fill="#7971DA" transform="translate(0,0)"/>
<path d="" fill="#7971DA" transform="translate(0,0)"/>
<path d="" fill="#7972DA" transform="translate(0,0)"/>
<path d="" fill="#7770DC" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7672DA" transform="translate(0,0)"/>
<path d="" fill="#7872DC" transform="translate(0,0)"/>
<path d="" fill="#7870D8" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7671DA" transform="translate(0,0)"/>
<path d="" fill="#766DD8" transform="translate(0,0)"/>
<path d="" fill="#7870D9" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7972DB" transform="translate(0,0)"/>
<path d="" fill="#7871DC" transform="translate(0,0)"/>
<path d="" fill="#7772DC" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7773DC" transform="translate(0,0)"/>
<path d="" fill="#766DD7" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#7873DA" transform="translate(0,0)"/>
<path d="" fill="#7773DA" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7972DB" transform="translate(0,0)"/>
<path d="" fill="#7572DB" transform="translate(0,0)"/>
<path d="" fill="#6A60CB" transform="translate(0,0)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7972DA" transform="translate(0,0)"/>
<path d="" fill="#7973DB" transform="translate(0,0)"/>
<path d="" fill="#716FD9" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7972DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7972DA" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7773DB" transform="translate(0,0)"/>
<path d="" fill="#7771DC" transform="translate(0,0)"/>
<path d="" fill="#7873DC" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7872DC" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7873DB" transform="translate(0,0)"/>
<path d="" fill="#7372D9" transform="translate(0,0)"/>
<path d="" fill="#706FD9" transform="translate(0,0)"/>
<path d="" fill="#7067D2" transform="translate(0,0)"/>
<path d="" fill="#766FD8" transform="translate(0,0)"/>
<path d="" fill="#786ED9" transform="translate(0,0)"/>
<path d="" fill="#7870D8" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7873D9" transform="translate(0,0)"/>
<path d="" fill="#7972DB" transform="translate(0,0)"/>
<path d="" fill="#7874DB" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7670DA" transform="translate(0,0)"/>
<path d="" fill="#7471D9" transform="translate(0,0)"/>
<path d="" fill="#7370DA" transform="translate(0,0)"/>
<path d="" fill="#706FD9" transform="translate(0,0)"/>
<path d="" fill="#657BE5" transform="translate(0,0)"/>
<path d="" fill="#7068D1" transform="translate(0,0)"/>
<path d="" fill="#756BD5" transform="translate(0,0)"/>
<path d="" fill="#776ED6" transform="translate(0,0)"/>
<path d="" fill="#766FD7" transform="translate(0,0)"/>
<path d="" fill="#776ED7" transform="translate(0,0)"/>
<path d="" fill="#7770DA" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7870D9" transform="translate(0,0)"/>
<path d="" fill="#7873DB" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#736AD4" transform="translate(0,0)"/>
<path d="" fill="#756BD6" transform="translate(0,0)"/>
<path d="" fill="#7971D9" transform="translate(0,0)"/>
<path d="" fill="#7873DA" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7874DA" transform="translate(0,0)"/>
<path d="" fill="#7773DC" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7572DA" transform="translate(0,0)"/>
<path d="" fill="#7370DB" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#7974DA" transform="translate(0,0)"/>
<path d="" fill="#7972DC" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#7670D8" transform="translate(0,0)"/>
<path d="" fill="#7871D8" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7872DC" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#786FD7" transform="translate(0,0)"/>
<path d="" fill="#756DD6" transform="translate(0,0)"/>
<path d="" fill="#756FD8" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7870DB" transform="translate(0,0)"/>
<path d="" fill="#7873DB" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7873DB" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7873DC" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7471DA" transform="translate(0,0)"/>
<path d="" fill="#5F81E6" transform="translate(0,0)"/>
<path d="" fill="#746BD4" transform="translate(0,0)"/>
<path d="" fill="#766FD7" transform="translate(0,0)"/>
<path d="" fill="#7570D9" transform="translate(0,0)"/>
<path d="" fill="#7970DA" transform="translate(0,0)"/>
<path d="" fill="#7971D9" transform="translate(0,0)"/>
<path d="" fill="#7873DA" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7773DB" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7271D8" transform="translate(0,0)"/>
<path d="" fill="#736AD3" transform="translate(0,0)"/>
<path d="" fill="#756ED8" transform="translate(0,0)"/>
<path d="" fill="#776FD8" transform="translate(0,0)"/>
<path d="" fill="#7670D8" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7972DA" transform="translate(0,0)"/>
<path d="" fill="#7972D8" transform="translate(0,0)"/>
<path d="" fill="#7369D5" transform="translate(0,0)"/>
<path d="" fill="#766DD7" transform="translate(0,0)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#776ED7" transform="translate(0,0)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#7772DC" transform="translate(0,0)"/>
<path d="" fill="#7773DA" transform="translate(0,0)"/>
<path d="" fill="#7772D9" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7370DA" transform="translate(0,0)"/>
<path d="" fill="#756DD5" transform="translate(0,0)"/>
<path d="" fill="#766DD6" transform="translate(0,0)"/>
<path d="" fill="#776FDA" transform="translate(0,0)"/>
<path d="" fill="#786ED8" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7673DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#746BD5" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7972DB" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#766ED7" transform="translate(0,0)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#756ED7" transform="translate(0,0)"/>
<path d="" fill="#766FD7" transform="translate(0,0)"/>
<path d="" fill="#776FD8" transform="translate(0,0)"/>
<path d="" fill="#7572D9" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7672DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7473DA" transform="translate(0,0)"/>
<path d="" fill="#7070D9" transform="translate(0,0)"/>
<path d="" fill="#766DD6" transform="translate(0,0)"/>
<path d="" fill="#766ED8" transform="translate(0,0)"/>
<path d="" fill="#7571D9" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7773DA" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7773DB" transform="translate(0,0)"/>
<path d="" fill="#756CD6" transform="translate(0,0)"/>
<path d="" fill="#756FD7" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#7870D8" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7972DB" transform="translate(0,0)"/>
<path d="" fill="#7773DC" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#5C6AD5" transform="translate(0,0)"/>
<path d="" fill="#776ED8" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7870D9" transform="translate(0,0)"/>
<path d="" fill="#7471DA" transform="translate(0,0)"/>
<path d="" fill="#7671D7" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7773D8" transform="translate(0,0)"/>
<path d="" fill="#7771DC" transform="translate(0,0)"/>
<path d="" fill="#7672D9" transform="translate(0,0)"/>
<path d="" fill="#7773DB" transform="translate(0,0)"/>
<path d="" fill="#766DD5" transform="translate(0,0)"/>
<path d="" fill="#7570D8" transform="translate(0,0)"/>
<path d="" fill="#7671D6" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#776ED7" transform="translate(0,0)"/>
<path d="" fill="#7570D7" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7A73DB" transform="translate(0,0)"/>
<path d="" fill="#7774DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#756DD4" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#7771DB" transform="translate(0,0)"/>
<path d="" fill="#7773DA" transform="translate(0,0)"/>
<path d="" fill="#746DD5" transform="translate(0,0)"/>
<path d="" fill="#756CD4" transform="translate(0,0)"/>
<path d="" fill="#766DD5" transform="translate(0,0)"/>
<path d="" fill="#766DD8" transform="translate(0,0)"/>
<path d="" fill="#766FD7" transform="translate(0,0)"/>
<path d="" fill="#786FD8" transform="translate(0,0)"/>
<path d="" fill="#7771D7" transform="translate(0,0)"/>
<path d="" fill="#7870DA" transform="translate(0,0)"/>
<path d="" fill="#7971D8" transform="translate(0,0)"/>
<path d="" fill="#7971DA" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#7972D9" transform="translate(0,0)"/>
<path d="" fill="#7873DA" transform="translate(0,0)"/>
<path d="" fill="#746DD6" transform="translate(0,0)"/>
<path d="" fill="#7673DA" transform="translate(0,0)"/>
<path d="" fill="#7670D7" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#7771D9" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7773D9" transform="translate(0,0)"/>
<path d="" fill="#746AD5" transform="translate(0,0)"/>
<path d="" fill="#766FD8" transform="translate(0,0)"/>
<path d="" fill="#7773DA" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#766DD5" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#7772D8" transform="translate(0,0)"/>
<path d="" fill="#7972D9" transform="translate(0,0)"/>
<path d="" fill="#7974DC" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#766FD8" transform="translate(0,0)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="" fill="#746DD6" transform="translate(0,0)"/>
<path d="" fill="#7772D9" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7772DB" transform="translate(0,0)"/>
<path d="" fill="#7671DA" transform="translate(0,0)"/>
<path d="" fill="#746BD4" transform="translate(0,0)"/>
<path d="" fill="#766CD6" transform="translate(0,0)"/>
<path d="" fill="#776FD7" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#7871D8" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#756DD6" transform="translate(0,0)"/>
<path d="" fill="#766CD7" transform="translate(0,0)"/>
<path d="" fill="#766DD6" transform="translate(0,0)"/>
<path d="" fill="#766FD8" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#7870D9" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7871DB" transform="translate(0,0)"/>
<path d="" fill="#7872DB" transform="translate(0,0)"/>
<path d="" fill="#7773DB" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#756CD4" transform="translate(0,0)"/>
<path d="" fill="#746CD5" transform="translate(0,0)"/>
<path d="" fill="#766FD9" transform="translate(0,0)"/>
<path d="" fill="#776FD8" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="" fill="#756DD8" transform="translate(0,0)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="" fill="#7672DA" transform="translate(0,0)"/>
<path d="" fill="#7873DA" transform="translate(0,0)"/>
<path d="" fill="#766DD5" transform="translate(0,0)"/>
<path d="" fill="#766ED6" transform="translate(0,0)"/>
<path d="" fill="#7671D9" transform="translate(0,0)"/>
<path d="" fill="#7771D8" transform="translate(0,0)"/>
<path d="" fill="#736AD1" transform="translate(0,0)"/>
<path d="" fill="#746AD4" transform="translate(0,0)"/>
<path d="" fill="#766BD5" transform="translate(0,0)"/>
<path d="" fill="#766ED6" transform="translate(0,0)"/>
<path d="" fill="#786FD8" transform="translate(0,0)"/>
<path d="" fill="#7771DA" transform="translate(0,0)"/>
<path d="" fill="#716BD3" transform="translate(0,0)"/>
<path d="" fill="#736BD5" transform="translate(0,0)"/>
<path d="" fill="#766ED4" transform="translate(0,0)"/>
<path d="" fill="#766DD5" transform="translate(0,0)"/>
<path d="" fill="#776DD7" transform="translate(0,0)"/>
<path d="" fill="#776FD8" transform="translate(0,0)"/>
<path d="" fill="#786FD9" transform="translate(0,0)"/>
<path d="" fill="#7971DA" transform="translate(0,0)"/>
<path d="" fill="#7872DA" transform="translate(0,0)"/>
<path d="" fill="#7871DA" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#736AD3" transform="translate(0,0)"/>
<path d="" fill="#766DD5" transform="translate(0,0)"/>
<path d="" fill="#7873DA" transform="translate(0,0)"/>
<path d="" fill="#7772DA" transform="translate(0,0)"/>
<path d="" fill="#7774D9" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#746ED3" transform="translate(0,0)"/>
<path d="" fill="#7871D9" transform="translate(0,0)"/>
<path d="" fill="#7873DA" transform="translate(0,0)"/>
<path d="" fill="#7674D8" transform="translate(0,0)"/>
<path d="" fill="#6182E7" transform="translate(0,0)"/>
<path d="" fill="#766ED7" transform="translate(0,0)"/>
<path d="" fill="#7770D9" transform="translate(0,0)"/>
<path d="" fill="#7971D9" transform="translate(0,0)"/>
<path d="" fill="#7870D9" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#746ED6" transform="translate(0,0)"/>
<path d="" fill="#756ED7" transform="translate(0,0)"/>
<path d="" fill="#776FD9" transform="translate(0,0)"/>
<path d="" fill="#7673DA" transform="translate(0,0)"/>
<path d="" fill="#5D70D7" transform="translate(0,0)"/>
<path d="" fill="#776FD5" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#726BD1" transform="translate(0,0)"/>
<path d="" fill="#776ED6" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#776FD7" transform="translate(0,0)"/>
<path d="" fill="#7872D9" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#736CD5" transform="translate(0,0)"/>
<path d="" fill="#766DD6" transform="translate(0,0)"/>
<path d="" fill="#7772D8" transform="translate(0,0)"/>
<path d="" fill="#766CD4" transform="translate(0,0)"/>
<path d="" fill="#766FD6" transform="translate(0,0)"/>
<path d="" fill="#7770D8" transform="translate(0,0)"/>
<path d="" fill="#766ED6" transform="translate(0,0)"/>
<path d="" fill="#7673D9" transform="translate(0,0)"/>
<path d="" fill="#7771D5" transform="translate(0,0)"/>
<path d="" fill="#756BD3" transform="translate(0,0)"/>
<path d="" fill="#766DD6" transform="translate(0,0)"/>
<path d="" fill="#756CD3" transform="translate(0,0)"/>
<path d="" fill="#776ED5" transform="translate(0,0)"/>
<path d="" fill="#766ED6" transform="translate(0,0)"/>
<path d="" fill="#776ED6" transform="translate(0,0)"/>
<path d="" fill="#756ED6" transform="translate(0,0)"/>
<path d="" fill="#746ED4" transform="translate(0,0)"/>
<path d="" fill="#756ED5" transform="translate(0,0)"/>
<path d="" fill="#736CD4" transform="translate(0,0)"/>
<path d="" fill="#746DD4" transform="translate(0,0)"/>
<path d="" fill="#756ED5" transform="translate(0,0)"/>
<path d="" fill="#6376D8" transform="translate(0,0)"/>
<path d="" fill="#6976D5" transform="translate(0,0)"/>
<path d="" fill="#7884DD" transform="translate(0,0)"/>
<path d="" fill="#6D89E0" transform="translate(0,0)"/>
<path d="" fill="#ABAFF0" transform="translate(0,0)"/>
<path d="" fill="#ABAFEF" transform="translate(0,0)"/>
<path d="" fill="#ABAFEF" transform="translate(0,0)"/>
<path d="" fill="#A9AFEB" transform="translate(0,0)"/>
<path d="" fill="#A9AAEF" transform="translate(0,0)"/>
<path d="" fill="#ACB2F2" transform="translate(0,0)"/>
<path d="" fill="#B6B9F3" transform="translate(0,0)"/>
<path d="" fill="#A8A9EB" transform="translate(0,0)"/>
<path d="" fill="#AEB4EF" transform="translate(0,0)"/>
<path d="" fill="#AEAFEF" transform="translate(0,0)"/>
<path d="" fill="#AEB1F1" transform="translate(0,0)"/>
<path d="" fill="#A3ABE7" transform="translate(0,0)"/>
<path d="" fill="#ADB1F1" transform="translate(0,0)"/>
<path d="" fill="#B1B3EE" transform="translate(0,0)"/>
<path d="" fill="#AEB2EC" transform="translate(0,0)"/>
<path d="" fill="#B1B7F4" transform="translate(0,0)"/>
<path d="" fill="#ADB3F1" transform="translate(0,0)"/>
<path d="" fill="#AFB7F0" transform="translate(0,0)"/>
<path d="" fill="#B0B3ED" transform="translate(0,0)"/>
<path d="" fill="#AFB1EE" transform="translate(0,0)"/>
<path d="" fill="#B4B6EF" transform="translate(0,0)"/>
<path d="" fill="#B0B2EF" transform="translate(0,0)"/>
<path d="" fill="#B1B7F1" transform="translate(0,0)"/>
<path d="" fill="#ACB1F1" transform="translate(0,0)"/>
<path d="" fill="#AFB5F3" transform="translate(0,0)"/>
<path d="" fill="#B4B8F3" transform="translate(0,0)"/>
<path d="" fill="#B1B2EE" transform="translate(0,0)"/>
<path d="" fill="#B5B7F1" transform="translate(0,0)"/>
<path d="" fill="#B1B4F1" transform="translate(0,0)"/>
<path d="" fill="#B4B8F5" transform="translate(0,0)"/>
<path d="" fill="#A8AFEF" transform="translate(0,0)"/>
<path d="" fill="#B0B3F2" transform="translate(0,0)"/>
<path d="" fill="#ACB2F2" transform="translate(0,0)"/>
<path d="" fill="#9FAAEE" transform="translate(0,0)"/>
<path d="" fill="#B5B7F2" transform="translate(0,0)"/>
<path d="" fill="#B4B4F2" transform="translate(0,0)"/>
<path d="" fill="#B9BBF5" transform="translate(0,0)"/>
<path d="" fill="#AFB1F2" transform="translate(0,0)"/>
<path d="" fill="#BBBFF5" transform="translate(0,0)"/>
<path d="" fill="#B6B9F2" transform="translate(0,0)"/>
<path d="" fill="#B6B8F3" transform="translate(0,0)"/>
<path d="" fill="#B7B9F6" transform="translate(0,0)"/>
<path d="" fill="#B2B5F3" transform="translate(0,0)"/>
<path d="" fill="#B7BAF5" transform="translate(0,0)"/>
<path d="" fill="#B2B6F1" transform="translate(0,0)"/>
<path d="" fill="#AEAFEF" transform="translate(0,0)"/>
<path d="" fill="#B4B6F1" transform="translate(0,0)"/>
<path d="" fill="#B6B8F2" transform="translate(0,0)"/>
<path d="" fill="#B6BAF1" transform="translate(0,0)"/>
<path d="" fill="#B2B7F5" transform="translate(0,0)"/>
<path d="" fill="#A1ABEF" transform="translate(0,0)"/>
<path d="" fill="#B8BAF4" transform="translate(0,0)"/>
<path d="" fill="#B6B8F4" transform="translate(0,0)"/>
<path d="" fill="#BABAF5" transform="translate(0,0)"/>
<path d="" fill="#BCBCF4" transform="translate(0,0)"/>
<path d="" fill="#AFB2EE" transform="translate(0,0)"/>
<path d="" fill="#BBBAF4" transform="translate(0,0)"/>
<path d="" fill="#BBBAF3" transform="translate(0,0)"/>
<path d="" fill="#B9BAF2" transform="translate(0,0)"/>
<path d="" fill="#B8B8F4" transform="translate(0,0)"/>
<path d="" fill="#B5B5EE" transform="translate(0,0)"/>
<path d="" fill="#BDBFF4" transform="translate(0,0)"/>
<path d="" fill="#B3B5F3" transform="translate(0,0)"/>
<path d="" fill="#BFC2F7" transform="translate(0,0)"/>
<path d="" fill="#B4B9F1" transform="translate(0,0)"/>
<path d="" fill="#A0ABEE" transform="translate(0,0)"/>
<path d="" fill="#B8B7F3" transform="translate(0,0)"/>
<path d="" fill="#B9BAF5" transform="translate(0,0)"/>
<path d="" fill="#B7B9F4" transform="translate(0,0)"/>
<path d="" fill="#B5B6F4" transform="translate(0,0)"/>
<path d="" fill="#B3B6F4" transform="translate(0,0)"/>
<path d="" fill="#C3C4FA" transform="translate(0,0)"/>
<path d="" fill="#B7BBF5" transform="translate(0,0)"/>
<path d="" fill="#BBBBF2" transform="translate(0,0)"/>
<path d="" fill="#BCBDF1" transform="translate(0,0)"/>
<path d="" fill="#B4B7F3" transform="translate(0,0)"/>
<path d="" fill="#B5B6F3" transform="translate(0,0)"/>
<path d="" fill="#BABCF8" transform="translate(0,0)"/>
<path d="" fill="#BDBEF9" transform="translate(0,0)"/>
<path d="" fill="#B9BDF6" transform="translate(0,0)"/>
<path d="" fill="#BABDF6" transform="translate(0,0)"/>
<path d="" fill="#B7BCF4" transform="translate(0,0)"/>
<path d="" fill="#B2B9F2" transform="translate(0,0)"/>
<path d="" fill="#A1ACEE" transform="translate(0,0)"/>
<path d="" fill="#B8B7F3" transform="translate(0,0)"/>
<path d="" fill="#C0BDF5" transform="translate(0,0)"/>
<path d="" fill="#B9BBF3" transform="translate(0,0)"/>
<path d="" fill="#BDBFF4" transform="translate(0,0)"/>
<path d="" fill="#B6BBF4" transform="translate(0,0)"/>
<path d="" fill="#B4B6F5" transform="translate(0,0)"/>
<path d="" fill="#BABDF9" transform="translate(0,0)"/>
<path d="" fill="#BDC0F7" transform="translate(0,0)"/>
<path d="" fill="#B8BAF5" transform="translate(0,0)"/>
<path d="" fill="#B7BCF4" transform="translate(0,0)"/>
<path d="" fill="#BBBCF4" transform="translate(0,0)"/>
<path d="" fill="#BFBEF6" transform="translate(0,0)"/>
<path d="" fill="#BFBCF5" transform="translate(0,0)"/>
<path d="" fill="#B4B6F4" transform="translate(0,0)"/>
<path d="" fill="#B6BAF6" transform="translate(0,0)"/>
<path d="" fill="#BABCF6" transform="translate(0,0)"/>
<path d="" fill="#B9BBF6" transform="translate(0,0)"/>
<path d="" fill="#B9BCF7" transform="translate(0,0)"/>
<path d="" fill="#BDC1F4" transform="translate(0,0)"/>
<path d="" fill="#ABB2EB" transform="translate(0,0)"/>
<path d="" fill="#A7B1F0" transform="translate(0,0)"/>
<path d="" fill="#BBBBF4" transform="translate(0,0)"/>
<path d="" fill="#B7B9F4" transform="translate(0,0)"/>
<path d="" fill="#BDC0F9" transform="translate(0,0)"/>
<path d="" fill="#B7BEEF" transform="translate(0,0)"/>
<path d="" fill="#B8BDF4" transform="translate(0,0)"/>
<path d="" fill="#B1B0EF" transform="translate(0,0)"/>
<path d="" fill="#BFC2F3" transform="translate(0,0)"/>
<path d="" fill="#AEB0EC" transform="translate(0,0)"/>
<path d="" fill="#B6B9F2" transform="translate(0,0)"/>
<path d="" fill="#BBBEF8" transform="translate(0,0)"/>
<path d="" fill="#B7BDF5" transform="translate(0,0)"/>
<path d="" fill="#BDBCF5" transform="translate(0,0)"/>
<path d="" fill="#BFBFF7" transform="translate(0,0)"/>
<path d="" fill="#B9BAF4" transform="translate(0,0)"/>
<path d="" fill="#B9BBF4" transform="translate(0,0)"/>
<path d="" fill="#BEBFF7" transform="translate(0,0)"/>
<path d="" fill="#B4B9F5" transform="translate(0,0)"/>
<path d="" fill="#BBBBF8" transform="translate(0,0)"/>
<path d="" fill="#C2C3F8" transform="translate(0,0)"/>
<path d="" fill="#B8B8F4" transform="translate(0,0)"/>
<path d="" fill="#BDBDF6" transform="translate(0,0)"/>
<path d="" fill="#BEBDF5" transform="translate(0,0)"/>
<path d="" fill="#B8BCF8" transform="translate(0,0)"/>
<path d="" fill="#B7BAF5" transform="translate(0,0)"/>
<path d="" fill="#B8BDF2" transform="translate(0,0)"/>
<path d="" fill="#BABAF7" transform="translate(0,0)"/>
<path d="" fill="#BDBFF6" transform="translate(0,0)"/>
<path d="" fill="#BDBFFA" transform="translate(0,0)"/>
<path d="" fill="#BFC0F8" transform="translate(0,0)"/>
<path d="" fill="#C3C4FA" transform="translate(0,0)"/>
<path d="" fill="#BABDFA" transform="translate(0,0)"/>
<path d="" fill="#B5B9F4" transform="translate(0,0)"/>
<path d="" fill="#B2B2EF" transform="translate(0,0)"/>
<path d="" fill="#B6B7F2" transform="translate(0,0)"/>
<path d="" fill="#B6B7F5" transform="translate(0,0)"/>
<path d="" fill="#BABCF5" transform="translate(0,0)"/>
<path d="" fill="#ADB8F4" transform="translate(0,0)"/>
<path d="" fill="#BCBCF6" transform="translate(0,0)"/>
<path d="" fill="#C0C3F4" transform="translate(0,0)"/>
<path d="" fill="#BBBCF5" transform="translate(0,0)"/>
<path d="" fill="#BABCF7" transform="translate(0,0)"/>
<path d="" fill="#BEBDF2" transform="translate(0,0)"/>
<path d="" fill="#BABDF3" transform="translate(0,0)"/>
<path d="" fill="#B7B8F5" transform="translate(0,0)"/>
<path d="" fill="#B4B5F4" transform="translate(0,0)"/>
<path d="" fill="#B6B7F2" transform="translate(0,0)"/>
<path d="" fill="#B3B8EF" transform="translate(0,0)"/>
<path d="" fill="#C3C6F7" transform="translate(0,0)"/>
<path d="" fill="#B8BBF6" transform="translate(0,0)"/>
<path d="" fill="#B4B6F4" transform="translate(0,0)"/>
<path d="" fill="#BAB9F2" transform="translate(0,0)"/>
<path d="" fill="#BBBBF4" transform="translate(0,0)"/>
<path d="" fill="#B6BAF5" transform="translate(0,0)"/>
<path d="" fill="#BABCF4" transform="translate(0,0)"/>
<path d="" fill="#BFBFF4" transform="translate(0,0)"/>
<path d="" fill="#B9BDF7" transform="translate(0,0)"/>
<path d="" fill="#A9B1F3" transform="translate(0,0)"/>
<path d="" fill="#BCBCF5" transform="translate(0,0)"/>
<path d="" fill="#B9BAF4" transform="translate(0,0)"/>
<path d="" fill="#BDBFF8" transform="translate(0,0)"/>
<path d="" fill="#B8BCF7" transform="translate(0,0)"/>
<path d="" fill="#B4B9F5" transform="translate(0,0)"/>
<path d="" fill="#C1BFF3" transform="translate(0,0)"/>
<path d="" fill="#B9BBF5" transform="translate(0,0)"/>
<path d="" fill="#BBBEF7" transform="translate(0,0)"/>
<path d="" fill="#BBBFF7" transform="translate(0,0)"/>
<path d="" fill="#B9BDF8" transform="translate(0,0)"/>
<path d="" fill="#B9BCF4" transform="translate(0,0)"/>
<path d="" fill="#C2C2F4" transform="translate(0,0)"/>
<path d="" fill="#B8BBF6" transform="translate(0,0)"/>
<path d="" fill="#BFC0F6" transform="translate(0,0)"/>
<path d="" fill="#C1C3F6" transform="translate(0,0)"/>
<path d="" fill="#B8BCF6" transform="translate(0,0)"/>
<path d="" fill="#B7BDF6" transform="translate(0,0)"/>
<path d="" fill="#BABCF3" transform="translate(0,0)"/>
<path d="" fill="#BEC1F9" transform="translate(0,0)"/>
<path d="" fill="#B4BAF1" transform="translate(0,0)"/>
<path d="" fill="#A2ADEE" transform="translate(0,0)"/>
<path d="" fill="#BDBBF4" transform="translate(0,0)"/>
<path d="" fill="#B7BAF4" transform="translate(0,0)"/>
<path d="" fill="#B9BAF4" transform="translate(0,0)"/>
<path d="" fill="#BBBEF8" transform="translate(0,0)"/>
<path d="" fill="#B1B5F0" transform="translate(0,0)"/>
<path d="" fill="#B7BAF5" transform="translate(0,0)"/>
<path d="" fill="#B8B9F6" transform="translate(0,0)"/>
<path d="" fill="#BFC1F6" transform="translate(0,0)"/>
<path d="" fill="#AEAEEC" transform="translate(0,0)"/>
<path d="" fill="#BBBAF6" transform="translate(0,0)"/>
<path d="" fill="#C1C0F3" transform="translate(0,0)"/>
<path d="" fill="#B9BAF5" transform="translate(0,0)"/>
<path d="" fill="#B4B3F3" transform="translate(0,0)"/>
<path d="" fill="#B7B8F4" transform="translate(0,0)"/>
<path d="" fill="#BABCF6" transform="translate(0,0)"/>
<path d="" fill="#B9BCF8" transform="translate(0,0)"/>
<path d="" fill="#B5BBF9" transform="translate(0,0)"/>
<path d="" fill="#AEAFED" transform="translate(0,0)"/>
<path d="" fill="#B6B9F1" transform="translate(0,0)"/>
<path d="" fill="#B5B7F3" transform="translate(0,0)"/>
<path d="" fill="#B5B7F5" transform="translate(0,0)"/>
<path d="" fill="#BBBCF3" transform="translate(0,0)"/>
<path d="" fill="#B4B7F4" transform="translate(0,0)"/>
<path d="" fill="#B3B9F4" transform="translate(0,0)"/>
<path d="" fill="#B5B7F4" transform="translate(0,0)"/>
<path d="" fill="#B3B7F3" transform="translate(0,0)"/>
<path d="" fill="#B5BBF4" transform="translate(0,0)"/>
<path d="" fill="#B8BCF7" transform="translate(0,0)"/>
<path d="" fill="#635DBC" transform="translate(0,0)"/>
<path d="" fill="#AFB1F2" transform="translate(0,0)"/>
<path d="" fill="#B1B5F5" transform="translate(0,0)"/>
<path d="" fill="#B8BCF7" transform="translate(0,0)"/>
<path d="" fill="#B5BCF1" transform="translate(0,0)"/>
<path d="" fill="#B4B9F7" transform="translate(0,0)"/>
<path d="" fill="#B2B3F1" transform="translate(0,0)"/>
<path d="" fill="#B1B7F5" transform="translate(0,0)"/>
<path d="" fill="#AAB3F0" transform="translate(0,0)"/>
<path d="" fill="#554FB5" transform="translate(0,0)"/>
<path d="" fill="#ADB5F5" transform="translate(0,0)"/>
<path d="" fill="#B3B8F5" transform="translate(0,0)"/>
<path d="" fill="#AFB7F4" transform="translate(0,0)"/>
<path d="" fill="#A5A6EA" transform="translate(0,0)"/>
<path d="" fill="#B4B8F2" transform="translate(0,0)"/>
<path d="" fill="#A6A9EB" transform="translate(0,0)"/>
<path d="" fill="#AAB1ED" transform="translate(0,0)"/>
<path d="" fill="#ABB2EF" transform="translate(0,0)"/>
<path d="" fill="#A8B3EB" transform="translate(0,0)"/>
<path d="" fill="#A9B1EC" transform="translate(0,0)"/>
<path d="" fill="#ACB4EE" transform="translate(0,0)"/>
<path d="" fill="#5650B2" transform="translate(0,0)"/>
<path d="" fill="#BDBFF4" transform="translate(0,0)"/>
<path d="" fill="#BCBFF4" transform="translate(0,0)"/>
<path d="" fill="#BABDF6" transform="translate(0,0)"/>
<path d="" fill="#BCC1F2" transform="translate(0,0)"/>
<path d="" fill="#BCC0F6" transform="translate(0,0)"/>
<path d="" fill="#BCC0F6" transform="translate(0,0)"/>
<path d="" fill="#B9BEF6" transform="translate(0,0)"/>
<path d="" fill="#B8BCF4" transform="translate(0,0)"/>
<path d="" fill="#BDC0F6" transform="translate(0,0)"/>
<path d="" fill="#BCC1F8" transform="translate(0,0)"/>
<path d="" fill="#BDBFF8" transform="translate(0,0)"/>
<path d="" fill="#BDBEF5" transform="translate(0,0)"/>
<path d="" fill="#C0C3FA" transform="translate(0,0)"/>
<path d="" fill="#C0BFF5" transform="translate(0,0)"/>
<path d="" fill="#BEC0F4" transform="translate(0,0)"/>
<path d="" fill="#BFC3F6" transform="translate(0,0)"/>
<path d="" fill="#C0C3F5" transform="translate(0,0)"/>
<path d="" fill="#BEC0F4" transform="translate(0,0)"/>
<path d="" fill="#B6B5F4" transform="translate(0,0)"/>
<path d="" fill="#BABCF2" transform="translate(0,0)"/>
<path d="" fill="#BEC2F8" transform="translate(0,0)"/>
<path d="" fill="#C3C7F8" transform="translate(0,0)"/>
<path d="" fill="#C0C5F9" transform="translate(0,0)"/>
<path d="" fill="#BFC2F4" transform="translate(0,0)"/>
<path d="" fill="#BDC0F2" transform="translate(0,0)"/>
<path d="" fill="#BDC1F1" transform="translate(0,0)"/>
<path d="" fill="#BDC1F3" transform="translate(0,0)"/>
<path d="" fill="#BDC2F7" transform="translate(0,0)"/>
<path d="" fill="#B9C0F3" transform="translate(0,0)"/>
<path d="" fill="#C3C9F5" transform="translate(0,0)"/>
<path d="" fill="#BEC2F8" transform="translate(0,0)"/>
<path d="" fill="#BEC2F8" transform="translate(0,0)"/>
<path d="" fill="#C6C9F8" transform="translate(0,0)"/>
<path d="" fill="#BABCF1" transform="translate(0,0)"/>
<path d="" fill="#C6C9F7" transform="translate(0,0)"/>
<path d="" fill="#C1C5F6" transform="translate(0,0)"/>
<path d="" fill="#C3C2F6" transform="translate(0,0)"/>
<path d="" fill="#CDCBF9" transform="translate(0,0)"/>
<path d="" fill="#C3C2F8" transform="translate(0,0)"/>
<path d="" fill="#C4C4F9" transform="translate(0,0)"/>
<path d="" fill="#C0C3F8" transform="translate(0,0)"/>
<path d="" fill="#BFC2F5" transform="translate(0,0)"/>
<path d="" fill="#BDBFF5" transform="translate(0,0)"/>
<path d="" fill="#C5C7F5" transform="translate(0,0)"/>
<path d="" fill="#C4C6F8" transform="translate(0,0)"/>
<path d="" fill="#C9CCF9" transform="translate(0,0)"/>
<path d="" fill="#B5BCF2" transform="translate(0,0)"/>
<path d="" fill="#C3C4F6" transform="translate(0,0)"/>
<path d="" fill="#B6B7F2" transform="translate(0,0)"/>
<path d="" fill="#B8B9F0" transform="translate(0,0)"/>
<path d="" fill="#C2C4F8" transform="translate(0,0)"/>
<path d="" fill="#C4C5F5" transform="translate(0,0)"/>
<path d="" fill="#BFC4F5" transform="translate(0,0)"/>
<path d="" fill="#B1B9F4" transform="translate(0,0)"/>
<path d="" fill="#ACB6F2" transform="translate(0,0)"/>
<path d="" fill="#C1C4F7" transform="translate(0,0)"/>
<path d="" fill="#C4C7F8" transform="translate(0,0)"/>
<path d="" fill="#C0C2F7" transform="translate(0,0)"/>
<path d="" fill="#C4C8F8" transform="translate(0,0)"/>
<path d="" fill="#C0C1F9" transform="translate(0,0)"/>
<path d="" fill="#C1C3F8" transform="translate(0,0)"/>
<path d="" fill="#C6C9F7" transform="translate(0,0)"/>
<path d="" fill="#C7CBF8" transform="translate(0,0)"/>
<path d="" fill="#B4BCF3" transform="translate(0,0)"/>
<path d="" fill="#C9C9F8" transform="translate(0,0)"/>
<path d="" fill="#BBBCF0" transform="translate(0,0)"/>
<path d="" fill="#C1C3F6" transform="translate(0,0)"/>
<path d="" fill="#B5BDF3" transform="translate(0,0)"/>
<path d="" fill="#C9CAF6" transform="translate(0,0)"/>
<path d="" fill="#C6C9F9" transform="translate(0,0)"/>
<path d="" fill="#C7CBFA" transform="translate(0,0)"/>
<path d="" fill="#CACEF8" transform="translate(0,0)"/>
<path d="" fill="#C5C8F9" transform="translate(0,0)"/>
<path d="" fill="#C9CDF9" transform="translate(0,0)"/>
<path d="" fill="#C6CAF9" transform="translate(0,0)"/>
<path d="" fill="#C1C5F8" transform="translate(0,0)"/>
<path d="" fill="#CBCEF6" transform="translate(0,0)"/>
<path d="" fill="#C0C6F8" transform="translate(0,0)"/>
<path d="" fill="#C2C8F6" transform="translate(0,0)"/>
<path d="" fill="#CACBF9" transform="translate(0,0)"/>
<path d="" fill="#C7CCF8" transform="translate(0,0)"/>
<path d="" fill="#B9BAF0" transform="translate(0,0)"/>
<path d="" fill="#C3C4F4" transform="translate(0,0)"/>
<path d="" fill="#D8D3F8" transform="translate(0,0)"/>
<path d="" fill="#CDCFF9" transform="translate(0,0)"/>
<path d="" fill="#DBDBFA" transform="translate(0,0)"/>
<path d="" fill="#E0E1FB" transform="translate(0,0)"/>
<path d="" fill="#C8C7EF" transform="translate(0,0)"/>
<path d="" fill="#D2D5FB" transform="translate(0,0)"/>
<path d="" fill="#7A78CD" transform="translate(0,0)"/>
<path d="" fill="#BDBDF3" transform="translate(0,0)"/>
<path d="" fill="#C6C9FA" transform="translate(0,0)"/>
<path d="" fill="#CDD0FD" transform="translate(0,0)"/>
<path d="" fill="#D3D2FC" transform="translate(0,0)"/>
<path d="" fill="#C1BFF5" transform="translate(0,0)"/>
<path d="" fill="#C2C5F9" transform="translate(0,0)"/>
<path d="" fill="#C3C9F8" transform="translate(0,0)"/>
<path d="" fill="#B6BDF3" transform="translate(0,0)"/>
<path d="" fill="#D3D3FA" transform="translate(0,0)"/>
<path d="" fill="#CCCFF9" transform="translate(0,0)"/>
<path d="" fill="#C8C9F9" transform="translate(0,0)"/>
<path d="" fill="#CCCFFC" transform="translate(0,0)"/>
<path d="" fill="#CECDFA" transform="translate(0,0)"/>
<path d="" fill="#C6CAF8" transform="translate(0,0)"/>
<path d="" fill="#C8C8F5" transform="translate(0,0)"/>
<path d="" fill="#D8D4FA" transform="translate(0,0)"/>
<path d="" fill="#C8C8FA" transform="translate(0,0)"/>
<path d="" fill="#CCCFFC" transform="translate(0,0)"/>
<path d="" fill="#CBCEFA" transform="translate(0,0)"/>
<path d="" fill="#D2D4F8" transform="translate(0,0)"/>
<path d="" fill="#CBCEFB" transform="translate(0,0)"/>
<path d="" fill="#D0D3F9" transform="translate(0,0)"/>
<path d="" fill="#CFD2F9" transform="translate(0,0)"/>
<path d="" fill="#CBCFFB" transform="translate(0,0)"/>
<path d="" fill="#D1D2FB" transform="translate(0,0)"/>
<path d="" fill="#CFD5F9" transform="translate(0,0)"/>
<path d="" fill="#D5D4F6" transform="translate(0,0)"/>
<path d="" fill="#CFD0FB" transform="translate(0,0)"/>
<path d="" fill="#D1D2FB" transform="translate(0,0)"/>
<path d="" fill="#C1C0F4" transform="translate(0,0)"/>
<path d="" fill="#CBCEF6" transform="translate(0,0)"/>
<path d="" fill="#C1C7F6" transform="translate(0,0)"/>
<path d="" fill="#D0D3FB" transform="translate(0,0)"/>
<path d="" fill="#C8CBFA" transform="translate(0,0)"/>
<path d="" fill="#BEC4F6" transform="translate(0,0)"/>
<path d="" fill="#BFBDF4" transform="translate(0,0)"/>
<path d="" fill="#DAD9FA" transform="translate(0,0)"/>
<path d="" fill="#D2D6FB" transform="translate(0,0)"/>
<path d="" fill="#D0D1FB" transform="translate(0,0)"/>
<path d="" fill="#CBCDF8" transform="translate(0,0)"/>
<path d="" fill="#CFD2F8" transform="translate(0,0)"/>
<path d="" fill="#CECEF6" transform="translate(0,0)"/>
<path d="" fill="#D7D6F8" transform="translate(0,0)"/>
<path d="" fill="#CDCFF8" transform="translate(0,0)"/>
<path d="" fill="#D6D5F8" transform="translate(0,0)"/>
<path d="" fill="#D4D8FA" transform="translate(0,0)"/>
<path d="" fill="#CACDF9" transform="translate(0,0)"/>
<path d="" fill="#BABDF4" transform="translate(0,0)"/>
<path d="" fill="#D9D6FC" transform="translate(0,0)"/>
<path d="" fill="#D9D7FC" transform="translate(0,0)"/>
<path d="" fill="#CECDF9" transform="translate(0,0)"/>
<path d="" fill="#CDCDFD" transform="translate(0,0)"/>
<path d="" fill="#CFD1FE" transform="translate(0,0)"/>
<path d="" fill="#CED1FD" transform="translate(0,0)"/>
<path d="" fill="#D1D1FA" transform="translate(0,0)"/>
<path d="" fill="#CDCEFA" transform="translate(0,0)"/>
<path d="" fill="#D2D4FA" transform="translate(0,0)"/>
<path d="" fill="#CBCFFB" transform="translate(0,0)"/>
<path d="" fill="#CFD1FB" transform="translate(0,0)"/>
<path d="" fill="#CCCEFA" transform="translate(0,0)"/>
<path d="" fill="#CACEF9" transform="translate(0,0)"/>
<path d="" fill="#6460BE" transform="translate(0,0)"/>
<path d="" fill="#CFD2FF" transform="translate(0,0)"/>
<path d="" fill="#C8C8FA" transform="translate(0,0)"/>
<path d="" fill="#CDCEFA" transform="translate(0,0)"/>
<path d="" fill="#D2D3FC" transform="translate(0,0)"/>
<path d="" fill="#CCCCFC" transform="translate(0,0)"/>
<path d="" fill="#CBCDFB" transform="translate(0,0)"/>
<path d="" fill="#CBCEF9" transform="translate(0,0)"/>
<path d="" fill="#CCD0FD" transform="translate(0,0)"/>
<path d="" fill="#CDCFF9" transform="translate(0,0)"/>
<path d="" fill="#CACEFA" transform="translate(0,0)"/>
<path d="" fill="#CBCFF9" transform="translate(0,0)"/>
<path d="" fill="#CCCFFA" transform="translate(0,0)"/>
<path d="" fill="#CDD1F8" transform="translate(0,0)"/>
<path d="" fill="#D1D4FC" transform="translate(0,0)"/>
<path d="" fill="#BEC1F5" transform="translate(0,0)"/>
<path d="" fill="#D3D4FC" transform="translate(0,0)"/>
<path d="" fill="#D2D4F9" transform="translate(0,0)"/>
<path d="" fill="#CBCAF9" transform="translate(0,0)"/>
<path d="" fill="#D0CFF9" transform="translate(0,0)"/>
<path d="" fill="#BFC6F9" transform="translate(0,0)"/>
<path d="" fill="#CCCCFA" transform="translate(0,0)"/>
<path d="" fill="#D1D1FA" transform="translate(0,0)"/>
<path d="" fill="#CFD0FA" transform="translate(0,0)"/>
<path d="" fill="#D0D2FA" transform="translate(0,0)"/>
<path d="" fill="#DFDDFD" transform="translate(0,0)"/>
<path d="" fill="#D1D2FB" transform="translate(0,0)"/>
<path d="" fill="#CBCFFC" transform="translate(0,0)"/>
<path d="" fill="#D2D5FC" transform="translate(0,0)"/>
<path d="" fill="#D1D5F9" transform="translate(0,0)"/>
<path d="" fill="#BDC4F9" transform="translate(0,0)"/>
<path d="" fill="#D6D4FA" transform="translate(0,0)"/>
<path d="" fill="#D1D1FA" transform="translate(0,0)"/>
<path d="" fill="#C8CEFC" transform="translate(0,0)"/>
<path d="" fill="#BCC3F8" transform="translate(0,0)"/>
<path d="" fill="#CBCCF7" transform="translate(0,0)"/>
<path d="" fill="#BCC3F6" transform="translate(0,0)"/>
<path d="" fill="#CFD1FA" transform="translate(0,0)"/>
<path d="" fill="#D1D1FB" transform="translate(0,0)"/>
<path d="" fill="#D2D1F9" transform="translate(0,0)"/>
<path d="" fill="#CBCBF8" transform="translate(0,0)"/>
<path d="" fill="#CECFFC" transform="translate(0,0)"/>
<path d="" fill="#D1D3FC" transform="translate(0,0)"/>
<path d="" fill="#C3C9F9" transform="translate(0,0)"/>
<path d="" fill="#BFC4F7" transform="translate(0,0)"/>
<path d="" fill="#C0C1F5" transform="translate(0,0)"/>
<path d="" fill="#CED0FA" transform="translate(0,0)"/>
<path d="" fill="#CED0FA" transform="translate(0,0)"/>
<path d="" fill="#C2C9F5" transform="translate(0,0)"/>
<path d="" fill="#DCDBF9" transform="translate(0,0)"/>
<path d="" fill="#CCCFF9" transform="translate(0,0)"/>
<path d="" fill="#C1C2F4" transform="translate(0,0)"/>
<path d="" fill="#D9D9FC" transform="translate(0,0)"/>
<path d="" fill="#CDCFFB" transform="translate(0,0)"/>
<path d="" fill="#D1D3FB" transform="translate(0,0)"/>
<path d="" fill="#CDD1F8" transform="translate(0,0)"/>
<path d="" fill="#D9DDFF" transform="translate(0,0)"/>
<path d="" fill="#5C58BA" transform="translate(0,0)"/>
<path d="" fill="#CDD1FD" transform="translate(0,0)"/>
<path d="" fill="#CECFFC" transform="translate(0,0)"/>
<path d="" fill="#CED0FE" transform="translate(0,0)"/>
<path d="" fill="#C0C2F5" transform="translate(0,0)"/>
<path d="" fill="#CFD0FD" transform="translate(0,0)"/>
<path d="" fill="#CACBFA" transform="translate(0,0)"/>
<path d="" fill="#D1D1FC" transform="translate(0,0)"/>
<path d="" fill="#D6D6FB" transform="translate(0,0)"/>
<path d="" fill="#D1D2FC" transform="translate(0,0)"/>
<path d="" fill="#D5D6FB" transform="translate(0,0)"/>
<path d="" fill="#D6D8FC" transform="translate(0,0)"/>
<path d="" fill="#CACAF9" transform="translate(0,0)"/>
<path d="" fill="#D0D3FA" transform="translate(0,0)"/>
<path d="" fill="#C5C8F8" transform="translate(0,0)"/>
<path d="" fill="#D7D7FA" transform="translate(0,0)"/>
<path d="" fill="#D0D3FB" transform="translate(0,0)"/>
<path d="" fill="#D3D5FB" transform="translate(0,0)"/>
<path d="" fill="#CFD3FB" transform="translate(0,0)"/>
<path d="" fill="#D0D2FB" transform="translate(0,0)"/>
<path d="" fill="#C3C1F3" transform="translate(0,0)"/>
<path d="" fill="#CFD0F7" transform="translate(0,0)"/>
<path d="" fill="#D0D0FC" transform="translate(0,0)"/>
<path d="" fill="#D1D2FB" transform="translate(0,0)"/>
<path d="" fill="#CECFFD" transform="translate(0,0)"/>
<path d="" fill="#CECEF9" transform="translate(0,0)"/>
<path d="" fill="#CBCDFC" transform="translate(0,0)"/>
<path d="" fill="#CACCF7" transform="translate(0,0)"/>
<path d="" fill="#D2D4FA" transform="translate(0,0)"/>
<path d="" fill="#CACDFA" transform="translate(0,0)"/>
<path d="" fill="#C2C3F6" transform="translate(0,0)"/>
<path d="" fill="#E2E1FB" transform="translate(0,0)"/>
<path d="" fill="#D2D3FB" transform="translate(0,0)"/>
<path d="" fill="#CDD2FD" transform="translate(0,0)"/>
<path d="" fill="#CCD0FC" transform="translate(0,0)"/>
<path d="" fill="#C7C7FA" transform="translate(0,0)"/>
<path d="" fill="#BEC5F3" transform="translate(0,0)"/>
<path d="" fill="#D1D0FC" transform="translate(0,0)"/>
<path d="" fill="#D6D8FF" transform="translate(0,0)"/>
<path d="" fill="#D2D2FB" transform="translate(0,0)"/>
<path d="" fill="#D0D2FC" transform="translate(0,0)"/>
<path d="" fill="#DEDCFE" transform="translate(0,0)"/>
<path d="" fill="#D2D1FC" transform="translate(0,0)"/>
<path d="" fill="#C3C7F6" transform="translate(0,0)"/>
<path d="" fill="#BFBFF1" transform="translate(0,0)"/>
<path d="" fill="#D7D5FE" transform="translate(0,0)"/>
<path d="" fill="#D1D3FC" transform="translate(0,0)"/>
<path d="" fill="#D3D3FB" transform="translate(0,0)"/>
<path d="" fill="#D2D1F9" transform="translate(0,0)"/>
<path d="" fill="#DCDAFF" transform="translate(0,0)"/>
<path d="" fill="#CFCFF8" transform="translate(0,0)"/>
<path d="" fill="#CFD3FC" transform="translate(0,0)"/>
<path d="" fill="#C0C1F6" transform="translate(0,0)"/>
<path d="" fill="#CFD0FB" transform="translate(0,0)"/>
<path d="" fill="#CACDFB" transform="translate(0,0)"/>
<path d="" fill="#D0D1F9" transform="translate(0,0)"/>
<path d="" fill="#C9CEF9" transform="translate(0,0)"/>
<path d="" fill="#D5D9FA" transform="translate(0,0)"/>
<path d="" fill="#CACCF8" transform="translate(0,0)"/>
<path d="" fill="#D0D5FB" transform="translate(0,0)"/>
<path d="" fill="#BDC4F6" transform="translate(0,0)"/>
<path d="" fill="#CFD1FC" transform="translate(0,0)"/>
<path d="" fill="#D1D2FF" transform="translate(0,0)"/>
<path d="" fill="#D1D2FD" transform="translate(0,0)"/>
<path d="" fill="#CDCFFD" transform="translate(0,0)"/>
<path d="" fill="#CCCFFB" transform="translate(0,0)"/>
<path d="" fill="#BFC1F5" transform="translate(0,0)"/>
<path d="" fill="#D6D7FA" transform="translate(0,0)"/>
<path d="" fill="#D2D3FA" transform="translate(0,0)"/>
<path d="" fill="#CCCFF9" transform="translate(0,0)"/>
<path d="" fill="#D3D2FE" transform="translate(0,0)"/>
<path d="" fill="#D0D0FC" transform="translate(0,0)"/>
<path d="" fill="#D9DBFA" transform="translate(0,0)"/>
<path d="" fill="#D2D3FB" transform="translate(0,0)"/>
<path d="" fill="#D4D5FA" transform="translate(0,0)"/>
<path d="" fill="#DFE2FD" transform="translate(0,0)"/>
<path d="" fill="#CFD1FA" transform="translate(0,0)"/>
<path d="" fill="#D7D8FA" transform="translate(0,0)"/>
<path d="" fill="#D4D4FC" transform="translate(0,0)"/>
<path d="" fill="#D0D3FB" transform="translate(0,0)"/>
<path d="" fill="#D2D5FD" transform="translate(0,0)"/>
<path d="" fill="#CFD1F8" transform="translate(0,0)"/>
<path d="" fill="#BDC4F9" transform="translate(0,0)"/>
<path d="" fill="#CBCCF7" transform="translate(0,0)"/>
<path d="" fill="#D1D0FE" transform="translate(0,0)"/>
<path d="" fill="#CED0FD" transform="translate(0,0)"/>
<path d="" fill="#BFC6F8" transform="translate(0,0)"/>
<path d="" fill="#CFCFF9" transform="translate(0,0)"/>
<path d="" fill="#CCCFF6" transform="translate(0,0)"/>
<path d="" fill="#CED0F9" transform="translate(0,0)"/>
<path d="" fill="#C6CCF8" transform="translate(0,0)"/>
<path d="" fill="#CBCDFA" transform="translate(0,0)"/>
<path d="" fill="#CCCFF5" transform="translate(0,0)"/>
<path d="" fill="#CACCFA" transform="translate(0,0)"/>
<path d="" fill="#DEDAFC" transform="translate(0,0)"/>
<path d="" fill="#DBD9FC" transform="translate(0,0)"/>
<path d="" fill="#D0D3FB" transform="translate(0,0)"/>
<path d="" fill="#7775CC" transform="translate(0,0)"/>
<path d="" fill="#D1CFFC" transform="translate(0,0)"/>
<path d="" fill="#CBCEF9" transform="translate(0,0)"/>
<path d="" fill="#CACDF8" transform="translate(0,0)"/>
<path d="" fill="#C1C2F5" transform="translate(0,0)"/>
<path d="" fill="#CBCDF9" transform="translate(0,0)"/>
<path d="" fill="#C9CDFA" transform="translate(0,0)"/>
<path d="" fill="#CED0F5" transform="translate(0,0)"/>
<path d="" fill="#C8CBF9" transform="translate(0,0)"/>
<path d="" fill="#CCCCF9" transform="translate(0,0)"/>
<path d="" fill="#D9DAF7" transform="translate(0,0)"/>
<path d="" fill="#C2C5F8" transform="translate(0,0)"/>
<path d="" fill="#CDD0FC" transform="translate(0,0)"/>
<path d="" fill="#C7CBFA" transform="translate(0,0)"/>
<path d="" fill="#C8CCFB" transform="translate(0,0)"/>
<path d="" fill="#CED1FA" transform="translate(0,0)"/>
<path d="" fill="#CFD3FD" transform="translate(0,0)"/>
<path d="" fill="#D2D6F9" transform="translate(0,0)"/>
<path d="" fill="#C9CDF8" transform="translate(0,0)"/>
<path d="" fill="#CBCBF9" transform="translate(0,0)"/>
<path d="" fill="#C3CBF6" transform="translate(0,0)"/>
<path d="" fill="#CACBF9" transform="translate(0,0)"/>
<path d="" fill="#C4C7F8" transform="translate(0,0)"/>
<path d="" fill="#CDD0F9" transform="translate(0,0)"/>
<path d="" fill="#CBCEF5" transform="translate(0,0)"/>
<path d="" fill="#C1C7F8" transform="translate(0,0)"/>
<path d="" fill="#CFD2F8" transform="translate(0,0)"/>
<path d="" fill="#C7CAF8" transform="translate(0,0)"/>
<path d="" fill="#CECFFA" transform="translate(0,0)"/>
<path d="" fill="#C4C6F8" transform="translate(0,0)"/>
<path d="" fill="#C8CAF6" transform="translate(0,0)"/>
<path d="" fill="#C5CAF5" transform="translate(0,0)"/>
<path d="" fill="#CACDFA" transform="translate(0,0)"/>
<path d="" fill="#C7CBF7" transform="translate(0,0)"/>
<path d="" fill="#C8CBF7" transform="translate(0,0)"/>
<path d="" fill="#C6CAF9" transform="translate(0,0)"/>
<path d="" fill="#C5C9F9" transform="translate(0,0)"/>
<path d="" fill="#C3CAF9" transform="translate(0,0)"/>
<path d="" fill="#C5C7F8" transform="translate(0,0)"/>
<path d="" fill="#C8C8F8" transform="translate(0,0)"/>
<path d="" fill="#C9CDFC" transform="translate(0,0)"/>
<path d="" fill="#C7C9F7" transform="translate(0,0)"/>
<path d="" fill="#CACCF7" transform="translate(0,0)"/>
<path d="" fill="#CBCEF9" transform="translate(0,0)"/>
<path d="" fill="#C9CBF9" transform="translate(0,0)"/>
<path d="" fill="#C7CBF9" transform="translate(0,0)"/>
<path d="" fill="#C5C6F4" transform="translate(0,0)"/>
<path d="" fill="#C3C6F8" transform="translate(0,0)"/>
<path d="" fill="#C9C9F6" transform="translate(0,0)"/>
<path d="" fill="#C1C2F8" transform="translate(0,0)"/>
<path d="" fill="#CDCFF9" transform="translate(0,0)"/>
<path d="" fill="#D0D0FA" transform="translate(0,0)"/>
<path d="" fill="#CCCDF8" transform="translate(0,0)"/>
<path d="" fill="#C9CBF5" transform="translate(0,0)"/>
<path d="" fill="#D0D0F8" transform="translate(0,0)"/>
<path d="" fill="#C7CAF8" transform="translate(0,0)"/>
<path d="" fill="#C5CBFB" transform="translate(0,0)"/>
<path d="" fill="#C3C7F8" transform="translate(0,0)"/>
<path d="" fill="#C4C8F8" transform="translate(0,0)"/>
<path d="" fill="#C7CAF9" transform="translate(0,0)"/>
<path d="" fill="#CCCCF8" transform="translate(0,0)"/>
<path d="" fill="#C9C9F6" transform="translate(0,0)"/>
<path d="" fill="#C9CCF5" transform="translate(0,0)"/>
<path d="" fill="#CBCCF9" transform="translate(0,0)"/>
<path d="" fill="#C9C9F6" transform="translate(0,0)"/>
<path d="" fill="#C8CBF7" transform="translate(0,0)"/>
<path d="" fill="#C3C6F8" transform="translate(0,0)"/>
<path d="" fill="#BDC1F7" transform="translate(0,0)"/>
<path d="" fill="#CECEFA" transform="translate(0,0)"/>
<path d="" fill="#C7CBF9" transform="translate(0,0)"/>
<path d="" fill="#C6CAF8" transform="translate(0,0)"/>
<path d="" fill="#C8CBFB" transform="translate(0,0)"/>
<path d="" fill="#8C8BDC" transform="translate(0,0)"/>
<path d="" fill="#CACBFB" transform="translate(0,0)"/>
<path d="" fill="#CBCDFA" transform="translate(0,0)"/>
<path d="" fill="#CBCBFA" transform="translate(0,0)"/>
<path d="" fill="#BFC4F9" transform="translate(0,0)"/>
<path d="" fill="#BEC3F8" transform="translate(0,0)"/>
<path d="" fill="#BFBFF4" transform="translate(0,0)"/>
<path d="" fill="#CBC9F6" transform="translate(0,0)"/>
<path d="" fill="#CBCCF8" transform="translate(0,0)"/>
<path d="" fill="#C3C7F8" transform="translate(0,0)"/>
<path d="" fill="#BEC4F6" transform="translate(0,0)"/>
<path d="" fill="#C3C8F8" transform="translate(0,0)"/>
<path d="" fill="#C5C8F9" transform="translate(0,0)"/>
<path d="" fill="#CCCDF7" transform="translate(0,0)"/>
<path d="" fill="#CBCEFA" transform="translate(0,0)"/>
<path d="" fill="#C7C9FB" transform="translate(0,0)"/>
<path d="" fill="#C5C9FB" transform="translate(0,0)"/>
<path d="" fill="#C3C9F9" transform="translate(0,0)"/>
<path d="" fill="#C8C9F8" transform="translate(0,0)"/>
<path d="" fill="#B6BDF6" transform="translate(0,0)"/>
<path d="" fill="#BBB9F2" transform="translate(0,0)"/>
<path d="" fill="#CECCF8" transform="translate(0,0)"/>
<path d="" fill="#C7CAF9" transform="translate(0,0)"/>
<path d="" fill="#C8CBFC" transform="translate(0,0)"/>
<path d="" fill="#C7CBFA" transform="translate(0,0)"/>
<path d="" fill="#B8BFF7" transform="translate(0,0)"/>
<path d="" fill="#BABAF3" transform="translate(0,0)"/>
<path d="" fill="#C7C8F4" transform="translate(0,0)"/>
<path d="" fill="#CACCFA" transform="translate(0,0)"/>
<path d="" fill="#C7C6F8" transform="translate(0,0)"/>
<path d="" fill="#CDCEFB" transform="translate(0,0)"/>
<path d="" fill="#BAC2F5" transform="translate(0,0)"/>
<path d="" fill="#CCCAF6" transform="translate(0,0)"/>
<path d="" fill="#C8C8F8" transform="translate(0,0)"/>
<path d="" fill="#C6C6FA" transform="translate(0,0)"/>
<path d="" fill="#C8C9F9" transform="translate(0,0)"/>
<path d="" fill="#C9CBF9" transform="translate(0,0)"/>
<path d="" fill="#C7C6F5" transform="translate(0,0)"/>
<path d="" fill="#CCCEF8" transform="translate(0,0)"/>
<path d="" fill="#BDBDF1" transform="translate(0,0)"/>
<path d="" fill="#C5C7F3" transform="translate(0,0)"/>
<path d="" fill="#CCCEF9" transform="translate(0,0)"/>
<path d="" fill="#CFD3FC" transform="translate(0,0)"/>
<path d="" fill="#C5CAFC" transform="translate(0,0)"/>
<path d="" fill="#C3CAF9" transform="translate(0,0)"/>
<path d="" fill="#BDBDF1" transform="translate(0,0)"/>
<path d="" fill="#C2BFF5" transform="translate(0,0)"/>
<path d="" fill="#C9C9F9" transform="translate(0,0)"/>
<path d="" fill="#C7C9FC" transform="translate(0,0)"/>
<path d="" fill="#BDBEF1" transform="translate(0,0)"/>
<path d="" fill="#C4C5F5" transform="translate(0,0)"/>
<path d="" fill="#C9C8FA" transform="translate(0,0)"/>
<path d="" fill="#C5C9F9" transform="translate(0,0)"/>
<path d="" fill="#CACDFB" transform="translate(0,0)"/>
<path d="" fill="#BCC4FA" transform="translate(0,0)"/>
<path d="" fill="#C8C7F6" transform="translate(0,0)"/>
<path d="" fill="#D0D2FA" transform="translate(0,0)"/>
<path d="" fill="#CBCAFB" transform="translate(0,0)"/>
<path d="" fill="#CBCBF9" transform="translate(0,0)"/>
<path d="" fill="#CECCF7" transform="translate(0,0)"/>
<path d="" fill="#CCCCF9" transform="translate(0,0)"/>
<path d="" fill="#D0CEFB" transform="translate(0,0)"/>
<path d="" fill="#D2D2FB" transform="translate(0,0)"/>
<path d="" fill="#CBCEFA" transform="translate(0,0)"/>
<path d="" fill="#C6CAF9" transform="translate(0,0)"/>
<path d="" fill="#BEC5F6" transform="translate(0,0)"/>
<path d="" fill="#CECEF8" transform="translate(0,0)"/>
<path d="" fill="#BDBCF1" transform="translate(0,0)"/>
<path d="" fill="#D2D3FA" transform="translate(0,0)"/>
<path d="" fill="#C5CAF9" transform="translate(0,0)"/>
<path d="" fill="#C9CAF9" transform="translate(0,0)"/>
<path d="" fill="#D0CFFA" transform="translate(0,0)"/>
<path d="" fill="#D3D4FA" transform="translate(0,0)"/>
<path d="" fill="#C8CCF9" transform="translate(0,0)"/>
<path d="" fill="#BBBCF6" transform="translate(0,0)"/>
<path d="" fill="#C7C9FA" transform="translate(0,0)"/>
<path d="" fill="#C4CAFA" transform="translate(0,0)"/>
<path d="" fill="#CECEFA" transform="translate(0,0)"/>
<path d="" fill="#C3C7F6" transform="translate(0,0)"/>
<path d="" fill="#CACCF9" transform="translate(0,0)"/>
<path d="" fill="#CFD3FB" transform="translate(0,0)"/>
<path d="" fill="#CECEF9" transform="translate(0,0)"/>
<path d="" fill="#CDCFFA" transform="translate(0,0)"/>
<path d="" fill="#CBCEF8" transform="translate(0,0)"/>
<path d="" fill="#CCCEFB" transform="translate(0,0)"/>
<path d="" fill="#BDC3F8" transform="translate(0,0)"/>
<path d="" fill="#B8B6EF" transform="translate(0,0)"/>
<path d="" fill="#BEBEF2" transform="translate(0,0)"/>
<path d="" fill="#CBCDFA" transform="translate(0,0)"/>
<path d="" fill="#CDCEFA" transform="translate(0,0)"/>
<path d="" fill="#CDCDFB" transform="translate(0,0)"/>
<path d="" fill="#D4D7FA" transform="translate(0,0)"/>
<path d="" fill="#CDCFFB" transform="translate(0,0)"/>
<path d="" fill="#CCD1FC" transform="translate(0,0)"/>
<path d="" fill="#B6C2F7" transform="translate(0,0)"/>
<path d="" fill="#BABAF0" transform="translate(0,0)"/>
<path d="" fill="#D7D6FB" transform="translate(0,0)"/>
<path d="" fill="#CED0FC" transform="translate(0,0)"/>
<path d="" fill="#D2D1F9" transform="translate(0,0)"/>
<path d="" fill="#D2D3FA" transform="translate(0,0)"/>
<path d="" fill="#CDCBFB" transform="translate(0,0)"/>
<path d="" fill="#CFD3FC" transform="translate(0,0)"/>
<path d="" fill="#D0D3FD" transform="translate(0,0)"/>
<path d="" fill="#CCCFFB" transform="translate(0,0)"/>
<path d="" fill="#D3D3FA" transform="translate(0,0)"/>
<path d="" fill="#D6D3F6" transform="translate(0,0)"/>
<path d="" fill="#D4D1F9" transform="translate(0,0)"/>
<path d="" fill="#CBCFFC" transform="translate(0,0)"/>
<path d="" fill="#D4D4FD" transform="translate(0,0)"/>
<path d="" fill="#CBCCFB" transform="translate(0,0)"/>
<path d="" fill="#D1CEFA" transform="translate(0,0)"/>
<path d="" fill="#D0CEF8" transform="translate(0,0)"/>
<path d="" fill="#D7D9FB" transform="translate(0,0)"/>
<path d="" fill="#CED0FB" transform="translate(0,0)"/>
<path d="" fill="#CBCDFB" transform="translate(0,0)"/>
<path d="" fill="#CDCEF9" transform="translate(0,0)"/>
<path d="" fill="#BEC6F3" transform="translate(0,0)"/>
<path d="" fill="#D1D3FC" transform="translate(0,0)"/>
<path d="" fill="#D4D4F9" transform="translate(0,0)"/>
<path d="" fill="#DBD8F8" transform="translate(0,0)"/>
<path d="" fill="#D2D4FB" transform="translate(0,0)"/>
<path d="" fill="#CCD2F9" transform="translate(0,0)"/>
<path d="" fill="#CBD0F7" transform="translate(0,0)"/>
<path d="" fill="#D2D1F8" transform="translate(0,0)"/>
<path d="" fill="#D2D3F9" transform="translate(0,0)"/>
<path d="" fill="#CCCEFC" transform="translate(0,0)"/>
<path d="" fill="#D3D7F9" transform="translate(0,0)"/>
<path d="" fill="#D0D3FD" transform="translate(0,0)"/>
<path d="" fill="#D1D1FA" transform="translate(0,0)"/>
<path d="" fill="#CCD0FB" transform="translate(0,0)"/>
<path d="" fill="#CED1FD" transform="translate(0,0)"/>
<path d="" fill="#D6D5F9" transform="translate(0,0)"/>
<path d="" fill="#CECFFB" transform="translate(0,0)"/>
<path d="" fill="#D1D3F8" transform="translate(0,0)"/>
<path d="" fill="#CECDF9" transform="translate(0,0)"/>
<path d="" fill="#D0CFFD" transform="translate(0,0)"/>
<path d="" fill="#CCCDFC" transform="translate(0,0)"/>
<path d="" fill="#CCCFF9" transform="translate(0,0)"/>
<path d="" fill="#CDCEFA" transform="translate(0,0)"/>
<path d="" fill="#D1D3FE" transform="translate(0,0)"/>
<path d="" fill="#CED2FF" transform="translate(0,0)"/>
<path d="" fill="#CED3FE" transform="translate(0,0)"/>
<path d="" fill="#CDCEFD" transform="translate(0,0)"/>
<path d="" fill="#D1D0FA" transform="translate(0,0)"/>
<path d="" fill="#CDCFFA" transform="translate(0,0)"/>
<path d="" fill="#CDCEFB" transform="translate(0,0)"/>
<path d="" fill="#D2D1FB" transform="translate(0,0)"/>
<path d="" fill="#CDD1FC" transform="translate(0,0)"/>
<path d="" fill="#CFD3FE" transform="translate(0,0)"/>
<path d="" fill="#CACDFD" transform="translate(0,0)"/>
<path d="" fill="#7C99E5" transform="translate(0,0)"/>
<path d="" fill="#D5D6F8" transform="translate(0,0)"/>
<path d="" fill="#CFCDF8" transform="translate(0,0)"/>
<path d="" fill="#D0CFFA" transform="translate(0,0)"/>
<path d="" fill="#CDCEFB" transform="translate(0,0)"/>
<path d="" fill="#D1D2FA" transform="translate(0,0)"/>
<path d="" fill="#D0D0FA" transform="translate(0,0)"/>
<path d="" fill="#D0CDF7" transform="translate(0,0)"/>
<path d="" fill="#CED1FB" transform="translate(0,0)"/>
<path d="" fill="#C9CEF9" transform="translate(0,0)"/>
<path d="" fill="#B4BDF3" transform="translate(0,0)"/>
<path d="" fill="#D1D2FC" transform="translate(0,0)"/>
<path d="" fill="#D0CFF8" transform="translate(0,0)"/>
<path d="" fill="#CDCDFA" transform="translate(0,0)"/>
<path d="" fill="#C6CBFA" transform="translate(0,0)"/>
<path d="" fill="#DBDBFA" transform="translate(0,0)"/>
<path d="" fill="#D3D6FD" transform="translate(0,0)"/>
<path d="" fill="#C7CAF7" transform="translate(0,0)"/>
<path d="" fill="#D0CEF9" transform="translate(0,0)"/>
<path d="" fill="#CCCFF7" transform="translate(0,0)"/>
<path d="" fill="#D0D0F7" transform="translate(0,0)"/>
<path d="" fill="#CDCFF9" transform="translate(0,0)"/>
<path d="" fill="#D3D2F9" transform="translate(0,0)"/>
<path d="" fill="#D0D1FA" transform="translate(0,0)"/>
<path d="" fill="#D0D3FC" transform="translate(0,0)"/>
<path d="" fill="#D9DAFA" transform="translate(0,0)"/>
<path d="" fill="#C2C6F9" transform="translate(0,0)"/>
<path d="" fill="#D3D3FD" transform="translate(0,0)"/>
<path d="" fill="#D0D0FB" transform="translate(0,0)"/>
<path d="" fill="#CED2FB" transform="translate(0,0)"/>
<path d="" fill="#B6BFF8" transform="translate(0,0)"/>
<path d="" fill="#D3D0FC" transform="translate(0,0)"/>
<path d="" fill="#D5D4FB" transform="translate(0,0)"/>
<path d="" fill="#CFD0F8" transform="translate(0,0)"/>
<path d="" fill="#CDCFF9" transform="translate(0,0)"/>
<path d="" fill="#D1D1F8" transform="translate(0,0)"/>
<path d="" fill="#D2D3FC" transform="translate(0,0)"/>
<path d="" fill="#CDCFF9" transform="translate(0,0)"/>
<path d="" fill="#D1CFFA" transform="translate(0,0)"/>
<path d="" fill="#CDCFFC" transform="translate(0,0)"/>
<path d="" fill="#E0DDFD" transform="translate(0,0)"/>
<path d="" fill="#D1D2FB" transform="translate(0,0)"/>
<path d="" fill="#C9CCF7" transform="translate(0,0)"/>
<path d="" fill="#CFCEFB" transform="translate(0,0)"/>
<path d="" fill="#D0CEF9" transform="translate(0,0)"/>
<path d="" fill="#CDCDF9" transform="translate(0,0)"/>
<path d="" fill="#D2D4F9" transform="translate(0,0)"/>
<path d="" fill="#D2D1FB" transform="translate(0,0)"/>
<path d="" fill="#CBCBFA" transform="translate(0,0)"/>
<path d="" fill="#C0C1F2" transform="translate(0,0)"/>
<path d="" fill="#D2D3FA" transform="translate(0,0)"/>
<path d="" fill="#D3D5F9" transform="translate(0,0)"/>
<path d="" fill="#D0CFFD" transform="translate(0,0)"/>
<path d="" fill="#D4D4F8" transform="translate(0,0)"/>
<path d="" fill="#CACCF8" transform="translate(0,0)"/>
<path d="" fill="#D8D7FC" transform="translate(0,0)"/>
<path d="" fill="#D4D6FA" transform="translate(0,0)"/>
<path d="" fill="#D1D2FB" transform="translate(0,0)"/>
<path d="" fill="#D5D3F8" transform="translate(0,0)"/>
<path d="" fill="#D2D4F9" transform="translate(0,0)"/>
<path d="" fill="#D8D9FC" transform="translate(0,0)"/>
<path d="" fill="#D0D0FD" transform="translate(0,0)"/>
<path d="" fill="#DAD8FD" transform="translate(0,0)"/>
<path d="" fill="#D0D0FC" transform="translate(0,0)"/>
<path d="" fill="#D1D1FD" transform="translate(0,0)"/>
<path d="" fill="#D4D3FD" transform="translate(0,0)"/>
<path d="" fill="#D0D3FE" transform="translate(0,0)"/>
<path d="" fill="#D5D5FA" transform="translate(0,0)"/>
<path d="" fill="#BAC0F4" transform="translate(0,0)"/>
<path d="" fill="#CFCEFA" transform="translate(0,0)"/>
<path d="" fill="#CFD0F8" transform="translate(0,0)"/>
<path d="" fill="#D7D9FB" transform="translate(0,0)"/>
<path d="" fill="#CFD0FB" transform="translate(0,0)"/>
<path d="" fill="#D3D2F9" transform="translate(0,0)"/>
<path d="" fill="#E3E5FF" transform="translate(0,0)"/>
<path d="" fill="#D9DAFD" transform="translate(0,0)"/>
<path d="" fill="#D5D9FB" transform="translate(0,0)"/>
<path d="" fill="#CFD0F9" transform="translate(0,0)"/>
<path d="" fill="#D4D7FD" transform="translate(0,0)"/>
<path d="" fill="#D0CDF6" transform="translate(0,0)"/>
<path d="" fill="#D3D5FA" transform="translate(0,0)"/>
<path d="" fill="#D0D0FA" transform="translate(0,0)"/>
<path d="" fill="#D8DAF9" transform="translate(0,0)"/>
<path d="" fill="#D0D0F9" transform="translate(0,0)"/>
<path d="" fill="#CDCCF9" transform="translate(0,0)"/>
<path d="" fill="#CECFF8" transform="translate(0,0)"/>
<path d="" fill="#CDCCF6" transform="translate(0,0)"/>
<path d="" fill="#D9D9FA" transform="translate(0,0)"/>
<path d="" fill="#D0CFFA" transform="translate(0,0)"/>
<path d="" fill="#DCE0FB" transform="translate(0,0)"/>
<path d="" fill="#B6C0F2" transform="translate(0,0)"/>
<path d="" fill="#D3D6FB" transform="translate(0,0)"/>
<path d="" fill="#B5B5ED" transform="translate(0,0)"/>
<path d="" fill="#CECFF7" transform="translate(0,0)"/>
<path d="" fill="#CDCDF7" transform="translate(0,0)"/>
<path d="" fill="#B4B6EE" transform="translate(0,0)"/>
<path d="" fill="#CECEF9" transform="translate(0,0)"/>
<path d="" fill="#CECCF5" transform="translate(0,0)"/>
<path d="" fill="#D4D2F5" transform="translate(0,0)"/>
<path d="" fill="#DAD9F9" transform="translate(0,0)"/>
<path d="" fill="#D3D1F6" transform="translate(0,0)"/>
<path d="" fill="#CAC8FA" transform="translate(0,0)"/>
<path d="" fill="#CACAF9" transform="translate(0,0)"/>
<path d="" fill="#D3D3FA" transform="translate(0,0)"/>
<path d="" fill="#D2D4FB" transform="translate(0,0)"/>
<path d="" fill="#CDD1FE" transform="translate(0,0)"/>
<path d="" fill="#D1D1F8" transform="translate(0,0)"/>
<path d="" fill="#C7C6F5" transform="translate(0,0)"/>
<path d="" fill="#CED0F4" transform="translate(0,0)"/>
<path d="" fill="#D5D3FA" transform="translate(0,0)"/>
<path d="" fill="#D4D6FC" transform="translate(0,0)"/>
<path d="" fill="#CECDF8" transform="translate(0,0)"/>
<path d="" fill="#CBCDFB" transform="translate(0,0)"/>
<path d="" fill="#CFCFFB" transform="translate(0,0)"/>
<path d="" fill="#CFD0F9" transform="translate(0,0)"/>
<path d="" fill="#B8C0F6" transform="translate(0,0)"/>
<path d="" fill="#E0E4FD" transform="translate(0,0)"/>
<path d="" fill="#BABDF2" transform="translate(0,0)"/>
<path d="" fill="#B7C1F5" transform="translate(0,0)"/>
<path d="" fill="#D0D3FA" transform="translate(0,0)"/>
<path d="" fill="#CDD2F8" transform="translate(0,0)"/>
<path d="" fill="#C4C2F5" transform="translate(0,0)"/>
<path d="" fill="#C8C9F9" transform="translate(0,0)"/>
<path d="" fill="#D7D5FB" transform="translate(0,0)"/>
<path d="" fill="#CDCCF7" transform="translate(0,0)"/>
<path d="" fill="#D0D3FD" transform="translate(0,0)"/>
<path d="" fill="#CED0F6" transform="translate(0,0)"/>
<path d="" fill="#C3C7F8" transform="translate(0,0)"/>
<path d="" fill="#BBC3F4" transform="translate(0,0)"/>
<path d="" fill="#D1D0FA" transform="translate(0,0)"/>
<path d="" fill="#CACAF6" transform="translate(0,0)"/>
<path d="" fill="#D2D2FC" transform="translate(0,0)"/>
<path d="" fill="#C5CBF9" transform="translate(0,0)"/>
<path d="" fill="#B5BFF5" transform="translate(0,0)"/>
<path d="" fill="#D4D2FA" transform="translate(0,0)"/>
<path d="" fill="#D2D2FB" transform="translate(0,0)"/>
<path d="" fill="#CBCDF9" transform="translate(0,0)"/>
<path d="" fill="#D1D3FD" transform="translate(0,0)"/>
<path d="" fill="#D0D3FA" transform="translate(0,0)"/>
<path d="" fill="#C5CBF9" transform="translate(0,0)"/>
<path d="" fill="#BEBDF4" transform="translate(0,0)"/>
<path d="" fill="#BEBCF3" transform="translate(0,0)"/>
<path d="" fill="#CBCAF9" transform="translate(0,0)"/>
<path d="" fill="#D2D1FB" transform="translate(0,0)"/>
<path d="" fill="#D5D5FA" transform="translate(0,0)"/>
<path d="" fill="#D2CFFC" transform="translate(0,0)"/>
<path d="" fill="#D0D0F6" transform="translate(0,0)"/>
<path d="" fill="#CCCFFA" transform="translate(0,0)"/>
<path d="" fill="#CED0F7" transform="translate(0,0)"/>
<path d="" fill="#D5D5FC" transform="translate(0,0)"/>
<path d="" fill="#C7CBFA" transform="translate(0,0)"/>
<path d="" fill="#BFBEF6" transform="translate(0,0)"/>
<path d="" fill="#CDD1F9" transform="translate(0,0)"/>
<path d="" fill="#D5D3F8" transform="translate(0,0)"/>
<path d="" fill="#D2D3F9" transform="translate(0,0)"/>
<path d="" fill="#E0E1FD" transform="translate(0,0)"/>
<path d="" fill="#CCCAF7" transform="translate(0,0)"/>
<path d="" fill="#D5D3F9" transform="translate(0,0)"/>
<path d="" fill="#CED0F8" transform="translate(0,0)"/>
<path d="" fill="#C6CDF8" transform="translate(0,0)"/>
<path d="" fill="#C8CCFA" transform="translate(0,0)"/>
<path d="" fill="#CACCF8" transform="translate(0,0)"/>
<path d="" fill="#D4D7FA" transform="translate(0,0)"/>
<path d="" fill="#D2D5FB" transform="translate(0,0)"/>
<path d="" fill="#C9CDF9" transform="translate(0,0)"/>
<path d="" fill="#B7B5EF" transform="translate(0,0)"/>
<path d="" fill="#CCCEF9" transform="translate(0,0)"/>
<path d="" fill="#CBCBF7" transform="translate(0,0)"/>
<path d="" fill="#CCCBFC" transform="translate(0,0)"/>
<path d="" fill="#D1D2FE" transform="translate(0,0)"/>
<path d="" fill="#CECEFA" transform="translate(0,0)"/>
<path d="" fill="#CED1FC" transform="translate(0,0)"/>
<path d="" fill="#B0B2EE" transform="translate(0,0)"/>
<path d="" fill="#C9C6F7" transform="translate(0,0)"/>
<path d="" fill="#D9DBFE" transform="translate(0,0)"/>
<path d="" fill="#C5C9F9" transform="translate(0,0)"/>
<path d="" fill="#C3CAF4" transform="translate(0,0)"/>
<path d="" fill="#C6CBFA" transform="translate(0,0)"/>
<path d="" fill="#B1B2EE" transform="translate(0,0)"/>
<path d="" fill="#D0CFFC" transform="translate(0,0)"/>
<path d="" fill="#D0D2FE" transform="translate(0,0)"/>
<path d="" fill="#CACCF8" transform="translate(0,0)"/>
<path d="" fill="#CACDFC" transform="translate(0,0)"/>
<path d="" fill="#D0D2FA" transform="translate(0,0)"/>
<path d="" fill="#CFCFF9" transform="translate(0,0)"/>
<path d="" fill="#AFB0EB" transform="translate(0,0)"/>
<path d="" fill="#CED2FC" transform="translate(0,0)"/>
<path d="" fill="#C5C9FA" transform="translate(0,0)"/>
<path d="" fill="#CACBF8" transform="translate(0,0)"/>
<path d="" fill="#C9CCFB" transform="translate(0,0)"/>
<path d="" fill="#CDD1FE" transform="translate(0,0)"/>
<path d="" fill="#BAC0F5" transform="translate(0,0)"/>
<path d="" fill="#CED2F8" transform="translate(0,0)"/>
<path d="" fill="#CECFFA" transform="translate(0,0)"/>
<path d="" fill="#D0D1F8" transform="translate(0,0)"/>
<path d="" fill="#B2B2EC" transform="translate(0,0)"/>
<path d="" fill="#D4D5FB" transform="translate(0,0)"/>
<path d="" fill="#CDCFFA" transform="translate(0,0)"/>
<path d="" fill="#C6C9F7" transform="translate(0,0)"/>
<path d="" fill="#CECFF7" transform="translate(0,0)"/>
<path d="" fill="#CCCEFB" transform="translate(0,0)"/>
<path d="" fill="#CFD1F9" transform="translate(0,0)"/>
<path d="" fill="#D1CFFB" transform="translate(0,0)"/>
<path d="" fill="#C9CAF7" transform="translate(0,0)"/>
<path d="" fill="#D5D6FA" transform="translate(0,0)"/>
<path d="" fill="#CCD1FA" transform="translate(0,0)"/>
<path d="" fill="#D1D1FA" transform="translate(0,0)"/>
<path d="" fill="#D4D7FA" transform="translate(0,0)"/>
<path d="" fill="#C9CEF7" transform="translate(0,0)"/>
<path d="" fill="#BCC6F7" transform="translate(0,0)"/>
<path d="" fill="#D0D0F9" transform="translate(0,0)"/>
<path d="" fill="#C9CDFA" transform="translate(0,0)"/>
<path d="" fill="#D1D1F8" transform="translate(0,0)"/>
<path d="" fill="#C9C8F5" transform="translate(0,0)"/>
<path d="" fill="#CCCCF9" transform="translate(0,0)"/>
<path d="" fill="#C9C9F6" transform="translate(0,0)"/>
<path d="" fill="#D2D4F7" transform="translate(0,0)"/>
<path d="" fill="#C3C8F6" transform="translate(0,0)"/>
<path d="" fill="#C5C8F7" transform="translate(0,0)"/>
<path d="" fill="#C5C9F8" transform="translate(0,0)"/>
<path d="" fill="#C4C9F8" transform="translate(0,0)"/>
<path d="" fill="#D5D4FA" transform="translate(0,0)"/>
<path d="" fill="#B7C0F4" transform="translate(0,0)"/>
<path d="" fill="#C7C7F7" transform="translate(0,0)"/>
<path d="" fill="#C9CDF6" transform="translate(0,0)"/>
<path d="" fill="#D3D4F8" transform="translate(0,0)"/>
<path d="" fill="#C5CBF7" transform="translate(0,0)"/>
<path d="" fill="#C1C7F7" transform="translate(0,0)"/>
<path d="" fill="#CFD0F7" transform="translate(0,0)"/>
<path d="" fill="#C0C5F8" transform="translate(0,0)"/>
<path d="" fill="#C1C9F8" transform="translate(0,0)"/>
<path d="" fill="#C4C5F5" transform="translate(0,0)"/>
<path d="" fill="#C6C8F5" transform="translate(0,0)"/>
<path d="" fill="#C9C9F1" transform="translate(0,0)"/>
<path d="" fill="#C5CBFA" transform="translate(0,0)"/>
<path d="" fill="#C1C9F5" transform="translate(0,0)"/>
<path d="" fill="#C9CAF8" transform="translate(0,0)"/>
<path d="" fill="#C3C6F8" transform="translate(0,0)"/>
<path d="" fill="#C3C9F9" transform="translate(0,0)"/>
<path d="" fill="#B4B5F0" transform="translate(0,0)"/>
<path d="" fill="#C0C3F5" transform="translate(0,0)"/>
<path d="" fill="#C1C4F7" transform="translate(0,0)"/>
<path d="" fill="#C0C6F5" transform="translate(0,0)"/>
<path d="" fill="#C0C6F5" transform="translate(0,0)"/>
<path d="" fill="#CDD4F8" transform="translate(0,0)"/>
<path d="" fill="#C1C7F5" transform="translate(0,0)"/>
<path d="" fill="#C4CAF8" transform="translate(0,0)"/>
<path d="" fill="#C8CDF9" transform="translate(0,0)"/>
<path d="" fill="#CCD0FA" transform="translate(0,0)"/>
<path d="" fill="#BFC1F2" transform="translate(0,0)"/>
<path d="" fill="#C3C5F8" transform="translate(0,0)"/>
<path d="" fill="#C1C6F7" transform="translate(0,0)"/>
<path d="" fill="#CCCBF5" transform="translate(0,0)"/>
<path d="" fill="#C2C4F4" transform="translate(0,0)"/>
<path d="" fill="#D2D4FA" transform="translate(0,0)"/>
<path d="" fill="#C1C6F6" transform="translate(0,0)"/>
<path d="" fill="#BFC4F4" transform="translate(0,0)"/>
<path d="" fill="#BFC5F1" transform="translate(0,0)"/>
<path d="" fill="#B6B7EC" transform="translate(0,0)"/>
<path d="" fill="#C1C1F3" transform="translate(0,0)"/>
<path d="" fill="#C1C3F5" transform="translate(0,0)"/>
<path d="" fill="#C5C6F2" transform="translate(0,0)"/>
<path d="" fill="#C4C4F4" transform="translate(0,0)"/>
<path d="" fill="#C0C0F3" transform="translate(0,0)"/>
<path d="" fill="#C0C4F3" transform="translate(0,0)"/>
<path d="" fill="#D0D6F8" transform="translate(0,0)"/>
<path d="" fill="#C9CDF7" transform="translate(0,0)"/>
<path d="" fill="#BBC1F6" transform="translate(0,0)"/>
<path d="" fill="#B7B7EE" transform="translate(0,0)"/>
<path d="" fill="#C4C2F2" transform="translate(0,0)"/>
<path d="" fill="#C1C2F3" transform="translate(0,0)"/>
<path d="" fill="#BFC4F4" transform="translate(0,0)"/>
<path d="" fill="#C2C8F5" transform="translate(0,0)"/>
<path d="" fill="#B5BEEF" transform="translate(0,0)"/>
<path d="" fill="#BFC0F5" transform="translate(0,0)"/>
<path d="" fill="#BABDF3" transform="translate(0,0)"/>
<path d="" fill="#BDBEF5" transform="translate(0,0)"/>
<path d="" fill="#B8B9F2" transform="translate(0,0)"/>
<path d="" fill="#B9BBF3" transform="translate(0,0)"/>
<path d="" fill="#BCBDF7" transform="translate(0,0)"/>
<path d="" fill="#BCC3F4" transform="translate(0,0)"/>
<path d="" fill="#BDBFF2" transform="translate(0,0)"/>
<path d="" fill="#B9BEF3" transform="translate(0,0)"/>
<path d="" fill="#BAC1F3" transform="translate(0,0)"/>
<path d="" fill="#B9BAF0" transform="translate(0,0)"/>
<path d="" fill="#B7B9F0" transform="translate(0,0)"/>
<path d="" fill="#B9BBF0" transform="translate(0,0)"/>
<path d="" fill="#B9BAF2" transform="translate(0,0)"/>
<path d="" fill="#B8B9F0" transform="translate(0,0)"/>
<path d="" fill="#B8B9F0" transform="translate(0,0)"/>
<path d="" fill="#B6BBF1" transform="translate(0,0)"/>
<path d="" fill="#B6BAF0" transform="translate(0,0)"/>
<path d="" fill="#BABCF1" transform="translate(0,0)"/>
<path d="" fill="#B7BDF2" transform="translate(0,0)"/>
<path d="" fill="#BCC1F3" transform="translate(0,0)"/>
<path d="" fill="#BDC1F2" transform="translate(0,0)"/>
<path d="" fill="#BCC2F3" transform="translate(0,0)"/>
<path d="" fill="#B8BDEF" transform="translate(0,0)"/>
<path d="" fill="#BABEF2" transform="translate(0,0)"/>
<path d="" fill="#BBC1F6" transform="translate(0,0)"/>
<path d="" fill="#BBBFF6" transform="translate(0,0)"/>
<path d="" fill="#BEBEEE" transform="translate(0,0)"/>
<path d="" fill="#BEC0F2" transform="translate(0,0)"/>
<path d="" fill="#B9BBF2" transform="translate(0,0)"/>
<path d="" fill="#BFC6F2" transform="translate(0,0)"/>
<path d="" fill="#C4C9F7" transform="translate(0,0)"/>
<path d="" fill="#B8C1F5" transform="translate(0,0)"/>
<path d="" fill="#B5B8EF" transform="translate(0,0)"/>
<path d="" fill="#BBBAF0" transform="translate(0,0)"/>
<path d="" fill="#BCBCF0" transform="translate(0,0)"/>
<path d="" fill="#BABAF0" transform="translate(0,0)"/>
<path d="" fill="#B1B5EF" transform="translate(0,0)"/>
<path d="" fill="#B1B6EF" transform="translate(0,0)"/>
<path d="" fill="#B9BDF3" transform="translate(0,0)"/>
<path d="" fill="#B9C0F1" transform="translate(0,0)"/>
<path d="" fill="#BEC4F0" transform="translate(0,0)"/>
<path d="" fill="#B8BFF5" transform="translate(0,0)"/>
<path d="" fill="#BFC4F7" transform="translate(0,0)"/>
<path d="" fill="#BDC3F4" transform="translate(0,0)"/>
<path d="" fill="#BDC3F4" transform="translate(0,0)"/>
<path d="" fill="#BDC3F3" transform="translate(0,0)"/>
<path d="" fill="#B5B7F0" transform="translate(0,0)"/>
<path d="" fill="#5958B6" transform="translate(0,0)"/>
<path d="" fill="#726FC7" transform="translate(0,0)"/>
<path d="" fill="#4E4FB2" transform="translate(0,0)"/>
</svg>
