<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C0.85 0.002 1.701 0.004 2.577 0.007 C4.019 0.007 4.019 0.007 5.489 0.007 C7.056 0.015 7.056 0.015 8.655 0.023 C9.719 0.024 10.783 0.025 11.879 0.027 C15.293 0.033 18.706 0.045 22.12 0.058 C24.428 0.063 26.736 0.067 29.044 0.071 C34.715 0.082 40.386 0.099 46.057 0.12 C49.247 11.811 51.28 23.705 53.432 35.62 C53.838 37.853 54.245 40.086 54.651 42.319 C55.443 46.677 56.233 51.035 57.021 55.394 C57.831 59.877 58.65 64.358 59.475 68.839 C63.689 91.871 67.324 115.006 71.057 138.12 C71.233 136.961 71.408 135.802 71.589 134.608 C75.532 108.625 79.751 82.719 84.547 56.879 C85.103 53.872 85.657 50.864 86.209 47.857 C87.223 42.34 88.243 36.825 89.267 31.311 C89.666 29.159 90.064 27.006 90.461 24.854 C91.016 21.847 91.577 18.841 92.14 15.835 C92.304 14.94 92.468 14.045 92.637 13.123 C93.481 8.648 94.48 4.409 96.057 0.12 C102.305 0.095 108.553 0.077 114.801 0.065 C116.929 0.06 119.056 0.053 121.183 0.045 C124.234 0.033 127.284 0.027 130.335 0.023 C131.771 0.015 131.771 0.015 133.236 0.007 C134.557 0.007 134.557 0.007 135.905 0.007 C136.684 0.005 137.464 0.002 138.267 0 C140.057 0.12 140.057 0.12 141.057 1.12 C141.155 3.713 141.187 6.278 141.178 8.871 C141.179 10.104 141.179 10.104 141.18 11.361 C141.182 14.136 141.176 16.91 141.171 19.685 C141.171 21.665 141.171 23.646 141.171 25.626 C141.172 31.021 141.166 36.415 141.159 41.809 C141.153 47.44 141.152 53.071 141.151 58.702 C141.148 69.373 141.14 80.043 141.129 90.714 C141.118 102.859 141.113 115.004 141.108 127.149 C141.097 152.139 141.08 177.13 141.057 202.12 C139.622 202.286 138.187 202.449 136.751 202.612 C135.952 202.703 135.153 202.795 134.329 202.889 C131.618 203.165 128.942 203.25 126.218 203.253 C125.218 203.254 124.218 203.256 123.188 203.257 C121.67 203.251 121.67 203.251 120.12 203.245 C119.08 203.249 118.041 203.253 116.97 203.257 C115.484 203.255 115.484 203.255 113.968 203.253 C113.072 203.252 112.176 203.251 111.253 203.25 C109.057 203.12 109.057 203.12 107.057 202.12 C105.904 195.808 105.915 189.534 105.928 183.134 C105.925 181.525 105.925 181.525 105.923 179.883 C105.918 176.357 105.921 172.83 105.925 169.304 C105.924 166.843 105.923 164.383 105.922 161.923 C105.92 156.774 105.922 151.625 105.927 146.477 C105.933 139.884 105.93 133.291 105.924 126.698 C105.92 121.621 105.921 116.543 105.924 111.465 C105.924 109.034 105.924 106.603 105.921 104.173 C105.919 100.774 105.923 97.376 105.928 93.977 C105.926 92.977 105.924 91.977 105.922 90.947 C105.937 85.895 106.189 81.099 107.057 76.12 C106.985 76.712 106.913 77.304 106.839 77.913 C106.358 82.003 105.924 85.994 106.057 90.12 C105.067 90.12 104.077 90.12 103.057 90.12 C103.009 90.785 102.961 91.45 102.911 92.135 C102.362 98.958 101.341 105.558 99.991 112.265 C99.793 113.278 99.594 114.292 99.39 115.336 C98.551 119.623 97.699 123.907 96.848 128.191 C96.224 131.34 95.608 134.49 94.991 137.64 C94.7 139.084 94.7 139.084 94.404 140.557 C93.506 145.148 92.815 149.414 93.057 154.12 C92.397 154.12 91.737 154.12 91.057 154.12 C90.727 159.07 90.397 164.02 90.057 169.12 C89.397 169.12 88.737 169.12 88.057 169.12 C88.008 169.715 87.96 170.309 87.909 170.921 C87.324 177.408 86.308 183.731 85.057 190.12 C84.791 191.53 84.791 191.53 84.518 192.968 C83.191 199.854 83.191 199.854 82.057 202.12 C79.136 202.534 79.136 202.534 75.307 202.745 C74.638 202.782 73.969 202.82 73.279 202.858 C56.083 203.739 56.083 203.739 50.057 202.12 C47.04 187.177 44.817 172.1 42.514 157.034 C42.18 154.856 41.846 152.679 41.511 150.502 C38.66 131.954 35.868 113.402 33.415 94.797 C33.244 93.513 33.073 92.229 32.897 90.905 C32.686 89.279 32.686 89.279 32.471 87.62 C32.186 85.13 32.186 85.13 31.057 83.12 C30.29 98.539 29.871 113.93 29.741 129.366 C29.72 131.486 29.699 133.605 29.678 135.725 C29.624 141.217 29.574 146.709 29.525 152.201 C29.474 157.84 29.419 163.479 29.364 169.118 C29.258 180.119 29.156 191.12 29.057 202.12 C18.497 202.12 7.937 202.12 -2.943 202.12 C-3.096 177.094 -3.245 152.068 -3.385 127.043 C-3.451 115.422 -3.519 103.802 -3.591 92.182 C-3.654 82.054 -3.713 71.925 -3.768 61.796 C-3.797 56.434 -3.828 51.071 -3.863 45.709 C-3.896 40.659 -3.925 35.61 -3.949 30.561 C-3.959 28.71 -3.971 26.858 -3.984 25.007 C-4.003 22.475 -4.015 19.942 -4.025 17.409 C-4.032 16.682 -4.038 15.954 -4.045 15.204 C-4.056 10.411 -3.686 5.847 -2.943 1.12 C-1.943 0.12 -1.943 0.12 0 0 Z M107.057 67.12 C107.057 68.77 107.057 70.42 107.057 72.12 C107.387 72.12 107.717 72.12 108.057 72.12 C108.057 70.47 108.057 68.82 108.057 67.12 C107.727 67.12 107.397 67.12 107.057 67.12 Z M104.057 85.12 C105.057 89.12 105.057 89.12 105.057 89.12 Z " fill="#EB373A" transform="translate(418.94252014160156,109.87974548339844)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.146 1.905 3.292 1.809 4.473 1.711 C6.003 1.598 7.533 1.486 9.062 1.375 C10.191 1.278 10.191 1.278 11.342 1.18 C12.086 1.128 12.831 1.077 13.598 1.023 C14.273 0.971 14.949 0.919 15.645 0.865 C18.921 1.053 21.863 2.072 25 3 C25.047 31.911 25.082 60.821 25.104 89.732 C25.106 92.401 25.108 95.07 25.11 97.739 C25.11 98.402 25.111 99.066 25.111 99.749 C25.12 110.502 25.136 121.254 25.154 132.006 C25.173 143.033 25.184 154.06 25.188 165.087 C25.191 171.283 25.197 177.479 25.211 183.675 C25.225 189.501 25.229 195.327 25.226 201.153 C25.227 203.297 25.231 205.44 25.238 207.584 C25.248 210.5 25.246 213.416 25.241 216.332 C25.246 217.189 25.252 218.047 25.258 218.931 C25.228 224.772 25.228 224.772 23 227 C21.68 227 20.36 227 19 227 C19 227.66 19 228.32 19 229 C18.34 228.67 17.68 228.34 17 228 C14.638 227.905 12.274 227.868 9.91 227.859 C8.472 227.841 7.034 227.821 5.596 227.801 C3.326 227.772 1.058 227.75 -1.212 227.75 C-3.401 227.746 -5.588 227.712 -7.777 227.672 C-9.09 227.663 -10.402 227.653 -11.754 227.644 C-15 227 -15 227 -16.951 224.646 C-18 222 -18 222 -18 219 C-18.66 218.67 -19.32 218.34 -20 218 C-20.625 214.938 -20.625 214.938 -21 212 C-21.66 212 -22.32 212 -23 212 C-23.038 211.396 -23.077 210.793 -23.116 210.171 C-23.628 203.729 -24.964 197.959 -26.922 191.816 C-27.367 190.38 -27.367 190.38 -27.821 188.914 C-28.788 185.794 -29.769 182.678 -30.75 179.562 C-31.426 177.392 -32.1 175.221 -32.774 173.05 C-33.811 169.709 -34.848 166.369 -35.889 163.029 C-38.236 155.493 -40.543 147.945 -42.816 140.387 C-43.264 138.9 -43.264 138.9 -43.72 137.382 C-44.865 133.575 -46.008 129.767 -47.149 125.959 C-47.927 123.362 -48.707 120.767 -49.488 118.172 C-49.715 117.413 -49.941 116.653 -50.174 115.871 C-51.291 112.166 -52.537 108.58 -54 105 C-54.004 105.623 -54.008 106.245 -54.013 106.887 C-54.116 122.017 -54.225 137.147 -54.339 152.278 C-54.395 159.594 -54.448 166.911 -54.497 174.228 C-54.54 180.603 -54.586 186.978 -54.637 193.353 C-54.663 196.73 -54.688 200.108 -54.709 203.486 C-54.733 207.25 -54.763 211.015 -54.795 214.779 C-54.8 215.907 -54.806 217.035 -54.812 218.197 C-54.827 219.726 -54.827 219.726 -54.842 221.286 C-54.848 222.177 -54.855 223.069 -54.861 223.987 C-55 226 -55 226 -56 227 C-58.655 227.101 -61.283 227.14 -63.938 227.133 C-65.13 227.134 -65.13 227.134 -66.347 227.136 C-68.032 227.136 -69.717 227.135 -71.401 227.13 C-73.989 227.125 -76.576 227.13 -79.164 227.137 C-80.797 227.136 -82.43 227.135 -84.062 227.133 C-84.842 227.135 -85.621 227.137 -86.424 227.139 C-91.885 227.115 -91.885 227.115 -93 226 C-93.112 223.296 -93.159 220.617 -93.165 217.912 C-93.171 217.054 -93.176 216.196 -93.182 215.312 C-93.2 212.413 -93.21 209.515 -93.221 206.616 C-93.232 204.547 -93.244 202.477 -93.256 200.408 C-93.287 194.767 -93.313 189.126 -93.336 183.485 C-93.357 178.776 -93.38 174.068 -93.404 169.359 C-93.459 158.242 -93.51 147.125 -93.558 136.008 C-93.607 124.55 -93.663 113.092 -93.723 101.635 C-93.774 91.798 -93.822 81.96 -93.866 72.123 C-93.892 66.249 -93.92 60.374 -93.951 54.5 C-93.981 48.965 -94.005 43.431 -94.025 37.896 C-94.033 35.871 -94.043 33.846 -94.055 31.821 C-94.113 21.831 -94.142 11.942 -93 2 C-88.375 1.071 -84.123 0.952 -79.414 1.121 C-78.361 1.157 -78.361 1.157 -77.287 1.193 C-75.066 1.269 -72.846 1.353 -70.625 1.438 C-69.111 1.491 -67.596 1.544 -66.082 1.596 C-62.388 1.724 -58.694 1.86 -55 2 C-52.99 8.538 -50.982 15.077 -48.976 21.617 C-48.297 23.829 -47.617 26.042 -46.937 28.254 C-43.234 40.298 -39.566 52.352 -36.004 64.438 C-35.701 65.456 -35.398 66.475 -35.085 67.525 C-34.81 68.464 -34.535 69.403 -34.252 70.371 C-34.009 71.195 -33.767 72.02 -33.517 72.869 C-33 75 -33 75 -33 78 C-32.01 78.495 -32.01 78.495 -31 79 C-30.395 81.04 -29.898 83.111 -29.438 85.188 C-27.113 95.187 -24.068 104.972 -21.086 114.79 C-20.703 116.05 -20.32 117.311 -19.926 118.609 C-19.579 119.743 -19.233 120.877 -18.876 122.046 C-18.191 124.356 -17.556 126.656 -17 129 C-16.225 125.57 -15.877 122.415 -15.886 118.901 C-15.887 117.897 -15.887 116.894 -15.887 115.859 C-15.892 114.769 -15.897 113.679 -15.902 112.556 C-15.904 111.4 -15.905 110.243 -15.907 109.051 C-15.911 106.542 -15.917 104.034 -15.925 101.525 C-15.938 97.558 -15.944 93.592 -15.949 89.625 C-15.96 81.208 -15.98 72.792 -16 64.375 C-16.022 55.281 -16.043 46.188 -16.053 37.094 C-16.059 32.514 -16.073 27.935 -16.088 23.355 C-16.091 20.965 -16.095 18.575 -16.098 16.185 C-16.103 15.091 -16.108 13.997 -16.113 12.87 C-16.113 11.871 -16.113 10.873 -16.114 9.845 C-16.116 8.977 -16.118 8.108 -16.12 7.214 C-16 5 -16 5 -15 2 C-10.269 1.228 -5.785 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#EB373A" transform="translate(759,346)"/>
<path d="M0 0 C1.243 0.009 2.485 0.018 3.766 0.027 C6.802 0.051 9.839 0.083 12.875 0.125 C13.37 1.115 13.37 1.115 13.875 2.125 C15.666 2.886 17.484 3.584 19.312 4.25 C32.5 9.606 41.07 19.354 46.875 32.125 C47.535 32.125 48.195 32.125 48.875 32.125 C49.205 33.115 49.535 34.105 49.875 35.125 C49.215 35.125 48.555 35.125 47.875 35.125 C48.273 36.306 48.273 36.306 48.68 37.512 C54.728 56.164 55.037 75.149 55.078 94.566 C55.082 95.931 55.082 95.931 55.087 97.323 C55.102 102.139 55.111 106.955 55.115 111.771 C55.121 116.676 55.145 121.581 55.173 126.487 C55.192 130.321 55.197 134.156 55.199 137.99 C55.202 139.795 55.209 141.601 55.223 143.406 C55.391 167.912 52.892 194.425 37.438 214.562 C25.925 226.075 11.186 231.089 -4.938 231.562 C-17.207 231.466 -29.214 228.593 -39.125 221.125 C-39.904 220.538 -40.682 219.952 -41.484 219.348 C-52.301 210.473 -59.581 197.024 -61.125 183.125 C-61.785 182.795 -62.445 182.465 -63.125 182.125 C-63.598 179.569 -63.711 177.032 -63.885 174.439 C-64.033 172.401 -64.195 170.364 -64.381 168.329 C-65.242 158.681 -65.289 149.079 -65.293 139.402 C-65.296 137.608 -65.3 135.814 -65.303 134.019 C-65.309 130.273 -65.311 126.527 -65.31 122.781 C-65.31 118.041 -65.324 113.3 -65.341 108.56 C-65.352 104.856 -65.354 101.152 -65.354 97.447 C-65.355 95.703 -65.359 93.958 -65.367 92.214 C-65.431 76.589 -65.221 60.246 -60.875 45.125 C-60.659 44.346 -60.443 43.568 -60.22 42.766 C-55.349 26.01 -46.112 13.374 -30.773 4.812 C-20.889 0.062 -10.79 -0.15 0 0 Z M-14.125 42.125 C-22.227 52.217 -22.277 65.531 -22.27 77.937 C-22.274 78.873 -22.277 79.809 -22.28 80.774 C-22.29 83.844 -22.292 86.914 -22.293 89.984 C-22.296 92.138 -22.3 94.291 -22.303 96.444 C-22.309 100.949 -22.311 105.455 -22.31 109.96 C-22.31 115.704 -22.324 121.447 -22.341 127.191 C-22.352 131.637 -22.354 136.082 -22.354 140.528 C-22.355 142.644 -22.359 144.76 -22.367 146.876 C-22.861 169.047 -22.861 169.047 -15.625 189.625 C-14.882 190.368 -14.882 190.368 -14.125 191.125 C-13.135 191.125 -12.145 191.125 -11.125 191.125 C-11.125 191.785 -11.125 192.445 -11.125 193.125 C-2.721 194.525 -2.721 194.525 4.875 191.125 C12.486 183.731 12.836 172.34 13.009 162.341 C13.018 160.668 13.021 158.996 13.02 157.323 C13.024 156.4 13.027 155.477 13.03 154.525 C13.04 151.502 13.042 148.48 13.043 145.457 C13.046 143.332 13.05 141.208 13.053 139.083 C13.059 134.64 13.061 130.196 13.06 125.752 C13.06 120.095 13.074 114.438 13.091 108.781 C13.102 104.394 13.104 100.007 13.104 95.62 C13.105 93.537 13.109 91.453 13.117 89.369 C14.227 64.084 14.227 64.084 4.188 42.062 C3.424 41.423 2.661 40.784 1.875 40.125 C1.545 39.795 1.215 39.465 0.875 39.125 C-4.643 38.594 -9.844 38.211 -14.125 42.125 Z " fill="#EB373A" transform="translate(443.125,343.875)"/>
<path d="M0 0 C11.492 8.981 18.34 24.617 21.02 38.633 C21.207 42.32 21.207 42.32 21.02 44.633 C21.68 44.963 22.34 45.293 23.02 45.633 C23.71 75.964 24.371 106.292 24.02 136.633 C24.007 137.79 23.994 138.948 23.981 140.14 C23.618 164.01 20.172 188.728 3.02 206.633 C-10.499 218.209 -27.695 220.96 -44.98 219.633 C-59.071 217.775 -71.921 211.83 -80.98 200.633 C-88.143 190.732 -91.793 179.547 -94.23 167.695 C-94.394 166.91 -94.557 166.124 -94.725 165.315 C-97.13 152.743 -97.142 139.93 -97.148 127.176 C-97.152 125.619 -97.155 124.062 -97.159 122.505 C-97.164 119.257 -97.166 116.009 -97.166 112.761 C-97.166 109.487 -97.173 106.213 -97.186 102.939 C-97.505 25.61 -97.505 25.61 -75.32 2.781 C-54.339 -16.484 -22.779 -15.725 0 0 Z M-45.98 30.633 C-58.739 50.442 -54.142 80.194 -54.111 102.824 C-54.105 106.992 -54.108 111.16 -54.114 115.328 C-54.118 118.597 -54.117 121.867 -54.114 125.137 C-54.114 126.672 -54.114 128.207 -54.117 129.742 C-54.126 137.793 -53.988 145.81 -53.523 153.849 C-53.483 154.546 -53.442 155.243 -53.401 155.961 C-52.879 164.184 -52.285 171.042 -46.98 177.633 C-46.527 178.21 -46.073 178.788 -45.605 179.383 C-41.914 182.222 -38.092 182.85 -33.543 182.633 C-28.523 181.921 -25.125 179.57 -21.98 175.633 C-17.678 165.9 -17.735 155.787 -17.918 145.32 C-17.953 142.866 -17.983 140.412 -18.008 137.957 C-18.035 136.35 -18.035 136.35 -18.062 134.71 C-17.98 131.633 -17.98 131.633 -17.487 128.832 C-16.813 124.577 -16.838 120.419 -16.848 116.117 C-16.845 114.736 -16.845 114.736 -16.843 113.327 C-16.841 110.346 -16.847 107.364 -16.855 104.383 C-16.858 102.862 -16.858 102.862 -16.861 101.311 C-14.774 65.157 -14.774 65.157 -24.98 31.633 C-28.531 28.251 -31.265 27.373 -36.043 27.195 C-40.198 27.413 -42.898 27.639 -45.98 30.633 Z " fill="#EB373A" transform="translate(616.98046875,355.3671875)"/>
<path d="M0 0 C1.761 1.471 3.425 3 5.078 4.59 C6.347 5.735 6.347 5.735 7.641 6.902 C19.987 20.515 24.147 37.702 24.211 55.645 C24.078 57.59 24.078 57.59 23.078 60.59 C16.973 60.614 10.867 60.633 4.761 60.645 C2.683 60.65 0.604 60.657 -1.475 60.665 C-4.456 60.677 -7.437 60.683 -10.418 60.688 C-11.821 60.695 -11.821 60.695 -13.253 60.703 C-14.113 60.703 -14.974 60.703 -15.861 60.703 C-16.623 60.706 -17.384 60.708 -18.169 60.71 C-19.922 60.59 -19.922 60.59 -20.922 59.59 C-21.03 57.654 -21.107 55.716 -21.172 53.777 C-21.764 45.501 -23.678 37.834 -29.672 31.84 C-33.927 30.203 -37.424 29.798 -41.922 30.59 C-46.775 32.97 -49.328 35.882 -51.922 40.59 C-52.757 43.014 -52.757 43.014 -52.922 45.59 C-53.025 46.642 -53.128 47.694 -53.234 48.777 C-52.438 58.497 -47.935 64.911 -40.643 71.189 C-34.33 76.327 -27.595 80.937 -20.922 85.59 C-8.364 94.355 4.333 103.838 13.078 116.59 C13.904 117.787 13.904 117.787 14.746 119.008 C24.706 134.477 26.596 154.099 23.43 172.012 C19.457 188.107 11.541 202.269 -2.484 211.527 C-4.28 212.577 -6.092 213.6 -7.922 214.59 C-8.995 215.187 -8.995 215.187 -10.09 215.797 C-25.886 223.71 -46.26 223.628 -62.922 218.59 C-63.252 217.93 -63.582 217.27 -63.922 216.59 C-65.27 216.145 -65.27 216.145 -66.645 215.691 C-78.142 211.827 -86.417 202.07 -91.734 191.59 C-96.64 181.418 -97.755 171.818 -97.922 160.59 C-97.427 159.6 -97.427 159.6 -96.922 158.59 C-88.516 157.416 -79.838 157.502 -71.359 157.465 C-70.623 157.46 -69.886 157.455 -69.127 157.451 C-64.628 157.464 -60.362 157.855 -55.922 158.59 C-55.812 159.386 -55.703 160.183 -55.59 161.004 C-54.294 169.666 -52.798 175.696 -45.922 181.59 C-41.257 184.522 -36.109 183.665 -30.922 182.59 C-28.016 181.167 -28.016 181.167 -25.922 179.59 C-25.262 179.59 -24.602 179.59 -23.922 179.59 C-19.257 168.261 -17.473 159.38 -21.922 147.59 C-28.489 136.186 -40.217 128.119 -50.797 120.715 C-57.88 115.75 -64.399 110.261 -70.922 104.59 C-71.684 103.963 -72.446 103.337 -73.23 102.691 C-85.007 92.912 -93.963 79.024 -96.371 63.738 C-98.133 43.233 -93.452 24.757 -80.109 8.777 C-60.935 -11.536 -23.352 -17.134 0 0 Z " fill="#EB363A" transform="translate(335.921875,353.41015625)"/>
<path d="M0 0 C2.625 -0.267 5.011 -0.348 7.633 -0.293 C8.761 -0.287 8.761 -0.287 9.911 -0.28 C12.316 -0.263 14.72 -0.226 17.125 -0.188 C18.754 -0.172 20.383 -0.159 22.012 -0.146 C26.008 -0.113 30.004 -0.062 34 0 C36.369 5.253 38.181 10.515 39.82 16.035 C40.21 17.331 40.21 17.331 40.608 18.652 C41.451 21.454 42.288 24.258 43.125 27.062 C43.709 29.007 44.293 30.951 44.877 32.895 C46.59 38.595 48.296 44.297 50 50 C50.463 51.548 50.926 53.096 51.389 54.644 C51.601 55.351 51.812 56.058 52.03 56.786 C52.628 58.783 53.225 60.78 53.823 62.777 C54.558 65.232 55.292 67.687 56.024 70.143 C57.567 75.297 59.127 80.442 60.777 85.562 C61.05 86.409 61.323 87.255 61.604 88.127 C62.083 89.602 62.568 91.075 63.062 92.545 C64.54 97.069 65.597 101.622 66.581 106.279 C66.981 108.168 66.981 108.168 67.389 110.095 C67.591 111.053 67.792 112.012 68 113 C67.987 111.959 67.975 110.918 67.962 109.845 C67.843 99.933 67.725 90.022 67.609 80.11 C67.549 75.016 67.489 69.923 67.427 64.83 C67.368 59.896 67.31 54.961 67.252 50.027 C67.23 48.164 67.208 46.301 67.185 44.438 C67.121 39.199 67.068 33.96 67.026 28.721 C67.008 26.816 66.985 24.911 66.959 23.006 C66.917 19.98 66.888 16.953 66.861 13.926 C66.845 12.992 66.829 12.058 66.813 11.096 C66.808 10.226 66.802 9.355 66.797 8.459 C66.788 7.693 66.779 6.927 66.769 6.138 C67 4 67 4 69 0 C79.89 0 90.78 0 102 0 C103.546 3.092 103.137 6.351 103.127 9.744 C103.129 10.957 103.129 10.957 103.132 12.195 C103.136 14.92 103.133 17.646 103.129 20.371 C103.131 22.319 103.133 24.267 103.135 26.214 C103.139 31.516 103.137 36.817 103.134 42.119 C103.132 47.657 103.134 53.194 103.136 58.732 C103.138 68.033 103.135 77.335 103.13 86.636 C103.125 97.402 103.127 108.169 103.132 118.935 C103.137 128.165 103.137 137.395 103.135 146.625 C103.133 152.143 103.133 157.662 103.136 163.181 C103.139 168.368 103.137 173.556 103.131 178.743 C103.13 180.651 103.13 182.56 103.133 184.468 C103.135 187.064 103.132 189.66 103.127 192.256 C103.13 193.401 103.13 193.401 103.134 194.569 C103.114 199.772 103.114 199.772 102 202 C96.315 203.497 90.336 203.147 84.5 203.125 C82.971 203.131 82.971 203.131 81.41 203.137 C76.498 203.13 71.826 203.041 67 202 C66.328 199.897 65.663 197.793 65 195.688 C64.629 194.516 64.257 193.344 63.875 192.137 C63 189 63 189 63 186 C62.34 185.67 61.68 185.34 61 185 C60.514 183.231 60.514 183.231 60.137 180.855 C58.861 173.865 56.822 167.12 54.812 160.312 C54.405 158.919 53.997 157.525 53.589 156.131 C51.325 148.403 49 140.695 46.625 133 C43.287 122.174 40.203 111.277 37.125 100.375 C36.878 99.501 36.631 98.628 36.376 97.728 C35.667 95.223 34.962 92.717 34.258 90.211 C34.045 89.457 33.831 88.702 33.612 87.925 C32.594 84.289 31.742 80.709 31 77 C31 117.92 31 158.84 31 201 C25.199 203.9 19.773 203.329 13.375 203.312 C12.122 203.329 10.869 203.345 9.578 203.361 C8.384 203.362 7.191 203.363 5.961 203.363 C4.866 203.366 3.77 203.369 2.642 203.372 C1.77 203.249 0.898 203.127 0 203 C-2.472 199.292 -2.279 196.47 -2.254 192.181 C-2.257 191.376 -2.26 190.571 -2.263 189.741 C-2.271 187.035 -2.265 184.329 -2.259 181.623 C-2.261 179.687 -2.265 177.75 -2.269 175.814 C-2.279 170.547 -2.275 165.281 -2.269 160.015 C-2.264 154.511 -2.268 149.007 -2.271 143.503 C-2.275 134.259 -2.27 125.015 -2.261 115.771 C-2.25 105.074 -2.253 94.378 -2.264 83.681 C-2.273 74.507 -2.275 65.333 -2.27 56.16 C-2.266 50.676 -2.266 45.193 -2.273 39.709 C-2.278 34.555 -2.274 29.4 -2.263 24.246 C-2.26 22.35 -2.261 20.455 -2.265 18.56 C-2.271 15.98 -2.264 13.4 -2.254 10.819 C-2.258 10.064 -2.263 9.308 -2.267 8.529 C-2.239 4.801 -2.124 3.186 0 0 Z " fill="#EB373A" transform="translate(639,110)"/>
<path d="M0 0 C14.837 11.632 19.57 29.403 21.795 47.374 C25.83 86.919 31.262 144.258 7.793 178.516 C7.365 178.991 6.937 179.467 6.496 179.957 C5.836 179.957 5.176 179.957 4.496 179.957 C4.166 180.947 3.836 181.937 3.496 182.957 C-7.473 193.439 -21.66 196.83 -36.374 197.029 C-42.212 196.832 -47.856 195.347 -53.504 193.957 C-55.169 193.615 -56.835 193.278 -58.504 192.957 C-58.504 192.297 -58.504 191.637 -58.504 190.957 C-59.239 190.689 -59.973 190.421 -60.73 190.145 C-71.514 185.527 -77.974 176.205 -82.254 165.645 C-90.088 143.477 -89.841 120 -89.765 96.741 C-89.754 92.83 -89.765 88.919 -89.777 85.008 C-89.813 23.588 -89.813 23.588 -71.18 4.402 C-51.655 -12.437 -21.476 -14.806 0 0 Z M-43.754 32.27 C-52.502 47.513 -49.665 70.665 -49.634 87.524 C-49.629 90.988 -49.634 94.452 -49.641 97.916 C-49.645 108.685 -49.539 119.439 -49.082 130.199 C-49.046 131.085 -49.011 131.972 -48.974 132.885 C-48.555 141.561 -47.385 149.572 -42.504 156.957 C-38.56 160.531 -34.265 160.531 -29.164 160.359 C-24.727 159.688 -23.365 158.462 -20.504 154.957 C-11.86 139.512 -15.192 115.24 -15.243 97.998 C-15.255 94.108 -15.248 90.218 -15.236 86.328 C-15.229 83.277 -15.231 80.226 -15.236 77.175 C-15.238 75.742 -15.236 74.31 -15.231 72.877 C-14.948 51.025 -14.948 51.025 -22.441 31.02 C-25.374 28.087 -27.188 27.103 -31.316 26.645 C-36.455 27.148 -40.123 28.538 -43.754 32.27 Z " fill="#EB363A" transform="translate(372.50390625,117.04296875)"/>
<path d="M0 0 C0.833 0.006 1.667 0.011 2.525 0.017 C16.013 0.281 29.105 4.449 38.75 14.312 C48.568 27.44 51.396 41.283 52.438 57.312 C52.51 58.408 52.51 58.408 52.585 59.526 C52.971 66.613 52.971 66.613 51.75 69.312 C48.132 70.518 44.82 70.456 41.055 70.445 C40.307 70.446 39.559 70.447 38.788 70.448 C37.209 70.449 35.631 70.447 34.052 70.443 C31.627 70.438 29.202 70.443 26.777 70.449 C25.245 70.449 23.712 70.447 22.18 70.445 C21.086 70.448 21.086 70.448 19.97 70.451 C14.865 70.427 14.865 70.427 13.75 69.312 C13.559 67.921 13.427 66.522 13.32 65.121 C13.247 64.258 13.173 63.395 13.098 62.506 C12.951 60.696 12.811 58.887 12.676 57.076 C12.174 51.319 11.2 46.573 8.75 41.312 C5.796 38.943 5.796 38.943 1.75 39.312 C1.75 38.653 1.75 37.993 1.75 37.312 C0.678 37.704 -0.395 38.096 -1.5 38.5 C-2.405 38.831 -2.405 38.831 -3.328 39.168 C-7.009 41.36 -8.882 44.245 -10.262 48.246 C-16.181 78.937 -20.558 134.843 -9.25 164.312 C-6.762 167.898 -6.762 167.898 -3.25 170.312 C1 170.661 3.667 170.794 7.562 169.062 C15.094 159.595 13.645 143.731 12.75 132.312 C11.917 130.146 11.917 130.146 9.75 129.312 C10.08 128.982 10.41 128.653 10.75 128.312 C10.04 128.252 9.329 128.191 8.598 128.129 C7.219 128.003 7.219 128.003 5.812 127.875 C4.896 127.794 3.979 127.713 3.035 127.629 C0.75 127.312 0.75 127.312 -0.25 126.312 C-0.35 124.106 -0.381 121.896 -0.383 119.688 C-0.384 119.025 -0.385 118.363 -0.386 117.681 C-0.386 116.279 -0.385 114.877 -0.38 113.475 C-0.375 111.322 -0.38 109.169 -0.387 107.016 C-0.386 105.656 -0.385 104.297 -0.383 102.938 C-0.382 101.692 -0.381 100.447 -0.379 99.164 C-0.25 96.312 -0.25 96.312 0.75 95.312 C7.027 94.439 13.196 94.141 19.527 94.109 C20.866 94.101 20.866 94.101 22.232 94.092 C24.1 94.083 25.968 94.076 27.836 94.072 C30.704 94.062 33.572 94.031 36.439 94 C38.26 93.993 40.081 93.988 41.902 93.984 C42.761 93.972 43.619 93.96 44.503 93.947 C46.902 93.956 46.902 93.956 50.75 94.312 C52.807 97.398 53 98.029 53.004 101.512 C53.01 102.367 53.015 103.222 53.021 104.103 C53.017 105.038 53.013 105.974 53.009 106.937 C53.012 107.924 53.016 108.912 53.019 109.929 C53.028 113.2 53.022 116.471 53.016 119.742 C53.017 122.013 53.019 124.283 53.021 126.554 C53.024 131.317 53.02 136.08 53.011 140.843 C52.999 146.949 53.006 153.054 53.018 159.16 C53.025 163.851 53.023 168.542 53.018 173.232 C53.016 175.483 53.018 177.735 53.023 179.986 C53.028 183.134 53.02 186.281 53.009 189.429 C53.013 190.36 53.017 191.291 53.021 192.251 C52.968 200.423 52.968 200.423 50.75 204.312 C46.79 206.293 41.791 205.497 37.438 205.5 C36.391 205.512 35.345 205.524 34.268 205.537 C33.255 205.539 32.242 205.541 31.199 205.543 C30.275 205.547 29.35 205.551 28.397 205.555 C25.75 205.312 25.75 205.312 20.75 203.312 C20.255 195.887 20.255 195.887 19.75 188.312 C18.946 189.674 18.141 191.035 17.312 192.438 C13.948 197.512 10.023 200.388 4.75 203.312 C4.126 203.68 3.502 204.047 2.859 204.426 C-5.942 208.126 -16.71 206.548 -25.289 203.086 C-38.718 196.855 -44.429 187.749 -49.441 174.059 C-50.247 171.322 -50.417 169.147 -50.25 166.312 C-51.24 166.312 -52.23 166.312 -53.25 166.312 C-53.066 165.643 -52.882 164.974 -52.692 164.285 C-52.149 160.632 -52.661 157.368 -53.172 153.75 C-54.889 139.625 -54.551 125.366 -54.511 111.161 C-54.499 107.124 -54.506 103.087 -54.518 99.05 C-54.618 57.67 -54.618 57.67 -50.062 41.5 C-49.848 40.723 -49.634 39.946 -49.413 39.145 C-45.612 26.065 -39.921 14.483 -27.844 7.281 C-18.589 2.336 -10.449 -0.074 0 0 Z " fill="#EB3639" transform="translate(814.25,107.6875)"/>
<path d="M0 0 C0.695 0.49 1.39 0.98 2.105 1.484 C3.157 2.206 4.209 2.928 5.293 3.672 C19.137 17.516 23.13 37.165 23.168 56.047 C23.152 58.193 23.133 60.339 23.105 62.484 C20.094 63.99 17.334 63.576 13.98 63.547 C12.926 63.541 12.926 63.541 11.851 63.535 C8.84 63.519 5.829 63.491 2.818 63.464 C0.622 63.446 -1.574 63.434 -3.77 63.422 C-5.746 63.404 -5.746 63.404 -7.762 63.387 C-10.8 63.262 -10.8 63.262 -12.895 64.484 C-14.957 63.734 -14.957 63.734 -16.895 62.484 C-18.131 58.774 -18.127 55.228 -18.27 51.359 C-18.57 44.88 -19.392 39.532 -21.895 33.484 C-22.225 32.494 -22.555 31.504 -22.895 30.484 C-26.617 29.244 -30.027 28.955 -33.895 29.484 C-37.746 31.624 -39.719 34.136 -41.279 38.267 C-43.857 47.55 -44.264 57.283 -44.52 66.859 C-44.557 68.101 -44.594 69.343 -44.632 70.622 C-45.145 89.49 -45.178 108.37 -44.52 127.234 C-44.473 128.705 -44.473 128.705 -44.426 130.206 C-43.957 143.777 -43.957 143.777 -37.895 155.484 C-34.187 157.956 -31.194 158.141 -26.895 157.484 C-22.64 155.215 -21.045 152.439 -19.5 147.914 C-18.526 144.007 -18.11 140.026 -17.715 136.023 C-16.895 133.484 -16.895 133.484 -14.807 132.34 C-11.253 131.296 -8.182 131.242 -4.477 131.254 C-3.778 131.254 -3.08 131.255 -2.36 131.255 C-0.892 131.258 0.576 131.266 2.045 131.279 C4.296 131.297 6.547 131.299 8.799 131.299 C19.81 131.337 19.81 131.337 22.105 132.484 C22.939 149.895 20.216 166.817 8.855 180.797 C-1.693 191.874 -15.045 197.019 -30.223 197.555 C-39.089 197.385 -46.91 195.924 -54.969 192.191 C-55.604 191.958 -56.24 191.725 -56.895 191.484 C-57.555 191.814 -58.215 192.144 -58.895 192.484 C-58.895 191.494 -58.895 190.504 -58.895 189.484 C-59.475 189.218 -60.055 188.951 -60.652 188.676 C-67.286 185.151 -71.784 179.705 -75.668 173.402 C-76.88 171.356 -76.88 171.356 -78.895 169.484 C-79.398 167.789 -79.398 167.789 -79.895 165.484 C-80.192 164.435 -80.49 163.386 -80.797 162.305 C-81.309 160.476 -81.309 160.476 -81.832 158.609 C-82.341 156.819 -82.341 156.819 -82.859 154.992 C-85.88 142.735 -86.252 130.466 -86.457 117.922 C-86.501 115.499 -86.548 113.077 -86.6 110.655 C-86.631 109.163 -86.658 107.672 -86.68 106.181 C-86.738 103.268 -86.884 100.538 -87.472 97.68 C-87.895 95.484 -87.895 95.484 -86.895 92.484 C-86.808 91.057 -86.777 89.626 -86.781 88.197 C-86.781 86.906 -86.781 86.906 -86.781 85.588 C-86.789 84.201 -86.789 84.201 -86.797 82.785 C-86.798 81.835 -86.8 80.885 -86.801 79.906 C-86.807 76.869 -86.819 73.833 -86.832 70.797 C-86.837 68.739 -86.842 66.681 -86.846 64.623 C-86.857 59.577 -86.874 54.531 -86.895 49.484 C-86.235 49.484 -85.575 49.484 -84.895 49.484 C-84.928 48.324 -84.962 47.164 -84.996 45.969 C-84.991 41.746 -84.324 37.816 -83.27 33.734 C-83.093 33.044 -82.916 32.354 -82.734 31.643 C-80.174 22.224 -75.799 12.556 -68.895 5.484 C-68.235 5.484 -67.575 5.484 -66.895 5.484 C-66.895 4.824 -66.895 4.164 -66.895 3.484 C-64.926 1.977 -64.926 1.977 -62.395 0.359 C-61.559 -0.179 -60.724 -0.718 -59.863 -1.273 C-59.214 -1.683 -58.564 -2.093 -57.895 -2.516 C-58.225 -3.176 -58.555 -3.836 -58.895 -4.516 C-57.533 -4.67 -57.533 -4.67 -56.145 -4.828 C-52.836 -5.275 -49.782 -6.135 -46.582 -7.078 C-30.705 -11.092 -13.783 -9.084 0 0 Z " fill="#EB363A" transform="translate(250.89453125,116.515625)"/>
<path d="M0 0 C0.779 0.002 1.558 0.004 2.361 0.006 C3.156 0.005 3.952 0.004 4.771 0.003 C6.456 0.003 8.14 0.004 9.825 0.009 C12.413 0.014 15 0.009 17.588 0.002 C19.22 0.003 20.853 0.004 22.486 0.006 C23.265 0.004 24.044 0.002 24.847 0 C30.309 0.024 30.309 0.024 31.424 1.139 C31.582 2.647 31.672 4.162 31.735 5.676 C31.777 6.662 31.818 7.648 31.86 8.663 C31.919 10.307 31.919 10.307 31.978 11.983 C32.02 13.139 32.062 14.295 32.105 15.485 C32.595 30.381 32.57 45.279 32.558 60.181 C32.555 64.361 32.558 68.542 32.559 72.723 C32.561 79.73 32.559 86.736 32.554 93.743 C32.549 101.782 32.55 109.821 32.556 117.86 C32.56 124.836 32.561 131.813 32.558 138.789 C32.557 142.924 32.557 147.059 32.56 151.193 C32.572 168.198 32.502 185.16 31.424 202.139 C27.508 202.714 23.712 203.266 19.752 203.272 C18.922 203.273 18.091 203.274 17.236 203.276 C16.39 203.272 15.545 203.268 14.674 203.264 C13.843 203.268 13.013 203.272 12.158 203.276 C5.808 203.266 -0.194 202.719 -6.576 202.139 C-7.071 127.394 -7.071 127.394 -7.576 51.139 C-7.906 51.139 -8.236 51.139 -8.576 51.139 C-8.576 48.829 -8.576 46.519 -8.576 44.139 C-8.246 44.139 -7.916 44.139 -7.576 44.139 C-7.539 42.193 -7.539 42.193 -7.5 40.208 C-7.407 35.412 -7.306 30.617 -7.204 25.822 C-7.161 23.744 -7.119 21.665 -7.079 19.586 C-7.022 16.605 -6.958 13.624 -6.893 10.643 C-6.876 9.707 -6.859 8.772 -6.842 7.808 C-6.822 6.947 -6.802 6.087 -6.782 5.2 C-6.766 4.438 -6.751 3.676 -6.735 2.892 C-6.377 -1.071 -3.282 0.014 0 0 Z " fill="#EB363A" transform="translate(587.576416015625,109.86105346679688)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 2.65 2 4.3 2 6 C0.68 6.33 -0.64 6.66 -2 7 C-2 8.65 -2 10.3 -2 12 C-9.972 13.822 -17.925 14.71 -26.062 15.438 C-27.432 15.564 -28.802 15.69 -30.171 15.816 C-43.967 17.067 -57.784 18.049 -71.604 18.998 C-72.458 19.058 -73.312 19.117 -74.193 19.179 C-75.795 19.291 -77.397 19.4 -79 19.506 C-81.94 19.712 -84.8 19.96 -87.692 20.54 C-90.284 21.056 -92.614 21.063 -95.254 21.004 C-102.402 20.944 -109.502 21.363 -116.632 21.798 C-132.329 22.75 -148.016 23.404 -163.734 23.891 C-167.689 24.023 -171.641 24.192 -175.594 24.366 C-187.178 24.868 -198.761 25.191 -210.352 25.461 C-211.689 25.492 -211.689 25.492 -213.054 25.524 C-238.597 26.121 -264.142 26.659 -289.688 27.125 C-291.584 27.16 -291.584 27.16 -293.518 27.195 C-340.274 28.039 -386.963 28.012 -433.716 27.084 C-438.927 26.981 -444.138 26.89 -449.349 26.799 C-460.993 26.594 -472.636 26.348 -484.279 26.066 C-487.174 25.996 -490.068 25.927 -492.962 25.859 C-504.642 25.583 -516.321 25.297 -528 25 C-528.33 24.34 -528.66 23.68 -529 23 C-530.093 23.162 -531.186 23.325 -532.312 23.492 C-537.544 24.143 -542.734 24.146 -548 24.125 C-548.927 24.129 -549.854 24.133 -550.809 24.137 C-557.626 24.127 -564.398 23.766 -571.198 23.312 C-576.997 22.928 -582.799 22.605 -588.602 22.281 C-589.734 22.218 -590.867 22.155 -592.035 22.089 C-594.38 21.959 -596.725 21.828 -599.07 21.698 C-602.597 21.502 -606.125 21.304 -609.652 21.105 C-611.969 20.976 -614.285 20.847 -616.602 20.719 C-617.728 20.656 -618.855 20.593 -620.016 20.529 C-625.523 20.227 -631.031 19.948 -636.542 19.719 C-637.563 19.676 -638.585 19.633 -639.637 19.589 C-641.556 19.511 -643.476 19.437 -645.395 19.367 C-646.24 19.333 -647.084 19.299 -647.955 19.264 C-648.692 19.237 -649.429 19.209 -650.188 19.182 C-652 19 -652 19 -654 18 C-654 17.34 -654 16.68 -654 16 C-654.66 15.67 -655.32 15.34 -656 15 C-646.59 14.815 -646.59 14.815 -642 16 C-641.67 15.34 -641.34 14.68 -641 14 C-640.46 14.318 -639.919 14.636 -639.363 14.964 C-636.545 16.2 -634.27 16.388 -631.2 16.576 C-629.465 16.686 -629.465 16.686 -627.695 16.799 C-626.476 16.865 -625.256 16.931 -624 17 C-622.21 17.101 -622.21 17.101 -620.383 17.205 C-616.523 17.413 -612.662 17.605 -608.801 17.789 C-607.389 17.857 -605.978 17.926 -604.566 17.994 C-601.639 18.135 -598.712 18.274 -595.785 18.412 C-592.031 18.59 -588.278 18.774 -584.524 18.96 C-581.624 19.103 -578.724 19.241 -575.823 19.377 C-574.438 19.443 -573.052 19.511 -571.667 19.581 C-569.743 19.677 -567.82 19.766 -565.896 19.854 C-564.802 19.907 -563.709 19.959 -562.583 20.013 C-560.012 20.191 -560.012 20.191 -558 19 C-556.211 19.227 -554.423 19.459 -552.637 19.709 C-546.776 20.356 -540.86 20.373 -534.969 20.535 C-533.491 20.578 -532.013 20.622 -530.535 20.665 C-527.435 20.756 -524.335 20.844 -521.235 20.93 C-517.338 21.038 -513.441 21.153 -509.544 21.27 C-494.534 21.716 -479.536 22.152 -464.517 21.96 C-459.677 21.915 -455.036 22.174 -450.235 22.773 C-446.292 23.173 -442.357 23.102 -438.398 23.062 C-437.518 23.059 -436.637 23.055 -435.73 23.051 C-432.945 23.039 -430.16 23.02 -427.375 23 C-423.695 22.973 -420.016 22.953 -416.336 22.938 C-415.503 22.929 -414.671 22.921 -413.813 22.912 C-409.095 22.893 -404.653 23.209 -400 24 C-399.67 23.34 -399.34 22.68 -399 22 C-399 22.66 -399 23.32 -399 24 C-397.7 23.893 -397.7 23.893 -396.374 23.783 C-384.771 22.934 -373.214 22.882 -361.584 22.925 C-358.066 22.938 -354.548 22.944 -351.03 22.949 C-343.582 22.96 -336.135 22.98 -328.688 23 C-320.007 23.024 -311.326 23.044 -302.645 23.056 C-299.178 23.062 -295.711 23.075 -292.243 23.088 C-290.132 23.091 -288.02 23.095 -285.909 23.098 C-284.944 23.103 -283.98 23.108 -282.986 23.113 C-282.105 23.113 -281.223 23.113 -280.315 23.114 C-279.55 23.116 -278.784 23.118 -277.996 23.12 C-275.79 23.074 -275.79 23.074 -273 22 C-269.409 22 -265.937 22.45 -262.375 22.879 C-259.85 23.152 -259.85 23.152 -257 22 C-254.72 21.927 -252.437 21.916 -250.156 21.938 C-248.759 21.946 -247.361 21.955 -245.964 21.963 C-244.869 21.972 -244.869 21.972 -243.752 21.982 C-233.412 22.065 -223.078 22.129 -212.75 21.562 C-212.015 21.523 -211.279 21.484 -210.521 21.443 C-207.879 21.279 -205.33 21.075 -202.746 20.484 C-199.316 19.879 -196.065 19.906 -192.582 19.938 C-191.534 19.943 -191.534 19.943 -190.465 19.949 C-188.248 19.961 -186.03 19.98 -183.812 20 C-180.882 20.027 -177.951 20.047 -175.02 20.062 C-173.072 20.08 -173.072 20.08 -171.085 20.098 C-168.088 20.218 -168.088 20.218 -166 19 C-164.253 18.931 -162.504 18.927 -160.756 18.953 C-159.657 18.964 -158.558 18.974 -157.426 18.985 C-156.226 18.999 -155.026 19.013 -153.789 19.027 C-139.566 19.108 -125.42 18.555 -111.217 17.773 C-107.287 17.558 -103.357 17.355 -99.426 17.154 C-96.917 17.022 -94.408 16.888 -91.898 16.754 C-90.728 16.693 -89.557 16.633 -88.351 16.571 C-87.281 16.511 -86.21 16.451 -85.107 16.389 C-83.695 16.312 -83.695 16.312 -82.254 16.234 C-79.967 16.13 -79.967 16.13 -78 15 C-74.642 14.941 -71.299 14.979 -67.941 15.039 C-64.025 15.132 -64.025 15.132 -60.178 14.527 C-57.233 13.814 -54.352 13.684 -51.328 13.5 C-50.024 13.418 -48.719 13.335 -47.375 13.25 C-46 13.166 -44.625 13.083 -43.25 13 C-35.467 12.525 -27.729 12.052 -20 11 C-16.848 10.734 -13.695 10.505 -10.539 10.281 C-8.01 10.149 -8.01 10.149 -6 9 C-6 8.01 -6 7.02 -6 6 C-19.774 6.748 -33.529 7.726 -47.287 8.729 C-51.02 9.001 -54.754 9.27 -58.488 9.539 C-60.888 9.713 -63.288 9.888 -65.688 10.062 C-67.342 10.181 -67.342 10.181 -69.031 10.302 C-73.709 10.646 -78.373 11.019 -83.033 11.555 C-86.908 11.99 -90.605 12.065 -94.5 12 C-100.453 11.943 -106.368 12.095 -112.312 12.438 C-113.397 12.496 -113.397 12.496 -114.504 12.557 C-117.542 12.745 -120.097 13.032 -123 14 C-125.556 14.049 -128.09 14.045 -130.645 14.004 C-142.341 13.912 -154.004 14.442 -165.688 14.938 C-170.2 15.124 -174.713 15.309 -179.227 15.492 C-180.878 15.56 -180.878 15.56 -182.563 15.629 C-189.09 15.893 -195.618 16.128 -202.148 16.32 C-203.258 16.353 -203.258 16.353 -204.391 16.387 C-207.724 16.484 -211.058 16.575 -214.391 16.658 C-216.087 16.705 -216.087 16.705 -217.816 16.754 C-218.78 16.778 -219.744 16.802 -220.737 16.826 C-223.033 16.859 -223.033 16.859 -225 18 C-225 17.34 -225 16.68 -225 16 C-225.66 16.33 -226.32 16.66 -227 17 C-229.113 17.126 -231.229 17.188 -233.346 17.221 C-233.996 17.233 -234.647 17.244 -235.317 17.256 C-237.472 17.293 -239.626 17.322 -241.781 17.352 C-243.276 17.375 -244.77 17.398 -246.265 17.422 C-249.399 17.47 -252.533 17.515 -255.667 17.558 C-259.689 17.612 -263.71 17.676 -267.732 17.743 C-270.819 17.793 -273.905 17.838 -276.992 17.882 C-278.475 17.903 -279.958 17.926 -281.442 17.951 C-283.512 17.985 -285.583 18.012 -287.654 18.038 C-289.424 18.063 -289.424 18.063 -291.229 18.089 C-294 18 -294 18 -296 17 C-296.33 17.66 -296.66 18.32 -297 19 C-298.006 18.837 -299.011 18.674 -300.047 18.506 C-303.838 18.021 -307.428 17.886 -311.246 17.902 C-311.902 17.904 -312.558 17.905 -313.234 17.907 C-315.302 17.912 -317.37 17.925 -319.438 17.938 C-320.851 17.943 -322.264 17.947 -323.678 17.951 C-327.119 17.962 -330.559 17.979 -334 18 C-334 18.66 -334 19.32 -334 20 C-334.66 19.67 -335.32 19.34 -336 19 C-337.402 18.849 -338.81 18.751 -340.219 18.684 C-341.07 18.642 -341.92 18.6 -342.797 18.557 C-343.689 18.517 -344.581 18.478 -345.5 18.438 C-346.792 18.373 -346.792 18.373 -348.109 18.307 C-354.077 18.025 -360.027 17.949 -366 18 C-366.33 18.66 -366.66 19.32 -367 20 C-367 19.34 -367 18.68 -367 18 C-368.47 18.008 -368.47 18.008 -369.969 18.016 C-396.872 18.144 -423.769 17.929 -450.669 17.594 C-453.209 17.563 -455.749 17.533 -458.289 17.503 C-477.094 17.28 -495.89 16.96 -514.686 16.327 C-524.851 15.994 -534.995 15.945 -545.164 16.062 C-548.214 16.067 -551.142 15.918 -554.164 15.508 C-558.07 14.991 -561.812 14.867 -565.75 14.875 C-567.024 14.872 -568.297 14.87 -569.609 14.867 C-572.812 14.844 -572.812 14.844 -575.016 15.633 C-577.597 16.11 -579.448 15.293 -581.903 14.516 C-586.162 13.468 -590.571 13.558 -594.938 13.438 C-596.982 13.367 -599.026 13.296 -601.07 13.223 C-602.072 13.188 -603.074 13.153 -604.106 13.118 C-609.451 12.9 -614.781 12.492 -620.113 12.086 C-622.942 11.774 -622.942 11.774 -625 13 C-625 12.34 -625 11.68 -625 11 C-625.927 11.084 -626.854 11.168 -627.809 11.254 C-629.006 11.356 -630.204 11.458 -631.438 11.562 C-633.23 11.719 -633.23 11.719 -635.059 11.879 C-636.029 11.919 -637 11.959 -638 12 C-638.495 11.505 -638.495 11.505 -639 11 C-640.638 10.998 -642.276 11 -643.914 11.023 C-647.635 10.982 -651.308 10.499 -655 10.062 C-656.152 9.932 -656.152 9.932 -657.328 9.799 C-661.898 9.274 -666.45 8.672 -671 8 C-671 7.34 -671 6.68 -671 6 C-671.99 5.67 -672.98 5.34 -674 5 C-673.67 4.34 -673.34 3.68 -673 3 C-669.125 3.875 -669.125 3.875 -668 5 C-665.477 5.304 -662.973 5.549 -660.441 5.754 C-659.664 5.819 -658.887 5.884 -658.086 5.951 C-656.426 6.089 -654.766 6.224 -653.105 6.357 C-650.631 6.557 -648.158 6.769 -645.686 6.982 C-634.768 7.889 -623.953 8.17 -613 8 C-613 8.66 -613 9.32 -613 10 C-612.145 9.947 -612.145 9.947 -611.272 9.892 C-608.702 9.735 -606.132 9.586 -603.562 9.438 C-602.216 9.353 -602.216 9.353 -600.842 9.268 C-599.988 9.219 -599.134 9.171 -598.254 9.121 C-597.463 9.074 -596.672 9.027 -595.858 8.978 C-594 9 -594 9 -593 10 C-591.206 10.153 -589.407 10.238 -587.608 10.297 C-586.45 10.337 -585.293 10.377 -584.101 10.418 C-582.831 10.457 -581.562 10.495 -580.254 10.535 C-578.941 10.578 -577.628 10.621 -576.275 10.665 C-573.471 10.757 -570.667 10.845 -567.864 10.93 C-564.353 11.037 -560.843 11.152 -557.332 11.269 C-540.623 11.825 -523.931 12.13 -507.211 12.015 C-505.474 12.003 -503.737 12 -502 12 C-502 12.66 -502 13.32 -502 14 C-501.417 13.835 -500.833 13.671 -500.232 13.501 C-496.709 12.71 -493.181 12.999 -489.594 13.117 C-487.738 13.166 -485.882 13.214 -484.026 13.262 C-483.011 13.289 -481.997 13.317 -480.951 13.345 C-454.156 14.048 -427.362 14.119 -400.559 14.133 C-399.55 14.133 -398.542 14.134 -397.503 14.134 C-385.012 14.14 -372.52 14.136 -360.029 14.129 C-351.405 14.124 -342.781 14.127 -334.157 14.132 C-325.751 14.137 -317.345 14.137 -308.939 14.135 C-303.955 14.133 -298.972 14.133 -293.989 14.136 C-289.35 14.139 -284.711 14.137 -280.072 14.131 C-278.372 14.13 -276.672 14.13 -274.972 14.133 C-272.66 14.135 -270.347 14.132 -268.034 14.127 C-266.093 14.126 -266.093 14.126 -264.113 14.126 C-260.966 14.143 -260.966 14.143 -258 13 C-255 13 -255 13 -252.747 13.503 C-249.441 14.101 -246.401 14.041 -243.042 13.946 C-242.361 13.928 -241.681 13.91 -240.98 13.892 C-238.74 13.832 -236.501 13.764 -234.262 13.695 C-232.696 13.651 -231.131 13.607 -229.565 13.564 C-226.286 13.471 -223.007 13.375 -219.728 13.276 C-215.542 13.149 -211.356 13.031 -207.17 12.916 C-203.936 12.827 -200.703 12.733 -197.469 12.638 C-195.927 12.593 -194.384 12.549 -192.842 12.507 C-190.677 12.446 -188.512 12.38 -186.346 12.313 C-184.504 12.259 -184.504 12.259 -182.624 12.203 C-178.944 12.043 -178.944 12.043 -175.094 11.481 C-170.761 10.906 -166.51 10.923 -162.146 10.988 C-152.627 11.055 -143.168 10.691 -133.664 10.195 C-131.967 10.11 -130.27 10.025 -128.573 9.941 C-125.042 9.764 -121.511 9.584 -117.98 9.401 C-113.549 9.172 -109.118 8.949 -104.687 8.729 C-88.188 7.906 -71.693 7.039 -55.211 5.93 C-54.018 5.851 -52.824 5.772 -51.595 5.691 C-50.509 5.615 -49.424 5.539 -48.306 5.46 C-46.903 5.363 -46.903 5.363 -45.472 5.263 C-43.2 5.021 -41.16 4.744 -39 4 C-38.67 3.34 -38.34 2.68 -38 2 C-38 2.99 -38 3.98 -38 5 C-36.541 4.859 -35.083 4.712 -33.625 4.562 C-32.813 4.481 -32.001 4.4 -31.164 4.316 C-28.957 4.109 -28.957 4.109 -27 3 C-25.417 2.979 -23.833 3.009 -22.25 3.062 C-15.248 3.137 -6.36 3.18 0 0 Z M0 3 C1 5 1 5 1 5 Z " fill="#D6614B" transform="translate(846,864)"/>
<path d="M0 0 C0.704 0.318 1.408 0.636 2.134 0.964 C5.167 2.06 7.65 2.377 10.863 2.576 C12.01 2.649 13.158 2.723 14.34 2.799 C15.548 2.865 16.756 2.931 18 3 C19.788 3.101 19.788 3.101 21.613 3.205 C25.475 3.413 29.337 3.604 33.199 3.789 C34.611 3.857 36.022 3.926 37.434 3.994 C40.361 4.135 43.288 4.274 46.215 4.412 C49.969 4.59 53.722 4.774 57.476 4.96 C60.376 5.103 63.276 5.241 66.177 5.377 C67.562 5.443 68.948 5.511 70.333 5.581 C72.257 5.677 74.18 5.766 76.104 5.854 C77.198 5.907 78.291 5.959 79.417 6.013 C81.988 6.191 81.988 6.191 84 5 C85.789 5.227 87.577 5.459 89.363 5.709 C95.224 6.356 101.14 6.373 107.031 6.535 C108.509 6.578 109.987 6.622 111.465 6.665 C114.565 6.756 117.665 6.844 120.765 6.93 C124.662 7.038 128.559 7.153 132.456 7.27 C147.466 7.716 162.464 8.152 177.483 7.96 C182.323 7.915 186.964 8.174 191.765 8.773 C195.708 9.173 199.643 9.102 203.602 9.062 C204.482 9.059 205.363 9.055 206.27 9.051 C209.055 9.039 211.84 9.02 214.625 9 C218.305 8.973 221.984 8.953 225.664 8.938 C226.497 8.929 227.329 8.921 228.187 8.912 C232.905 8.893 237.347 9.209 242 10 C242.33 9.34 242.66 8.68 243 8 C243 8.66 243 9.32 243 10 C243.867 9.929 244.733 9.857 245.626 9.783 C257.229 8.934 268.786 8.882 280.416 8.925 C283.934 8.938 287.452 8.944 290.97 8.949 C298.418 8.96 305.865 8.98 313.312 9 C321.993 9.024 330.674 9.044 339.355 9.056 C342.822 9.062 346.289 9.075 349.757 9.088 C351.868 9.091 353.98 9.095 356.091 9.098 C357.056 9.103 358.02 9.108 359.014 9.113 C359.895 9.113 360.777 9.113 361.685 9.114 C362.45 9.116 363.216 9.118 364.004 9.12 C366.21 9.074 366.21 9.074 369 8 C372.591 8 376.063 8.45 379.625 8.879 C382.15 9.152 382.15 9.152 385 8 C387.28 7.927 389.563 7.916 391.844 7.938 C393.241 7.946 394.639 7.955 396.036 7.963 C397.131 7.972 397.131 7.972 398.248 7.982 C408.588 8.065 418.922 8.129 429.25 7.562 C429.985 7.523 430.721 7.484 431.479 7.443 C434.121 7.279 436.67 7.075 439.254 6.484 C442.684 5.879 445.935 5.906 449.418 5.938 C450.466 5.943 450.466 5.943 451.535 5.949 C453.752 5.961 455.97 5.98 458.188 6 C461.118 6.027 464.049 6.047 466.98 6.062 C468.928 6.08 468.928 6.08 470.915 6.098 C473.912 6.218 473.912 6.218 476 5 C477.747 4.931 479.496 4.927 481.244 4.953 C482.343 4.964 483.442 4.974 484.574 4.985 C485.774 4.999 486.974 5.013 488.211 5.027 C502.434 5.108 516.58 4.555 530.783 3.773 C534.713 3.558 538.643 3.355 542.574 3.154 C545.083 3.022 547.592 2.888 550.102 2.754 C551.272 2.693 552.443 2.633 553.649 2.571 C554.719 2.511 555.79 2.451 556.893 2.389 C558.305 2.312 558.305 2.312 559.746 2.234 C562.033 2.13 562.033 2.13 564 1 C567.358 0.941 570.701 0.979 574.059 1.039 C577.572 1.078 580.619 0.905 584 0 C586.082 -0.067 588.167 -0.085 590.25 -0.062 C591.866 -0.049 591.866 -0.049 593.516 -0.035 C594.335 -0.024 595.155 -0.012 596 0 C596.33 0.99 596.66 1.98 597 3 C595.814 3.086 594.629 3.171 593.407 3.26 C589 3.579 584.592 3.902 580.185 4.227 C578.279 4.367 576.373 4.505 574.467 4.643 C571.724 4.841 568.981 5.043 566.238 5.246 C565.389 5.307 564.54 5.367 563.665 5.429 C559.689 5.727 555.912 6.213 552 7 C550.249 7.045 548.497 7.043 546.746 7.004 C539.598 6.944 532.498 7.363 525.368 7.798 C509.671 8.75 493.984 9.404 478.266 9.891 C474.311 10.023 470.359 10.192 466.406 10.366 C454.822 10.868 443.239 11.191 431.648 11.461 C430.757 11.482 429.865 11.503 428.946 11.524 C403.403 12.121 377.858 12.659 352.312 13.125 C350.416 13.16 350.416 13.16 348.482 13.195 C301.726 14.039 255.037 14.012 208.284 13.084 C203.073 12.981 197.862 12.89 192.651 12.799 C181.007 12.594 169.364 12.348 157.721 12.066 C154.826 11.996 151.932 11.927 149.038 11.859 C137.358 11.583 125.679 11.297 114 11 C113.505 10.01 113.505 10.01 113 9 C111.36 9.244 111.36 9.244 109.688 9.492 C104.456 10.143 99.266 10.146 94 10.125 C93.073 10.129 92.146 10.133 91.191 10.137 C84.374 10.127 77.602 9.766 70.802 9.312 C65.003 8.928 59.201 8.605 53.398 8.281 C52.266 8.218 51.133 8.155 49.965 8.089 C47.62 7.959 45.275 7.828 42.93 7.698 C39.403 7.502 35.875 7.304 32.348 7.105 C30.031 6.976 27.715 6.847 25.398 6.719 C24.272 6.656 23.145 6.593 21.984 6.529 C16.477 6.227 10.969 5.948 5.458 5.719 C4.437 5.676 3.415 5.633 2.363 5.589 C0.444 5.511 -1.476 5.437 -3.395 5.367 C-4.24 5.333 -5.084 5.299 -5.955 5.264 C-6.692 5.237 -7.429 5.209 -8.188 5.182 C-10 5 -10 5 -12 4 C-12 3.34 -12 2.68 -12 2 C-12.66 1.67 -13.32 1.34 -14 1 C-4.59 0.815 -4.59 0.815 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DE6C54" transform="translate(204,878)"/>
<path d="M0 0 C2.938 1.375 2.938 1.375 5 3 C5 3.66 5 4.32 5 5 C5.66 5 6.32 5 7 5 C7 4.34 7 3.68 7 3 C11.964 -0.055 16.292 -1.062 22 0 C26.117 2.395 27.651 3.944 29.148 8.477 C30.343 13.751 30.255 18.993 30.188 24.375 C30.187 25.371 30.186 26.368 30.186 27.395 C30.141 34.718 30.141 34.718 29 37 C21.25 37.125 21.25 37.125 19 36 C18.939 34.698 18.879 33.396 18.816 32.055 C18.732 30.328 18.647 28.602 18.562 26.875 C18.523 26.019 18.484 25.163 18.443 24.281 C18.218 19.783 17.865 15.432 17 11 C15.208 10.217 15.208 10.217 13 10 C10.799 11.1 10.799 11.1 9 13 C8.376 15.37 8.376 15.37 8.426 18.035 C8.398 19.009 8.37 19.983 8.342 20.986 C8.327 22.509 8.327 22.509 8.312 24.062 C8.271 26.074 8.225 28.086 8.176 30.098 C8.165 30.99 8.153 31.882 8.142 32.801 C8 35 8 35 7 37 C3.7 37 0.4 37 -3 37 C-3 36.34 -3 35.68 -3 35 C-3.66 34.67 -4.32 34.34 -5 34 C-4.34 34 -3.68 34 -3 34 C-3.495 22.12 -3.495 22.12 -4 10 C-6.31 10.33 -8.62 10.66 -11 11 C-13.234 14.351 -13.238 15.418 -13.195 19.324 C-13.189 20.352 -13.182 21.38 -13.176 22.439 C-13.159 23.511 -13.142 24.583 -13.125 25.688 C-13.116 26.771 -13.107 27.854 -13.098 28.971 C-13.074 31.647 -13.041 34.324 -13 37 C-14.937 37.081 -16.875 37.139 -18.812 37.188 C-19.891 37.222 -20.97 37.257 -22.082 37.293 C-25 37 -25 37 -26.887 35.488 C-27.254 34.997 -27.621 34.506 -28 34 C-27.34 33.34 -26.68 32.68 -26 32 C-25.627 28.795 -25.627 28.795 -25.586 25.023 C-25.567 24.338 -25.547 23.653 -25.527 22.947 C-25.467 20.756 -25.421 18.566 -25.375 16.375 C-25.337 14.891 -25.298 13.406 -25.258 11.922 C-25.162 8.281 -25.078 4.641 -25 1 C-21.37 1 -17.74 1 -14 1 C-13.67 1.66 -13.34 2.32 -13 3 C-11.732 2.258 -11.732 2.258 -10.438 1.5 C-6.557 -0.193 -4.191 -0.457 0 0 Z " fill="#8F4A3B" transform="translate(549,731)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C2.843 1.493 2.843 1.493 3.703 0.977 C6.397 -0.169 7.88 -0.047 10.75 0.375 C11.549 0.486 12.348 0.597 13.172 0.711 C13.775 0.806 14.378 0.902 15 1 C15.144 1.928 15.289 2.856 15.438 3.812 C15.921 7.007 15.921 7.007 17 10 C16.667 11.667 16.334 13.333 16 15 C15.906 16.526 15.839 18.054 15.789 19.582 C15.747 20.857 15.747 20.857 15.703 22.158 C15.651 23.916 15.607 25.674 15.57 27.432 C15.314 34.499 14.26 40.265 9.625 45.875 C7.089 47.928 5.08 49.001 2 50 C1.01 50.495 1.01 50.495 0 51 C-2.211 50.912 -4.42 50.757 -6.625 50.562 C-7.814 50.461 -9.002 50.359 -10.227 50.254 C-11.142 50.17 -12.057 50.086 -13 50 C-13 49.34 -13 48.68 -13 48 C-15.639 46.586 -18.071 45.628 -21 45 C-21.381 42.674 -21.713 40.339 -22 38 C-21.505 37.505 -21.505 37.505 -21 37 C-19.206 37.038 -17.413 37.155 -15.625 37.312 C-14.648 37.391 -13.671 37.47 -12.664 37.551 C-10 38 -10 38 -7 40 C-3.875 40.167 -3.875 40.167 -1 40 C-0.67 38.68 -0.34 37.36 0 36 C0.66 36.66 1.32 37.32 2 38 C2 37.01 2 36.02 2 35 C1.432 35.16 0.863 35.32 0.277 35.484 C-4.517 36.57 -9.257 36.736 -13.812 34.812 C-16.28 32.768 -18.091 30.567 -20 28 C-20.66 27.34 -21.32 26.68 -22 26 C-22.043 24 -22.041 22 -22 20 C-22.33 19.01 -22.66 18.02 -23 17 C-22.34 17 -21.68 17 -21 17 C-20.938 15.804 -20.876 14.607 -20.812 13.375 C-20.217 8.704 -18.432 6.279 -15 3 C-10.3 -0.205 -5.569 -0.858 0 0 Z M-6.625 10 C-9.914 12.77 -10.435 14.766 -11 19 C-10.371 21.769 -9.46 23.483 -8 26 C-7.01 26.33 -6.02 26.66 -5 27 C-4.67 26.67 -4.34 26.34 -4 26 C-3.01 26.33 -2.02 26.66 -1 27 C1.29 26.161 1.29 26.161 3 24 C4.098 20.298 4.426 17.137 3.062 13.5 C2.712 13.005 2.361 12.51 2 12 C1.34 12 0.68 12 0 12 C0 11.34 0 10.68 0 10 C-2.74 8.884 -3.814 8.929 -6.625 10 Z " fill="#864234" transform="translate(705,659)"/>
<path d="M0 0 C1.818 0.594 1.818 0.594 3.629 -0.062 C6.131 -0.635 8.026 -0.461 10.562 -0.125 C11.761 0.028 11.761 0.028 12.984 0.184 C13.588 0.267 14.191 0.351 14.812 0.438 C14.937 5.418 15.027 10.397 15.087 15.379 C15.112 17.07 15.146 18.762 15.19 20.453 C15.625 37.833 15.625 37.833 9.812 44.438 C4.897 49.055 -0.75 48.719 -7.199 48.656 C-11.273 48.358 -13.7 47.561 -17.188 45.438 C-18.625 43.25 -18.625 43.25 -19.188 41.438 C-19.847 41.438 -20.508 41.438 -21.188 41.438 C-21.683 39.952 -21.683 39.952 -22.188 38.438 C-19.425 35.675 -16.98 35.859 -13.188 35.438 C-13.67 34.918 -14.152 34.399 -14.648 33.863 C-19.904 28.06 -22.748 23.912 -22.574 15.918 C-21.74 10.57 -19.283 5.215 -15.129 1.691 C-10.642 -1.207 -5.118 -1.316 0 0 Z M-8.5 10 C-10.587 13.014 -11.115 14.765 -11.188 18.438 C-10.301 22.285 -10.301 22.285 -8.188 25.438 C-5.341 26.773 -5.341 26.773 -2.188 26.438 C1.541 24.704 1.541 24.704 3.812 21.438 C4.382 15.875 4.382 15.875 2.188 11 C-1.601 8.507 -4.553 7.333 -8.5 10 Z M2.812 31.438 C2.482 32.097 2.153 32.758 1.812 33.438 C2.472 34.097 3.133 34.758 3.812 35.438 C3.482 34.117 3.153 32.798 2.812 31.438 Z M-2.438 34.438 C-5.24 35.457 -7.236 35.606 -10.188 35.438 C-9.84 37.413 -9.84 37.413 -9.188 39.438 C-6.378 40.842 -4.299 40.686 -1.188 40.438 C1.155 39.058 1.155 39.058 2.812 37.438 C2.153 36.117 1.492 34.798 0.812 33.438 C-0.446 33.37 -0.446 33.37 -2.438 34.438 Z " fill="#813D2F" transform="translate(665.1875,731.5625)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.99 2 2.98 2 4 C2.034 5.824 2.068 7.647 2.103 9.471 C2.156 12.561 2.189 15.65 2.22 18.74 C2.24 20.363 2.267 21.985 2.302 23.608 C2.651 40.393 2.651 40.393 -2 47 C-7.105 51.974 -14.129 52.547 -21 53 C-25.7 52.373 -28.949 49.493 -32 46 C-32.938 42.625 -32.938 42.625 -33 40 C-31.231 39.778 -29.46 39.573 -27.688 39.375 C-26.701 39.259 -25.715 39.143 -24.699 39.023 C-22 39 -22 39 -19 41 C-19.495 41.495 -19.495 41.495 -20 42 C-18.35 42.66 -16.7 43.32 -15 44 C-14.67 43.34 -14.34 42.68 -14 42 C-12.515 41.505 -12.515 41.505 -11 41 C-10.67 39.02 -10.34 37.04 -10 35 C-10.763 35.474 -11.526 35.949 -12.312 36.438 C-17.027 39.179 -17.027 39.179 -20.086 38.617 C-25.899 36.732 -29.796 34.59 -33.105 29.199 C-35.17 24.124 -34.826 17.77 -33.277 12.578 C-31.31 8.34 -28.99 5.558 -25 3 C-19.316 1.105 -14.502 1.853 -9 4 C-8.67 3.67 -8.34 3.34 -8 3 C-5.667 2.959 -3.333 2.958 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z M-20.75 13 C-23.278 16.371 -23.709 17.967 -23.414 22.059 C-22.714 25.343 -21.613 26.923 -19 29 C-15.405 29.248 -15.405 29.248 -12 28 C-9.858 25.819 -9.052 24.594 -8.785 21.527 C-8.877 20.673 -8.968 19.818 -9.062 18.938 C-9.136 18.08 -9.209 17.223 -9.285 16.34 C-10.204 13.331 -11.261 12.489 -14 11 C-18.028 10.608 -18.028 10.608 -20.75 13 Z " fill="#884436" transform="translate(529,657)"/>
<path d="M0 0 C6.756 0.276 6.756 0.276 9.574 2.949 C11.165 5.329 12.499 7.757 13.828 10.293 C15.115 12.483 16.406 14.671 17.703 16.855 C18.734 18.65 19.766 20.444 20.828 22.293 C21.158 15.363 21.488 8.433 21.828 1.293 C25.458 1.293 29.088 1.293 32.828 1.293 C32.828 15.813 32.828 30.333 32.828 45.293 C28.629 46.343 26.034 46.021 21.828 45.293 C21.498 44.303 21.168 43.313 20.828 42.293 C20.168 41.963 19.508 41.633 18.828 41.293 C17.63 39.197 16.493 37.066 15.391 34.918 C12.81 29.569 12.81 29.569 8.828 25.293 C8.168 25.293 7.508 25.293 6.828 25.293 C6.912 25.899 6.996 26.505 7.082 27.129 C7.845 33.407 7.635 39.021 6.828 45.293 C1.878 45.788 1.878 45.788 -3.172 46.293 C-3.364 40.064 -3.543 33.834 -3.709 27.604 C-3.767 25.483 -3.83 23.363 -3.896 21.243 C-3.989 18.2 -4.07 15.157 -4.148 12.113 C-4.181 11.161 -4.213 10.209 -4.246 9.228 C-4.266 8.347 -4.286 7.467 -4.307 6.559 C-4.34 5.393 -4.34 5.393 -4.374 4.204 C-4.06 1.241 -2.877 0.388 0 0 Z M6.828 21.293 C7.488 21.953 8.148 22.613 8.828 23.293 C8.828 22.633 8.828 21.973 8.828 21.293 C8.168 21.293 7.508 21.293 6.828 21.293 Z " fill="#8F3431" transform="translate(361.171875,932.70703125)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C4.32 2 5.64 2 7 2 C7.364 2.75 7.727 3.5 8.102 4.273 C11.433 11.057 15 17.588 19 24 C19.029 20.875 19.047 17.75 19.062 14.625 C19.071 13.736 19.079 12.846 19.088 11.93 C19.091 11.079 19.094 10.228 19.098 9.352 C19.103 8.566 19.108 7.781 19.114 6.971 C19.126 4.922 19.126 4.922 18 3 C20.478 1.761 22.237 1.833 25 1.812 C25.887 1.798 26.774 1.784 27.688 1.77 C30 2 30 2 32 4 C31.34 4 30.68 4 30 4 C30 17.86 30 31.72 30 46 C25.545 46.495 25.545 46.495 21 47 C20.602 46.397 20.203 45.793 19.793 45.172 C18.998 43.973 18.998 43.973 18.188 42.75 C17.665 41.961 17.143 41.172 16.605 40.359 C15.57 38.838 14.491 37.345 13.363 35.891 C12 34 12 34 11 31 C9.35 28.03 7.7 25.06 6 22 C5.67 29.92 5.34 37.84 5 46 C2.36 46.33 -0.28 46.66 -3 47 C-3.99 47.495 -3.99 47.495 -5 48 C-5.927 45.219 -6.122 43.579 -6.114 40.712 C-6.113 39.421 -6.113 39.421 -6.113 38.104 C-6.108 37.179 -6.103 36.254 -6.098 35.301 C-6.096 34.351 -6.095 33.4 -6.093 32.421 C-6.088 29.385 -6.075 26.349 -6.062 23.312 C-6.057 21.255 -6.053 19.197 -6.049 17.139 C-6.038 12.092 -6.021 7.046 -6 2 C-4.02 1.67 -2.04 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#892C2C" transform="translate(302,932)"/>
<path d="M0 0 C3.412 2.24 5.257 5.465 6.797 9.18 C7.109 12.18 7.109 12.18 6.797 14.18 C2.478 15.184 -0.315 15.512 -4.203 13.18 C-5.016 11.055 -5.016 11.055 -5.203 9.18 C-6.853 9.18 -8.503 9.18 -10.203 9.18 C-10.203 8.52 -10.203 7.86 -10.203 7.18 C-11.152 7.675 -12.101 8.17 -13.078 8.68 C-16.203 10.18 -16.203 10.18 -18.203 10.18 C-20.3 17.512 -21.147 24.123 -17.766 31.18 C-16.352 33.284 -16.352 33.284 -14.203 34.18 C-10.53 34.313 -7.705 34.347 -4.203 33.18 C-4.533 32.19 -4.863 31.2 -5.203 30.18 C-8.078 28.742 -11.114 28.038 -14.203 27.18 C-14.246 25.18 -14.244 23.179 -14.203 21.18 C-12.307 19.283 -8.992 19.827 -6.453 19.68 C-1.373 19.392 -1.373 19.392 3.625 18.523 C4.342 18.41 5.058 18.297 5.797 18.18 C8.838 21.221 8.09 24.228 8.119 28.385 C8.116 29.451 8.113 30.518 8.109 31.617 C8.125 32.68 8.142 33.743 8.158 34.838 C8.159 35.857 8.159 36.876 8.16 37.926 C8.163 38.861 8.166 39.796 8.169 40.76 C7.737 43.571 6.989 44.441 4.797 46.18 C4.137 45.52 3.477 44.86 2.797 44.18 C2.116 44.015 1.436 43.85 0.734 43.68 C0.095 43.515 -0.544 43.35 -1.203 43.18 C-1.533 42.52 -1.863 41.86 -2.203 41.18 C-2.614 41.655 -3.026 42.131 -3.449 42.621 C-6.571 45.395 -10.152 45.414 -14.203 45.18 C-17.692 44.143 -20.933 42.77 -24.203 41.18 C-24.203 40.52 -24.203 39.86 -24.203 39.18 C-25.193 38.85 -26.183 38.52 -27.203 38.18 C-31.453 31.907 -33.156 24.699 -32.203 17.18 C-30.216 10.425 -27.604 4.56 -21.703 0.43 C-14.964 -2.94 -6.925 -2.908 0 0 Z " fill="#8E302F" transform="translate(430.203125,934.8203125)"/>
<path d="M0 0 C2.528 2.341 4.452 4.905 6 8 C6.04 10 6.043 12 6 14 C2.7 13.67 -0.6 13.34 -4 13 C-4.33 11.68 -4.66 10.36 -5 9 C-5.66 9 -6.32 9 -7 9 C-7 8.34 -7 7.68 -7 7 C-11.715 7.02 -14.969 7.069 -18.562 10.312 C-21.075 16.758 -21.04 23.212 -18.625 29.688 C-17.207 32.094 -17.207 32.094 -15 33 C-13.293 33.07 -11.583 33.084 -9.875 33.062 C-8.965 33.053 -8.055 33.044 -7.117 33.035 C-6.419 33.024 -5.72 33.012 -5 33 C-4.67 31.35 -4.34 29.7 -4 28 C-5.32 28.33 -6.64 28.66 -8 29 C-8 28.67 -8 28.34 -8 28 C-10.31 27.34 -12.62 26.68 -15 26 C-14.34 23.69 -13.68 21.38 -13 19 C-11.68 19 -10.36 19 -9 19 C-8.67 18.01 -8.34 17.02 -8 16 C-8 16.66 -8 17.32 -8 18 C-4.535 17.505 -4.535 17.505 -1 17 C-1 17.66 -1 18.32 -1 19 C0.702 18.969 0.702 18.969 2.438 18.938 C6 19 6 19 7 20 C7.087 21.635 7.107 23.273 7.098 24.91 C7.094 25.9 7.091 26.889 7.088 27.908 C7.075 29.47 7.075 29.47 7.062 31.062 C7.058 32.107 7.053 33.152 7.049 34.229 C7.037 36.819 7.021 39.41 7 42 C3.528 42.667 1.087 43.014 -2.438 42.438 C-6.015 41.748 -6.015 41.748 -9.375 43.062 C-15.076 44.537 -19.639 43.288 -24.75 40.5 C-31.089 33.457 -32.733 26.664 -32.355 17.277 C-31.598 10.29 -29.528 5.607 -24 1 C-16.487 -4.131 -8 -4.545 0 0 Z M-3 39 C-2 41 -2 41 -2 41 Z " fill="#933431" transform="translate(572,936)"/>
<path d="M0 0 C10.56 0 21.12 0 32 0 C33 2 33 2 32 10 C25.07 10 18.14 10 11 10 C11.33 12.64 11.66 15.28 12 18 C13.132 17.977 14.264 17.954 15.43 17.93 C16.911 17.911 18.393 17.893 19.875 17.875 C20.621 17.858 21.368 17.841 22.137 17.824 C24.092 17.807 26.047 17.897 28 18 C30 20 30 20 32 23 C31.01 23 30.02 23 29 23 C29 24.65 29 26.3 29 28 C23.06 28 17.12 28 11 28 C11 30.64 11 33.28 11 36 C11.66 36 12.32 36 13 36 C13.33 35.34 13.66 34.68 14 34 C14 34.66 14 35.32 14 36 C19.61 36 25.22 36 31 36 C33.25 42.75 33.25 42.75 32 47 C27.498 47.025 22.995 47.043 18.493 47.055 C16.961 47.06 15.43 47.067 13.898 47.075 C11.696 47.088 9.495 47.093 7.293 47.098 C6.608 47.103 5.924 47.108 5.219 47.113 C2.191 47.114 -0.102 46.966 -3 46 C-3 45.34 -3 44.68 -3 44 C-2.34 44 -1.68 44 -1 44 C-1.186 43.443 -1.371 42.886 -1.562 42.312 C-2 40 -2 40 -1.531 37.707 C-0.971 34.854 -0.902 32.279 -0.938 29.375 C-0.947 28.434 -0.956 27.493 -0.965 26.523 C-0.976 25.691 -0.988 24.858 -1 24 C-1 23.01 -1 22.02 -1 21 C-1.012 19.723 -1.023 18.445 -1.035 17.129 C-1.044 15.607 -1.054 14.085 -1.062 12.562 C-1.075 11.417 -1.075 11.417 -1.088 10.248 C-1.091 9.507 -1.094 8.767 -1.098 8.004 C-1.103 7.332 -1.108 6.66 -1.114 5.967 C-1 4 -1 4 0 0 Z " fill="#793128" transform="translate(279,649)"/>
<path d="M0 0 C3.754 3.67 5.673 6.935 7 12 C6.34 12.66 5.68 13.32 5 14 C2.617 13.977 2.617 13.977 -0.125 13.625 C-1.035 13.514 -1.945 13.403 -2.883 13.289 C-3.581 13.194 -4.28 13.098 -5 13 C-5.33 11.68 -5.66 10.36 -6 9 C-8.97 8.67 -11.94 8.34 -15 8 C-15 9.65 -15 11.3 -15 13 C-13.647 13.46 -12.292 13.918 -10.938 14.375 C-10.116 14.653 -9.295 14.932 -8.449 15.219 C-6.411 15.869 -4.333 16.389 -2.25 16.875 C1.771 18.267 3.456 19.584 6 23 C7.907 27.641 8.292 30.951 6.938 35.812 C4.051 40.562 1.249 42.848 -4.062 44.375 C-10.969 45.659 -18.196 44.888 -24.062 40.938 C-26.556 37.157 -27.834 33.353 -29 29 C-24.744 27.839 -21.288 27.948 -17 29 C-15.688 31 -15.688 31 -15 33 C-12.229 34.386 -10.067 34.188 -7 34 C-6.01 33.34 -5.02 32.68 -4 32 C-4.33 31.01 -4.66 30.02 -5 29 C-8.274 27.632 -11.548 26.512 -14.922 25.426 C-20.261 23.59 -24.357 21.287 -27 16 C-27.598 9.899 -26.459 6.861 -23 2 C-16.103 -3.173 -7.882 -3.211 0 0 Z " fill="#8D312F" transform="translate(651,935)"/>
<path d="M0 0 C1.65 1.65 3.3 3.3 5 5 C4.01 5.33 3.02 5.66 2 6 C2.33 10.62 2.66 15.24 3 20 C4.65 19.01 6.3 18.02 8 17 C13.226 16.43 16.567 17.179 21 20 C25.003 24.28 25.126 28.397 25.098 34.078 C25.094 35.238 25.091 36.398 25.088 37.594 C25.075 39.404 25.075 39.404 25.062 41.25 C25.058 42.472 25.053 43.694 25.049 44.953 C25.037 47.969 25.021 50.984 25 54 C23.354 54.027 21.708 54.046 20.062 54.062 C19.146 54.074 18.229 54.086 17.285 54.098 C15 54 15 54 14 53 C13.855 51.402 13.773 49.799 13.719 48.195 C13.681 47.219 13.644 46.244 13.605 45.238 C13.571 44.211 13.536 43.184 13.5 42.125 C13.431 40.107 13.358 38.088 13.281 36.07 C13.251 35.171 13.221 34.271 13.189 33.344 C13.081 30.847 13.081 30.847 12 28 C8.584 27.75 8.584 27.75 5 28 C2.796 31.306 2.768 32.241 2.84 36.07 C2.851 37.049 2.862 38.027 2.873 39.035 C2.894 40.055 2.916 41.074 2.938 42.125 C2.972 44.148 3.002 46.172 3.027 48.195 C3.045 49.093 3.063 49.99 3.082 50.915 C3 53 3 53 2 54 C0.147 54.072 -1.708 54.084 -3.562 54.062 C-4.574 54.053 -5.586 54.044 -6.629 54.035 C-7.411 54.024 -8.194 54.012 -9 54 C-9.051 46.773 -9.086 39.547 -9.11 32.32 C-9.12 29.86 -9.134 27.4 -9.151 24.94 C-9.175 21.411 -9.186 17.881 -9.195 14.352 C-9.206 13.246 -9.216 12.141 -9.227 11.002 C-9.227 9.981 -9.227 8.961 -9.227 7.909 C-9.231 7.007 -9.236 6.106 -9.241 5.177 C-9 3 -9 3 -7 1 C-7 1.66 -7 2.32 -7 3 C-3.069 2.425 -3.069 2.425 0 0 Z " fill="#A25F4D" transform="translate(594,642)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C3.64 3 6.28 3 9 3 C9.075 7.429 9.129 11.858 9.165 16.287 C9.18 17.792 9.2 19.296 9.226 20.8 C9.263 22.972 9.28 25.144 9.293 27.316 C9.309 28.62 9.324 29.924 9.341 31.268 C8.844 36.712 7.202 40.497 3.75 44.812 C-0.925 48.532 -5.223 49.226 -11.062 48.875 C-15.614 48.267 -18.528 47.142 -22 44 C-25.492 38.874 -26.573 35.121 -26.391 28.879 C-26.385 28.134 -26.379 27.389 -26.373 26.622 C-26.351 24.268 -26.301 21.916 -26.25 19.562 C-26.23 17.956 -26.212 16.35 -26.195 14.744 C-26.151 10.829 -26.083 6.915 -26 3 C-25.34 3 -24.68 3 -24 3 C-23.67 2.34 -23.34 1.68 -23 1 C-23 1.66 -23 2.32 -23 3 C-21.298 2.969 -21.298 2.969 -19.562 2.938 C-16 3 -16 3 -15 4 C-14.866 6.255 -14.796 8.515 -14.754 10.773 C-14.721 12.169 -14.688 13.565 -14.654 14.961 C-14.607 17.169 -14.562 19.376 -14.523 21.584 C-14.483 23.71 -14.432 25.835 -14.379 27.961 C-14.354 29.238 -14.329 30.514 -14.303 31.83 C-14.237 35.157 -14.237 35.157 -12 38 C-9.793 38.364 -9.793 38.364 -7.375 38.188 C-6.149 38.147 -6.149 38.147 -4.898 38.105 C-3.959 38.053 -3.959 38.053 -3 38 C-2.98 36.991 -2.96 35.982 -2.94 34.942 C-2.864 31.192 -2.775 27.443 -2.683 23.693 C-2.644 22.072 -2.61 20.45 -2.578 18.828 C-2.532 16.494 -2.474 14.161 -2.414 11.828 C-2.403 11.105 -2.391 10.382 -2.379 9.637 C-2.271 5.953 -2.023 3.137 0 0 Z " fill="#973935" transform="translate(527,931)"/>
<path d="M0 0 C4.192 3.525 7.387 7.656 7.943 13.254 C8.615 31.526 8.615 31.526 4.234 37.715 C0.955 41.244 -1.947 42.944 -6.562 44.25 C-11.839 44.339 -16.732 43.44 -20.863 39.949 C-26.422 33.603 -27.774 25.505 -28.25 17.312 C-27.371 11.555 -24.764 6.924 -21.25 2.312 C-20.817 1.735 -20.384 1.158 -19.938 0.562 C-13.765 -4.01 -6.428 -3.337 0 0 Z M-12.938 7.938 C-16.908 12.312 -16.377 18.744 -16.25 24.312 C-15.906 27.316 -15.331 29.448 -14.25 32.312 C-11.037 34.455 -9.983 34.966 -6.25 34.312 C-2.606 29.936 -2.796 25.664 -2.875 20.25 C-2.863 19.448 -2.852 18.645 -2.84 17.818 C-2.858 14.27 -2.89 11.951 -4.656 8.82 C-6.193 7.246 -6.193 7.246 -8.125 6.25 C-10.47 6.145 -10.47 6.145 -12.938 7.938 Z " fill="#8D312F" transform="translate(769.25,935.6875)"/>
<path d="M0 0 C1.257 0.048 2.514 0.097 3.809 0.146 C5.127 0.203 6.446 0.259 7.805 0.316 C9.145 0.37 10.484 0.422 11.824 0.475 C15.11 0.605 18.395 0.74 21.68 0.879 C21.68 3.849 21.68 6.819 21.68 9.879 C18.222 10.743 15.69 11.014 12.18 11.004 C10.772 11.008 10.772 11.008 9.336 11.012 C6.958 10.893 4.953 10.554 2.68 9.879 C2.68 12.519 2.68 15.159 2.68 17.879 C3.823 17.867 4.966 17.856 6.145 17.844 C7.635 17.834 9.126 17.825 10.617 17.816 C11.372 17.808 12.127 17.8 12.904 17.791 C14.83 17.782 16.755 17.827 18.68 17.879 C19.68 18.879 19.68 18.879 19.777 20.945 C19.745 23.257 19.712 25.568 19.68 27.879 C14.07 27.879 8.46 27.879 2.68 27.879 C2.68 30.519 2.68 33.159 2.68 35.879 C8.95 35.879 15.22 35.879 21.68 35.879 C21.68 38.849 21.68 41.819 21.68 44.879 C15.764 46.166 9.888 46.026 3.867 46.004 C2.275 46.01 2.275 46.01 0.65 46.016 C-0.861 46.014 -0.861 46.014 -2.402 46.012 C-3.322 46.011 -4.243 46.009 -5.191 46.008 C-7.32 45.879 -7.32 45.879 -8.32 44.879 C-8.413 43.497 -8.438 42.111 -8.434 40.726 C-8.434 39.405 -8.434 39.405 -8.434 38.057 C-8.426 36.621 -8.426 36.621 -8.418 35.156 C-8.417 34.181 -8.415 33.206 -8.414 32.201 C-8.408 29.073 -8.396 25.945 -8.383 22.816 C-8.378 20.701 -8.373 18.586 -8.369 16.471 C-8.358 11.273 -8.341 6.076 -8.32 0.879 C-5.474 -0.544 -3.174 -0.129 0 0 Z " fill="#933331" transform="translate(269.3203125,933.12109375)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C11.67 1.32 11.34 2.64 11 4 C10.91 5.65 10.869 7.304 10.867 8.957 C10.866 9.905 10.865 10.853 10.863 11.83 C10.867 12.814 10.871 13.798 10.875 14.812 C10.871 15.785 10.867 16.758 10.863 17.76 C10.87 22.924 10.909 27.939 12 33 C12.66 33.33 13.32 33.66 14 34 C14 34.66 14 35.32 14 36 C16.64 35.67 19.28 35.34 22 35 C22.943 30.958 23.193 27.094 23.316 22.949 C23.337 22.29 23.358 21.632 23.379 20.953 C23.444 18.864 23.504 16.776 23.562 14.688 C23.606 13.265 23.649 11.842 23.693 10.42 C23.8 6.947 23.901 3.473 24 0 C27.3 0 30.6 0 34 0 C36.244 4.487 35.23 10.636 35.25 15.562 C35.271 16.814 35.291 18.065 35.312 19.354 C35.378 34.826 35.378 34.826 30.725 40.576 C26.689 44.302 22.198 46.006 16.73 45.996 C9.895 45.463 5.556 43.207 1 38 C-0.232 34.305 -0.124 30.897 -0.098 27.051 C-0.096 26.278 -0.095 25.506 -0.093 24.71 C-0.088 22.244 -0.075 19.778 -0.062 17.312 C-0.057 15.64 -0.053 13.967 -0.049 12.295 C-0.038 8.197 -0.021 4.098 0 0 Z " fill="#903230" transform="translate(585,934)"/>
<path d="M0 0 C10.89 0 21.78 0 33 0 C34.546 3.092 34.137 6.351 34.127 9.744 C34.129 10.553 34.13 11.362 34.132 12.195 C34.136 14.92 34.133 17.646 34.129 20.371 C34.131 22.319 34.133 24.267 34.135 26.214 C34.139 31.516 34.137 36.817 34.134 42.119 C34.132 47.657 34.134 53.194 34.136 58.732 C34.138 68.033 34.135 77.335 34.13 86.636 C34.125 97.402 34.127 108.169 34.132 118.935 C34.137 128.165 34.137 137.395 34.135 146.625 C34.133 152.143 34.133 157.662 34.136 163.181 C34.139 168.368 34.137 173.556 34.131 178.743 C34.13 180.651 34.13 182.56 34.133 184.468 C34.135 187.064 34.132 189.66 34.127 192.256 C34.129 193.019 34.131 193.783 34.134 194.569 C34.114 199.772 34.114 199.772 33 202 C26.07 202 19.14 202 12 202 C12 201.67 12 201.34 12 201 C17.94 200.67 23.88 200.34 30 200 C30.33 134.99 30.66 69.98 31 3 C21.1 3 11.2 3 1 3 C1.131 13.89 1.267 24.78 1.404 35.67 C1.437 38.299 1.47 40.928 1.502 43.557 C1.805 68.556 1.805 68.556 2.359 93.551 C2.387 94.579 2.415 95.608 2.444 96.667 C2.57 101.241 2.711 105.815 2.871 110.388 C2.918 111.949 2.965 113.51 3.012 115.07 C3.058 116.378 3.104 117.685 3.151 119.032 C3 122 3 122 1 124 C0.909 122.138 0.909 122.138 0.816 120.238 C0.732 118.596 0.647 116.954 0.562 115.312 C0.523 114.496 0.484 113.679 0.443 112.838 C0.381 111.644 0.381 111.644 0.316 110.426 C0.28 109.698 0.243 108.97 0.205 108.22 C0.048 105.989 0.048 105.989 -0.5 103.805 C-1.095 100.467 -1.163 97.289 -1.205 93.897 C-1.22 92.851 -1.22 92.851 -1.235 91.785 C-1.267 89.483 -1.292 87.181 -1.316 84.879 C-1.337 83.257 -1.358 81.636 -1.379 80.014 C-1.423 76.605 -1.464 73.197 -1.503 69.788 C-1.551 65.486 -1.605 61.184 -1.661 56.882 C-1.766 48.739 -1.859 40.597 -1.936 32.454 C-1.979 27.91 -2.035 23.367 -2.097 18.824 C-2.116 16.829 -2.135 14.834 -2.152 12.84 C-2.167 11.972 -2.183 11.104 -2.198 10.21 C-2.221 5.986 -1.938 3.876 0 0 Z " fill="#ED3534" transform="translate(708,110)"/>
<path d="M0 0 C3.024 3.326 3.898 5.377 4.625 9.812 C4.225 16.382 0.105 21.118 -4.625 25.348 C-7.043 27.295 -9.487 29.177 -12 31 C-10.857 30.988 -9.713 30.977 -8.535 30.965 C-7.044 30.955 -5.553 30.946 -4.062 30.938 C-3.308 30.929 -2.553 30.921 -1.775 30.912 C0.15 30.903 2.075 30.948 4 31 C5 32 5 32 5.098 34.066 C5.086 34.89 5.074 35.714 5.062 36.562 C5.053 37.389 5.044 38.215 5.035 39.066 C5.024 39.704 5.012 40.343 5 41 C3.125 42.062 3.125 42.062 1 43 C0.34 42.67 -0.32 42.34 -1 42 C-2.586 41.927 -4.174 41.916 -5.762 41.938 C-7.194 41.949 -7.194 41.949 -8.654 41.961 C-10.657 41.987 -12.66 42.013 -14.662 42.039 C-19.899 42.081 -24.841 41.924 -30 41 C-27.649 31.829 -25.15 27.202 -17 22 C-16.134 21.134 -16.134 21.134 -15.25 20.25 C-14.837 19.837 -14.425 19.425 -14 19 C-13.34 19 -12.68 19 -12 19 C-11.67 18.01 -11.34 17.02 -11 16 C-10.34 15.34 -9.68 14.68 -9 14 C-8.165 11.575 -8.165 11.575 -8 9 C-8.66 8.01 -9.32 7.02 -10 6 C-13.084 5.75 -13.084 5.75 -16 6 C-16.247 7.114 -16.495 8.227 -16.75 9.375 C-18 13 -18 13 -19.812 14.438 C-22.36 15.093 -24.379 15.269 -27 15 C-27.99 14.34 -28.98 13.68 -30 13 C-26.385 2.154 -26.385 2.154 -22 -1.5 C-14.303 -5.348 -7.101 -4.96 0 0 Z " fill="#892D2C" transform="translate(735,937)"/>
<path d="M0 0 C3.517 2.78 5.983 6.427 7.863 10.473 C7.863 11.793 7.863 13.113 7.863 14.473 C5.303 16.146 3.905 16.474 0.801 16.41 C-2.137 15.473 -2.137 15.473 -4.012 13.035 C-5.137 10.473 -5.137 10.473 -5.137 8.473 C-8.9 7.661 -11.324 7.148 -14.949 8.598 C-17.582 10.855 -18.314 12.105 -19.137 15.473 C-19.513 21.216 -19.018 26.018 -17.137 31.473 C-14.632 33.791 -13.071 33.473 -9.637 33.473 C-5.875 32.832 -5.875 32.832 -4.324 29.41 C-2.137 26.473 -2.137 26.473 0.328 26.078 C2.864 26.096 5.342 26.213 7.863 26.473 C7.598 32.844 5.487 37.029 0.863 41.473 C-3.85 44.335 -8.671 45.058 -14.137 44.473 C-19.721 43.093 -23.441 41.013 -27.137 36.473 C-31.394 29.175 -32.569 21.047 -30.453 12.812 C-28.007 6.083 -24.842 1.284 -18.324 -1.902 C-11.726 -3.196 -6.004 -3.357 0 0 Z " fill="#8F2C2C" transform="translate(211.13671875,935.52734375)"/>
<path d="M0 0 C4.29 0 8.58 0 13 0 C13.424 1.284 13.848 2.568 14.285 3.891 C14.877 5.677 15.47 7.464 16.062 9.25 C16.373 10.188 16.684 11.125 17.004 12.092 C18.632 16.98 20.291 21.854 22.023 26.707 C22.344 27.607 22.665 28.507 22.996 29.435 C23.614 31.159 24.237 32.881 24.865 34.601 C25.146 35.383 25.426 36.166 25.715 36.973 C25.964 37.656 26.212 38.339 26.469 39.043 C27 41 27 41 27 45 C23.37 44.67 19.74 44.34 16 44 C14.515 39.545 14.515 39.545 13 35 C8.71 35 4.42 35 0 35 C-0.99 37.97 -1.98 40.94 -3 44 C-6.378 44.845 -8.675 45.108 -12 44 C-12 44.66 -12 45.32 -12 46 C-14 45 -14 45 -15 43 C-14.383 40.764 -14.383 40.764 -13.34 37.969 C-12.961 36.938 -12.582 35.907 -12.191 34.845 C-11.777 33.741 -11.364 32.637 -10.938 31.5 C-10.102 29.231 -9.269 26.962 -8.438 24.691 C-8.024 23.564 -7.61 22.436 -7.184 21.274 C-5.525 16.687 -3.964 12.07 -2.438 7.438 C-2.061 6.297 -2.061 6.297 -1.677 5.133 C-1.116 3.423 -0.557 1.712 0 0 Z M6 12 C5.468 13.601 4.95 15.206 4.438 16.812 C4.147 17.706 3.857 18.599 3.559 19.52 C2.75 22.112 2.75 22.112 4 25 C5.98 25 7.96 25 10 25 C9.717 23.394 9.423 21.79 9.125 20.188 C8.881 18.848 8.881 18.848 8.633 17.48 C8.088 14.773 8.088 14.773 6 12 Z " fill="#892E2C" transform="translate(472,934)"/>
<path d="M0 0 C3.439 2.634 4.683 4.347 5.256 8.632 C5.288 9.491 5.319 10.35 5.352 11.234 C5.41 12.636 5.41 12.636 5.469 14.066 C5.5 15.034 5.531 16.003 5.562 17 C5.621 18.475 5.621 18.475 5.68 19.98 C5.774 22.403 5.859 24.826 5.938 27.25 C6.928 27.58 7.918 27.91 8.938 28.25 C8.608 30.56 8.278 32.87 7.938 35.25 C4.217 36.157 2.153 36.315 -1.562 35.188 C-5.706 34.078 -7.121 34.802 -11.062 36.25 C-16.433 36.931 -20.477 36.342 -25.062 33.25 C-27.456 30.377 -28.023 28.523 -28.562 24.812 C-27.955 20.483 -26.379 17.989 -23.062 15.25 C-20.194 14.025 -17.31 13.434 -14.25 12.875 C-13.068 12.629 -13.068 12.629 -11.861 12.379 C-9.933 11.981 -7.998 11.613 -6.062 11.25 C-6.392 10.26 -6.723 9.27 -7.062 8.25 C-9.373 8.25 -11.683 8.25 -14.062 8.25 C-14.723 9.57 -15.382 10.89 -16.062 12.25 C-19.692 11.92 -23.322 11.59 -27.062 11.25 C-25.461 3.244 -25.461 3.244 -21.688 0.562 C-15.213 -2.675 -6.519 -3.492 0 0 Z M-15.062 21.25 C-16.053 22.735 -16.053 22.735 -17.062 24.25 C-16.733 25.24 -16.402 26.23 -16.062 27.25 C-11.384 27.677 -11.384 27.677 -7.188 26 C-5.957 24.087 -5.447 22.483 -5.062 20.25 C-8.879 18.782 -11.391 19.654 -15.062 21.25 Z " fill="#874033" transform="translate(354.0625,732.75)"/>
<path d="M0 0 C10.89 0 21.78 0 33 0 C33 0.66 33 1.32 33 2 C22.77 2 12.54 2 2 2 C2 67.01 2 132.02 2 199 C10.91 199 19.82 199 29 199 C28.97 195.829 28.939 192.658 28.908 189.391 C28.807 178.814 28.708 168.237 28.609 157.66 C28.549 151.258 28.489 144.857 28.427 138.456 C28.368 132.257 28.31 126.058 28.252 119.86 C28.23 117.516 28.208 115.173 28.185 112.83 C28.031 96.886 27.971 80.944 28 65 C28.66 65 29.32 65 30 65 C30.805 97.983 31.093 130.946 31.062 163.938 C31.061 165.32 31.061 165.32 31.06 166.73 C31.049 178.153 31.028 189.577 31 201 C25.684 202.772 20.001 202.227 14.438 202.25 C12.551 202.281 12.551 202.281 10.627 202.312 C9.429 202.318 8.231 202.323 6.996 202.328 C5.895 202.337 4.793 202.347 3.658 202.356 C2.781 202.239 1.904 202.121 1 202 C-1.474 198.289 -1.276 195.466 -1.247 191.174 C-1.25 190.368 -1.252 189.563 -1.255 188.733 C-1.26 186.024 -1.252 183.316 -1.243 180.607 C-1.244 178.669 -1.246 176.731 -1.249 174.793 C-1.254 169.523 -1.246 164.252 -1.236 158.982 C-1.227 153.475 -1.229 147.968 -1.229 142.46 C-1.228 133.212 -1.219 123.964 -1.206 114.716 C-1.19 104.011 -1.185 93.307 -1.186 82.602 C-1.186 72.319 -1.181 62.036 -1.173 51.753 C-1.169 47.37 -1.167 42.987 -1.167 38.603 C-1.166 33.446 -1.16 28.289 -1.15 23.132 C-1.147 21.235 -1.146 19.338 -1.146 17.441 C-1.147 14.86 -1.141 12.28 -1.134 9.699 C-1.135 8.941 -1.137 8.182 -1.138 7.4 C-1.114 2.228 -1.114 2.228 0 0 Z " fill="#EB3332" transform="translate(638,111)"/>
<path d="M0 0 C3.788 2.084 5.468 5.003 6.797 8.988 C6.991 10.983 7.108 12.985 7.172 14.988 C7.221 16.04 7.27 17.092 7.32 18.176 C6.797 20.988 6.797 20.988 4.5 22.641 C3.608 23.085 2.716 23.53 1.797 23.988 C-0.126 25.708 -0.126 25.708 -1.828 27.551 C-3.922 29.754 -5.639 31.279 -8.203 32.988 C-3.583 32.988 1.037 32.988 5.797 32.988 C8.225 40.417 8.225 40.417 6.797 43.988 C4.817 44.483 4.817 44.483 2.797 44.988 C2.797 44.328 2.797 43.668 2.797 42.988 C1.107 43.232 1.107 43.232 -0.617 43.48 C-5.282 44.062 -9.882 44.136 -14.578 44.113 C-15.384 44.117 -16.189 44.121 -17.02 44.125 C-22.95 44.115 -22.95 44.115 -25.203 42.988 C-25.728 39.987 -25.755 37.842 -24.066 35.23 C-21.268 31.992 -18.249 29.022 -15.137 26.09 C-14.602 25.578 -14.067 25.065 -13.517 24.538 C-12.456 23.527 -11.382 22.531 -10.292 21.552 C-6.232 17.695 -5.181 14.49 -4.203 8.988 C-7.054 8.221 -7.054 8.221 -10.203 7.988 C-12.183 9.052 -12.183 9.052 -13.203 10.988 C-13.203 12.308 -13.203 13.628 -13.203 14.988 C-16.833 15.648 -20.463 16.308 -24.203 16.988 C-25.333 11.339 -24.849 9.928 -22.203 4.988 C-15.668 -2.553 -9.262 -3.787 0 0 Z " fill="#8F302F" transform="translate(805.203125,935.01171875)"/>
<path d="M0 0 C4.18 2.85 7.119 5.981 8.75 10.875 C8.75 14.175 8.75 17.475 8.75 20.875 C1.16 21.205 -6.43 21.535 -14.25 21.875 C-13.59 23.195 -12.93 24.515 -12.25 25.875 C-8.976 27.141 -8.976 27.141 -5.25 26.875 C-2.846 25.45 -1.252 23.877 0.75 21.875 C0.75 22.535 0.75 23.195 0.75 23.875 C1.699 23.854 2.647 23.834 3.625 23.812 C6.75 23.875 6.75 23.875 8.75 24.875 C7.507 29.845 6.05 32.075 1.75 34.875 C-3.704 37.746 -8.974 37.396 -14.812 35.875 C-20.016 34.007 -22.642 30.631 -25.25 25.875 C-26.125 23.312 -26.125 23.312 -26.25 20.875 C-26.374 19.823 -26.498 18.771 -26.625 17.688 C-26.008 11.417 -23.123 5.035 -18.25 0.875 C-12.271 -1.83 -6.17 -2.136 0 0 Z M-11 8 C-13.392 9.993 -13.936 10.841 -14.25 13.875 C-13.26 13.545 -12.27 13.215 -11.25 12.875 C-10.92 13.205 -10.59 13.535 -10.25 13.875 C-7.579 14.016 -4.926 13.917 -2.25 13.875 C-2.356 11.423 -2.356 11.423 -3.25 8.875 C-6.353 6.806 -7.522 6.577 -11 8 Z " fill="#8B4637" transform="translate(418.25,732.125)"/>
<path d="M0 0 C3.375 2.893 4.929 5.933 5.52 10.377 C5.706 13.274 5.809 16.161 5.875 19.062 C5.909 20.06 5.943 21.057 5.979 22.084 C6.322 32.472 6.322 32.472 5.188 35.875 C3.417 35.956 1.646 36.014 -0.125 36.062 C-1.111 36.097 -2.097 36.132 -3.113 36.168 C-5.812 35.875 -5.812 35.875 -7.668 34.363 C-8.235 33.627 -8.235 33.627 -8.812 32.875 C-8.153 31.885 -7.492 30.895 -6.812 29.875 C-6.522 27.765 -6.522 27.765 -6.582 25.461 C-6.584 24.618 -6.586 23.775 -6.588 22.906 C-6.6 22.03 -6.612 21.153 -6.625 20.25 C-6.626 19.366 -6.626 18.481 -6.627 17.57 C-6.282 13.395 -6.282 13.395 -7.812 9.875 C-10.145 9.834 -12.48 9.833 -14.812 9.875 C-16.359 13.34 -17.087 16.17 -17.227 19.957 C-17.265 20.888 -17.304 21.82 -17.344 22.779 C-17.375 23.739 -17.406 24.699 -17.438 25.688 C-17.476 26.667 -17.515 27.646 -17.555 28.654 C-17.649 31.061 -17.734 33.468 -17.812 35.875 C-27.679 36.342 -27.679 36.342 -32 34.562 C-32.598 34.006 -33.196 33.449 -33.812 32.875 C-33.812 31.885 -33.812 30.895 -33.812 29.875 C-32.492 30.205 -31.173 30.535 -29.812 30.875 C-29.317 15.53 -29.317 15.53 -28.812 -0.125 C-18.812 -0.125 -18.812 -0.125 -15.812 0.875 C-15.091 0.566 -14.369 0.256 -13.625 -0.062 C-8.921 -1.84 -4.675 -1.87 0 0 Z " fill="#7C382B" transform="translate(672.8125,660.125)"/>
<path d="M0 0 C3.333 -0.117 6.666 -0.188 10 -0.25 C10.935 -0.284 11.869 -0.317 12.832 -0.352 C19.121 -0.44 23.754 0.406 29 4 C32.068 7.164 32.246 10.122 32.438 14.375 C32.27 19.776 31.359 22.757 28 27 C22.67 31.131 16.422 30.281 10 30 C10.084 30.915 10.168 31.83 10.254 32.773 C10.356 33.962 10.458 35.15 10.562 36.375 C10.719 38.15 10.719 38.15 10.879 39.961 C11 43 11 43 10 45 C3.375 45.125 3.375 45.125 0 44 C-1.039 39.741 -1.145 35.608 -1.133 31.25 C-1.134 30.514 -1.135 29.779 -1.136 29.021 C-1.136 27.471 -1.135 25.921 -1.13 24.371 C-1.125 22.008 -1.13 19.645 -1.137 17.281 C-1.136 15.771 -1.135 14.26 -1.133 12.75 C-1.136 11.697 -1.136 11.697 -1.139 10.622 C-1.122 6.956 -0.87 3.565 0 0 Z M10 9 C10 12.96 10 16.92 10 21 C14.528 21.333 14.528 21.333 19 21 C21.406 19.017 21.406 19.017 22 16 C21.446 12.753 21.446 12.753 20 10 C17.713 8.856 16.407 8.897 13.875 8.938 C11.957 8.968 11.957 8.968 10 9 Z " fill="#8F2F2E" transform="translate(224,934)"/>
<path d="M0 0 C3.577 2.32 4.978 5.577 6.312 9.5 C6.936 13.791 7.052 18.044 7.125 22.375 C7.163 23.515 7.201 24.654 7.24 25.828 C7.473 34.178 7.473 34.178 6.312 36.5 C4.521 36.554 2.729 36.593 0.938 36.625 C-0.06 36.648 -1.058 36.671 -2.086 36.695 C-4.688 36.5 -4.688 36.5 -6.688 34.5 C-6.688 33.51 -6.688 32.52 -6.688 31.5 C-6.027 31.5 -5.368 31.5 -4.688 31.5 C-4.826 28.187 -4.974 24.875 -5.125 21.562 C-5.164 20.617 -5.204 19.671 -5.244 18.697 C-5.286 17.798 -5.328 16.899 -5.371 15.973 C-5.408 15.14 -5.444 14.307 -5.482 13.449 C-5.481 11.497 -5.481 11.497 -6.688 10.5 C-10.146 10.167 -10.146 10.167 -13.688 10.5 C-15.736 13.573 -15.93 14.112 -15.848 17.555 C-15.837 18.335 -15.826 19.114 -15.814 19.918 C-15.793 20.729 -15.772 21.54 -15.75 22.375 C-15.539 32.103 -15.539 32.103 -16.688 36.5 C-18.437 36.581 -20.187 36.639 -21.938 36.688 C-22.912 36.722 -23.887 36.757 -24.891 36.793 C-28.085 36.458 -29.416 35.733 -31.688 33.5 C-31.688 32.84 -31.688 32.18 -31.688 31.5 C-30.697 31.5 -29.707 31.5 -28.688 31.5 C-28.192 16.155 -28.192 16.155 -27.688 0.5 C-17.688 0.5 -17.688 0.5 -14.688 1.5 C-13.945 1.191 -13.202 0.881 -12.438 0.562 C-8.189 -1.079 -4.428 -0.824 0 0 Z M-5.688 32.5 C-4.688 34.5 -4.688 34.5 -4.688 34.5 Z " fill="#80392D" transform="translate(343.6875,659.5)"/>
<path d="M0 0 C4.386 2.571 7.037 5.677 8.688 10.5 C9.498 16.309 9.796 22.246 7 27.562 C2.872 32.806 -0.68 35.284 -7.312 36.5 C-13.641 36.601 -18.2 35.209 -23.312 31.5 C-27.084 27.116 -29.173 22.292 -29.312 16.5 C-27.589 8.928 -24.047 4.019 -17.625 -0.312 C-11.696 -2.438 -5.844 -2.486 0 0 Z M-14.625 9.312 C-17.042 13.878 -17.185 18.424 -16.312 23.5 C-14.799 25.647 -13.952 26.368 -11.375 26.898 C-10.694 26.891 -10.014 26.883 -9.312 26.875 C-8.632 26.883 -7.951 26.89 -7.25 26.898 C-4.653 26.364 -3.824 25.68 -2.312 23.5 C-1.629 19.452 -1.714 15.557 -2.312 11.5 C-4.747 8.439 -6.12 7.762 -9.875 6.938 C-12.462 7.353 -12.462 7.353 -14.625 9.312 Z " fill="#854032" transform="translate(510.3125,732.5)"/>
<path d="M0 0 C2 1 2 1 3 2 C5.211 1.801 7.419 1.566 9.625 1.312 C15.933 0.811 19.141 0.914 24 5 C26.336 8.511 26.394 11.918 26.625 16.062 C26.662 16.723 26.699 17.383 26.738 18.063 C27.542 33.375 27.542 33.375 26 38 C22.7 38 19.4 38 16 38 C15.34 29.42 14.68 20.84 14 12 C14 12.66 14 13.32 14 14 C13.237 13.835 12.474 13.67 11.688 13.5 C8.964 12.909 8.964 12.909 6 13 C4.057 18.584 3.873 23.079 4.375 28.938 C4.432 29.717 4.488 30.496 4.547 31.299 C4.687 33.2 4.842 35.1 5 37 C2.475 38.262 0.688 38.099 -2.125 38.062 C-3.035 38.053 -3.945 38.044 -4.883 38.035 C-5.581 38.024 -6.28 38.012 -7 38 C-7.168 33.355 -7.328 28.711 -7.482 24.066 C-7.536 22.486 -7.591 20.905 -7.648 19.325 C-7.73 17.054 -7.805 14.783 -7.879 12.512 C-7.906 11.805 -7.933 11.099 -7.961 10.371 C-8.055 7.261 -7.994 4.981 -7 2 C-4.69 2 -2.38 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8A4738" transform="translate(464,658)"/>
<path d="M0 0 C3.548 2.838 5.332 5.303 6.188 9.75 C6.279 11.69 6.318 13.632 6.32 15.574 C6.322 17.24 6.322 17.24 6.324 18.939 C6.32 20.094 6.316 21.248 6.312 22.438 C6.318 24.186 6.318 24.186 6.324 25.971 C6.323 27.077 6.322 28.184 6.32 29.324 C6.319 30.341 6.318 31.358 6.317 32.405 C6.188 34.75 6.188 34.75 5.188 35.75 C3.501 35.822 1.812 35.834 0.125 35.812 C-0.794 35.803 -1.713 35.794 -2.66 35.785 C-3.37 35.774 -4.081 35.762 -4.812 35.75 C-4.838 35.033 -4.863 34.317 -4.888 33.579 C-5.004 30.344 -5.127 27.109 -5.25 23.875 C-5.289 22.747 -5.329 21.619 -5.369 20.457 C-5.411 19.381 -5.453 18.304 -5.496 17.195 C-5.533 16.2 -5.569 15.205 -5.607 14.18 C-5.662 11.742 -5.662 11.742 -6.812 9.75 C-9.493 8.506 -10.669 8.703 -13.5 9.625 C-16.354 12.248 -16.539 14.206 -16.77 17.996 C-16.837 22.341 -16.831 26.682 -16.75 31.027 C-16.812 33.75 -16.812 33.75 -17.812 35.75 C-21.112 35.75 -24.413 35.75 -27.812 35.75 C-28.675 32.023 -28.941 28.572 -28.945 24.75 C-28.947 23.616 -28.948 22.481 -28.949 21.312 C-28.945 20.137 -28.941 18.961 -28.938 17.75 C-28.941 16.574 -28.945 15.399 -28.949 14.188 C-28.948 13.053 -28.947 11.919 -28.945 10.75 C-28.944 9.203 -28.944 9.203 -28.942 7.625 C-28.819 4.894 -28.428 2.409 -27.812 -0.25 C-24.513 -0.25 -21.212 -0.25 -17.812 -0.25 C-17.812 0.41 -17.812 1.07 -17.812 1.75 C-16.822 1.255 -15.832 0.76 -14.812 0.25 C-9.751 -1.902 -5.156 -2.292 0 0 Z " fill="#8F4A3B" transform="translate(631.8125,732.25)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C11 8.25 11 16.5 11 25 C12.98 25 14.96 25 17 25 C18.534 28.069 17.55 30.701 17 34 C15.35 34 13.7 34 12 34 C12 37.3 12 40.6 12 44 C7.684 44.762 4.256 45.161 0 44 C0.33 40.7 0.66 37.4 1 34 C-5.27 34 -11.54 34 -18 34 C-18 25.001 -18 25.001 -15.078 21.055 C-14.465 20.205 -13.851 19.356 -13.219 18.48 C-12.569 17.621 -11.919 16.761 -11.25 15.875 C-9.984 14.143 -8.718 12.412 -7.453 10.68 C-6.841 9.875 -6.23 9.07 -5.6 8.241 C-4.226 6.316 -3.259 4.497 -2.234 2.383 C-1.128 1.203 -1.128 1.203 0 0 Z M0 15 C-1.059 16.261 -2.072 17.56 -3.062 18.875 C-3.61 19.594 -4.158 20.314 -4.723 21.055 C-6.183 22.891 -6.183 22.891 -6 25 C-3.69 25 -1.38 25 1 25 C1 21.7 1 18.4 1 15 C0.67 15 0.34 15 0 15 Z " fill="#90312F" transform="translate(832,934)"/>
<path d="M0 0 C0.728 0.024 1.456 0.047 2.207 0.072 C4.53 0.148 6.853 0.232 9.176 0.316 C10.752 0.37 12.328 0.422 13.904 0.475 C17.766 0.604 21.627 0.739 25.488 0.879 C25.488 1.869 25.488 2.859 25.488 3.879 C14.268 3.549 3.048 3.219 -8.512 2.879 C-8.44 7.449 -8.367 12.018 -8.293 16.727 C-8.194 23.465 -8.101 30.203 -8.012 36.941 C-8 37.835 -7.988 38.729 -7.976 39.651 C-7.447 79.997 -7.426 120.342 -7.449 160.691 C-7.45 161.462 -7.45 162.232 -7.451 163.026 C-7.462 182.977 -7.483 202.928 -7.512 222.879 C8.823 223.374 8.823 223.374 25.488 223.879 C25.488 182.629 25.488 141.379 25.488 98.879 C25.818 98.879 26.148 98.879 26.488 98.879 C26.488 140.789 26.488 182.699 26.488 225.879 C14.608 225.879 2.728 225.879 -9.512 225.879 C-11.548 223.843 -10.671 219.588 -10.677 216.791 C-10.682 215.933 -10.688 215.075 -10.694 214.191 C-10.712 211.292 -10.722 208.394 -10.733 205.495 C-10.744 203.426 -10.756 201.356 -10.768 199.287 C-10.799 193.646 -10.824 188.005 -10.848 182.364 C-10.868 177.655 -10.892 172.946 -10.916 168.238 C-10.971 157.121 -11.022 146.004 -11.069 134.886 C-11.118 123.429 -11.175 111.971 -11.235 100.513 C-11.286 90.676 -11.334 80.839 -11.377 71.002 C-11.404 65.128 -11.431 59.253 -11.463 53.379 C-11.492 47.844 -11.516 42.31 -11.537 36.775 C-11.545 34.75 -11.555 32.725 -11.567 30.7 C-11.625 20.71 -11.653 10.821 -10.512 0.879 C-6.91 -0.322 -3.735 -0.143 0 0 Z " fill="#E92A2A" transform="translate(676.51171875,347.12109375)"/>
<path d="M0 0 C2.797 2.355 3.838 3.513 5 7 C5.66 7.66 6.32 8.32 7 9 C4.223 9.926 2.323 10.193 -0.562 10.25 C-1.74 10.289 -1.74 10.289 -2.941 10.328 C-3.621 10.22 -4.3 10.112 -5 10 C-5.66 9.01 -6.32 8.02 -7 7 C-10.584 6.75 -10.584 6.75 -14 7 C-13.67 7.99 -13.34 8.98 -13 10 C-10.342 10.962 -10.342 10.962 -7.188 11.562 C2.034 13.55 2.034 13.55 5 18 C5.456 22.633 5.425 25.061 3.5 29.312 C-0.714 33.843 -5.407 35.111 -11.5 35.438 C-16.291 35.255 -20.123 33.812 -24 31 C-26.716 27.604 -27 26.433 -27 22 C-26.361 21.876 -25.721 21.753 -25.062 21.625 C-24.382 21.419 -23.701 21.212 -23 21 C-22.67 20.34 -22.34 19.68 -22 19 C-22 19.66 -22 20.32 -22 21 C-20.35 20.67 -18.7 20.34 -17 20 C-17.482 19.685 -17.964 19.371 -18.461 19.047 C-25.177 14.496 -25.177 14.496 -26 11 C-25.921 6.674 -25.473 4.591 -22.75 1.188 C-15.485 -4.592 -7.987 -4.301 0 0 Z M-16 20 C-16 21.65 -16 23.3 -16 25 C-11.879 26.827 -11.879 26.827 -7.625 26.125 C-7.089 25.754 -6.553 25.383 -6 25 C-6 24.01 -6 23.02 -6 22 C-9.508 20.396 -12.144 19.78 -16 20 Z " fill="#7C372B" transform="translate(751,662)"/>
<path d="M0 0 C3.399 1.93 5.659 3.905 7.688 7.312 C8.438 10.625 8.438 10.625 8.688 13.312 C6.707 13.312 4.728 13.312 2.688 13.312 C2.688 14.303 2.688 15.293 2.688 16.312 C1.368 16.312 0.048 16.312 -1.312 16.312 C-1.684 15.178 -2.055 14.044 -2.438 12.875 C-3.179 10.931 -3.179 10.931 -4.312 9.312 C-7.396 8.255 -7.396 8.255 -10.312 8.312 C-13.228 11.228 -14.611 13.281 -14.688 17.375 C-14.369 20.715 -14.135 22.579 -12.312 25.312 C-8.292 25.589 -8.292 25.589 -4.312 25.312 C-3.292 23.995 -2.293 22.66 -1.312 21.312 C1.382 20.637 3.897 20.495 6.688 20.312 C7.875 21.938 7.875 21.938 8.688 24.312 C7.129 28.47 5.156 31.537 1.688 34.312 C-4.292 37.031 -10.292 37.144 -16.5 35.25 C-20.879 33.448 -23.135 30.424 -25.312 26.312 C-27.29 20.379 -27.321 13.674 -24.688 7.938 C-19.39 -1.244 -9.674 -4.493 0 0 Z " fill="#884435" transform="translate(381.3125,660.6875)"/>
<path d="M0 0 C3.827 2.524 5.71 6.22 7.125 10.5 C7.125 11.49 7.125 12.48 7.125 13.5 C5.475 13.5 3.825 13.5 2.125 13.5 C1.795 14.16 1.465 14.82 1.125 15.5 C1.125 14.84 1.125 14.18 1.125 13.5 C0.465 13.5 -0.195 13.5 -0.875 13.5 C-0.875 14.16 -0.875 14.82 -0.875 15.5 C-2.875 14.5 -2.875 14.5 -4.688 11.438 C-6.55 8.304 -6.55 8.304 -9.438 7.812 C-12.025 8.309 -12.025 8.309 -13.688 10.5 C-15.504 15.089 -15.49 19.101 -14.25 23.938 C-12.139 26.336 -9.961 26.243 -6.875 26.5 C-6.215 26.5 -5.555 26.5 -4.875 26.5 C-4.215 24.85 -3.555 23.2 -2.875 21.5 C0.425 21.83 3.725 22.16 7.125 22.5 C7.125 25.723 6.623 27.86 4.629 30.422 C0.294 34.643 -3.341 36.527 -9.422 36.695 C-15.401 36.219 -19.298 34.5 -23.312 30 C-27.77 24.462 -27.505 18.339 -26.875 11.5 C-25.167 6.084 -21.342 2.731 -16.875 -0.5 C-11.424 -2.317 -5.255 -2.582 0 0 Z " fill="#8A4536" transform="translate(472.875,732.5)"/>
<path d="M0 0 C0 3.3 0 6.6 0 10 C2.31 10 4.62 10 7 10 C7 12.97 7 15.94 7 19 C5 20 5 20 0 19 C0 24.94 0 30.88 0 37 C2.97 36.505 2.97 36.505 6 36 C8 38 8 38 8.438 40.938 C8 44 8 44 6.062 45.938 C1.2 47.625 -3.605 47.105 -8.277 44.949 C-8.846 44.636 -9.414 44.323 -10 44 C-10 43.34 -10 42.68 -10 42 C-10.99 41.67 -11.98 41.34 -13 41 C-12.34 41 -11.68 41 -11 41 C-11.156 39.819 -11.312 38.638 -11.473 37.422 C-11.67 35.865 -11.866 34.307 -12.062 32.75 C-12.166 31.973 -12.27 31.195 -12.377 30.395 C-12.517 29.263 -12.517 29.263 -12.66 28.109 C-12.749 27.418 -12.838 26.727 -12.93 26.015 C-13.006 23.832 -12.534 22.104 -12 20 C-12.33 19.67 -12.66 19.34 -13 19 C-13.33 19.99 -13.66 20.98 -14 22 C-16.034 20.326 -16.919 19.443 -17.398 16.809 C-17.387 15.635 -17.387 15.635 -17.375 14.438 C-17.383 13.652 -17.39 12.867 -17.398 12.059 C-17.267 11.379 -17.135 10.7 -17 10 C-16.01 9.34 -15.02 8.68 -14 8 C-12.661 5.694 -11.847 3.542 -11 1 C-9.004 0.234 -9.004 0.234 -6.562 -0.25 C-5.759 -0.42 -4.956 -0.59 -4.129 -0.766 C-2 -1 -2 -1 0 0 Z M-12 8 C-11 10 -11 10 -11 10 Z " fill="#7F3B2E" transform="translate(573,650)"/>
<path d="M0 0 C0 3.3 0 6.6 0 10 C0.99 9.01 1.98 8.02 3 7 C5.125 7.125 5.125 7.125 7 8 C8.177 11.53 8.199 14.295 8 18 C7.34 18.99 6.68 19.98 6 21 C6 20.34 6 19.68 6 19 C3.525 19.495 3.525 19.495 1 20 C0.67 25.28 0.34 30.56 0 36 C3.465 36.495 3.465 36.495 7 37 C8 38 8 38 8.062 40.562 C8.042 41.367 8.021 42.171 8 43 C8.66 43.33 9.32 43.66 10 44 C6.065 46.623 2.69 47.064 -2 47 C-6.257 45.502 -8.819 43.949 -11 40 C-11.51 36.758 -11.603 33.528 -11.688 30.25 C-11.722 29.362 -11.756 28.474 -11.791 27.559 C-11.873 25.373 -11.943 23.187 -12 21 C-13.32 20.67 -14.64 20.34 -16 20 C-16.33 17.03 -16.66 14.06 -17 11 C-15.35 10.34 -13.7 9.68 -12 9 C-11.67 6.36 -11.34 3.72 -11 1 C-3.375 -1.125 -3.375 -1.125 0 0 Z " fill="#8E4A3B" transform="translate(426,650)"/>
<path d="M0 0 C11.22 0 22.44 0 34 0 C35 6 35 6 34 9 C29.942 9.984 26.165 10.039 22 10 C22.072 10.958 22.144 11.916 22.219 12.902 C22.933 22.951 23.112 32.929 23 43 C23.66 43.33 24.32 43.66 25 44 C23.397 44.196 21.792 44.381 20.188 44.562 C18.848 44.719 18.848 44.719 17.48 44.879 C15 45 15 45 12 44 C11.005 36.613 10.882 29.384 10.938 21.938 C10.942 20.786 10.947 19.634 10.951 18.447 C10.963 15.631 10.981 12.816 11 10 C7.37 10 3.74 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#913230" transform="translate(660,934)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 2.65 2 4.3 2 6 C0.68 6.33 -0.64 6.66 -2 7 C-2 8.65 -2 10.3 -2 12 C-10.164 13.931 -18.407 14.786 -26.744 15.542 C-29.024 15.752 -31.303 15.979 -33.582 16.207 C-35.034 16.342 -36.486 16.476 -37.938 16.609 C-39.246 16.733 -40.554 16.856 -41.902 16.983 C-42.925 16.989 -43.947 16.994 -45 17 C-45.99 16.01 -45.99 16.01 -47 15 C-43.383 12.589 -40.507 12.598 -36.312 12.438 C-30.016 12.152 -30.016 12.152 -23.75 11.5 C-19.355 10.914 -14.961 10.595 -10.539 10.281 C-8.01 10.149 -8.01 10.149 -6 9 C-6 8.01 -6 7.02 -6 6 C-19.774 6.748 -33.529 7.726 -47.287 8.729 C-51.02 9.001 -54.754 9.27 -58.488 9.539 C-60.888 9.713 -63.288 9.888 -65.688 10.062 C-67.342 10.181 -67.342 10.181 -69.031 10.302 C-73.709 10.646 -78.373 11.019 -83.033 11.555 C-86.908 11.99 -90.605 12.065 -94.5 12 C-100.453 11.943 -106.368 12.095 -112.312 12.438 C-113.036 12.477 -113.759 12.516 -114.504 12.557 C-117.542 12.745 -120.097 13.032 -123 14 C-125.578 14.046 -128.134 14.039 -130.711 13.996 C-137.94 13.936 -145.131 14.123 -152.352 14.473 C-153.832 14.543 -153.832 14.543 -155.342 14.616 C-157.387 14.714 -159.433 14.813 -161.478 14.912 C-181.728 15.88 -201.757 16.158 -222 15 C-222 14.67 -222 14.34 -222 14 C-216.602 13.254 -211.295 12.825 -205.844 12.719 C-205.097 12.7 -204.351 12.682 -203.581 12.663 C-200.427 12.585 -197.272 12.517 -194.118 12.448 C-191.797 12.396 -189.477 12.338 -187.156 12.281 C-186.457 12.268 -185.758 12.254 -185.037 12.24 C-181.747 12.159 -178.566 11.94 -175.304 11.489 C-170.898 10.922 -166.581 10.922 -162.146 10.988 C-152.627 11.055 -143.168 10.691 -133.664 10.195 C-131.967 10.11 -130.27 10.025 -128.573 9.941 C-125.042 9.764 -121.511 9.584 -117.98 9.401 C-113.549 9.172 -109.118 8.949 -104.687 8.729 C-88.188 7.906 -71.693 7.039 -55.211 5.93 C-54.018 5.851 -52.824 5.772 -51.595 5.691 C-50.509 5.615 -49.424 5.539 -48.306 5.46 C-46.903 5.363 -46.903 5.363 -45.472 5.263 C-43.2 5.021 -41.16 4.744 -39 4 C-38.67 3.34 -38.34 2.68 -38 2 C-38 2.99 -38 3.98 -38 5 C-36.541 4.859 -35.083 4.712 -33.625 4.562 C-32.813 4.481 -32.001 4.4 -31.164 4.316 C-28.957 4.109 -28.957 4.109 -27 3 C-25.417 2.979 -23.833 3.009 -22.25 3.062 C-15.248 3.137 -6.36 3.18 0 0 Z M0 3 C1 5 1 5 1 5 Z " fill="#D04B3B" transform="translate(846,864)"/>
<path d="M0 0 C0.763 0.351 1.526 0.701 2.312 1.062 C5.048 2.324 5.048 2.324 8 1 C10.333 0.96 12.667 0.956 15 1 C15 4.96 15 8.92 15 13 C14.01 13 13.02 13 12 13 C9.997 13.314 7.996 13.645 6 14 C3.855 20.4 3.632 26.352 3.521 33.061 C3.357 38.066 2.845 41.699 0 46 C-4.444 48.515 -7.919 49.372 -13 49 C-13 45.7 -13 42.4 -13 39 C-10.525 38.505 -10.525 38.505 -8 38 C-8.005 36.945 -8.01 35.89 -8.016 34.803 C-8.034 30.902 -8.045 27.002 -8.055 23.101 C-8.06 21.411 -8.067 19.72 -8.075 18.029 C-8.088 15.604 -8.093 13.179 -8.098 10.754 C-8.103 9.993 -8.108 9.233 -8.113 8.45 C-8.113 6.633 -8.062 4.816 -8 3 C-7 2 -7 2 -3.438 1.938 C-2.303 1.958 -1.169 1.979 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8E4638" transform="translate(376,730)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 65.67 1.66 131.34 2 199 C-8.143 200.127 -8.143 200.127 -12.84 200.133 C-13.84 200.134 -14.839 200.135 -15.869 200.137 C-17.388 200.131 -17.388 200.131 -18.938 200.125 C-19.977 200.129 -21.017 200.133 -22.088 200.137 C-23.079 200.135 -24.069 200.134 -25.09 200.133 C-25.986 200.132 -26.881 200.131 -27.804 200.129 C-30 200 -30 200 -32 199 C-32.17 173.017 -31.87 147.043 -31.5 121.062 C-31.482 119.803 -31.482 119.803 -31.464 118.518 C-31.314 108.012 -31.159 97.506 -31 87 C-30.67 87 -30.34 87 -30 87 C-30 123.3 -30 159.6 -30 197 C-20.43 197 -10.86 197 -1 197 C-1.005 192.276 -1.01 187.552 -1.016 182.684 C-1.033 166.939 -1.044 151.193 -1.052 135.447 C-1.057 125.916 -1.064 116.385 -1.075 106.854 C-1.086 98.528 -1.092 90.202 -1.094 81.876 C-1.095 77.485 -1.098 73.094 -1.106 68.702 C-1.143 45.783 -0.796 22.906 0 0 Z M-30 63 C-29.67 63 -29.34 63 -29 63 C-29 70.92 -29 78.84 -29 87 C-29.33 87 -29.66 87 -30 87 C-30 79.08 -30 71.16 -30 63 Z " fill="#E72023" transform="translate(558,113)"/>
<path d="M0 0 C1.518 0 3.036 0.006 4.554 0.016 C5.352 0.017 6.15 0.019 6.972 0.02 C9.529 0.026 12.087 0.038 14.644 0.051 C16.374 0.056 18.104 0.061 19.834 0.065 C24.083 0.076 28.332 0.093 32.582 0.114 C32.582 0.444 32.582 0.774 32.582 1.114 C15.257 1.609 15.257 1.609 -2.418 2.114 C-2.748 67.784 -3.078 133.454 -3.418 201.114 C-2.758 200.784 -2.098 200.454 -1.418 200.114 C9.472 200.114 20.362 200.114 31.582 200.114 C31.582 169.094 31.582 138.074 31.582 106.114 C32.242 105.784 32.902 105.454 33.582 105.114 C34.325 109.757 34.708 114.208 34.711 118.909 C34.714 120.222 34.717 121.535 34.721 122.888 C34.719 124.31 34.717 125.731 34.715 127.153 C34.715 128.639 34.716 130.126 34.717 131.613 C34.719 134.731 34.717 137.85 34.712 140.968 C34.707 144.9 34.71 148.832 34.716 152.764 C34.736 169.238 34.634 185.666 33.582 202.114 C29.671 202.713 25.868 203.24 21.91 203.246 C21.08 203.248 20.25 203.249 19.394 203.25 C18.549 203.246 17.703 203.243 16.832 203.239 C16.002 203.242 15.171 203.246 14.316 203.25 C7.966 203.24 1.964 202.694 -4.418 202.114 C-4.913 127.369 -4.913 127.369 -5.418 51.114 C-5.748 51.114 -6.078 51.114 -6.418 51.114 C-6.418 48.804 -6.418 46.494 -6.418 44.114 C-6.088 44.114 -5.758 44.114 -5.418 44.114 C-5.381 42.167 -5.381 42.167 -5.342 40.182 C-5.248 35.387 -5.148 30.592 -5.046 25.797 C-5.002 23.718 -4.961 21.639 -4.921 19.561 C-4.863 16.579 -4.8 13.598 -4.735 10.617 C-4.718 9.682 -4.701 8.747 -4.684 7.783 C-4.664 6.922 -4.644 6.061 -4.624 5.174 C-4.608 4.413 -4.592 3.651 -4.576 2.866 C-4.281 -0.403 -3.38 0.112 0 0 Z " fill="#F0413F" transform="translate(585.418212890625,109.886474609375)"/>
<path d="M0 0 C-0.14 4.459 -0.288 8.917 -0.438 13.375 C-0.477 14.627 -0.516 15.878 -0.557 17.168 C-0.794 24.149 -1.175 31.062 -2 38 C-2.99 38 -3.98 38 -5 38 C-2.525 39.98 -2.525 39.98 0 42 C-0.547 45.375 -1.055 47.082 -3 50 C-6.198 51.21 -7.563 51.179 -10.75 49.875 C-13 48 -13 48 -13.938 45.688 C-14 43 -14 43 -13.002 40.229 C-11.928 36.769 -11.848 34.245 -12.023 30.641 C-12.075 29.442 -12.127 28.243 -12.18 27.008 C-12.244 25.768 -12.309 24.528 -12.375 23.25 C-12.489 20.797 -12.601 18.344 -12.711 15.891 C-12.765 14.723 -12.819 13.556 -12.874 12.354 C-13.041 7.9 -13.058 3.456 -13 -1 C-8.017 -2.215 -4.846 -1.51 0 0 Z " fill="#7D3629" transform="translate(702,718)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 65.01 1 130.02 1 197 C10.24 197 19.48 197 29 197 C28.99 192.956 28.979 188.913 28.968 184.747 C28.935 171.412 28.913 158.076 28.896 144.741 C28.886 136.653 28.872 128.565 28.849 120.477 C28.829 113.429 28.816 106.381 28.812 99.333 C28.809 95.6 28.803 91.867 28.789 88.133 C28.772 83.969 28.772 79.805 28.773 75.641 C28.762 73.778 28.762 73.778 28.751 71.877 C28.754 70.745 28.757 69.614 28.759 68.448 C28.758 67.463 28.756 66.477 28.754 65.462 C29 63 29 63 31 60 C32.17 92.176 31.957 124.312 31.562 156.5 C31.55 157.547 31.537 158.594 31.524 159.673 C31.362 172.782 31.187 185.891 31 199 C26.746 199.025 22.492 199.043 18.238 199.055 C16.789 199.06 15.341 199.067 13.893 199.075 C11.815 199.088 9.738 199.093 7.66 199.098 C6.409 199.103 5.157 199.108 3.867 199.114 C1 199 1 199 0 198 C-0.098 195.445 -0.13 192.918 -0.12 190.363 C-0.121 189.554 -0.122 188.744 -0.123 187.91 C-0.125 185.177 -0.119 182.444 -0.114 179.711 C-0.113 177.76 -0.113 175.809 -0.114 173.858 C-0.114 168.544 -0.108 163.23 -0.101 157.917 C-0.095 152.37 -0.095 146.823 -0.093 141.277 C-0.09 130.765 -0.082 120.254 -0.072 109.742 C-0.061 97.779 -0.055 85.815 -0.05 73.852 C-0.04 49.234 -0.022 24.617 0 0 Z " fill="#E71D1F" transform="translate(417,113)"/>
<path d="M0 0 C0.704 0.318 1.408 0.636 2.134 0.964 C5.167 2.06 7.65 2.377 10.863 2.576 C12.01 2.649 13.158 2.723 14.34 2.799 C15.548 2.865 16.756 2.931 18 3 C19.788 3.101 19.788 3.101 21.613 3.205 C25.475 3.413 29.337 3.604 33.199 3.789 C34.611 3.857 36.022 3.926 37.434 3.994 C40.361 4.135 43.288 4.274 46.215 4.412 C49.969 4.59 53.722 4.774 57.476 4.96 C60.376 5.103 63.276 5.241 66.177 5.377 C67.562 5.443 68.948 5.511 70.333 5.581 C72.257 5.677 74.18 5.766 76.104 5.854 C77.198 5.907 78.291 5.959 79.417 6.013 C81.988 6.191 81.988 6.191 84 5 C85.777 5.228 87.553 5.46 89.327 5.709 C95.384 6.369 101.506 6.373 107.594 6.535 C109.11 6.578 110.627 6.622 112.143 6.665 C115.31 6.756 118.477 6.844 121.644 6.93 C125.685 7.04 129.727 7.154 133.768 7.27 C137.647 7.38 141.527 7.489 145.406 7.598 C146.137 7.618 146.867 7.639 147.62 7.66 C149.036 7.7 150.452 7.739 151.868 7.777 C153.802 7.83 155.736 7.885 157.669 7.941 C160.409 7.99 163.113 7.94 165.85 7.838 C167.409 7.918 167.409 7.918 169 8 C169.99 9.485 169.99 9.485 171 11 C162.032 15.484 143.661 11.761 133.625 11.5 C131.714 11.452 129.802 11.403 127.891 11.355 C123.26 11.239 118.63 11.12 114 11 C113.505 10.01 113.505 10.01 113 9 C111.36 9.244 111.36 9.244 109.688 9.492 C104.456 10.143 99.266 10.146 94 10.125 C93.073 10.129 92.146 10.133 91.191 10.137 C84.374 10.127 77.602 9.766 70.802 9.312 C65.003 8.928 59.201 8.605 53.398 8.281 C52.266 8.218 51.133 8.155 49.965 8.089 C47.62 7.959 45.275 7.828 42.93 7.698 C39.403 7.502 35.875 7.304 32.348 7.105 C30.031 6.976 27.715 6.847 25.398 6.719 C24.272 6.656 23.145 6.593 21.984 6.529 C16.477 6.227 10.969 5.948 5.458 5.719 C4.437 5.676 3.415 5.633 2.363 5.589 C0.444 5.511 -1.476 5.437 -3.395 5.367 C-4.24 5.333 -5.084 5.299 -5.955 5.264 C-6.692 5.237 -7.429 5.209 -8.188 5.182 C-10 5 -10 5 -12 4 C-12 3.34 -12 2.68 -12 2 C-12.66 1.67 -13.32 1.34 -14 1 C-4.59 0.815 -4.59 0.815 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DF644F" transform="translate(204,878)"/>
<path d="M0 0 C1.791 -0.054 3.583 -0.093 5.375 -0.125 C6.373 -0.148 7.37 -0.171 8.398 -0.195 C11 0 11 0 13 2 C12.34 2 11.68 2 11 2 C11 15.86 11 29.72 11 44 C6 45 6 45 3 45 C3 45.66 3 46.32 3 47 C2.34 47 1.68 47 1 47 C0.065 44.195 -0.122 42.518 -0.114 39.62 C-0.113 38.74 -0.113 37.859 -0.113 36.952 C-0.108 36.005 -0.103 35.058 -0.098 34.082 C-0.096 33.11 -0.095 32.137 -0.093 31.135 C-0.088 28.028 -0.075 24.92 -0.062 21.812 C-0.057 19.706 -0.053 17.6 -0.049 15.494 C-0.038 10.329 -0.021 5.165 0 0 Z " fill="#7F2426" transform="translate(339,934)"/>
<path d="M0 0 C1.518 0 3.036 0.006 4.554 0.016 C5.352 0.017 6.15 0.019 6.972 0.02 C9.529 0.026 12.087 0.038 14.644 0.051 C16.374 0.056 18.104 0.061 19.834 0.065 C24.083 0.076 28.332 0.093 32.582 0.114 C33.535 16.986 33.693 33.843 33.707 50.739 C33.709 51.772 33.712 52.806 33.714 53.871 C33.77 82.061 33.77 82.061 32.582 90.114 C31.592 90.609 31.592 90.609 30.582 91.114 C30.582 61.744 30.582 32.374 30.582 2.114 C19.362 2.114 8.142 2.114 -3.418 2.114 C-3.088 67.454 -2.758 132.794 -2.418 200.114 C-3.078 200.114 -3.738 200.114 -4.418 200.114 C-4.442 174.184 -4.459 148.255 -4.47 122.326 C-4.475 110.288 -4.482 98.249 -4.494 86.211 C-4.504 75.722 -4.51 65.234 -4.512 54.745 C-4.514 49.188 -4.517 43.631 -4.524 38.073 C-4.531 32.849 -4.533 27.625 -4.531 22.401 C-4.532 20.477 -4.534 18.554 -4.537 16.63 C-4.542 14.016 -4.541 11.403 -4.538 8.789 C-4.541 8.017 -4.544 7.246 -4.547 6.451 C-4.529 0.15 -4.529 0.15 0 0 Z " fill="#E51316" transform="translate(586.418212890625,110.886474609375)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9.061 1.276 9.121 2.552 9.184 3.867 C9.268 5.536 9.353 7.206 9.438 8.875 C9.477 9.717 9.516 10.559 9.557 11.426 C9.619 12.634 9.619 12.634 9.684 13.867 C9.72 14.611 9.757 15.354 9.795 16.121 C9.9 18.089 9.9 18.089 11 20 C10.867 20.786 10.734 21.573 10.598 22.383 C9.747 27.529 9.633 32.668 9.438 37.875 C9.394 38.945 9.351 40.015 9.307 41.117 C9.201 43.745 9.099 46.372 9 49 C6.03 49 3.06 49 0 49 C0 32.83 0 16.66 0 0 Z " fill="#7C392D" transform="translate(586,646)"/>
<path d="M0 0 C3.981 1.577 5.607 2.41 8 6 C9.405 11.654 9.148 17.462 9.125 23.25 C9.131 24.758 9.131 24.758 9.137 26.297 C9.135 27.256 9.134 28.215 9.133 29.203 C9.132 30.076 9.131 30.949 9.129 31.849 C9 34 9 34 8 36 C0.25 36.125 0.25 36.125 -2 35 C-2.049 33.733 -2.098 32.466 -2.148 31.16 C-2.224 29.461 -2.299 27.762 -2.375 26.062 C-2.406 25.232 -2.437 24.401 -2.469 23.545 C-2.736 17.818 -3.618 12.566 -5 7 C-6.65 7 -8.3 7 -10 7 C-10.66 5.68 -11.32 4.36 -12 3 C-7.887 0.009 -5.049 -0.531 0 0 Z " fill="#874234" transform="translate(570,732)"/>
<path d="M0 0 C3.63 0.33 7.26 0.66 11 1 C12.119 4.357 12.114 6.882 12.098 10.418 C12.094 11.672 12.091 12.925 12.088 14.217 C12.08 15.527 12.071 16.837 12.062 18.188 C12.057 19.523 12.053 20.858 12.049 22.193 C12.037 25.462 12.021 28.731 12 32 C12.66 32 13.32 32 14 32 C13 35 13 35 11 37 C8.398 37.195 8.398 37.195 5.375 37.125 C4.372 37.107 3.369 37.089 2.336 37.07 C1.18 37.036 1.18 37.036 0 37 C-1.324 29.31 -0.894 21.771 -0.562 14 C-0.509 12.639 -0.456 11.279 -0.404 9.918 C-0.275 6.612 -0.14 3.306 0 0 Z " fill="#7A3529" transform="translate(394,659)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C11.528 3.056 11.142 6.246 11.133 9.598 C11.134 10.347 11.135 11.097 11.136 11.869 C11.136 13.453 11.135 15.038 11.13 16.622 C11.125 19.056 11.13 21.49 11.137 23.924 C11.136 25.461 11.135 26.998 11.133 28.535 C11.135 29.267 11.137 30 11.139 30.754 C11.115 35.885 11.115 35.885 10 37 C8.314 37.072 6.625 37.084 4.938 37.062 C4.018 37.053 3.099 37.044 2.152 37.035 C1.442 37.024 0.732 37.012 0 37 C-0.025 36.132 -0.05 35.264 -0.076 34.37 C-0.171 31.153 -0.27 27.936 -0.372 24.72 C-0.416 23.327 -0.457 21.934 -0.497 20.541 C-0.555 18.54 -0.619 16.54 -0.684 14.539 C-0.72 13.335 -0.757 12.13 -0.795 10.889 C-0.811 8.06 -0.811 8.06 -2 6 C-1.394 3.981 -0.731 1.977 0 0 Z " fill="#78362A" transform="translate(439,659)"/>
<path d="M0 0 C1.375 0.017 1.375 0.017 2.777 0.035 C3.696 0.044 4.616 0.053 5.562 0.062 C6.273 0.074 6.983 0.086 7.715 0.098 C8.788 3.593 8.844 6.828 8.848 10.473 C8.849 11.705 8.85 12.937 8.852 14.207 C8.848 15.491 8.844 16.775 8.84 18.098 C8.844 19.382 8.848 20.665 8.852 21.988 C8.85 23.221 8.849 24.453 8.848 25.723 C8.847 26.849 8.845 27.976 8.844 29.137 C8.715 32.098 8.715 32.098 7.715 36.098 C4.415 36.098 1.115 36.098 -2.285 36.098 C-4.039 31.59 -4.485 28.024 -4.297 23.203 C-4.25 21.919 -4.203 20.634 -4.154 19.311 C-4.124 18.65 -4.095 17.99 -4.064 17.31 C-3.972 15.274 -3.898 13.238 -3.826 11.201 C-3.386 0.145 -3.386 0.145 0 0 Z " fill="#763126" transform="translate(588.28515625,731.90234375)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.114 1.691 2.114 1.691 3.25 1.375 C6.378 0.948 7.326 1.503 10 3 C10.025 7.824 10.043 12.648 10.055 17.471 C10.06 19.114 10.067 20.756 10.075 22.398 C10.088 24.754 10.093 27.109 10.098 29.465 C10.103 30.203 10.108 30.942 10.113 31.703 C10.113 33.469 10.062 35.235 10 37 C9 38 9 38 6.715 38.098 C5.34 38.08 5.34 38.08 3.938 38.062 C3.018 38.053 2.099 38.044 1.152 38.035 C0.442 38.024 -0.268 38.012 -1 38 C-1.025 32.91 -1.043 27.819 -1.055 22.729 C-1.06 20.997 -1.067 19.264 -1.075 17.532 C-1.088 15.045 -1.093 12.557 -1.098 10.07 C-1.103 9.293 -1.108 8.516 -1.113 7.715 C-1.114 2.228 -1.114 2.228 0 0 Z " fill="#763227" transform="translate(627,658)"/>
<path d="M0 0 C2.051 0.033 4.102 0.065 6.152 0.098 C6.152 1.418 6.152 2.738 6.152 4.098 C3.512 4.428 0.872 4.758 -1.848 5.098 C-3.104 8.004 -3.848 9.894 -3.848 13.098 C-0.548 12.438 2.752 11.778 6.152 11.098 C6.812 8.788 7.472 6.478 8.152 4.098 C8.482 4.098 8.812 4.098 9.152 4.098 C9.332 8.045 9.246 10.696 7.152 14.098 C8.142 14.428 9.132 14.758 10.152 15.098 C10.606 15.593 11.06 16.088 11.527 16.598 C13.622 18.531 15.374 18.722 18.152 19.098 C18.152 19.428 18.152 19.758 18.152 20.098 C17.26 20.074 16.368 20.051 15.449 20.027 C14.279 20.009 13.108 19.991 11.902 19.973 C10.742 19.949 9.582 19.926 8.387 19.902 C5.238 20.092 3.834 20.607 1.152 22.098 C-4.436 22.548 -8.059 22.327 -12.848 19.098 C-15.242 16.225 -15.808 14.371 -16.348 10.66 C-15.691 5.984 -14.429 4.157 -10.848 1.098 C-8.04 0.644 -5.718 0.859 -2.848 1.098 C-1.848 0.098 -1.848 0.098 0 0 Z " fill="#874033" transform="translate(341.84765625,746.90234375)"/>
<path d="M0 0 C10.89 0 21.78 0 33 0 C33 0.66 33 1.32 33 2 C22.77 2 12.54 2 2 2 C2 52.49 2 102.98 2 155 C1.67 155 1.34 155 1 155 C-0.187 147.537 -0.134 140.139 -0.114 132.598 C-0.113 131.148 -0.113 129.699 -0.114 128.249 C-0.114 124.335 -0.108 120.42 -0.101 116.505 C-0.095 112.407 -0.095 108.308 -0.093 104.209 C-0.09 96.457 -0.082 88.705 -0.072 80.953 C-0.061 72.123 -0.055 63.294 -0.05 54.464 C-0.04 36.309 -0.022 18.155 0 0 Z " fill="#E61E21" transform="translate(638,111)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.754 1.857 2.754 1.857 4.543 1.711 C6.091 1.598 7.639 1.486 9.188 1.375 C9.956 1.311 10.725 1.246 11.518 1.18 C15.881 0.88 19.011 0.905 23 3 C23 3.33 23 3.66 23 4 C11.12 4 -0.76 4 -13 4 C-13 49.54 -13 95.08 -13 142 C-15.013 138.981 -15.268 138.242 -15.409 134.828 C-15.447 133.982 -15.486 133.137 -15.525 132.266 C-15.555 131.344 -15.585 130.422 -15.617 129.471 C-15.654 128.498 -15.692 127.524 -15.731 126.521 C-16.152 114.503 -16.145 102.481 -16.133 90.457 C-16.133 87.985 -16.134 85.512 -16.136 83.04 C-16.137 77.911 -16.135 72.782 -16.13 67.654 C-16.125 61.074 -16.128 54.495 -16.134 47.915 C-16.138 42.834 -16.136 37.753 -16.134 32.672 C-16.133 30.244 -16.134 27.815 -16.136 25.387 C-16.139 22.015 -16.135 18.644 -16.129 15.273 C-16.131 14.271 -16.133 13.269 -16.136 12.237 C-16.133 11.322 -16.13 10.407 -16.127 9.465 C-16.127 8.67 -16.126 7.876 -16.126 7.057 C-16 5 -16 5 -15 2 C-10.269 1.228 -5.785 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E92426" transform="translate(759,346)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C3.136 10.824 3.311 21.353 3.301 32.344 C3.305 34.203 3.309 36.062 3.314 37.922 C3.321 41.804 3.321 45.687 3.316 49.57 C3.31 54.484 3.327 59.397 3.35 64.31 C3.365 68.149 3.366 71.987 3.363 75.826 C3.363 77.634 3.368 79.442 3.378 81.25 C3.553 116.528 3.553 116.528 -3 131 C-4.485 131.99 -4.485 131.99 -6 133 C-5.507 129.151 -4.895 125.39 -4.049 121.602 C-0.989 107.65 -0.396 94.227 -0.391 79.98 C-0.376 77.656 -0.359 75.332 -0.341 73.008 C-0.297 66.967 -0.277 60.926 -0.262 54.884 C-0.242 48.689 -0.2 42.494 -0.16 36.299 C-0.085 24.199 -0.035 12.1 0 0 Z " fill="#EC3131" transform="translate(495,406)"/>
<path d="M0 0 C4.875 0.875 4.875 0.875 6 2 C8.523 2.304 11.027 2.549 13.559 2.754 C14.336 2.819 15.113 2.884 15.914 2.951 C17.574 3.089 19.234 3.224 20.895 3.357 C23.369 3.557 25.842 3.769 28.314 3.982 C39.232 4.889 50.047 5.17 61 5 C61 5.66 61 6.32 61 7 C61.57 6.964 62.141 6.929 62.728 6.892 C65.298 6.735 67.868 6.586 70.438 6.438 C71.784 6.353 71.784 6.353 73.158 6.268 C74.012 6.219 74.866 6.171 75.746 6.121 C76.537 6.074 77.328 6.027 78.142 5.978 C80 6 80 6 81 7 C82.732 7.142 84.469 7.217 86.207 7.265 C87.318 7.299 88.429 7.332 89.574 7.367 C90.785 7.399 91.995 7.432 93.242 7.465 C95.777 7.543 98.313 7.622 100.848 7.701 C104.866 7.821 108.884 7.938 112.903 8.05 C116.766 8.159 120.629 8.28 124.492 8.402 C125.698 8.433 126.905 8.463 128.148 8.494 C129.265 8.531 130.383 8.567 131.535 8.605 C133.012 8.647 133.012 8.647 134.52 8.69 C137 9 137 9 140 11 C138.84 11.025 137.679 11.05 136.483 11.076 C132.175 11.17 127.866 11.27 123.557 11.372 C121.693 11.416 119.829 11.457 117.964 11.497 C115.283 11.555 112.603 11.619 109.922 11.684 C109.089 11.7 108.257 11.717 107.399 11.734 C106.619 11.754 105.838 11.774 105.034 11.795 C104.35 11.81 103.666 11.826 102.962 11.842 C100.961 11.941 100.961 11.941 98.94 12.569 C96.366 13.141 94.555 12.294 92.097 11.516 C87.838 10.468 83.429 10.558 79.062 10.438 C77.018 10.367 74.974 10.296 72.93 10.223 C71.928 10.188 70.926 10.153 69.894 10.118 C64.549 9.9 59.219 9.492 53.887 9.086 C51.058 8.774 51.058 8.774 49 10 C49 9.34 49 8.68 49 8 C48.073 8.084 47.146 8.168 46.191 8.254 C44.994 8.356 43.796 8.458 42.562 8.562 C40.77 8.719 40.77 8.719 38.941 8.879 C37.971 8.919 37 8.959 36 9 C35.505 8.505 35.505 8.505 35 8 C33.362 7.998 31.724 8 30.086 8.023 C26.365 7.982 22.692 7.499 19 7.062 C17.848 6.932 17.848 6.932 16.672 6.799 C12.102 6.274 7.55 5.672 3 5 C3 4.34 3 3.68 3 3 C2.01 2.67 1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CC4838" transform="translate(172,867)"/>
<path d="M0 0 C10.56 0 21.12 0 32 0 C33 2 33 2 32 10 C25.07 10 18.14 10 11 10 C11.33 12.64 11.66 15.28 12 18 C13.132 17.977 14.264 17.954 15.43 17.93 C16.911 17.911 18.393 17.893 19.875 17.875 C20.621 17.858 21.368 17.841 22.137 17.824 C24.092 17.807 26.047 17.897 28 18 C30 20 30 20 32 23 C31.01 23 30.02 23 29 23 C29 24.65 29 26.3 29 28 C23.06 28 17.12 28 11 28 C11 30.64 11 33.28 11 36 C11.66 36 12.32 36 13 36 C13.33 35.34 13.66 34.68 14 34 C14 34.66 14 35.32 14 36 C19.61 36 25.22 36 31 36 C33.25 42.75 33.25 42.75 32 47 C27.498 47.025 22.995 47.043 18.493 47.055 C16.961 47.06 15.43 47.067 13.898 47.075 C11.696 47.088 9.495 47.093 7.293 47.098 C6.608 47.103 5.924 47.108 5.219 47.113 C2.191 47.114 -0.102 46.966 -3 46 C-3 45.34 -3 44.68 -3 44 C-2.34 44 -1.68 44 -1 44 C-1.186 43.443 -1.371 42.886 -1.562 42.312 C-2 40 -2 40 -1.531 37.707 C-0.971 34.854 -0.902 32.279 -0.938 29.375 C-0.947 28.434 -0.956 27.493 -0.965 26.523 C-0.976 25.691 -0.988 24.858 -1 24 C-1 23.01 -1 22.02 -1 21 C-1.012 19.723 -1.023 18.445 -1.035 17.129 C-1.044 15.607 -1.054 14.085 -1.062 12.562 C-1.075 11.417 -1.075 11.417 -1.088 10.248 C-1.091 9.507 -1.094 8.767 -1.098 8.004 C-1.103 7.332 -1.108 6.66 -1.114 5.967 C-1 4 -1 4 0 0 Z M1 1 C1 15.85 1 30.7 1 46 C10.9 46 20.8 46 31 46 C31 43.36 31 40.72 31 38 C24.07 38 17.14 38 10 38 C10 34.04 10 30.08 10 26 C15.94 26 21.88 26 28 26 C28 24.02 28 22.04 28 20 C22.06 20 16.12 20 10 20 C10 16.37 10 12.74 10 9 C16.6 9 23.2 9 30 9 C30 6.36 30 3.72 30 1 C20.43 1 10.86 1 1 1 Z " fill="#C3745F" transform="translate(279,649)"/>
<path d="M0 0 C1.164 0.093 2.328 0.186 3.527 0.281 C4.782 0.39 4.782 0.39 6.062 0.5 C2.319 2.954 -1.322 2.687 -5.625 2.562 C-11.874 2.47 -18.073 2.569 -24.312 2.938 C-25.391 2.997 -25.391 2.997 -26.491 3.057 C-29.513 3.245 -32.05 3.537 -34.938 4.5 C-37.516 4.546 -40.072 4.539 -42.648 4.496 C-49.878 4.436 -57.068 4.623 -64.289 4.973 C-65.276 5.02 -66.263 5.067 -67.279 5.116 C-69.325 5.214 -71.37 5.313 -73.416 5.412 C-93.666 6.38 -113.694 6.658 -133.938 5.5 C-133.938 5.17 -133.938 4.84 -133.938 4.5 C-128.539 3.754 -123.233 3.325 -117.781 3.219 C-117.035 3.2 -116.288 3.182 -115.519 3.163 C-112.364 3.085 -109.21 3.017 -106.055 2.948 C-103.735 2.896 -101.414 2.838 -99.094 2.781 C-98.395 2.768 -97.695 2.754 -96.975 2.74 C-92.921 2.64 -88.986 2.254 -84.97 1.713 C-81.393 1.338 -77.826 1.442 -74.234 1.5 C-63.859 1.581 -53.544 1.074 -43.188 0.5 C-13.644 -1.125 -13.644 -1.125 0 0 Z " fill="#D25843" transform="translate(757.9375,873.5)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 1.65 8 3.3 8 5 C8.99 4.67 9.98 4.34 11 4 C11.688 5.688 11.688 5.688 12 8 C11.526 8.65 11.051 9.299 10.562 9.969 C8.244 14.467 8.43 18.819 8.312 23.812 C8.278 24.792 8.244 25.771 8.209 26.779 C8.127 29.186 8.058 31.593 8 34 C5.36 34 2.72 34 0 34 C0 22.78 0 11.56 0 0 Z " fill="#753126" transform="translate(605,733)"/>
<path d="M0 0 C1.534 3.067 0.548 6.043 0.075 9.332 C-1.242 19.464 -1.255 29.489 -1.195 39.691 C-1.192 41.618 -1.189 43.544 -1.187 45.47 C-1.179 50.479 -1.16 55.488 -1.137 60.497 C-1.117 65.633 -1.108 70.769 -1.098 75.904 C-1.076 85.936 -1.042 95.968 -1 106 C-8.756 94.366 -5.153 73.374 -5.13 60.003 C-5.125 56.616 -5.128 53.229 -5.134 49.842 C-5.138 46.549 -5.135 43.257 -5.133 39.965 C-5.135 38.772 -5.137 37.579 -5.139 36.349 C-5.053 5.053 -5.053 5.053 0 0 Z " fill="#ED3030" transform="translate(288,154)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.846 7.777 2.139 15.445 2.114 23.264 C2.114 24.436 2.114 25.608 2.114 26.816 C2.113 30.652 2.106 34.488 2.098 38.324 C2.096 40.999 2.094 43.674 2.093 46.348 C2.09 52.645 2.082 58.942 2.072 65.238 C2.061 72.414 2.055 79.589 2.05 86.764 C2.04 101.51 2.022 116.255 2 131 C-4.93 131 -11.86 131 -19 131 C-19 130.67 -19 130.34 -19 130 C-13.06 129.67 -7.12 129.34 -1 129 C-0.67 86.43 -0.34 43.86 0 0 Z " fill="#E61D1F" transform="translate(739,181)"/>
<path d="M0 0 C4.875 2.625 4.875 2.625 6 6 C6.083 7.992 6.107 9.987 6.098 11.98 C6.094 13.144 6.091 14.307 6.088 15.506 C6.08 16.721 6.071 17.936 6.062 19.188 C6.058 20.414 6.053 21.641 6.049 22.904 C6.037 25.936 6.021 28.968 6 32 C3.36 32 0.72 32 -2 32 C-2.33 23.42 -2.66 14.84 -3 6 C-3.99 6.33 -4.98 6.66 -6 7 C-5.67 7.66 -5.34 8.32 -5 9 C-7.375 8.688 -7.375 8.688 -10 8 C-10.66 7.01 -11.32 6.02 -12 5 C-10.762 4.918 -9.525 4.835 -8.25 4.75 C-5.011 4.365 -4.121 4.1 -1.5 1.938 C-1.005 1.298 -0.51 0.659 0 0 Z " fill="#733126" transform="translate(483,663)"/>
<path d="M0 0 C2 3 2 3 2 5 C2.66 5.33 3.32 5.66 4 6 C5.734 82.124 5.734 82.124 4.625 110.125 C4.594 110.972 4.563 111.818 4.532 112.691 C4.307 117.117 3.64 120.827 2 125 C1.67 125 1.34 125 1 125 C1.002 123.685 1.004 122.369 1.007 121.014 C1.027 108.555 1.042 96.096 1.052 83.636 C1.057 77.233 1.064 70.829 1.075 64.425 C1.086 58.233 1.092 52.041 1.095 45.849 C1.097 43.499 1.1 41.149 1.106 38.799 C1.135 25.826 0.984 12.939 0 0 Z " fill="#EC292A" transform="translate(636,395)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.939 0.776 0.879 1.552 0.816 2.352 C0.733 3.432 0.649 4.512 0.562 5.625 C0.481 6.669 0.4 7.713 0.316 8.789 C0.27 9.425 0.223 10.061 0.175 10.716 C0.046 12.402 -0.097 14.088 -0.246 15.772 C-1.104 26.178 -1.128 36.526 -1.098 46.961 C-1.096 48.847 -1.094 50.732 -1.093 52.618 C-1.09 57.533 -1.08 62.449 -1.069 67.364 C-1.058 72.399 -1.054 77.434 -1.049 82.469 C-1.038 92.313 -1.021 102.156 -1 112 C-1.33 112 -1.66 112 -2 112 C-3.58 106.385 -4.312 101.338 -4.379 95.52 C-4.396 94.744 -4.413 93.968 -4.43 93.168 C-4.483 90.717 -4.524 88.265 -4.562 85.812 C-4.607 83.35 -4.653 80.887 -4.705 78.424 C-4.737 76.9 -4.764 75.375 -4.786 73.85 C-4.843 70.883 -4.981 68.094 -5.578 65.182 C-6 63 -6 63 -5 60 C-4.914 58.573 -4.882 57.142 -4.886 55.712 C-4.887 54.421 -4.887 54.421 -4.887 53.104 C-4.894 51.716 -4.894 51.716 -4.902 50.301 C-4.904 49.351 -4.905 48.4 -4.907 47.421 C-4.912 44.385 -4.925 41.349 -4.938 38.312 C-4.943 36.255 -4.947 34.197 -4.951 32.139 C-4.962 27.092 -4.979 22.046 -5 17 C-4.34 17 -3.68 17 -3 17 C-3.062 15.866 -3.124 14.731 -3.188 13.562 C-3.214 8.662 -1.76 4.526 0 0 Z " fill="#EB2526" transform="translate(169,149)"/>
<path d="M0 0 C-0.33 8.25 -0.66 16.5 -1 25 C-1.33 25 -1.66 25 -2 25 C-2 16.75 -2 8.5 -2 0 C-5.3 0 -8.6 0 -12 0 C-11.842 0.684 -11.683 1.368 -11.52 2.073 C-10.937 5.355 -10.786 8.501 -10.684 11.832 C-10.642 13.124 -10.6 14.417 -10.557 15.748 C-10.517 17.103 -10.477 18.458 -10.438 19.812 C-10.394 21.187 -10.351 22.561 -10.307 23.936 C-10.201 27.29 -10.099 30.645 -10 34 C-7.69 34.33 -5.38 34.66 -3 35 C-3 32.03 -3 29.06 -3 26 C-2.34 26 -1.68 26 -1 26 C-1.33 29.96 -1.66 33.92 -2 38 C-2.99 38 -3.98 38 -5 38 C-2.525 39.98 -2.525 39.98 0 42 C-0.547 45.375 -1.055 47.082 -3 50 C-6.198 51.21 -7.563 51.179 -10.75 49.875 C-13 48 -13 48 -13.938 45.688 C-14 43 -14 43 -13.002 40.229 C-11.928 36.769 -11.848 34.245 -12.023 30.641 C-12.075 29.442 -12.127 28.243 -12.18 27.008 C-12.244 25.768 -12.309 24.528 -12.375 23.25 C-12.489 20.797 -12.601 18.344 -12.711 15.891 C-12.765 14.723 -12.819 13.556 -12.874 12.354 C-13.041 7.9 -13.058 3.456 -13 -1 C-7.537 -2.332 -5.966 -1.705 0 0 Z " fill="#C78067" transform="translate(702,718)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 33.33 1 66.66 1 101 C0.34 100.67 -0.32 100.34 -1 100 C-3.035 90.706 -2.146 80.447 -2.133 70.961 C-2.133 69.227 -2.134 67.493 -2.136 65.759 C-2.137 62.143 -2.135 58.528 -2.13 54.912 C-2.125 50.298 -2.128 45.684 -2.134 41.07 C-2.138 37.496 -2.136 33.922 -2.134 30.348 C-2.133 28.647 -2.134 26.947 -2.136 25.246 C-2.155 8.191 -2.155 8.191 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#EA2B2B" transform="translate(800,164)"/>
<path d="M0 0 C1.616 0.014 1.616 0.014 3.266 0.027 C4.085 0.039 4.905 0.051 5.75 0.062 C6.245 1.548 6.245 1.548 6.75 3.062 C5.564 3.148 4.379 3.234 3.157 3.322 C-1.25 3.641 -5.658 3.965 -10.065 4.289 C-11.971 4.429 -13.877 4.568 -15.783 4.705 C-18.526 4.903 -21.269 5.106 -24.012 5.309 C-25.285 5.399 -25.285 5.399 -26.585 5.492 C-30.553 5.789 -34.356 6.202 -38.25 7.062 C-39.874 7.107 -41.5 7.102 -43.125 7.062 C-47.46 7.008 -51.748 7.121 -56.078 7.352 C-57.629 7.433 -57.629 7.433 -59.211 7.516 C-61.309 7.63 -63.407 7.752 -65.503 7.883 C-66.96 7.96 -66.96 7.96 -68.445 8.039 C-69.322 8.091 -70.199 8.144 -71.102 8.198 C-73.25 8.062 -73.25 8.062 -75.25 6.062 C-70.036 3.977 -65.298 3.66 -59.766 3.465 C-58.869 3.426 -57.972 3.386 -57.048 3.346 C-54.199 3.223 -51.35 3.11 -48.5 3 C-45.636 2.884 -42.773 2.766 -39.909 2.643 C-38.131 2.567 -36.352 2.496 -34.574 2.429 C-30.275 2.364 -30.275 2.364 -26.25 1.062 C-22.892 1.003 -19.549 1.041 -16.191 1.102 C-10.655 1.163 -5.537 -0.06 0 0 Z " fill="#DD614C" transform="translate(794.25,877.9375)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C2.566 2.836 3.133 2.672 3.716 2.503 C6.086 1.981 8.114 1.903 10.537 1.947 C11.874 1.967 11.874 1.967 13.237 1.988 C14.191 2.009 15.146 2.029 16.129 2.051 C17.608 2.079 17.608 2.079 19.117 2.108 C21.199 2.15 23.281 2.195 25.363 2.242 C28.554 2.312 31.744 2.367 34.936 2.42 C36.958 2.463 38.981 2.506 41.004 2.551 C41.96 2.565 42.916 2.58 43.902 2.595 C45.233 2.63 45.233 2.63 46.592 2.665 C47.763 2.69 47.763 2.69 48.958 2.715 C51 3 51 3 54 5 C31.854 5.719 9.81 6.165 -12.336 5.329 C-21.062 5.001 -29.766 4.924 -38.497 5.037 C-43.772 5.087 -48.779 4.779 -54 4 C-54 3.67 -54 3.34 -54 3 C-46.74 2.67 -39.48 2.34 -32 2 C-32 1.67 -32 1.34 -32 1 C-31.066 1.005 -30.132 1.01 -29.17 1.016 C-25.717 1.033 -22.264 1.045 -18.81 1.055 C-17.314 1.06 -15.817 1.067 -14.32 1.075 C-12.173 1.088 -10.026 1.093 -7.879 1.098 C-6.585 1.103 -5.292 1.108 -3.959 1.114 C-1.113 1.34 -1.113 1.34 0 0 Z " fill="#D65744" transform="translate(342,875)"/>
<path d="M0 0 C0.953 16.873 1.111 33.73 1.125 50.625 C1.127 51.659 1.13 52.692 1.133 53.757 C1.188 81.948 1.188 81.948 0 90 C-0.99 90.495 -0.99 90.495 -2 91 C-2 61.63 -2 32.26 -2 2 C-12.89 2 -23.78 2 -35 2 C-35 1.67 -35 1.34 -35 1 C-30.337 0.857 -25.674 0.715 -21.011 0.573 C-19.424 0.524 -17.837 0.476 -16.25 0.427 C-13.971 0.357 -11.693 0.288 -9.414 0.219 C-8.703 0.197 -7.991 0.175 -7.258 0.152 C-2.228 0 -2.228 0 0 0 Z " fill="#E82223" transform="translate(619,111)"/>
<path d="M0 0 C5.61 0 11.22 0 17 0 C17 6.93 17 13.86 17 21 C15.02 21 13.04 21 11 21 C11 20.01 11 19.02 11 18 C9.35 18 7.7 18 6 18 C6.33 16.35 6.66 14.7 7 13 C7.66 13 8.32 13 9 13 C9.33 10.69 9.66 8.38 10 6 C6.7 6 3.4 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#7E2426" transform="translate(560,956)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.01 1.18 1.021 2.359 1.031 3.575 C1.129 14.754 1.232 25.933 1.339 37.113 C1.394 42.858 1.448 48.604 1.497 54.35 C1.545 59.907 1.598 65.464 1.653 71.02 C1.673 73.128 1.692 75.236 1.709 77.344 C1.807 89.265 2.085 101.111 3 113 C2.34 113 1.68 113 1 113 C-1.407 103.169 -1.275 93.482 -1.23 83.438 C-1.229 81.638 -1.229 79.838 -1.229 78.039 C-1.228 74.276 -1.219 70.515 -1.206 66.752 C-1.189 61.987 -1.185 57.222 -1.186 52.456 C-1.185 48.739 -1.18 45.022 -1.173 41.305 C-1.17 39.552 -1.168 37.798 -1.167 36.044 C-1.156 23.999 -0.738 12.022 0 0 Z " fill="#E92627" transform="translate(522,411)"/>
<path d="M0 0 C4.994 0.403 9.674 1.442 13.566 4.75 C16.974 8.819 15.875 15.486 15.875 20.312 C7.955 19.982 0.035 19.653 -8.125 19.312 C-7.795 17.992 -7.465 16.673 -7.125 15.312 C-2.175 15.312 2.775 15.312 7.875 15.312 C7.875 13.663 7.875 12.013 7.875 10.312 C7.215 10.312 6.555 10.312 5.875 10.312 C4.875 7.312 4.875 7.312 4.875 3.312 C1.575 2.982 -1.725 2.653 -5.125 2.312 C-3.125 0.312 -3.125 0.312 0 0 Z " fill="#7B372B" transform="translate(410.125,731.6875)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.133 8.513 1.263 17.026 1.391 25.539 C1.451 29.495 1.511 33.451 1.573 37.408 C1.884 57.274 2.079 77.131 2 97 C-1.732 91.401 -1.148 84.188 -1.129 77.628 C-1.131 76.833 -1.133 76.039 -1.135 75.22 C-1.139 72.621 -1.136 70.021 -1.133 67.422 C-1.133 65.604 -1.134 63.787 -1.136 61.97 C-1.137 58.174 -1.135 54.378 -1.13 50.582 C-1.125 45.717 -1.128 40.853 -1.134 35.988 C-1.138 32.241 -1.136 28.494 -1.134 24.747 C-1.133 22.953 -1.134 21.159 -1.136 19.364 C-1.139 16.861 -1.135 14.358 -1.129 11.854 C-1.131 11.115 -1.133 10.375 -1.136 9.613 C-1.122 6.267 -1.043 3.199 0 0 Z " fill="#E92626" transform="translate(322,166)"/>
<path d="M0 0 C0.846 -0.021 1.691 -0.041 2.562 -0.062 C3.38 -0.068 4.197 -0.073 5.039 -0.078 C5.776 -0.087 6.513 -0.097 7.272 -0.106 C9.938 0.345 11.345 1.513 13.375 3.25 C12.227 3.272 12.227 3.272 11.055 3.294 C7.536 3.369 4.018 3.466 0.5 3.562 C-0.704 3.585 -1.908 3.608 -3.148 3.631 C-16.907 4.042 -26.944 7.593 -37.438 16.688 C-38.159 17.533 -38.881 18.379 -39.625 19.25 C-40.115 19.822 -40.605 20.395 -41.109 20.984 C-45.16 25.997 -47.513 31.187 -49.625 37.25 C-50.285 37.25 -50.945 37.25 -51.625 37.25 C-52.243 28.648 -46.549 22.439 -41.375 16 C-30.497 4.151 -15.53 0.096 0 0 Z " fill="#EE2F2F" transform="translate(438.625,343.75)"/>
<path d="M0 0 C2.894 5.789 3.166 11.248 3.352 17.633 C3.375 18.371 3.399 19.109 3.423 19.87 C3.497 22.288 3.562 24.706 3.625 27.125 C3.647 27.952 3.67 28.779 3.693 29.631 C4.029 42.537 3.992 55.408 3.625 68.312 C3.604 69.102 3.582 69.892 3.56 70.706 C3.191 83.809 3.191 83.809 2 85 C1.632 87.328 1.298 89.662 1 92 C0.67 92 0.34 92 0 92 C0 61.64 0 31.28 0 0 Z " fill="#EC2A2A" transform="translate(392,163)"/>
<path d="M0 0 C1.077 0.478 1.077 0.478 2.176 0.965 C8.99 3.463 16.874 2.97 24.07 3.211 C26.318 3.286 28.565 3.362 30.812 3.438 C32.505 3.495 32.505 3.495 34.231 3.553 C42.916 3.879 51.412 4.664 60 6 C60 6.33 60 6.66 60 7 C46.807 8.638 33.77 7.46 20.562 6.562 C17.162 6.339 13.762 6.119 10.361 5.9 C8.26 5.765 6.158 5.626 4.057 5.483 C-0.976 5.15 -5.956 4.922 -11 5 C-11.33 4.01 -11.66 3.02 -12 2 C-12.66 1.67 -13.32 1.34 -14 1 C-4.59 0.815 -4.59 0.815 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D94F3F" transform="translate(204,878)"/>
<path d="M0 0 C3.706 0.649 5.317 2.44 7.453 5.43 C7.453 6.09 7.453 6.75 7.453 7.43 C8.113 7.43 8.773 7.43 9.453 7.43 C9.971 9.488 10.467 11.551 10.953 13.617 C11.232 14.766 11.51 15.914 11.797 17.098 C12.663 22.814 12.453 28.648 12.453 34.43 C21.693 34.43 30.933 34.43 40.453 34.43 C40.453 35.09 40.453 35.75 40.453 36.43 C33.304 37.232 26.299 37.504 19.105 37.367 C16.476 37.241 16.476 37.241 14.453 38.43 C12.391 37.68 12.391 37.68 10.453 36.43 C9.216 32.72 9.221 29.173 9.078 25.305 C8.777 18.826 7.956 13.478 5.453 7.43 C5.123 6.44 4.793 5.45 4.453 4.43 C1.168 3.335 -1.284 3.33 -4.734 3.367 C-5.821 3.376 -6.908 3.385 -8.027 3.395 C-8.859 3.406 -9.69 3.418 -10.547 3.43 C-7.459 -0.365 -4.65 -0.122 0 0 Z " fill="#ED2D2E" transform="translate(223.546875,142.5703125)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 2.65 2 4.3 2 6 C0.68 6.33 -0.64 6.66 -2 7 C-2 8.65 -2 10.3 -2 12 C-10.164 13.931 -18.407 14.786 -26.744 15.542 C-29.024 15.752 -31.303 15.979 -33.582 16.207 C-35.034 16.342 -36.486 16.476 -37.938 16.609 C-39.9 16.795 -39.9 16.795 -41.902 16.983 C-42.925 16.989 -43.947 16.994 -45 17 C-45.66 16.34 -46.32 15.68 -47 15 C-43.383 12.589 -40.507 12.598 -36.312 12.438 C-30.016 12.152 -30.016 12.152 -23.75 11.5 C-19.355 10.914 -14.961 10.595 -10.539 10.281 C-8.01 10.149 -8.01 10.149 -6 9 C-6 8.01 -6 7.02 -6 6 C-10.95 5.01 -10.95 5.01 -16 4 C-16 3.67 -16 3.34 -16 3 C-15.013 2.867 -14.025 2.734 -13.008 2.598 C-11.726 2.421 -10.445 2.244 -9.125 2.062 C-7.849 1.888 -6.573 1.714 -5.258 1.535 C-2.124 1.186 -2.124 1.186 0 0 Z M0 3 C1 5 1 5 1 5 Z " fill="#D74E3C" transform="translate(846,864)"/>
<path d="M0 0 C4.277 2.303 6.7 4.099 8.25 8.75 C8.91 9.41 9.57 10.07 10.25 10.75 C7.473 11.676 5.573 11.943 2.688 12 C1.51 12.039 1.51 12.039 0.309 12.078 C-0.371 11.97 -1.05 11.862 -1.75 11.75 C-2.41 10.76 -3.07 9.77 -3.75 8.75 C-7.334 8.5 -7.334 8.5 -10.75 8.75 C-10.42 9.74 -10.09 10.73 -9.75 11.75 C-7.644 12.556 -7.644 12.556 -5.188 12.938 C-4.361 13.096 -3.535 13.255 -2.684 13.418 C-2.046 13.528 -1.407 13.637 -0.75 13.75 C-0.75 14.08 -0.75 14.41 -0.75 14.75 C-1.534 14.894 -2.317 15.039 -3.125 15.188 C-5.714 15.628 -5.714 15.628 -7.75 16.75 C-7.75 15.76 -7.75 14.77 -7.75 13.75 C-10.72 13.255 -10.72 13.255 -13.75 12.75 C-13.75 11.1 -13.75 9.45 -13.75 7.75 C-10.833 5.766 -9.626 5.762 -6 6.125 C-4.928 6.331 -3.855 6.538 -2.75 6.75 C-2.75 6.09 -2.75 5.43 -2.75 4.75 C-4.73 4.75 -6.71 4.75 -8.75 4.75 C-8.75 4.42 -8.75 4.09 -8.75 3.75 C-7.1 3.75 -5.45 3.75 -3.75 3.75 C-3.75 2.76 -3.75 1.77 -3.75 0.75 C-10.185 1.245 -10.185 1.245 -16.75 1.75 C-12.152 -2.848 -5.681 -1.894 0 0 Z " fill="#924A3B" transform="translate(747.75,660.25)"/>
<path d="M0 0 C2.906 0.598 4.985 1.451 7.434 3.117 C8.076 3.554 8.719 3.991 9.381 4.441 C10.06 4.914 10.738 5.387 11.438 5.875 C12.495 6.607 12.495 6.607 13.574 7.354 C19.405 11.454 24.875 15.884 30.234 20.578 C32.391 22.466 34.578 24.271 36.812 26.062 C43.791 32.091 50.256 40.562 54 49 C54.156 51.793 54.156 51.793 54 54 C51.304 52.652 51.046 51.228 49.812 48.5 C40.534 29.539 22.192 16.882 5.029 5.578 C3.319 4.434 1.654 3.223 0 2 C0 1.34 0 0.68 0 0 Z " fill="#ED2D2D" transform="translate(301,431)"/>
<path d="M0 0 C5.232 1.698 9.14 4.195 12 9 C12.75 12.312 12.75 12.312 13 15 C11.02 15 9.04 15 7 15 C7 15.99 7 16.98 7 18 C5.68 18 4.36 18 3 18 C2.629 16.886 2.257 15.773 1.875 14.625 C0.521 11.386 0.238 11.107 -3.188 9.562 C-4.58 9.284 -4.58 9.284 -6 9 C-6 8.67 -6 8.34 -6 8 C-4.35 8 -2.7 8 -1 8 C-1 6.02 -1 4.04 -1 2 C-4.63 2 -8.26 2 -12 2 C-7.923 -0.718 -4.786 -0.627 0 0 Z " fill="#A05947" transform="translate(377,659)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.98 1.67 4.96 1.34 7 1 C7.33 2.65 7.66 4.3 8 6 C10.64 6.33 13.28 6.66 16 7 C13.861 9.139 12.867 9.427 10 10 C9.67 11.32 9.34 12.64 9 14 C12.63 14 16.26 14 20 14 C14.784 16.608 10.36 17.075 4.742 15.277 C0.482 13.336 -2.199 11.503 -4 7 C-4 5.68 -4 4.36 -4 3 C-2.68 2.67 -1.36 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#914C3C" transform="translate(728,681)"/>
<path d="M0 0 C1.162 3.486 0.777 4.341 -0.312 7.75 C-5.019 24.405 -4.612 41.827 -5 59 C-5.022 59.968 -5.044 60.937 -5.066 61.934 C-5.404 76.956 -5.71 91.978 -6 107 C-6.33 107 -6.66 107 -7 107 C-7.047 98.731 -7.082 90.461 -7.104 82.192 C-7.114 78.349 -7.128 74.505 -7.151 70.662 C-7.24 55.413 -7.238 40.209 -6.18 24.988 C-6.11 23.928 -6.04 22.868 -5.969 21.776 C-4.594 4.594 -4.594 4.594 0 0 Z " fill="#E71D1F" transform="translate(768,141)"/>
<path d="M0 0 C1.791 -0.054 3.583 -0.093 5.375 -0.125 C6.373 -0.148 7.37 -0.171 8.398 -0.195 C11 0 11 0 13 2 C12.34 2 11.68 2 11 2 C11 15.86 11 29.72 11 44 C6 45 6 45 3 45 C3 45.66 3 46.32 3 47 C2.34 47 1.68 47 1 47 C0.065 44.195 -0.122 42.518 -0.114 39.62 C-0.113 38.74 -0.113 37.859 -0.113 36.952 C-0.108 36.005 -0.103 35.058 -0.098 34.082 C-0.096 33.11 -0.095 32.137 -0.093 31.135 C-0.088 28.028 -0.075 24.92 -0.062 21.812 C-0.057 19.706 -0.053 17.6 -0.049 15.494 C-0.038 10.329 -0.021 5.165 0 0 Z M2 1 C2 14.86 2 28.72 2 43 C4.64 43 7.28 43 10 43 C10 29.14 10 15.28 10 1 C7.36 1 4.72 1 2 1 Z " fill="#BF564C" transform="translate(339,934)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.094 5.508 2.342 10.966 2.562 16.562 C2.605 17.614 2.647 18.666 2.691 19.75 C3.259 35.825 3.085 51.918 3 68 C-1.374 60.165 -0.137 49.913 -0.098 41.211 C-0.096 40.027 -0.095 38.842 -0.093 37.622 C-0.088 33.873 -0.075 30.124 -0.062 26.375 C-0.057 23.819 -0.053 21.263 -0.049 18.707 C-0.038 12.471 -0.021 6.236 0 0 Z " fill="#EA2728" transform="translate(203,194)"/>
<path d="M0 0 C15.84 0 31.68 0 48 0 C48 1.98 48 3.96 48 6 C47.34 6 46.68 6 46 6 C46 5.01 46 4.02 46 3 C30.82 3 15.64 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EF2F30" transform="translate(417,110)"/>
<path d="M0 0 C0 3.3 0 6.6 0 10 C2.31 10 4.62 10 7 10 C7 12.97 7 15.94 7 19 C5 20 5 20 0 19 C0 24.94 0 30.88 0 37 C-0.33 37 -0.66 37 -1 37 C-1 30.73 -1 24.46 -1 18 C1.31 18 3.62 18 6 18 C5.67 16.02 5.34 14.04 5 12 C2.03 11.505 2.03 11.505 -1 11 C-1 7.7 -1 4.4 -1 1 C-5.455 1.495 -5.455 1.495 -10 2 C-9.979 3.299 -9.959 4.599 -9.938 5.938 C-9.916 7.292 -9.929 8.648 -10 10 C-11 11 -11 11 -13.562 11.062 C-14.367 11.042 -15.171 11.021 -16 11 C-16 13.31 -16 15.62 -16 18 C-14.02 18 -12.04 18 -10 18 C-9.985 18.676 -9.971 19.352 -9.956 20.048 C-9.881 23.116 -9.785 26.183 -9.688 29.25 C-9.665 30.313 -9.642 31.377 -9.619 32.473 C-9.584 33.497 -9.548 34.522 -9.512 35.578 C-9.486 36.521 -9.459 37.463 -9.432 38.435 C-8.937 41.372 -7.952 42.792 -6 45 C-7.32 44.67 -8.64 44.34 -10 44 C-10 43.34 -10 42.68 -10 42 C-10.99 41.67 -11.98 41.34 -13 41 C-12.34 41 -11.68 41 -11 41 C-11.156 39.819 -11.312 38.638 -11.473 37.422 C-11.67 35.865 -11.866 34.307 -12.062 32.75 C-12.166 31.973 -12.27 31.195 -12.377 30.395 C-12.517 29.263 -12.517 29.263 -12.66 28.109 C-12.749 27.418 -12.838 26.727 -12.93 26.015 C-13.006 23.832 -12.534 22.104 -12 20 C-12.33 19.67 -12.66 19.34 -13 19 C-13.33 19.99 -13.66 20.98 -14 22 C-16.034 20.326 -16.919 19.443 -17.398 16.809 C-17.387 15.635 -17.387 15.635 -17.375 14.438 C-17.383 13.652 -17.39 12.867 -17.398 12.059 C-17.267 11.379 -17.135 10.7 -17 10 C-16.01 9.34 -15.02 8.68 -14 8 C-12.661 5.694 -11.847 3.542 -11 1 C-9.004 0.234 -9.004 0.234 -6.562 -0.25 C-5.759 -0.42 -4.956 -0.59 -4.129 -0.766 C-2 -1 -2 -1 0 0 Z M-12 8 C-11 10 -11 10 -11 10 Z " fill="#D38E74" transform="translate(573,650)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.114 1.691 2.114 1.691 3.25 1.375 C6.378 0.948 7.326 1.503 10 3 C10.025 7.824 10.043 12.648 10.055 17.471 C10.06 19.114 10.067 20.756 10.075 22.398 C10.088 24.754 10.093 27.109 10.098 29.465 C10.103 30.203 10.108 30.942 10.113 31.703 C10.113 33.469 10.062 35.235 10 37 C9 38 9 38 6.715 38.098 C5.34 38.08 5.34 38.08 3.938 38.062 C3.018 38.053 2.099 38.044 1.152 38.035 C0.442 38.024 -0.268 38.012 -1 38 C-1.025 32.91 -1.043 27.819 -1.055 22.729 C-1.06 20.997 -1.067 19.264 -1.075 17.532 C-1.088 15.045 -1.093 12.557 -1.098 10.07 C-1.103 9.293 -1.108 8.516 -1.113 7.715 C-1.114 2.228 -1.114 2.228 0 0 Z M0 4 C0 14.56 0 25.12 0 36 C2.64 36 5.28 36 8 36 C8 25.44 8 14.88 8 4 C5.36 4 2.72 4 0 4 Z " fill="#BE7862" transform="translate(627,658)"/>
<path d="M0 0 C4.875 0.875 4.875 0.875 6 2 C7.397 2.204 8.802 2.348 10.209 2.465 C11.104 2.542 11.999 2.618 12.922 2.698 C13.897 2.777 14.873 2.856 15.879 2.938 C16.88 3.022 17.88 3.107 18.911 3.195 C22.128 3.468 25.345 3.734 28.562 4 C31.755 4.267 34.947 4.535 38.139 4.805 C40.128 4.973 42.117 5.139 44.106 5.302 C49.425 5.748 54.709 6.297 60 7 C60 7.33 60 7.66 60 8 C56.7 8 53.4 8 50 8 C49.67 8.66 49.34 9.32 49 10 C49 9.34 49 8.68 49 8 C48.073 8.084 47.146 8.168 46.191 8.254 C44.994 8.356 43.796 8.458 42.562 8.562 C40.77 8.719 40.77 8.719 38.941 8.879 C37.971 8.919 37 8.959 36 9 C35.505 8.505 35.505 8.505 35 8 C33.362 7.998 31.724 8 30.086 8.023 C26.365 7.982 22.692 7.499 19 7.062 C17.848 6.932 17.848 6.932 16.672 6.799 C12.102 6.274 7.55 5.672 3 5 C3 4.34 3 3.68 3 3 C2.01 2.67 1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C23329" transform="translate(172,867)"/>
<path d="M0 0 C1.455 0.014 1.455 0.014 2.939 0.027 C5.314 0.051 7.688 0.084 10.062 0.125 C10.062 0.455 10.062 0.785 10.062 1.125 C8.746 1.223 7.43 1.321 6.074 1.422 C-11.128 2.83 -24.541 6.093 -36.141 19.734 C-38.832 23.316 -40.963 27.111 -42.938 31.125 C-43.268 31.125 -43.597 31.125 -43.938 31.125 C-43.938 23.71 -39.725 18.539 -34.938 13.125 C-34.278 13.125 -33.618 13.125 -32.938 13.125 C-32.938 12.465 -32.938 11.805 -32.938 11.125 C-30.969 9.617 -30.969 9.617 -28.438 8 C-27.602 7.461 -26.767 6.922 -25.906 6.367 C-25.257 5.957 -24.607 5.547 -23.938 5.125 C-24.267 4.465 -24.597 3.805 -24.938 3.125 C-23.927 3.001 -22.916 2.877 -21.875 2.75 C-18.035 2.253 -18.035 2.253 -14.875 1.094 C-9.977 -0.342 -5.06 -0.09 0 0 Z " fill="#EC2828" transform="translate(216.9375,108.875)"/>
<path d="M0 0 C4.792 0.471 6.893 1.608 10.812 4.375 C13.184 7.848 13.207 11.314 13.438 15.438 C13.475 16.098 13.512 16.758 13.55 17.438 C14.354 32.75 14.354 32.75 12.812 37.375 C9.512 37.375 6.212 37.375 2.812 37.375 C2.152 28.465 1.493 19.555 0.812 10.375 C1.473 10.375 2.132 10.375 2.812 10.375 C3.832 13.434 3.928 15.499 3.91 18.699 C3.907 19.727 3.904 20.755 3.9 21.814 C3.892 22.886 3.884 23.958 3.875 25.062 C3.87 26.146 3.866 27.229 3.861 28.346 C3.85 31.022 3.833 33.699 3.812 36.375 C6.452 36.375 9.092 36.375 11.812 36.375 C11.483 26.805 11.152 17.235 10.812 7.375 C8.337 6.385 8.337 6.385 5.812 5.375 C3.482 6.334 3.482 6.334 1.812 9.375 C1.483 8.715 1.152 8.055 0.812 7.375 C1.75 5.25 1.75 5.25 2.812 3.375 C0.173 3.045 -2.467 2.715 -5.188 2.375 C-3.188 0.375 -3.188 0.375 0 0 Z " fill="#C17C65" transform="translate(477.1875,658.625)"/>
<path d="M0 0 C14.52 0 29.04 0 44 0 C44 0.99 44 1.98 44 3 C42.107 3.022 42.107 3.022 40.176 3.044 C35.506 3.102 30.836 3.179 26.166 3.262 C24.142 3.296 22.119 3.324 20.095 3.346 C17.191 3.38 14.287 3.432 11.383 3.488 C10.474 3.495 9.566 3.501 8.63 3.508 C7.789 3.527 6.949 3.547 6.083 3.568 C5.341 3.579 4.599 3.59 3.835 3.601 C1.586 3.867 1.586 3.867 0 7 C0 4.69 0 2.38 0 0 Z " fill="#EE2F30" transform="translate(515,110)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 13.86 9 27.72 9 42 C6.03 42 3.06 42 0 42 C0.33 41.34 0.66 40.68 1 40 C2.98 40.33 4.96 40.66 7 41 C7 28.13 7 15.26 7 2 C5.35 2 3.7 2 2 2 C2 9.59 2 17.18 2 25 C1.67 25 1.34 25 1 25 C0.67 16.75 0.34 8.5 0 0 Z " fill="#812424" transform="translate(384,935)"/>
<path d="M0 0 C1.375 0.017 1.375 0.017 2.777 0.035 C3.696 0.044 4.616 0.053 5.562 0.062 C6.273 0.074 6.983 0.086 7.715 0.098 C8.788 3.593 8.844 6.828 8.848 10.473 C8.849 11.705 8.85 12.937 8.852 14.207 C8.848 15.491 8.844 16.775 8.84 18.098 C8.844 19.382 8.848 20.665 8.852 21.988 C8.85 23.221 8.849 24.453 8.848 25.723 C8.847 26.849 8.845 27.976 8.844 29.137 C8.715 32.098 8.715 32.098 7.715 36.098 C4.415 36.098 1.115 36.098 -2.285 36.098 C-4.039 31.59 -4.485 28.024 -4.297 23.203 C-4.25 21.919 -4.203 20.634 -4.154 19.311 C-4.124 18.65 -4.095 17.99 -4.064 17.31 C-3.972 15.274 -3.898 13.238 -3.826 11.201 C-3.386 0.145 -3.386 0.145 0 0 Z M-1.285 1.098 C-1.285 11.988 -1.285 22.878 -1.285 34.098 C1.355 34.098 3.995 34.098 6.715 34.098 C6.715 23.208 6.715 12.318 6.715 1.098 C4.075 1.098 1.435 1.098 -1.285 1.098 Z " fill="#BE7962" transform="translate(588.28515625,731.90234375)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C-0.28 6.898 -5.56 10.167 -11.793 13.488 C-20.504 17.561 -27.979 19.316 -37.562 19.188 C-38.942 19.187 -38.942 19.187 -40.35 19.186 C-45.006 19.155 -49.421 18.952 -54 18 C-54 17.67 -54 17.34 -54 17 C-53.391 16.98 -52.782 16.96 -52.155 16.94 C-24.455 16.135 -24.455 16.135 -1.625 1.625 C-1.089 1.089 -0.553 0.553 0 0 Z " fill="#EC2A29" transform="translate(479,555)"/>
<path d="M0 0 C4.29 0 8.58 0 13 0 C13.66 2.97 14.32 5.94 15 9 C14.34 8.67 13.68 8.34 13 8 C13 7.01 13 6.02 13 5 C12.34 5 11.68 5 11 5 C10.34 4.34 9.68 3.68 9 3 C8.67 6.96 8.34 10.92 8 15 C7.01 15 6.02 15 5 15 C5 13.35 5 11.7 5 10 C2.083 9.833 2.083 9.833 -1 10 C-1.66 10.66 -2.32 11.32 -3 12 C-2.384 7.687 -1.406 4.219 0 0 Z " fill="#993A36" transform="translate(472,934)"/>
<path d="M0 0 C3.125 0.375 3.125 0.375 5.562 1.938 C7.125 4.375 7.125 4.375 7 8.062 C6.711 9.156 6.423 10.249 6.125 11.375 C3.006 12.934 0.552 12.736 -2.875 12.375 C-4.875 10.188 -4.875 10.188 -5.875 7.375 C-5.565 2.93 -4.705 0.614 0 0 Z " fill="#99503F" transform="translate(398.875,643.625)"/>
<path d="M0 0 C3.438 0.875 3.438 0.875 4.938 2.625 C5.555 5.402 5.291 7.171 4.438 9.875 C2.438 11.875 2.438 11.875 -0.938 12.125 C-4.198 11.9 -5.869 11.599 -8.562 9.875 C-6.445 -0.226 -6.445 -0.226 0 0 Z " fill="#843E2F" transform="translate(445.5625,644.125)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C8.67 5.28 8.34 10.56 8 16 C7.67 16 7.34 16 7 16 C7 11.38 7 6.76 7 2 C5.35 2 3.7 2 2 2 C2 17.18 2 32.36 2 48 C1.34 48 0.68 48 0 48 C0 32.16 0 16.32 0 0 Z " fill="#742D22" transform="translate(586,646)"/>
<path d="M0 0 C4.476 1.212 4.476 1.212 6 3.812 C5.777 7.46 5.616 9.196 3 11.812 C-2.977 12.356 -2.977 12.356 -6 10.75 C-7.634 7.585 -6.853 5.162 -6 1.812 C-3 -0.188 -3 -0.188 0 0 Z " fill="#8D4535" transform="translate(632,644.1875)"/>
<path d="M0 0 C0.388 2.291 0.388 2.291 0.398 5.121 C0.404 6.648 0.404 6.648 0.41 8.205 C0.399 9.272 0.387 10.338 0.375 11.438 C0.387 12.5 0.398 13.563 0.41 14.658 C0.406 15.677 0.402 16.696 0.398 17.746 C0.395 18.681 0.392 19.616 0.388 20.58 C-0.061 23.383 -0.813 24.263 -3 26 C-3.66 25.34 -4.32 24.68 -5 24 C-7.069 23.357 -7.069 23.357 -9 23 C-8.67 22.34 -8.34 21.68 -8 21 C-6.02 21 -4.04 21 -2 21 C-2 14.4 -2 7.8 -2 1 C-7.94 1 -13.88 1 -20 1 C-20 2.98 -20 4.96 -20 7 C-16.7 7 -13.4 7 -10 7 C-10.33 8.32 -10.66 9.64 -11 11 C-11.773 10.567 -11.773 10.567 -12.562 10.125 C-15.624 8.712 -18.749 7.875 -22 7 C-22.043 5 -22.041 3 -22 1 C-20.096 -0.904 -16.735 -0.353 -14.188 -0.5 C-9.403 -0.718 -9.403 -0.718 -4.738 -1.656 C-3 -2 -3 -2 0 0 Z " fill="#BD5349" transform="translate(438,955)"/>
<path d="M0 0 C1.875 1.125 1.875 1.125 3 3 C3.438 6 3.438 6 3 9 C1.5 10.938 1.5 10.938 -1 12 C-3.771 11.937 -6.256 11.503 -9 11 C-9.831 7.676 -10.334 5.224 -9 2 C-5.531 -0.313 -4.078 -0.425 0 0 Z " fill="#A35C47" transform="translate(594,716)"/>
<path d="M0 0 C2.275 2.167 4.236 4.396 6 7 C6 7.66 6 8.32 6 9 C6.99 9.33 7.98 9.66 9 10 C9 10.66 9 11.32 9 12 C9.559 12.278 10.119 12.557 10.695 12.844 C12.257 13.627 13.814 14.419 15.367 15.219 C16.609 15.853 16.609 15.853 17.875 16.5 C18.697 16.923 19.52 17.346 20.367 17.781 C25.228 20.031 29.714 21.247 35.055 21.633 C36.163 21.717 37.272 21.8 38.414 21.887 C39.556 21.965 40.698 22.044 41.875 22.125 C43.623 22.255 43.623 22.255 45.406 22.387 C48.27 22.598 51.135 22.802 54 23 C54 23.33 54 23.66 54 24 C49.854 24.059 45.709 24.094 41.562 24.125 C39.815 24.15 39.815 24.15 38.033 24.176 C31.346 24.213 25.46 23.755 19 22 C19 21.34 19 20.68 19 20 C18.268 19.736 17.536 19.471 16.781 19.199 C9.576 16.092 3.929 10.717 0 4 C0 2.68 0 1.36 0 0 Z " fill="#ED2929" transform="translate(295,288)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.195 2.75 2.381 5.499 2.562 8.25 C2.647 9.416 2.647 9.416 2.732 10.605 C2.781 11.36 2.829 12.114 2.879 12.891 C2.926 13.582 2.973 14.273 3.022 14.985 C2.998 17.203 2.799 18.925 2 21 C-3.225 22.742 -8.846 21.939 -14.306 21.758 C-16.492 21.688 -18.677 21.633 -20.863 21.58 C-22.253 21.537 -23.642 21.494 -25.031 21.449 C-26.295 21.411 -27.559 21.374 -28.861 21.335 C-32 21 -32 21 -35 19 C-23.45 19 -11.9 19 0 19 C0 12.73 0 6.46 0 0 Z " fill="#EB2A2A" transform="translate(864,156)"/>
<path d="M0 0 C-3.044 2.029 -5.68 2.994 -9.125 4.188 C-15.394 6.554 -20.055 9.525 -25 14 C-26.299 15.145 -26.299 15.145 -27.625 16.312 C-30.513 19.581 -32.921 23.077 -35.352 26.695 C-36.779 28.691 -38.207 30.337 -40 32 C-39.562 26.797 -37.691 23.549 -34.5 19.562 C-34.049 18.993 -33.599 18.423 -33.134 17.836 C-25.258 8.157 -13.089 -1.51 0 0 Z " fill="#EE2B2C" transform="translate(287,347)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.026 6.346 2.043 12.691 2.055 19.037 C2.06 21.187 2.067 23.336 2.075 25.486 C2.129 39.029 1.89 52.485 1 66 C0.67 66 0.34 66 0 66 C-0.154 57.69 -0.302 49.38 -0.443 41.07 C-0.509 37.211 -0.576 33.353 -0.648 29.495 C-0.718 25.773 -0.782 22.051 -0.843 18.329 C-0.867 16.908 -0.893 15.486 -0.921 14.064 C-0.959 12.077 -0.991 10.089 -1.022 8.102 C-1.042 6.97 -1.062 5.837 -1.082 4.67 C-1 2 -1 2 0 0 Z " fill="#E41B1E" transform="translate(600,448)"/>
<path d="M0 0 C0 3.96 0 7.92 0 12 C-0.99 12 -1.98 12 -3 12 C-5.336 12.317 -7.669 12.648 -10 13 C-11.446 5.892 -11.446 5.892 -9.5 2 C-6.209 -0.633 -4.107 -0.395 0 0 Z " fill="#7C3529" transform="translate(391,731)"/>
<path d="M0 0 C5.377 0.326 8.439 3.263 12 7 C20.071 16.741 26.209 28.27 26 41 C23.473 38.473 23.183 36.503 22.25 33.062 C18.821 21.659 12.821 9.664 2.223 3.344 C1.489 2.9 0.756 2.457 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EC2B2C" transform="translate(610,353)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.506 1.508 1.013 2.016 0.504 2.539 C-0.137 3.227 -0.777 3.916 -1.438 4.625 C-2.076 5.298 -2.714 5.971 -3.371 6.664 C-5.502 9.363 -5.502 9.363 -6 15 C4.23 15 14.46 15 25 15 C25 12.69 25 10.38 25 8 C18.73 8 12.46 8 6 8 C8 6 8 6 11.879 5.77 C13.44 5.774 15.001 5.788 16.562 5.812 C17.356 5.813 18.149 5.814 18.967 5.814 C24.86 5.86 24.86 5.86 26 7 C26.072 8.519 26.084 10.042 26.062 11.562 C26.053 12.389 26.044 13.215 26.035 14.066 C26.024 14.704 26.012 15.343 26 16 C24.125 17.062 24.125 17.062 22 18 C21.34 17.67 20.68 17.34 20 17 C18.414 16.927 16.826 16.916 15.238 16.938 C14.284 16.945 13.329 16.953 12.346 16.961 C10.343 16.987 8.34 17.013 6.338 17.039 C1.101 17.081 -3.841 16.924 -9 16 C-6.243 5.243 -6.243 5.243 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#C1554B" transform="translate(714,962)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C4.236 14.955 3.107 30.801 3 46 C2.01 46 1.02 46 0 46 C0 30.82 0 15.64 0 0 Z " fill="#EC3030" transform="translate(599,402)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C4.32 2 5.64 2 7 2 C6.67 2.66 6.34 3.32 6 4 C2.7 3.67 -0.6 3.34 -4 3 C-4 16.86 -4 30.72 -4 45 C-1.36 45 1.28 45 4 45 C4 35.76 4 26.52 4 17 C6.365 20.548 6.18 21.807 5.977 25.98 C5.925 27.144 5.873 28.307 5.82 29.506 C5.756 30.721 5.691 31.936 5.625 33.188 C5.568 34.414 5.512 35.641 5.453 36.904 C5.312 39.937 5.16 42.968 5 46 C3.721 46.144 2.442 46.289 1.125 46.438 C-1.257 46.706 -2.845 46.922 -5 48 C-5.927 45.219 -6.122 43.579 -6.114 40.712 C-6.113 39.421 -6.113 39.421 -6.113 38.104 C-6.108 37.179 -6.103 36.254 -6.098 35.301 C-6.096 34.351 -6.095 33.4 -6.093 32.421 C-6.088 29.385 -6.075 26.349 -6.062 23.312 C-6.057 21.255 -6.053 19.197 -6.049 17.139 C-6.038 12.092 -6.021 7.046 -6 2 C-4.02 1.67 -2.04 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C96055" transform="translate(302,932)"/>
<path d="M0 0 C3.63 0.33 7.26 0.66 11 1 C12.119 4.357 12.114 6.882 12.098 10.418 C12.094 11.672 12.091 12.925 12.088 14.217 C12.08 15.527 12.071 16.837 12.062 18.188 C12.057 19.523 12.053 20.858 12.049 22.193 C12.037 25.462 12.021 28.731 12 32 C12.66 32 13.32 32 14 32 C13 35 13 35 11 37 C8.398 37.195 8.398 37.195 5.375 37.125 C4.372 37.107 3.369 37.089 2.336 37.07 C1.18 37.036 1.18 37.036 0 37 C-1.324 29.31 -0.894 21.771 -0.562 14 C-0.509 12.639 -0.456 11.279 -0.404 9.918 C-0.275 6.612 -0.14 3.306 0 0 Z M1 2 C1 13.22 1 24.44 1 36 C3.97 36 6.94 36 10 36 C10 24.78 10 13.56 10 2 C7.03 2 4.06 2 1 2 Z " fill="#D08971" transform="translate(394,659)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8.049 4.378 8.086 8.756 8.11 13.135 C8.12 14.625 8.134 16.115 8.151 17.604 C8.175 19.744 8.186 21.884 8.195 24.023 C8.206 25.312 8.216 26.6 8.227 27.927 C8 31 8 31 6 33 C6 22.77 6 12.54 6 2 C4.35 2 2.7 2 1 2 C1 11.24 1 20.48 1 30 C0.67 30 0.34 30 0 30 C0 20.1 0 10.2 0 0 Z " fill="#7A1E1F" transform="translate(527,935)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.495 0.701 2.99 1.402 3.5 2.125 C8.241 7.577 15.015 11.237 22 13 C25.297 13.225 28.572 13.19 31.875 13.125 C32.754 13.116 33.633 13.107 34.539 13.098 C36.693 13.074 38.846 13.038 41 13 C40 15 40 15 37.32 16.129 C27.625 18.673 17.523 15.842 9 11 C5.708 8.601 2.606 6.145 0 3 C0 2.01 0 1.02 0 0 Z " fill="#ED2C2B" transform="translate(774,297)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 9.57 8 19.14 8 29 C7.67 29 7.34 29 7 29 C7 20.09 7 11.18 7 2 C5.35 2 3.7 2 2 2 C2 11.9 2 21.8 2 32 C0 30 0 30 -0.227 27.019 C-0.217 25.773 -0.206 24.526 -0.195 23.242 C-0.192 22.579 -0.19 21.916 -0.187 21.233 C-0.176 19.113 -0.15 16.994 -0.125 14.875 C-0.115 13.439 -0.106 12.003 -0.098 10.566 C-0.076 7.044 -0.041 3.522 0 0 Z " fill="#7F2021" transform="translate(502,935)"/>
<path d="M0 0 C3.449 3.449 3.128 7.115 3.133 11.824 C3.135 13.49 3.135 13.49 3.137 15.189 C3.133 16.344 3.129 17.498 3.125 18.688 C3.129 19.853 3.133 21.019 3.137 22.221 C3.135 23.327 3.134 24.434 3.133 25.574 C3.132 26.591 3.131 27.608 3.129 28.655 C3 31 3 31 2 32 C-1.3 32 -4.6 32 -8 32 C-8 24.08 -8 16.16 -8 8 C-7.67 8 -7.34 8 -7 8 C-7 15.26 -7 22.52 -7 30 C-4.36 30 -1.72 30 1 30 C0.67 20.1 0.34 10.2 0 0 Z " fill="#BB745E" transform="translate(635,736)"/>
<path d="M0 0 C-2.02 2.02 -3.593 2.864 -6.125 4.125 C-17.81 10.531 -23.557 20.889 -28 33 C-28.33 33 -28.66 33 -29 33 C-29.644 24.431 -24.374 16.679 -19.188 10.125 C-13.824 4.388 -8.114 -0.578 0 0 Z " fill="#ED2A2A" transform="translate(319,113)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1 1.68 1 1 1 C1 3.64 1 6.28 1 9 C1.66 9 2.32 9 3 9 C3.33 8.34 3.66 7.68 4 7 C4 7.66 4 8.32 4 9 C9.61 9 15.22 9 21 9 C23 15 23 15 22 20 C11.44 20 0.88 20 -10 20 C-10 19.67 -10 19.34 -10 19 C0.23 19 10.46 19 21 19 C21 16.36 21 13.72 21 11 C14.07 11 7.14 11 0 11 C0 7.37 0 3.74 0 0 Z " fill="#CC7F67" transform="translate(289,676)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C11.528 3.056 11.142 6.246 11.133 9.598 C11.134 10.347 11.135 11.097 11.136 11.869 C11.136 13.453 11.135 15.038 11.13 16.622 C11.125 19.056 11.13 21.49 11.137 23.924 C11.136 25.461 11.135 26.998 11.133 28.535 C11.135 29.267 11.137 30 11.139 30.754 C11.115 35.885 11.115 35.885 10 37 C8.314 37.072 6.625 37.084 4.938 37.062 C4.018 37.053 3.099 37.044 2.152 37.035 C1.442 37.024 0.732 37.012 0 37 C-0.025 36.132 -0.05 35.264 -0.076 34.37 C-0.171 31.153 -0.27 27.936 -0.372 24.72 C-0.416 23.327 -0.457 21.934 -0.497 20.541 C-0.555 18.54 -0.619 16.54 -0.684 14.539 C-0.72 13.335 -0.757 12.13 -0.795 10.889 C-0.811 8.06 -0.811 8.06 -2 6 C-1.394 3.981 -0.731 1.977 0 0 Z M1 2 C1 13.22 1 24.44 1 36 C3.97 36 6.94 36 10 36 C10.025 31.319 10.043 26.637 10.055 21.956 C10.06 20.362 10.067 18.768 10.075 17.175 C10.088 14.888 10.093 12.602 10.098 10.316 C10.103 9.6 10.108 8.883 10.113 8.145 C10.113 6.429 10.062 4.714 10 3 C9.67 2.67 9.34 2.34 9 2 C6.36 2 3.72 2 1 2 Z " fill="#D08A71" transform="translate(439,659)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.168 3.604 1.334 7.208 1.5 10.812 C1.548 11.837 1.595 12.861 1.645 13.916 C1.69 14.899 1.735 15.882 1.781 16.895 C1.844 18.253 1.844 18.253 1.908 19.64 C2.031 22.794 2.011 24.968 1 28 C-0.77 28.081 -2.541 28.139 -4.312 28.188 C-5.299 28.222 -6.285 28.257 -7.301 28.293 C-10 28 -10 28 -11.855 26.488 C-12.233 25.997 -12.611 25.506 -13 25 C-12.01 23.515 -12.01 23.515 -11 22 C-10.636 19.877 -10.636 19.877 -10.586 17.551 C-10.547 16.703 -10.509 15.856 -10.469 14.982 C-10.438 14.101 -10.407 13.22 -10.375 12.312 C-10.336 11.42 -10.298 10.527 -10.258 9.607 C-10.164 7.405 -10.078 5.203 -10 3 C-7.831 6.254 -7.759 7.134 -7.805 10.887 C-7.811 11.825 -7.818 12.762 -7.824 13.729 C-7.841 14.705 -7.858 15.681 -7.875 16.688 C-7.884 17.676 -7.893 18.664 -7.902 19.682 C-7.926 22.121 -7.959 24.561 -8 27 C-5.36 27 -2.72 27 0 27 C0 18.09 0 9.18 0 0 Z " fill="#C48068" transform="translate(677,668)"/>
<path d="M0 0 C1.023 0.976 2.027 1.974 3 3 C2.448 2.929 1.896 2.857 1.327 2.783 C-6.333 1.896 -13.922 1.886 -21.625 1.938 C-23.561 1.944 -23.561 1.944 -25.535 1.951 C-28.69 1.963 -31.845 1.979 -35 2 C-34.01 1.67 -33.02 1.34 -32 1 C-32 0.34 -32 -0.32 -32 -1 C-22.307 -2.881 -9.413 -3.585 0 0 Z " fill="#EF2E2F" transform="translate(598,346)"/>
<path d="M0 0 C-2.003 2.003 -3.559 2.862 -6.062 4.125 C-15.357 9.251 -21.017 15.743 -26 25 C-26.993 26.671 -27.991 28.339 -29 30 C-30.162 27.189 -30.067 26.163 -28.883 23.301 C-23.103 13.476 -13.193 -1.649 0 0 Z " fill="#ED2B2B" transform="translate(560,349)"/>
<path d="M0 0 C1.098 0.001 2.197 0.003 3.329 0.004 C6.813 0.01 10.297 0.022 13.781 0.035 C16.154 0.04 18.526 0.045 20.898 0.049 C26.693 0.06 32.487 0.077 38.281 0.098 C38.281 0.428 38.281 0.758 38.281 1.098 C37.075 1.158 35.868 1.219 34.625 1.281 C33.01 1.366 31.396 1.45 29.781 1.535 C28.594 1.594 28.594 1.594 27.383 1.654 C26.203 1.717 26.203 1.717 25 1.781 C23.932 1.836 23.932 1.836 22.842 1.892 C20.712 2.063 18.59 2.332 16.473 2.625 C12.45 3.14 8.557 3.124 4.5 2.977 C3.801 2.953 3.102 2.929 2.382 2.905 C0.181 2.829 -2.019 2.745 -4.219 2.66 C-5.724 2.607 -7.229 2.554 -8.734 2.502 C-12.396 2.374 -16.057 2.238 -19.719 2.098 C-19.719 1.768 -19.719 1.438 -19.719 1.098 C-13.112 0.099 -6.675 -0.033 0 0 Z " fill="#C84C3A" transform="translate(643.71875,876.90234375)"/>
<path d="M0 0 C-3.006 3.006 -5.824 2.596 -10 3 C-8.762 4.176 -8.762 4.176 -7.5 5.375 C-5 8 -5 8 -5 10 C0.28 10 5.56 10 11 10 C5.465 12.768 0.166 13.329 -6 13 C-9.125 10.812 -9.125 10.812 -11 8 C-11.598 7.319 -12.196 6.639 -12.812 5.938 C-14 4 -14 4 -14 0 C-8.977 -1.13 -5.021 -0.779 0 0 Z " fill="#A25B48" transform="translate(510,697)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.33 10 0.66 10 1 C7.03 1 4.06 1 1 1 C1 12.22 1 23.44 1 35 C3.97 35 6.94 35 10 35 C10 28.4 10 21.8 10 15 C10.33 15 10.66 15 11 15 C11 21.93 11 28.86 11 36 C0.888 36.366 0.888 36.366 -3.188 34.688 C-3.786 34.131 -4.384 33.574 -5 33 C-5 32.01 -5 31.02 -5 30 C-3.68 30.33 -2.36 30.66 -1 31 C-0.505 15.655 -0.505 15.655 0 0 Z " fill="#D59178" transform="translate(644,660)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.33 10 0.66 10 1 C7.03 1 4.06 1 1 1 C1 12.22 1 23.44 1 35 C3.97 35 6.94 35 10 35 C10 27.74 10 20.48 10 13 C10.33 13 10.66 13 11 13 C11.195 15.895 11.382 18.791 11.562 21.688 C11.647 22.912 11.647 22.912 11.732 24.162 C11.983 28.295 12.111 31.958 11 36 C9.251 36.081 7.5 36.139 5.75 36.188 C4.775 36.222 3.801 36.257 2.797 36.293 C-0.397 35.958 -1.729 35.233 -4 33 C-4 32.34 -4 31.68 -4 31 C-3.01 31 -2.02 31 -1 31 C-0.505 15.655 -0.505 15.655 0 0 Z " fill="#D2886F" transform="translate(316,660)"/>
<path d="M0 0 C6.354 -0.051 12.707 -0.086 19.061 -0.11 C21.223 -0.12 23.386 -0.134 25.548 -0.151 C28.653 -0.175 31.758 -0.186 34.863 -0.195 C35.832 -0.206 36.801 -0.216 37.799 -0.227 C38.7 -0.227 39.601 -0.227 40.529 -0.227 C41.718 -0.234 41.718 -0.234 42.931 -0.241 C45 0 45 0 48 2 C35.13 2.33 22.26 2.66 9 3 C9 2.34 9 1.68 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CF5240" transform="translate(348,878)"/>
<path d="M0 0 C6.665 2.001 8.783 7.209 12 13 C15.084 19.131 17.154 24.324 17.062 31.25 C17.053 32.142 17.044 33.034 17.035 33.953 C17.024 34.629 17.012 35.304 17 36 C15.096 32.601 14.209 29.202 13.25 25.438 C11.264 18.313 8.68 10.663 3.316 5.324 C2 4 2 4 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EB2A2A" transform="translate(254,121)"/>
<path d="M0 0 C2.438 0.75 2.438 0.75 4.438 2.75 C4.688 5.25 4.688 5.25 4.438 7.75 C2.438 9.75 2.438 9.75 -0.562 10 C-3.562 9.75 -3.562 9.75 -5.562 7.75 C-5.875 4.75 -5.875 4.75 -5.562 1.75 C-2.562 -0.25 -2.562 -0.25 0 0 Z " fill="#742E23" transform="translate(695.5625,757.25)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 8.91 1 17.82 1 27 C-1.64 27 -4.28 27 -7 27 C-7 19.08 -7 11.16 -7 3 C-5 5 -5 5 -4.773 7.063 C-4.783 7.89 -4.794 8.718 -4.805 9.57 C-4.811 10.466 -4.818 11.362 -4.824 12.285 C-4.841 13.222 -4.858 14.159 -4.875 15.125 C-4.884 16.07 -4.893 17.015 -4.902 17.988 C-4.926 20.326 -4.959 22.663 -5 25 C-3.35 25 -1.7 25 0 25 C0 16.75 0 8.5 0 0 Z " fill="#6D271D" transform="translate(616,668)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C5.131 9.914 5.237 16.299 5.125 23.5 C5.116 24.609 5.107 25.717 5.098 26.859 C5.074 29.573 5.041 32.286 5 35 C4.34 35 3.68 35 3 35 C2.67 35.99 2.34 36.98 2 38 C2.01 37.325 2.021 36.651 2.032 35.956 C2.074 32.845 2.099 29.735 2.125 26.625 C2.142 25.564 2.159 24.503 2.176 23.41 C2.212 17.43 1.832 11.957 0.433 6.134 C0 4 0 4 0 0 Z " fill="#EB2E2E" transform="translate(355,486)"/>
<path d="M0 0 C4.189 3.461 8.076 7.05 11.875 10.938 C16.286 15.409 20.93 19.301 26 23 C25.01 23.33 24.02 23.66 23 24 C20.871 22.664 20.871 22.664 18.438 20.625 C17.558 19.89 16.679 19.155 15.773 18.398 C2.451 6.877 2.451 6.877 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EC2C2C" transform="translate(253,443)"/>
<path d="M0 0 C0.855 -0.021 1.709 -0.041 2.59 -0.062 C3.416 -0.068 4.242 -0.073 5.094 -0.078 C6.218 -0.092 6.218 -0.092 7.365 -0.106 C10.053 0.342 11.456 1.497 13.5 3.25 C12.815 3.275 12.13 3.3 11.424 3.326 C8.282 3.443 5.141 3.565 2 3.688 C0.384 3.746 0.384 3.746 -1.266 3.807 C-7.067 4.037 -12.753 4.42 -18.5 5.25 C-17.84 4.59 -17.18 3.93 -16.5 3.25 C-15.51 3.25 -14.52 3.25 -13.5 3.25 C-13.5 2.59 -13.5 1.93 -13.5 1.25 C-8.995 -0.023 -4.653 0.028 0 0 Z " fill="#EC2C2D" transform="translate(814.5,107.75)"/>
<path d="M0 0 C2.179 0.674 2.179 0.674 5.812 -0.562 C8.783 -0.232 11.753 0.097 14.812 0.438 C14.812 12.317 14.812 24.197 14.812 36.438 C14.482 36.438 14.153 36.438 13.812 36.438 C13.812 24.888 13.812 13.337 13.812 1.438 C10.842 1.438 7.872 1.438 4.812 1.438 C4.482 2.428 4.153 3.418 3.812 4.438 C3.302 4.11 2.792 3.783 2.266 3.445 C-1.055 2.081 -3.861 2.246 -7.438 2.312 C-8.701 2.331 -9.964 2.349 -11.266 2.367 C-12.23 2.39 -13.194 2.414 -14.188 2.438 C-13.188 0.438 -13.188 0.438 -11.562 -0.188 C-7.679 -0.801 -3.827 -0.984 0 0 Z " fill="#CE8D73" transform="translate(665.1875,731.5625)"/>
<path d="M0 0 C11.195 -0.249 21.925 0.331 33 2 C33 2.33 33 2.66 33 3 C21.555 4.232 10.457 3.749 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#DB5746" transform="translate(231,882)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C2.459 2.859 3.917 2.712 5.375 2.562 C6.187 2.481 6.999 2.4 7.836 2.316 C10.043 2.109 10.043 2.109 12 1 C14.889 1.089 17.744 1.234 20.625 1.438 C21.427 1.49 22.229 1.542 23.055 1.596 C25.037 1.725 27.018 1.862 29 2 C24.651 6.349 15.756 5.054 9.895 5.098 C7.867 5.091 5.84 5.079 3.812 5.062 C2.241 5.056 2.241 5.056 0.639 5.049 C-1.908 5.037 -4.454 5.021 -7 5 C-3.375 2 -3.375 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C53C2E" transform="translate(807,866)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 9.57 1 19.14 1 29 C2.65 29 4.3 29 6 29 C5.988 27.975 5.977 26.95 5.965 25.895 C5.955 24.534 5.946 23.173 5.938 21.812 C5.929 21.139 5.921 20.465 5.912 19.771 C5.894 16.046 6.107 12.621 7 9 C7.33 9 7.66 9 8 9 C8 16.26 8 23.52 8 31 C5.36 31 2.72 31 0 31 C0 20.77 0 10.54 0 0 Z " fill="#6D291F" transform="translate(646,664)"/>
<path d="M0 0 C3.613 2.409 3.789 3.576 5.043 7.637 C5.394 8.772 5.746 9.907 6.107 11.076 C6.464 12.268 6.82 13.46 7.188 14.688 C7.74 16.446 7.74 16.446 8.303 18.24 C10.045 23.966 11.536 29.001 11 35 C8.401 32.401 7.927 30.111 6.836 26.602 C6.639 25.97 6.442 25.339 6.239 24.688 C5.824 23.353 5.413 22.016 5.004 20.679 C4.379 18.637 3.741 16.6 3.102 14.562 C2.7 13.263 2.299 11.964 1.898 10.664 C1.534 9.483 1.17 8.302 0.794 7.084 C0 4 0 4 0 0 Z " fill="#EA2A2A" transform="translate(686,158)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C2.97 2.33 5.94 2.66 9 3 C9 3.33 9 3.66 9 4 C7.35 4.33 5.7 4.66 4 5 C6.31 5.66 8.62 6.32 11 7 C11 7.33 11 7.66 11 8 C6.71 8 2.42 8 -2 8 C-2 7.34 -2 6.68 -2 6 C-2.619 5.732 -3.238 5.464 -3.875 5.188 C-6 4 -6 4 -8 1 C-3.375 -1.125 -3.375 -1.125 0 0 Z " fill="#783326" transform="translate(507,699)"/>
<path d="M0 0 C-0.66 0 -1.32 0 -2 0 C-2 0.66 -2 1.32 -2 2 C-11.33 4.102 -19.565 3.954 -29 3 C-29 2.34 -29 1.68 -29 1 C-29.99 0.67 -30.98 0.34 -32 0 C-31.294 0.005 -30.588 0.01 -29.86 0.016 C-26.657 0.037 -23.453 0.05 -20.25 0.062 C-19.139 0.071 -18.028 0.079 -16.883 0.088 C-15.278 0.093 -15.278 0.093 -13.641 0.098 C-12.656 0.103 -11.672 0.108 -10.657 0.114 C-0.939 -0.302 -0.939 -0.302 0 0 Z " fill="#EE2B2A" transform="translate(237,310)"/>
<path d="M0 0 C2.896 2.768 5.262 5.647 7.625 8.875 C11.2 13.431 14.813 16.136 19.898 18.844 C22 20 22 20 24 22 C16.992 21.851 12.993 18.738 8 14 C0 5.336 0 5.336 0 0 Z " fill="#EC302E" transform="translate(390,546)"/>
<path d="M0 0 C9.9 0.33 19.8 0.66 30 1 C30 1.66 30 2.32 30 3 C4.688 5.125 4.688 5.125 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EA2526" transform="translate(470,309)"/>
<path d="M0 0 C1.923 3.247 2.257 5.678 2.266 9.43 C2.269 10.994 2.269 10.994 2.273 12.59 C2.266 13.674 2.258 14.758 2.25 15.875 C2.258 16.951 2.265 18.028 2.273 19.137 C2.26 24.464 2.18 29.034 0 34 C-0.33 34 -0.66 34 -1 34 C-1.025 29.319 -1.043 24.637 -1.055 19.956 C-1.06 18.362 -1.067 16.768 -1.075 15.175 C-1.088 12.888 -1.093 10.602 -1.098 8.316 C-1.103 7.6 -1.108 6.883 -1.113 6.145 C-1.113 4.429 -1.062 2.714 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E92325" transform="translate(618,215)"/>
<path d="M0 0 C4.501 0.514 6.764 1.841 10 5 C20.642 18.726 20.642 18.726 20 26 C17.683 23.103 16.097 20.214 14.5 16.875 C11.338 10.905 7.279 7.196 2.195 2.898 C1.471 2.272 0.746 1.645 0 1 C0 0.67 0 0.34 0 0 Z " fill="#EC2A2B" transform="translate(334,355)"/>
<path d="M0 0 C0.949 -0.021 1.899 -0.041 2.877 -0.062 C3.788 -0.068 4.698 -0.073 5.637 -0.078 C6.463 -0.087 7.288 -0.097 8.139 -0.106 C10.78 0.327 11.648 1.211 13.312 3.25 C12.698 3.245 12.084 3.24 11.452 3.234 C8.614 3.213 5.776 3.2 2.938 3.188 C1.972 3.179 1.006 3.171 0.012 3.162 C-6.286 3.141 -12.436 3.489 -18.688 4.25 C-16.688 1.25 -16.688 1.25 -14.23 0.578 C-9.48 0.138 -4.769 0.026 0 0 Z " fill="#EF2F2F" transform="translate(340.6875,107.75)"/>
<path d="M0 0 C3.3 0.66 6.6 1.32 10 2 C10 2.66 10 3.32 10 4 C10.66 4.66 11.32 5.32 12 6 C8.579 8.281 7.318 8.219 3.312 8.125 C1.822 8.098 1.822 8.098 0.301 8.07 C-0.458 8.047 -1.218 8.024 -2 8 C-2.33 7.34 -2.66 6.68 -3 6 C-2.01 5.67 -1.02 5.34 0 5 C0 3.35 0 1.7 0 0 Z " fill="#763024" transform="translate(655,757)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.196 4.125 1.381 8.25 1.562 12.375 C1.619 13.551 1.675 14.726 1.732 15.938 C1.781 17.059 1.829 18.18 1.879 19.336 C1.926 20.373 1.973 21.41 2.022 22.478 C2 25 2 25 1 27 C-0.791 27.054 -2.583 27.093 -4.375 27.125 C-5.373 27.148 -6.37 27.171 -7.398 27.195 C-10 27 -10 27 -12 25 C-12 24.01 -12 23.02 -12 22 C-11.34 22 -10.68 22 -10 22 C-10 15.4 -10 8.8 -10 2 C-9.67 2 -9.34 2 -9 2 C-8.67 9.59 -8.34 17.18 -8 25 C-5.36 25.33 -2.72 25.66 0 26 C0 17.42 0 8.84 0 0 Z M-11 23 C-10 25 -10 25 -10 25 Z " fill="#CD856C" transform="translate(349,669)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 14.85 1 29.7 1 45 C-0.32 45 -1.64 45 -3 45 C-3 44.34 -3 43.68 -3 43 C-2.34 43 -1.68 43 -1 43 C-1.186 42.443 -1.371 41.886 -1.562 41.312 C-2 39 -2 39 -1.531 36.707 C-0.971 33.854 -0.902 31.279 -0.938 28.375 C-0.947 27.434 -0.956 26.493 -0.965 25.523 C-0.976 24.691 -0.988 23.858 -1 23 C-1 22.01 -1 21.02 -1 20 C-1.012 18.711 -1.023 17.422 -1.035 16.094 C-1.044 14.563 -1.054 13.031 -1.062 11.5 C-1.075 10.342 -1.075 10.342 -1.088 9.16 C-1.102 5.846 -1.055 3.166 0 0 Z " fill="#C16954" transform="translate(279,650)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C4.785 2.245 4.785 2.245 8.031 2.125 C9.234 2.11 10.437 2.094 11.676 2.078 C13.569 2.039 13.569 2.039 15.5 2 C17.989 1.949 20.479 1.907 22.969 1.875 C24.631 1.84 24.631 1.84 26.326 1.805 C29 2 29 2 31 4 C27.703 5.099 25.27 5.114 21.801 5.098 C20.592 5.094 19.384 5.091 18.139 5.088 C16.876 5.08 15.613 5.071 14.312 5.062 C12.401 5.056 12.401 5.056 10.451 5.049 C7.301 5.037 4.15 5.021 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#EA2527" transform="translate(638,308)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C6.67 2.97 6.34 5.94 6 9 C3.36 9 0.72 9 -2 9 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#832626" transform="translate(462,968)"/>
<path d="M0 0 C10.23 0.33 20.46 0.66 31 1 C28 4 28 4 25.627 4.356 C24.711 4.347 23.795 4.338 22.852 4.328 C21.846 4.323 20.841 4.318 19.805 4.312 C18.231 4.282 18.231 4.282 16.625 4.25 C15.583 4.245 14.542 4.24 13.469 4.234 C8.831 4.187 4.538 4.023 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EC2F2F" transform="translate(707,309)"/>
<path d="M0 0 C2.402 2.225 4.039 4.53 5.75 7.312 C8.237 11.124 10.839 13.891 14.387 16.715 C16.414 18.33 18.205 20.134 20 22 C13.333 21.25 9.468 16.658 5 12 C1.761 7.882 0 5.323 0 0 Z " fill="#EC2C2B" transform="translate(174,284)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C4.01 1.495 4.01 1.495 3 2 C3 3.98 3 5.96 3 8 C3.66 8 4.32 8 5 8 C5 9.65 5 11.3 5 13 C0.05 13 -4.9 13 -10 13 C-9.67 11.68 -9.34 10.36 -9 9 C-9 9.99 -9 10.98 -9 12 C-8.67 11.34 -8.34 10.68 -8 10 C-7.01 10 -6.02 10 -5 10 C-5 10.66 -5 11.32 -5 12 C-2.36 12 0.28 12 3 12 C2.34 10.35 1.68 8.7 1 7 C-1.31 6.67 -3.62 6.34 -6 6 C-5.67 5.34 -5.34 4.68 -5 4 C-4.34 3.856 -3.68 3.711 -3 3.562 C-2.01 3.284 -2.01 3.284 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#A7614E" transform="translate(413,734)"/>
<path d="M0 0 C2.189 2.074 3.871 4.198 5.562 6.688 C9.262 11.735 14.01 15.292 19 19 C17.344 19.785 17.344 19.785 15 20 C12.344 18.121 12.344 18.121 9.5 15.438 C8.562 14.57 7.623 13.702 6.656 12.809 C0 5.771 0 5.771 0 0 Z " fill="#EC2E2D" transform="translate(246,545)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3.33 2.32 3.66 3 4 C2.719 5.648 2.425 7.293 2.125 8.938 C1.963 9.854 1.8 10.771 1.633 11.715 C1.424 12.469 1.215 13.223 1 14 C0.01 14.495 0.01 14.495 -1 15 C-0.67 13.02 -0.34 11.04 0 9 C-2.64 9 -5.28 9 -8 9 C-5.381 7.254 -3.962 6.613 -1 6 C-1 5.34 -1 4.68 -1 4 C-2.98 4 -4.96 4 -7 4 C-7.33 4.66 -7.66 5.32 -8 6 C-11 5 -11 5 -14 3 C-7.07 1.515 -7.07 1.515 0 0 Z " fill="#A55F4B" transform="translate(348,744)"/>
<path d="M0 0 C2 1.375 2 1.375 4 3 C4 3.66 4 4.32 4 5 C0.29 7.473 -1.611 7.385 -6 7 C-7.338 6.007 -8.672 5.006 -10 4 C-12.676 3.441 -15.267 3.228 -18 3 C-18.33 4.98 -18.66 6.96 -19 9 C-19.33 9 -19.66 9 -20 9 C-20.381 6.674 -20.713 4.339 -21 2 C-20.67 1.67 -20.34 1.34 -20 1 C-18.206 1.038 -16.413 1.155 -14.625 1.312 C-13.159 1.43 -13.159 1.43 -11.664 1.551 C-9 2 -9 2 -6 4 C-2.875 4.167 -2.875 4.167 0 4 C0 2.68 0 1.36 0 0 Z " fill="#BF7861" transform="translate(704,695)"/>
<path d="M0 0 C0.57 0.236 1.14 0.472 1.727 0.715 C1.014 1.249 0.301 1.782 -0.434 2.332 C-7.039 7.47 -11.906 12.575 -16.273 19.715 C-16.922 17.949 -16.922 17.949 -17.273 15.715 C-14.721 10.433 -6.234 -0.782 0 0 Z " fill="#EF2D2D" transform="translate(788.2734375,115.28515625)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.546 1.866 1.092 2.732 0.625 3.625 C-0.863 6.715 -1.988 9.729 -3 13 C-3.33 13 -3.66 13 -4 13 C-4 16.96 -4 20.92 -4 25 C-4.99 24.34 -5.98 23.68 -7 23 C-7.125 20.062 -7.125 20.062 -7 17 C-7.33 16.01 -7.66 15.02 -8 14 C-7.34 14 -6.68 14 -6 14 C-5.938 12.804 -5.876 11.607 -5.812 10.375 C-5.251 5.593 -3.31 3.395 0 0 Z " fill="#A86350" transform="translate(690,662)"/>
<path d="M0 0 C2.68 1.002 3.808 1.67 5.266 4.18 C7.795 11.104 9.019 17.618 8 25 C7.34 25.66 6.68 26.32 6 27 C5.836 25.407 5.836 25.407 5.668 23.781 C4.862 16.438 4.065 9.797 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#ED2B2C" transform="translate(312,496)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 5.94 1.66 11.88 2 18 C3.732 17.979 5.465 17.959 7.25 17.938 C13.543 17.93 19.741 18.379 26 19 C26 19.33 26 19.66 26 20 C17.42 20 8.84 20 0 20 C0 13.4 0 6.8 0 0 Z " fill="#E71C1F" transform="translate(836,292)"/>
<path d="M0 0 C5.355 0.325 8.404 3.338 12 7 C14.008 9.283 15.431 11.351 17 14 C16.625 16.211 16.625 16.211 16 18 C15.515 17.348 15.031 16.695 14.531 16.023 C10.595 10.891 6.878 6.735 1.469 3.156 C0.984 2.775 0.499 2.393 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EC2C2D" transform="translate(841,115)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.457 2.447 2.915 2.895 2.355 3.355 C1.282 4.262 1.282 4.262 0.188 5.188 C-0.874 6.075 -0.874 6.075 -1.957 6.98 C-6.025 11.002 -7.387 14.706 -8.188 20.312 C-8.425 21.915 -8.425 21.915 -8.668 23.551 C-8.778 24.359 -8.887 25.167 -9 26 C-9.33 26 -9.66 26 -10 26 C-10.621 17.89 -9.698 10.527 -4.398 4.039 C-2.998 2.625 -1.506 1.301 0 0 Z " fill="#AB3C37" transform="translate(190,935)"/>
<path d="M0 0 C1.741 0.007 1.741 0.007 3.518 0.014 C6.366 0.025 9.214 0.042 12.062 0.062 C12.062 0.722 12.062 1.383 12.062 2.062 C1.251 3.192 -9.124 2.859 -19.938 2.062 C-19.938 1.732 -19.938 1.403 -19.938 1.062 C-13.297 -0.161 -6.735 -0.05 0 0 Z " fill="#C94434" transform="translate(769.9375,871.9375)"/>
<path d="M0 0 C1.938 1.25 1.938 1.25 2.75 3.375 C2.812 3.994 2.874 4.612 2.938 5.25 C2.174 4.92 1.411 4.59 0.625 4.25 C-5.447 2.603 -11.986 2.467 -18.062 4.25 C-17.25 2.312 -17.25 2.312 -16.062 0.25 C-10.859 -1.485 -5.305 -1.299 0 0 Z " fill="#96513F" transform="translate(509.0625,731.75)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C14.67 0.66 14.34 1.32 14 2 C16.31 2.33 18.62 2.66 21 3 C21 3.33 21 3.66 21 4 C13.74 4 6.48 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#6E2A1F" transform="translate(403,747)"/>
<path d="M0 0 C1.145 0.278 1.145 0.278 2.312 0.562 C5.09 1.228 5.09 1.228 8 0 C8.66 0.66 9.32 1.32 10 2 C12.52 2.371 12.52 2.371 15.445 2.414 C17.06 2.472 17.06 2.472 18.707 2.531 C20.399 2.578 20.399 2.578 22.125 2.625 C23.828 2.683 23.828 2.683 25.566 2.742 C28.377 2.837 31.188 2.922 34 3 C34 3.33 34 3.66 34 4 C22.78 4 11.56 4 0 4 C0 2.667 0 1.333 0 0 Z " fill="#DC6A52" transform="translate(585,877)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.121 0.75 1.242 1.5 1.367 2.273 C2.156 13.325 2.156 13.325 6 23 C6.041 24.666 6.043 26.334 6 28 C1.808 24.183 0.238 19.664 -0.129 14.017 C-0.255 9.34 -0.155 4.674 0 0 Z " fill="#EA2828" transform="translate(562,505)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 14.52 2 29.04 2 44 C1.67 44 1.34 44 1 44 C1 32.12 1 20.24 1 8 C0.34 7.67 -0.32 7.34 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#E72627" transform="translate(600,448)"/>
<path d="M0 0 C3.366 1.122 4.062 2.004 6.25 4.688 C9.544 8.586 13.05 11.775 17 15 C16.67 15.99 16.34 16.98 16 18 C9.587 13.478 4.231 8.679 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EE2F2F" transform="translate(283,413)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C3.481 5.208 3.96 7.416 4.438 9.625 C4.704 10.855 4.971 12.085 5.246 13.352 C5.495 14.556 5.744 15.76 6 17 C6.238 18.101 6.477 19.202 6.723 20.336 C6.814 21.215 6.906 22.094 7 23 C6.34 23.66 5.68 24.32 5 25 C4.154 21.962 3.326 18.919 2.5 15.875 C2.262 15.019 2.023 14.163 1.777 13.281 C0.555 8.743 -0.311 4.748 0 0 Z " fill="#EB2E2E" transform="translate(285,257)"/>
<path d="M0 0 C0.66 0.681 1.32 1.361 2 2.062 C7.55 7.581 13.033 12.371 20 16 C20 16.33 20 16.66 20 17 C14.541 17.47 12.292 15.363 8.227 12.012 C5.201 9.278 2.188 6.466 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EC2D2C" transform="translate(535,551)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.63 1.66 7.26 2 11 C1.34 11 0.68 11 0 11 C-0.056 12.013 -0.056 12.013 -0.113 13.047 C-0.212 14.385 -0.212 14.385 -0.312 15.75 C-0.371 16.632 -0.429 17.513 -0.488 18.422 C-1.064 21.323 -2.076 22.792 -4 25 C-3.55 16.397 -2.139 8.341 0 0 Z " fill="#EB2A29" transform="translate(507,268)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 14.52 1 29.04 1 44 C0.67 44 0.34 44 0 44 C-0.038 42.403 -0.038 42.403 -0.076 40.775 C-0.17 36.814 -0.27 32.854 -0.372 28.894 C-0.416 27.182 -0.457 25.47 -0.497 23.758 C-0.555 21.293 -0.619 18.828 -0.684 16.363 C-0.7 15.602 -0.717 14.84 -0.734 14.055 C-0.819 11.001 -0.966 8.15 -1.645 5.165 C-2 3 -2 3 0 0 Z " fill="#F13837" transform="translate(165,209)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-2.957 1.388 -5.915 1.761 -8.875 2.125 C-10.138 2.293 -10.138 2.293 -11.426 2.465 C-12.231 2.562 -13.037 2.658 -13.867 2.758 C-14.611 2.852 -15.354 2.946 -16.121 3.043 C-18 3 -18 3 -20 1 C-14.166 -2.889 -6.325 -2.906 0 0 Z " fill="#DA5643" transform="translate(819,878)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-9.9 1 -19.8 1 -30 1 C-30 0.67 -30 0.34 -30 0 C-24.818 -0.799 -19.669 -1.339 -14.438 -1.688 C-13.709 -1.747 -12.98 -1.807 -12.229 -1.869 C-7.674 -2.168 -4.305 -1.775 0 0 Z " fill="#E92122" transform="translate(782,349)"/>
<path d="M0 0 C0.799 0.009 1.598 0.018 2.422 0.027 C3.025 0.039 3.628 0.051 4.25 0.062 C4.58 2.043 4.91 4.023 5.25 6.062 C0.865 5.542 -3.423 4.953 -7.75 4.062 C-8.41 4.062 -9.07 4.062 -9.75 4.062 C-9.42 3.072 -9.09 2.082 -8.75 1.062 C-5.859 -0.057 -3.072 -0.045 0 0 Z " fill="#732C21" transform="translate(739.75,660.9375)"/>
<path d="M0 0 C1 3 1 3 -0.438 6 C-3.587 9.687 -6.261 11.121 -11 12 C-13.494 11.944 -15.55 11.562 -18 11 C-18 10.34 -18 9.68 -18 9 C-17.359 8.997 -16.719 8.995 -16.059 8.992 C-10.004 8.721 -6.673 7.683 -2.277 3.473 C-0.901 2.009 -0.901 2.009 0 0 Z " fill="#EB2C2C" transform="translate(827,270)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2 7.625 2 7.625 2 11 C1.021 12.021 0.02 13.021 -1 14 C-1.33 14.99 -1.66 15.98 -2 17 C-2.99 16.67 -3.98 16.34 -5 16 C-4.67 15.34 -4.34 14.68 -4 14 C-4.949 14.041 -5.898 14.082 -6.875 14.125 C-10 14 -10 14 -12 12 C-11.374 11.963 -10.747 11.925 -10.102 11.887 C-9.284 11.821 -8.467 11.755 -7.625 11.688 C-6.813 11.629 -6.001 11.571 -5.164 11.512 C-2.621 11.17 -2.621 11.17 -1 8 C-0.549 5.951 -0.549 5.951 -0.375 3.812 C-0.251 2.554 -0.128 1.296 0 0 Z " fill="#9A5443" transform="translate(669,745)"/>
<path d="M0 0 C3.5 0.125 3.5 0.125 4.5 1.125 C4.541 3.125 4.543 5.125 4.5 7.125 C4.17 6.465 3.84 5.805 3.5 5.125 C1.19 5.455 -1.12 5.785 -3.5 6.125 C-3.882 4.469 -4.214 2.8 -4.5 1.125 C-3.5 0.125 -3.5 0.125 0 0 Z " fill="#813B2D" transform="translate(591.5,718.875)"/>
<path d="M0 0 C9.739 -0.123 19.291 0.162 29 1 C25.682 2.99 23.2 3.236 19.359 3.195 C18.289 3.189 17.22 3.182 16.117 3.176 C15.006 3.159 13.895 3.142 12.75 3.125 C11.623 3.116 10.497 3.107 9.336 3.098 C6.557 3.074 3.779 3.041 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D85C45" transform="translate(735,873)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C3.78 4.39 5.919 4.185 9 4 C11.302 3.102 11.302 3.102 13 2 C12.67 2.99 12.34 3.98 12 5 C8.873 6.564 5.42 6.4 2 6 C1.01 5.34 0.02 4.68 -1 4 C-1 3.34 -1 2.68 -1 2 C-3.64 2 -6.28 2 -9 2 C-8.526 2.949 -8.051 3.898 -7.562 4.875 C-6.708 6.583 -5.854 8.292 -5 10 C-6.32 9.34 -7.64 8.68 -9 8 C-9 7.34 -9 6.68 -9 6 C-9.66 6 -10.32 6 -11 6 C-11.33 5.01 -11.66 4.02 -12 3 C-9.727 0.727 -9.076 0.68 -6 0.375 C-5.299 0.3 -4.597 0.225 -3.875 0.148 C-2 0 -2 0 0 0 Z " fill="#C17A63" transform="translate(655,767)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.66 0.67 4.32 0.34 5 0 C7 -0.04 9 -0.043 11 0 C10.649 0.598 10.299 1.196 9.938 1.812 C8.667 4.108 8.667 4.108 10 7 C7.36 7 4.72 7 2 7 C0.539 4.353 0 3.106 0 0 Z " fill="#8F4A3A" transform="translate(615,734)"/>
<path d="M0 0 C5.04 4.102 6.599 12.191 7.254 18.41 C7.25 20.312 7.25 20.312 7 23 C4.087 20.087 3.479 16.49 2.375 12.625 C2.146 11.857 1.916 11.088 1.68 10.297 C0.61 6.617 0 3.898 0 0 Z " fill="#EA2A2A" transform="translate(714,388)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.508 2.686 2.006 5.374 2.5 8.062 C2.643 8.815 2.786 9.567 2.934 10.342 C3.776 14.977 4.15 19.289 4 24 C3.67 23.01 3.34 22.02 3 21 C2.34 21 1.68 21 1 21 C-0.327 14.032 -0.088 7.07 0 0 Z " fill="#EA2A2B" transform="translate(458,230)"/>
<path d="M0 0 C1.181 3.544 0.711 4.628 -0.375 8.125 C-1.833 13.124 -2.575 17.816 -3 23 C-5.379 19.432 -5.495 17.112 -5 13 C-2.825 2.825 -2.825 2.825 0 0 Z " fill="#EC2B2C" transform="translate(768,141)"/>
<path d="M0 0 C3.167 -0.029 6.333 -0.047 9.5 -0.062 C10.387 -0.071 11.274 -0.079 12.188 -0.088 C17.558 -0.108 22.686 0.212 28 1 C28 1.33 28 1.66 28 2 C24.7 2 21.4 2 18 2 C17.67 2.66 17.34 3.32 17 4 C17 3.34 17 2.68 17 2 C16.108 2.107 15.216 2.214 14.297 2.324 C13.126 2.444 11.956 2.564 10.75 2.688 C9.59 2.815 8.43 2.943 7.234 3.074 C3.675 2.993 2.518 2.425 0 0 Z " fill="#C03529" transform="translate(204,873)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.839 8.493 0.38 14.695 -4 22 C-5.437 18.197 -4.501 15.854 -3.125 12.125 C-1.643 8.073 -0.6 4.278 0 0 Z " fill="#EC2A2B" transform="translate(456,512)"/>
<path d="M0 0 C1.896 3.791 2.794 6.885 3.688 11 C3.959 12.217 4.231 13.434 4.512 14.688 C4.948 17.644 5.024 19.251 4 22 C4 21.34 4 20.68 4 20 C3.34 20 2.68 20 2 20 C1.876 18.866 1.752 17.731 1.625 16.562 C1.419 15.387 1.212 14.211 1 13 C0.34 12.67 -0.32 12.34 -1 12 C-0.67 8.04 -0.34 4.08 0 0 Z " fill="#EB2929" transform="translate(381,514)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.461 8.608 -0.272 16.615 -2 25 C-2.33 25 -2.66 25 -3 25 C-3.185 10.219 -3.185 10.219 -2 4 C-1.34 4 -0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#EA2728" transform="translate(495,223)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 9.9 8 19.8 8 30 C7.67 30 7.34 30 7 30 C6.67 21.09 6.34 12.18 6 3 C4.35 2.67 2.7 2.34 1 2 C0.67 2.66 0.34 3.32 0 4 C0 2.68 0 1.36 0 0 Z " fill="#7A1A1C" transform="translate(610,935)"/>
<path d="M0 0 C4.678 0.46 6.917 1.831 10.812 4.375 C10.812 5.365 10.812 6.355 10.812 7.375 C9.637 6.849 9.637 6.849 8.438 6.312 C7.571 6.003 6.705 5.694 5.812 5.375 C3.482 6.334 3.482 6.334 1.812 9.375 C1.483 8.715 1.152 8.055 0.812 7.375 C1.75 5.25 1.75 5.25 2.812 3.375 C0.173 3.045 -2.467 2.715 -5.188 2.375 C-3.188 0.375 -3.188 0.375 0 0 Z " fill="#A15E4B" transform="translate(477.1875,658.625)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-0.592 2.075 -1.183 2.15 -1.793 2.227 C-8.097 3.276 -10.304 4.848 -14 10 C-14.66 10 -15.32 10 -16 10 C-15.705 6.465 -15.251 5.238 -12.625 2.75 C-8.329 -0.114 -5.062 -0.096 0 0 Z " fill="#EC2F30" transform="translate(819,143)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.179 3.947 1.094 6.598 -1 10 C-0.01 10.33 0.98 10.66 2 11 C2.454 11.495 2.908 11.99 3.375 12.5 C5.469 14.433 7.222 14.625 10 15 C10 15.33 10 15.66 10 16 C6.75 16.25 6.75 16.25 3 16 C2.361 15.34 1.721 14.68 1.062 14 C-1.368 11.643 -1.993 11.727 -5.25 11.75 C-6.488 11.832 -7.725 11.915 -9 12 C-6 10 -6 10 -3 10 C-2.01 6.7 -1.02 3.4 0 0 Z " fill="#6F291E" transform="translate(350,751)"/>
<path d="M0 0 C2 1 2 1 3.105 4.238 C3.475 5.594 3.835 6.952 4.188 8.312 C4.378 8.99 4.568 9.667 4.764 10.365 C5.69 13.864 6.386 16.567 5 20 C2 14.375 2 14.375 2 11 C1.34 10.67 0.68 10.34 0 10 C0 6.7 0 3.4 0 0 Z " fill="#EB2C2C" transform="translate(700,285)"/>
<path d="M0 0 C2.365 3.548 2.18 4.807 1.977 8.98 C1.925 10.144 1.873 11.307 1.82 12.506 C1.756 13.721 1.691 14.936 1.625 16.188 C1.568 17.414 1.512 18.641 1.453 19.904 C1.312 22.937 1.16 25.968 1 29 C-0.279 29.144 -1.558 29.289 -2.875 29.438 C-5.257 29.706 -6.845 29.922 -9 31 C-9.33 30.01 -9.66 29.02 -10 28 C-8 27 -8 27 0 28 C0 18.76 0 9.52 0 0 Z " fill="#C3584D" transform="translate(306,949)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-3.31 2 -5.62 2 -8 2 C-7.67 2.99 -7.34 3.98 -7 5 C-4.894 5.806 -4.894 5.806 -2.438 6.188 C-1.611 6.346 -0.785 6.505 0.066 6.668 C0.704 6.778 1.343 6.887 2 7 C2 7.33 2 7.66 2 8 C1.216 8.144 0.433 8.289 -0.375 8.438 C-2.964 8.878 -2.964 8.878 -5 10 C-5 9.01 -5 8.02 -5 7 C-7.97 6.505 -7.97 6.505 -11 6 C-11 4.35 -11 2.7 -11 1 C-7.23 -1.564 -4.326 -0.832 0 0 Z " fill="#B26C57" transform="translate(745,667)"/>
<path d="M0 0 C-1.98 0.99 -1.98 0.99 -4 2 C-4 2.99 -4 3.98 -4 5 C-4.33 5.66 -4.66 6.32 -5 7 C-2.36 7 0.28 7 3 7 C3 7.33 3 7.66 3 8 C0.69 8.33 -1.62 8.66 -4 9 C-4.66 10.65 -5.32 12.3 -6 14 C-6.857 10.919 -7.269 7.932 -7.562 4.75 C-7.646 3.858 -7.73 2.966 -7.816 2.047 C-7.877 1.371 -7.938 0.696 -8 0 C-4.713 -0.8 -3.29 -1.097 0 0 Z " fill="#B26F5A" transform="translate(662,661)"/>
<path d="M0 0 C-4.239 3.508 -8.229 3.355 -13.477 3.398 C-16.351 2.945 -17.229 2.247 -19 0 C-18.169 0.012 -17.337 0.023 -16.48 0.035 C-14.85 0.049 -14.85 0.049 -13.188 0.062 C-11.569 0.08 -11.569 0.08 -9.918 0.098 C-0.681 -0.211 -0.681 -0.211 0 0 Z " fill="#ED2A2B" transform="translate(449,537)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.356 5.553 -0.501 11.013 -1.562 16.5 C-1.701 17.227 -1.84 17.954 -1.982 18.703 C-2.319 20.469 -2.659 22.235 -3 24 C-3.33 24 -3.66 24 -4 24 C-3.886 21.103 -3.759 18.208 -3.625 15.312 C-3.594 14.496 -3.563 13.679 -3.531 12.838 C-3.056 3.056 -3.056 3.056 0 0 Z " fill="#EB2527" transform="translate(527,386)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C12 0.33 12 0.66 12 1 C11.095 1.004 11.095 1.004 10.172 1.008 C9.373 1.046 8.573 1.085 7.75 1.125 C6.961 1.148 6.172 1.171 5.359 1.195 C1.644 2.462 0.721 4.558 -1 8 C-1.281 10.786 -1.389 13.124 -1.25 15.875 C-1.232 16.563 -1.214 17.252 -1.195 17.961 C-1.149 19.641 -1.077 21.321 -1 23 C-3.825 20.175 -3.3 18.252 -3.312 14.312 C-3.249 8.724 -2.764 4.837 0 0 Z " fill="#C96356" transform="translate(553,942)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.931 1.038 -0.931 1.038 -1.88 1.076 C-4.691 1.192 -7.502 1.315 -10.312 1.438 C-11.289 1.477 -12.265 1.516 -13.271 1.557 C-14.209 1.599 -15.147 1.64 -16.113 1.684 C-17.409 1.739 -17.409 1.739 -18.732 1.795 C-21.165 1.927 -21.165 1.927 -24 3 C-26.399 2.913 -28.795 2.76 -31.188 2.562 C-32.46 2.461 -33.732 2.359 -35.043 2.254 C-36.019 2.17 -36.995 2.086 -38 2 C-38 1.67 -38 1.34 -38 1 C-25.369 -0.406 -12.688 -0.096 0 0 Z " fill="#DA5A48" transform="translate(279,875)"/>
<path d="M0 0 C2.972 1.125 5.334 2.223 8 4 C8.804 4.144 9.609 4.289 10.438 4.438 C13.297 5.065 14.794 6.121 17 8 C10.262 8.396 6.44 7.965 1.188 3.438 C0 2 0 2 0 0 Z " fill="#A05A48" transform="translate(686,687)"/>
<path d="M0 0 C6.747 0 10.467 4.578 15 9 C14.67 10.32 14.34 11.64 14 13 C13.361 12.134 12.721 11.267 12.062 10.375 C9.151 6.832 5.661 4.603 1.793 2.199 C1.201 1.803 0.61 1.408 0 1 C0 0.67 0 0.34 0 0 Z " fill="#EC2C2C" transform="translate(365,115)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.461 2.647 4 3.894 4 7 C1.36 7 -1.28 7 -4 7 C-4.66 5.68 -5.32 4.36 -6 3 C-4.02 3 -2.04 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#932A28" transform="translate(432,941)"/>
<path d="M0 0 C0.195 6.055 0.195 6.055 0 8 C-0.66 8.66 -1.32 9.32 -2 10 C-2 8.02 -2 6.04 -2 4 C-2.33 4.66 -2.66 5.32 -3 6 C-4.32 5.67 -5.64 5.34 -7 5 C-6.34 5 -5.68 5 -5 5 C-5 4.34 -5 3.68 -5 3 C-5.99 2.67 -6.98 2.34 -8 2 C-6.142 -1.716 -3.445 -1.069 0 0 Z " fill="#9C5847" transform="translate(710,688)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.015 0.676 1.029 1.352 1.044 2.048 C1.119 5.116 1.215 8.183 1.312 11.25 C1.335 12.313 1.358 13.377 1.381 14.473 C1.416 15.497 1.452 16.522 1.488 17.578 C1.514 18.521 1.541 19.463 1.568 20.435 C2.063 23.372 3.048 24.792 5 27 C3.68 26.67 2.36 26.34 1 26 C1 25.34 1 24.68 1 24 C0.01 23.67 -0.98 23.34 -2 23 C-1.34 23 -0.68 23 0 23 C-0.168 21.842 -0.335 20.685 -0.508 19.492 C-0.714 17.953 -0.92 16.414 -1.125 14.875 C-1.237 14.114 -1.349 13.354 -1.465 12.57 C-2.08 7.817 -1.949 4.519 0 0 Z " fill="#C48168" transform="translate(562,668)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.675 2.227 3.34 4.456 4 6.688 C4.371 7.929 4.742 9.17 5.125 10.449 C5.877 13.501 6.19 15.889 6 19 C1.969 15.533 1.125 11.037 0 6 C-0.188 2.375 -0.188 2.375 0 0 Z " fill="#E82526" transform="translate(728,519)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.33 8 0.66 8 1 C6.02 1.33 4.04 1.66 2 2 C2 6.95 2 11.9 2 17 C-0.69 11.619 -1.224 8.051 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#EB2F2E" transform="translate(239,512)"/>
<path d="M0 0 C7.138 0.155 11.475 3.742 17 8 C16.67 8.99 16.34 9.98 16 11 C15.348 10.529 14.695 10.059 14.023 9.574 C9.477 6.336 5.101 3.282 0 1 C0 0.67 0 0.34 0 0 Z " fill="#ED2C2C" transform="translate(460,349)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C1.66 4 2.32 4 3 4 C3.904 7.837 4.106 11.375 4.062 15.312 C4.053 16.381 4.044 17.45 4.035 18.551 C4.024 19.359 4.012 20.167 4 21 C-0 15 -0.315 7.082 0 0 Z " fill="#EA2626" transform="translate(473,162)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.75 4.438 2.75 4.438 3 8 C2.34 8.66 1.68 9.32 1 10 C0.67 10 0.34 10 0 10 C-0.33 10.66 -0.66 11.32 -1 12 C-1.66 11.67 -2.32 11.34 -3 11 C-3 10.34 -3 9.68 -3 9 C-5.31 9 -7.62 9 -10 9 C-9.67 8.34 -9.34 7.68 -9 7 C-6.934 6.586 -6.934 6.586 -4.438 6.375 C-3.611 6.3 -2.785 6.225 -1.934 6.148 C-0.976 6.075 -0.976 6.075 0 6 C-0.278 5.072 -0.278 5.072 -0.562 4.125 C-1 2 -1 2 0 0 Z " fill="#A96552" transform="translate(708,660)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C3.64 4.34 6.28 3.68 9 3 C9.33 4.32 9.66 5.64 10 7 C9.67 7.33 9.34 7.66 9 8 C7.142 7.91 5.288 7.754 3.438 7.562 C1.92 7.41 1.92 7.41 0.371 7.254 C-0.803 7.128 -0.803 7.128 -2 7 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#9E5342" transform="translate(329,737)"/>
<path d="M0 0 C0.639 0.33 1.279 0.66 1.938 1 C-1.062 2 -1.062 2 -4.062 1 C-5.624 0.933 -7.188 0.915 -8.75 0.938 C-9.558 0.947 -10.366 0.956 -11.199 0.965 C-11.814 0.976 -12.429 0.988 -13.062 1 C-13.062 1.66 -13.062 2.32 -13.062 3 C-14.176 3.474 -15.29 3.949 -16.438 4.438 C-18.33 5.253 -20.22 6.079 -22.062 7 C-20.605 3.157 -18.673 1.849 -15.062 0 C-10.066 -1.666 -5.005 -1.634 0 0 Z " fill="#B5705A" transform="translate(418.0625,732)"/>
<path d="M0 0 C0.826 0.485 0.826 0.485 1.668 0.98 C-1.918 2.773 -6.128 1.99 -10.07 1.918 C-13.274 1.882 -13.274 1.882 -16.125 2.543 C-18.332 2.98 -18.332 2.98 -20.332 1.98 C-14.139 -1.323 -6.687 -2.923 0 0 Z " fill="#B9705A" transform="translate(352.33203125,732.01953125)"/>
<path d="M0 0 C3 2 3 2 4 5 C4.145 6.407 4.246 7.818 4.316 9.23 C4.358 10.033 4.4 10.835 4.443 11.662 C4.483 12.495 4.522 13.329 4.562 14.188 C4.606 15.032 4.649 15.877 4.693 16.748 C4.799 18.832 4.9 20.916 5 23 C4.67 23 4.34 23 4 23 C3.327 19.918 2.663 16.834 2 13.75 C1.809 12.877 1.618 12.005 1.422 11.105 C1.241 10.261 1.061 9.417 0.875 8.547 C0.707 7.772 0.54 6.997 0.367 6.198 C0 4 0 4 0 0 Z " fill="#EB2526" transform="translate(490,382)"/>
<path d="M0 0 C1.976 2.963 2.264 3.68 2.414 7.012 C2.453 7.769 2.491 8.526 2.531 9.307 C2.578 10.485 2.578 10.485 2.625 11.688 C2.664 12.485 2.702 13.282 2.742 14.104 C2.836 16.069 2.919 18.034 3 20 C-0.021 16.985 -0.974 15.569 -1.172 11.23 C-1.053 9.881 -0.912 8.533 -0.75 7.188 C-0.651 6.148 -0.651 6.148 -0.551 5.088 C-0.387 3.39 -0.196 1.695 0 0 Z " fill="#EA282A" transform="translate(451,179)"/>
<path d="M0 0 C3.776 6.58 3.451 14.747 2 22 C1.67 22 1.34 22 1 22 C0.027 17.143 -0.12 12.55 -0.062 7.625 C-0.058 6.889 -0.053 6.153 -0.049 5.395 C-0.037 3.596 -0.019 1.798 0 0 Z " fill="#EC2E2F" transform="translate(356,160)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.66 3 2.32 3 3 C3.66 3 4.32 3 5 3 C4.959 3.701 4.918 4.403 4.875 5.125 C4.859 8.069 4.859 8.069 6.062 10.938 C7.123 14.402 6.83 16.515 6 20 C5.67 20 5.34 20 5 20 C4.963 19.42 4.925 18.84 4.887 18.242 C4.458 13.149 3.539 9.525 1 5 C1 4.34 1 3.68 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#6D2A1F" transform="translate(516,662)"/>
<path d="M0 0 C1.418 0.115 2.834 0.243 4.25 0.375 C5.039 0.445 5.828 0.514 6.641 0.586 C9 1 9 1 10.812 2 C14.019 3.466 17.281 3.468 20.75 3.625 C21.447 3.664 22.145 3.702 22.863 3.742 C24.575 3.836 26.288 3.919 28 4 C28 4.33 28 4.66 28 5 C8.092 5.445 8.092 5.445 2 3 C1.34 2.01 0.68 1.02 0 0 Z " fill="#F0443E" transform="translate(418,570)"/>
<path d="M0 0 C4.449 2.966 4.911 6.925 6 12 C6.204 14.408 6.158 16.58 6 19 C3.293 16.293 2.899 13.737 1.875 10.062 C1.522 8.816 1.169 7.569 0.805 6.285 C0 3 0 3 0 0 Z " fill="#EA2928" transform="translate(705,359)"/>
<path d="M0 0 C-5.569 3.558 -9.357 4.344 -16 4 C-19.438 3 -19.438 3 -22 2 C-18.589 0.903 -15.793 1.058 -12.25 1.438 C-11.265 1.539 -10.28 1.641 -9.266 1.746 C-8.144 1.872 -8.144 1.872 -7 2 C-7 1.34 -7 0.68 -7 0 C-2.25 -1.125 -2.25 -1.125 0 0 Z " fill="#BF4A42" transform="translate(213,976)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 6.6 1 13.2 1 20 C-1.856 17.144 -1.987 15.267 -2.188 11.25 C-2.15 7.192 -1.279 3.838 0 0 Z " fill="#8A1F20" transform="translate(183,947)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.883 4.518 2.106 8.775 2.062 13.375 C2.053 14.62 2.044 15.865 2.035 17.148 C2.024 18.089 2.012 19.03 2 20 C-0.305 16.543 -0.404 14.661 -0.688 10.562 C-0.774 9.429 -0.86 8.296 -0.949 7.129 C-0.993 4.458 -0.821 2.513 0 0 Z " fill="#ED2C2D" transform="translate(420,504)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.67 6.6 0.34 13.2 0 20 C-2 18 -2 18 -2.266 15.391 C-2.214 4.981 -2.214 4.981 0 0 Z " fill="#EC2627" transform="translate(282,391)"/>
<path d="M0 0 C1.5 1.375 1.5 1.375 3 3 C3 3.66 3 4.32 3 5 C5.64 5 8.28 5 11 5 C11 3.35 11 1.7 11 0 C13 2 13 2 13 6 C10.44 7.674 9.042 8.001 5.938 7.938 C3 7 3 7 1.125 4.562 C0 2 0 2 0 0 Z " fill="#BD4D44" transform="translate(206,944)"/>
<path d="M0 0 C2.635 2.405 4.403 5.019 6.25 8.062 C7.062 9.379 7.062 9.379 7.891 10.723 C8.257 11.474 8.623 12.226 9 13 C8.67 13.66 8.34 14.32 8 15 C7.34 15 6.68 15 6 15 C4.99 13.256 3.992 11.505 3 9.75 C2.165 8.288 2.165 8.288 1.312 6.797 C0 4 0 4 0 0 Z " fill="#9D3C37" transform="translate(373,942)"/>
<path d="M0 0 C2.228 0.538 3.657 0.13 5.812 -0.562 C5.482 1.087 5.153 2.737 4.812 4.438 C4.138 4.11 3.464 3.783 2.77 3.445 C-0.803 2.228 -3.735 2.242 -7.5 2.312 C-8.754 2.331 -10.009 2.349 -11.301 2.367 C-12.253 2.39 -13.206 2.414 -14.188 2.438 C-13.188 0.438 -13.188 0.438 -11.562 -0.188 C-7.679 -0.801 -3.827 -0.984 0 0 Z " fill="#C6836B" transform="translate(665.1875,731.5625)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.98 1.67 4.96 1.34 7 1 C7 2.65 7 4.3 7 6 C2.545 5.01 2.545 5.01 -2 4 C-2 5.65 -2 7.3 -2 9 C-2.66 8.34 -3.32 7.68 -4 7 C-4 5.68 -4 4.36 -4 3 C-2.68 2.67 -1.36 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B46B56" transform="translate(728,681)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.308 5.418 -0.758 10.682 -2 16 C-2.99 15.67 -3.98 15.34 -5 15 C-3.971 9.554 -2.754 4.882 0 0 Z " fill="#EC3433" transform="translate(425,387)"/>
<path d="M0 0 C6.397 -0.465 11.538 0.427 17 4 C17.33 4.99 17.66 5.98 18 7 C12 5 6 3 0 1 C0 0.67 0 0.34 0 0 Z " fill="#EE2728" transform="translate(314,347)"/>
<path d="M0 0 C1.188 3.429 0.802 5.378 -0.438 8.75 C-0.725 9.549 -1.012 10.348 -1.309 11.172 C-1.537 11.775 -1.765 12.378 -2 13 C2.455 12.505 2.455 12.505 7 12 C6.67 12.99 6.34 13.98 6 15 C3.36 14.67 0.72 14.34 -2 14 C-2 14.66 -2 15.32 -2 16 C-4 15 -4 15 -5 13 C-4.267 10.764 -3.44 8.558 -2.562 6.375 C-2.089 5.186 -1.616 3.998 -1.129 2.773 C-0.756 1.858 -0.384 0.943 0 0 Z " fill="#C05549" transform="translate(462,964)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C3.64 5 6.28 5 9 5 C9.33 4.01 9.66 3.02 10 2 C10 3.32 10 4.64 10 6 C6.874 7.853 4.627 8.372 1 8 C0.01 7.34 -0.98 6.68 -2 6 C-1.333 4 -0.667 2 0 0 Z " fill="#BD564B" transform="translate(707,944)"/>
<path d="M0 0 C0 1.98 0 3.96 0 6 C-2.64 6 -5.28 6 -8 6 C-8.562 4.062 -8.562 4.062 -9 2 C-6.986 -0.014 -2.769 0 0 0 Z " fill="#894436" transform="translate(433,662)"/>
<path d="M0 0 C-2.008 4.819 -5.397 6.809 -10 9 C-13 9.312 -13 9.312 -15 9 C-12.442 6.016 -9.494 4.711 -6 3 C-5.258 2.443 -4.515 1.886 -3.75 1.312 C-2 0 -2 0 0 0 Z " fill="#EC2D2B" transform="translate(618,561)"/>
<path d="M0 0 C3.748 1.249 4.227 2.894 5.938 6.309 C7.043 8.324 7.043 8.324 10 10 C9.67 10.99 9.34 11.98 9 13 C5.113 9.977 2.151 7.541 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EC2A2B" transform="translate(280,525)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C26 0.33 26 0.66 26 1 C24.866 1.061 23.731 1.121 22.562 1.184 C21.042 1.268 19.521 1.353 18 1.438 C17.256 1.477 16.512 1.516 15.746 1.557 C10.793 1.837 5.917 2.335 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#E92022" transform="translate(527,310)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C4.206 7.672 4.094 12.215 4 17 C3.34 16.67 2.68 16.34 2 16 C0.415 10.806 -0.285 5.421 0 0 Z " fill="#EA2D2D" transform="translate(703,215)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C6.67 0.99 6.34 1.98 6 3 C6.66 3 7.32 3 8 3 C8 3.66 8 4.32 8 5 C5.36 5 2.72 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#812425" transform="translate(418,957)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 2.65 1.34 4.3 1 6 C0.01 5.67 -0.98 5.34 -2 5 C-2.33 6.32 -2.66 7.64 -3 9 C-4.32 8.67 -5.64 8.34 -7 8 C-6.67 6.35 -6.34 4.7 -6 3 C-4.68 3 -3.36 3 -2 3 C-2 2.34 -2 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#702A1F" transform="translate(519,684)"/>
<path d="M0 0 C1.938 0.114 3.876 0.242 5.812 0.375 C6.891 0.445 7.97 0.514 9.082 0.586 C12 1 12 1 15 3 C14.34 4.65 13.68 6.3 13 8 C12.34 7.67 11.68 7.34 11 7 C11.33 6.01 11.66 5.02 12 4 C6.06 2.515 6.06 2.515 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A75E4C" transform="translate(733,680)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-3.31 1.67 -5.62 1.34 -8 1 C-8 3.97 -8 6.94 -8 10 C-9.32 9.67 -10.64 9.34 -12 9 C-10.276 0.776 -9.091 -1.715 0 0 Z " fill="#B86D57" transform="translate(449,645)"/>
<path d="M0 0 C4.593 2.041 6.264 4.593 8.285 8.992 C9.149 11.418 9.319 13.447 9 16 C6.334 13.631 4.926 11.196 3.375 8 C2.737 6.701 2.737 6.701 2.086 5.375 C1 3 1 3 0 0 Z " fill="#EA282A" transform="translate(481,365)"/>
<path d="M0 0 C3.234 6.047 3.118 12.298 3 19 C0.259 16.259 0.014 14.49 -0.098 10.645 C-0.086 9.38 -0.074 8.115 -0.062 6.812 C-0.053 5.54 -0.044 4.268 -0.035 2.957 C-0.024 1.981 -0.012 1.005 0 0 Z " fill="#EA2628" transform="translate(467,289)"/>
<path d="M0 0 C2.456 2.553 2.988 3.859 3.293 7.453 C3.258 8.706 3.223 9.959 3.188 11.25 C3.16 12.513 3.133 13.777 3.105 15.078 C3.071 16.042 3.036 17.007 3 18 C-0.619 12.572 -0.111 6.317 0 0 Z " fill="#E92627" transform="translate(483,219)"/>
<path d="M0 0 C3.377 1.393 4.871 2.823 6.75 5.938 C7.178 6.627 7.606 7.317 8.047 8.027 C9 10 9 10 9 13 C6 12 6 12 4.5 9.312 C4.005 8.219 3.51 7.126 3 6 C2.443 4.886 1.886 3.773 1.312 2.625 C0.879 1.759 0.446 0.893 0 0 Z " fill="#EB2A2B" transform="translate(347,144)"/>
<path d="M0 0 C1.938 0.375 1.938 0.375 4 1 C4.33 1.66 4.66 2.32 5 3 C7.322 3.407 9.657 3.744 12 4 C12.33 3.01 12.66 2.02 13 1 C13 2.98 13 4.96 13 7 C9.7 6.67 6.4 6.34 3 6 C2.67 4.68 2.34 3.36 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B34A42" transform="translate(565,943)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.811 1.094 6.625 1.13 8.438 1.125 C9.859 1.129 9.859 1.129 11.309 1.133 C13.701 1.015 15.713 0.681 18 0 C17.67 1.32 17.34 2.64 17 4 C16.34 3.67 15.68 3.34 15 3 C14.299 3.33 13.597 3.66 12.875 4 C8.755 5.433 6.258 4.977 2 4 C0.5 1.938 0.5 1.938 0 0 Z " fill="#BA755E" transform="translate(650,763)"/>
<path d="M0 0 C2.288 2.288 2.755 3.808 3.688 6.875 C3.959 7.739 4.231 8.602 4.512 9.492 C5.017 12.087 4.856 13.531 4 16 C4 15.34 4 14.68 4 14 C3.34 14 2.68 14 2 14 C0.409 9.127 -0.212 5.192 0 0 Z " fill="#E82829" transform="translate(683,227)"/>
<path d="M0 0 C3.96 1.273 5.377 3.683 7.25 7.18 C8.22 9.533 8.305 11.487 8 14 C0 4.571 0 4.571 0 0 Z " fill="#8F312F" transform="translate(312,945)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 4.96 3 8.92 3 13 C1.68 13.33 0.36 13.66 -1 14 C-0.835 12.804 -0.67 11.607 -0.5 10.375 C-0.06 6.895 0.088 3.512 0 0 Z " fill="#AE453E" transform="translate(809,943)"/>
<path d="M0 0 C2.312 1.562 2.312 1.562 4 4 C4.457 6.852 4.284 9.067 4 12 C3.34 12.66 2.68 13.32 2 14 C2.058 13.385 2.116 12.77 2.176 12.137 C2.313 9.68 2.351 7.438 2 5 C-0.161 2.92 -0.161 2.92 -3 2 C-3.763 1.608 -4.526 1.216 -5.312 0.812 C-5.869 0.544 -6.426 0.276 -7 0 C-4.234 -1.383 -2.926 -0.906 0 0 Z " fill="#AA604D" transform="translate(752,676)"/>
<path d="M0 0 C0.668 1.738 0.668 1.738 1 4 C-0.074 6.105 -0.074 6.105 -1.688 8.188 C-2.467 9.23 -2.467 9.23 -3.262 10.293 C-5.182 12.179 -6.373 12.617 -9 13 C-8.602 12.481 -8.203 11.961 -7.793 11.426 C-4.978 7.721 -2.286 4.064 0 0 Z " fill="#EC2A29" transform="translate(628,546)"/>
<path d="M0 0 C2.268 3.652 2.39 7.013 2.625 11.25 C2.7 12.513 2.775 13.777 2.852 15.078 C2.901 16.042 2.95 17.007 3 18 C0.544 15.447 0.012 14.141 -0.293 10.547 C-0.258 9.294 -0.223 8.041 -0.188 6.75 C-0.16 5.487 -0.133 4.223 -0.105 2.922 C-0.071 1.958 -0.036 0.993 0 0 Z " fill="#E92525" transform="translate(462,257)"/>
<path d="M0 0 C3 1 3 1 4.168 2.637 C6.307 6.94 7.493 10.074 7 15 C4.22 13.61 4.041 12.053 2.938 9.188 C2.596 8.315 2.254 7.442 1.902 6.543 C1.131 4.37 0.508 2.247 0 0 Z " fill="#EA2829" transform="translate(381,129)"/>
<path d="M0 0 C2.479 2.155 3.576 3.711 4 7 C3.67 7.66 3.34 8.32 3 9 C1.68 9 0.36 9 -1 9 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#953431" transform="translate(369,935)"/>
<path d="M0 0 C1.343 4.363 1.028 8.605 0.938 13.125 C0.992 15.629 1.268 17.621 2 20 C-1.265 17.141 -2.012 14.596 -2.859 10.43 C-3.093 6.4 -1.708 3.613 0 0 Z " fill="#BF7861" transform="translate(484,741)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.188 4.625 3.188 4.625 3 7 C0.36 7 -2.28 7 -5 7 C-5.33 5.68 -5.66 4.36 -6 3 C-5.01 3.495 -5.01 3.495 -4 4 C-4 4.66 -4 5.32 -4 6 C-3.01 6 -2.02 6 -1 6 C-1.021 5.196 -1.041 4.391 -1.062 3.562 C-1 1 -1 1 0 0 Z " fill="#7F3225" transform="translate(385,666)"/>
<path d="M0 0 C0.972 2.591 1.116 3.697 0.109 6.324 C-0.339 7.104 -0.788 7.884 -1.25 8.688 C-1.688 9.475 -2.127 10.263 -2.578 11.074 C-4 13 -4 13 -7 14 C-5.236 9.028 -2.606 4.566 0 0 Z " fill="#EC2928" transform="translate(488,540)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-3.942 2.981 -5.991 3.165 -9.062 3.188 C-10.379 3.209 -10.379 3.209 -11.723 3.23 C-14 3 -14 3 -16 1 C-10.629 0.318 -5.415 -0.115 0 0 Z " fill="#EC2629" transform="translate(307,537)"/>
<path d="M0 0 C3.124 2.556 4.032 5.127 5 9 C5.075 11.332 5.093 13.669 5 16 C1.378 12.378 0.12 6.967 -0.188 1.938 C-0.126 1.298 -0.064 0.659 0 0 Z " fill="#EA2828" transform="translate(728,435)"/>
<path d="M0 0 C2 3 2 3 2 5 C2.66 5.33 3.32 5.66 4 6 C3.34 9.63 2.68 13.26 2 17 C1.67 17 1.34 17 1 17 C0.67 11.39 0.34 5.78 0 0 Z " fill="#EA2929" transform="translate(636,395)"/>
<path d="M0 0 C4.233 1.512 5.857 4.157 8 8 C8.75 11.375 8.75 11.375 9 14 C8.67 13.34 8.34 12.68 8 12 C7.34 12 6.68 12 6 12 C4 8 2 4 0 0 Z " fill="#EB2828" transform="translate(589,383)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.29 5.616 1.108 9.098 -2 14 C-2.99 14.495 -2.99 14.495 -4 15 C-2.073 4.442 -2.073 4.442 0 0 Z " fill="#EB2626" transform="translate(269,270)"/>
<path d="M0 0 C4.556 0.556 4.556 0.556 6 2 C6.041 3.666 6.043 5.334 6 7 C5.01 7.33 4.02 7.66 3 8 C3 6.35 3 4.7 3 3 C1.35 2.67 -0.3 2.34 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#84382B" transform="translate(693,757)"/>
<path d="M0 0 C2.062 0.438 2.062 0.438 4 1 C4 2.65 4 4.3 4 6 C3.34 5.34 2.68 4.68 2 4 C0.855 4.712 0.855 4.712 -0.312 5.438 C-4.396 7.812 -6.485 8.098 -11 7 C-11 6.67 -11 6.34 -11 6 C-7.7 5.34 -4.4 4.68 -1 4 C-1 3.01 -1 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B06D59" transform="translate(517,688)"/>
<path d="M0 0 C4.097 2.048 5.533 3.257 8 7 C8.75 10.312 8.75 10.312 9 13 C8.67 12.34 8.34 11.68 8 11 C7.34 11 6.68 11 6 11 C4.02 7.37 2.04 3.74 0 0 Z " fill="#EC2A2B" transform="translate(306,383)"/>
<path d="M0 0 C4.572 5.611 5.239 9.949 5 17 C3.147 14.22 2.314 12.217 1.375 9.062 C1.115 8.208 0.854 7.353 0.586 6.473 C0 4 0 4 0 0 Z " fill="#EA2525" transform="translate(674,118)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C3.964 3.577 3.964 3.577 6.125 3.688 C7.404 3.791 8.683 3.894 10 4 C10.33 2.68 10.66 1.36 11 0 C11.66 1.98 12.32 3.96 13 6 C8.744 7.161 5.316 6.762 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#BA4F44" transform="translate(487,972)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.215 4.824 -0.215 4.824 -1.938 6.688 C-2.503 7.31 -3.069 7.933 -3.652 8.574 C-4.097 9.045 -4.542 9.515 -5 10 C-6.32 9.67 -7.64 9.34 -9 9 C-6.03 6.03 -3.06 3.06 0 0 Z " fill="#A03D39" transform="translate(793,958)"/>
<path d="M0 0 C2.815 2.815 2.628 5.217 3.125 9.125 C3.293 10.406 3.46 11.688 3.633 13.008 C3.754 13.995 3.875 14.983 4 16 C1.752 13.948 0.998 12.993 0 10 C-0.068 8.272 -0.085 6.542 -0.062 4.812 C-0.053 3.911 -0.044 3.01 -0.035 2.082 C-0.024 1.395 -0.012 0.708 0 0 Z " fill="#EB2728" transform="translate(168,262)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.96 1.66 7.92 2 12 C1.01 12 0.02 12 -1 12 C-1.33 12.99 -1.66 13.98 -2 15 C-1.859 12.875 -1.712 10.75 -1.562 8.625 C-1.481 7.442 -1.4 6.258 -1.316 5.039 C-1 2 -1 2 0 0 Z " fill="#E92626" transform="translate(510,252)"/>
<path d="M0 0 C2.701 0.091 5.369 0.237 8.062 0.438 C9.2 0.516 9.2 0.516 10.361 0.596 C12.241 0.726 14.121 0.862 16 1 C16 2.65 16 4.3 16 6 C15.63 5.513 15.26 5.025 14.879 4.523 C12.23 2.375 10.306 2.6 6.938 2.625 C5.342 2.613 5.342 2.613 3.715 2.602 C2.819 2.733 1.923 2.865 1 3 C0.34 3.99 -0.32 4.98 -1 6 C-1.043 4.334 -1.041 2.666 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B14D43" transform="translate(471,966)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C-3.679 6.434 -7.814 7.533 -14 7 C-14.66 6.34 -15.32 5.68 -16 5 C-14.824 5.041 -13.649 5.082 -12.438 5.125 C-8.239 5.118 -4.922 4.521 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#CA7E66" transform="translate(349,762)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.369 7.508 1.369 7.508 -0.5 10 C-0.995 10.33 -1.49 10.66 -2 11 C-1.67 9.02 -1.34 7.04 -1 5 C-3.64 5 -6.28 5 -9 5 C-5.772 2.848 -4.716 2.799 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#AD6753" transform="translate(349,748)"/>
<path d="M0 0 C3.375 -0.125 3.375 -0.125 7 0 C7.66 0.66 8.32 1.32 9 2 C7.068 3.932 3.608 3.634 1 4 C0.01 4.495 0.01 4.495 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#9A5440" transform="translate(537,738)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 3.96 7 7.92 7 12 C3.894 12 2.647 11.461 0 10 C1.98 10 3.96 10 6 10 C6 7.03 6 4.06 6 1 C3.69 1.33 1.38 1.66 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C98068" transform="translate(384,731)"/>
<path d="M0 0 C2.062 0.438 2.062 0.438 4 1 C-1.002 4.001 -4.316 4.474 -10 4 C-10.66 3.34 -11.32 2.68 -12 2 C-11.218 1.939 -10.435 1.879 -9.629 1.816 C-8.617 1.733 -7.605 1.649 -6.562 1.562 C-5.553 1.481 -4.544 1.4 -3.504 1.316 C-1.064 1.268 -1.064 1.268 0 0 Z " fill="#ED2022" transform="translate(346,276)"/>
<path d="M0 0 C3 2 3 2 4 5 C4.068 6.728 4.085 8.458 4.062 10.188 C4.053 11.089 4.044 11.99 4.035 12.918 C4.024 13.605 4.012 14.292 4 15 C1.813 11.719 1.456 9.669 0.875 5.812 C0.707 4.726 0.54 3.639 0.367 2.52 C0.246 1.688 0.125 0.857 0 0 Z " fill="#E92929" transform="translate(860,139)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2 2.32 2 3 2 C3.872 4.995 4.108 7.636 4.062 10.75 C4.053 11.549 4.044 12.348 4.035 13.172 C4.024 13.775 4.012 14.378 4 15 C1.736 12.736 1.516 11.69 0.875 8.625 C0.707 7.854 0.54 7.083 0.367 6.289 C0 4 0 4 0 0 Z " fill="#E92627" transform="translate(388,145)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.423 1.969 2.423 1.969 3.875 1.938 C7 2 7 2 9 3 C8.34 4.32 7.68 5.64 7 7 C7 6.01 7 5.02 7 4 C3.7 4.33 0.4 4.66 -3 5 C-2.01 3.35 -1.02 1.7 0 0 Z " fill="#BC725B" transform="translate(418,754)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 1.32 8 2.64 8 4 C5.36 4.33 2.72 4.66 0 5 C0.66 4.67 1.32 4.34 2 4 C1.34 2.68 0.68 1.36 0 0 Z " fill="#762E22" transform="translate(340,747)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.045 2.46 0.086 3.918 -0.875 5.375 C-1.409 6.187 -1.942 6.999 -2.492 7.836 C-4 10 -4 10 -6 12 C-6.312 9.312 -6.312 9.312 -6 6 C-3.562 3.188 -3.562 3.188 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#95503E" transform="translate(491,733)"/>
<path d="M0 0 C3 1 3 1 4 2.75 C4 5 4 5 1.562 7.75 C0.294 8.864 0.294 8.864 -1 10 C-1.99 9.67 -2.98 9.34 -4 9 C-3.34 9 -2.68 9 -2 9 C-1.34 6.03 -0.68 3.06 0 0 Z " fill="#964F3D" transform="translate(384,685)"/>
<path d="M0 0 C1.32 1.65 2.64 3.3 4 5 C1.118 5.961 -0.699 6.106 -3.688 6.062 C-4.9 6.049 -4.9 6.049 -6.137 6.035 C-7.059 6.018 -7.059 6.018 -8 6 C-8 5.01 -8 4.02 -8 3 C-5.36 3.33 -2.72 3.66 0 4 C0 2.68 0 1.36 0 0 Z " fill="#BA7059" transform="translate(754,666)"/>
<path d="M0 0 C2 1 2 1 3.125 4.25 C3.855 7.377 4.192 9.826 4 13 C3.67 12.34 3.34 11.68 3 11 C2.34 11 1.68 11 1 11 C-0.176 7.326 -0.074 3.831 0 0 Z " fill="#E82728" transform="translate(708,452)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.64 1.66 5.28 2 8 C1.34 8 0.68 8 0 8 C-0.33 12.62 -0.66 17.24 -1 22 C-1.33 22 -1.66 22 -2 22 C-2.301 14.449 -1.426 7.411 0 0 Z " fill="#F0413E" transform="translate(381,394)"/>
<path d="M0 0 C1.482 0.254 2.961 0.529 4.438 0.812 C5.673 1.039 5.673 1.039 6.934 1.27 C7.956 1.631 7.956 1.631 9 2 C9.33 2.99 9.66 3.98 10 5 C10.99 5.33 11.98 5.66 13 6 C8.731 5.498 4.832 5.029 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#EF2E2E" transform="translate(195,306)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.226 5.419 0.467 9.775 -1 15 C-1.33 15 -1.66 15 -2 15 C-2.054 13.25 -2.093 11.5 -2.125 9.75 C-2.16 8.288 -2.16 8.288 -2.195 6.797 C-1.999 3.98 -1.446 2.391 0 0 Z " fill="#EB2728" transform="translate(513,123)"/>
<path d="M0 0 C4.457 0.262 6.515 0.577 9.938 3.562 C10.618 4.367 11.299 5.171 12 6 C8.556 6 7.252 5.204 4.312 3.5 C3.504 3.036 2.696 2.572 1.863 2.094 C1.248 1.733 0.634 1.372 0 1 C0 0.67 0 0.34 0 0 Z " fill="#EE2A2B" transform="translate(240,113)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C8.67 1.65 8.34 3.3 8 5 C7.67 4.01 7.34 3.02 7 2 C5.015 2.267 5.015 2.267 3 3 C2.67 3.99 2.34 4.98 2 6 C1.34 4.02 0.68 2.04 0 0 Z " fill="#762E20" transform="translate(525,733)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 2.65 0.68 4.3 0 6 C-0.66 6 -1.32 6 -2 6 C-2 8.31 -2 10.62 -2 13 C-2.66 12.34 -3.32 11.68 -4 11 C-3.745 6.415 -3.14 3.413 0 0 Z " fill="#AC6651" transform="translate(729,662)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.082 1.582 1.139 3.166 1.188 4.75 C1.222 5.632 1.257 6.513 1.293 7.422 C0.949 10.447 -0.002 11.756 -2 14 C-1.86 11.854 -1.713 9.708 -1.562 7.562 C-1.481 6.368 -1.4 5.173 -1.316 3.941 C-1 1 -1 1 0 0 Z " fill="#E82324" transform="translate(513,237)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.476 1.706 1.476 1.706 0.941 2.426 C0.486 3.048 0.031 3.671 -0.438 4.312 C-0.89 4.927 -1.342 5.542 -1.809 6.176 C-3.173 8.097 -3.173 8.097 -4 11 C-4.66 11 -5.32 11 -6 11 C-5.627 5.65 -4.004 3.491 0 0 Z " fill="#A03E39" transform="translate(751,935)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C15 0.99 15 1.98 15 3 C13.02 3 11.04 3 9 3 C9 2.34 9 1.68 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#CA4C3B" transform="translate(348,878)"/>
<path d="M0 0 C0.745 0.009 1.49 0.018 2.258 0.027 C4.089 0.051 5.919 0.087 7.75 0.125 C7.75 0.455 7.75 0.785 7.75 1.125 C7.002 1.186 6.255 1.246 5.484 1.309 C4.5 1.392 3.515 1.476 2.5 1.562 C1.038 1.684 1.038 1.684 -0.453 1.809 C-3.17 2.05 -3.17 2.05 -5.438 2.688 C-7.904 3.283 -9.82 2.732 -12.25 2.125 C-8.408 -0.443 -4.436 -0.102 0 0 Z " fill="#CE4034" transform="translate(207.25,871.875)"/>
<path d="M0 0 C2 3 2 3 2 6 C0.35 6 -1.3 6 -3 6 C-3.33 6.66 -3.66 7.32 -4 8 C-4 7.34 -4 6.68 -4 6 C-4.66 6 -5.32 6 -6 6 C-6 6.66 -6 7.32 -6 8 C-6.66 7.67 -7.32 7.34 -8 7 C-8.33 5.68 -8.66 4.36 -9 3 C-4.545 3.99 -4.545 3.99 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C2775F" transform="translate(478,740)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.25 6.75 1.25 6.75 -1 9 C-4.625 9.125 -4.625 9.125 -8 9 C-8.33 8.34 -8.66 7.68 -9 7 C-6.03 7.495 -6.03 7.495 -3 8 C-3 7.34 -3 6.68 -3 6 C-2.01 6 -1.02 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#B9735C" transform="translate(636,647)"/>
<path d="M0 0 C4.044 1.779 7.461 4.395 11 7 C9.164 7.605 9.164 7.605 7 8 C3.587 5.806 1.49 3.792 0 0 Z " fill="#EC2A2A" transform="translate(283,470)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C4.296 9.41 4.296 9.41 4 14 C1.685 12.182 1.046 11.244 0.488 8.301 C0.43 7.315 0.372 6.329 0.312 5.312 C0.247 4.319 0.181 3.325 0.113 2.301 C0.076 1.542 0.039 0.782 0 0 Z " fill="#E7282A" transform="translate(669,176)"/>
<path d="M0 0 C0.806 0.003 1.611 0.005 2.441 0.008 C3.646 0.004 3.646 0.004 4.875 0 C7.004 0.133 7.004 0.133 9.004 1.133 C8.674 2.123 8.344 3.113 8.004 4.133 C8.004 3.473 8.004 2.813 8.004 2.133 C5.034 2.133 2.064 2.133 -0.996 2.133 C-1.326 3.453 -1.656 4.773 -1.996 6.133 C-2.378 4.477 -2.71 2.808 -2.996 1.133 C-1.996 0.133 -1.996 0.133 0 0 Z " fill="#C35C51" transform="translate(624.99609375,962.8671875)"/>
<path d="M0 0 C1 3 1 3 0.25 5.562 C-1 8 -1 8 -4 10 C-4.99 9.67 -5.98 9.34 -7 9 C-6.578 8.518 -6.157 8.036 -5.723 7.539 C-5.175 6.907 -4.627 6.276 -4.062 5.625 C-3.517 4.999 -2.972 4.372 -2.41 3.727 C-0.93 2.044 -0.93 2.044 0 0 Z " fill="#8F4838" transform="translate(716,697)"/>
<path d="M0 0 C5.394 -0.274 8.519 -0.361 13 3 C12.67 3.66 12.34 4.32 12 5 C10.68 4.67 9.36 4.34 8 4 C8 3.34 8 2.68 8 2 C5.36 1.67 2.72 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B8735D" transform="translate(662,659)"/>
<path d="M0 0 C1.439 0.453 2.876 0.912 4.312 1.375 C5.113 1.63 5.914 1.885 6.738 2.148 C9 3 9 3 12 5 C9.69 5.33 7.38 5.66 5 6 C5 5.34 5 4.68 5 4 C4.361 3.897 3.721 3.794 3.062 3.688 C2.382 3.461 1.701 3.234 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#ED2D2C" transform="translate(267,566)"/>
<path d="M0 0 C-1.451 3.826 -3.358 5.223 -7 7 C-7.99 7 -8.98 7 -10 7 C-10 6.34 -10 5.68 -10 5 C-8.32 3.711 -8.32 3.711 -6.125 2.375 C-5.406 1.929 -4.686 1.483 -3.945 1.023 C-2 0 -2 0 0 0 Z " fill="#ED2A29" transform="translate(337,560)"/>
<path d="M0 0 C3.767 1.739 6.212 3.903 9 7 C9 7.66 9 8.32 9 9 C6.296 8.636 5.256 8.319 3.516 6.152 C2.889 5.056 2.889 5.056 2.25 3.938 C1.822 3.204 1.394 2.471 0.953 1.715 C0.639 1.149 0.324 0.583 0 0 Z " fill="#EC2729" transform="translate(302,484)"/>
<path d="M0 0 C2.538 1.269 2.894 2.416 4.125 4.938 C4.478 5.648 4.831 6.358 5.195 7.09 C6 9 6 9 6 11 C3.591 10.036 2.24 9.348 0.75 7.188 C-0.111 4.677 -0.146 2.637 0 0 Z " fill="#EA2727" transform="translate(245,429)"/>
<path d="M0 0 C2 1 2 1 3.125 3.688 C3.989 6.958 4.16 9.637 4 13 C0.255 9.255 0.311 5.017 0 0 Z " fill="#EB2829" transform="translate(452,390)"/>
<path d="M0 0 C3.35 1.117 3.793 1.747 5.688 4.562 C6.124 5.203 6.561 5.844 7.012 6.504 C7.338 6.998 7.664 7.491 8 8 C7.01 8.495 7.01 8.495 6 9 C0.429 3.857 0.429 3.857 0 0 Z " fill="#EC2D2D" transform="translate(207,267)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.193 4.824 0.757 8.496 -1 13 C-1.33 13 -1.66 13 -2 13 C-2.054 11.584 -2.093 10.167 -2.125 8.75 C-2.148 7.961 -2.171 7.172 -2.195 6.359 C-1.983 3.793 -1.287 2.208 0 0 Z " fill="#E92626" transform="translate(510,139)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C3 6.96 3 10.92 3 15 C1.116 11.231 0.666 9.32 0.375 5.25 C0.3 4.265 0.225 3.28 0.148 2.266 C0.099 1.518 0.05 0.77 0 0 Z " fill="#EB2523" transform="translate(465,119)"/>
<path d="M0 0 C7.429 -0.286 7.429 -0.286 11 2 C10.67 2.99 10.34 3.98 10 5 C5.05 3.02 5.05 3.02 0 1 C0 0.67 0 0.34 0 0 Z " fill="#EC292A" transform="translate(829,111)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C1.125 3.875 1.125 3.875 0 5 C-1.686 5.072 -3.375 5.084 -5.062 5.062 C-5.982 5.053 -6.901 5.044 -7.848 5.035 C-8.558 5.024 -9.268 5.012 -10 5 C-10 4.67 -10 4.34 -10 4 C-8.907 3.856 -7.814 3.711 -6.688 3.562 C-3.069 3.117 -3.069 3.117 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A6433D" transform="translate(426,966)"/>
<path d="M0 0 C2.062 0.438 2.062 0.438 4 1 C3.519 1.552 3.038 2.103 2.543 2.672 C0.76 5.361 0.522 7.058 0.312 10.25 C0.247 11.142 0.181 12.034 0.113 12.953 C0.076 13.629 0.039 14.304 0 15 C-0.33 15 -0.66 15 -1 15 C-1.027 12.688 -1.046 10.375 -1.062 8.062 C-1.074 6.775 -1.086 5.487 -1.098 4.16 C-1 1 -1 1 0 0 Z " fill="#BB7761" transform="translate(494,740)"/>
<path d="M0 0 C2 1 2 1 2.852 3.285 C3.107 4.202 3.362 5.118 3.625 6.062 C3.885 6.982 4.146 7.901 4.414 8.848 C4.607 9.558 4.801 10.268 5 11 C2 10 2 10 0.812 7.75 C0.001 5.002 -0.163 2.846 0 0 Z " fill="#ED2C2B" transform="translate(528,536)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.027 1.458 1.046 2.917 1.062 4.375 C1.074 5.187 1.086 5.999 1.098 6.836 C1 9 1 9 0 11 C-0.66 11 -1.32 11 -2 11 C-2.33 11.66 -2.66 12.32 -3 13 C-2.69 11.395 -2.377 9.791 -2.062 8.188 C-1.888 7.294 -1.714 6.401 -1.535 5.48 C-1 3 -1 3 0 0 Z " fill="#EB2727" transform="translate(356,525)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.094 3.867 -1.201 4.718 -2.312 5.562 C-2.927 6.038 -3.542 6.514 -4.176 7.004 C-4.778 7.333 -5.38 7.661 -6 8 C-6.99 7.67 -7.98 7.34 -9 7 C-6.03 4.69 -3.06 2.38 0 0 Z " fill="#ED2526" transform="translate(255,298)"/>
<path d="M0 0 C4.29 0.33 8.58 0.66 13 1 C13 2.65 13 4.3 13 6 C8.71 4.35 4.42 2.7 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B74F46" transform="translate(635,959)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C3.31 6.33 5.62 6.66 8 7 C8 7.33 8 7.66 8 8 C5.36 8.33 2.72 8.66 0 9 C-0.845 5.622 -1.108 3.325 0 0 Z " fill="#C1584E" transform="translate(781,943)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2 7.625 2 7.625 2 11 C1.34 11.66 0.68 12.32 0 13 C-0.99 12.34 -1.98 11.68 -3 11 C-2.34 10.01 -1.68 9.02 -1 8 C-0.549 5.951 -0.549 5.951 -0.375 3.812 C-0.251 2.554 -0.128 1.296 0 0 Z " fill="#A96552" transform="translate(669,745)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8.66 1.32 9.32 2.64 10 4 C7.36 3.34 4.72 2.68 2 2 C1.67 2.66 1.34 3.32 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#752D1F" transform="translate(686,698)"/>
<path d="M0 0 C-0.66 0.66 -1.32 1.32 -2 2 C-4.602 1.977 -4.602 1.977 -7.625 1.625 C-8.628 1.514 -9.631 1.403 -10.664 1.289 C-11.82 1.146 -11.82 1.146 -13 1 C-13.33 1.99 -13.66 2.98 -14 4 C-14 2.68 -14 1.36 -14 0 C-8.977 -1.13 -5.021 -0.779 0 0 Z " fill="#BE7962" transform="translate(510,697)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-3.63 2.33 -7.26 2.66 -11 3 C-8.894 -1.211 -4.117 -0.078 0 0 Z " fill="#EC2626" transform="translate(819,143)"/>
<path d="M0 0 C2 1 2 1 2.852 3.285 C3.107 4.202 3.362 5.118 3.625 6.062 C3.885 6.982 4.146 7.901 4.414 8.848 C4.607 9.558 4.801 10.268 5 11 C2 10 2 10 0 7 C-0.079 4.668 -0.088 2.332 0 0 Z " fill="#A03F3B" transform="translate(624,965)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.33 3.97 3.66 6.94 4 10 C-0.609 4.647 -0.609 4.647 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9B3834" transform="translate(309,951)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C-0.11 4.288 -3.016 6.837 -7 9 C-5.661 4.617 -3.878 2.459 0 0 Z " fill="#AF4A42" transform="translate(550,935)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-3.97 3 -6.94 3 -10 3 C-7.285 0.285 -3.679 -1.839 0 0 Z " fill="#CD4030" transform="translate(848,865)"/>
<path d="M0 0 C2.438 0.688 2.438 0.688 5 2 C5.812 4.625 5.812 4.625 6 7 C5.34 7 4.68 7 4 7 C2.286 5.041 0.616 3.041 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#984B3C" transform="translate(748,661)"/>
<path d="M0 0 C2.695 -0.077 5.04 0.008 7.688 0.562 C10.015 1.245 10.015 1.245 12 0 C12.99 0.66 13.98 1.32 15 2 C11.306 3.231 7.845 3.464 4 3 C1.5 1.5 1.5 1.5 0 0 Z " fill="#EF4341" transform="translate(573,536)"/>
<path d="M0 0 C2.421 2.421 2.528 3.801 3.125 7.125 C3.293 8.035 3.46 8.945 3.633 9.883 C3.754 10.581 3.875 11.28 4 12 C3.67 11.34 3.34 10.68 3 10 C2.34 10 1.68 10 1 10 C0.67 6.7 0.34 3.4 0 0 Z " fill="#E92424" transform="translate(719,489)"/>
<path d="M0 0 C1 3 1 3 -0.188 5.625 C-2 8 -2 8 -4.188 8.812 C-4.786 8.874 -5.384 8.936 -6 9 C-2.25 2.25 -2.25 2.25 0 0 Z " fill="#ED2728" transform="translate(381,289)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C-1.438 6.75 -1.438 6.75 -4 9 C-4.66 8.67 -5.32 8.34 -6 8 C-4.02 5.36 -2.04 2.72 0 0 Z " fill="#EC2424" transform="translate(263,287)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C3.286 8.429 3.286 8.429 3 13 C2.67 12.34 2.34 11.68 2 11 C1.34 11 0.68 11 0 11 C0 7.37 0 3.74 0 0 Z " fill="#E92727" transform="translate(764,268)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.458 3.796 -0.201 5.472 -3 8 C-3.66 7.67 -4.32 7.34 -5 7 C-4.217 5.828 -3.423 4.662 -2.625 3.5 C-2.184 2.85 -1.743 2.201 -1.289 1.531 C-0.651 0.773 -0.651 0.773 0 0 Z " fill="#EE2B2C" transform="translate(230,268)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C12 0.33 12 0.66 12 1 C11.397 1.049 10.793 1.098 10.172 1.148 C8.973 1.261 8.973 1.261 7.75 1.375 C6.567 1.479 6.567 1.479 5.359 1.586 C2.914 2.015 1.152 2.786 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#AC4740" transform="translate(553,942)"/>
<path d="M0 0 C0.763 0.206 1.526 0.413 2.312 0.625 C1.322 1.12 1.322 1.12 0.312 1.625 C-0.347 1.295 -1.008 0.965 -1.688 0.625 C-2.018 1.945 -2.347 3.265 -2.688 4.625 C-3.678 4.625 -4.668 4.625 -5.688 4.625 C-5.688 3.635 -5.688 2.645 -5.688 1.625 C-2.688 -0.375 -2.688 -0.375 0 0 Z " fill="#9B3B37" transform="translate(716.6875,935.375)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.133 0.771 1.266 1.542 1.402 2.336 C1.579 3.339 1.756 4.342 1.938 5.375 C2.112 6.373 2.286 7.37 2.465 8.398 C2.877 10.969 2.877 10.969 4 13 C2.68 12.67 1.36 12.34 0 12 C0 8.04 0 4.08 0 0 Z " fill="#B26A55" transform="translate(456,746)"/>
<path d="M0 0 C3.375 0.312 3.375 0.312 7 1 C7.66 1.99 8.32 2.98 9 4 C6.03 3.67 3.06 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EE2A2B" transform="translate(230,109)"/>
<path d="M0 0 C1.875 0.625 1.875 0.625 4 2 C5.25 5.125 5.25 5.125 6 8 C5.01 8.495 5.01 8.495 4 9 C0 2.25 0 2.25 0 0 Z " fill="#9D3C36" transform="translate(374,961)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.99 6 1.98 6 3 C5.01 3 4.02 3 3 3 C3.33 3.66 3.66 4.32 4 5 C2.68 4.34 1.36 3.68 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9E3C38" transform="translate(626,952)"/>
<path d="M0 0 C-3.67 1.223 -6.34 0.88 -10.188 0.562 C-11.46 0.461 -12.732 0.359 -14.043 0.254 C-15.019 0.17 -15.995 0.086 -17 0 C-17 -0.33 -17 -0.66 -17 -1 C-14.918 -1.223 -12.834 -1.428 -10.75 -1.625 C-9.59 -1.741 -8.43 -1.857 -7.234 -1.977 C-4.061 -2 -2.617 -1.697 0 0 Z " fill="#D54F40" transform="translate(258,877)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.464 3.67 -0.072 3.34 -0.625 3 C-3.539 1.773 -5.869 1.858 -9 2 C-8 0 -8 0 -6.188 -0.688 C-3.812 -1.027 -2.267 -0.729 0 0 Z " fill="#A15B49" transform="translate(518,667)"/>
<path d="M0 0 C2.64 0.33 5.28 0.66 8 1 C8 1.99 8 2.98 8 4 C5.69 4 3.38 4 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#8F4938" transform="translate(440,652)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.438 4.625 -0.438 4.625 -2 7 C-3.32 6.67 -4.64 6.34 -6 6 C-2.25 1.125 -2.25 1.125 0 0 Z " fill="#97312E" transform="translate(254,954)"/>
<path d="M0 0 C3.5 0.25 3.5 0.25 5.5 2.25 C2.505 3.248 1.327 3.257 -1.688 2.812 C-2.946 2.627 -4.204 2.441 -5.5 2.25 C-3.5 0.25 -3.5 0.25 0 0 Z " fill="#97302E" transform="translate(244.5,934.75)"/>
<path d="M0 0 C0.949 0.351 1.897 0.701 2.875 1.062 C2.875 2.383 2.875 3.702 2.875 5.062 C2.215 5.062 1.555 5.062 0.875 5.062 C0.215 4.072 -0.445 3.082 -1.125 2.062 C-3.175 0.87 -3.175 0.87 -5.125 0.062 C-3.125 -0.938 -3.125 -0.938 0 0 Z " fill="#8F4435" transform="translate(354.125,733.9375)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-1.33 2.67 -1.66 2.34 -2 2 C-4.328 1.632 -6.662 1.298 -9 1 C-5.668 -2.332 -3.957 -2.638 0 0 Z " fill="#A75F4C" transform="translate(339,668)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.66 3 2.32 3 3 C3.66 3 4.32 3 5 3 C4.34 4.32 3.68 5.64 3 7 C2.567 6.041 2.567 6.041 2.125 5.062 C1.172 2.848 1.172 2.848 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#743025" transform="translate(516,662)"/>
<path d="M0 0 C3.006 3.006 2.596 5.824 3 10 C2.67 9.34 2.34 8.68 2 8 C1.01 7.67 0.02 7.34 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#E92928" transform="translate(740,557)"/>
<path d="M0 0 C3.96 0.33 7.92 0.66 12 1 C12 1.33 12 1.66 12 2 C8.7 2.33 5.4 2.66 2 3 C1.34 2.01 0.68 1.02 0 0 Z " fill="#EC282A" transform="translate(321,309)"/>
<path d="M0 0 C3 -0.312 3 -0.312 6 0 C6.66 0.66 7.32 1.32 8 2 C4.7 2.33 1.4 2.66 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#ED2928" transform="translate(324,109)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.429 2.354 4.087 3.48 3.625 6.25 C3.419 6.827 3.212 7.405 3 8 C0.376 4.939 0 4.268 0 0 Z " fill="#A23F39" transform="translate(745,967)"/>
<path d="M0 0 C2.041 1.616 4.041 3.286 6 5 C6 5.66 6 6.32 6 7 C4.35 6.67 2.7 6.34 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#904536" transform="translate(328,760)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.33 8 0.66 8 1 C6.639 1.433 6.639 1.433 5.25 1.875 C2.678 2.765 0.387 3.715 -2 5 C-1.34 3.35 -0.68 1.7 0 0 Z " fill="#70271C" transform="translate(470,756)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C5.332 2.079 7.668 2.088 10 2 C10 2.66 10 3.32 10 4 C6.7 4 3.4 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#AD6955" transform="translate(507,683)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1 4 -1 4 -4.562 4.062 C-5.697 4.042 -6.831 4.021 -8 4 C-5.537 1.537 -3.293 1.002 0 0 Z " fill="#EF2929" transform="translate(320,568)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.33 3.64 3.66 6.28 4 9 C1.477 7.026 1.007 6.033 0.312 2.812 C0.209 1.884 0.106 0.956 0 0 Z " fill="#EC2E2E" transform="translate(730,529)"/>
<path d="M0 0 C3.006 3.006 2.596 5.824 3 10 C2.67 9.34 2.34 8.68 2 8 C1.34 8 0.68 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#E72727" transform="translate(705,441)"/>
<path d="M0 0 C2.962 0.613 4.381 1.254 7 3 C6.67 3.99 6.34 4.98 6 6 C3.979 4.358 1.98 2.691 0 1 C0 0.67 0 0.34 0 0 Z " fill="#EA2728" transform="translate(293,425)"/>
<path d="M0 0 C0.582 0.752 0.582 0.752 1.176 1.52 C0.186 2.015 0.186 2.015 -0.824 2.52 C-1.484 2.19 -2.144 1.86 -2.824 1.52 C-4.361 1.295 -5.904 1.112 -7.449 0.957 C-8.266 0.873 -9.084 0.789 -9.926 0.703 C-10.552 0.643 -11.179 0.582 -11.824 0.52 C-11.824 0.19 -11.824 -0.14 -11.824 -0.48 C-10.223 -0.73 -8.618 -0.954 -7.012 -1.168 C-6.118 -1.296 -5.225 -1.423 -4.305 -1.555 C-1.824 -1.48 -1.824 -1.48 0 0 Z " fill="#F14240" transform="translate(305.82421875,382.48046875)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2.414 3.066 2.414 3.066 2.625 5.562 C2.7 6.389 2.775 7.215 2.852 8.066 C2.925 9.024 2.925 9.024 3 10 C1.512 8.73 1.512 8.73 0 7 C-0.188 3.312 -0.188 3.312 0 0 Z " fill="#E92223" transform="translate(700,285)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 4.63 2.34 8.26 2 12 C1.67 12 1.34 12 1 12 C0.67 8.04 0.34 4.08 0 0 Z " fill="#F03F3E" transform="translate(452,197)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 3.96 2.34 7.92 2 12 C1.67 12 1.34 12 1 12 C0.67 8.04 0.34 4.08 0 0 Z " fill="#EF403E" transform="translate(205,158)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-3.64 2 -6.28 2 -9 2 C-5.946 -1.054 -3.953 -1.227 0 0 Z " fill="#A23C37" transform="translate(205,941)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5.33 2.32 5.66 3.64 6 5 C5.01 5 4.02 5 3 5 C3 4.34 3 3.68 3 3 C2.01 2.67 1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D34133" transform="translate(172,867)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.611 5.013 0.735 6.397 -1 9 C-1.66 8.67 -2.32 8.34 -3 8 C-2.01 5.36 -1.02 2.72 0 0 Z " fill="#9E5A49" transform="translate(527,696)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.99 7 1.98 7 3 C5.68 3.33 4.36 3.66 3 4 C2.01 2.68 1.02 1.36 0 0 Z " fill="#C67E67" transform="translate(471,668)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.64 1.217 -1.64 1.217 -3.312 1.438 C-6.931 1.883 -6.931 1.883 -10 3 C-10 2.01 -10 1.02 -10 0 C-6.717 -1.642 -3.575 -0.519 0 0 Z " fill="#A8634F" transform="translate(606,668)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.65 2 3.3 2 5 C2.99 5.33 3.98 5.66 5 6 C3.35 6.33 1.7 6.66 0 7 C0 4.69 0 2.38 0 0 Z " fill="#722B1E" transform="translate(557,661)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 0.99 8 1.98 8 3 C5.657 2.744 3.322 2.407 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#7B3224" transform="translate(458,661)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.188 3.375 1.188 3.375 1 7 C0.01 7.66 -0.98 8.32 -2 9 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#EC2524" transform="translate(491,530)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2.594 3.649 2.742 6.292 3 9 C2.67 8.34 2.34 7.68 2 7 C1.34 7 0.68 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#EA2929" transform="translate(712,466)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.67 2.31 0.34 4.62 0 7 C-0.66 7 -1.32 7 -2 7 C-2.33 7.66 -2.66 8.32 -3 9 C-2.502 5.265 -2.125 3.188 0 0 Z " fill="#EA2A29" transform="translate(425,387)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-1.323 4.938 -1.323 4.938 -4.75 5.25 C-5.493 5.168 -6.235 5.085 -7 5 C-2.25 1.125 -2.25 1.125 0 0 Z " fill="#EC2827" transform="translate(373,299)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.745 1.316 1.471 2.628 1.188 3.938 C1.037 4.668 0.886 5.399 0.73 6.152 C0.489 6.762 0.248 7.372 0 8 C-0.99 8.33 -1.98 8.66 -3 9 C-2.301 5.853 -1.239 2.974 0 0 Z " fill="#E82729" transform="translate(834,288)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2.594 3.649 2.742 6.292 3 9 C2.67 8.34 2.34 7.68 2 7 C1.34 7 0.68 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#E92424" transform="translate(692,258)"/>
<path d="M0 0 C3 2 3 2 3.688 5.125 C3.791 6.074 3.894 7.023 4 8 C3.01 7.67 2.02 7.34 1 7 C0.67 4.69 0.34 2.38 0 0 Z " fill="#EB2D2C" transform="translate(697,194)"/>
<path d="M0 0 C1.212 0.041 1.212 0.041 2.449 0.082 C3.064 0.117 3.679 0.152 4.312 0.188 C4.312 0.518 4.312 0.847 4.312 1.188 C2.735 1.404 2.735 1.404 1.125 1.625 C-1.842 2.063 -4.755 2.566 -7.688 3.188 C-5.268 0.205 -3.789 -0.165 0 0 Z " fill="#EB1B1D" transform="translate(220.6875,142.8125)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.96 1 7.92 1 12 C0.67 12 0.34 12 0 12 C0 8.04 0 4.08 0 0 Z " fill="#F24241" transform="translate(705,170)"/>
<path d="M0 0 C2.438 0.375 2.438 0.375 5 1 C5.33 1.66 5.66 2.32 6 3 C3.133 2.427 2.139 2.139 0 0 Z " fill="#D85342" transform="translate(225,877)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.34 4 -0.32 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#B86F59" transform="translate(705,680)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#F14441" transform="translate(579,209)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#F14241" transform="translate(579,199)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.67 8 0.34 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#F04240" transform="translate(579,144)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.99 2 2.98 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#C7594D" transform="translate(438,944)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.33 7 0.66 7 1 C4.69 1 2.38 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DE6B52" transform="translate(691,879)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C2493F" transform="translate(178,953)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.33 6 0.66 6 1 C4.02 1 2.04 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E06D54" transform="translate(684,879)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.34 0.66 3.68 1.32 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#DD654D" transform="translate(734,877)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.33 6 0.66 6 1 C4.02 1 2.04 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E17057" transform="translate(560,877)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.33 6 0.66 6 1 C4.02 1 2.04 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E17057" transform="translate(535,877)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.67 0.66 4.34 1.32 4 2 C2.68 1.34 1.36 0.68 0 0 Z " fill="#D95543" transform="translate(232,877)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#F04643" transform="translate(376,467)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#F14442" transform="translate(579,280)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#F24342" transform="translate(579,265)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C1544A" transform="translate(816,953)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C0564C" transform="translate(740,945)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C7554B" transform="translate(323,932)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DE6B52" transform="translate(700,879)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#D74F3E" transform="translate(211,876)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DC5A47" transform="translate(258,873)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C5725A" transform="translate(657,781)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C1735A" transform="translate(625,754)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BE745C" transform="translate(667,747)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BC6F59" transform="translate(345,741)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BA7159" transform="translate(707,676)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C2745F" transform="translate(532,675)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 1.67 1.02 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BF735C" transform="translate(698,670)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#ED5249" transform="translate(696,574)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#F14643" transform="translate(377,507)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#F24745" transform="translate(376,440)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#F04341" transform="translate(579,178)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#F0443F" transform="translate(659,108)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CA574A" transform="translate(596,980)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C3564C" transform="translate(538,964)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#BF4E46" transform="translate(831,953)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C5564B" transform="translate(428,950)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C2574C" transform="translate(637,944)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#C54D43" transform="translate(834,932)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CB5D4D" transform="translate(530,932)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#C55247" transform="translate(266,932)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#C54F45" transform="translate(234,932)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#E07259" transform="translate(580,882)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E37158" transform="translate(438,882)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#DE664E" transform="translate(740,878)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D94F40" transform="translate(212,878)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#E07157" transform="translate(568,877)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#E27256" transform="translate(545,877)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#E16F57" transform="translate(472,877)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#E27057" transform="translate(450,877)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B96E5A" transform="translate(648,765)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#C47961" transform="translate(666,728)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#C27860" transform="translate(583,690)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BD745C" transform="translate(367,676)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#C2765E" transform="translate(428,670)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C1765E" transform="translate(642,667)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C6755E" transform="translate(728,657)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#EE4340" transform="translate(353,415)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#EF4642" transform="translate(342,415)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#F14742" transform="translate(334,415)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#F14540" transform="translate(323,415)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#EE4642" transform="translate(196,311)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#EF4441" transform="translate(833,300)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#F14442" transform="translate(579,298)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#EF413E" transform="translate(868,252)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#F14241" transform="translate(579,250)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#F24441" transform="translate(579,194)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#F24241" transform="translate(579,187)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#F04645" transform="translate(207,177)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#F14341" transform="translate(579,162)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#F14341" transform="translate(579,131)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C64A3F" transform="translate(194,980)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BA5047" transform="translate(601,968)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BC5146" transform="translate(200,968)"/>
<path d="" fill="#C45349" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BB554A" transform="translate(720,953)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BC5548" transform="translate(420,952)"/>
<path d="" fill="#C1544A" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BF554A" transform="translate(651,949)"/>
<path d="" fill="#C1544C" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BD534A" transform="translate(722,943)"/>
<path d="" fill="#C55349" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C84E43" transform="translate(844,934)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CA5A4E" transform="translate(689,932)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CA5A4D" transform="translate(661,932)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CB5C4E" transform="translate(507,932)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DE5F4C" transform="translate(278,879)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D64739" transform="translate(182,879)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#DB664E" transform="translate(731,877)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#E17058" transform="translate(552,877)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#E26F56" transform="translate(424,877)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#E06A52" transform="translate(364,876)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DC6049" transform="translate(765,875)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#E16650" transform="translate(334,875)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#E06650" transform="translate(326,875)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DE6A51" transform="translate(702,873)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DB5744" transform="translate(799,867)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D44436" transform="translate(183,867)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C4715A" transform="translate(663,781)"/>
<path d="" fill="#C86E56" transform="translate(0,0)"/>
<path d="" fill="#C1755D" transform="translate(0,0)"/>
<path d="" fill="#C5765E" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BC725D" transform="translate(421,754)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BD735D" transform="translate(405,754)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BE745C" transform="translate(641,752)"/>
<path d="" fill="#C37860" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BD725C" transform="translate(562,741)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B96F58" transform="translate(464,741)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BF765E" transform="translate(677,730)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C0745B" transform="translate(574,730)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BF7760" transform="translate(670,728)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C2745C" transform="translate(510,710)"/>
<path d="" fill="#C57862" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BF7760" transform="translate(689,693)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C2775E" transform="translate(623,690)"/>
<path d="" fill="#C1745C" transform="translate(0,0)"/>
<path d="" fill="#B97059" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BF705A" transform="translate(376,680)"/>
<path d="" fill="#C2745B" transform="translate(0,0)"/>
<path d="" fill="#BE755E" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BF735C" transform="translate(662,670)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C1745C" transform="translate(435,671)"/>
<path d="" fill="#C47761" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BE7059" transform="translate(351,668)"/>
<path d="" fill="#C16E57" transform="translate(0,0)"/>
<path d="" fill="#C37860" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C2725A" transform="translate(353,666)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C47A61" transform="translate(657,658)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#F14845" transform="translate(521,525)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#F24644" transform="translate(377,517)"/>
<path d="" fill="#F14643" transform="translate(0,0)"/>
<path d="" fill="#EF403F" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EF4743" transform="translate(278,470)"/>
<path d="" fill="#F24544" transform="translate(0,0)"/>
<path d="" fill="#EE4342" transform="translate(0,0)"/>
<path d="" fill="#F04341" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#F14442" transform="translate(698,292)"/>
<path d="" fill="#F04744" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="" fill="#EF4442" transform="translate(0,0)"/>
<path d="" fill="#F24341" transform="translate(0,0)"/>
<path d="" fill="#F34643" transform="translate(0,0)"/>
<path d="" fill="#F04341" transform="translate(0,0)"/>
<path d="" fill="#F24542" transform="translate(0,0)"/>
<path d="" fill="#F44643" transform="translate(0,0)"/>
<path d="" fill="#F04340" transform="translate(0,0)"/>
<path d="" fill="#F24644" transform="translate(0,0)"/>
<path d="" fill="#F24542" transform="translate(0,0)"/>
<path d="" fill="#F34543" transform="translate(0,0)"/>
<path d="" fill="#F14340" transform="translate(0,0)"/>
<path d="" fill="#F14443" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#EF4341" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#F04441" transform="translate(505,144)"/>
<path d="" fill="#F04240" transform="translate(0,0)"/>
<path d="" fill="#F14440" transform="translate(0,0)"/>
<path d="" fill="#F04240" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#F04440" transform="translate(555,108)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#EF4440" transform="translate(539,108)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#EC413D" transform="translate(207,107)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9574B" transform="translate(413,980)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C55448" transform="translate(710,979)"/>
<path d="" fill="#C54E46" transform="translate(0,0)"/>
<path d="" fill="#C6554A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BB584B" transform="translate(642,968)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BE5247" transform="translate(563,968)"/>
<path d="" fill="#BC5046" transform="translate(0,0)"/>
<path d="" fill="#C0534A" transform="translate(0,0)"/>
<path d="" fill="#C55247" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BE544A" transform="translate(713,960)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BF5146" transform="translate(209,960)"/>
<path d="" fill="#C2564A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2564B" transform="translate(626,957)"/>
<path d="" fill="#C35148" transform="translate(0,0)"/>
<path d="" fill="#BE544A" transform="translate(0,0)"/>
<path d="" fill="#C2544A" transform="translate(0,0)"/>
<path d="" fill="#C7594C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C1584B" transform="translate(573,952)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BE5549" transform="translate(417,952)"/>
<path d="" fill="#C3544A" transform="translate(0,0)"/>
<path d="" fill="#C2574A" transform="translate(0,0)"/>
<path d="" fill="#C4584C" transform="translate(0,0)"/>
<path d="" fill="#C55046" transform="translate(0,0)"/>
<path d="" fill="#C2594E" transform="translate(0,0)"/>
<path d="" fill="#C85C4E" transform="translate(0,0)"/>
<path d="" fill="#C4584C" transform="translate(0,0)"/>
<path d="" fill="#C3574B" transform="translate(0,0)"/>
<path d="" fill="#C85C4E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C64C43" transform="translate(839,932)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9594D" transform="translate(684,932)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CA594C" transform="translate(681,932)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C75A4D" transform="translate(588,932)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CC5C50" transform="translate(476,932)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CB594C" transform="translate(345,932)"/>
<path d="" fill="#C9584B" transform="translate(0,0)"/>
<path d="" fill="#DE6852" transform="translate(0,0)"/>
<path d="" fill="#DF7258" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E06953" transform="translate(340,884)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E16651" transform="translate(332,884)"/>
<path d="" fill="#E27459" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E27459" transform="translate(572,882)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DE5C4A" transform="translate(263,879)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DD5A48" transform="translate(254,879)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DC5D4B" transform="translate(256,878)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DD5847" transform="translate(251,878)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DE634D" transform="translate(745,877)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E06A52" transform="translate(726,877)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E27157" transform="translate(579,877)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E06F55" transform="translate(556,877)"/>
<path d="" fill="#E37056" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E16E55" transform="translate(416,877)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E06D55" transform="translate(620,876)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E06E55" transform="translate(604,876)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E16E56" transform="translate(404,876)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E26852" transform="translate(356,876)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D84D3E" transform="translate(201,875)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DA5746" transform="translate(249,872)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DB5845" transform="translate(240,872)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DB5745" transform="translate(796,868)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D64B3A" transform="translate(836,865)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D84A38" transform="translate(842,864)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C96D57" transform="translate(365,783)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C96E58" transform="translate(364,780)"/>
<path d="" fill="#C36F59" transform="translate(0,0)"/>
<path d="" fill="#C4755B" transform="translate(0,0)"/>
<path d="" fill="#BC705A" transform="translate(0,0)"/>
<path d="" fill="#C5765D" transform="translate(0,0)"/>
<path d="" fill="#BE755C" transform="translate(0,0)"/>
<path d="" fill="#C1735B" transform="translate(0,0)"/>
<path d="" fill="#BF745B" transform="translate(0,0)"/>
<path d="" fill="#BB745C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BD7258" transform="translate(477,752)"/>
<path d="" fill="#C1715A" transform="translate(0,0)"/>
<path d="" fill="#BF715A" transform="translate(0,0)"/>
<path d="" fill="#BE725B" transform="translate(0,0)"/>
<path d="" fill="#C1765D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2765D" transform="translate(600,739)"/>
<path d="" fill="#C2775F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3765E" transform="translate(604,730)"/>
<path d="" fill="#B9735B" transform="translate(0,0)"/>
<path d="" fill="#C1765E" transform="translate(0,0)"/>
<path d="" fill="#C57860" transform="translate(0,0)"/>
<path d="" fill="#BF735B" transform="translate(0,0)"/>
<path d="" fill="#B96A54" transform="translate(0,0)"/>
<path d="" fill="#C3765F" transform="translate(0,0)"/>
<path d="" fill="#C16E58" transform="translate(0,0)"/>
<path d="" fill="#C2765D" transform="translate(0,0)"/>
<path d="" fill="#C06D58" transform="translate(0,0)"/>
<path d="" fill="#C27962" transform="translate(0,0)"/>
<path d="" fill="#C2765F" transform="translate(0,0)"/>
<path d="" fill="#C27860" transform="translate(0,0)"/>
<path d="" fill="#BC7059" transform="translate(0,0)"/>
<path d="" fill="#C1755E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3765E" transform="translate(581,671)"/>
<path d="" fill="#C2755E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BF745C" transform="translate(370,671)"/>
<path d="" fill="#BC735D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BB6C56" transform="translate(331,670)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BF775E" transform="translate(496,664)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BC6E57" transform="translate(349,664)"/>
<path d="" fill="#C87962" transform="translate(0,0)"/>
<path d="" fill="#C37059" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2755D" transform="translate(598,659)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6745D" transform="translate(375,657)"/>
<path d="" fill="#C5735C" transform="translate(0,0)"/>
<path d="" fill="#C66F5A" transform="translate(0,0)"/>
<path d="" fill="#C5775F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F05249" transform="translate(564,575)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EE4F47" transform="translate(308,575)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F05048" transform="translate(418,573)"/>
<path d="" fill="#EF4D46" transform="translate(0,0)"/>
<path d="" fill="#EE4A44" transform="translate(0,0)"/>
<path d="" fill="#F14945" transform="translate(0,0)"/>
<path d="" fill="#F04946" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF4A46" transform="translate(730,541)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EA4D48" transform="translate(440,536)"/>
<path d="" fill="#EB453F" transform="translate(0,0)"/>
<path d="" fill="#F04846" transform="translate(0,0)"/>
<path d="" fill="#EF4746" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04743" transform="translate(275,470)"/>
<path d="" fill="#EF4645" transform="translate(0,0)"/>
<path d="" fill="#F24744" transform="translate(0,0)"/>
<path d="" fill="#EE4843" transform="translate(0,0)"/>
<path d="" fill="#EF4343" transform="translate(0,0)"/>
<path d="" fill="#F14441" transform="translate(0,0)"/>
<path d="" fill="#EF4240" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F14543" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04642" transform="translate(350,415)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EE4542" transform="translate(347,415)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF4843" transform="translate(339,415)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04641" transform="translate(331,415)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EE4641" transform="translate(328,415)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04241" transform="translate(318,415)"/>
<path d="" fill="#EC4643" transform="translate(0,0)"/>
<path d="" fill="#F24342" transform="translate(0,0)"/>
<path d="" fill="#F14542" transform="translate(0,0)"/>
<path d="" fill="#F14744" transform="translate(0,0)"/>
<path d="" fill="#F04644" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04643" transform="translate(605,347)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F24442" transform="translate(325,347)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF4642" transform="translate(284,344)"/>
<path d="" fill="#F34643" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04842" transform="translate(358,311)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F04442" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F24543" transform="translate(311,309)"/>
<path d="" fill="#EE4543" transform="translate(0,0)"/>
<path d="" fill="#ED423D" transform="translate(0,0)"/>
<path d="" fill="#EE423F" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F0413E" transform="translate(0,0)"/>
<path d="" fill="#F14544" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ED4340" transform="translate(168,280)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F14745" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04843" transform="translate(224,272)"/>
<path d="" fill="#F14443" transform="translate(0,0)"/>
<path d="" fill="#EE4642" transform="translate(0,0)"/>
<path d="" fill="#F44543" transform="translate(0,0)"/>
<path d="" fill="#F14442" transform="translate(0,0)"/>
<path d="" fill="#F34442" transform="translate(0,0)"/>
<path d="" fill="#ED4441" transform="translate(0,0)"/>
<path d="" fill="#EC4341" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="" fill="#F04743" transform="translate(0,0)"/>
<path d="" fill="#F14342" transform="translate(0,0)"/>
<path d="" fill="#EE403C" transform="translate(0,0)"/>
<path d="" fill="#F1443F" transform="translate(0,0)"/>
<path d="" fill="#F34543" transform="translate(0,0)"/>
<path d="" fill="#F14744" transform="translate(0,0)"/>
<path d="" fill="#F04743" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF4543" transform="translate(485,215)"/>
<path d="" fill="#F14643" transform="translate(0,0)"/>
<path d="" fill="#F04340" transform="translate(0,0)"/>
<path d="" fill="#F04644" transform="translate(0,0)"/>
<path d="" fill="#EE4444" transform="translate(0,0)"/>
<path d="" fill="#F34745" transform="translate(0,0)"/>
<path d="" fill="#F34444" transform="translate(0,0)"/>
<path d="" fill="#EF4442" transform="translate(0,0)"/>
<path d="" fill="#EF4642" transform="translate(0,0)"/>
<path d="" fill="#F24A46" transform="translate(0,0)"/>
<path d="" fill="#EE4442" transform="translate(0,0)"/>
<path d="" fill="#F14441" transform="translate(0,0)"/>
<path d="" fill="#EC3F3C" transform="translate(0,0)"/>
<path d="" fill="#F04241" transform="translate(0,0)"/>
<path d="" fill="#F34340" transform="translate(0,0)"/>
<path d="" fill="#F24240" transform="translate(0,0)"/>
<path d="" fill="#F14441" transform="translate(0,0)"/>
<path d="" fill="#EE4642" transform="translate(0,0)"/>
<path d="" fill="#F04340" transform="translate(0,0)"/>
<path d="" fill="#F1423F" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF443F" transform="translate(716,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EE413D" transform="translate(667,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF433F" transform="translate(612,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF433F" transform="translate(604,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF4240" transform="translate(596,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04340" transform="translate(548,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04440" transform="translate(532,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F0453F" transform="translate(529,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F14441" transform="translate(524,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F0423F" transform="translate(518,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F1433F" transform="translate(452,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F04340" transform="translate(444,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F14440" transform="translate(436,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#F0443F" transform="translate(428,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EF433F" transform="translate(420,108)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EE413D" transform="translate(230,107)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#EE423F" transform="translate(334,106)"/>
<path d="" fill="#AD150A" transform="translate(0,0)"/>
<path d="" fill="#CB584C" transform="translate(0,0)"/>
<path d="" fill="#CB5548" transform="translate(0,0)"/>
<path d="" fill="#CC554A" transform="translate(0,0)"/>
<path d="" fill="#C9564C" transform="translate(0,0)"/>
<path d="" fill="#C9584B" transform="translate(0,0)"/>
<path d="" fill="#CB584D" transform="translate(0,0)"/>
<path d="" fill="#C9584B" transform="translate(0,0)"/>
<path d="" fill="#CA5548" transform="translate(0,0)"/>
<path d="" fill="#C75448" transform="translate(0,0)"/>
<path d="" fill="#C85449" transform="translate(0,0)"/>
<path d="" fill="#CB554A" transform="translate(0,0)"/>
<path d="" fill="#CA5A4C" transform="translate(0,0)"/>
<path d="" fill="#C8584B" transform="translate(0,0)"/>
<path d="" fill="#CB574C" transform="translate(0,0)"/>
<path d="" fill="#CC574A" transform="translate(0,0)"/>
<path d="" fill="#CA5347" transform="translate(0,0)"/>
<path d="" fill="#CB5549" transform="translate(0,0)"/>
<path d="" fill="#CB584C" transform="translate(0,0)"/>
<path d="" fill="#C8554B" transform="translate(0,0)"/>
<path d="" fill="#CA5047" transform="translate(0,0)"/>
<path d="" fill="#CD493F" transform="translate(0,0)"/>
<path d="" fill="#C94A3D" transform="translate(0,0)"/>
<path d="" fill="#CA564B" transform="translate(0,0)"/>
<path d="" fill="#C55448" transform="translate(0,0)"/>
<path d="" fill="#C85C4F" transform="translate(0,0)"/>
<path d="" fill="#C65549" transform="translate(0,0)"/>
<path d="" fill="#C75548" transform="translate(0,0)"/>
<path d="" fill="#C45148" transform="translate(0,0)"/>
<path d="" fill="#CB5044" transform="translate(0,0)"/>
<path d="" fill="#C15249" transform="translate(0,0)"/>
<path d="" fill="#C25149" transform="translate(0,0)"/>
<path d="" fill="#C3544A" transform="translate(0,0)"/>
<path d="" fill="#C35548" transform="translate(0,0)"/>
<path d="" fill="#C24F45" transform="translate(0,0)"/>
<path d="" fill="#C45346" transform="translate(0,0)"/>
<path d="" fill="#C25449" transform="translate(0,0)"/>
<path d="" fill="#C3564B" transform="translate(0,0)"/>
<path d="" fill="#C25447" transform="translate(0,0)"/>
<path d="" fill="#C34F46" transform="translate(0,0)"/>
<path d="" fill="#C25248" transform="translate(0,0)"/>
<path d="" fill="#C05449" transform="translate(0,0)"/>
<path d="" fill="#C45548" transform="translate(0,0)"/>
<path d="" fill="#C7544A" transform="translate(0,0)"/>
<path d="" fill="#C04F46" transform="translate(0,0)"/>
<path d="" fill="#C0574C" transform="translate(0,0)"/>
<path d="" fill="#C15046" transform="translate(0,0)"/>
<path d="" fill="#BF5146" transform="translate(0,0)"/>
<path d="" fill="#C0594F" transform="translate(0,0)"/>
<path d="" fill="#C7564B" transform="translate(0,0)"/>
<path d="" fill="#B95145" transform="translate(0,0)"/>
<path d="" fill="#BB5246" transform="translate(0,0)"/>
<path d="" fill="#BB5346" transform="translate(0,0)"/>
<path d="" fill="#BF554B" transform="translate(0,0)"/>
<path d="" fill="#C2554B" transform="translate(0,0)"/>
<path d="" fill="#BB2519" transform="translate(0,0)"/>
<path d="" fill="#C65347" transform="translate(0,0)"/>
<path d="" fill="#C15549" transform="translate(0,0)"/>
<path d="" fill="#C65A4E" transform="translate(0,0)"/>
<path d="" fill="#C4574C" transform="translate(0,0)"/>
<path d="" fill="#BF4E46" transform="translate(0,0)"/>
<path d="" fill="#C64940" transform="translate(0,0)"/>
<path d="" fill="#C3584B" transform="translate(0,0)"/>
<path d="" fill="#C35248" transform="translate(0,0)"/>
<path d="" fill="#C35047" transform="translate(0,0)"/>
<path d="" fill="#C35148" transform="translate(0,0)"/>
<path d="" fill="#C55047" transform="translate(0,0)"/>
<path d="" fill="#C25449" transform="translate(0,0)"/>
<path d="" fill="#C45548" transform="translate(0,0)"/>
<path d="" fill="#C65449" transform="translate(0,0)"/>
<path d="" fill="#C5554A" transform="translate(0,0)"/>
<path d="" fill="#C2493E" transform="translate(0,0)"/>
<path d="" fill="#C54B41" transform="translate(0,0)"/>
<path d="" fill="#C55246" transform="translate(0,0)"/>
<path d="" fill="#BC4F46" transform="translate(0,0)"/>
<path d="" fill="#BE4D43" transform="translate(0,0)"/>
<path d="" fill="#C55146" transform="translate(0,0)"/>
<path d="" fill="#C4564B" transform="translate(0,0)"/>
<path d="" fill="#C2554B" transform="translate(0,0)"/>
<path d="" fill="#BF473D" transform="translate(0,0)"/>
<path d="" fill="#BC5349" transform="translate(0,0)"/>
<path d="" fill="#C25448" transform="translate(0,0)"/>
<path d="" fill="#C55146" transform="translate(0,0)"/>
<path d="" fill="#C0594E" transform="translate(0,0)"/>
<path d="" fill="#C0554B" transform="translate(0,0)"/>
<path d="" fill="#B94F46" transform="translate(0,0)"/>
<path d="" fill="#C4574D" transform="translate(0,0)"/>
<path d="" fill="#C45449" transform="translate(0,0)"/>
<path d="" fill="#C0564B" transform="translate(0,0)"/>
<path d="" fill="#C1554B" transform="translate(0,0)"/>
<path d="" fill="#B84C43" transform="translate(0,0)"/>
<path d="" fill="#C4594B" transform="translate(0,0)"/>
<path d="" fill="#C1564B" transform="translate(0,0)"/>
<path d="" fill="#BD5549" transform="translate(0,0)"/>
<path d="" fill="#C85B4E" transform="translate(0,0)"/>
<path d="" fill="#C4584C" transform="translate(0,0)"/>
<path d="" fill="#BF5549" transform="translate(0,0)"/>
<path d="" fill="#C4584B" transform="translate(0,0)"/>
<path d="" fill="#C15447" transform="translate(0,0)"/>
<path d="" fill="#BC5548" transform="translate(0,0)"/>
<path d="" fill="#BD5448" transform="translate(0,0)"/>
<path d="" fill="#C2554A" transform="translate(0,0)"/>
<path d="" fill="#BC5147" transform="translate(0,0)"/>
<path d="" fill="#BD5148" transform="translate(0,0)"/>
<path d="" fill="#BB544B" transform="translate(0,0)"/>
<path d="" fill="#BC5749" transform="translate(0,0)"/>
<path d="" fill="#BC5649" transform="translate(0,0)"/>
<path d="" fill="#C5564A" transform="translate(0,0)"/>
<path d="" fill="#C3584C" transform="translate(0,0)"/>
<path d="" fill="#BF5249" transform="translate(0,0)"/>
<path d="" fill="#C55244" transform="translate(0,0)"/>
<path d="" fill="#C45648" transform="translate(0,0)"/>
<path d="" fill="#C7584B" transform="translate(0,0)"/>
<path d="" fill="#BE5349" transform="translate(0,0)"/>
<path d="" fill="#BF534A" transform="translate(0,0)"/>
<path d="" fill="#C2574C" transform="translate(0,0)"/>
<path d="" fill="#C34F47" transform="translate(0,0)"/>
<path d="" fill="#C05448" transform="translate(0,0)"/>
<path d="" fill="#C55B4E" transform="translate(0,0)"/>
<path d="" fill="#C55449" transform="translate(0,0)"/>
<path d="" fill="#C75047" transform="translate(0,0)"/>
<path d="" fill="#C0554B" transform="translate(0,0)"/>
<path d="" fill="#C35549" transform="translate(0,0)"/>
<path d="" fill="#C55B4E" transform="translate(0,0)"/>
<path d="" fill="#C5564A" transform="translate(0,0)"/>
<path d="" fill="#C35145" transform="translate(0,0)"/>
<path d="" fill="#C5594D" transform="translate(0,0)"/>
<path d="" fill="#C7594C" transform="translate(0,0)"/>
<path d="" fill="#C4554B" transform="translate(0,0)"/>
<path d="" fill="#BD5447" transform="translate(0,0)"/>
<path d="" fill="#C65045" transform="translate(0,0)"/>
<path d="" fill="#BA5249" transform="translate(0,0)"/>
<path d="" fill="#BE564B" transform="translate(0,0)"/>
<path d="" fill="#C85349" transform="translate(0,0)"/>
<path d="" fill="#C7594C" transform="translate(0,0)"/>
<path d="" fill="#C15A4D" transform="translate(0,0)"/>
<path d="" fill="#AC1309" transform="translate(0,0)"/>
<path d="" fill="#BD574A" transform="translate(0,0)"/>
<path d="" fill="#BE4B42" transform="translate(0,0)"/>
<path d="" fill="#C0584C" transform="translate(0,0)"/>
<path d="" fill="#C4584D" transform="translate(0,0)"/>
<path d="" fill="#C35A4C" transform="translate(0,0)"/>
<path d="" fill="#CC5F52" transform="translate(0,0)"/>
<path d="" fill="#AB150A" transform="translate(0,0)"/>
<path d="" fill="#C7574C" transform="translate(0,0)"/>
<path d="" fill="#CE5B4D" transform="translate(0,0)"/>
<path d="" fill="#CD5E50" transform="translate(0,0)"/>
<path d="" fill="#CE5F4E" transform="translate(0,0)"/>
<path d="" fill="#CF5F4F" transform="translate(0,0)"/>
<path d="" fill="#CC5B4D" transform="translate(0,0)"/>
<path d="" fill="#CB574C" transform="translate(0,0)"/>
<path d="" fill="#CD5D50" transform="translate(0,0)"/>
<path d="" fill="#CA554A" transform="translate(0,0)"/>
<path d="" fill="#C7594E" transform="translate(0,0)"/>
<path d="" fill="#CA5B4D" transform="translate(0,0)"/>
<path d="" fill="#C8594C" transform="translate(0,0)"/>
<path d="" fill="#C8594E" transform="translate(0,0)"/>
<path d="" fill="#C75A4C" transform="translate(0,0)"/>
<path d="" fill="#C6584D" transform="translate(0,0)"/>
<path d="" fill="#CB5C4F" transform="translate(0,0)"/>
<path d="" fill="#C95C4F" transform="translate(0,0)"/>
<path d="" fill="#C7594C" transform="translate(0,0)"/>
<path d="" fill="#CC5D4F" transform="translate(0,0)"/>
<path d="" fill="#C9594E" transform="translate(0,0)"/>
<path d="" fill="#CB5A4E" transform="translate(0,0)"/>
<path d="" fill="#CA594B" transform="translate(0,0)"/>
<path d="" fill="#CA584B" transform="translate(0,0)"/>
<path d="" fill="#CB584C" transform="translate(0,0)"/>
<path d="" fill="#C85749" transform="translate(0,0)"/>
<path d="" fill="#C75147" transform="translate(0,0)"/>
<path d="" fill="#C75045" transform="translate(0,0)"/>
<path d="" fill="#C74F46" transform="translate(0,0)"/>
<path d="" fill="#C95146" transform="translate(0,0)"/>
<path d="" fill="#CB5E50" transform="translate(0,0)"/>
<path d="" fill="#CA5E51" transform="translate(0,0)"/>
<path d="" fill="#CB5F4F" transform="translate(0,0)"/>
<path d="" fill="#CC5E4F" transform="translate(0,0)"/>
<path d="" fill="#CF6050" transform="translate(0,0)"/>
<path d="" fill="#CC594C" transform="translate(0,0)"/>
<path d="" fill="#CB5F50" transform="translate(0,0)"/>
<path d="" fill="#CA584C" transform="translate(0,0)"/>
<path d="" fill="#BC261A" transform="translate(0,0)"/>
<path d="" fill="#C12D1F" transform="translate(0,0)"/>
<path d="" fill="#D04539" transform="translate(0,0)"/>
<path d="" fill="#D45A4D" transform="translate(0,0)"/>
<path d="" fill="#BE271A" transform="translate(0,0)"/>
<path d="" fill="#DD614D" transform="translate(0,0)"/>
<path d="" fill="#E06A54" transform="translate(0,0)"/>
<path d="" fill="#DF6A53" transform="translate(0,0)"/>
<path d="" fill="#DF6E55" transform="translate(0,0)"/>
<path d="" fill="#E06F55" transform="translate(0,0)"/>
<path d="" fill="#E16E57" transform="translate(0,0)"/>
<path d="" fill="#E07057" transform="translate(0,0)"/>
<path d="" fill="#E17057" transform="translate(0,0)"/>
<path d="" fill="#DF7056" transform="translate(0,0)"/>
<path d="" fill="#DF7056" transform="translate(0,0)"/>
<path d="" fill="#DD6350" transform="translate(0,0)"/>
<path d="" fill="#DF6D54" transform="translate(0,0)"/>
<path d="" fill="#E27359" transform="translate(0,0)"/>
<path d="" fill="#E17258" transform="translate(0,0)"/>
<path d="" fill="#E47259" transform="translate(0,0)"/>
<path d="" fill="#E07359" transform="translate(0,0)"/>
<path d="" fill="#E27459" transform="translate(0,0)"/>
<path d="" fill="#E17358" transform="translate(0,0)"/>
<path d="" fill="#E27459" transform="translate(0,0)"/>
<path d="" fill="#E17259" transform="translate(0,0)"/>
<path d="" fill="#E17259" transform="translate(0,0)"/>
<path d="" fill="#E17358" transform="translate(0,0)"/>
<path d="" fill="#E2735A" transform="translate(0,0)"/>
<path d="" fill="#E17258" transform="translate(0,0)"/>
<path d="" fill="#E17159" transform="translate(0,0)"/>
<path d="" fill="#E37358" transform="translate(0,0)"/>
<path d="" fill="#E47259" transform="translate(0,0)"/>
<path d="" fill="#E37158" transform="translate(0,0)"/>
<path d="" fill="#E27157" transform="translate(0,0)"/>
<path d="" fill="#E27258" transform="translate(0,0)"/>
<path d="" fill="#E37058" transform="translate(0,0)"/>
<path d="" fill="#E26F57" transform="translate(0,0)"/>
<path d="" fill="#E17359" transform="translate(0,0)"/>
<path d="" fill="#E3745A" transform="translate(0,0)"/>
<path d="" fill="#E27257" transform="translate(0,0)"/>
<path d="" fill="#DE5F4C" transform="translate(0,0)"/>
<path d="" fill="#DE624E" transform="translate(0,0)"/>
<path d="" fill="#E4745A" transform="translate(0,0)"/>
<path d="" fill="#E37457" transform="translate(0,0)"/>
<path d="" fill="#E3735A" transform="translate(0,0)"/>
<path d="" fill="#E36E56" transform="translate(0,0)"/>
<path d="" fill="#E36E55" transform="translate(0,0)"/>
<path d="" fill="#E26C54" transform="translate(0,0)"/>
<path d="" fill="#E16D56" transform="translate(0,0)"/>
<path d="" fill="#E06F57" transform="translate(0,0)"/>
<path d="" fill="#E17359" transform="translate(0,0)"/>
<path d="" fill="#E07157" transform="translate(0,0)"/>
<path d="" fill="#E27458" transform="translate(0,0)"/>
<path d="" fill="#E4745B" transform="translate(0,0)"/>
<path d="" fill="#E17157" transform="translate(0,0)"/>
<path d="" fill="#E36F55" transform="translate(0,0)"/>
<path d="" fill="#E26D55" transform="translate(0,0)"/>
<path d="" fill="#E46D55" transform="translate(0,0)"/>
<path d="" fill="#E16851" transform="translate(0,0)"/>
<path d="" fill="#E17158" transform="translate(0,0)"/>
<path d="" fill="#E1725A" transform="translate(0,0)"/>
<path d="" fill="#E17156" transform="translate(0,0)"/>
<path d="" fill="#E17259" transform="translate(0,0)"/>
<path d="" fill="#E27157" transform="translate(0,0)"/>
<path d="" fill="#E37158" transform="translate(0,0)"/>
<path d="" fill="#E16E58" transform="translate(0,0)"/>
<path d="" fill="#E16D56" transform="translate(0,0)"/>
<path d="" fill="#E26B53" transform="translate(0,0)"/>
<path d="" fill="#E26952" transform="translate(0,0)"/>
<path d="" fill="#DD624B" transform="translate(0,0)"/>
<path d="" fill="#DE634A" transform="translate(0,0)"/>
<path d="" fill="#DE674C" transform="translate(0,0)"/>
<path d="" fill="#DF6A53" transform="translate(0,0)"/>
<path d="" fill="#E06A51" transform="translate(0,0)"/>
<path d="" fill="#DE6A52" transform="translate(0,0)"/>
<path d="" fill="#E0604C" transform="translate(0,0)"/>
<path d="" fill="#DD5D4A" transform="translate(0,0)"/>
<path d="" fill="#DB5947" transform="translate(0,0)"/>
<path d="" fill="#DB5746" transform="translate(0,0)"/>
<path d="" fill="#D85343" transform="translate(0,0)"/>
<path d="" fill="#D85141" transform="translate(0,0)"/>
<path d="" fill="#AD160D" transform="translate(0,0)"/>
<path d="" fill="#DD644E" transform="translate(0,0)"/>
<path d="" fill="#E26A52" transform="translate(0,0)"/>
<path d="" fill="#D64638" transform="translate(0,0)"/>
<path d="" fill="#D44335" transform="translate(0,0)"/>
<path d="" fill="#DE624C" transform="translate(0,0)"/>
<path d="" fill="#E16F56" transform="translate(0,0)"/>
<path d="" fill="#E27156" transform="translate(0,0)"/>
<path d="" fill="#E37057" transform="translate(0,0)"/>
<path d="" fill="#E17057" transform="translate(0,0)"/>
<path d="" fill="#E37258" transform="translate(0,0)"/>
<path d="" fill="#E27157" transform="translate(0,0)"/>
<path d="" fill="#E27157" transform="translate(0,0)"/>
<path d="" fill="#E37056" transform="translate(0,0)"/>
<path d="" fill="#E37057" transform="translate(0,0)"/>
<path d="" fill="#E37156" transform="translate(0,0)"/>
<path d="" fill="#E47057" transform="translate(0,0)"/>
<path d="" fill="#E27058" transform="translate(0,0)"/>
<path d="" fill="#E26F57" transform="translate(0,0)"/>
<path d="" fill="#E47158" transform="translate(0,0)"/>
<path d="" fill="#E26F55" transform="translate(0,0)"/>
<path d="" fill="#DB5645" transform="translate(0,0)"/>
<path d="" fill="#DD5B47" transform="translate(0,0)"/>
<path d="" fill="#DD5C48" transform="translate(0,0)"/>
<path d="" fill="#E0644B" transform="translate(0,0)"/>
<path d="" fill="#DD624C" transform="translate(0,0)"/>
<path d="" fill="#DE674E" transform="translate(0,0)"/>
<path d="" fill="#E27056" transform="translate(0,0)"/>
<path d="" fill="#E07058" transform="translate(0,0)"/>
<path d="" fill="#E06E56" transform="translate(0,0)"/>
<path d="" fill="#E06F56" transform="translate(0,0)"/>
<path d="" fill="#E26F58" transform="translate(0,0)"/>
<path d="" fill="#E46F57" transform="translate(0,0)"/>
<path d="" fill="#E26E55" transform="translate(0,0)"/>
<path d="" fill="#E36B54" transform="translate(0,0)"/>
<path d="" fill="#E26952" transform="translate(0,0)"/>
<path d="" fill="#E26A50" transform="translate(0,0)"/>
<path d="" fill="#E16752" transform="translate(0,0)"/>
<path d="" fill="#D84E40" transform="translate(0,0)"/>
<path d="" fill="#DD604B" transform="translate(0,0)"/>
<path d="" fill="#DE6C53" transform="translate(0,0)"/>
<path d="" fill="#DE6A53" transform="translate(0,0)"/>
<path d="" fill="#E06D53" transform="translate(0,0)"/>
<path d="" fill="#E06E55" transform="translate(0,0)"/>
<path d="" fill="#E16E55" transform="translate(0,0)"/>
<path d="" fill="#E0664F" transform="translate(0,0)"/>
<path d="" fill="#E06750" transform="translate(0,0)"/>
<path d="" fill="#E06850" transform="translate(0,0)"/>
<path d="" fill="#DF634D" transform="translate(0,0)"/>
<path d="" fill="#DB5C47" transform="translate(0,0)"/>
<path d="" fill="#E06850" transform="translate(0,0)"/>
<path d="" fill="#DF614C" transform="translate(0,0)"/>
<path d="" fill="#DF614D" transform="translate(0,0)"/>
<path d="" fill="#DC674F" transform="translate(0,0)"/>
<path d="" fill="#DE6952" transform="translate(0,0)"/>
<path d="" fill="#DE6A50" transform="translate(0,0)"/>
<path d="" fill="#DD5B49" transform="translate(0,0)"/>
<path d="" fill="#DE5A49" transform="translate(0,0)"/>
<path d="" fill="#DD5C49" transform="translate(0,0)"/>
<path d="" fill="#DE5A45" transform="translate(0,0)"/>
<path d="" fill="#E26951" transform="translate(0,0)"/>
<path d="" fill="#DB5644" transform="translate(0,0)"/>
<path d="" fill="#D75543" transform="translate(0,0)"/>
<path d="" fill="#D54C3B" transform="translate(0,0)"/>
<path d="" fill="#D74F3C" transform="translate(0,0)"/>
<path d="" fill="#D6503E" transform="translate(0,0)"/>
<path d="" fill="#D85441" transform="translate(0,0)"/>
<path d="" fill="#B31C11" transform="translate(0,0)"/>
<path d="" fill="#E05E49" transform="translate(0,0)"/>
<path d="" fill="#E0604A" transform="translate(0,0)"/>
<path d="" fill="#E16952" transform="translate(0,0)"/>
<path d="" fill="#E26B52" transform="translate(0,0)"/>
<path d="" fill="#E56953" transform="translate(0,0)"/>
<path d="" fill="#E46550" transform="translate(0,0)"/>
<path d="" fill="#DC5141" transform="translate(0,0)"/>
<path d="" fill="#DC4E3F" transform="translate(0,0)"/>
<path d="" fill="#DD5A46" transform="translate(0,0)"/>
<path d="" fill="#DF5C45" transform="translate(0,0)"/>
<path d="" fill="#E05C48" transform="translate(0,0)"/>
<path d="" fill="#D7483B" transform="translate(0,0)"/>
<path d="" fill="#D24334" transform="translate(0,0)"/>
<path d="" fill="#D44233" transform="translate(0,0)"/>
<path d="" fill="#DB5440" transform="translate(0,0)"/>
<path d="" fill="#D34937" transform="translate(0,0)"/>
<path d="" fill="#D94D3B" transform="translate(0,0)"/>
<path d="" fill="#D54435" transform="translate(0,0)"/>
<path d="" fill="#D95340" transform="translate(0,0)"/>
<path d="" fill="#D85541" transform="translate(0,0)"/>
<path d="" fill="#DB4F3D" transform="translate(0,0)"/>
<path d="" fill="#D9503E" transform="translate(0,0)"/>
<path d="" fill="#E16C54" transform="translate(0,0)"/>
<path d="" fill="#DD6A52" transform="translate(0,0)"/>
<path d="" fill="#DE6149" transform="translate(0,0)"/>
<path d="" fill="#DE6E54" transform="translate(0,0)"/>
<path d="" fill="#AB160A" transform="translate(0,0)"/>
<path d="" fill="#C6715A" transform="translate(0,0)"/>
<path d="" fill="#CA6E58" transform="translate(0,0)"/>
<path d="" fill="#C96E54" transform="translate(0,0)"/>
<path d="" fill="#CA6E56" transform="translate(0,0)"/>
<path d="" fill="#C4705A" transform="translate(0,0)"/>
<path d="" fill="#C86E58" transform="translate(0,0)"/>
<path d="" fill="#CA715B" transform="translate(0,0)"/>
<path d="" fill="#C5725C" transform="translate(0,0)"/>
<path d="" fill="#C6765D" transform="translate(0,0)"/>
<path d="" fill="#CB735A" transform="translate(0,0)"/>
<path d="" fill="#C0745B" transform="translate(0,0)"/>
<path d="" fill="#C37862" transform="translate(0,0)"/>
<path d="" fill="#BF755C" transform="translate(0,0)"/>
<path d="" fill="#B92417" transform="translate(0,0)"/>
<path d="" fill="#BD735E" transform="translate(0,0)"/>
<path d="" fill="#C3755F" transform="translate(0,0)"/>
<path d="" fill="#C4745F" transform="translate(0,0)"/>
<path d="" fill="#C2735B" transform="translate(0,0)"/>
<path d="" fill="#BD6954" transform="translate(0,0)"/>
<path d="" fill="#C4765D" transform="translate(0,0)"/>
<path d="" fill="#BF6F57" transform="translate(0,0)"/>
<path d="" fill="#C2715A" transform="translate(0,0)"/>
<path d="" fill="#BD291B" transform="translate(0,0)"/>
<path d="" fill="#C4775E" transform="translate(0,0)"/>
<path d="" fill="#BE755D" transform="translate(0,0)"/>
<path d="" fill="#C0745D" transform="translate(0,0)"/>
<path d="" fill="#BC6F56" transform="translate(0,0)"/>
<path d="" fill="#C6745C" transform="translate(0,0)"/>
<path d="" fill="#BB715A" transform="translate(0,0)"/>
<path d="" fill="#C2745D" transform="translate(0,0)"/>
<path d="" fill="#BE745D" transform="translate(0,0)"/>
<path d="" fill="#C96C55" transform="translate(0,0)"/>
<path d="" fill="#C5735A" transform="translate(0,0)"/>
<path d="" fill="#C07259" transform="translate(0,0)"/>
<path d="" fill="#BE7259" transform="translate(0,0)"/>
<path d="" fill="#C66E57" transform="translate(0,0)"/>
<path d="" fill="#C5735D" transform="translate(0,0)"/>
<path d="" fill="#C3735A" transform="translate(0,0)"/>
<path d="" fill="#C2755D" transform="translate(0,0)"/>
<path d="" fill="#C1755D" transform="translate(0,0)"/>
<path d="" fill="#BF765D" transform="translate(0,0)"/>
<path d="" fill="#BA6F57" transform="translate(0,0)"/>
<path d="" fill="#BF725B" transform="translate(0,0)"/>
<path d="" fill="#C57A62" transform="translate(0,0)"/>
<path d="" fill="#BA7258" transform="translate(0,0)"/>
<path d="" fill="#B96B52" transform="translate(0,0)"/>
<path d="" fill="#BE705A" transform="translate(0,0)"/>
<path d="" fill="#BE7058" transform="translate(0,0)"/>
<path d="" fill="#BE705B" transform="translate(0,0)"/>
<path d="" fill="#BC6C56" transform="translate(0,0)"/>
<path d="" fill="#B82517" transform="translate(0,0)"/>
<path d="" fill="#C0735B" transform="translate(0,0)"/>
<path d="" fill="#BB705A" transform="translate(0,0)"/>
<path d="" fill="#C0765C" transform="translate(0,0)"/>
<path d="" fill="#C0745C" transform="translate(0,0)"/>
<path d="" fill="#BB705B" transform="translate(0,0)"/>
<path d="" fill="#C0745E" transform="translate(0,0)"/>
<path d="" fill="#C1765F" transform="translate(0,0)"/>
<path d="" fill="#BC6E59" transform="translate(0,0)"/>
<path d="" fill="#BD7059" transform="translate(0,0)"/>
<path d="" fill="#C2775E" transform="translate(0,0)"/>
<path d="" fill="#C0755D" transform="translate(0,0)"/>
<path d="" fill="#BD745D" transform="translate(0,0)"/>
<path d="" fill="#C1765D" transform="translate(0,0)"/>
<path d="" fill="#BF755E" transform="translate(0,0)"/>
<path d="" fill="#C0755D" transform="translate(0,0)"/>
<path d="" fill="#BC6F57" transform="translate(0,0)"/>
<path d="" fill="#C5785E" transform="translate(0,0)"/>
<path d="" fill="#C2765E" transform="translate(0,0)"/>
<path d="" fill="#C57860" transform="translate(0,0)"/>
<path d="" fill="#C3785F" transform="translate(0,0)"/>
<path d="" fill="#C5785F" transform="translate(0,0)"/>
<path d="" fill="#BE745E" transform="translate(0,0)"/>
<path d="" fill="#C3775F" transform="translate(0,0)"/>
<path d="" fill="#C5765D" transform="translate(0,0)"/>
<path d="" fill="#C7775F" transform="translate(0,0)"/>
<path d="" fill="#BB6C56" transform="translate(0,0)"/>
<path d="" fill="#BF735D" transform="translate(0,0)"/>
<path d="" fill="#C0735C" transform="translate(0,0)"/>
<path d="" fill="#BE755B" transform="translate(0,0)"/>
<path d="" fill="#BF765D" transform="translate(0,0)"/>
<path d="" fill="#C0755D" transform="translate(0,0)"/>
<path d="" fill="#BE7358" transform="translate(0,0)"/>
<path d="" fill="#C0765D" transform="translate(0,0)"/>
<path d="" fill="#C3765D" transform="translate(0,0)"/>
<path d="" fill="#BF755C" transform="translate(0,0)"/>
<path d="" fill="#BF735B" transform="translate(0,0)"/>
<path d="" fill="#C37961" transform="translate(0,0)"/>
<path d="" fill="#C47A62" transform="translate(0,0)"/>
<path d="" fill="#C27660" transform="translate(0,0)"/>
<path d="" fill="#C27A62" transform="translate(0,0)"/>
<path d="" fill="#C1745D" transform="translate(0,0)"/>
<path d="" fill="#BE745D" transform="translate(0,0)"/>
<path d="" fill="#C67C64" transform="translate(0,0)"/>
<path d="" fill="#C27961" transform="translate(0,0)"/>
<path d="" fill="#C37861" transform="translate(0,0)"/>
<path d="" fill="#C07760" transform="translate(0,0)"/>
<path d="" fill="#C2775F" transform="translate(0,0)"/>
<path d="" fill="#C17560" transform="translate(0,0)"/>
<path d="" fill="#BF725A" transform="translate(0,0)"/>
<path d="" fill="#C73628" transform="translate(0,0)"/>
<path d="" fill="#C6785F" transform="translate(0,0)"/>
<path d="" fill="#BA271C" transform="translate(0,0)"/>
<path d="" fill="#C4765F" transform="translate(0,0)"/>
<path d="" fill="#C47860" transform="translate(0,0)"/>
<path d="" fill="#C77A60" transform="translate(0,0)"/>
<path d="" fill="#C57961" transform="translate(0,0)"/>
<path d="" fill="#BD2B1D" transform="translate(0,0)"/>
<path d="" fill="#C57761" transform="translate(0,0)"/>
<path d="" fill="#C5775D" transform="translate(0,0)"/>
<path d="" fill="#C3775F" transform="translate(0,0)"/>
<path d="" fill="#C6775F" transform="translate(0,0)"/>
<path d="" fill="#C73225" transform="translate(0,0)"/>
<path d="" fill="#C57760" transform="translate(0,0)"/>
<path d="" fill="#BF2C20" transform="translate(0,0)"/>
<path d="" fill="#D24E3C" transform="translate(0,0)"/>
<path d="" fill="#CB725A" transform="translate(0,0)"/>
<path d="" fill="#C8775F" transform="translate(0,0)"/>
<path d="" fill="#BF755E" transform="translate(0,0)"/>
<path d="" fill="#C0755C" transform="translate(0,0)"/>
<path d="" fill="#BA735C" transform="translate(0,0)"/>
<path d="" fill="#C2765E" transform="translate(0,0)"/>
<path d="" fill="#BC745D" transform="translate(0,0)"/>
<path d="" fill="#BD745D" transform="translate(0,0)"/>
<path d="" fill="#C27862" transform="translate(0,0)"/>
<path d="" fill="#C5765F" transform="translate(0,0)"/>
<path d="" fill="#C2775E" transform="translate(0,0)"/>
<path d="" fill="#C3745E" transform="translate(0,0)"/>
<path d="" fill="#BC7057" transform="translate(0,0)"/>
<path d="" fill="#BB6F58" transform="translate(0,0)"/>
<path d="" fill="#C0755D" transform="translate(0,0)"/>
<path d="" fill="#C2765E" transform="translate(0,0)"/>
<path d="" fill="#C3775E" transform="translate(0,0)"/>
<path d="" fill="#BF7861" transform="translate(0,0)"/>
<path d="" fill="#BD745D" transform="translate(0,0)"/>
<path d="" fill="#C3765E" transform="translate(0,0)"/>
<path d="" fill="#C3765E" transform="translate(0,0)"/>
<path d="" fill="#C3775E" transform="translate(0,0)"/>
<path d="" fill="#C3735E" transform="translate(0,0)"/>
<path d="" fill="#C67760" transform="translate(0,0)"/>
<path d="" fill="#C67860" transform="translate(0,0)"/>
<path d="" fill="#C2765E" transform="translate(0,0)"/>
<path d="" fill="#BF735C" transform="translate(0,0)"/>
<path d="" fill="#C1775E" transform="translate(0,0)"/>
<path d="" fill="#C47861" transform="translate(0,0)"/>
<path d="" fill="#BF745B" transform="translate(0,0)"/>
<path d="" fill="#C07760" transform="translate(0,0)"/>
<path d="" fill="#C0775E" transform="translate(0,0)"/>
<path d="" fill="#C1755E" transform="translate(0,0)"/>
<path d="" fill="#C0735D" transform="translate(0,0)"/>
<path d="" fill="#C27960" transform="translate(0,0)"/>
<path d="" fill="#C27961" transform="translate(0,0)"/>
<path d="" fill="#C0765F" transform="translate(0,0)"/>
<path d="" fill="#BE7058" transform="translate(0,0)"/>
<path d="" fill="#C67A62" transform="translate(0,0)"/>
<path d="" fill="#C77861" transform="translate(0,0)"/>
<path d="" fill="#C2755D" transform="translate(0,0)"/>
<path d="" fill="#C27860" transform="translate(0,0)"/>
<path d="" fill="#C2745C" transform="translate(0,0)"/>
<path d="" fill="#C17159" transform="translate(0,0)"/>
<path d="" fill="#BF7159" transform="translate(0,0)"/>
<path d="" fill="#C27860" transform="translate(0,0)"/>
<path d="" fill="#C3775F" transform="translate(0,0)"/>
<path d="" fill="#BB725C" transform="translate(0,0)"/>
<path d="" fill="#BF715A" transform="translate(0,0)"/>
<path d="" fill="#BD765E" transform="translate(0,0)"/>
<path d="" fill="#C2785F" transform="translate(0,0)"/>
<path d="" fill="#C2775F" transform="translate(0,0)"/>
<path d="" fill="#BD7058" transform="translate(0,0)"/>
<path d="" fill="#C26F58" transform="translate(0,0)"/>
<path d="" fill="#C37761" transform="translate(0,0)"/>
<path d="" fill="#BA715A" transform="translate(0,0)"/>
<path d="" fill="#B8705B" transform="translate(0,0)"/>
<path d="" fill="#BA705B" transform="translate(0,0)"/>
<path d="" fill="#B9715D" transform="translate(0,0)"/>
<path d="" fill="#C1735D" transform="translate(0,0)"/>
<path d="" fill="#BE7159" transform="translate(0,0)"/>
<path d="" fill="#C16F59" transform="translate(0,0)"/>
<path d="" fill="#C67760" transform="translate(0,0)"/>
<path d="" fill="#C6775F" transform="translate(0,0)"/>
<path d="" fill="#C2715B" transform="translate(0,0)"/>
<path d="" fill="#C46C59" transform="translate(0,0)"/>
<path d="" fill="#C16C56" transform="translate(0,0)"/>
<path d="" fill="#BD6A56" transform="translate(0,0)"/>
<path d="" fill="#C66856" transform="translate(0,0)"/>
<path d="" fill="#BC725B" transform="translate(0,0)"/>
<path d="" fill="#C47861" transform="translate(0,0)"/>
<path d="" fill="#C16E56" transform="translate(0,0)"/>
<path d="" fill="#BF735C" transform="translate(0,0)"/>
<path d="" fill="#C2715A" transform="translate(0,0)"/>
<path d="" fill="#C27760" transform="translate(0,0)"/>
<path d="" fill="#C2654F" transform="translate(0,0)"/>
<path d="" fill="#C2765F" transform="translate(0,0)"/>
<path d="" fill="#C1735A" transform="translate(0,0)"/>
<path d="" fill="#C3765E" transform="translate(0,0)"/>
<path d="" fill="#C1765F" transform="translate(0,0)"/>
<path d="" fill="#BF705B" transform="translate(0,0)"/>
<path d="" fill="#BD735C" transform="translate(0,0)"/>
<path d="" fill="#BF775F" transform="translate(0,0)"/>
<path d="" fill="#BE735D" transform="translate(0,0)"/>
<path d="" fill="#C2735D" transform="translate(0,0)"/>
<path d="" fill="#BC6D57" transform="translate(0,0)"/>
<path d="" fill="#B21C12" transform="translate(0,0)"/>
<path d="" fill="#BF725C" transform="translate(0,0)"/>
<path d="" fill="#BF715E" transform="translate(0,0)"/>
<path d="" fill="#BE715B" transform="translate(0,0)"/>
<path d="" fill="#C5755D" transform="translate(0,0)"/>
<path d="" fill="#BB735B" transform="translate(0,0)"/>
<path d="" fill="#BD735D" transform="translate(0,0)"/>
<path d="" fill="#C66954" transform="translate(0,0)"/>
<path d="" fill="#C87B63" transform="translate(0,0)"/>
<path d="" fill="#C47962" transform="translate(0,0)"/>
<path d="" fill="#C27660" transform="translate(0,0)"/>
<path d="" fill="#C0765E" transform="translate(0,0)"/>
<path d="" fill="#C6785E" transform="translate(0,0)"/>
<path d="" fill="#BB705A" transform="translate(0,0)"/>
<path d="" fill="#C47960" transform="translate(0,0)"/>
<path d="" fill="#C2735D" transform="translate(0,0)"/>
<path d="" fill="#C76855" transform="translate(0,0)"/>
<path d="" fill="#C37760" transform="translate(0,0)"/>
<path d="" fill="#C57861" transform="translate(0,0)"/>
<path d="" fill="#C57962" transform="translate(0,0)"/>
<path d="" fill="#C1735E" transform="translate(0,0)"/>
<path d="" fill="#C5775F" transform="translate(0,0)"/>
<path d="" fill="#BB6A55" transform="translate(0,0)"/>
<path d="" fill="#C46F59" transform="translate(0,0)"/>
<path d="" fill="#BE745C" transform="translate(0,0)"/>
<path d="" fill="#C2775F" transform="translate(0,0)"/>
<path d="" fill="#C5765E" transform="translate(0,0)"/>
<path d="" fill="#C47A61" transform="translate(0,0)"/>
<path d="" fill="#C3735A" transform="translate(0,0)"/>
<path d="" fill="#BE6D58" transform="translate(0,0)"/>
<path d="" fill="#C57760" transform="translate(0,0)"/>
<path d="" fill="#BD715A" transform="translate(0,0)"/>
<path d="" fill="#BF735B" transform="translate(0,0)"/>
<path d="" fill="#BE6C55" transform="translate(0,0)"/>
<path d="" fill="#C1735C" transform="translate(0,0)"/>
<path d="" fill="#CD3D2D" transform="translate(0,0)"/>
<path d="" fill="#CA7B63" transform="translate(0,0)"/>
<path d="" fill="#C57861" transform="translate(0,0)"/>
<path d="" fill="#C5735C" transform="translate(0,0)"/>
<path d="" fill="#C2725A" transform="translate(0,0)"/>
<path d="" fill="#C67C62" transform="translate(0,0)"/>
<path d="" fill="#C5745E" transform="translate(0,0)"/>
<path d="" fill="#C47960" transform="translate(0,0)"/>
<path d="" fill="#BF6C58" transform="translate(0,0)"/>
<path d="" fill="#C67863" transform="translate(0,0)"/>
<path d="" fill="#C57A61" transform="translate(0,0)"/>
<path d="" fill="#BC7158" transform="translate(0,0)"/>
<path d="" fill="#C5765E" transform="translate(0,0)"/>
<path d="" fill="#C66754" transform="translate(0,0)"/>
<path d="" fill="#C87861" transform="translate(0,0)"/>
<path d="" fill="#C16E5A" transform="translate(0,0)"/>
<path d="" fill="#C37860" transform="translate(0,0)"/>
<path d="" fill="#C5765E" transform="translate(0,0)"/>
<path d="" fill="#C2745F" transform="translate(0,0)"/>
<path d="" fill="#C87961" transform="translate(0,0)"/>
<path d="" fill="#C87761" transform="translate(0,0)"/>
<path d="" fill="#C27760" transform="translate(0,0)"/>
<path d="" fill="#C1765F" transform="translate(0,0)"/>
<path d="" fill="#C1765D" transform="translate(0,0)"/>
<path d="" fill="#C0725B" transform="translate(0,0)"/>
<path d="" fill="#C1735C" transform="translate(0,0)"/>
<path d="" fill="#C1765D" transform="translate(0,0)"/>
<path d="" fill="#C1725D" transform="translate(0,0)"/>
<path d="" fill="#BD6D57" transform="translate(0,0)"/>
<path d="" fill="#C06F5A" transform="translate(0,0)"/>
<path d="" fill="#C3705B" transform="translate(0,0)"/>
<path d="" fill="#C26F5B" transform="translate(0,0)"/>
<path d="" fill="#C67761" transform="translate(0,0)"/>
<path d="" fill="#C57A61" transform="translate(0,0)"/>
<path d="" fill="#C57660" transform="translate(0,0)"/>
<path d="" fill="#C4765F" transform="translate(0,0)"/>
<path d="" fill="#C3745D" transform="translate(0,0)"/>
<path d="" fill="#C8795F" transform="translate(0,0)"/>
<path d="" fill="#C4755D" transform="translate(0,0)"/>
<path d="" fill="#CA7960" transform="translate(0,0)"/>
<path d="" fill="#C67962" transform="translate(0,0)"/>
<path d="" fill="#C3735D" transform="translate(0,0)"/>
<path d="" fill="#C47760" transform="translate(0,0)"/>
<path d="" fill="#C87960" transform="translate(0,0)"/>
<path d="" fill="#C77861" transform="translate(0,0)"/>
<path d="" fill="#C5775F" transform="translate(0,0)"/>
<path d="" fill="#C9765E" transform="translate(0,0)"/>
<path d="" fill="#C97761" transform="translate(0,0)"/>
<path d="" fill="#CC7861" transform="translate(0,0)"/>
<path d="" fill="#C36F5B" transform="translate(0,0)"/>
<path d="" fill="#CD6955" transform="translate(0,0)"/>
<path d="" fill="#CE6652" transform="translate(0,0)"/>
<path d="" fill="#CB7760" transform="translate(0,0)"/>
<path d="" fill="#CB7860" transform="translate(0,0)"/>
<path d="" fill="#CD765E" transform="translate(0,0)"/>
<path d="" fill="#C63123" transform="translate(0,0)"/>
<path d="" fill="#DE604D" transform="translate(0,0)"/>
<path d="" fill="#DB6B55" transform="translate(0,0)"/>
<path d="" fill="#C93527" transform="translate(0,0)"/>
<path d="" fill="#AF1A11" transform="translate(0,0)"/>
<path d="" fill="#B11C13" transform="translate(0,0)"/>
<path d="" fill="#CB3729" transform="translate(0,0)"/>
<path d="" fill="#CD3A2A" transform="translate(0,0)"/>
<path d="" fill="#D13C2E" transform="translate(0,0)"/>
<path d="" fill="#DB4F3E" transform="translate(0,0)"/>
<path d="" fill="#E06C57" transform="translate(0,0)"/>
<path d="" fill="#AE150C" transform="translate(0,0)"/>
<path d="" fill="#D53F31" transform="translate(0,0)"/>
<path d="" fill="#D13A2D" transform="translate(0,0)"/>
<path d="" fill="#B11A10" transform="translate(0,0)"/>
<path d="" fill="#E65247" transform="translate(0,0)"/>
<path d="" fill="#D1382B" transform="translate(0,0)"/>
<path d="" fill="#B41F13" transform="translate(0,0)"/>
<path d="" fill="#D64032" transform="translate(0,0)"/>
<path d="" fill="#F05047" transform="translate(0,0)"/>
<path d="" fill="#F05047" transform="translate(0,0)"/>
<path d="" fill="#EB4D45" transform="translate(0,0)"/>
<path d="" fill="#B92519" transform="translate(0,0)"/>
<path d="" fill="#D23B2C" transform="translate(0,0)"/>
<path d="" fill="#ED5047" transform="translate(0,0)"/>
<path d="" fill="#ED5148" transform="translate(0,0)"/>
<path d="" fill="#EE5249" transform="translate(0,0)"/>
<path d="" fill="#ED5148" transform="translate(0,0)"/>
<path d="" fill="#EE5249" transform="translate(0,0)"/>
<path d="" fill="#EF5248" transform="translate(0,0)"/>
<path d="" fill="#EE544B" transform="translate(0,0)"/>
<path d="" fill="#EE534D" transform="translate(0,0)"/>
<path d="" fill="#EF5449" transform="translate(0,0)"/>
<path d="" fill="#EF534A" transform="translate(0,0)"/>
<path d="" fill="#EF544A" transform="translate(0,0)"/>
<path d="" fill="#D73F32" transform="translate(0,0)"/>
<path d="" fill="#EF4F49" transform="translate(0,0)"/>
<path d="" fill="#EF5149" transform="translate(0,0)"/>
<path d="" fill="#EE4A44" transform="translate(0,0)"/>
<path d="" fill="#F04F46" transform="translate(0,0)"/>
<path d="" fill="#EB4F47" transform="translate(0,0)"/>
<path d="" fill="#EF4E48" transform="translate(0,0)"/>
<path d="" fill="#EF4B48" transform="translate(0,0)"/>
<path d="" fill="#F04E48" transform="translate(0,0)"/>
<path d="" fill="#EF4C46" transform="translate(0,0)"/>
<path d="" fill="#F04E47" transform="translate(0,0)"/>
<path d="" fill="#EE4A43" transform="translate(0,0)"/>
<path d="" fill="#EC4A43" transform="translate(0,0)"/>
<path d="" fill="#ED4D47" transform="translate(0,0)"/>
<path d="" fill="#EE4D45" transform="translate(0,0)"/>
<path d="" fill="#F14A46" transform="translate(0,0)"/>
<path d="" fill="#D53D30" transform="translate(0,0)"/>
<path d="" fill="#F14944" transform="translate(0,0)"/>
<path d="" fill="#EC4943" transform="translate(0,0)"/>
<path d="" fill="#D63A30" transform="translate(0,0)"/>
<path d="" fill="#ED4742" transform="translate(0,0)"/>
<path d="" fill="#F04846" transform="translate(0,0)"/>
<path d="" fill="#EE4440" transform="translate(0,0)"/>
<path d="" fill="#ED4643" transform="translate(0,0)"/>
<path d="" fill="#D53A2D" transform="translate(0,0)"/>
<path d="" fill="#D83D32" transform="translate(0,0)"/>
<path d="" fill="#D53A2D" transform="translate(0,0)"/>
<path d="" fill="#EB4342" transform="translate(0,0)"/>
<path d="" fill="#F14B45" transform="translate(0,0)"/>
<path d="" fill="#ED4744" transform="translate(0,0)"/>
<path d="" fill="#EF4B47" transform="translate(0,0)"/>
<path d="" fill="#ED4946" transform="translate(0,0)"/>
<path d="" fill="#EF4945" transform="translate(0,0)"/>
<path d="" fill="#F04944" transform="translate(0,0)"/>
<path d="" fill="#F04A46" transform="translate(0,0)"/>
<path d="" fill="#B41D13" transform="translate(0,0)"/>
<path d="" fill="#D4392D" transform="translate(0,0)"/>
<path d="" fill="#E04436" transform="translate(0,0)"/>
<path d="" fill="#F14A47" transform="translate(0,0)"/>
<path d="" fill="#F04B45" transform="translate(0,0)"/>
<path d="" fill="#EF4745" transform="translate(0,0)"/>
<path d="" fill="#F24845" transform="translate(0,0)"/>
<path d="" fill="#F04744" transform="translate(0,0)"/>
<path d="" fill="#F04845" transform="translate(0,0)"/>
<path d="" fill="#ED4A44" transform="translate(0,0)"/>
<path d="" fill="#EC4540" transform="translate(0,0)"/>
<path d="" fill="#F24745" transform="translate(0,0)"/>
<path d="" fill="#D6392E" transform="translate(0,0)"/>
<path d="" fill="#F04644" transform="translate(0,0)"/>
<path d="" fill="#F24545" transform="translate(0,0)"/>
<path d="" fill="#EF4343" transform="translate(0,0)"/>
<path d="" fill="#F14742" transform="translate(0,0)"/>
<path d="" fill="#F14845" transform="translate(0,0)"/>
<path d="" fill="#F14844" transform="translate(0,0)"/>
<path d="" fill="#EA4741" transform="translate(0,0)"/>
<path d="" fill="#F34A48" transform="translate(0,0)"/>
<path d="" fill="#F04745" transform="translate(0,0)"/>
<path d="" fill="#ED4A47" transform="translate(0,0)"/>
<path d="" fill="#F14B48" transform="translate(0,0)"/>
<path d="" fill="#F24846" transform="translate(0,0)"/>
<path d="" fill="#F14A47" transform="translate(0,0)"/>
<path d="" fill="#EC4241" transform="translate(0,0)"/>
<path d="" fill="#F14744" transform="translate(0,0)"/>
<path d="" fill="#F04745" transform="translate(0,0)"/>
<path d="" fill="#B61F15" transform="translate(0,0)"/>
<path d="" fill="#BB2419" transform="translate(0,0)"/>
<path d="" fill="#F24645" transform="translate(0,0)"/>
<path d="" fill="#F04442" transform="translate(0,0)"/>
<path d="" fill="#F04745" transform="translate(0,0)"/>
<path d="" fill="#F04744" transform="translate(0,0)"/>
<path d="" fill="#F24745" transform="translate(0,0)"/>
<path d="" fill="#F14745" transform="translate(0,0)"/>
<path d="" fill="#F14645" transform="translate(0,0)"/>
<path d="" fill="#D8382D" transform="translate(0,0)"/>
<path d="" fill="#F04645" transform="translate(0,0)"/>
<path d="" fill="#EF4743" transform="translate(0,0)"/>
<path d="" fill="#F14944" transform="translate(0,0)"/>
<path d="" fill="#F04844" transform="translate(0,0)"/>
<path d="" fill="#CC3126" transform="translate(0,0)"/>
<path d="" fill="#EC4641" transform="translate(0,0)"/>
<path d="" fill="#F04141" transform="translate(0,0)"/>
<path d="" fill="#F34B45" transform="translate(0,0)"/>
<path d="" fill="#F04544" transform="translate(0,0)"/>
<path d="" fill="#D7392D" transform="translate(0,0)"/>
<path d="" fill="#F34A46" transform="translate(0,0)"/>
<path d="" fill="#F04443" transform="translate(0,0)"/>
<path d="" fill="#F04643" transform="translate(0,0)"/>
<path d="" fill="#EE4947" transform="translate(0,0)"/>
<path d="" fill="#EF4340" transform="translate(0,0)"/>
<path d="" fill="#EF4744" transform="translate(0,0)"/>
<path d="" fill="#F04745" transform="translate(0,0)"/>
<path d="" fill="#F14642" transform="translate(0,0)"/>
<path d="" fill="#F14743" transform="translate(0,0)"/>
<path d="" fill="#F24644" transform="translate(0,0)"/>
<path d="" fill="#F04642" transform="translate(0,0)"/>
<path d="" fill="#F14342" transform="translate(0,0)"/>
<path d="" fill="#F34A47" transform="translate(0,0)"/>
<path d="" fill="#F14545" transform="translate(0,0)"/>
<path d="" fill="#F14543" transform="translate(0,0)"/>
<path d="" fill="#C1261C" transform="translate(0,0)"/>
<path d="" fill="#F14242" transform="translate(0,0)"/>
<path d="" fill="#ED4441" transform="translate(0,0)"/>
<path d="" fill="#F04642" transform="translate(0,0)"/>
<path d="" fill="#EE4742" transform="translate(0,0)"/>
<path d="" fill="#EF4645" transform="translate(0,0)"/>
<path d="" fill="#B81F14" transform="translate(0,0)"/>
<path d="" fill="#F24846" transform="translate(0,0)"/>
<path d="" fill="#D8372D" transform="translate(0,0)"/>
<path d="" fill="#EE4340" transform="translate(0,0)"/>
<path d="" fill="#F04844" transform="translate(0,0)"/>
<path d="" fill="#ED4441" transform="translate(0,0)"/>
<path d="" fill="#F14845" transform="translate(0,0)"/>
<path d="" fill="#EC4743" transform="translate(0,0)"/>
<path d="" fill="#F34844" transform="translate(0,0)"/>
<path d="" fill="#ED4542" transform="translate(0,0)"/>
<path d="" fill="#F24745" transform="translate(0,0)"/>
<path d="" fill="#EE4442" transform="translate(0,0)"/>
<path d="" fill="#B51C13" transform="translate(0,0)"/>
<path d="" fill="#DB3A30" transform="translate(0,0)"/>
<path d="" fill="#F14543" transform="translate(0,0)"/>
<path d="" fill="#F14544" transform="translate(0,0)"/>
<path d="" fill="#EF4642" transform="translate(0,0)"/>
<path d="" fill="#F24544" transform="translate(0,0)"/>
<path d="" fill="#EF4643" transform="translate(0,0)"/>
<path d="" fill="#F14544" transform="translate(0,0)"/>
<path d="" fill="#F14543" transform="translate(0,0)"/>
<path d="" fill="#F14745" transform="translate(0,0)"/>
<path d="" fill="#F24745" transform="translate(0,0)"/>
<path d="" fill="#F34746" transform="translate(0,0)"/>
<path d="" fill="#F14945" transform="translate(0,0)"/>
<path d="" fill="#F24644" transform="translate(0,0)"/>
<path d="" fill="#DD3A2F" transform="translate(0,0)"/>
<path d="" fill="#F14644" transform="translate(0,0)"/>
<path d="" fill="#F34544" transform="translate(0,0)"/>
<path d="" fill="#F14948" transform="translate(0,0)"/>
<path d="" fill="#F1413F" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F04744" transform="translate(0,0)"/>
<path d="" fill="#EF4843" transform="translate(0,0)"/>
<path d="" fill="#F14844" transform="translate(0,0)"/>
<path d="" fill="#F04344" transform="translate(0,0)"/>
<path d="" fill="#F14B48" transform="translate(0,0)"/>
<path d="" fill="#F24543" transform="translate(0,0)"/>
<path d="" fill="#F04744" transform="translate(0,0)"/>
<path d="" fill="#F14A46" transform="translate(0,0)"/>
<path d="" fill="#EF403D" transform="translate(0,0)"/>
<path d="" fill="#F24443" transform="translate(0,0)"/>
<path d="" fill="#F24844" transform="translate(0,0)"/>
<path d="" fill="#F14643" transform="translate(0,0)"/>
<path d="" fill="#F24643" transform="translate(0,0)"/>
<path d="" fill="#F14944" transform="translate(0,0)"/>
<path d="" fill="#ED443F" transform="translate(0,0)"/>
<path d="" fill="#F14643" transform="translate(0,0)"/>
<path d="" fill="#EF4542" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="" fill="#F0433F" transform="translate(0,0)"/>
<path d="" fill="#EF4644" transform="translate(0,0)"/>
<path d="" fill="#F34442" transform="translate(0,0)"/>
<path d="" fill="#F04340" transform="translate(0,0)"/>
<path d="" fill="#EE4744" transform="translate(0,0)"/>
<path d="" fill="#EF4844" transform="translate(0,0)"/>
<path d="" fill="#F14746" transform="translate(0,0)"/>
<path d="" fill="#F14740" transform="translate(0,0)"/>
<path d="" fill="#EF4844" transform="translate(0,0)"/>
<path d="" fill="#EE4F47" transform="translate(0,0)"/>
<path d="" fill="#F14842" transform="translate(0,0)"/>
<path d="" fill="#EA4441" transform="translate(0,0)"/>
<path d="" fill="#BA2016" transform="translate(0,0)"/>
<path d="" fill="#DC3730" transform="translate(0,0)"/>
<path d="" fill="#F14644" transform="translate(0,0)"/>
<path d="" fill="#F14744" transform="translate(0,0)"/>
<path d="" fill="#EE4643" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="" fill="#F14643" transform="translate(0,0)"/>
<path d="" fill="#EE4743" transform="translate(0,0)"/>
<path d="" fill="#F04542" transform="translate(0,0)"/>
<path d="" fill="#EF4742" transform="translate(0,0)"/>
<path d="" fill="#F04845" transform="translate(0,0)"/>
<path d="" fill="#F04540" transform="translate(0,0)"/>
<path d="" fill="#F04644" transform="translate(0,0)"/>
<path d="" fill="#F34741" transform="translate(0,0)"/>
<path d="" fill="#F54742" transform="translate(0,0)"/>
<path d="" fill="#C02318" transform="translate(0,0)"/>
<path d="" fill="#ED4440" transform="translate(0,0)"/>
<path d="" fill="#F14642" transform="translate(0,0)"/>
<path d="" fill="#F04845" transform="translate(0,0)"/>
<path d="" fill="#F24641" transform="translate(0,0)"/>
<path d="" fill="#ED4341" transform="translate(0,0)"/>
<path d="" fill="#F04242" transform="translate(0,0)"/>
<path d="" fill="#EF4643" transform="translate(0,0)"/>
<path d="" fill="#F34744" transform="translate(0,0)"/>
<path d="" fill="#F24643" transform="translate(0,0)"/>
<path d="" fill="#F04742" transform="translate(0,0)"/>
<path d="" fill="#BE2117" transform="translate(0,0)"/>
<path d="" fill="#C12419" transform="translate(0,0)"/>
<path d="" fill="#EC423D" transform="translate(0,0)"/>
<path d="" fill="#ED4441" transform="translate(0,0)"/>
<path d="" fill="#F54643" transform="translate(0,0)"/>
<path d="" fill="#F24542" transform="translate(0,0)"/>
<path d="" fill="#ED3F39" transform="translate(0,0)"/>
<path d="" fill="#EE4442" transform="translate(0,0)"/>
<path d="" fill="#F34644" transform="translate(0,0)"/>
<path d="" fill="#EE4544" transform="translate(0,0)"/>
<path d="" fill="#F14342" transform="translate(0,0)"/>
<path d="" fill="#EE4543" transform="translate(0,0)"/>
<path d="" fill="#F34744" transform="translate(0,0)"/>
<path d="" fill="#EF4641" transform="translate(0,0)"/>
<path d="" fill="#F24743" transform="translate(0,0)"/>
<path d="" fill="#F14744" transform="translate(0,0)"/>
<path d="" fill="#ED413E" transform="translate(0,0)"/>
<path d="" fill="#EE4643" transform="translate(0,0)"/>
<path d="" fill="#F24543" transform="translate(0,0)"/>
<path d="" fill="#F24544" transform="translate(0,0)"/>
<path d="" fill="#ED4540" transform="translate(0,0)"/>
<path d="" fill="#F34444" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F34744" transform="translate(0,0)"/>
<path d="" fill="#F04544" transform="translate(0,0)"/>
<path d="" fill="#F14342" transform="translate(0,0)"/>
<path d="" fill="#EF4644" transform="translate(0,0)"/>
<path d="" fill="#F04643" transform="translate(0,0)"/>
<path d="" fill="#F24443" transform="translate(0,0)"/>
<path d="" fill="#F34542" transform="translate(0,0)"/>
<path d="" fill="#F04643" transform="translate(0,0)"/>
<path d="" fill="#F14542" transform="translate(0,0)"/>
<path d="" fill="#EF413E" transform="translate(0,0)"/>
<path d="" fill="#EC413E" transform="translate(0,0)"/>
<path d="" fill="#F24541" transform="translate(0,0)"/>
<path d="" fill="#EF4544" transform="translate(0,0)"/>
<path d="" fill="#F34644" transform="translate(0,0)"/>
<path d="" fill="#F24543" transform="translate(0,0)"/>
<path d="" fill="#CF2B23" transform="translate(0,0)"/>
<path d="" fill="#EE4644" transform="translate(0,0)"/>
<path d="" fill="#EE4543" transform="translate(0,0)"/>
<path d="" fill="#F04442" transform="translate(0,0)"/>
<path d="" fill="#EE4544" transform="translate(0,0)"/>
<path d="" fill="#F14342" transform="translate(0,0)"/>
<path d="" fill="#EF4643" transform="translate(0,0)"/>
<path d="" fill="#F14440" transform="translate(0,0)"/>
<path d="" fill="#F14442" transform="translate(0,0)"/>
<path d="" fill="#ED4643" transform="translate(0,0)"/>
<path d="" fill="#F04444" transform="translate(0,0)"/>
<path d="" fill="#EF4746" transform="translate(0,0)"/>
<path d="" fill="#F34543" transform="translate(0,0)"/>
<path d="" fill="#F14444" transform="translate(0,0)"/>
<path d="" fill="#EF4443" transform="translate(0,0)"/>
<path d="" fill="#F04440" transform="translate(0,0)"/>
<path d="" fill="#F24543" transform="translate(0,0)"/>
<path d="" fill="#F04240" transform="translate(0,0)"/>
<path d="" fill="#F34645" transform="translate(0,0)"/>
<path d="" fill="#F04A47" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="" fill="#ED4542" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F04440" transform="translate(0,0)"/>
<path d="" fill="#F14543" transform="translate(0,0)"/>
<path d="" fill="#F04643" transform="translate(0,0)"/>
<path d="" fill="#F34443" transform="translate(0,0)"/>
<path d="" fill="#EF4240" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="" fill="#F14341" transform="translate(0,0)"/>
<path d="" fill="#ED4542" transform="translate(0,0)"/>
<path d="" fill="#F24846" transform="translate(0,0)"/>
<path d="" fill="#EF4443" transform="translate(0,0)"/>
<path d="" fill="#EF403F" transform="translate(0,0)"/>
<path d="" fill="#F34544" transform="translate(0,0)"/>
<path d="" fill="#EE4443" transform="translate(0,0)"/>
<path d="" fill="#F04642" transform="translate(0,0)"/>
<path d="" fill="#F04744" transform="translate(0,0)"/>
<path d="" fill="#EF4040" transform="translate(0,0)"/>
<path d="" fill="#F04642" transform="translate(0,0)"/>
<path d="" fill="#F14341" transform="translate(0,0)"/>
<path d="" fill="#F44444" transform="translate(0,0)"/>
<path d="" fill="#F24643" transform="translate(0,0)"/>
<path d="" fill="#F14441" transform="translate(0,0)"/>
<path d="" fill="#F34341" transform="translate(0,0)"/>
<path d="" fill="#F04542" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F04240" transform="translate(0,0)"/>
<path d="" fill="#EF4444" transform="translate(0,0)"/>
<path d="" fill="#F04744" transform="translate(0,0)"/>
<path d="" fill="#F14644" transform="translate(0,0)"/>
<path d="" fill="#EE403C" transform="translate(0,0)"/>
<path d="" fill="#F24440" transform="translate(0,0)"/>
<path d="" fill="#F04946" transform="translate(0,0)"/>
<path d="" fill="#F24644" transform="translate(0,0)"/>
<path d="" fill="#EE423E" transform="translate(0,0)"/>
<path d="" fill="#EF4544" transform="translate(0,0)"/>
<path d="" fill="#F14642" transform="translate(0,0)"/>
<path d="" fill="#F24744" transform="translate(0,0)"/>
<path d="" fill="#F14643" transform="translate(0,0)"/>
<path d="" fill="#F23F3E" transform="translate(0,0)"/>
<path d="" fill="#F2403E" transform="translate(0,0)"/>
<path d="" fill="#F54544" transform="translate(0,0)"/>
<path d="" fill="#F04844" transform="translate(0,0)"/>
<path d="" fill="#F14341" transform="translate(0,0)"/>
<path d="" fill="#F34845" transform="translate(0,0)"/>
<path d="" fill="#ED413F" transform="translate(0,0)"/>
<path d="" fill="#F24744" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="" fill="#F24544" transform="translate(0,0)"/>
<path d="" fill="#F04542" transform="translate(0,0)"/>
<path d="" fill="#EE423F" transform="translate(0,0)"/>
<path d="" fill="#F24643" transform="translate(0,0)"/>
<path d="" fill="#F44343" transform="translate(0,0)"/>
<path d="" fill="#EF4340" transform="translate(0,0)"/>
<path d="" fill="#EE403D" transform="translate(0,0)"/>
<path d="" fill="#F54743" transform="translate(0,0)"/>
<path d="" fill="#EF4644" transform="translate(0,0)"/>
<path d="" fill="#F14945" transform="translate(0,0)"/>
<path d="" fill="#F04443" transform="translate(0,0)"/>
<path d="" fill="#F24442" transform="translate(0,0)"/>
<path d="" fill="#F24541" transform="translate(0,0)"/>
<path d="" fill="#F0423F" transform="translate(0,0)"/>
<path d="" fill="#F24543" transform="translate(0,0)"/>
<path d="" fill="#F24645" transform="translate(0,0)"/>
<path d="" fill="#F14543" transform="translate(0,0)"/>
<path d="" fill="#EF4542" transform="translate(0,0)"/>
<path d="" fill="#F24443" transform="translate(0,0)"/>
<path d="" fill="#F34443" transform="translate(0,0)"/>
<path d="" fill="#EE4444" transform="translate(0,0)"/>
<path d="" fill="#F14442" transform="translate(0,0)"/>
<path d="" fill="#F04442" transform="translate(0,0)"/>
<path d="" fill="#EE3D3C" transform="translate(0,0)"/>
<path d="" fill="#EF4542" transform="translate(0,0)"/>
<path d="" fill="#F24442" transform="translate(0,0)"/>
<path d="" fill="#EF4544" transform="translate(0,0)"/>
<path d="" fill="#F34542" transform="translate(0,0)"/>
<path d="" fill="#F04643" transform="translate(0,0)"/>
<path d="" fill="#F54945" transform="translate(0,0)"/>
<path d="" fill="#F34343" transform="translate(0,0)"/>
<path d="" fill="#EF4744" transform="translate(0,0)"/>
<path d="" fill="#F14543" transform="translate(0,0)"/>
<path d="" fill="#F14744" transform="translate(0,0)"/>
<path d="" fill="#F04341" transform="translate(0,0)"/>
<path d="" fill="#F04441" transform="translate(0,0)"/>
<path d="" fill="#F04441" transform="translate(0,0)"/>
<path d="" fill="#EE403D" transform="translate(0,0)"/>
<path d="" fill="#F24544" transform="translate(0,0)"/>
<path d="" fill="#F24442" transform="translate(0,0)"/>
<path d="" fill="#F14442" transform="translate(0,0)"/>
<path d="" fill="#EF4644" transform="translate(0,0)"/>
<path d="" fill="#F14643" transform="translate(0,0)"/>
<path d="" fill="#F04544" transform="translate(0,0)"/>
<path d="" fill="#F14441" transform="translate(0,0)"/>
<path d="" fill="#EE4342" transform="translate(0,0)"/>
<path d="" fill="#F14543" transform="translate(0,0)"/>
<path d="" fill="#F04544" transform="translate(0,0)"/>
<path d="" fill="#EE4541" transform="translate(0,0)"/>
<path d="" fill="#F34544" transform="translate(0,0)"/>
<path d="" fill="#F14443" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F14544" transform="translate(0,0)"/>
<path d="" fill="#F14442" transform="translate(0,0)"/>
<path d="" fill="#EF4443" transform="translate(0,0)"/>
<path d="" fill="#F24442" transform="translate(0,0)"/>
<path d="" fill="#F14644" transform="translate(0,0)"/>
<path d="" fill="#F24141" transform="translate(0,0)"/>
<path d="" fill="#F14342" transform="translate(0,0)"/>
<path d="" fill="#EF4442" transform="translate(0,0)"/>
<path d="" fill="#F14342" transform="translate(0,0)"/>
<path d="" fill="#F34543" transform="translate(0,0)"/>
<path d="" fill="#F04340" transform="translate(0,0)"/>
<path d="" fill="#F44440" transform="translate(0,0)"/>
<path d="" fill="#F24241" transform="translate(0,0)"/>
<path d="" fill="#F14341" transform="translate(0,0)"/>
<path d="" fill="#F34542" transform="translate(0,0)"/>
<path d="" fill="#F04340" transform="translate(0,0)"/>
<path d="" fill="#F04442" transform="translate(0,0)"/>
<path d="" fill="#ED4640" transform="translate(0,0)"/>
<path d="" fill="#EF4745" transform="translate(0,0)"/>
<path d="" fill="#F14442" transform="translate(0,0)"/>
<path d="" fill="#F14241" transform="translate(0,0)"/>
<path d="" fill="#F14843" transform="translate(0,0)"/>
<path d="" fill="#F34441" transform="translate(0,0)"/>
<path d="" fill="#EF4945" transform="translate(0,0)"/>
<path d="" fill="#EF4644" transform="translate(0,0)"/>
<path d="" fill="#EE4341" transform="translate(0,0)"/>
<path d="" fill="#ED4341" transform="translate(0,0)"/>
<path d="" fill="#F14340" transform="translate(0,0)"/>
<path d="" fill="#ED4341" transform="translate(0,0)"/>
<path d="" fill="#F14643" transform="translate(0,0)"/>
<path d="" fill="#F24442" transform="translate(0,0)"/>
<path d="" fill="#ED3F3C" transform="translate(0,0)"/>
<path d="" fill="#F14441" transform="translate(0,0)"/>
<path d="" fill="#EB4441" transform="translate(0,0)"/>
<path d="" fill="#F04443" transform="translate(0,0)"/>
<path d="" fill="#F04742" transform="translate(0,0)"/>
<path d="" fill="#F04442" transform="translate(0,0)"/>
<path d="" fill="#F34442" transform="translate(0,0)"/>
<path d="" fill="#F04541" transform="translate(0,0)"/>
<path d="" fill="#F04645" transform="translate(0,0)"/>
<path d="" fill="#F24340" transform="translate(0,0)"/>
<path d="" fill="#ED4644" transform="translate(0,0)"/>
<path d="" fill="#EE3E3A" transform="translate(0,0)"/>
<path d="" fill="#F04545" transform="translate(0,0)"/>
<path d="" fill="#F14542" transform="translate(0,0)"/>
<path d="" fill="#EF4543" transform="translate(0,0)"/>
<path d="" fill="#ED3E3B" transform="translate(0,0)"/>
<path d="" fill="#EF4441" transform="translate(0,0)"/>
<path d="" fill="#EF4A44" transform="translate(0,0)"/>
<path d="" fill="#F14441" transform="translate(0,0)"/>
<path d="" fill="#F14442" transform="translate(0,0)"/>
<path d="" fill="#F24340" transform="translate(0,0)"/>
<path d="" fill="#F24342" transform="translate(0,0)"/>
<path d="" fill="#F04241" transform="translate(0,0)"/>
<path d="" fill="#F04643" transform="translate(0,0)"/>
<path d="" fill="#F14341" transform="translate(0,0)"/>
<path d="" fill="#F14440" transform="translate(0,0)"/>
<path d="" fill="#F24441" transform="translate(0,0)"/>
<path d="" fill="#F54341" transform="translate(0,0)"/>
<path d="" fill="#F04543" transform="translate(0,0)"/>
<path d="" fill="#F34342" transform="translate(0,0)"/>
<path d="" fill="#EF4542" transform="translate(0,0)"/>
<path d="" fill="#F14341" transform="translate(0,0)"/>
<path d="" fill="#F24543" transform="translate(0,0)"/>
<path d="" fill="#F24441" transform="translate(0,0)"/>
<path d="" fill="#EF4440" transform="translate(0,0)"/>
<path d="" fill="#E8413C" transform="translate(0,0)"/>
<path d="" fill="#F04442" transform="translate(0,0)"/>
<path d="" fill="#EB403D" transform="translate(0,0)"/>
<path d="" fill="#EC3C39" transform="translate(0,0)"/>
<path d="" fill="#EE4440" transform="translate(0,0)"/>
<path d="" fill="#F24241" transform="translate(0,0)"/>
<path d="" fill="#F14340" transform="translate(0,0)"/>
<path d="" fill="#F1453F" transform="translate(0,0)"/>
<path d="" fill="#F04440" transform="translate(0,0)"/>
<path d="" fill="#ED413D" transform="translate(0,0)"/>
<path d="" fill="#EE4542" transform="translate(0,0)"/>
<path d="" fill="#EB4642" transform="translate(0,0)"/>
<path d="" fill="#EE433F" transform="translate(0,0)"/>
<path d="" fill="#E73F3C" transform="translate(0,0)"/>
<path d="" fill="#EF4743" transform="translate(0,0)"/>
<path d="" fill="#EE4641" transform="translate(0,0)"/>
<path d="" fill="#F14642" transform="translate(0,0)"/>
<path d="" fill="#EC423F" transform="translate(0,0)"/>
<path d="" fill="#E93E39" transform="translate(0,0)"/>
<path d="" fill="#EF4742" transform="translate(0,0)"/>
<path d="" fill="#F04641" transform="translate(0,0)"/>
<path d="" fill="#EB3D3B" transform="translate(0,0)"/>
<path d="" fill="#EE443F" transform="translate(0,0)"/>
<path d="" fill="#ED4140" transform="translate(0,0)"/>
<path d="" fill="#EB4642" transform="translate(0,0)"/>
<path d="" fill="#E93F3C" transform="translate(0,0)"/>
<path d="" fill="#EB4440" transform="translate(0,0)"/>
<path d="" fill="#EE4540" transform="translate(0,0)"/>
<path d="" fill="#F24740" transform="translate(0,0)"/>
<path d="" fill="#F1433E" transform="translate(0,0)"/>
<path d="" fill="#EF443F" transform="translate(0,0)"/>
<path d="" fill="#F1433F" transform="translate(0,0)"/>
<path d="" fill="#ED423E" transform="translate(0,0)"/>
<path d="" fill="#ED413C" transform="translate(0,0)"/>
<path d="" fill="#F0443F" transform="translate(0,0)"/>
<path d="" fill="#F04440" transform="translate(0,0)"/>
<path d="" fill="#F0423F" transform="translate(0,0)"/>
<path d="" fill="#F1423E" transform="translate(0,0)"/>
<path d="" fill="#EF4340" transform="translate(0,0)"/>
<path d="" fill="#F14440" transform="translate(0,0)"/>
<path d="" fill="#F14440" transform="translate(0,0)"/>
<path d="" fill="#F04440" transform="translate(0,0)"/>
<path d="" fill="#F04341" transform="translate(0,0)"/>
<path d="" fill="#F04441" transform="translate(0,0)"/>
<path d="" fill="#F14641" transform="translate(0,0)"/>
<path d="" fill="#EF4441" transform="translate(0,0)"/>
<path d="" fill="#EE4440" transform="translate(0,0)"/>
<path d="" fill="#F14440" transform="translate(0,0)"/>
<path d="" fill="#F1443F" transform="translate(0,0)"/>
<path d="" fill="#F14440" transform="translate(0,0)"/>
<path d="" fill="#EC413D" transform="translate(0,0)"/>
<path d="" fill="#EF423B" transform="translate(0,0)"/>
<path d="" fill="#EC3F3A" transform="translate(0,0)"/>
<path d="" fill="#EC423E" transform="translate(0,0)"/>
<path d="" fill="#EF433F" transform="translate(0,0)"/>
<path d="" fill="#ED413E" transform="translate(0,0)"/>
<path d="" fill="#EB413E" transform="translate(0,0)"/>
<path d="" fill="#F0433E" transform="translate(0,0)"/>
<path d="" fill="#EE4440" transform="translate(0,0)"/>
<path d="" fill="#EE4440" transform="translate(0,0)"/>
<path d="" fill="#EC403F" transform="translate(0,0)"/>
<path d="" fill="#EE403F" transform="translate(0,0)"/>
<path d="" fill="#EC403D" transform="translate(0,0)"/>
<path d="" fill="#B91E14" transform="translate(0,0)"/>
<path d="" fill="#EA3F36" transform="translate(0,0)"/>
<path d="" fill="#E83F36" transform="translate(0,0)"/>
<path d="" fill="#E23B31" transform="translate(0,0)"/>
<path d="" fill="#E63E34" transform="translate(0,0)"/>
<path d="" fill="#E23930" transform="translate(0,0)"/>
<path d="" fill="#E33A32" transform="translate(0,0)"/>
<path d="" fill="#A81209" transform="translate(0,0)"/>
<path d="" fill="#E43830" transform="translate(0,0)"/>
<path d="" fill="#E23831" transform="translate(0,0)"/>
<path d="" fill="#DF372D" transform="translate(0,0)"/>
<path d="" fill="#E3382F" transform="translate(0,0)"/>
</svg>
