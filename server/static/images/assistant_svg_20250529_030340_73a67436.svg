<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C0.186 1.114 0.371 2.227 0.562 3.375 C1.628 7.269 1.628 7.269 5.188 8.5 C9.124 9.016 12.145 8.925 16 8 C16.33 7.34 16.66 6.68 17 6 C17.603 6.228 18.207 6.456 18.828 6.691 C19.627 6.979 20.427 7.266 21.25 7.562 C22.433 7.998 22.433 7.998 23.641 8.441 C26.233 9.055 27.567 9.071 30 8 C30.33 7.01 30.66 6.02 31 5 C31.763 5.351 32.526 5.701 33.312 6.062 C38.368 7.348 42.876 6.675 48 6 C48.99 7.485 48.99 7.485 50 9 C50.619 8.835 51.237 8.67 51.875 8.5 C57.495 7.601 62.908 6.996 68.125 9.5 C68.744 9.995 69.362 10.49 70 11 C70.743 10.288 70.743 10.288 71.5 9.562 C77.046 6.096 82.833 7.013 89 8 C89.66 7.67 90.32 7.34 91 7 C93.793 6.985 96.527 7.05 99.312 7.188 C100.072 7.206 100.832 7.225 101.615 7.244 C106.626 7.492 109.305 8.32 113 12 C113.577 11.711 114.155 11.423 114.75 11.125 C115.864 10.568 115.864 10.568 117 10 C117.59 9.652 118.181 9.304 118.789 8.945 C121.523 7.776 123.66 7.734 126.625 7.75 C127.587 7.745 128.548 7.74 129.539 7.734 C132 8 132 8 134 10 C136.535 9.624 138.826 8.714 141.25 7.875 C144.802 7.046 147.019 7.174 150.555 7.777 C154.962 8.179 159.338 7.582 163.73 7.156 C167.559 6.973 170.565 7.282 174 9 C174.66 8.67 175.32 8.34 176 8 C180.747 7.736 185.457 7.691 190.125 8.625 C193.676 9.088 196.325 8.282 199.762 7.41 C202.966 6.823 205.816 7.449 209 8 C212.408 7.604 215.008 6.662 218 5 C218.66 5.66 219.32 6.32 220 7 C221.485 6.505 221.485 6.505 223 6 C223 5.34 223 4.68 223 4 C223.908 3.711 224.815 3.423 225.75 3.125 C228.322 2.235 230.613 1.285 233 0 C233.99 0.66 234.98 1.32 236 2 C238.625 2.167 238.625 2.167 241 2 C241.66 4.64 242.32 7.28 243 10 C246.185 10.277 246.185 10.277 249 8 C251.121 7.516 251.121 7.516 253.438 7.25 C254.199 7.152 254.961 7.054 255.746 6.953 C258 7 258 7 260.035 8.047 C262.556 9.27 263.352 8.767 266 8 C268.249 7.932 270.5 7.915 272.75 7.938 C274.506 7.951 274.506 7.951 276.297 7.965 C277.189 7.976 278.081 7.988 279 8 C279.33 7.34 279.66 6.68 280 6 C281.465 6.567 282.922 7.154 284.375 7.75 C285.187 8.075 285.999 8.4 286.836 8.734 C289.445 10.26 289.907 11.218 291 14 C291.227 16.382 291.227 16.382 291.195 18.984 C291.189 19.919 291.182 20.854 291.176 21.816 C291.159 22.784 291.142 23.753 291.125 24.75 C291.116 25.734 291.107 26.717 291.098 27.73 C291.074 30.154 291.038 32.577 291 35 C287.7 35 284.4 35 281 35 C280.837 32.982 280.674 30.964 280.512 28.945 C280.126 26.605 280.126 26.605 277 25 C277.99 24.67 278.98 24.34 280 24 C279.01 24 278.02 24 277 24 C276.963 24.699 276.925 25.397 276.887 26.117 C276.788 27.482 276.788 27.482 276.688 28.875 C276.629 29.78 276.571 30.685 276.512 31.617 C276 34 276 34 273 36 C269.312 35.625 269.312 35.625 266 35 C265.67 34.01 265.34 33.02 265 32 C264.319 32.474 263.639 32.949 262.938 33.438 C258.192 35.962 254.522 36.387 249.312 35.062 C247.673 34.537 247.673 34.537 246 34 C244.68 34 243.36 34 242 34 C241.34 34.66 240.68 35.32 240 36 C237.793 36.012 237.793 36.012 235.188 35.688 C234.335 35.588 233.483 35.489 232.605 35.387 C230.048 35.007 227.529 34.531 225 34 C224.805 32.23 224.619 30.459 224.438 28.688 C224.333 27.701 224.229 26.715 224.121 25.699 C224 23 224 23 225 20 C224.34 20 223.68 20 223 20 C222.34 21.32 221.68 22.64 221 24 C220.67 22.68 220.34 21.36 220 20 C220 24.95 220 29.9 220 35 C213.976 35.661 208.956 36.141 203 35 C199.665 34.909 196.335 34.941 193 35 C193 34.34 193 33.68 193 33 C192.394 33.24 191.788 33.48 191.164 33.727 C185.243 35.822 181.21 36.154 175.129 34.543 C172.882 33.876 172.882 33.876 170 34 C169.67 34 169.34 34 169 34 C168.783 32.36 168.783 32.36 168.562 30.688 C168.117 27.069 168.117 27.069 167 24 C166.34 24 165.68 24 165 24 C163.986 27.621 163.501 31.278 163 35 C162.143 34.988 161.286 34.977 160.402 34.965 C153.298 34.88 153.298 34.88 146.25 35.625 C143.092 35.989 141.447 35.721 138.438 34.938 C134.259 33.863 132.069 34.496 128 36 C123.167 35.746 119.968 34.765 116 32 C115.01 32.99 115.01 32.99 114 34 C110.068 35.011 106.019 35.448 102 36 C101.67 36.99 101.34 37.98 101 39 C100.01 37.02 100.01 37.02 99 35 C99.031 36.949 99.031 36.949 99.062 38.938 C99.084 40.292 99.071 41.648 99 43 C98 44 98 44 95.934 44.098 C95.11 44.086 94.286 44.074 93.438 44.062 C92.611 44.053 91.785 44.044 90.934 44.035 C90.296 44.024 89.657 44.012 89 44 C88.01 41.36 87.02 38.72 86 36 C84.783 36.021 83.566 36.041 82.312 36.062 C79.08 36.068 76.151 35.788 73 35 C73 34.34 73 33.68 73 33 C70.614 33.462 68.253 33.93 65.91 34.582 C60.58 35.994 57.307 35.977 52 34 C50.669 33.659 49.336 33.321 48 33 C48 32.01 48 31.02 48 30 C47.01 29.34 46.02 28.68 45 28 C45.33 27.34 45.66 26.68 46 26 C45.34 26 44.68 26 44 26 C43.67 26.66 43.34 27.32 43 28 C38.05 27.01 38.05 27.01 33 26 C33.021 27.114 33.041 28.227 33.062 29.375 C33 33 33 33 32 35 C29.69 35 27.38 35 25 35 C24.505 35.99 24.505 35.99 24 37 C23.01 37 22.02 37 21 37 C21 36.34 21 35.68 21 35 C19.969 35.186 18.938 35.371 17.875 35.562 C14 36 14 36 11.344 35.531 C7.076 34.853 2.937 34.893 -1.375 34.938 C-2.206 34.942 -3.038 34.947 -3.895 34.951 C-5.93 34.963 -7.965 34.981 -10 35 C-10.33 34.34 -10.66 33.68 -11 33 C-12.32 33 -13.64 33 -15 33 C-15 33.66 -15 34.32 -15 35 C-20.716 36.098 -24.818 36.193 -30.176 33.789 C-32.117 32.949 -33.913 32.353 -36 32 C-36.413 32.454 -36.825 32.908 -37.25 33.375 C-40.068 35.991 -43.229 35.641 -47 36 C-47.99 36.495 -47.99 36.495 -49 37 C-48.959 37.784 -48.918 38.567 -48.875 39.375 C-49 42 -49 42 -51 44 C-57.323 44.339 -57.323 44.339 -60 43 C-61.575 40.374 -62 39.129 -62 36 C-61.505 35.505 -61.505 35.505 -61 35 C-60.914 33.657 -60.893 32.31 -60.902 30.965 C-60.906 30.156 -60.909 29.347 -60.912 28.514 C-60.92 27.664 -60.929 26.813 -60.938 25.938 C-60.944 24.656 -60.944 24.656 -60.951 23.35 C-60.963 21.233 -60.981 19.117 -61 17 C-61.66 17.33 -62.32 17.66 -63 18 C-62.493 14.281 -61.884 11.265 -60 8 C-58.35 8 -56.7 8 -55 8 C-55 7.34 -55 6.68 -55 6 C-49.141 6.515 -43.677 7.46 -38 9 C-37.305 9.165 -36.61 9.33 -35.895 9.5 C-34 10 -34 10 -32 11 C-31.67 10.67 -31.34 10.34 -31 10 C-25.321 7.262 -21.99 6.698 -15.883 8.262 C-14 9 -14 9 -12 11 C-11.961 9.952 -11.961 9.952 -11.922 8.883 C-11.837 7.518 -11.837 7.518 -11.75 6.125 C-11.704 5.22 -11.657 4.315 -11.609 3.383 C-11 1 -11 1 -9.188 -0.344 C-5.967 -1.31 -3.254 -0.626 0 0 Z M-24 18 C-24.33 18.66 -24.66 19.32 -25 20 C-24.34 20 -23.68 20 -23 20 C-23.33 19.34 -23.66 18.68 -24 18 Z M-21 18 C-22.562 19.312 -22.562 19.312 -24 21 C-24 21.99 -24 22.98 -24 24 C-22.02 24.495 -22.02 24.495 -20 25 C-19.34 23.68 -18.68 22.36 -18 21 C-18.99 20.01 -19.98 19.02 -21 18 Z M104 18 C105 20 105 20 105 20 Z M144 18 C145 20 145 20 145 20 Z M252 18 C251.67 18.99 251.34 19.98 251 21 C251.66 21 252.32 21 253 21 C252.67 20.01 252.34 19.02 252 18 Z M254 18 C254.33 19.65 254.66 21.3 255 23 C255.66 21.68 256.32 20.36 257 19 C256.01 18.67 255.02 18.34 254 18 Z M-44 19 C-43.67 20.32 -43.34 21.64 -43 23 C-42.67 22.01 -42.34 21.02 -42 20 C-42.66 19.67 -43.32 19.34 -44 19 Z M99 19 C100 23 100 23 100 23 Z M124 20 C123.67 20.66 123.34 21.32 123 22 C123.66 22 124.32 22 125 22 C124.67 21.34 124.34 20.68 124 20 Z M126 20 C126.33 20.66 126.66 21.32 127 22 C127.33 21.34 127.66 20.68 128 20 C127.34 20 126.68 20 126 20 Z M276 20 C276.33 20.66 276.66 21.32 277 22 C277.66 22 278.32 22 279 22 C279 21.34 279 20.68 279 20 C278.01 20 277.02 20 276 20 Z M-45 23 C-44.67 23.66 -44.34 24.32 -44 25 C-43.67 24.34 -43.34 23.68 -43 23 C-43.66 23 -44.32 23 -45 23 Z M254 23 C253.34 23.66 252.68 24.32 252 25 C252.99 25 253.98 25 255 25 C254.67 24.34 254.34 23.68 254 23 Z " fill="#F1F3F1" transform="translate(543,622)"/>
<path d="M0 0 C0.692 -0.002 1.385 -0.004 2.098 -0.005 C4.417 -0.01 6.736 -0.008 9.055 -0.005 C10.734 -0.007 12.414 -0.009 14.094 -0.012 C18.709 -0.018 23.324 -0.018 27.94 -0.017 C32.917 -0.017 37.895 -0.022 42.872 -0.027 C52.623 -0.035 62.374 -0.038 72.125 -0.038 C80.049 -0.039 87.972 -0.041 95.896 -0.044 C118.352 -0.053 140.809 -0.058 163.266 -0.057 C164.476 -0.057 165.687 -0.057 166.934 -0.057 C168.147 -0.057 169.359 -0.057 170.608 -0.057 C190.265 -0.056 209.922 -0.066 229.579 -0.08 C249.754 -0.094 269.93 -0.101 290.105 -0.1 C301.436 -0.1 312.766 -0.103 324.096 -0.113 C333.741 -0.122 343.385 -0.125 353.029 -0.118 C357.952 -0.115 362.874 -0.115 367.796 -0.123 C372.3 -0.131 376.804 -0.13 381.308 -0.121 C382.939 -0.119 384.57 -0.121 386.201 -0.127 C388.416 -0.134 390.63 -0.129 392.844 -0.12 C394.075 -0.12 395.306 -0.121 396.574 -0.121 C401.037 0.212 404.557 1.23 408.297 3.691 C410.773 7.405 410.656 10.8 409.922 15.129 C408.485 18.066 408.485 18.066 405.922 20.129 C397.647 22.653 389.326 22.406 380.76 22.376 C379.113 22.377 377.467 22.38 375.82 22.383 C371.311 22.39 366.801 22.384 362.292 22.375 C357.422 22.369 352.553 22.374 347.684 22.377 C338.151 22.382 328.617 22.377 319.084 22.367 C307.979 22.357 296.875 22.357 285.77 22.358 C265.954 22.358 246.138 22.349 226.321 22.334 C207.099 22.32 187.876 22.313 168.653 22.314 C166.878 22.314 166.878 22.314 165.066 22.314 C163.884 22.314 162.703 22.314 161.485 22.314 C139.594 22.315 117.703 22.309 95.811 22.3 C88.073 22.297 80.336 22.296 72.598 22.295 C63.165 22.295 53.732 22.29 44.3 22.281 C39.489 22.277 34.677 22.273 29.866 22.275 C25.459 22.276 21.051 22.272 16.644 22.265 C15.053 22.263 13.461 22.263 11.87 22.265 C-11.081 22.287 -11.081 22.287 -15.64 19.004 C-18.6 15.513 -19.158 13.685 -19.078 9.129 C-17.563 5.113 -16.672 3.525 -13.078 1.129 C-8.695 0.144 -4.481 0.003 0 0 Z " fill="#DFBBA6" transform="translate(323.07779693603516,528.8713655471802)"/>
<path d="M0 0 C8.236 4.181 11.716 13.832 15.625 21.625 C19.717 29.721 23.88 37.677 28.688 45.375 C33.215 36.907 37.693 28.427 41.938 19.812 C50.626 2.313 50.626 2.313 59.688 -1.625 C64.319 -2.349 67.805 -2.075 72 0.062 C76.138 3.623 78.475 5.725 79.195 11.315 C79.219 12.882 79.221 14.45 79.205 16.017 C79.212 16.876 79.219 17.736 79.226 18.621 C79.244 21.455 79.232 24.287 79.219 27.121 C79.221 29.095 79.225 31.069 79.23 33.043 C79.236 37.178 79.228 41.312 79.209 45.448 C79.186 50.742 79.199 56.035 79.223 61.329 C79.238 65.405 79.233 69.48 79.223 73.556 C79.22 75.508 79.223 77.459 79.233 79.411 C79.243 82.142 79.227 84.872 79.205 87.603 C79.213 88.406 79.221 89.209 79.23 90.036 C79.151 95.262 78.103 98.202 74.688 102.375 C71.659 104.755 68.44 104.735 64.648 104.629 C60.86 104.138 58.553 102.363 56.093 99.554 C54.518 97.112 54.214 95.636 54.006 92.76 C53.942 91.879 53.878 90.999 53.811 90.091 C53.615 85.794 53.564 81.512 53.59 77.211 C53.591 76.293 53.593 75.375 53.594 74.429 C53.6 71.536 53.612 68.643 53.625 65.75 C53.63 63.772 53.635 61.794 53.639 59.816 C53.65 55.003 53.667 50.189 53.688 45.375 C52.107 48.189 50.531 51.006 48.957 53.824 C48.516 54.609 48.075 55.394 47.621 56.202 C44.342 62.079 41.29 68.036 38.471 74.147 C36.13 79.033 33.422 83.349 28.188 85.312 C25.073 85.39 23.406 84.909 20.688 83.375 C17.599 79.882 15.425 76.262 13.305 72.121 C13.004 71.538 12.704 70.956 12.395 70.355 C11.445 68.51 10.504 66.662 9.562 64.812 C8.924 63.568 8.284 62.324 7.645 61.08 C5.303 56.523 2.976 51.959 0.688 47.375 C0.686 47.988 0.685 48.601 0.683 49.233 C0.663 55.625 0.609 62.016 0.535 68.408 C0.512 70.792 0.497 73.176 0.492 75.56 C0.483 78.991 0.442 82.421 0.395 85.852 C0.398 86.914 0.402 87.977 0.406 89.072 C0.244 97.35 0.244 97.35 -2.732 101.033 C-6.577 104.522 -8.949 104.717 -13.988 104.66 C-17.563 104.222 -19.646 102.717 -22.312 100.375 C-25.008 96.581 -24.608 92.311 -24.603 87.846 C-24.61 86.987 -24.616 86.128 -24.623 85.243 C-24.642 82.404 -24.646 79.566 -24.648 76.727 C-24.655 74.751 -24.662 72.776 -24.669 70.801 C-24.681 66.66 -24.684 62.519 -24.683 58.378 C-24.683 53.076 -24.71 47.775 -24.745 42.473 C-24.767 38.393 -24.771 34.313 -24.77 30.233 C-24.772 28.278 -24.781 26.324 -24.797 24.37 C-24.816 21.634 -24.81 18.899 -24.798 16.163 C-24.81 15.358 -24.821 14.554 -24.832 13.725 C-24.774 8.493 -23.765 5.531 -20.312 1.375 C-14.258 -2.95 -6.685 -3.02 0 0 Z " fill="#4A738C" transform="translate(476.3125,222.625)"/>
<path d="M0 0 C0.741 0.044 1.482 0.088 2.246 0.133 C7.614 1.853 10.956 7.495 14.25 11.812 C14.679 12.374 15.107 12.935 15.549 13.514 C19.067 18.152 22.447 22.882 25.807 27.636 C29.07 32.235 32.471 36.711 35.931 41.163 C39.928 46.316 43.761 51.576 47.562 56.875 C48.496 54.075 48.676 52.46 48.644 49.575 C48.638 48.71 48.631 47.846 48.624 46.955 C48.613 46.024 48.602 45.092 48.59 44.133 C48.577 42.16 48.565 40.188 48.553 38.215 C48.528 35.105 48.5 31.996 48.467 28.886 C48.436 25.885 48.419 22.884 48.402 19.883 C48.389 18.956 48.375 18.028 48.362 17.073 C48.339 11.3 48.595 7.16 52.5 2.625 C55.878 -0.242 59.146 -0.462 63.496 -0.414 C66.878 0.059 68.989 1.731 71.562 3.875 C75.252 9.41 74.712 16.435 74.708 22.924 C74.711 23.685 74.714 24.445 74.718 25.228 C74.727 27.716 74.729 30.204 74.73 32.691 C74.734 34.434 74.737 36.177 74.741 37.92 C74.747 41.562 74.748 45.204 74.748 48.846 C74.748 53.502 74.761 58.158 74.779 62.814 C74.79 66.409 74.792 70.005 74.791 73.6 C74.792 75.316 74.797 77.033 74.805 78.749 C74.814 81.151 74.811 83.553 74.805 85.955 C74.811 86.656 74.817 87.357 74.822 88.079 C74.787 93.665 74.229 98.832 70.625 103.25 C66.683 106.356 62.39 106.37 57.562 105.875 C48.958 102.517 43.552 92.186 38.438 84.926 C25.954 66.116 25.954 66.116 11.562 48.875 C11.559 49.476 11.555 50.076 11.551 50.695 C11.51 56.957 11.44 63.219 11.355 69.48 C11.327 71.816 11.306 74.152 11.292 76.487 C11.27 79.849 11.223 83.209 11.172 86.57 C11.17 88.132 11.17 88.132 11.168 89.724 C11.047 95.739 10.56 99.283 6.562 103.875 C3.534 106.255 0.315 106.235 -3.477 106.129 C-7.301 105.634 -9.58 103.763 -12.188 101.062 C-14.997 96.147 -14.59 90.298 -14.599 84.778 C-14.604 83.988 -14.609 83.199 -14.614 82.385 C-14.628 79.789 -14.635 77.194 -14.641 74.598 C-14.646 72.785 -14.652 70.971 -14.658 69.158 C-14.668 65.363 -14.674 61.568 -14.678 57.773 C-14.683 52.92 -14.707 48.067 -14.736 43.213 C-14.754 39.47 -14.76 35.727 -14.761 31.984 C-14.764 30.196 -14.772 28.407 -14.785 26.618 C-14.802 24.112 -14.8 21.607 -14.794 19.102 C-14.808 18.004 -14.808 18.004 -14.822 16.883 C-14.776 11.141 -13.525 6.598 -10 2 C-6.929 -0.211 -3.678 -0.293 0 0 Z M48.562 57.875 C49.562 59.875 49.562 59.875 49.562 59.875 Z " fill="#48728A" transform="translate(635.4375,221.125)"/>
<path d="M0 0 C6.236 7.699 13 16.754 13 27 C14.485 27.495 14.485 27.495 16 28 C15.34 28 14.68 28 14 28 C14 29.65 14 31.3 14 33 C14.66 33 15.32 33 16 33 C14.63 48.209 12.017 60.281 3 73 C2.475 73.763 1.951 74.526 1.41 75.312 C0.712 76.148 0.712 76.148 0 77 C-0.66 77 -1.32 77 -2 77 C-2 77.66 -2 78.32 -2 79 C-16.49 91.696 -31.871 95.008 -50.539 94.2 C-55.777 93.775 -60.289 92.245 -65 90 C-65.781 89.649 -66.562 89.299 -67.367 88.938 C-80.146 82.57 -90.279 71.111 -94.988 57.719 C-98.321 45.47 -98.822 34.235 -95 22 C-94.742 21.121 -94.484 20.242 -94.219 19.336 C-89.846 6.583 -79.797 -4.103 -68.137 -10.594 C-62.873 -13.098 -57.718 -14.893 -52 -16 C-51.045 -16.201 -51.045 -16.201 -50.07 -16.406 C-32.297 -19.357 -12.853 -12.225 0 0 Z M-66.586 17.828 C-71.91 24.84 -74.181 33.209 -73.906 41.992 C-72.517 51.422 -68.691 59.563 -61.16 65.531 C-53.499 70.74 -45.427 72.923 -36.172 71.594 C-27.19 69.486 -19.549 65.1 -14.289 57.387 C-9.587 48.681 -7.652 39.684 -10 30 C-13.636 19.305 -19.857 13.854 -29.188 8 C-42.908 4.401 -57.118 6.914 -66.586 17.828 Z " fill="#49738B" transform="translate(496,389)"/>
<path d="M0 0 C5.381 4.912 9.542 10.962 13.75 16.871 C16.788 21.095 19.93 25.238 23.085 29.375 C28.926 37.102 34.435 45.074 40 53 C40.006 52.45 40.012 51.899 40.018 51.332 C40.082 45.589 40.166 39.846 40.262 34.104 C40.296 31.962 40.324 29.82 40.346 27.679 C40.38 24.595 40.432 21.513 40.488 18.43 C40.495 17.476 40.501 16.523 40.508 15.541 C40.642 9.44 41.325 4.771 45.75 0.188 C49.548 -1.817 52.891 -2.516 57.125 -1.48 C60.199 -0.243 62.434 0.921 64.009 3.921 C65.712 9.272 66.434 14.034 66.388 19.637 C66.393 20.448 66.399 21.259 66.404 22.095 C66.417 24.744 66.409 27.393 66.398 30.043 C66.4 31.899 66.403 33.755 66.407 35.611 C66.412 39.487 66.405 43.362 66.391 47.238 C66.374 52.198 66.384 57.157 66.402 62.117 C66.413 65.944 66.409 69.771 66.401 73.598 C66.399 75.427 66.402 77.255 66.409 79.084 C66.417 81.64 66.405 84.195 66.388 86.751 C66.394 87.5 66.4 88.25 66.407 89.023 C66.339 94.631 65.167 98.86 61.121 102.855 C56.554 105.32 50.983 104.422 46.062 103.375 C43.201 101.468 42.526 99.063 41 96 C39.731 94.198 38.415 92.428 37.062 90.688 C33.318 85.785 29.721 80.806 26.188 75.75 C21.075 68.448 15.78 61.3 10.398 54.195 C8.878 52.17 7.405 50.107 6 48 C5.992 48.563 5.984 49.126 5.975 49.706 C5.889 55.579 5.79 61.451 5.683 67.324 C5.644 69.514 5.61 71.704 5.578 73.894 C5.532 77.047 5.474 80.199 5.414 83.352 C5.403 84.327 5.391 85.302 5.379 86.307 C5.25 92.268 4.942 97.338 1.312 102.312 C-1.85 104.62 -4.103 104.868 -8 105 C-12.293 104.272 -14.358 103.675 -17.375 100.5 C-20.207 96.143 -20.16 92.785 -20.177 87.698 C-20.184 86.84 -20.19 85.981 -20.197 85.096 C-20.217 82.246 -20.228 79.397 -20.238 76.547 C-20.242 75.574 -20.246 74.602 -20.251 73.6 C-20.272 68.451 -20.286 63.302 -20.295 58.154 C-20.306 52.836 -20.341 47.519 -20.38 42.202 C-20.406 38.113 -20.415 34.024 -20.418 29.936 C-20.423 27.976 -20.435 26.016 -20.453 24.056 C-20.478 21.31 -20.477 18.565 -20.47 15.819 C-20.483 15.012 -20.496 14.204 -20.509 13.372 C-20.461 8.132 -19.46 5.165 -16 1 C-10.779 -2.243 -5.505 -2.802 0 0 Z M4 45 C5 47 5 47 5 47 Z " fill="#48728A" transform="translate(663,378)"/>
<path d="M0 0 C10.66 10.158 18.504 21.642 19.078 36.816 C19.393 54.983 15.501 69.826 2.613 83.32 C-7.816 92.812 -7.816 92.812 -13.125 92.812 C-13.455 93.472 -13.785 94.132 -14.125 94.812 C-27.697 100.637 -45.452 99.527 -58.949 94.305 C-63.386 92.22 -67.277 89.839 -71.125 86.812 C-72.156 86.07 -73.188 85.327 -74.25 84.562 C-85.843 73.474 -91.906 57.652 -92.5 41.75 C-91.966 28.149 -86.714 15.31 -78.125 4.812 C-77.465 4.812 -76.805 4.812 -76.125 4.812 C-75.795 3.822 -75.465 2.832 -75.125 1.812 C-73.174 0.215 -71.268 -1.216 -69.188 -2.625 C-68.597 -3.025 -68.007 -3.426 -67.398 -3.838 C-46.928 -17.373 -18.721 -16.23 0 0 Z M-59.125 18.812 C-65.451 26.712 -68.777 35.115 -68.41 45.305 C-67.564 52.747 -65.016 60.033 -60.125 65.812 C-59.465 65.812 -58.805 65.812 -58.125 65.812 C-57.795 66.803 -57.465 67.793 -57.125 68.812 C-48.276 74.761 -39.471 76.785 -29 75.25 C-20.647 73.261 -13.89 67.858 -9.125 60.812 C-4.527 53.311 -3.244 44.753 -4.531 36.074 C-7.061 26.394 -12.55 18.928 -21.125 13.812 C-32.501 7.153 -49.932 9.364 -59.125 18.812 Z " fill="#4A738C" transform="translate(614.125,385.1875)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.258 2.083 3.516 2.165 4.812 2.25 C13.404 3.005 23.436 8.375 29.465 14.484 C32.672 18.358 33.858 21.739 33.547 26.781 C32.543 30.856 30.33 33.519 27 36 C23.5 37.188 23.5 37.188 20 37 C17 35.375 17 35.375 14 33 C12.819 32.116 11.631 31.241 10.438 30.375 C9.9 29.97 9.362 29.565 8.809 29.148 C1.752 24.668 -7.65 24.151 -15.75 25.562 C-23.854 27.823 -29.56 32.647 -33.848 39.785 C-37.874 47.524 -38.344 56.444 -35.75 64.75 C-32.211 73.941 -27.547 80.112 -18.438 84.375 C-10.347 87.306 -1.489 87.274 6.562 84.25 C10.127 82.573 13.317 80.942 16 78 C16.33 76.02 16.66 74.04 17 72 C10.73 72 4.46 72 -2 72 C-2.222 68.75 -2.428 65.501 -2.625 62.25 C-2.689 61.33 -2.754 60.409 -2.82 59.461 C-2.872 58.571 -2.923 57.682 -2.977 56.766 C-3.029 55.949 -3.081 55.132 -3.135 54.29 C-2.984 51.732 -2.289 50.191 -1 48 C0.65 48 2.3 48 4 48 C4 47.34 4 46.68 4 46 C4.66 46.33 5.32 46.66 6 47 C7.939 47.073 9.88 47.084 11.82 47.062 C13.584 47.051 13.584 47.051 15.383 47.039 C17.854 47.013 20.326 46.987 22.797 46.961 C28.96 46.921 34.92 46.93 41 48 C43.653 50.653 42.322 59.381 42.35 63.107 C42.367 64.688 42.394 66.269 42.432 67.85 C42.691 78.553 42.134 86.117 34.859 94.453 C20.842 106.394 5.41 111.312 -13 110 C-29.812 107.507 -42.646 100.145 -52.883 86.488 C-61.185 73.535 -64.087 58.088 -60.924 43.055 C-57.647 29.221 -50.415 16.868 -38.297 8.877 C-25.984 1.563 -13.923 0.656 0 0 Z " fill="#4A738B" transform="translate(786,219)"/>
<path d="M0 0 C2.875 2.5 2.875 2.5 5 5 C5.681 5.696 5.681 5.696 6.375 6.406 C13.198 13.656 18.387 23.158 20 33 C20.131 33.731 20.263 34.462 20.398 35.215 C22.243 47.863 19.317 61.896 12.293 72.602 C10.6 74.808 8.827 76.904 7 79 C6.257 80.031 5.515 81.062 4.75 82.125 C-4.463 91.757 -18.246 97.425 -31.445 98.203 C-48.199 98.417 -62.423 94.195 -74.742 82.43 C-80.267 76.485 -87 68.515 -87 60 C-87.66 59.67 -88.32 59.34 -89 59 C-89.729 55.721 -89.813 52.348 -90 49 C-90.054 48.051 -90.108 47.103 -90.164 46.125 C-90.399 38.091 -89.649 29.298 -86 22 C-86.66 21.67 -87.32 21.34 -88 21 C-87.01 20.67 -86.02 20.34 -85 20 C-83.313 17.453 -83.313 17.453 -81.812 14.438 C-81.283 13.426 -80.753 12.414 -80.207 11.371 C-79.809 10.589 -79.41 9.806 -79 9 C-78.34 9 -77.68 9 -77 9 C-76.752 8.421 -76.505 7.842 -76.25 7.246 C-71.134 -1.947 -60.201 -6.913 -50.562 -9.812 C-32.297 -13.908 -15.438 -10.539 0 0 Z M-56 19 C-56 19.66 -56 20.32 -56 21 C-56.99 21.33 -57.98 21.66 -59 22 C-64.932 30.203 -66.888 39.024 -66 49 C-64.472 56.861 -60.317 63.683 -54.688 69.312 C-46.988 74.147 -39.416 76.944 -30.238 75.594 C-19.942 73.181 -13.077 68.325 -7.375 59.43 C-3.082 51.843 -2.543 42.435 -4.793 34.043 C-6.341 29.991 -7.742 26.931 -11 24 C-11.66 24.33 -12.32 24.66 -13 25 C-13.062 24.257 -13.124 23.515 -13.188 22.75 C-14.281 19.048 -15.79 18.094 -19 16 C-31.02 9.99 -45.367 10.326 -56 19 Z " fill="#4A738B" transform="translate(417,231)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.99 2.33 1.98 2.66 3 3 C5.006 6.695 5.664 9.849 5 14 C2.846 17.218 0.5 20.25 -3 22 C-9.632 22.829 -13.027 21.228 -18.508 17.508 C-25.33 13.381 -32.243 13.38 -40 14 C-43.716 14.848 -43.716 14.848 -46 17 C-46.975 20.09 -46.975 20.09 -47 23 C-43.427 26.135 -40.312 27.845 -35.809 29.332 C-34.616 29.739 -33.423 30.145 -32.193 30.564 C-29.718 31.387 -27.241 32.208 -24.764 33.025 C-13.732 36.793 -1.227 41.471 5 52 C9.431 62.675 10.14 71.212 6 82 C1.489 90.521 -5.811 95.633 -14.676 98.992 C-30.402 103.76 -46.419 102.68 -61 95 C-66.127 92.162 -70.254 88.728 -72 83 C-72.408 78.151 -71.731 74.981 -68.812 71.062 C-65.908 68.933 -63.474 67.889 -60 67 C-60 67.66 -60 68.32 -60 69 C-58.979 69.093 -58.979 69.093 -57.938 69.188 C-54.588 70.114 -52.687 71.562 -50.039 73.746 C-44.582 77.102 -38.646 77.34 -32.438 77.312 C-31.344 77.337 -31.344 77.337 -30.229 77.361 C-25.436 77.366 -22.138 76.657 -18 74 C-15.65 70.474 -15.336 69.15 -16 65 C-20.557 60.421 -25.363 58.359 -31.375 56.188 C-32.206 55.876 -33.038 55.565 -33.895 55.244 C-35.926 54.485 -37.962 53.741 -40 53 C-40.33 53.66 -40.66 54.32 -41 55 C-41 54.01 -41 53.02 -41 52 C-41.639 51.918 -42.279 51.835 -42.938 51.75 C-53.104 49.26 -62.886 43.719 -68.848 35 C-72.494 28.672 -73.127 21.53 -71.383 14.52 C-68.246 5.568 -62.716 -0.836 -54.438 -5.438 C-36.238 -13.7 -16.512 -10.174 0 0 Z " fill="#4B748C" transform="translate(385,382)"/>
<path d="M0 0 C0.677 0.473 1.354 0.946 2.051 1.434 C3 2.073 3.948 2.712 4.926 3.371 C8.791 7.122 10.799 10.641 11.551 15.996 C10.953 20.104 9.656 22.233 7.051 25.434 C3.78 27.614 1.931 28.123 -1.949 28.434 C-5.79 27.625 -7.909 25.604 -10.699 22.996 C-17.467 16.854 -26.661 15.014 -35.676 15.152 C-43.749 16.151 -52.298 19.818 -57.414 26.285 C-63.07 34.777 -63.843 43.406 -62.949 53.434 C-61.191 60.808 -57.44 67.265 -51.184 71.68 C-49.488 72.686 -47.719 73.565 -45.949 74.434 C-45.186 74.825 -44.423 75.217 -43.637 75.621 C-34.233 78.464 -25.195 77.6 -16.398 73.141 C-12.501 70.698 -9.657 68.227 -6.887 64.559 C-5.28 62.542 -4.482 61.582 -1.98 60.887 C2.451 60.721 5.665 61.629 9.051 64.434 C11.331 67.265 12.007 69.141 12.551 72.746 C11.564 80.021 7.096 85.088 1.578 89.695 C-3.581 93.243 -9.284 95.802 -14.949 98.434 C-15.939 98.929 -15.939 98.929 -16.949 99.434 C-33.493 102.103 -49.557 100.49 -63.949 91.434 C-75.901 82.305 -84.338 70.472 -86.949 55.434 C-87.239 51.97 -87.274 48.533 -87.262 45.059 C-87.278 44.177 -87.294 43.295 -87.311 42.387 C-87.321 28.034 -81.052 15.959 -71.305 5.656 C-52.428 -12.156 -21.526 -14.128 0 0 Z " fill="#49738B" transform="translate(304.94921875,228.56640625)"/>
<path d="M0 0 C3.084 1.199 6.061 2.48 9 4 C9.99 4.495 9.99 4.495 11 5 C11.495 6.98 11.495 6.98 12 9 C16.95 9.99 16.95 9.99 22 11 C22.66 9.02 23.32 7.04 24 5 C33.155 3.627 33.155 3.627 36 5 C38.625 7.438 38.625 7.438 41 10 C41.99 10.99 41.99 10.99 43 12 C43.99 11.34 44.98 10.68 46 10 C50.819 8.929 53.539 8.961 58 11 C58.66 10.67 59.32 10.34 60 10 C60.99 10.495 60.99 10.495 62 11 C62.289 10.196 62.577 9.391 62.875 8.562 C64 6 64 6 66 5 C70.975 4.363 75.673 4.291 80.125 6.812 C82.415 8.689 82.94 9.66 83.461 12.605 C83.512 16.416 83.236 19.942 82.406 23.664 C81.783 26.127 81.783 26.127 83 28.875 C84.2 32.624 83.764 35.182 83 39 C82.67 38.34 82.34 37.68 82 37 C81.022 37.017 81.022 37.017 80.023 37.035 C72.55 37.112 72.55 37.112 68.938 36.375 C65.364 35.919 62.691 36.66 59.227 37.512 C54.517 38.544 50.444 38.19 45.875 36.875 C42.184 35.652 42.184 35.652 39.562 37 C36.013 38.385 33.591 37.465 29.988 36.508 C26.875 35.713 24.756 36.304 21.688 37.062 C17.682 37.881 14.31 37.989 10.25 37.562 C3.12 36.816 -3.828 37.236 -10.958 37.77 C-17.289 38.171 -22.847 37.542 -29 36 C-29 33.36 -29 30.72 -29 28 C-29.99 28 -30.98 28 -32 28 C-32 27.34 -32 26.68 -32 26 C-33.65 26.66 -35.3 27.32 -37 28 C-37.33 30.64 -37.66 33.28 -38 36 C-44.964 38.277 -44.964 38.277 -49 37 C-49.893 31.064 -49.97 25.93 -49 20 C-49.99 20 -50.98 20 -52 20 C-52 20.66 -52 21.32 -52 22 C-52.66 22 -53.32 22 -54 22 C-53.67 24.97 -53.34 27.94 -53 31 C-53.66 31 -54.32 31 -55 31 C-55 31.99 -55 32.98 -55 34 C-54.34 34.33 -53.68 34.66 -53 35 C-53.66 35 -54.32 35 -55 35 C-55 35.66 -55 36.32 -55 37 C-56.98 37 -58.96 37 -61 37 C-61.33 37.66 -61.66 38.32 -62 39 C-62 38.34 -62 37.68 -62 37 C-62.66 37 -63.32 37 -64 37 C-65.013 33.962 -65.129 31.888 -65.133 28.711 C-65.134 27.687 -65.135 26.664 -65.137 25.609 C-65.133 24.542 -65.129 23.475 -65.125 22.375 C-65.129 21.3 -65.133 20.225 -65.137 19.117 C-65.135 17.586 -65.135 17.586 -65.133 16.023 C-65.132 15.084 -65.131 14.145 -65.129 13.177 C-65 11 -65 11 -64 10 C-60.549 9.749 -57.084 9.815 -53.625 9.812 C-52.653 9.8 -51.681 9.788 -50.68 9.775 C-49.28 9.772 -49.28 9.772 -47.852 9.77 C-46.994 9.765 -46.136 9.761 -45.252 9.757 C-43 10 -43 10 -40 12 C-38.312 11.39 -36.648 10.712 -35 10 C-28.444 8.778 -28.444 8.778 -26 10 C-26 10.66 -26 11.32 -26 12 C-25.348 11.783 -24.695 11.567 -24.023 11.344 C-16.566 8.968 -16.566 8.968 -12.688 9.188 C-9.647 9.293 -9.647 9.293 -7.438 6.062 C-6.633 5.052 -5.829 4.041 -5 3 C-2.188 2.125 -2.188 2.125 0 2 C0 1.34 0 0.68 0 0 Z M51 21 C52 23 52 23 52 23 Z M54 23 C55 25 55 25 55 25 Z M77 23 C77 24.32 77 25.64 77 27 C77.99 26.67 78.98 26.34 80 26 C79.34 26 78.68 26 78 26 C77.67 25.01 77.34 24.02 77 23 Z M53 25 C54 27 54 27 54 27 Z " fill="#F0F2F0" transform="translate(349,620)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C5.625 2.167 5.625 2.167 8 2 C8.99 5.96 8.99 5.96 10 10 C13.185 10.277 13.185 10.277 16 8 C18.121 7.516 18.121 7.516 20.438 7.25 C21.58 7.103 21.58 7.103 22.746 6.953 C25 7 25 7 27.035 8.047 C29.556 9.27 30.352 8.767 33 8 C35.249 7.932 37.5 7.915 39.75 7.938 C41.506 7.951 41.506 7.951 43.297 7.965 C44.189 7.976 45.081 7.988 46 8 C46.33 7.34 46.66 6.68 47 6 C48.465 6.567 49.922 7.154 51.375 7.75 C52.187 8.075 52.999 8.4 53.836 8.734 C56.445 10.26 56.907 11.218 58 14 C58.227 16.382 58.227 16.382 58.195 18.984 C58.186 20.386 58.186 20.386 58.176 21.816 C58.151 23.269 58.151 23.269 58.125 24.75 C58.116 25.734 58.107 26.717 58.098 27.73 C58.074 30.154 58.038 32.577 58 35 C54.7 35 51.4 35 48 35 C47.837 32.982 47.674 30.964 47.512 28.945 C47.126 26.605 47.126 26.605 44 25 C44.99 24.67 45.98 24.34 47 24 C46.01 24 45.02 24 44 24 C43.963 24.699 43.925 25.397 43.887 26.117 C43.788 27.482 43.788 27.482 43.688 28.875 C43.629 29.78 43.571 30.685 43.512 31.617 C43 34 43 34 40 36 C36.312 35.625 36.312 35.625 33 35 C32.67 34.01 32.34 33.02 32 32 C31.319 32.474 30.639 32.949 29.938 33.438 C25.192 35.962 21.522 36.387 16.312 35.062 C15.219 34.712 14.126 34.361 13 34 C11.68 34 10.36 34 9 34 C8.01 34.99 8.01 34.99 7 36 C4.793 36.012 4.793 36.012 2.188 35.688 C1.335 35.588 0.483 35.489 -0.395 35.387 C-2.952 35.007 -5.471 34.531 -8 34 C-8.195 32.23 -8.381 30.459 -8.562 28.688 C-8.719 27.208 -8.719 27.208 -8.879 25.699 C-9 23 -9 23 -8 20 C-8.66 20 -9.32 20 -10 20 C-10.66 21.32 -11.32 22.64 -12 24 C-12.495 22.02 -12.495 22.02 -13 20 C-13 24.95 -13 29.9 -13 35 C-19.024 35.661 -24.044 36.141 -30 35 C-33.335 34.909 -36.665 34.941 -40 35 C-40 34.34 -40 33.68 -40 33 C-40.606 33.24 -41.212 33.48 -41.836 33.727 C-47.757 35.822 -51.79 36.154 -57.871 34.543 C-60.118 33.876 -60.118 33.876 -63 34 C-63.33 34 -63.66 34 -64 34 C-64.144 32.907 -64.289 31.814 -64.438 30.688 C-64.883 27.069 -64.883 27.069 -66 24 C-66.66 24 -67.32 24 -68 24 C-68.33 24.66 -68.66 25.32 -69 26 C-69.812 23.312 -69.812 23.312 -70 20 C-67.562 17.125 -67.562 17.125 -65 15 C-67.915 16.074 -69.778 16.778 -72 19 C-72.465 22.122 -72.465 22.122 -72.625 25.625 C-72.7 26.814 -72.775 28.002 -72.852 29.227 C-72.901 30.142 -72.95 31.057 -73 32 C-74.32 32 -75.64 32 -77 32 C-77 25.07 -77 18.14 -77 11 C-75.68 11 -74.36 11 -73 11 C-72.505 12.485 -72.505 12.485 -72 14 C-70.02 12.68 -68.04 11.36 -66 10 C-71.28 10 -76.56 10 -82 10 C-81.34 11.98 -80.68 13.96 -80 16 C-80.687 15.893 -81.374 15.786 -82.082 15.676 C-82.983 15.556 -83.884 15.436 -84.812 15.312 C-85.706 15.185 -86.599 15.057 -87.52 14.926 C-90.028 14.774 -90.028 14.774 -91.805 16.262 C-93.758 19.103 -93.298 21.665 -93 25 C-92.34 25.99 -91.68 26.98 -91 28 C-88 28.417 -88 28.417 -85 28 C-84.34 27.34 -83.68 26.68 -83 26 C-82.01 26 -81.02 26 -80 26 C-81.75 30.875 -81.75 30.875 -84 32 C-91.378 32.476 -91.378 32.476 -94.125 30.375 C-94.744 29.921 -95.362 29.467 -96 29 C-99.836 29.824 -99.836 29.824 -103 32 C-107.892 32.498 -110.116 32.474 -114.5 30.125 C-117.101 27.638 -117.466 25.547 -117.625 22 C-117.36 17.551 -116.373 15.076 -113 12 C-109.936 10.468 -107.383 10.771 -104 11 C-99.4 13.2 -99.4 13.2 -98 16 C-97.092 15.196 -96.185 14.391 -95.25 13.562 C-92.897 11.589 -91.131 10.361 -88 10 C-90.97 9.505 -90.97 9.505 -94 9 C-90.898 7.155 -89.162 6.852 -85.625 7.438 C-80.05 8.158 -74.777 7.706 -69.199 7.156 C-65.401 6.971 -62.408 7.296 -59 9 C-58.34 8.67 -57.68 8.34 -57 8 C-52.253 7.736 -47.543 7.691 -42.875 8.625 C-39.324 9.088 -36.675 8.282 -33.238 7.41 C-30.034 6.823 -27.184 7.449 -24 8 C-20.592 7.604 -17.992 6.662 -15 5 C-14.01 5.99 -14.01 5.99 -13 7 C-12.01 6.67 -11.02 6.34 -10 6 C-10 5.34 -10 4.68 -10 4 C-8.639 3.567 -8.639 3.567 -7.25 3.125 C-4.678 2.235 -2.387 1.285 0 0 Z M19 18 C18.67 18.99 18.34 19.98 18 21 C18.66 21 19.32 21 20 21 C19.67 20.01 19.34 19.02 19 18 Z M21 18 C21.33 19.65 21.66 21.3 22 23 C22.66 21.68 23.32 20.36 24 19 C23.01 18.67 22.02 18.34 21 18 Z M-109 20 C-109.33 20.66 -109.66 21.32 -110 22 C-109.34 22 -108.68 22 -108 22 C-108.33 21.34 -108.66 20.68 -109 20 Z M-107 20 C-106.67 20.66 -106.34 21.32 -106 22 C-105.67 21.34 -105.34 20.68 -105 20 C-105.66 20 -106.32 20 -107 20 Z M43 20 C43.33 20.66 43.66 21.32 44 22 C44.66 22 45.32 22 46 22 C46 21.34 46 20.68 46 20 C45.01 20 44.02 20 43 20 Z M21 23 C20.34 23.66 19.68 24.32 19 25 C19.99 25 20.98 25 22 25 C21.67 24.34 21.34 23.68 21 23 Z " fill="#EFF1EF" transform="translate(776,622)"/>
<path d="M0 0 C2.638 2.262 3.491 3.069 4.625 6.25 C4.821 9.131 4.908 11.914 4.9 14.795 C4.907 16.086 4.907 16.086 4.915 17.403 C4.929 20.246 4.928 23.09 4.926 25.934 C4.93 27.911 4.934 29.889 4.939 31.867 C4.946 36.012 4.946 40.157 4.941 44.302 C4.935 49.613 4.952 54.923 4.975 60.234 C4.99 64.318 4.991 68.403 4.988 72.487 C4.988 74.445 4.993 76.403 5.003 78.361 C5.016 81.1 5.009 83.837 4.997 86.576 C5.009 87.788 5.009 87.788 5.02 89.024 C4.952 96.296 2.114 99.833 -2.375 105.25 C-2.375 104.59 -2.375 103.93 -2.375 103.25 C-3.365 103.745 -3.365 103.745 -4.375 104.25 C-9.581 104.65 -13.021 104.218 -17.375 101.25 C-20.557 96.281 -20.816 92.197 -20.795 86.494 C-20.803 85.635 -20.812 84.776 -20.821 83.89 C-20.844 81.062 -20.845 78.234 -20.844 75.406 C-20.851 73.434 -20.859 71.461 -20.867 69.489 C-20.88 65.36 -20.882 61.231 -20.876 57.102 C-20.87 51.816 -20.901 46.53 -20.941 41.244 C-20.967 37.173 -20.97 33.101 -20.966 29.03 C-20.968 27.081 -20.978 25.132 -20.995 23.183 C-21.018 20.457 -21.008 17.732 -20.99 15.006 C-21.003 14.205 -21.017 13.403 -21.03 12.578 C-20.944 6.797 -19.493 4.311 -15.375 0.25 C-10.661 -3.326 -4.918 -2.55 0 0 Z " fill="#48718A" transform="translate(595.375,222.75)"/>
<path d="M0 0 C0.99 0.99 0.99 0.99 2 2 C1.67 2.33 1.34 2.66 1 3 C0.749 6.284 0.815 9.582 0.812 12.875 C0.8 13.799 0.788 14.724 0.775 15.676 C0.773 16.564 0.772 17.452 0.77 18.367 C0.765 19.183 0.761 19.999 0.757 20.839 C0.866 23.296 0.866 23.296 3 26 C3.5 29.375 3.5 29.375 3 33 C1.176 35.126 -0.661 36.392 -3 38 C-3.66 37.34 -4.32 36.68 -5 36 C-9.343 34.941 -13.514 34.94 -17.949 35.062 C-20 35 -20 35 -23 34 C-23.99 34.99 -24.98 35.98 -26 37 C-26 36.34 -26 35.68 -26 35 C-27.862 35.052 -27.862 35.052 -29.762 35.105 C-31.404 35.134 -33.046 35.161 -34.688 35.188 C-35.912 35.225 -35.912 35.225 -37.162 35.264 C-42.084 35.323 -44.857 34.906 -49 32 C-51.217 32.65 -51.217 32.65 -53 34 C-55.46 35.23 -57.132 35.133 -59.875 35.125 C-60.739 35.128 -61.602 35.13 -62.492 35.133 C-64.807 35.01 -66.783 34.65 -69 34 C-69 33.34 -69 32.68 -69 32 C-73.332 32.86 -73.332 32.86 -77 35 C-82.44 35.584 -86.56 35.453 -91 32 C-91.949 31.526 -92.898 31.051 -93.875 30.562 C-96.5 27.398 -96.306 26.218 -96.125 22.188 C-95.375 16.394 -94.03 13.214 -90 9 C-86.081 6.628 -82.512 6.624 -78 7 C-74.937 7.845 -71.968 8.869 -69 10 C-68.34 9.01 -67.68 8.02 -67 7 C-61.239 6.503 -56.531 7.42 -51 9 C-51 8.67 -51 8.34 -51 8 C-47.37 7.67 -43.74 7.34 -40 7 C-39.333 11.667 -38.667 16.333 -38 21 C-36.482 16.447 -35.89 12.841 -36 8 C-27.977 7.1 -20.07 6.885 -12 7 C-12 5.02 -12 3.04 -12 1 C-4.437 -1.521 -4.437 -1.521 0 0 Z M-11 6 C-10 8 -10 8 -10 8 Z M-72 11 C-71 13 -71 13 -71 13 Z M-63 18 C-63 19.98 -63 21.96 -63 24 C-62.01 24 -61.02 24 -60 24 C-60 22.35 -60 20.7 -60 19 C-60.99 18.67 -61.98 18.34 -63 18 Z M-14 19 C-14.33 19.99 -14.66 20.98 -15 22 C-14.01 22.495 -14.01 22.495 -13 23 C-13.33 21.68 -13.66 20.36 -14 19 Z M-11 19 C-11.495 20.98 -11.495 20.98 -12 23 C-11.34 23 -10.68 23 -10 23 C-10.33 21.68 -10.66 20.36 -11 19 Z M-58 20 C-58.33 20.99 -58.66 21.98 -59 23 C-58.01 23.495 -58.01 23.495 -57 24 C-57.33 22.68 -57.66 21.36 -58 20 Z M-17 21 C-16 23 -16 23 -16 23 Z " fill="#F4F6F5" transform="translate(684,673)"/>
<path d="M0 0 C1.691 0.09 3.38 0.246 5.062 0.438 C6.441 0.59 6.441 0.59 7.848 0.746 C8.558 0.83 9.268 0.914 10 1 C10.338 1.82 10.675 2.64 11.023 3.484 C11.469 4.562 11.915 5.64 12.375 6.75 C12.816 7.817 13.257 8.885 13.711 9.984 C14.733 12.376 15.802 14.694 17 17 C17.422 15.929 17.843 14.858 18.277 13.754 C18.831 12.357 19.384 10.96 19.938 9.562 C20.215 8.855 20.493 8.148 20.779 7.42 C22.887 2.113 22.887 2.113 24 1 C29.18 0.21 34.788 -0.521 40 0 C42 3 42 3 42 6 C42.66 6 43.32 6 44 6 C44.495 6.99 44.495 6.99 45 8 C45.941 7.988 46.882 7.977 47.852 7.965 C49.097 7.956 50.342 7.947 51.625 7.938 C53.47 7.92 53.47 7.92 55.352 7.902 C58.678 7.991 61.729 8.445 65 9 C67.747 8.111 68.925 7.083 70.875 4.938 C73.61 2.444 75.336 2.22 79 2 C82.375 2.438 82.375 2.438 85 3 C85 5 85 5 83.5 6.625 C83.005 7.079 82.51 7.533 82 8 C83.145 7.938 83.145 7.938 84.312 7.875 C87 8 87 8 90 10 C90.438 12.312 90.438 12.312 90 15 C88.693 16.687 87.358 18.354 86 20 C85.211 22.685 85.211 22.685 85 25 C85.99 25 86.98 25 88 25 C88 26.32 88 27.64 88 29 C88.66 29 89.32 29 90 29 C89.398 31.02 88.727 33.021 88 35 C84.551 36.725 80.772 36.498 77 36 C74.625 34.188 74.625 34.188 73 32 C72.423 31.423 71.845 30.845 71.25 30.25 C70.837 29.837 70.425 29.425 70 29 C69.67 31.31 69.34 33.62 69 36 C65.37 36 61.74 36 58 36 C58 34.68 58 33.36 58 32 C57.34 32 56.68 32 56 32 C56 29.69 56 27.38 56 25 C52.685 26.709 52.685 26.709 52.312 30.125 C52.209 31.074 52.106 32.023 52 33 C52.99 33.495 52.99 33.495 54 34 C52.68 34 51.36 34 50 34 C49.34 34.99 48.68 35.98 48 37 C45 37.125 45 37.125 42 37 C41.01 37.495 41.01 37.495 40 38 C40 37.34 40 36.68 40 36 C38.742 36.082 37.484 36.165 36.188 36.25 C34.043 36.391 34.043 36.391 32 36 C29.263 33.263 28.537 30.924 28.188 27.125 C28.099 26.303 28.01 25.48 27.918 24.633 C28.017 21.468 28.887 18.948 30 16 C25.371 25.943 25.371 25.943 21 36 C18.03 36 15.06 36 12 36 C11.587 34.886 11.175 33.773 10.75 32.625 C9.645 30.007 9.251 29.165 6.812 27.562 C6.214 27.377 5.616 27.191 5 27 C5.99 27 6.98 27 8 27 C7.34 26.01 6.68 25.02 6 24 C5.163 21.488 4.382 18.974 3.625 16.438 C3.363 15.562 3.102 14.687 2.832 13.785 C2.557 12.866 2.283 11.947 2 11 C1.438 9.125 0.875 7.25 0.312 5.375 C-0.125 3.917 -0.562 2.458 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M82 18 C82.33 18.66 82.66 19.32 83 20 C83.33 19.34 83.66 18.68 84 18 C83.34 18 82.68 18 82 18 Z M55 21 C55 24 55 24 55 24 Z M82 22 C83 24 83 24 83 24 Z M49 24 C49 25.65 49 27.3 49 29 C49.33 29 49.66 29 50 29 C50 27.35 50 25.7 50 24 C49.67 24 49.34 24 49 24 Z M71 25 C72 28 72 28 72 28 Z M57 29 C58 31 58 31 58 31 Z " fill="#F0F2F2" transform="translate(347,672)"/>
<path d="M0 0 C2.969 1.39 4.697 2.697 7 5 C9.211 5.243 9.211 5.243 11.625 5.125 C12.442 5.107 13.26 5.089 14.102 5.07 C14.728 5.047 15.355 5.024 16 5 C17.502 8.003 17.093 10.791 17.062 14.125 C17.053 15.406 17.044 16.688 17.035 18.008 C17.024 18.995 17.012 19.983 17 21 C17.99 21 18.98 21 20 21 C21.178 17.993 21.178 17.993 21 14.625 C21 11.517 21.339 9.007 22 6 C26.292 5.942 30.583 5.906 34.875 5.875 C36.098 5.858 37.322 5.841 38.582 5.824 C39.749 5.818 40.915 5.811 42.117 5.805 C43.196 5.794 44.275 5.784 45.386 5.773 C48 6 48 6 50 8 C49.625 10.625 49.625 10.625 49 13 C49.99 13.495 49.99 13.495 51 14 C47.635 16.212 44.95 17.316 41 18 C40.939 18.999 40.879 19.998 40.816 21.027 C40.691 22.963 40.691 22.963 40.562 24.938 C40.441 26.869 40.441 26.869 40.316 28.84 C40 32 40 32 39 33 C37.511 33.087 36.019 33.107 34.527 33.098 C33.628 33.094 32.729 33.091 31.803 33.088 C30.384 33.075 30.384 33.075 28.938 33.062 C27.988 33.058 27.039 33.053 26.061 33.049 C23.707 33.037 21.354 33.021 19 33 C18.505 33.99 18.505 33.99 18 35 C18 34.34 18 33.68 18 33 C17.154 33.041 16.309 33.082 15.438 33.125 C12 33 12 33 8.625 31.75 C4.615 30.92 4.454 31.055 1.25 33.062 C-2.516 35.267 -4.741 35.012 -8.938 33.938 C-11.777 33.09 -11.777 33.09 -14 32 C-14.33 31.01 -14.66 30.02 -15 29 C-15.33 29.66 -15.66 30.32 -16 31 C-16.99 31 -17.98 31 -19 31 C-18.67 30.01 -18.34 29.02 -18 28 C-18.66 28 -19.32 28 -20 28 C-20.66 27.01 -21.32 26.02 -22 25 C-23.32 24.67 -24.64 24.34 -26 24 C-26 19.71 -26 15.42 -26 11 C-25.34 11 -24.68 11 -24 11 C-24 10.34 -24 9.68 -24 9 C-22.783 8.794 -21.566 8.587 -20.312 8.375 C-15.468 7.2 -13.865 4.961 -11 1 C-7.556 -0.722 -3.773 -0.379 0 0 Z " fill="#E7EAE8" transform="translate(226,624)"/>
<path d="M0 0 C1.156 0.126 1.156 0.126 2.336 0.254 C3.339 0.356 4.342 0.458 5.375 0.562 C6.373 0.667 7.37 0.771 8.398 0.879 C10.984 1.211 10.984 1.211 13 0 C15.793 -0.015 18.527 0.05 21.312 0.188 C22.072 0.206 22.832 0.225 23.615 0.244 C28.626 0.492 31.305 1.32 35 5 C35.577 4.711 36.155 4.423 36.75 4.125 C37.864 3.568 37.864 3.568 39 3 C39.59 2.652 40.181 2.304 40.789 1.945 C43.523 0.776 45.66 0.734 48.625 0.75 C49.587 0.745 50.548 0.74 51.539 0.734 C54 1 54 1 56 3 C57.877 2.636 57.877 2.636 60 2 C61.749 1.934 63.5 1.914 65.25 1.938 C66.142 1.947 67.034 1.956 67.953 1.965 C68.629 1.976 69.304 1.988 70 2 C67 4 67 4 63.5 5.438 C60.462 6.794 59.14 7.635 57 10 C56.34 10 55.68 10 55 10 C55 9.01 55 8.02 55 7 C51.338 4.656 48.276 4.475 44 5 C40.856 6.726 40.856 6.726 39 10 C38.309 13.475 38.286 16.528 39 20 C41.12 22.782 42.486 23.741 45.938 24.332 C50.623 24.572 52.999 23.445 57 21 C59.188 21.75 59.188 21.75 61 23 C63.525 24.262 65.312 24.099 68.125 24.062 C69.49 24.049 69.49 24.049 70.883 24.035 C71.581 24.024 72.28 24.012 73 24 C73.66 22.35 74.32 20.7 75 19 C73.948 19.742 73.948 19.742 72.875 20.5 C69.547 22.236 67.7 22.563 64 22 C61.205 19.671 61.015 18.12 60.562 14.5 C61 11 61 11 62.688 8.562 C65.483 6.674 66.687 6.515 70 7 C72.812 8 72.812 8 75 9 C74.01 7.515 74.01 7.515 73 6 C73 5.01 73 4.02 73 3 C76.619 1.794 79.925 1.892 83.688 1.938 C84.389 1.942 85.091 1.947 85.814 1.951 C87.543 1.963 89.272 1.981 91 2 C88.098 4.214 85.154 6.16 82 8 C82 6.68 82 5.36 82 4 C80.68 4 79.36 4 78 4 C78 10.93 78 17.86 78 25 C79.32 25 80.64 25 82 25 C81.954 24.108 81.907 23.216 81.859 22.297 C81.823 21.126 81.787 19.956 81.75 18.75 C81.704 17.59 81.657 16.43 81.609 15.234 C82 12 82 12 83.766 9.578 C86 8 86 8 90 8 C89 11 89 11 86 13 C85.934 14.94 85.883 16.884 85.961 18.824 C86.017 21.967 85.516 24.904 85 28 C84.143 27.988 83.286 27.977 82.402 27.965 C75.298 27.88 75.298 27.88 68.25 28.625 C65.092 28.989 63.447 28.721 60.438 27.938 C56.259 26.863 54.069 27.496 50 29 C45.167 28.746 41.968 27.765 38 25 C37.34 25.66 36.68 26.32 36 27 C32.068 28.011 28.019 28.448 24 29 C23.67 29.99 23.34 30.98 23 32 C22.34 30.68 21.68 29.36 21 28 C21.021 29.299 21.041 30.599 21.062 31.938 C21.084 33.292 21.071 34.648 21 36 C20 37 20 37 17.934 37.098 C17.11 37.086 16.286 37.074 15.438 37.062 C14.611 37.053 13.785 37.044 12.934 37.035 C12.296 37.024 11.657 37.012 11 37 C10.01 34.36 9.02 31.72 8 29 C6.783 29.021 5.566 29.041 4.312 29.062 C1.08 29.068 -1.849 28.788 -5 28 C-5 27.34 -5 26.68 -5 26 C-5.66 25.67 -6.32 25.34 -7 25 C-6.34 25 -5.68 25 -5 25 C-5.495 22.525 -5.495 22.525 -6 20 C-2.37 20.33 1.26 20.66 5 21 C5 20.01 5 19.02 5 18 C4.06 17.619 4.06 17.619 3.102 17.23 C2.284 16.886 1.467 16.542 0.625 16.188 C-0.187 15.851 -0.999 15.515 -1.836 15.168 C-4 14 -4 14 -6 11 C-6.062 8.562 -6.062 8.562 -5 6 C-2.759 4.338 -0.524 3.239 2 2 C1.34 1.34 0.68 0.68 0 0 Z M26 11 C27 13 27 13 27 13 Z M66 11 C67 13 67 13 67 13 Z M21 12 C22 16 22 16 22 16 Z " fill="#E9EDED" transform="translate(621,629)"/>
<path d="M0 0 C3.062 0.375 3.062 0.375 5 2.062 C6.327 4.951 5.789 6.352 5.062 9.375 C6.491 9.232 6.491 9.232 7.949 9.086 C9.203 8.975 10.458 8.864 11.75 8.75 C12.991 8.634 14.233 8.518 15.512 8.398 C19.039 8.375 20.931 8.876 24.062 10.375 C25.722 10.744 27.387 11.088 29.062 11.375 C29.062 10.385 29.062 9.395 29.062 8.375 C29.833 8.398 30.604 8.421 31.398 8.445 C32.903 8.472 32.903 8.472 34.438 8.5 C35.435 8.523 36.433 8.546 37.461 8.57 C40.225 8.647 40.225 8.647 42.062 6.375 C42.77 4.725 43.442 3.059 44.062 1.375 C50.215 0.496 50.215 0.496 52.062 0.375 C53.062 1.375 53.062 1.375 53.176 4.701 C53.176 6.177 53.17 7.653 53.16 9.129 C53.159 9.904 53.157 10.68 53.156 11.479 C53.15 13.965 53.138 16.451 53.125 18.938 C53.12 20.619 53.115 22.301 53.111 23.982 C53.1 28.113 53.083 32.244 53.062 36.375 C49.762 36.375 46.462 36.375 43.062 36.375 C42.733 37.365 42.403 38.355 42.062 39.375 C41.733 38.715 41.403 38.055 41.062 37.375 C38.592 36.567 38.592 36.567 35.688 36.062 C34.71 35.86 33.733 35.658 32.727 35.449 C30.051 35.094 30.051 35.094 28.336 36.707 C27.916 37.257 27.495 37.808 27.062 38.375 C26.403 39.035 25.743 39.695 25.062 40.375 C20.313 38.5 20.313 38.5 18.062 37.375 C17.75 34.826 17.549 32.371 17.438 29.812 C17.399 29.102 17.36 28.391 17.32 27.658 C17.226 25.898 17.143 24.136 17.062 22.375 C16.072 22.045 15.082 21.715 14.062 21.375 C13.733 22.035 13.402 22.695 13.062 23.375 C13.723 23.375 14.382 23.375 15.062 23.375 C14.733 24.035 14.402 24.695 14.062 25.375 C13.835 27.246 13.653 29.122 13.5 31 C13.356 32.774 13.211 34.548 13.062 36.375 C7.123 36.375 1.183 36.375 -4.938 36.375 C-5.749 33.128 -6.033 31.438 -5.816 28.266 C-5.768 27.512 -5.72 26.757 -5.67 25.98 C-5.586 24.815 -5.586 24.815 -5.5 23.625 C-5.448 22.832 -5.396 22.039 -5.342 21.223 C-5.213 19.273 -5.076 17.324 -4.938 15.375 C-5.598 15.375 -6.257 15.375 -6.938 15.375 C-6.611 13.041 -6.276 10.708 -5.938 8.375 C-5.793 7.282 -5.649 6.189 -5.5 5.062 C-4.704 1.258 -3.86 0.493 0 0 Z M15.062 18.375 C15.392 19.035 15.723 19.695 16.062 20.375 C16.723 20.045 17.382 19.715 18.062 19.375 C17.072 19.045 16.082 18.715 15.062 18.375 Z M42.062 21.375 C43.062 24.375 43.062 24.375 43.062 24.375 Z M36.062 22.375 C36.392 23.695 36.722 25.015 37.062 26.375 C38.382 26.045 39.702 25.715 41.062 25.375 C40.403 24.385 39.743 23.395 39.062 22.375 C38.072 22.375 37.082 22.375 36.062 22.375 Z " fill="#F2F4F4" transform="translate(450.9375,671.625)"/>
<path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.062 C3.778 1.269 3.778 1.269 4.125 2.5 C4.558 3.346 4.991 4.191 5.438 5.062 C7.096 5.436 8.763 5.77 10.438 6.062 C11.812 7.875 11.812 7.875 12.438 10.062 C12.062 12.312 12.062 12.312 11.438 14.062 C10.778 13.732 10.117 13.403 9.438 13.062 C9.438 13.722 9.438 14.383 9.438 15.062 C7.788 15.062 6.138 15.062 4.438 15.062 C4.933 18.033 4.933 18.033 5.438 21.062 C7.087 21.393 8.737 21.722 10.438 22.062 C11.428 27.013 11.428 27.013 12.438 32.062 C11.489 32.372 10.54 32.681 9.562 33 C6.577 33.936 6.577 33.936 4.438 35.062 C4.438 34.403 4.438 33.742 4.438 33.062 C3.365 32.959 2.293 32.856 1.188 32.75 C-2.421 32.088 -3.926 31.488 -6.562 29.062 C-7.522 30.053 -7.522 30.053 -8.5 31.062 C-12.609 33.746 -15.781 33.67 -20.562 33.062 C-23.5 32.062 -23.5 32.062 -25.562 31.062 C-26.222 31.062 -26.883 31.062 -27.562 31.062 C-27.893 32.383 -28.222 33.702 -28.562 35.062 C-30.048 34.567 -30.048 34.567 -31.562 34.062 C-33.226 33.714 -34.892 33.378 -36.562 33.062 C-36.562 34.053 -36.562 35.043 -36.562 36.062 C-40.302 35.553 -43.294 34.961 -46.562 33.062 C-47.449 32.588 -48.336 32.114 -49.25 31.625 C-51.562 30.062 -51.562 30.062 -52.562 27.062 C-52.578 25.506 -52.53 23.948 -52.441 22.395 C-52.393 21.502 -52.345 20.609 -52.295 19.689 C-52.239 18.761 -52.183 17.832 -52.125 16.875 C-52.073 15.935 -52.021 14.994 -51.967 14.025 C-51.837 11.704 -51.702 9.383 -51.562 7.062 C-43.431 5.316 -37.214 4.385 -29.562 8.062 C-29.232 7.403 -28.903 6.742 -28.562 6.062 C-27.572 6.722 -26.582 7.383 -25.562 8.062 C-24.899 7.571 -24.235 7.08 -23.551 6.574 C-19.869 4.711 -16.768 4.725 -12.75 4.875 C-11.711 4.895 -11.711 4.895 -10.65 4.916 C-8.954 4.951 -7.258 5.005 -5.562 5.062 C-5.418 4.423 -5.274 3.784 -5.125 3.125 C-4.17 -0.377 -3.914 0.069 0 0 Z M-28.562 8.062 C-28.232 9.053 -27.903 10.043 -27.562 11.062 C-27.562 10.072 -27.562 9.082 -27.562 8.062 C-27.893 8.062 -28.222 8.062 -28.562 8.062 Z M-17.562 15.062 C-18.222 15.722 -18.883 16.383 -19.562 17.062 C-19.244 19.171 -19.244 19.171 -18.562 21.062 C-18.562 20.072 -18.562 19.082 -18.562 18.062 C-18.232 18.722 -17.903 19.383 -17.562 20.062 C-16.572 19.732 -15.582 19.403 -14.562 19.062 C-14.893 17.742 -15.222 16.423 -15.562 15.062 C-16.222 15.062 -16.883 15.062 -17.562 15.062 Z " fill="#EBEEEC" transform="translate(571.5625,674.9375)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C-5.337 5.348 -12.344 5.194 -20 5 C-20 5.33 -20 5.66 -20 6 C-19.043 5.983 -19.043 5.983 -18.066 5.965 C-17.24 5.956 -16.414 5.947 -15.562 5.938 C-14.739 5.926 -13.915 5.914 -13.066 5.902 C-11 6 -11 6 -10 7 C-9.927 9.353 -9.916 11.708 -9.938 14.062 C-9.947 15.353 -9.956 16.643 -9.965 17.973 C-9.976 18.972 -9.988 19.971 -10 21 C-10.33 21 -10.66 21 -11 21 C-11 19.35 -11 17.7 -11 16 C-13.97 16.99 -16.94 17.98 -20 19 C-20 20.32 -20 21.64 -20 23 C-17.36 23 -14.72 23 -12 23 C-13.062 25 -13.062 25 -15 27 C-18.474 27.434 -20.867 27.602 -24 25.938 C-25.694 22.655 -25.045 20.444 -24 17 C-19.762 13.872 -16.161 13.69 -11 14 C-11.99 12.02 -11.99 12.02 -13 10 C-17.571 10.549 -21.674 11.417 -26 13 C-26 14.32 -26 15.64 -26 17 C-31.28 17 -36.56 17 -42 17 C-41.34 18.98 -40.68 20.96 -40 23 C-33.916 23.198 -33.916 23.198 -28 22 C-28.375 23.938 -28.375 23.938 -29 26 C-31.326 27.163 -32.787 27.166 -35.375 27.188 C-36.531 27.209 -36.531 27.209 -37.711 27.23 C-40.218 26.978 -41.833 26.252 -44 25 C-45.32 26.32 -46.64 27.64 -48 29 C-48.144 27.907 -48.289 26.814 -48.438 25.688 C-48.883 22.069 -48.883 22.069 -50 19 C-50.66 19 -51.32 19 -52 19 C-52.33 19.66 -52.66 20.32 -53 21 C-53.812 18.312 -53.812 18.312 -54 15 C-51.562 12.125 -51.562 12.125 -49 10 C-51.915 11.074 -53.778 11.778 -56 14 C-56.465 17.122 -56.465 17.122 -56.625 20.625 C-56.7 21.814 -56.775 23.002 -56.852 24.227 C-56.901 25.142 -56.95 26.057 -57 27 C-58.32 27 -59.64 27 -61 27 C-61 20.07 -61 13.14 -61 6 C-59.68 6 -58.36 6 -57 6 C-56.505 7.485 -56.505 7.485 -56 9 C-54.02 7.68 -52.04 6.36 -50 5 C-55.28 5 -60.56 5 -66 5 C-65.34 6.98 -64.68 8.96 -64 11 C-64.687 10.893 -65.374 10.786 -66.082 10.676 C-66.983 10.556 -67.884 10.436 -68.812 10.312 C-69.706 10.185 -70.599 10.057 -71.52 9.926 C-74.028 9.774 -74.028 9.774 -75.805 11.262 C-77.758 14.103 -77.298 16.665 -77 20 C-76.34 20.99 -75.68 21.98 -75 23 C-72 23.417 -72 23.417 -69 23 C-68.34 22.34 -67.68 21.68 -67 21 C-66.01 21 -65.02 21 -64 21 C-65.75 25.875 -65.75 25.875 -68 27 C-75.378 27.476 -75.378 27.476 -78.125 25.375 C-78.744 24.921 -79.362 24.467 -80 24 C-83.836 24.824 -83.836 24.824 -87 27 C-91.892 27.498 -94.116 27.474 -98.5 25.125 C-101.101 22.638 -101.466 20.547 -101.625 17 C-101.36 12.551 -100.373 10.076 -97 7 C-93.936 5.468 -91.383 5.771 -88 6 C-83.4 8.2 -83.4 8.2 -82 11 C-81.092 10.196 -80.185 9.391 -79.25 8.562 C-76.897 6.589 -75.131 5.361 -72 5 C-74.97 4.505 -74.97 4.505 -78 4 C-74.898 2.155 -73.162 1.852 -69.625 2.438 C-64.05 3.158 -58.777 2.706 -53.199 2.156 C-49.401 1.971 -46.408 2.296 -43 4 C-42.34 3.67 -41.68 3.34 -41 3 C-36.253 2.736 -31.543 2.691 -26.875 3.625 C-23.324 4.088 -20.675 3.282 -17.238 2.41 C-14.034 1.823 -11.184 2.449 -8 3 C-4.444 2.596 -3.041 2.027 0 0 Z M-93 15 C-93.33 15.66 -93.66 16.32 -94 17 C-93.34 17 -92.68 17 -92 17 C-92.33 16.34 -92.66 15.68 -93 15 Z M-91 15 C-90.67 15.66 -90.34 16.32 -90 17 C-89.67 16.34 -89.34 15.68 -89 15 C-89.66 15 -90.32 15 -91 15 Z " fill="#E9ECEA" transform="translate(760,627)"/>
<path d="M0 0 C3.37 0.568 5.69 1.435 8.34 3.605 C10.883 7.437 10.104 12.054 9.551 16.43 C9.355 17.309 9.159 18.188 8.957 19.094 C8.334 21.556 8.334 21.556 9.551 24.305 C10.751 28.054 10.314 30.612 9.551 34.43 C9.221 33.77 8.891 33.11 8.551 32.43 C7.572 32.447 7.572 32.447 6.574 32.465 C-0.899 32.542 -0.899 32.542 -4.512 31.805 C-8.085 31.349 -10.758 32.09 -14.223 32.941 C-18.932 33.974 -23.005 33.62 -27.574 32.305 C-31.265 31.082 -31.265 31.082 -33.887 32.43 C-37.436 33.815 -39.858 32.894 -43.461 31.938 C-45.43 31.347 -45.43 31.347 -47.449 31.43 C-49.449 31.43 -51.449 31.43 -53.449 31.43 C-52.624 30.852 -51.799 30.275 -50.949 29.68 C-50.124 28.937 -49.299 28.195 -48.449 27.43 C-48.449 26.11 -48.449 24.79 -48.449 23.43 C-49.109 23.43 -49.769 23.43 -50.449 23.43 C-50.779 24.09 -51.109 24.75 -51.449 25.43 C-52.769 25.43 -54.089 25.43 -55.449 25.43 C-55.779 25.76 -56.109 26.09 -56.449 26.43 C-59.324 26.742 -59.324 26.742 -62.449 26.43 C-65.244 24.101 -65.434 22.55 -65.887 18.93 C-65.449 15.43 -65.449 15.43 -63.762 12.992 C-61.049 11.159 -59.659 10.996 -56.449 11.43 C-56.119 11.76 -55.789 12.09 -55.449 12.43 C-53.763 12.502 -52.074 12.514 -50.387 12.492 C-49.468 12.483 -48.549 12.474 -47.602 12.465 C-46.891 12.453 -46.181 12.442 -45.449 12.43 C-45.389 13.166 -45.328 13.902 -45.266 14.66 C-45.14 16.124 -45.14 16.124 -45.012 17.617 C-44.931 18.58 -44.849 19.543 -44.766 20.535 C-44.473 23.216 -44.022 25.796 -43.449 28.43 C-40.809 28.76 -38.169 29.09 -35.449 29.43 C-35.449 28.44 -35.449 27.45 -35.449 26.43 C-37.099 26.1 -38.749 25.77 -40.449 25.43 C-40.449 20.81 -40.449 16.19 -40.449 11.43 C-38.469 11.43 -36.489 11.43 -34.449 11.43 C-34.449 10.77 -34.449 10.11 -34.449 9.43 C-33.789 9.43 -33.129 9.43 -32.449 9.43 C-33.109 10.75 -33.769 12.07 -34.449 13.43 C-35.769 13.43 -37.089 13.43 -38.449 13.43 C-38.449 17.06 -38.449 20.69 -38.449 24.43 C-37.459 24.1 -36.469 23.77 -35.449 23.43 C-35.325 22.13 -35.202 20.831 -35.074 19.492 C-34.863 17.277 -34.863 17.277 -34.449 15.43 C-33.789 15.1 -33.129 14.77 -32.449 14.43 C-32.435 15.021 -32.421 15.613 -32.406 16.223 C-32.149 20.487 -31.809 22.911 -29.324 26.492 C-25.708 28.929 -23.734 28.975 -19.449 28.43 C-17.096 27.549 -17.096 27.549 -15.449 26.43 C-15.449 27.42 -15.449 28.41 -15.449 29.43 C-14.129 29.43 -12.809 29.43 -11.449 29.43 C-11.47 28.693 -11.491 27.957 -11.513 27.198 C-11.596 23.859 -11.648 20.52 -11.699 17.18 C-11.749 15.441 -11.749 15.441 -11.801 13.668 C-11.814 12.553 -11.827 11.438 -11.84 10.289 C-11.861 9.263 -11.882 8.236 -11.903 7.179 C-10.783 0.397 -5.917 -0.298 0 0 Z M3.551 18.43 C3.551 19.75 3.551 21.07 3.551 22.43 C4.541 22.1 5.531 21.77 6.551 21.43 C5.891 21.43 5.231 21.43 4.551 21.43 C4.221 20.44 3.891 19.45 3.551 18.43 Z " fill="#F4F6F5" transform="translate(422.44921875,624.5703125)"/>
<path d="M0 0 C1.052 0.124 2.104 0.247 3.188 0.375 C1.553 2.083 0.318 3.31 -1.812 4.375 C-2.145 7.378 -2.145 7.378 -0.312 9.062 C0.43 9.712 0.43 9.712 1.188 10.375 C-0.132 10.375 -1.452 10.375 -2.812 10.375 C-2.812 17.305 -2.812 24.235 -2.812 31.375 C-1.163 31.375 0.488 31.375 2.188 31.375 C2.517 30.715 2.847 30.055 3.188 29.375 C3.188 30.695 3.188 32.015 3.188 33.375 C-0.772 33.375 -4.733 33.375 -8.812 33.375 C-8.812 32.715 -8.812 32.055 -8.812 31.375 C-8.153 31.375 -7.493 31.375 -6.812 31.375 C-6.899 28.603 -7.006 25.834 -7.125 23.062 C-7.159 21.884 -7.159 21.884 -7.193 20.682 C-7.229 19.924 -7.264 19.167 -7.301 18.387 C-7.327 17.69 -7.353 16.994 -7.38 16.276 C-7.945 13.792 -8.774 12.88 -10.812 11.375 C-15.014 11.108 -18.782 11.032 -22.812 12.375 C-22.812 13.365 -22.812 14.355 -22.812 15.375 C-22.276 15.045 -21.74 14.715 -21.188 14.375 C-18.22 13.125 -16.005 13.097 -12.812 13.375 C-10.812 15.375 -10.812 15.375 -10.812 18.375 C-11.369 18.473 -11.926 18.571 -12.5 18.672 C-18.194 19.788 -22.72 21.597 -27.812 24.375 C-27.29 21.612 -26.704 19.049 -25.812 16.375 C-28.783 16.87 -28.783 16.87 -31.812 17.375 C-31.812 18.695 -31.812 20.015 -31.812 21.375 C-33.132 20.715 -34.452 20.055 -35.812 19.375 C-35.152 19.375 -34.493 19.375 -33.812 19.375 C-33.72 18.447 -33.72 18.447 -33.625 17.5 C-33.357 16.799 -33.089 16.097 -32.812 15.375 C-31.16 14.673 -29.493 14.005 -27.812 13.375 C-27.483 12.385 -27.153 11.395 -26.812 10.375 C-31.253 11.229 -33.334 11.813 -36.312 15.312 C-38.224 18.309 -38.224 18.309 -36.812 21.375 C-36.899 23.607 -37.052 25.837 -37.25 28.062 C-37.352 29.242 -37.454 30.421 -37.559 31.637 C-37.642 32.54 -37.726 33.444 -37.812 34.375 C-44.438 35.5 -44.438 35.5 -47.812 34.375 C-48.706 28.439 -48.783 23.305 -47.812 17.375 C-48.803 17.375 -49.793 17.375 -50.812 17.375 C-50.812 18.035 -50.812 18.695 -50.812 19.375 C-51.473 19.375 -52.132 19.375 -52.812 19.375 C-52.483 22.345 -52.152 25.315 -51.812 28.375 C-52.473 28.375 -53.132 28.375 -53.812 28.375 C-53.812 29.365 -53.812 30.355 -53.812 31.375 C-53.152 31.705 -52.493 32.035 -51.812 32.375 C-52.473 32.375 -53.132 32.375 -53.812 32.375 C-53.812 33.035 -53.812 33.695 -53.812 34.375 C-55.793 34.375 -57.772 34.375 -59.812 34.375 C-60.142 35.035 -60.473 35.695 -60.812 36.375 C-60.812 35.715 -60.812 35.055 -60.812 34.375 C-61.473 34.375 -62.132 34.375 -62.812 34.375 C-63.825 31.337 -63.942 29.263 -63.945 26.086 C-63.947 25.062 -63.948 24.039 -63.949 22.984 C-63.945 21.917 -63.941 20.85 -63.938 19.75 C-63.941 18.675 -63.945 17.6 -63.949 16.492 C-63.947 14.961 -63.947 14.961 -63.945 13.398 C-63.944 12.459 -63.943 11.52 -63.942 10.552 C-63.812 8.375 -63.812 8.375 -62.812 7.375 C-59.361 7.124 -55.897 7.19 -52.438 7.188 C-51.466 7.175 -50.494 7.163 -49.492 7.15 C-48.092 7.147 -48.092 7.147 -46.664 7.145 C-45.806 7.14 -44.949 7.136 -44.065 7.132 C-41.812 7.375 -41.812 7.375 -38.812 9.375 C-37.124 8.765 -35.46 8.087 -33.812 7.375 C-27.257 6.153 -27.257 6.153 -24.812 7.375 C-24.812 8.035 -24.812 8.695 -24.812 9.375 C-24.16 9.158 -23.508 8.942 -22.836 8.719 C-15.379 6.343 -15.379 6.343 -11.5 6.562 C-10.613 6.501 -9.726 6.439 -8.812 6.375 C-8.008 5.385 -7.204 4.395 -6.375 3.375 C-3.812 0.375 -3.812 0.375 0 0 Z " fill="#F6F8F7" transform="translate(347.8125,622.625)"/>
<path d="M0 0 C0.66 -0.99 1.32 -1.98 2 -3 C5.364 -2.439 8.029 -1.671 11 0 C12.268 0.681 12.268 0.681 13.562 1.375 C16.237 3.158 17.56 4.12 19 7 C19.531 10.108 19.608 12.9 19 16 C16.818 18.756 15.128 20.436 12 22 C4.533 22.356 4.533 22.356 1 20 C1 20.99 1 21.98 1 23 C1.66 23.33 2.32 23.66 3 24 C2.01 24 1.02 24 0 24 C0 23.34 0 22.68 0 22 C-4.332 22.86 -4.332 22.86 -8 25 C-13.44 25.584 -17.56 25.453 -22 22 C-22.949 21.526 -23.898 21.051 -24.875 20.562 C-27.5 17.398 -27.306 16.218 -27.125 12.188 C-26.375 6.394 -25.03 3.214 -21 -1 C-14.137 -5.154 -6.931 -2.64 0 0 Z M-3 1 C-2 3 -2 3 -2 3 Z M6 8 C6 9.98 6 11.96 6 14 C6.99 14 7.98 14 9 14 C9 12.35 9 10.7 9 9 C8.01 8.67 7.02 8.34 6 8 Z M11 10 C10.67 10.99 10.34 11.98 10 13 C10.99 13.495 10.99 13.495 12 14 C11.67 12.68 11.34 11.36 11 10 Z " fill="#EBEEED" transform="translate(615,683)"/>
<path d="M0 0 C5.381 4.912 9.542 10.962 13.75 16.871 C16.788 21.095 19.93 25.238 23.085 29.375 C28.926 37.102 34.435 45.074 40 53 C40.006 52.45 40.012 51.899 40.018 51.332 C40.082 45.589 40.166 39.846 40.262 34.104 C40.296 31.962 40.324 29.82 40.346 27.679 C40.38 24.595 40.432 21.513 40.488 18.43 C40.495 17.476 40.501 16.523 40.508 15.541 C40.642 9.44 41.325 4.771 45.75 0.188 C49.548 -1.817 52.891 -2.516 57.125 -1.48 C60.199 -0.243 62.434 0.921 64.009 3.921 C65.712 9.272 66.434 14.034 66.388 19.637 C66.393 20.448 66.399 21.259 66.404 22.095 C66.417 24.744 66.409 27.393 66.398 30.043 C66.4 31.899 66.403 33.755 66.407 35.611 C66.412 39.487 66.405 43.362 66.391 47.238 C66.374 52.198 66.384 57.157 66.402 62.117 C66.413 65.944 66.409 69.771 66.401 73.598 C66.399 75.427 66.402 77.255 66.409 79.084 C66.417 81.64 66.405 84.195 66.388 86.751 C66.394 87.5 66.4 88.25 66.407 89.023 C66.339 94.631 65.167 98.86 61.121 102.855 C56.554 105.32 50.983 104.422 46.062 103.375 C43.201 101.468 42.526 99.063 41 96 C39.731 94.198 38.415 92.428 37.062 90.688 C33.318 85.785 29.721 80.806 26.188 75.75 C21.075 68.448 15.78 61.3 10.398 54.195 C8.878 52.17 7.405 50.107 6 48 C5.992 48.563 5.984 49.126 5.975 49.706 C5.889 55.579 5.79 61.451 5.683 67.324 C5.644 69.514 5.61 71.704 5.578 73.894 C5.532 77.047 5.474 80.199 5.414 83.352 C5.403 84.327 5.391 85.302 5.379 86.307 C5.25 92.268 4.942 97.338 1.312 102.312 C-1.85 104.62 -4.103 104.868 -8 105 C-12.293 104.272 -14.358 103.675 -17.375 100.5 C-20.207 96.143 -20.16 92.785 -20.177 87.698 C-20.184 86.84 -20.19 85.981 -20.197 85.096 C-20.217 82.246 -20.228 79.397 -20.238 76.547 C-20.242 75.574 -20.246 74.602 -20.251 73.6 C-20.272 68.451 -20.286 63.302 -20.295 58.154 C-20.306 52.836 -20.341 47.519 -20.38 42.202 C-20.406 38.113 -20.415 34.024 -20.418 29.936 C-20.423 27.976 -20.435 26.016 -20.453 24.056 C-20.478 21.31 -20.477 18.565 -20.47 15.819 C-20.483 15.012 -20.496 14.204 -20.509 13.372 C-20.461 8.132 -19.46 5.165 -16 1 C-10.779 -2.243 -5.505 -2.802 0 0 Z M-16.62 4.581 C-18.628 8.1 -18.418 11.51 -18.388 15.453 C-18.393 16.304 -18.399 17.155 -18.404 18.031 C-18.417 20.843 -18.409 23.653 -18.398 26.465 C-18.4 28.42 -18.403 30.375 -18.407 32.331 C-18.412 36.429 -18.405 40.527 -18.391 44.625 C-18.374 49.875 -18.384 55.125 -18.402 60.375 C-18.413 64.413 -18.409 68.451 -18.401 72.489 C-18.399 74.425 -18.402 76.361 -18.409 78.296 C-18.417 81.004 -18.405 83.71 -18.388 86.418 C-18.394 87.216 -18.4 88.015 -18.407 88.838 C-18.342 94.5 -17.131 97.712 -13.312 101.812 C-9.914 103.558 -6.692 103.674 -3 103 C0.443 100.876 1.716 99.851 3 96 C3.116 94.237 3.176 92.469 3.205 90.702 C3.225 89.621 3.245 88.541 3.266 87.427 C3.282 86.261 3.299 85.096 3.316 83.895 C3.337 82.7 3.358 81.506 3.379 80.275 C3.445 76.454 3.504 72.633 3.562 68.812 C3.606 66.225 3.649 63.637 3.693 61.049 C3.801 54.699 3.902 48.35 4 42 C4.327 42.466 4.654 42.931 4.991 43.411 C10.898 51.81 16.803 60.203 22.938 68.438 C26.509 73.242 30.057 78.056 33.496 82.957 C34.064 83.761 34.632 84.566 35.217 85.395 C36.282 86.908 37.339 88.426 38.386 89.951 C41.897 94.921 45.809 99.713 51 103 C54.321 103.443 56.932 103.407 60 102 C63.099 99.02 63.953 97.37 64.501 93.054 C64.522 91.483 64.521 89.912 64.502 88.342 C64.507 87.481 64.513 86.62 64.518 85.733 C64.531 82.895 64.514 80.057 64.496 77.219 C64.496 75.242 64.498 73.266 64.501 71.29 C64.502 67.151 64.489 63.012 64.467 58.873 C64.438 53.57 64.441 48.267 64.454 42.964 C64.461 38.884 64.453 34.804 64.44 30.724 C64.436 28.769 64.436 26.813 64.439 24.858 C64.442 22.125 64.426 19.392 64.404 16.658 C64.409 15.852 64.413 15.045 64.418 14.215 C64.342 8.523 63.174 5.252 59.25 1.188 C55.998 -0.529 53.625 -0.382 50 0 C46.173 1.701 44.538 3.149 43 7 C42.676 9.462 42.676 9.462 42.681 12.136 C42.661 13.156 42.641 14.177 42.621 15.229 C42.609 16.324 42.598 17.419 42.586 18.547 C42.567 19.673 42.547 20.8 42.527 21.96 C42.467 25.557 42.421 29.153 42.375 32.75 C42.337 35.189 42.298 37.628 42.258 40.066 C42.162 46.044 42.077 52.022 42 58 C40.099 55.475 38.2 52.949 36.301 50.422 C35.503 49.362 35.503 49.362 34.689 48.281 C31.458 43.98 28.292 39.643 25.188 35.25 C20.944 29.265 16.585 23.372 12.188 17.5 C11.553 16.651 10.918 15.802 10.264 14.927 C4.38 5.908 4.38 5.908 -4 0 C-9.51 -0.586 -12.685 0.741 -16.62 4.581 Z M4 45 C5 47 5 47 5 47 Z " fill="#CFD8DE" transform="translate(663,378)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.258 2.083 3.516 2.165 4.812 2.25 C13.404 3.005 23.436 8.375 29.465 14.484 C32.672 18.358 33.858 21.739 33.547 26.781 C32.543 30.856 30.33 33.519 27 36 C23.5 37.188 23.5 37.188 20 37 C17 35.375 17 35.375 14 33 C12.819 32.116 11.631 31.241 10.438 30.375 C9.9 29.97 9.362 29.565 8.809 29.148 C1.752 24.668 -7.65 24.151 -15.75 25.562 C-23.854 27.823 -29.56 32.647 -33.848 39.785 C-37.874 47.524 -38.344 56.444 -35.75 64.75 C-32.211 73.941 -27.547 80.112 -18.438 84.375 C-10.347 87.306 -1.489 87.274 6.562 84.25 C10.127 82.573 13.317 80.942 16 78 C16.33 76.02 16.66 74.04 17 72 C10.73 72 4.46 72 -2 72 C-2.222 68.75 -2.428 65.501 -2.625 62.25 C-2.689 61.33 -2.754 60.409 -2.82 59.461 C-2.872 58.571 -2.923 57.682 -2.977 56.766 C-3.029 55.949 -3.081 55.132 -3.135 54.29 C-2.984 51.732 -2.289 50.191 -1 48 C0.65 48 2.3 48 4 48 C4 47.34 4 46.68 4 46 C4.66 46.33 5.32 46.66 6 47 C7.939 47.073 9.88 47.084 11.82 47.062 C13.584 47.051 13.584 47.051 15.383 47.039 C17.854 47.013 20.326 46.987 22.797 46.961 C28.96 46.921 34.92 46.93 41 48 C43.653 50.653 42.322 59.381 42.35 63.107 C42.367 64.688 42.394 66.269 42.432 67.85 C42.691 78.553 42.134 86.117 34.859 94.453 C20.842 106.394 5.41 111.312 -13 110 C-29.812 107.507 -42.646 100.145 -52.883 86.488 C-61.185 73.535 -64.087 58.088 -60.924 43.055 C-57.647 29.221 -50.415 16.868 -38.297 8.877 C-25.984 1.563 -13.923 0.656 0 0 Z M-47.047 18.523 C-57.65 31.968 -60.932 45.991 -60 63 C-58.017 76.999 -50.742 88.98 -39.738 97.816 C-25.694 107.596 -9.535 110.155 7.25 107.312 C20.599 104.065 31.57 97.779 38.941 85.98 C41.135 81.876 41.116 78.514 41.098 73.926 C41.095 72.675 41.093 71.423 41.09 70.134 C41.081 68.543 41.072 66.952 41.062 65.312 C41.042 60.259 41.021 55.206 41 50 C27.14 50 13.28 50 -1 50 C-1 56.6 -1 63.2 -1 70 C5.6 70 12.2 70 19 70 C18 78 18 78 16.617 79.988 C7.501 86.917 -1.581 88.998 -13 88 C-21.994 85.858 -28.882 80.591 -33.875 72.875 C-38.903 64.402 -39.22 55.646 -38 46 C-35.335 36.966 -30.138 30.861 -22.273 25.914 C-15.012 22.427 -4.754 22.939 2.941 24.656 C8.451 26.657 12.472 29.885 16.969 33.566 C19.047 35.233 19.047 35.233 22.25 35.688 C26.42 34.645 28.806 32.575 31.375 29.188 C32.382 25.664 32.003 23.54 31 20 C24.96 10.817 15.708 5.604 5.084 3.33 C-14.03 0.307 -33.371 4.125 -47.047 18.523 Z " fill="#CFD8DD" transform="translate(786,219)"/>
<path d="M0 0 C2.875 0.875 2.875 0.875 4.875 2.875 C5 6 5 6 4.875 8.875 C6.161 8.715 6.161 8.715 7.473 8.551 C19.959 7.242 19.959 7.242 24.875 10.875 C29.737 18.031 27.7 27.776 26.875 35.875 C19.125 37 19.125 37 16.875 35.875 C16.461 33.809 16.461 33.809 16.25 31.312 C16.175 30.486 16.1 29.66 16.023 28.809 C15.974 28.171 15.925 27.532 15.875 26.875 C14.885 26.875 13.895 26.875 12.875 26.875 C12.916 27.803 12.957 28.731 13 29.688 C12.875 32.875 12.875 32.875 10.875 35.875 C10.215 35.875 9.555 35.875 8.875 35.875 C8.545 36.535 8.215 37.195 7.875 37.875 C7.875 37.215 7.875 36.555 7.875 35.875 C6.699 36.061 5.524 36.246 4.312 36.438 C0.347 37.064 -3.174 36.474 -7.125 35.875 C-7.224 31.391 -7.297 26.908 -7.345 22.423 C-7.365 20.898 -7.392 19.373 -7.427 17.849 C-7.475 15.654 -7.498 13.461 -7.516 11.266 C-7.537 9.946 -7.558 8.626 -7.579 7.267 C-7.125 3.875 -7.125 3.875 -5.296 1.405 C-3.125 -0.125 -3.125 -0.125 0 0 Z " fill="#EFF1F1" transform="translate(447.125,621.125)"/>
<path d="M0 0 C8.236 4.181 11.716 13.832 15.625 21.625 C19.717 29.721 23.88 37.677 28.688 45.375 C33.215 36.907 37.693 28.427 41.938 19.812 C50.626 2.313 50.626 2.313 59.688 -1.625 C64.319 -2.349 67.805 -2.075 72 0.062 C76.138 3.623 78.475 5.725 79.195 11.315 C79.219 12.882 79.221 14.45 79.205 16.017 C79.212 16.876 79.219 17.736 79.226 18.621 C79.244 21.455 79.232 24.287 79.219 27.121 C79.221 29.095 79.225 31.069 79.23 33.043 C79.236 37.178 79.228 41.312 79.209 45.448 C79.186 50.742 79.199 56.035 79.223 61.329 C79.238 65.405 79.233 69.48 79.223 73.556 C79.22 75.508 79.223 77.459 79.233 79.411 C79.243 82.142 79.227 84.872 79.205 87.603 C79.213 88.406 79.221 89.209 79.23 90.036 C79.151 95.262 78.103 98.202 74.688 102.375 C71.659 104.755 68.44 104.735 64.648 104.629 C60.749 104.124 58.469 102.183 55.812 99.438 C54.688 97.375 54.688 97.375 55 95.125 C55.227 94.548 55.454 93.97 55.688 93.375 C56.121 94.324 56.554 95.272 57 96.25 C58.481 99.575 58.481 99.575 61.688 101.375 C64.1 101.654 64.1 101.654 66.688 101.562 C67.554 101.556 68.42 101.55 69.312 101.543 C71.837 101.463 71.837 101.463 74.688 100.375 C75.972 96.522 75.86 92.869 75.88 88.852 C75.889 87.982 75.897 87.112 75.905 86.215 C75.93 83.333 75.947 80.452 75.961 77.57 C75.969 76.094 75.969 76.094 75.977 74.589 C76.004 69.383 76.023 64.177 76.038 58.971 C76.054 53.592 76.099 48.213 76.15 42.834 C76.184 38.699 76.195 34.564 76.201 30.429 C76.208 28.446 76.223 26.463 76.247 24.48 C76.278 21.705 76.278 18.932 76.271 16.157 C76.287 15.338 76.304 14.519 76.321 13.675 C76.274 9.387 76.071 7.85 73.276 4.389 C68.761 0.876 65.271 0.885 59.688 1.375 C52.965 3.443 50.763 8.599 47.559 14.427 C46.453 16.523 45.37 18.629 44.297 20.742 C43.907 21.503 43.517 22.264 43.115 23.048 C41.886 25.447 40.662 27.848 39.438 30.25 C38.602 31.883 37.766 33.516 36.93 35.148 C33.286 42.25 33.286 42.25 29.688 49.375 C26.045 48.161 25.594 46.787 23.91 43.487 C23.572 42.802 23.234 42.116 22.887 41.41 C22.528 40.703 22.169 39.995 21.799 39.267 C20.648 36.995 19.511 34.717 18.375 32.438 C11.077 14.469 11.077 14.469 -2.035 1.934 C-7.085 0.695 -12.544 -0.042 -17.312 2.398 C-19.962 4.5 -21.241 6.16 -22.312 9.375 C-22.414 12.041 -22.459 14.681 -22.458 17.347 C-22.461 18.159 -22.464 18.971 -22.468 19.807 C-22.477 22.494 -22.479 25.18 -22.48 27.867 C-22.484 29.734 -22.487 31.601 -22.491 33.468 C-22.497 37.384 -22.498 41.3 -22.498 45.215 C-22.498 50.231 -22.511 55.247 -22.529 60.263 C-22.54 64.12 -22.542 67.977 -22.541 71.834 C-22.542 73.683 -22.547 75.532 -22.555 77.382 C-22.564 79.969 -22.561 82.556 -22.555 85.144 C-22.561 85.907 -22.567 86.67 -22.572 87.455 C-22.543 92.465 -22.078 96.327 -18.75 100.188 C-15 102.015 -11.401 102.074 -7.312 101.375 C-4.811 99.499 -3.693 98.136 -2.312 95.375 C-2.193 93.65 -2.135 91.92 -2.107 90.191 C-2.087 89.11 -2.067 88.029 -2.047 86.915 C-2.022 85.159 -2.022 85.159 -1.996 83.367 C-1.975 82.171 -1.955 80.976 -1.933 79.744 C-1.868 75.913 -1.809 72.081 -1.75 68.25 C-1.707 65.658 -1.663 63.065 -1.619 60.473 C-1.512 54.107 -1.41 47.741 -1.312 41.375 C1.915 45.13 4.226 49.051 6.402 53.469 C7.095 54.843 7.789 56.216 8.482 57.59 C9.557 59.734 10.627 61.88 11.694 64.028 C12.734 66.117 13.786 68.199 14.84 70.281 C15.151 70.918 15.463 71.554 15.784 72.21 C17.871 76.316 20.192 79.372 23.688 82.375 C26.138 82.767 26.138 82.767 28.5 82.188 C29.29 82.05 30.08 81.912 30.895 81.77 C31.486 81.639 32.078 81.509 32.688 81.375 C32.901 80.612 33.115 79.849 33.336 79.062 C35.219 73.926 37.759 69.207 40.312 64.375 C40.846 63.358 41.38 62.341 41.931 61.293 C46.015 53.553 50.245 45.915 54.688 38.375 C55.018 38.375 55.347 38.375 55.688 38.375 C55.688 56.195 55.688 74.015 55.688 92.375 C55.357 92.375 55.028 92.375 54.688 92.375 C53.75 86.851 53.555 81.538 53.59 75.941 C53.591 75.063 53.593 74.186 53.594 73.281 C53.6 70.5 53.612 67.719 53.625 64.938 C53.63 63.042 53.635 61.147 53.639 59.252 C53.65 54.626 53.667 50.001 53.688 45.375 C45.69 59.565 45.69 59.565 38.465 74.158 C36.128 79.042 33.417 83.352 28.188 85.312 C25.073 85.39 23.406 84.909 20.688 83.375 C17.599 79.882 15.425 76.262 13.305 72.121 C13.004 71.538 12.704 70.956 12.395 70.355 C11.445 68.51 10.504 66.662 9.562 64.812 C8.924 63.568 8.284 62.324 7.645 61.08 C5.303 56.523 2.976 51.959 0.688 47.375 C0.686 47.988 0.685 48.601 0.683 49.233 C0.663 55.625 0.609 62.016 0.535 68.408 C0.512 70.792 0.497 73.176 0.492 75.56 C0.483 78.991 0.442 82.421 0.395 85.852 C0.398 86.914 0.402 87.977 0.406 89.072 C0.244 97.35 0.244 97.35 -2.732 101.033 C-6.577 104.522 -8.949 104.717 -13.988 104.66 C-17.563 104.222 -19.646 102.717 -22.312 100.375 C-25.008 96.581 -24.608 92.311 -24.603 87.846 C-24.61 86.987 -24.616 86.128 -24.623 85.243 C-24.642 82.404 -24.646 79.566 -24.648 76.727 C-24.655 74.751 -24.662 72.776 -24.669 70.801 C-24.681 66.66 -24.684 62.519 -24.683 58.378 C-24.683 53.076 -24.71 47.775 -24.745 42.473 C-24.767 38.393 -24.771 34.313 -24.77 30.233 C-24.772 28.278 -24.781 26.324 -24.797 24.37 C-24.816 21.634 -24.81 18.899 -24.798 16.163 C-24.81 15.358 -24.821 14.554 -24.832 13.725 C-24.774 8.493 -23.765 5.531 -20.312 1.375 C-14.258 -2.95 -6.685 -3.02 0 0 Z " fill="#D3DCE1" transform="translate(476.3125,222.625)"/>
<path d="M0 0 C3.084 1.199 6.061 2.48 9 4 C9.66 4.33 10.32 4.66 11 5 C11.495 6.98 11.495 6.98 12 9 C15.63 9.66 19.26 10.32 23 11 C22.01 12.32 21.02 13.64 20 15 C19.34 14.67 18.68 14.34 18 14 C12.146 13.615 12.146 13.615 7 16 C4.698 18.993 4.511 22.307 4.629 26.027 C4.926 28.312 4.926 28.312 7 31 C10.337 33.225 12.142 33.73 16.07 33.508 C19.266 32.667 20.92 30.447 23 28 C23.66 28 24.32 28 25 28 C24.75 30.375 24.75 30.375 24 33 C21.938 34.312 21.938 34.312 20 35 C20 35.66 20 36.32 20 37 C18.669 37.2 17.335 37.384 16 37.562 C14.886 37.719 14.886 37.719 13.75 37.879 C11 38 11 38 7.367 37.492 C1.343 36.654 -4.652 37.323 -10.691 37.771 C-17.117 38.161 -22.758 37.574 -29 36 C-29 33.36 -29 30.72 -29 28 C-29.99 28 -30.98 28 -32 28 C-32 27.34 -32 26.68 -32 26 C-33.65 26.66 -35.3 27.32 -37 28 C-37.33 30.64 -37.66 33.28 -38 36 C-38.33 36 -38.66 36 -39 36 C-39.027 34.042 -39.046 32.083 -39.062 30.125 C-39.074 29.034 -39.086 27.944 -39.098 26.82 C-39 24 -39 24 -38 22 C-38.66 21.67 -39.32 21.34 -40 21 C-36.375 15.25 -36.375 15.25 -33 13 C-30.312 12.875 -30.312 12.875 -28 13 C-28.188 14.875 -28.188 14.875 -29 17 C-30.667 17.667 -32.333 18.333 -34 19 C-34.887 21.041 -34.887 21.041 -35 23 C-34.34 23.33 -33.68 23.66 -33 24 C-33 22.68 -33 21.36 -33 20 C-31.02 19.67 -29.04 19.34 -27 19 C-27.33 21.31 -27.66 23.62 -28 26 C-26.886 25.361 -25.772 24.721 -24.625 24.062 C-20.068 21.447 -17.388 20.9 -12 21 C-12.66 19.68 -13.32 18.36 -14 17 C-17.522 16.92 -20.62 16.96 -24 18 C-24 17.01 -24 16.02 -24 15 C-20.748 12.832 -17.78 12.701 -14 12.539 C-10.876 13.259 -9.836 14.376 -8 17 C-7.52 19.934 -7.622 22.787 -7.75 25.75 C-7.768 26.543 -7.786 27.336 -7.805 28.152 C-7.852 30.102 -7.923 32.051 -8 34 C-8.66 34 -9.32 34 -10 34 C-10 34.66 -10 35.32 -10 36 C-6.04 36 -2.08 36 2 36 C1.67 35.34 1.34 34.68 1 34 C-0.65 34 -2.3 34 -4 34 C-4 27.07 -4 20.14 -4 13 C-3.01 12.67 -2.02 12.34 -1 12 C-1.99 11.34 -2.98 10.68 -4 10 C-3.75 8.125 -3.75 8.125 -3 6 C-0.938 4.75 -0.938 4.75 1 4 C-0.32 3.34 -1.64 2.68 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#F3F5F5" transform="translate(349,620)"/>
<path d="M0 0 C0.741 0.044 1.482 0.088 2.246 0.133 C7.614 1.853 10.956 7.495 14.25 11.812 C14.679 12.374 15.107 12.935 15.549 13.514 C19.067 18.152 22.447 22.882 25.807 27.636 C29.07 32.235 32.471 36.711 35.931 41.163 C39.928 46.316 43.761 51.576 47.562 56.875 C48.496 54.075 48.676 52.46 48.644 49.575 C48.638 48.71 48.631 47.846 48.624 46.955 C48.613 46.024 48.602 45.092 48.59 44.133 C48.577 42.16 48.565 40.188 48.553 38.215 C48.528 35.105 48.5 31.996 48.467 28.886 C48.436 25.885 48.419 22.884 48.402 19.883 C48.389 18.956 48.375 18.028 48.362 17.073 C48.339 11.3 48.595 7.16 52.5 2.625 C55.878 -0.242 59.146 -0.462 63.496 -0.414 C66.878 0.059 68.989 1.731 71.562 3.875 C75.252 9.41 74.712 16.435 74.708 22.924 C74.711 23.685 74.714 24.445 74.718 25.228 C74.727 27.716 74.729 30.204 74.73 32.691 C74.734 34.434 74.737 36.177 74.741 37.92 C74.747 41.562 74.748 45.204 74.748 48.846 C74.748 53.502 74.761 58.158 74.779 62.814 C74.79 66.409 74.792 70.005 74.791 73.6 C74.792 75.316 74.797 77.033 74.805 78.749 C74.814 81.151 74.811 83.553 74.805 85.955 C74.811 86.656 74.817 87.357 74.822 88.079 C74.787 93.665 74.229 98.832 70.625 103.25 C66.683 106.356 62.39 106.37 57.562 105.875 C48.958 102.517 43.552 92.186 38.438 84.926 C25.954 66.116 25.954 66.116 11.562 48.875 C11.559 49.476 11.555 50.076 11.551 50.695 C11.51 56.957 11.44 63.219 11.355 69.48 C11.327 71.816 11.306 74.152 11.292 76.487 C11.27 79.849 11.223 83.209 11.172 86.57 C11.17 88.132 11.17 88.132 11.168 89.724 C11.047 95.739 10.56 99.283 6.562 103.875 C3.534 106.255 0.315 106.235 -3.477 106.129 C-7.301 105.634 -9.58 103.763 -12.188 101.062 C-14.997 96.147 -14.59 90.298 -14.599 84.778 C-14.604 83.988 -14.609 83.199 -14.614 82.385 C-14.628 79.789 -14.635 77.194 -14.641 74.598 C-14.646 72.785 -14.652 70.971 -14.658 69.158 C-14.668 65.363 -14.674 61.568 -14.678 57.773 C-14.683 52.92 -14.707 48.067 -14.736 43.213 C-14.754 39.47 -14.76 35.727 -14.761 31.984 C-14.764 30.196 -14.772 28.407 -14.785 26.618 C-14.802 24.112 -14.8 21.607 -14.794 19.102 C-14.808 18.004 -14.808 18.004 -14.822 16.883 C-14.776 11.141 -13.525 6.598 -10 2 C-6.929 -0.211 -3.678 -0.293 0 0 Z M-10.438 4.875 C-13.104 8.66 -12.733 12.869 -12.728 17.312 C-12.735 18.161 -12.741 19.011 -12.748 19.886 C-12.767 22.692 -12.771 25.498 -12.773 28.305 C-12.78 30.258 -12.787 32.21 -12.794 34.163 C-12.806 38.257 -12.809 42.351 -12.808 46.445 C-12.808 51.686 -12.835 56.927 -12.87 62.168 C-12.892 66.202 -12.896 70.236 -12.895 74.269 C-12.897 76.202 -12.906 78.134 -12.922 80.066 C-12.941 82.771 -12.935 85.474 -12.923 88.179 C-12.935 88.974 -12.946 89.769 -12.957 90.588 C-12.899 95.785 -11.87 98.747 -8.438 102.875 C-4.98 104.604 -1.218 104.365 2.562 103.875 C5.433 102.081 7.054 100.892 8.562 97.875 C8.682 96.119 8.74 94.359 8.768 92.599 C8.788 91.498 8.808 90.397 8.828 89.263 C8.845 88.07 8.862 86.877 8.879 85.648 C8.9 84.43 8.92 83.213 8.942 81.958 C9.007 78.055 9.066 74.153 9.125 70.25 C9.168 67.609 9.212 64.969 9.256 62.328 C9.363 55.844 9.465 49.359 9.562 42.875 C14.676 49.659 19.746 56.465 24.688 63.375 C29.533 70.116 34.455 76.796 39.438 83.438 C40.044 84.246 40.65 85.054 41.274 85.887 C43.03 88.221 44.795 90.549 46.562 92.875 C47.501 94.122 47.501 94.122 48.458 95.394 C49.073 96.201 49.687 97.008 50.32 97.84 C50.871 98.567 51.421 99.294 51.989 100.043 C54.028 102.417 55.743 103.743 58.867 104.246 C64.662 104.409 64.662 104.409 69.625 101.562 C72.891 97.033 72.716 93 72.724 87.627 C72.729 86.786 72.734 85.946 72.739 85.079 C72.753 82.295 72.76 79.511 72.766 76.727 C72.771 74.792 72.777 72.858 72.783 70.924 C72.793 66.867 72.799 62.81 72.803 58.753 C72.808 53.555 72.832 48.358 72.861 43.16 C72.879 39.164 72.885 35.168 72.886 31.172 C72.889 29.256 72.897 27.34 72.91 25.424 C72.927 22.743 72.925 20.062 72.919 17.381 C72.928 16.591 72.937 15.8 72.947 14.985 C72.912 10.434 72.384 7.494 69.562 3.875 C65.556 1.172 61.232 1.184 56.562 1.875 C53.064 3.989 51.857 4.992 50.562 8.875 C50.475 10.639 50.445 12.406 50.449 14.173 C50.449 15.254 50.449 16.334 50.449 17.448 C50.454 18.614 50.46 19.779 50.465 20.98 C50.467 22.772 50.467 22.772 50.469 24.6 C50.475 28.421 50.487 32.242 50.5 36.062 C50.505 38.65 50.51 41.238 50.514 43.826 C50.525 50.176 50.541 56.525 50.562 62.875 C50.067 60.4 50.067 60.4 49.562 57.875 C48.903 58.535 48.242 59.195 47.562 59.875 C47.217 59.404 46.871 58.933 46.515 58.448 C39.06 48.295 31.564 38.174 23.983 28.114 C21.068 24.242 18.175 20.359 15.348 16.422 C14.606 15.392 14.606 15.392 13.848 14.341 C12.943 13.079 12.043 11.813 11.15 10.542 C8.322 6.606 5.414 2.561 0.5 1.379 C-3.914 1.512 -6.9 1.962 -10.438 4.875 Z " fill="#C9D4D9" transform="translate(635.4375,221.125)"/>
<path d="M0 0 C0.962 -0.005 1.923 -0.01 2.914 -0.016 C5.375 0.25 5.375 0.25 7.375 2.25 C9.252 1.886 9.252 1.886 11.375 1.25 C13.124 1.184 14.875 1.164 16.625 1.188 C17.517 1.197 18.409 1.206 19.328 1.215 C20.004 1.226 20.679 1.238 21.375 1.25 C18.375 3.25 18.375 3.25 14.875 4.688 C11.837 6.044 10.515 6.885 8.375 9.25 C7.715 9.25 7.055 9.25 6.375 9.25 C6.375 8.26 6.375 7.27 6.375 6.25 C2.713 3.906 -0.349 3.725 -4.625 4.25 C-7.769 5.976 -7.769 5.976 -9.625 9.25 C-10.316 12.725 -10.339 15.778 -9.625 19.25 C-7.505 22.032 -6.139 22.991 -2.688 23.582 C1.998 23.822 4.374 22.695 8.375 20.25 C10.562 21 10.562 21 12.375 22.25 C14.9 23.512 16.687 23.349 19.5 23.312 C20.865 23.299 20.865 23.299 22.258 23.285 C22.956 23.274 23.655 23.262 24.375 23.25 C25.035 21.6 25.695 19.95 26.375 18.25 C25.323 18.992 25.323 18.992 24.25 19.75 C20.922 21.486 19.075 21.813 15.375 21.25 C12.58 18.921 12.39 17.37 11.938 13.75 C12.375 10.25 12.375 10.25 14.062 7.812 C16.858 5.924 18.062 5.765 21.375 6.25 C24.188 7.25 24.188 7.25 26.375 8.25 C25.715 7.26 25.055 6.27 24.375 5.25 C24.375 4.26 24.375 3.27 24.375 2.25 C27.994 1.044 31.3 1.142 35.062 1.188 C35.764 1.192 36.466 1.197 37.189 1.201 C38.918 1.213 40.647 1.231 42.375 1.25 C39.473 3.464 36.529 5.41 33.375 7.25 C33.375 5.93 33.375 4.61 33.375 3.25 C32.055 3.25 30.735 3.25 29.375 3.25 C29.375 10.18 29.375 17.11 29.375 24.25 C30.695 24.25 32.015 24.25 33.375 24.25 C33.329 23.358 33.282 22.466 33.234 21.547 C33.198 20.376 33.162 19.206 33.125 18 C33.079 16.84 33.032 15.68 32.984 14.484 C33.375 11.25 33.375 11.25 35.141 8.828 C37.375 7.25 37.375 7.25 41.375 7.25 C40.375 10.25 40.375 10.25 37.375 12.25 C37.309 14.19 37.258 16.134 37.336 18.074 C37.392 21.217 36.891 24.154 36.375 27.25 C35.518 27.238 34.661 27.227 33.777 27.215 C26.673 27.13 26.673 27.13 19.625 27.875 C16.467 28.239 14.822 27.971 11.812 27.188 C7.634 26.113 5.444 26.746 1.375 28.25 C-3.458 27.996 -6.657 27.015 -10.625 24.25 C-11.285 24.91 -11.945 25.57 -12.625 26.25 C-16.557 27.261 -20.606 27.698 -24.625 28.25 C-24.955 29.24 -25.285 30.23 -25.625 31.25 C-26.285 29.93 -26.945 28.61 -27.625 27.25 C-27.604 28.549 -27.584 29.849 -27.562 31.188 C-27.541 32.542 -27.554 33.898 -27.625 35.25 C-28.625 36.25 -28.625 36.25 -30.691 36.348 C-31.515 36.336 -32.339 36.324 -33.188 36.312 C-34.014 36.303 -34.84 36.294 -35.691 36.285 C-36.329 36.274 -36.968 36.262 -37.625 36.25 C-37.625 35.92 -37.625 35.59 -37.625 35.25 C-36.841 35.106 -36.058 34.961 -35.25 34.812 C-32.661 34.372 -32.661 34.372 -30.625 33.25 C-30.625 29.62 -30.625 25.99 -30.625 22.25 C-29.635 22.58 -28.645 22.91 -27.625 23.25 C-21.962 23.878 -21.962 23.878 -17.188 21.375 C-14.371 17.545 -14.302 15.01 -14.625 10.25 C-16.372 6.522 -16.372 6.522 -19.625 4.25 C-21.162 4.025 -22.705 3.842 -24.25 3.688 C-25.067 3.604 -25.885 3.52 -26.727 3.434 C-27.353 3.373 -27.98 3.312 -28.625 3.25 C-28.295 2.59 -27.965 1.93 -27.625 1.25 C-25.855 1.169 -24.084 1.111 -22.312 1.062 C-21.326 1.028 -20.34 0.993 -19.324 0.957 C-16.625 1.25 -16.625 1.25 -14.77 2.762 C-14.392 3.253 -14.014 3.744 -13.625 4.25 C-13.048 3.961 -12.47 3.673 -11.875 3.375 C-10.761 2.818 -10.761 2.818 -9.625 2.25 C-9.035 1.902 -8.444 1.554 -7.836 1.195 C-5.102 0.026 -2.965 -0.016 0 0 Z M17.375 10.25 C18.375 12.25 18.375 12.25 18.375 12.25 Z " fill="#F3F5F4" transform="translate(669.625,629.75)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.99 2.33 1.98 2.66 3 3 C5.006 6.695 5.664 9.849 5 14 C3.368 16.791 1.557 19.013 -1 21 C-4.375 21.438 -4.375 21.438 -7 21 C-6.443 20.773 -5.886 20.546 -5.312 20.312 C-2.216 18.555 -0.231 16.789 2 14 C2.796 9.624 2.415 7.692 0.125 3.875 C-7.983 -4.642 -21.207 -7.091 -32.438 -7.438 C-43.871 -7.157 -54.582 -4.042 -63 4 C-68.49 11.62 -69.815 18.716 -69 28 C-66.962 35.709 -61.09 40.456 -54.551 44.523 C-47.232 48.47 -39.522 50.953 -31.562 53.289 C-23.998 55.622 -17.346 59.136 -13 66 C-12.938 69.438 -12.938 69.438 -14 73 C-18.879 77.879 -22.667 80.23 -29.583 80.33 C-30.278 80.324 -30.972 80.318 -31.688 80.312 C-32.405 80.307 -33.123 80.301 -33.862 80.295 C-42.087 80.107 -47.3 78.144 -54.125 73.75 C-57.558 71.604 -58.762 71.008 -62.938 70.875 C-65.966 71.802 -65.966 71.802 -67.938 73.562 C-69.492 77.129 -69.838 80.241 -69 84 C-65.126 91.383 -57.531 94.552 -49.957 96.973 C-34.856 100.927 -20.092 99.846 -6.375 92.062 C-0.059 87.938 3.84 82.639 5.688 75.25 C6.611 65.643 6.268 57.893 0.41 49.805 C-8.137 40.3 -21.5 36.945 -33.221 32.891 C-46.965 28.07 -46.965 28.07 -49 24 C-49.438 20.688 -49.438 20.688 -49 17 C-44.272 12.057 -40.552 10.873 -33.938 10.688 C-24.08 10.806 -16.37 14.665 -9 21 C-9.99 21.495 -9.99 21.495 -11 22 C-13.172 20.855 -13.172 20.855 -15.75 19.188 C-23.691 14.06 -30.678 13.255 -40 14 C-43.716 14.848 -43.716 14.848 -46 17 C-46.975 20.09 -46.975 20.09 -47 23 C-43.427 26.135 -40.312 27.845 -35.809 29.332 C-34.616 29.739 -33.423 30.145 -32.193 30.564 C-29.718 31.387 -27.241 32.208 -24.764 33.025 C-13.732 36.793 -1.227 41.471 5 52 C9.431 62.675 10.14 71.212 6 82 C1.489 90.521 -5.811 95.633 -14.676 98.992 C-30.402 103.76 -46.419 102.68 -61 95 C-66.127 92.162 -70.254 88.728 -72 83 C-72.408 78.151 -71.731 74.981 -68.812 71.062 C-65.908 68.933 -63.474 67.889 -60 67 C-60 67.66 -60 68.32 -60 69 C-58.979 69.093 -58.979 69.093 -57.938 69.188 C-54.588 70.114 -52.687 71.562 -50.039 73.746 C-44.582 77.102 -38.646 77.34 -32.438 77.312 C-31.344 77.337 -31.344 77.337 -30.229 77.361 C-25.436 77.366 -22.138 76.657 -18 74 C-15.65 70.474 -15.336 69.15 -16 65 C-20.557 60.421 -25.363 58.359 -31.375 56.188 C-32.206 55.876 -33.038 55.565 -33.895 55.244 C-35.926 54.485 -37.962 53.741 -40 53 C-40.33 53.66 -40.66 54.32 -41 55 C-41 54.01 -41 53.02 -41 52 C-41.639 51.918 -42.279 51.835 -42.938 51.75 C-53.104 49.26 -62.886 43.719 -68.848 35 C-72.494 28.672 -73.127 21.53 -71.383 14.52 C-68.246 5.568 -62.716 -0.836 -54.438 -5.438 C-36.238 -13.7 -16.512 -10.174 0 0 Z " fill="#D3DCE0" transform="translate(385,382)"/>
<path d="M0 0 C0.677 0.473 1.354 0.946 2.051 1.434 C3 2.073 3.948 2.712 4.926 3.371 C8.791 7.122 10.799 10.641 11.551 15.996 C10.953 20.104 9.656 22.233 7.051 25.434 C3.78 27.614 1.931 28.123 -1.949 28.434 C-5.79 27.625 -7.909 25.604 -10.699 22.996 C-17.467 16.854 -26.661 15.014 -35.676 15.152 C-43.749 16.151 -52.298 19.818 -57.414 26.285 C-63.07 34.777 -63.843 43.406 -62.949 53.434 C-61.191 60.808 -57.44 67.265 -51.184 71.68 C-49.488 72.686 -47.719 73.565 -45.949 74.434 C-45.186 74.825 -44.423 75.217 -43.637 75.621 C-34.233 78.464 -25.195 77.6 -16.398 73.141 C-12.501 70.698 -9.657 68.227 -6.887 64.559 C-5.28 62.542 -4.482 61.582 -1.98 60.887 C2.451 60.721 5.665 61.629 9.051 64.434 C11.331 67.265 12.007 69.141 12.551 72.746 C11.564 80.021 7.096 85.088 1.578 89.695 C-3.581 93.243 -9.284 95.802 -14.949 98.434 C-15.939 98.929 -15.939 98.929 -16.949 99.434 C-33.493 102.103 -49.557 100.49 -63.949 91.434 C-75.901 82.305 -84.338 70.472 -86.949 55.434 C-87.239 51.97 -87.274 48.533 -87.262 45.059 C-87.278 44.177 -87.294 43.295 -87.311 42.387 C-87.321 28.034 -81.052 15.959 -71.305 5.656 C-52.428 -12.156 -21.526 -14.128 0 0 Z M-73.949 10.434 C-84.863 24.352 -86.653 39.238 -84.949 56.434 C-82.558 70.63 -74.361 81.944 -62.949 90.434 C-53.485 96.137 -44.615 98.824 -33.637 98.746 C-32.8 98.74 -31.963 98.735 -31.101 98.729 C-17.514 98.463 -4.474 93.618 5.492 84.148 C8.773 80.539 10.191 77.179 10.551 72.371 C10.362 68.37 10.362 68.37 7.926 65.496 C4.722 63.198 2.985 62.434 -0.949 62.434 C-4.498 63.853 -6.348 65.873 -8.637 68.809 C-13.2 74.389 -19.893 77.389 -26.949 78.434 C-37.895 79.162 -44.963 77.638 -53.949 71.434 C-54.609 71.434 -55.269 71.434 -55.949 71.434 C-56.197 70.897 -56.444 70.361 -56.699 69.809 C-57.945 67.308 -57.945 67.308 -59.887 64.746 C-64.68 57.047 -65.975 49.444 -64.949 40.434 C-63.003 32.188 -59.455 24.653 -52.445 19.645 C-43.075 13.889 -33.644 13.1 -22.883 14.965 C-16.831 16.665 -12.204 19.41 -7.512 23.621 C-4.189 25.972 -2.013 26.434 2.051 26.434 C6.309 24.204 6.309 24.204 9.051 20.434 C9.786 16.406 9.877 12.907 7.84 9.273 C0.774 0.665 -9.959 -4.994 -20.949 -6.566 C-24.847 -6.875 -28.728 -6.975 -32.637 -7.004 C-33.599 -7.014 -34.56 -7.024 -35.552 -7.035 C-50.685 -6.85 -63.7 -0.642 -73.949 10.434 Z " fill="#D4DDE0" transform="translate(304.94921875,228.56640625)"/>
<path d="M0 0 C0.808 0.102 1.616 0.204 2.449 0.309 C3.064 0.392 3.679 0.476 4.312 0.562 C4.312 2.562 4.312 2.562 2.812 4.188 C2.317 4.641 1.822 5.095 1.312 5.562 C2.457 5.501 2.457 5.501 3.625 5.438 C6.312 5.562 6.312 5.562 9.312 7.562 C9.75 9.875 9.75 9.875 9.312 12.562 C8.005 14.25 6.671 15.916 5.312 17.562 C4.523 20.248 4.523 20.248 4.312 22.562 C5.303 22.562 6.293 22.562 7.312 22.562 C7.312 23.883 7.312 25.202 7.312 26.562 C7.972 26.562 8.632 26.562 9.312 26.562 C8.711 28.583 8.039 30.584 7.312 32.562 C3.863 34.287 0.085 34.061 -3.688 33.562 C-6.062 31.75 -6.062 31.75 -7.688 29.562 C-8.265 28.985 -8.842 28.408 -9.438 27.812 C-9.85 27.4 -10.262 26.987 -10.688 26.562 C-11.017 28.872 -11.348 31.183 -11.688 33.562 C-15.317 33.562 -18.947 33.562 -22.688 33.562 C-22.688 32.242 -22.688 30.923 -22.688 29.562 C-23.348 29.562 -24.007 29.562 -24.688 29.562 C-24.688 27.253 -24.688 24.942 -24.688 22.562 C-28.002 24.271 -28.002 24.271 -28.375 27.688 C-28.478 28.636 -28.581 29.585 -28.688 30.562 C-28.027 30.893 -27.368 31.222 -26.688 31.562 C-28.337 31.232 -29.988 30.903 -31.688 30.562 C-31.358 26.933 -31.027 23.303 -30.688 19.562 C-30.027 19.562 -29.368 19.562 -28.688 19.562 C-28.688 18.572 -28.688 17.582 -28.688 16.562 C-27.697 16.232 -26.707 15.903 -25.688 15.562 C-25.027 15.893 -24.368 16.222 -23.688 16.562 C-23.688 15.903 -23.688 15.242 -23.688 14.562 C-24.678 14.232 -25.668 13.903 -26.688 13.562 C-24.312 12.938 -24.312 12.938 -21.688 12.562 C-19.688 14.562 -19.688 14.562 -19.492 18.258 C-19.505 19.735 -19.529 21.211 -19.562 22.688 C-19.572 23.442 -19.581 24.196 -19.59 24.973 C-19.613 26.836 -19.649 28.699 -19.688 30.562 C-18.038 30.562 -16.387 30.562 -14.688 30.562 C-14.775 28.165 -14.882 25.771 -15 23.375 C-15.023 22.701 -15.045 22.028 -15.068 21.334 C-15.302 16.986 -15.904 13.964 -18.688 10.562 C-22.287 9.667 -22.287 9.667 -25.688 9.562 C-25.688 9.232 -25.688 8.903 -25.688 8.562 C-25.012 8.441 -24.337 8.32 -23.641 8.195 C-22.303 7.944 -22.303 7.944 -20.938 7.688 C-20.056 7.525 -19.174 7.363 -18.266 7.195 C-14.589 6.293 -12.305 5.18 -9.625 2.5 C-6.521 -0.604 -4.124 -0.538 0 0 Z M1.312 15.562 C1.642 16.222 1.972 16.883 2.312 17.562 C2.642 16.903 2.972 16.242 3.312 15.562 C2.653 15.562 1.993 15.562 1.312 15.562 Z M-25.688 18.562 C-25.688 21.562 -25.688 21.562 -25.688 21.562 Z M1.312 19.562 C2.312 21.562 2.312 21.562 2.312 21.562 Z M-9.688 22.562 C-8.688 25.562 -8.688 25.562 -8.688 25.562 Z M-23.688 26.562 C-22.688 28.562 -22.688 28.562 -22.688 28.562 Z " fill="#E5E9E6" transform="translate(427.6875,674.4375)"/>
<path d="M0 0 C3.062 0.375 3.062 0.375 5 2.062 C6.062 4.375 6.062 4.375 5.062 9.375 C9.683 9.045 14.303 8.715 19.062 8.375 C19.062 9.035 19.062 9.695 19.062 10.375 C19.723 10.705 20.382 11.035 21.062 11.375 C25.535 15.441 25.652 19.421 25.984 25.145 C26.01 25.881 26.036 26.617 26.062 27.375 C26.722 27.375 27.382 27.375 28.062 27.375 C27.733 30.015 27.403 32.655 27.062 35.375 C24.423 35.375 21.783 35.375 19.062 35.375 C19.062 29.765 19.062 24.155 19.062 18.375 C17.413 19.035 15.762 19.695 14.062 20.375 C13.733 21.365 13.402 22.355 13.062 23.375 C13.723 23.375 14.382 23.375 15.062 23.375 C14.733 24.035 14.402 24.695 14.062 25.375 C13.835 27.246 13.653 29.122 13.5 31 C13.356 32.774 13.211 34.548 13.062 36.375 C7.123 36.375 1.183 36.375 -4.938 36.375 C-5.749 33.128 -6.033 31.438 -5.816 28.266 C-5.768 27.512 -5.72 26.757 -5.67 25.98 C-5.586 24.815 -5.586 24.815 -5.5 23.625 C-5.448 22.832 -5.396 22.039 -5.342 21.223 C-5.213 19.273 -5.076 17.324 -4.938 15.375 C-5.598 15.375 -6.257 15.375 -6.938 15.375 C-6.611 13.041 -6.276 10.708 -5.938 8.375 C-5.793 7.282 -5.649 6.189 -5.5 5.062 C-4.704 1.258 -3.86 0.493 0 0 Z " fill="#F7F8F7" transform="translate(450.9375,671.625)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.619 1.835 2.237 1.67 2.875 1.5 C8.495 0.601 13.908 -0.004 19.125 2.5 C20.053 3.242 20.053 3.242 21 4 C21.536 3.505 22.072 3.01 22.625 2.5 C25.594 0.625 27.553 0.765 31 1 C31.495 1.99 31.495 1.99 32 3 C30.298 3.588 30.298 3.588 28.562 4.188 C24.857 5.592 24.857 5.592 23.938 8.5 C23.784 11.254 23.784 11.254 26 14 C28.176 15.222 28.176 15.222 30.625 16.188 C31.442 16.532 32.26 16.876 33.102 17.23 C33.728 17.484 34.355 17.738 35 18 C34.67 19.32 34.34 20.64 34 22 C29.781 22.196 26.83 21.802 23 20 C23.495 22.475 23.495 22.475 24 25 C18.002 27.816 13.044 29.84 6.375 28 C2.99 25.994 2.625 25.566 0.938 22.375 C-0.143 20.42 -0.143 20.42 -2 19 C-5.652 18.295 -9.104 18.125 -12.82 18.07 C-16 18 -16 18 -19 17 C-19 15.02 -19 13.04 -19 11 C-18.67 11 -18.34 11 -18 11 C-18 12.65 -18 14.3 -18 16 C-13.05 15.67 -8.1 15.34 -3 15 C-2.67 11.37 -2.34 7.74 -2 4 C-4.97 3.67 -7.94 3.34 -11 3 C-10.01 2.67 -9.02 2.34 -8 2 C-8 1.34 -8 0.68 -8 0 C-4.979 -0.971 -2.995 -1.136 0 0 Z " fill="#F2F4F4" transform="translate(592,629)"/>
<path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.062 C3.778 1.269 3.778 1.269 4.125 2.5 C4.558 3.346 4.991 4.191 5.438 5.062 C7.096 5.436 8.763 5.77 10.438 6.062 C11.812 7.875 11.812 7.875 12.438 10.062 C12.062 12.312 12.062 12.312 11.438 14.062 C10.778 13.732 10.117 13.403 9.438 13.062 C9.438 13.722 9.438 14.383 9.438 15.062 C7.788 15.062 6.138 15.062 4.438 15.062 C4.768 17.043 5.097 19.023 5.438 21.062 C7.087 21.393 8.737 21.722 10.438 22.062 C11.097 25.362 11.758 28.663 12.438 32.062 C11.489 32.372 10.54 32.681 9.562 33 C6.577 33.936 6.577 33.936 4.438 35.062 C4.438 34.403 4.438 33.742 4.438 33.062 C3.365 32.959 2.293 32.856 1.188 32.75 C-2.421 32.088 -3.926 31.488 -6.562 29.062 C-7.202 29.722 -7.841 30.383 -8.5 31.062 C-12.607 33.745 -15.796 33.436 -20.562 33.062 C-20.562 32.072 -20.562 31.082 -20.562 30.062 C-19.948 29.965 -19.333 29.867 -18.699 29.766 C-13.568 28.93 -13.568 28.93 -9.5 25.938 C-9.191 25.319 -8.881 24.7 -8.562 24.062 C-9.449 24.578 -10.336 25.094 -11.25 25.625 C-14.803 27.167 -16.005 27.409 -19.562 26.062 C-21.375 25 -21.375 25 -22.562 23.062 C-23.11 19.469 -23.245 16.277 -21.438 13.062 C-18.084 11.274 -14.804 11.801 -11.188 12.562 C-10.651 12.727 -10.115 12.893 -9.562 13.062 C-11.204 9.938 -11.204 9.938 -13.727 9.332 C-14.945 9.106 -14.945 9.106 -16.188 8.875 C-17.005 8.716 -17.822 8.558 -18.664 8.395 C-19.291 8.285 -19.917 8.175 -20.562 8.062 C-20.562 7.072 -20.562 6.082 -20.562 5.062 C-15.612 5.062 -10.663 5.062 -5.562 5.062 C-5.232 3.742 -4.903 2.423 -4.562 1.062 C-3.562 0.062 -3.562 0.062 0 0 Z M-17.562 15.062 C-18.222 15.722 -18.883 16.383 -19.562 17.062 C-19.244 19.171 -19.244 19.171 -18.562 21.062 C-18.562 20.072 -18.562 19.082 -18.562 18.062 C-18.232 18.722 -17.903 19.383 -17.562 20.062 C-16.572 19.732 -15.582 19.403 -14.562 19.062 C-14.893 17.742 -15.222 16.423 -15.562 15.062 C-16.222 15.062 -16.883 15.062 -17.562 15.062 Z " fill="#F5F7F5" transform="translate(571.5625,674.9375)"/>
<path d="M0 0 C2.875 2.5 2.875 2.5 5 5 C5.681 5.696 5.681 5.696 6.375 6.406 C13.198 13.656 18.387 23.158 20 33 C20.131 33.731 20.263 34.462 20.398 35.215 C22.243 47.863 19.317 61.896 12.293 72.602 C10.6 74.808 8.827 76.904 7 79 C6.257 80.031 5.515 81.062 4.75 82.125 C-4.463 91.757 -18.246 97.425 -31.445 98.203 C-48.199 98.417 -62.423 94.195 -74.742 82.43 C-80.267 76.485 -87 68.515 -87 60 C-87.66 59.67 -88.32 59.34 -89 59 C-89.729 55.721 -89.813 52.348 -90 49 C-90.054 48.051 -90.108 47.103 -90.164 46.125 C-90.399 38.091 -89.649 29.298 -86 22 C-86.66 21.67 -87.32 21.34 -88 21 C-87.01 20.67 -86.02 20.34 -85 20 C-83.313 17.453 -83.313 17.453 -81.812 14.438 C-81.283 13.426 -80.753 12.414 -80.207 11.371 C-79.809 10.589 -79.41 9.806 -79 9 C-78.34 9 -77.68 9 -77 9 C-76.752 8.421 -76.505 7.842 -76.25 7.246 C-71.134 -1.947 -60.201 -6.913 -50.562 -9.812 C-32.297 -13.908 -15.438 -10.539 0 0 Z M-56 -6 C-57.251 -5.507 -57.251 -5.507 -58.527 -5.004 C-70.944 0.525 -79.141 10.497 -84.812 22.625 C-90.343 38.343 -89.25 54.096 -82.312 69.125 C-75.2 82.649 -64.274 90.165 -50 95 C-38.818 97.463 -25.663 97.214 -15 93 C-14.163 92.671 -13.327 92.343 -12.465 92.004 C0.907 86.031 10.807 74.472 16 60.977 C20.701 46.984 20.049 33.242 13.75 19.812 C7.563 8.337 -1.683 -1.217 -14 -6 C-14.739 -6.298 -15.477 -6.596 -16.238 -6.902 C-28.3 -10.973 -44.201 -10.801 -56 -6 Z " fill="#D5DDE1" transform="translate(417,231)"/>
<path d="M0 0 C4.292 -0.058 8.583 -0.094 12.875 -0.125 C14.098 -0.142 15.322 -0.159 16.582 -0.176 C17.749 -0.182 18.915 -0.189 20.117 -0.195 C21.196 -0.206 22.275 -0.216 23.386 -0.227 C26 0 26 0 28 2 C27.625 4.625 27.625 4.625 27 7 C27.66 7.33 28.32 7.66 29 8 C25.911 10.059 25.291 10.239 21.812 10.125 C19.925 10.063 19.925 10.063 18 10 C18 9.34 18 8.68 18 8 C15.652 11.522 15.623 13.508 15.375 17.688 C15.3 18.867 15.225 20.046 15.148 21.262 C15.099 22.165 15.05 23.069 15 24 C13.68 24 12.36 24 11 24 C11 16.74 11 9.48 11 2 C7.7 1.67 4.4 1.34 1 1 C1.66 1.66 2.32 2.32 3 3 C4.32 3 5.64 3 7 3 C7 9.93 7 16.86 7 24 C5.68 24 4.36 24 3 24 C2.67 23.34 2.34 22.68 2 22 C1.01 22.66 0.02 23.32 -1 24 C-3.721 24.472 -6.275 24.444 -9 24 C-11.943 21.722 -12.212 19.277 -12.688 15.75 C-12.791 14.513 -12.894 13.275 -13 12 C-13.33 12 -13.66 12 -14 12 C-14 8.37 -14 4.74 -14 1 C-11.03 1 -8.06 1 -5 1 C-5 5.62 -5 10.24 -5 15 C-4.01 15 -3.02 15 -2 15 C-0.822 11.993 -0.822 11.993 -1 8.625 C-1 5.517 -0.661 3.007 0 0 Z " fill="#F3F6F4" transform="translate(248,630)"/>
<path d="M0 0 C10.66 10.158 18.504 21.642 19.078 36.816 C19.393 54.983 15.501 69.826 2.613 83.32 C-7.816 92.812 -7.816 92.812 -13.125 92.812 C-13.455 93.472 -13.785 94.132 -14.125 94.812 C-27.697 100.637 -45.452 99.527 -58.949 94.305 C-63.386 92.22 -67.277 89.839 -71.125 86.812 C-72.156 86.07 -73.188 85.327 -74.25 84.562 C-85.843 73.474 -91.906 57.652 -92.5 41.75 C-91.966 28.149 -86.714 15.31 -78.125 4.812 C-77.465 4.812 -76.805 4.812 -76.125 4.812 C-75.795 3.822 -75.465 2.832 -75.125 1.812 C-73.174 0.215 -71.268 -1.216 -69.188 -2.625 C-68.597 -3.025 -68.007 -3.426 -67.398 -3.838 C-46.928 -17.373 -18.721 -16.23 0 0 Z M-77.25 6.875 C-88.003 19.456 -91.183 33.594 -90.125 49.812 C-87.919 65.051 -80.152 77.452 -68.312 87.125 C-57.46 94.756 -44.236 98.026 -31.125 96.812 C-16.407 94.243 -2.051 88.813 7.037 76.2 C14.003 65.597 17.41 55.537 17.25 42.812 C17.262 41.892 17.273 40.972 17.285 40.023 C17.224 25.666 11.157 13.431 1.352 3.102 C-9.938 -7.333 -23.9 -11.522 -39.047 -11.336 C-54.275 -10.602 -66.773 -3.943 -77.25 6.875 Z " fill="#D6DEE2" transform="translate(614.125,385.1875)"/>
<path d="M0 0 C6.236 7.699 13 16.754 13 27 C14.485 27.495 14.485 27.495 16 28 C15.34 28 14.68 28 14 28 C14 29.65 14 31.3 14 33 C14.66 33 15.32 33 16 33 C14.63 48.209 12.017 60.281 3 73 C2.475 73.763 1.951 74.526 1.41 75.312 C0.712 76.148 0.712 76.148 0 77 C-0.66 77 -1.32 77 -2 77 C-2 77.66 -2 78.32 -2 79 C-16.49 91.696 -31.871 95.008 -50.539 94.2 C-55.777 93.775 -60.289 92.245 -65 90 C-65.781 89.649 -66.562 89.299 -67.367 88.938 C-80.146 82.57 -90.279 71.111 -94.988 57.719 C-98.321 45.47 -98.822 34.235 -95 22 C-94.742 21.121 -94.484 20.242 -94.219 19.336 C-89.846 6.583 -79.797 -4.103 -68.137 -10.594 C-62.873 -13.098 -57.718 -14.893 -52 -16 C-51.045 -16.201 -51.045 -16.201 -50.07 -16.406 C-32.297 -19.357 -12.853 -12.225 0 0 Z M-82.742 3.262 C-92.635 15.259 -97.169 29.501 -96 45 C-93.684 60.62 -86.814 73.269 -74.332 83.102 C-61.641 91.709 -46.966 94.745 -31.84 92.539 C-16.404 89.082 -3.814 80.636 5 67.5 C12.696 54.379 15.186 39.749 11.875 24.812 C7.761 10.47 -1.291 -0.38 -13.801 -8.309 C-37.156 -20.656 -64.699 -15.855 -82.742 3.262 Z " fill="#DDE4E7" transform="translate(496,389)"/>
<path d="M0 0 C2.969 1.39 4.697 2.697 7 5 C9.211 5.243 9.211 5.243 11.625 5.125 C12.442 5.107 13.26 5.089 14.102 5.07 C14.728 5.047 15.355 5.024 16 5 C16 5.66 16 6.32 16 7 C13.36 7 10.72 7 8 7 C8.66 7.66 9.32 8.32 10 9 C10.256 11.834 10.42 14.599 10.5 17.438 C10.539 23.527 10.539 23.527 13 29 C18.542 29.572 18.542 29.572 23.5 27.5 C23.995 27.005 24.49 26.51 25 26 C25 27.32 25 28.64 25 30 C26.32 30 27.64 30 29 30 C29 23.07 29 16.14 29 9 C27.35 9.33 25.7 9.66 24 10 C23.505 8.515 23.505 8.515 23 7 C26.96 7 30.92 7 35 7 C34.34 7.33 33.68 7.66 33 8 C33 15.26 33 22.52 33 30 C34.32 30 35.64 30 37 30 C36.948 28.662 36.948 28.662 36.895 27.297 C36.867 26.126 36.84 24.956 36.812 23.75 C36.76 22.01 36.76 22.01 36.707 20.234 C37.02 16.777 37.657 15.484 40 13 C40.66 13.33 41.32 13.66 42 14 C41.67 14.33 41.34 14.66 41 15 C43.475 15.495 43.475 15.495 46 16 C46 16.33 46 16.66 46 17 C44.35 17.33 42.7 17.66 41 18 C40.909 19.499 40.909 19.499 40.816 21.027 C40.733 22.318 40.649 23.608 40.562 24.938 C40.481 26.225 40.4 27.513 40.316 28.84 C40 32 40 32 39 33 C37.511 33.087 36.019 33.107 34.527 33.098 C33.628 33.094 32.729 33.091 31.803 33.088 C30.384 33.075 30.384 33.075 28.938 33.062 C27.988 33.058 27.039 33.053 26.061 33.049 C23.707 33.037 21.354 33.021 19 33 C18.505 33.99 18.505 33.99 18 35 C18 34.34 18 33.68 18 33 C17.154 33.041 16.309 33.082 15.438 33.125 C12 33 12 33 8.625 31.75 C4.14 30.822 2.556 31.814 -1.203 34.152 C-3 35 -3 35 -6 34 C-6 33.01 -6 32.02 -6 31 C-5.362 30.807 -4.724 30.613 -4.066 30.414 C-3.24 30.154 -2.414 29.893 -1.562 29.625 C-0.739 29.37 0.085 29.115 0.934 28.852 C3.178 28.191 3.178 28.191 4 26 C4.32 19.709 4.32 19.709 2 14 C2.33 13.34 2.66 12.68 3 12 C3.333 9 3.333 9 3 6 C-0.75 3.5 -2.559 3.464 -7 4 C-10.16 6.541 -11.715 8.146 -13 12 C-14.32 11.67 -15.64 11.34 -17 11 C-13.8 2.243 -9.606 -1.482 0 0 Z " fill="#F1F3F1" transform="translate(226,624)"/>
<path d="M0 0 C1.274 0.427 2.544 0.867 3.812 1.312 C4.52 1.556 5.228 1.8 5.957 2.051 C8.927 3.431 10.543 5.086 12 8 C12.084 9.594 12.107 11.192 12.098 12.789 C12.094 13.73 12.091 14.671 12.088 15.641 C12.075 17.118 12.075 17.118 12.062 18.625 C12.058 19.618 12.053 20.61 12.049 21.633 C12.037 24.089 12.019 26.544 12 29 C8.7 29 5.4 29 2 29 C1.837 26.982 1.674 24.964 1.512 22.945 C1.126 20.605 1.126 20.605 -2 19 C-1.01 18.67 -0.02 18.34 1 18 C0.01 18 -0.98 18 -2 18 C-2.33 21.3 -2.66 24.6 -3 28 C-4.98 28 -6.96 28 -9 28 C-9 27.34 -9 26.68 -9 26 C-8.01 26 -7.02 26 -6 26 C-6.046 25.036 -6.093 24.072 -6.141 23.078 C-6.195 21.183 -6.195 21.183 -6.25 19.25 C-6.296 17.997 -6.343 16.744 -6.391 15.453 C-6 12 -6 12 -4.184 9.539 C-2 8 -2 8 1.188 8.125 C2.116 8.414 3.044 8.702 4 9 C5.533 12.065 5.103 15.268 5.062 18.625 C5.058 19.331 5.053 20.038 5.049 20.766 C5.037 22.51 5.019 24.255 5 26 C6.32 26 7.64 26 9 26 C8.913 23.228 8.806 20.459 8.688 17.688 C8.654 16.509 8.654 16.509 8.619 15.307 C8.584 14.549 8.548 13.792 8.512 13.012 C8.486 12.315 8.459 11.619 8.432 10.901 C7.867 8.417 7.036 7.507 5 6 C0.426 5.637 -3.064 5.376 -7 8 C-7 7.34 -7 6.68 -7 6 C-7.99 5.67 -8.98 5.34 -10 5 C-10 11.93 -10 18.86 -10 26 C-15 22 -15 22 -15.312 19.379 C-15.209 18.449 -15.106 17.52 -15 16.562 C-14.798 11.936 -14.798 11.936 -17 7.812 C-21.122 5.322 -23.223 5.469 -28 6 C-30.646 6.836 -31.603 7.435 -33.188 9.688 C-34.151 12.18 -34.151 12.18 -34.5 15.688 C-34.665 16.781 -34.83 17.874 -35 19 C-35.66 19.33 -36.32 19.66 -37 20 C-37.253 13.674 -36.727 9.355 -32.938 4.188 C-29.133 1.355 -25.718 0.671 -21 1 C-20.331 1.349 -19.662 1.699 -18.973 2.059 C-16.448 3.263 -15.645 2.766 -13 2 C-10.751 1.932 -8.5 1.915 -6.25 1.938 C-4.494 1.951 -4.494 1.951 -2.703 1.965 C-1.811 1.976 -0.919 1.988 0 2 C0 1.34 0 0.68 0 0 Z M-3 14 C-2.67 14.66 -2.34 15.32 -2 16 C-1.34 16 -0.68 16 0 16 C0 15.34 0 14.68 0 14 C-0.99 14 -1.98 14 -3 14 Z " fill="#F5F7F6" transform="translate(822,628)"/>
<path d="M0 0 C0.227 1.114 0.454 2.227 0.688 3.375 C1.606 7.16 1.606 7.16 4.625 8.438 C5.409 8.623 6.192 8.809 7 9 C7 9.99 7 10.98 7 12 C6.34 12 5.68 12 5 12 C5.114 14.959 5.24 17.917 5.375 20.875 C5.406 21.717 5.437 22.559 5.469 23.426 C5.507 24.231 5.546 25.037 5.586 25.867 C5.633 26.983 5.633 26.983 5.681 28.121 C5.854 30.24 5.854 30.24 8 32 C9 33 10 34 11 35 C7.563 35.116 4.126 35.187 0.688 35.25 C-0.289 35.284 -1.265 35.317 -2.271 35.352 C-3.678 35.371 -3.678 35.371 -5.113 35.391 C-5.977 35.412 -6.841 35.433 -7.732 35.454 C-10 35 -10 35 -11.805 32.976 C-12.2 32.324 -12.594 31.672 -13 31 C-12.01 30.34 -11.02 29.68 -10 29 C-9.67 29.33 -9.34 29.66 -9 30 C-8.832 26.049 -8.672 22.098 -8.518 18.147 C-8.438 16.141 -8.353 14.135 -8.268 12.129 C-8.219 10.872 -8.171 9.615 -8.121 8.32 C-8.074 7.158 -8.027 5.995 -7.978 4.797 C-7.794 2.044 -7.794 2.044 -9 0 C-5.931 -1.534 -3.299 -0.55 0 0 Z " fill="#E2E7E8" transform="translate(543,622)"/>
<path d="M0 0 C2.638 2.262 3.491 3.069 4.625 6.25 C4.821 9.131 4.908 11.914 4.9 14.795 C4.907 16.086 4.907 16.086 4.915 17.403 C4.929 20.246 4.928 23.09 4.926 25.934 C4.93 27.911 4.934 29.889 4.939 31.867 C4.946 36.012 4.946 40.157 4.941 44.302 C4.935 49.613 4.952 54.923 4.975 60.234 C4.99 64.318 4.991 68.403 4.988 72.487 C4.988 74.445 4.993 76.403 5.003 78.361 C5.016 81.1 5.009 83.837 4.997 86.576 C5.009 87.788 5.009 87.788 5.02 89.024 C4.952 96.296 2.114 99.833 -2.375 105.25 C-2.375 104.59 -2.375 103.93 -2.375 103.25 C-3.365 103.745 -3.365 103.745 -4.375 104.25 C-9.581 104.65 -13.021 104.218 -17.375 101.25 C-20.557 96.281 -20.816 92.197 -20.795 86.494 C-20.803 85.635 -20.812 84.776 -20.821 83.89 C-20.844 81.062 -20.845 78.234 -20.844 75.406 C-20.851 73.434 -20.859 71.461 -20.867 69.489 C-20.88 65.36 -20.882 61.231 -20.876 57.102 C-20.87 51.816 -20.901 46.53 -20.941 41.244 C-20.967 37.173 -20.97 33.101 -20.966 29.03 C-20.968 27.081 -20.978 25.132 -20.995 23.183 C-21.018 20.457 -21.008 17.732 -20.99 15.006 C-21.003 14.205 -21.017 13.403 -21.03 12.578 C-20.944 6.797 -19.493 4.311 -15.375 0.25 C-10.661 -3.326 -4.918 -2.55 0 0 Z M-17.375 4.25 C-19.001 7.503 -18.54 11.084 -18.552 14.659 C-18.559 15.53 -18.565 16.4 -18.572 17.296 C-18.592 20.183 -18.603 23.07 -18.613 25.957 C-18.617 26.942 -18.621 27.927 -18.626 28.941 C-18.647 34.154 -18.661 39.366 -18.67 44.578 C-18.681 49.966 -18.716 55.352 -18.755 60.739 C-18.781 64.879 -18.79 69.018 -18.793 73.157 C-18.798 75.143 -18.81 77.129 -18.828 79.115 C-18.853 81.894 -18.852 84.673 -18.845 87.452 C-18.858 88.273 -18.871 89.093 -18.884 89.939 C-18.848 93.867 -18.644 95.814 -16.533 99.237 C-13.144 102.398 -9.925 102.585 -5.379 102.543 C-2.23 102.083 -0.54 100.481 1.625 98.25 C3.262 94.975 2.8 91.349 2.818 87.749 C2.826 86.869 2.834 85.989 2.843 85.083 C2.868 82.163 2.884 79.244 2.898 76.324 C2.904 75.328 2.909 74.332 2.915 73.306 C2.941 68.036 2.961 62.765 2.975 57.494 C2.992 52.047 3.037 46.599 3.087 41.152 C3.121 36.966 3.133 32.781 3.138 28.595 C3.145 26.587 3.16 24.579 3.184 22.57 C3.216 19.76 3.216 16.95 3.208 14.139 C3.225 13.31 3.241 12.48 3.258 11.625 C3.212 7.326 3.003 5.729 0.245 2.234 C-3.379 -0.51 -5.604 -0.4 -9.996 -0.184 C-13.324 0.423 -15.119 1.775 -17.375 4.25 Z " fill="#D7DFE4" transform="translate(595.375,222.75)"/>
<path d="M0 0 C3 2 3 2 3.448 3.864 C3.481 4.601 3.513 5.338 3.547 6.098 C3.607 7.299 3.607 7.299 3.668 8.525 C3.695 9.363 3.722 10.2 3.75 11.062 C3.793 11.904 3.835 12.745 3.879 13.611 C3.971 15.739 3.992 17.87 4 20 C1.641 22.359 -3.499 21.988 -6.688 22.062 C-9.126 22.062 -11.562 22.044 -14 22 C-14 21.34 -14 20.68 -14 20 C-14.606 20.24 -15.212 20.48 -15.836 20.727 C-22.535 23.097 -27.213 23.007 -34 21 C-34.33 19.68 -34.66 18.36 -35 17 C-32 17 -32 17 -30 18 C-28.502 18.095 -27.001 18.13 -25.5 18.125 C-24.706 18.128 -23.912 18.13 -23.094 18.133 C-20.941 18.133 -20.941 18.133 -19 17 C-18.67 16.01 -18.34 15.02 -18 14 C-18.536 14.33 -19.072 14.66 -19.625 15 C-22.672 16.283 -24.72 16.41 -28 16 C-30.375 14.188 -30.375 14.188 -32 12 C-32 11.01 -32 10.02 -32 9 C-26.72 9 -21.44 9 -16 9 C-16 7.68 -16 6.36 -16 5 C-7.405 0.223 -7.405 0.223 -3 1 C-1 3 -1 3 -1 6 C-1.615 6.133 -2.23 6.266 -2.863 6.402 C-4.076 6.667 -4.076 6.667 -5.312 6.938 C-6.113 7.112 -6.914 7.286 -7.738 7.465 C-10.145 7.999 -10.145 7.999 -13 9 C-13.677 12.403 -13.677 12.403 -14 16 C-12.057 18.38 -12.057 18.38 -8.5 18.25 C-4.943 18.38 -4.943 18.38 -3 16 C-5.31 16 -7.62 16 -10 16 C-10.33 14.35 -10.66 12.7 -11 11 C-7.488 8.592 -5.273 7.634 -1 8 C-0.67 5.36 -0.34 2.72 0 0 Z " fill="#EEF1F0" transform="translate(750,635)"/>
<path d="M0 0 C6.93 0 13.86 0 21 0 C23.25 6.75 23.25 6.75 23.195 10.305 C23.189 11.052 23.182 11.8 23.176 12.57 C23.159 13.331 23.142 14.091 23.125 14.875 C23.116 15.659 23.107 16.442 23.098 17.25 C23.074 19.167 23.038 21.083 23 23 C21.68 23 20.36 23 19 23 C18.67 18.05 18.34 13.1 18 8 C15.789 7.895 15.789 7.895 13 9 C13 9.66 13 10.32 13 11 C12.34 11 11.68 11 11 11 C11.33 13.97 11.66 16.94 12 20 C11.34 20 10.68 20 10 20 C10 20.99 10 21.98 10 23 C10.66 23.33 11.32 23.66 12 24 C11.34 24 10.68 24 10 24 C10 24.66 10 25.32 10 26 C8.02 26 6.04 26 4 26 C3.67 26.66 3.34 27.32 3 28 C3 27.34 3 26.68 3 26 C2.34 26 1.68 26 1 26 C-0.02 22.941 -0.115 20.876 -0.098 17.676 C-0.094 16.648 -0.091 15.62 -0.088 14.561 C-0.08 13.489 -0.071 12.417 -0.062 11.312 C-0.058 10.229 -0.053 9.146 -0.049 8.029 C-0.037 5.353 -0.019 2.676 0 0 Z " fill="#EFF1EE" transform="translate(284,631)"/>
<path d="M0 0 C2.5 3.804 2.289 7.682 2.275 12.07 C2.28 12.88 2.285 13.69 2.29 14.525 C2.304 17.202 2.303 19.878 2.301 22.555 C2.305 24.418 2.309 26.281 2.314 28.144 C2.321 32.05 2.321 35.955 2.316 39.861 C2.31 44.86 2.327 49.859 2.35 54.858 C2.365 58.706 2.366 62.555 2.363 66.403 C2.363 68.246 2.368 70.089 2.378 71.931 C2.391 74.512 2.384 77.091 2.372 79.671 C2.384 80.808 2.384 80.808 2.395 81.967 C2.34 87.604 1.188 91.839 -2.879 95.855 C-7.446 98.32 -13.017 97.422 -17.938 96.375 C-20.799 94.468 -21.474 92.063 -23 89 C-24.269 87.198 -25.585 85.428 -26.938 83.688 C-30.682 78.785 -34.279 73.806 -37.812 68.75 C-42.925 61.448 -48.22 54.3 -53.602 47.195 C-55.122 45.17 -56.595 43.107 -58 41 C-58.008 41.572 -58.016 42.144 -58.025 42.733 C-58.11 48.67 -58.21 54.607 -58.317 60.543 C-58.356 62.761 -58.391 64.978 -58.422 67.196 C-58.467 70.378 -58.525 73.56 -58.586 76.742 C-58.597 77.738 -58.609 78.734 -58.621 79.76 C-58.641 80.681 -58.661 81.602 -58.681 82.55 C-58.694 83.363 -58.708 84.176 -58.722 85.013 C-59 87 -59 87 -61 89 C-61 70.85 -61 52.7 -61 34 C-57.696 37.304 -55.275 40.269 -52.625 44.062 C-47.976 50.665 -43.211 57.174 -38.401 63.659 C-35.377 67.745 -32.387 71.848 -29.461 76.004 C-28.89 76.811 -28.318 77.618 -27.73 78.45 C-26.654 79.974 -25.586 81.503 -24.526 83.038 C-18.892 91.151 -18.892 91.151 -10.453 95.508 C-9.363 95.318 -9.363 95.318 -8.25 95.125 C-7.513 95.024 -6.775 94.924 -6.016 94.82 C-3.209 93.678 -2.495 92.661 -1 90 C-0.696 87.026 -0.562 84.259 -0.568 81.284 C-0.557 80.406 -0.547 79.527 -0.537 78.622 C-0.507 75.722 -0.497 72.822 -0.488 69.922 C-0.471 67.91 -0.453 65.898 -0.434 63.886 C-0.386 58.592 -0.357 53.298 -0.33 48.005 C-0.301 42.602 -0.254 37.199 -0.209 31.797 C-0.123 21.198 -0.056 10.599 0 0 Z M-60 38 C-59 40 -59 40 -59 40 Z " fill="#CCD7DB" transform="translate(727,385)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.99 0.34 3.98 0 5 C-0.101 7.666 -0.147 10.306 -0.145 12.972 C-0.149 13.784 -0.152 14.596 -0.155 15.432 C-0.165 18.119 -0.167 20.805 -0.168 23.492 C-0.171 25.359 -0.175 27.226 -0.178 29.093 C-0.184 33.009 -0.186 36.925 -0.185 40.84 C-0.185 45.856 -0.199 50.872 -0.216 55.888 C-0.227 59.745 -0.229 63.602 -0.229 67.459 C-0.23 69.308 -0.234 71.157 -0.242 73.007 C-0.252 75.594 -0.249 78.181 -0.243 80.769 C-0.249 81.532 -0.254 82.295 -0.26 83.08 C-0.23 88.09 0.234 91.952 3.562 95.812 C7.313 97.64 10.911 97.699 15 97 C17.502 95.124 18.619 93.761 20 91 C20.119 89.275 20.177 87.545 20.205 85.816 C20.225 84.735 20.245 83.654 20.266 82.54 C20.291 80.784 20.291 80.784 20.316 78.992 C20.337 77.796 20.358 76.601 20.379 75.369 C20.445 71.538 20.504 67.706 20.562 63.875 C20.606 61.283 20.649 58.69 20.693 56.098 C20.801 49.732 20.902 43.366 21 37 C24.228 40.755 26.538 44.676 28.715 49.094 C29.408 50.468 30.101 51.841 30.795 53.215 C31.869 55.359 32.94 57.505 34.007 59.653 C35.047 61.742 36.099 63.824 37.152 65.906 C37.464 66.543 37.776 67.179 38.097 67.835 C40.183 71.941 42.505 74.997 46 78 C48.45 78.392 48.45 78.392 50.812 77.812 C51.603 77.675 52.393 77.537 53.207 77.395 C53.799 77.264 54.39 77.134 55 77 C55.214 76.237 55.428 75.474 55.648 74.688 C57.531 69.551 60.071 64.832 62.625 60 C63.159 58.983 63.693 57.966 64.243 56.918 C68.327 49.178 72.557 41.54 77 34 C77.33 34 77.66 34 78 34 C78 51.82 78 69.64 78 88 C77.67 88 77.34 88 77 88 C76.063 82.476 75.868 77.163 75.902 71.566 C75.904 70.688 75.905 69.811 75.907 68.906 C75.912 66.125 75.925 63.344 75.938 60.562 C75.943 58.667 75.947 56.772 75.951 54.877 C75.962 50.251 75.979 45.626 76 41 C68.003 55.19 68.003 55.19 60.777 69.783 C58.441 74.667 55.729 78.977 50.5 80.938 C47.386 81.015 45.719 80.534 43 79 C39.911 75.507 37.738 71.887 35.617 67.746 C35.317 67.163 35.017 66.581 34.707 65.98 C33.758 64.135 32.816 62.287 31.875 60.438 C31.236 59.193 30.597 57.949 29.957 56.705 C27.616 52.148 25.289 47.584 23 43 C22.999 43.613 22.997 44.226 22.996 44.858 C22.976 51.25 22.922 57.641 22.847 64.033 C22.824 66.417 22.81 68.801 22.804 71.185 C22.795 74.616 22.754 78.046 22.707 81.477 C22.711 82.539 22.715 83.602 22.719 84.697 C22.556 92.975 22.556 92.975 19.581 96.658 C15.736 100.147 13.364 100.342 8.324 100.285 C4.749 99.847 2.667 98.342 0 96 C-2.712 92.196 -2.281 87.902 -2.259 83.424 C-2.262 82.56 -2.266 81.696 -2.269 80.806 C-2.278 77.953 -2.272 75.099 -2.266 72.246 C-2.267 70.264 -2.269 68.281 -2.271 66.299 C-2.274 62.145 -2.27 57.992 -2.261 53.838 C-2.249 48.509 -2.256 43.181 -2.268 37.852 C-2.275 33.759 -2.273 29.666 -2.268 25.573 C-2.266 23.608 -2.268 21.643 -2.273 19.677 C-2.278 16.934 -2.27 14.191 -2.259 11.447 C-2.263 10.632 -2.267 9.817 -2.271 8.977 C-2.242 5.124 -2.188 3.282 0 0 Z " fill="#F6F7F8" transform="translate(454,227)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.258 2.083 3.516 2.165 4.812 2.25 C13.404 3.005 23.436 8.375 29.465 14.484 C32.672 18.358 33.858 21.739 33.547 26.781 C32.543 30.856 30.33 33.519 27 36 C23.5 37.188 23.5 37.188 20 37 C17 35.375 17 35.375 14 33 C12.819 32.116 11.631 31.241 10.438 30.375 C9.9 29.97 9.362 29.565 8.809 29.148 C1.752 24.668 -7.65 24.151 -15.75 25.562 C-23.854 27.823 -29.56 32.647 -33.848 39.785 C-37.874 47.524 -38.344 56.444 -35.75 64.75 C-32.599 72.935 -28.661 79.376 -20.43 83.191 C-18.709 83.871 -16.978 84.523 -15.234 85.141 C-13 86 -13 86 -10 88 C-17.519 88.8 -22.372 85.879 -28.176 81.273 C-35.481 74.587 -39.087 67.283 -39.641 57.413 C-39.843 47.227 -37.774 39.407 -30.879 31.598 C-23.191 24.229 -16.498 22.494 -6.001 22.58 C2.559 22.893 8.202 25.397 14.75 30.75 C17.48 32.98 18.758 33.944 22.25 34.75 C26.108 33.698 27.62 32.196 30 29 C31.269 24.893 31.423 22.924 29.625 19 C21.855 10.119 12.245 4.991 0.478 3.729 C-15.835 2.656 -29.703 5.341 -42.891 15.449 C-48.044 20.057 -51.059 25.981 -54.301 31.988 C-56 35 -56 35 -58 37 C-56.693 25.964 -48.437 16.749 -40 10 C-27.359 1.754 -14.615 0.689 0 0 Z " fill="#D7DFE3" transform="translate(786,219)"/>
<path d="M0 0 C0.667 4.667 1.333 9.333 2 14 C3.518 9.447 4.11 5.841 4 1 C8.455 0.505 8.455 0.505 13 0 C13 8.25 13 16.5 13 25 C11.68 25 10.36 25 9 25 C8.67 24.01 8.34 23.02 8 22 C7.464 22.495 6.928 22.99 6.375 23.5 C3.318 25.431 1.562 25.445 -2 25 C-4.354 23.727 -5.672 22.935 -6.57 20.375 C-7.178 17.019 -7.366 13.65 -7.562 10.25 C-7.606 9.553 -7.649 8.855 -7.693 8.137 C-7.799 6.425 -7.9 4.712 -8 3 C-8.99 3.66 -9.98 4.32 -11 5 C-11 3.68 -11 2.36 -11 1 C-7.276 0.307 -3.789 -0.111 0 0 Z " fill="#F1F3F2" transform="translate(644,680)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 9.57 4 19.14 4 29 C2.68 29 1.36 29 0 29 C0 28.01 0 27.02 0 26 C-1.052 26.742 -1.052 26.742 -2.125 27.5 C-5.494 29.258 -7.251 29.547 -11 29 C-13.7 27.2 -15.542 25.916 -17 23 C-17.623 17.973 -17.776 14.213 -15 9.875 C-10.878 7.299 -8.815 7.465 -4 8 C-2.68 8.66 -1.36 9.32 0 10 C0 6.7 0 3.4 0 0 Z M-7 16 C-7.33 16.99 -7.66 17.98 -8 19 C-7.01 19.495 -7.01 19.495 -6 20 C-6.33 18.68 -6.66 17.36 -7 16 Z M-4 16 C-4.495 17.98 -4.495 17.98 -5 20 C-4.34 20 -3.68 20 -3 20 C-3.33 18.68 -3.66 17.36 -4 16 Z M-10 18 C-9 20 -9 20 -9 20 Z " fill="#245070" transform="translate(677,676)"/>
<path d="M0 0 C4 1 4 1 5 2 C5.99 1.34 6.98 0.68 8 0 C11.375 -0.438 11.375 -0.438 15 0 C18.847 3.178 20.833 5.266 21.312 10.25 C20.947 14.634 19.74 16.616 17 20 C13.873 21.564 10.42 21.4 7 21 C6.01 20.34 5.02 19.68 4 19 C4 22.63 4 26.26 4 30 C2.68 30 1.36 30 0 30 C0 20.1 0 10.2 0 0 Z M12 7 C13 9 13 9 13 9 Z M7 8 C8 12 8 12 8 12 Z " fill="#36627F" transform="translate(635,633)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 0.66 4.66 1.32 5 2 C5.99 1.34 6.98 0.68 8 0 C11.375 -0.438 11.375 -0.438 15 0 C18.847 3.178 20.833 5.266 21.312 10.25 C20.947 14.634 19.74 16.616 17 20 C13.873 21.564 10.42 21.4 7 21 C6.01 20.34 5.02 19.68 4 19 C4 22.63 4 26.26 4 30 C2.68 29.67 1.36 29.34 0 29 C0 19.43 0 9.86 0 0 Z M12 8 C12.33 9.32 12.66 10.64 13 12 C13.33 11.01 13.66 10.02 14 9 C13.34 8.67 12.68 8.34 12 8 Z M11 12 C11.33 12.66 11.66 13.32 12 14 C12.33 13.34 12.66 12.68 13 12 C12.34 12 11.68 12 11 12 Z " fill="#1C4266" transform="translate(487,633)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 9.57 4 19.14 4 29 C2.68 29 1.36 29 0 29 C-0.33 28.01 -0.66 27.02 -1 26 C-1.526 26.742 -1.526 26.742 -2.062 27.5 C-4 29 -4 29 -7.375 29.5 C-11.875 28.879 -13.189 27.473 -16 24 C-17.622 20.755 -17.552 17.507 -17 14 C-15.71 11.004 -14.677 10.185 -12 8 C-8.916 7.405 -6.08 7.371 -3 8 C-2.34 8.66 -1.68 9.32 -1 10 C-0.67 6.7 -0.34 3.4 0 0 Z M-4 17 C-3 20 -3 20 -3 20 Z M-10 18 C-9.67 19.32 -9.34 20.64 -9 22 C-7.68 21.67 -6.36 21.34 -5 21 C-5.66 20.01 -6.32 19.02 -7 18 C-7.99 18 -8.98 18 -10 18 Z " fill="#1E4463" transform="translate(497,676)"/>
<path d="M0 0 C1.794 -0.004 1.794 -0.004 3.625 -0.008 C6.553 0.107 9.158 0.447 12 1.125 C11.01 1.785 10.02 2.445 9 3.125 C9.639 3.723 10.279 4.321 10.938 4.938 C14.592 8.813 14.123 12.791 14.062 17.812 C14.058 18.514 14.053 19.216 14.049 19.939 C14.037 21.668 14.019 23.397 14 25.125 C12.35 25.125 10.7 25.125 9 25.125 C8.67 19.845 8.34 14.565 8 9.125 C4.815 8.848 4.815 8.848 2 11.125 C1.34 11.125 0.68 11.125 0 11.125 C0 12.115 0 13.105 0 14.125 C-0.66 14.125 -1.32 14.125 -2 14.125 C-2.33 19.075 -2.66 24.025 -3 29.125 C-5.31 29.125 -7.62 29.125 -10 29.125 C-10.66 29.455 -11.32 29.785 -12 30.125 C-12 29.465 -12 28.805 -12 28.125 C-14.64 28.125 -17.28 28.125 -20 28.125 C-20 27.795 -20 27.465 -20 27.125 C-14.72 27.125 -9.44 27.125 -4 27.125 C-4.33 26.135 -4.66 25.145 -5 24.125 C-6.65 24.455 -8.3 24.785 -10 25.125 C-9.984 24.141 -9.984 24.141 -9.968 23.137 C-9.927 20.175 -9.901 17.213 -9.875 14.25 C-9.858 13.217 -9.841 12.185 -9.824 11.121 C-9.818 10.135 -9.811 9.149 -9.805 8.133 C-9.789 6.766 -9.789 6.766 -9.773 5.372 C-9.781 2.922 -9.781 2.922 -12 1.125 C-7.95 0.159 -4.158 -0.009 0 0 Z " fill="#EDEFEE" transform="translate(399,679.875)"/>
<path d="M0 0 C3.507 0.031 6.051 0.682 9.25 2.188 C9.91 2.188 10.57 2.188 11.25 2.188 C11.25 8.457 11.25 14.727 11.25 21.188 C9.93 21.188 8.61 21.188 7.25 21.188 C6.92 20.528 6.59 19.867 6.25 19.188 C5.26 19.847 4.27 20.508 3.25 21.188 C-0.962 21.655 -3.036 21.653 -6.625 19.312 C-9.469 16.468 -9.954 14.799 -10.125 10.875 C-10.106 7.003 -9.899 6.37 -7.312 3.188 C-6.467 2.528 -5.621 1.867 -4.75 1.188 C-3.75 0.188 -3.75 0.188 0 0 Z M0.25 8.188 C1.25 10.188 1.25 10.188 1.25 10.188 Z M3.25 10.188 C4.25 12.188 4.25 12.188 4.25 12.188 Z M2.25 12.188 C3.25 14.188 3.25 14.188 3.25 14.188 Z " fill="#24506F" transform="translate(399.75,632.8125)"/>
<path d="M0 0 C0.619 0.959 0.619 0.959 1.25 1.938 C2.791 4.163 2.791 4.163 5.062 4.5 C7 5 7 5 8.312 6.812 C9.102 9.323 8.873 10.555 8 13 C7.34 12.67 6.68 12.34 6 12 C6 12.66 6 13.32 6 14 C4.35 14 2.7 14 1 14 C1.33 15.98 1.66 17.96 2 20 C3.65 20.33 5.3 20.66 7 21 C7.66 24.3 8.32 27.6 9 31 C7.577 31.464 7.577 31.464 6.125 31.938 C3.139 32.874 3.139 32.874 1 34 C1 33.34 1 32.68 1 32 C-0.072 31.897 -1.145 31.794 -2.25 31.688 C-5.888 31.021 -7.478 30.619 -10 28 C-10.75 25.812 -10.75 25.812 -11 24 C-9.35 23.67 -7.7 23.34 -6 23 C-6 19.04 -6 15.08 -6 11 C-6.99 11 -7.98 11 -9 11 C-9 10.01 -9 9.02 -9 8 C-8.01 8 -7.02 8 -6 8 C-6 6.02 -6 4.04 -6 2 C-4.35 2.33 -2.7 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#F1F2F0" transform="translate(575,676)"/>
<path d="M0 0 C0.741 0.044 1.482 0.088 2.246 0.133 C7.614 1.853 10.956 7.495 14.25 11.812 C14.679 12.374 15.107 12.935 15.549 13.514 C19.068 18.152 22.448 22.884 25.809 27.638 C28.664 31.66 31.606 35.6 34.625 39.5 C35.032 40.028 35.439 40.555 35.859 41.099 C37.528 43.259 39.2 45.416 40.892 47.559 C42.186 49.199 43.469 50.849 44.75 52.5 C45.507 53.462 46.263 54.423 47.043 55.414 C47.544 56.226 48.046 57.038 48.562 57.875 C48.232 58.865 47.903 59.855 47.562 60.875 C38.701 49.502 30.17 37.894 21.65 26.264 C20.38 24.531 19.109 22.797 17.837 21.065 C16.646 19.442 15.456 17.818 14.269 16.193 C9.279 8.572 9.279 8.572 2.562 2.875 C-3.805 2.378 -3.805 2.378 -9.438 4.875 C-12.098 8.677 -11.726 12.876 -11.712 17.328 C-11.72 18.604 -11.72 18.604 -11.728 19.906 C-11.742 22.718 -11.741 25.529 -11.738 28.34 C-11.742 30.295 -11.747 32.25 -11.751 34.206 C-11.759 38.304 -11.758 42.402 -11.753 46.5 C-11.747 51.75 -11.764 57 -11.788 62.25 C-11.802 66.288 -11.803 70.326 -11.8 74.364 C-11.801 76.3 -11.806 78.236 -11.816 80.171 C-11.828 82.879 -11.821 85.585 -11.81 88.293 C-11.817 89.091 -11.825 89.89 -11.833 90.713 C-11.79 95.269 -11.292 98.264 -8.438 101.875 C-6.112 103.038 -4.65 103.041 -2.062 103.062 C-1.292 103.077 -0.521 103.091 0.273 103.105 C2.781 102.853 4.395 102.127 6.562 100.875 C6.562 101.865 6.562 102.855 6.562 103.875 C2.833 106.361 -1.11 106.435 -5.438 105.875 C-9.116 104.24 -11.436 102.377 -13.438 98.875 C-14.452 94.177 -14.591 89.563 -14.599 84.778 C-14.604 83.988 -14.609 83.199 -14.614 82.385 C-14.628 79.789 -14.635 77.194 -14.641 74.598 C-14.646 72.785 -14.652 70.971 -14.658 69.158 C-14.668 65.363 -14.674 61.568 -14.678 57.773 C-14.683 52.92 -14.707 48.067 -14.736 43.213 C-14.754 39.47 -14.76 35.727 -14.761 31.984 C-14.764 30.196 -14.772 28.407 -14.785 26.618 C-14.802 24.112 -14.8 21.607 -14.794 19.102 C-14.808 18.004 -14.808 18.004 -14.822 16.883 C-14.776 11.141 -13.525 6.598 -10 2 C-6.929 -0.211 -3.678 -0.293 0 0 Z " fill="#BFCCD1" transform="translate(635.4375,221.125)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.65 4 3.3 4 5 C6.31 5 8.62 5 11 5 C11 5.99 11 6.98 11 8 C8.69 8 6.38 8 4 8 C4.141 9.959 4.288 11.917 4.438 13.875 C4.519 14.966 4.6 16.056 4.684 17.18 C4.83 19.947 4.83 19.947 6 22 C8.527 22.656 8.527 22.656 11 23 C11 23.99 11 24.98 11 26 C7.381 26.278 4.848 26.469 1.625 24.688 C-0.946 20.436 -0.832 15.886 -1 11 C-1.66 11 -2.32 11 -3 11 C-2.67 11.66 -2.34 12.32 -2 13 C-1.93 14.874 -1.916 16.75 -1.938 18.625 C-1.947 19.628 -1.956 20.631 -1.965 21.664 C-1.976 22.435 -1.988 23.206 -2 24 C-2.33 24 -2.66 24 -3 24 C-4.125 17.375 -4.125 17.375 -3 14 C-3.66 14 -4.32 14 -5 14 C-5.66 15.32 -6.32 16.64 -7 18 C-7.33 16.68 -7.66 15.36 -8 14 C-8 18.95 -8 23.9 -8 29 C-18.5 30.25 -18.5 30.25 -23 28 C-18.38 28 -13.76 28 -9 28 C-9.33 27.34 -9.66 26.68 -10 26 C-11.65 26 -13.3 26 -15 26 C-15 19.07 -15 12.14 -15 5 C-13.68 5 -12.36 5 -11 5 C-10.67 5.66 -10.34 6.32 -10 7 C-9.01 6.34 -8.02 5.68 -7 5 C-4.668 4.921 -2.332 4.912 0 5 C0 3.35 0 1.7 0 0 Z " fill="#E3E8E5" transform="translate(771,628)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.65 4 3.3 4 5 C5.98 5 7.96 5 10 5 C10 5.99 10 6.98 10 8 C8.02 8 6.04 8 4 8 C4.33 12.29 4.66 16.58 5 21 C5.99 21.33 6.98 21.66 8 22 C8.66 21.67 9.32 21.34 10 21 C8.68 20.67 7.36 20.34 6 20 C6 17.36 6 14.72 6 12 C8.31 12 10.62 12 13 12 C13 14.333 13 16.667 13 19 C13.295 21.298 13.629 23.588 13.977 25.879 C14 28 14 28 12 31 C12 30.34 12 29.68 12 29 C11.371 29.012 10.742 29.023 10.094 29.035 C9.238 29.044 8.382 29.053 7.5 29.062 C6.665 29.074 5.829 29.086 4.969 29.098 C1.296 28.977 -2.354 28.456 -6 28 C-6 22.06 -6 16.12 -6 10 C-4.68 10.33 -3.36 10.66 -2 11 C-1.67 10.34 -1.34 9.68 -1 9 C-2.32 9 -3.64 9 -5 9 C-5 7.68 -5 6.36 -5 5 C-3.35 5 -1.7 5 0 5 C0 3.35 0 1.7 0 0 Z M7 15 C7 16.32 7 17.64 7 19 C7.99 18.67 8.98 18.34 10 18 C9.34 18 8.68 18 8 18 C7.67 17.01 7.34 16.02 7 15 Z " fill="#E9ECEB" transform="translate(419,628)"/>
<path d="M0 0 C7.987 5.623 13.396 12.562 16 22 C17.098 34.21 15.889 43.653 7.875 53.312 C1.859 59.637 -5.358 63.567 -14.102 64.238 C-25.124 64.47 -33.052 62.684 -41.18 54.984 C-49.054 46.4 -51.503 38.528 -51.242 27.02 C-50.442 17.045 -46.352 9.992 -39.082 3.211 C-27.872 -5.873 -12.688 -6.865 0 0 Z M-42.586 8.828 C-47.91 15.84 -50.181 24.209 -49.906 32.992 C-48.517 42.422 -44.691 50.563 -37.16 56.531 C-29.499 61.74 -21.427 63.923 -12.172 62.594 C-3.19 60.486 4.451 56.1 9.711 48.387 C14.413 39.681 16.348 30.684 14 21 C10.364 10.305 4.143 4.854 -5.188 -1 C-18.908 -4.599 -33.118 -2.086 -42.586 8.828 Z " fill="#D3DCE1" transform="translate(472,398)"/>
<path d="M0 0 C8.956 5.456 14.763 12.348 17.984 22.324 C20.073 33.572 18.299 43.271 12.129 52.699 C7.355 59.155 1.428 63.863 -6.621 65.449 C-17.485 66.547 -26.478 66.138 -35.621 59.449 C-43.573 52.571 -47.817 43.784 -48.824 33.344 C-49.046 23.472 -46.013 15 -39.621 7.449 C-29.101 -2.709 -13.54 -6.357 0 0 Z M-37.621 7.449 C-43.947 15.349 -47.274 23.752 -46.906 33.941 C-46.06 41.383 -43.512 48.67 -38.621 54.449 C-37.961 54.449 -37.301 54.449 -36.621 54.449 C-36.291 55.439 -35.961 56.429 -35.621 57.449 C-26.773 63.398 -17.967 65.422 -7.496 63.887 C0.857 61.898 7.614 56.494 12.379 49.449 C16.977 41.948 18.26 33.39 16.973 24.711 C14.443 15.03 8.954 7.565 0.379 2.449 C-10.997 -4.21 -28.428 -1.999 -37.621 7.449 Z " fill="#D0D9DE" transform="translate(592.62109375,396.55078125)"/>
<path d="M0 0 C2.81 1.794 4.509 3.019 6 6 C6.555 10.632 6.636 13.982 4.125 18 C0.781 21.148 -1.459 21.231 -6.035 21.148 C-8.544 20.959 -10.003 20.553 -12 19 C-15.153 15.059 -15.533 11.97 -15 7 C-13.76 4.211 -12.715 2.556 -10.312 0.688 C-6.848 -0.343 -3.549 -0.591 0 0 Z M-7 7 C-7.33 7.99 -7.66 8.98 -8 10 C-7.34 10 -6.68 10 -6 10 C-6.33 9.01 -6.66 8.02 -7 7 Z M-5 7 C-4.67 8.65 -4.34 10.3 -4 12 C-3.34 10.68 -2.68 9.36 -2 8 C-2.99 7.67 -3.98 7.34 -5 7 Z M-5 12 C-5.66 12.66 -6.32 13.32 -7 14 C-6.01 14 -5.02 14 -4 14 C-4.33 13.34 -4.66 12.68 -5 12 Z " fill="#194165" transform="translate(802,633)"/>
<path d="M0 0 C1.691 0.09 3.38 0.246 5.062 0.438 C6.441 0.59 6.441 0.59 7.848 0.746 C8.558 0.83 9.268 0.914 10 1 C10.338 1.82 10.675 2.64 11.023 3.484 C11.469 4.562 11.915 5.64 12.375 6.75 C12.816 7.817 13.257 8.885 13.711 9.984 C14.733 12.376 15.802 14.694 17 17 C17.381 16.151 17.381 16.151 17.77 15.285 C18.114 14.552 18.458 13.818 18.812 13.062 C19.317 11.966 19.317 11.966 19.832 10.848 C21 9 21 9 24 8 C23.386 12.548 22.211 16.533 20.562 20.812 C19.915 22.524 19.915 22.524 19.254 24.27 C18 27 18 27 16 28 C14.661 24.377 13.33 20.751 12 17.125 C11.618 16.092 11.237 15.06 10.844 13.996 C10.483 13.01 10.122 12.024 9.75 11.008 C9.415 10.097 9.08 9.185 8.734 8.247 C8 6 8 6 8 4 C6.68 4 5.36 4 4 4 C4.454 5.158 4.454 5.158 4.918 6.339 C6.284 9.828 7.642 13.32 9 16.812 C9.477 18.027 9.954 19.242 10.445 20.494 C10.896 21.658 11.348 22.821 11.812 24.02 C12.231 25.093 12.65 26.167 13.082 27.273 C14 30 14 30 14 33 C15.65 33 17.3 33 19 33 C19.17 32.397 19.34 31.793 19.516 31.172 C20.935 26.489 22.032 23.147 26 20 C25.492 25.966 23.614 30.638 21 36 C18.03 36 15.06 36 12 36 C11.587 34.886 11.175 33.773 10.75 32.625 C9.645 30.007 9.251 29.165 6.812 27.562 C6.214 27.377 5.616 27.191 5 27 C5.99 27 6.98 27 8 27 C7.34 26.01 6.68 25.02 6 24 C5.163 21.488 4.382 18.974 3.625 16.438 C3.363 15.562 3.102 14.687 2.832 13.785 C2.557 12.866 2.283 11.947 2 11 C1.438 9.125 0.875 7.25 0.312 5.375 C-0.125 3.917 -0.562 2.458 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#EEF0EF" transform="translate(347,672)"/>
<path d="M0 0 C3.33 2.65 4.601 4.12 5.473 8.227 C5.76 12.63 5.429 14.458 2.625 18 C-0.987 20.752 -3.339 21.473 -7.961 21.406 C-10.883 20.824 -12.42 19.181 -14.09 16.781 C-15.619 13.788 -16 11.343 -16 8 C-12.866 0.742 -7.628 -1.629 0 0 Z M-8 7 C-8 8.98 -8 10.96 -8 13 C-7.01 13 -6.02 13 -5 13 C-5 11.35 -5 9.7 -5 8 C-5.99 7.67 -6.98 7.34 -8 7 Z M-3 9 C-3.33 9.99 -3.66 10.98 -4 12 C-3.01 12.495 -3.01 12.495 -2 13 C-2.33 11.68 -2.66 10.36 -3 9 Z " fill="#163C5D" transform="translate(629,684)"/>
<path d="M0 0 C3.253 1.344 4.578 2.256 6.312 5.312 C7.294 9.148 7.508 12.383 6.312 16.188 C4.128 19.204 1.774 20.702 -1.93 21.324 C-5.795 21.536 -7.596 21.258 -10.938 19.125 C-14.343 15.616 -14.331 12.982 -14.293 8.262 C-13.863 4.946 -12.391 3.25 -10 1 C-6.804 -0.598 -3.477 -0.367 0 0 Z M-6 7 C-6.33 7.66 -6.66 8.32 -7 9 C-6.34 9 -5.68 9 -5 9 C-5.33 8.34 -5.66 7.68 -6 7 Z M-3 7 C-4.562 8.312 -4.562 8.312 -6 10 C-6 10.99 -6 11.98 -6 13 C-4.02 13.495 -4.02 13.495 -2 14 C-1.34 12.68 -0.68 11.36 0 10 C-0.99 9.01 -1.98 8.02 -3 7 Z " fill="#13395C" transform="translate(525,633)"/>
<path d="M0 0 C4.6 2.2 4.6 2.2 6 5 C6.229 7.037 6.41 9.08 6.562 11.125 C6.688 12.769 6.688 12.769 6.816 14.445 C6.877 15.288 6.938 16.131 7 17 C3.021 20.183 0.144 21.762 -5.031 21.434 C-7.768 20.831 -9.903 19.696 -11.93 17.758 C-14.158 14.098 -13.804 10.08 -13 6 C-9.454 0.208 -6.632 -0.45 0 0 Z M-5 9 C-5.33 9.66 -5.66 10.32 -6 11 C-5.34 11 -4.68 11 -4 11 C-4.33 10.34 -4.66 9.68 -5 9 Z M-3 9 C-2.67 9.66 -2.34 10.32 -2 11 C-1.67 10.34 -1.34 9.68 -1 9 C-1.66 9 -2.32 9 -3 9 Z " fill="#EFF2F2" transform="translate(672,633)"/>
<path d="M0 0 C7.922 5.235 13.952 12.401 16.523 21.586 C18.406 31.634 16.825 40.945 11.211 49.461 C6.103 56.843 -0.845 61.783 -9.789 63.461 C-20.812 64.372 -28.991 62.881 -37.602 55.773 C-43.773 49.929 -47.77 43.035 -48.027 34.449 C-48.314 22.413 -47.228 14.381 -38.793 5.254 C-28.731 -3.749 -12.243 -5.892 0 0 Z M-36.789 5.461 C-36.789 6.121 -36.789 6.781 -36.789 7.461 C-37.779 7.791 -38.769 8.121 -39.789 8.461 C-45.721 16.664 -47.677 25.485 -46.789 35.461 C-45.261 43.322 -41.106 50.144 -35.477 55.773 C-27.777 60.608 -20.205 63.405 -11.027 62.055 C-0.731 59.642 6.134 54.786 11.836 45.891 C16.129 38.304 16.668 28.896 14.418 20.504 C12.87 16.452 11.469 13.392 8.211 10.461 C7.551 10.791 6.891 11.121 6.211 11.461 C6.149 10.718 6.087 9.976 6.023 9.211 C4.93 5.509 3.421 4.555 0.211 2.461 C-11.809 -3.549 -26.156 -3.213 -36.789 5.461 Z " fill="#DAE2E5" transform="translate(397.7890625,244.5390625)"/>
<path d="M0 0 C0.857 -0.017 1.714 -0.034 2.598 -0.051 C5.415 0.387 6.244 1.06 7.938 3.312 C9.144 6.931 9.046 10.238 9 14 C8.995 14.702 8.991 15.404 8.986 16.127 C8.975 17.856 8.957 19.584 8.938 21.312 C7 20.938 7 20.938 4.938 20.312 C4.607 19.653 4.278 18.992 3.938 18.312 C3.401 18.808 2.865 19.303 2.312 19.812 C-0.764 21.755 -2.481 21.835 -6.062 21.312 C-8.848 19.334 -9.873 18.153 -10.625 14.812 C-9.824 11.253 -8.055 10.261 -5.062 8.312 C-1.589 7.155 1.298 7.244 4.938 7.312 C4.278 6.322 3.617 5.332 2.938 4.312 C-0.914 4.027 -4.372 4.132 -8.062 5.312 C-8.393 4.322 -8.722 3.332 -9.062 2.312 C-5.944 0.497 -3.597 0.016 0 0 Z " fill="#173A5D" transform="translate(532.0625,683.6875)"/>
<path d="M0 0 C2.502 1.876 3.619 3.239 5 6 C5.072 8.552 5.093 11.075 5.062 13.625 C5.058 14.331 5.053 15.038 5.049 15.766 C5.037 17.51 5.019 19.255 5 21 C3.68 21 2.36 21 1 21 C0.67 20.01 0.34 19.02 0 18 C-0.536 18.495 -1.072 18.99 -1.625 19.5 C-4.701 21.443 -6.419 21.522 -10 21 C-12.438 19.25 -12.438 19.25 -14 17 C-14 14.036 -13.029 13.032 -11.062 10.812 C-7.09 7.322 -4.119 7.917 1 8 C0.67 6.68 0.34 5.36 0 4 C-4.474 3.911 -8.606 4.143 -13 5 C-10.312 -0.951 -6.096 -0.813 0 0 Z " fill="#21486A" transform="translate(564,633)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 2.98 2 4.96 2 7 C2.857 6.893 3.714 6.786 4.598 6.676 C17.084 5.367 17.084 5.367 22 9 C26.862 16.156 24.825 25.901 24 34 C16.25 35.125 16.25 35.125 14 34 C13.586 31.934 13.586 31.934 13.375 29.438 C13.3 28.611 13.225 27.785 13.148 26.934 C13.099 26.296 13.05 25.657 13 25 C12.01 25 11.02 25 10 25 C10 26.98 10 28.96 10 31 C9.34 31.33 8.68 31.66 8 32 C7.832 29.729 7.666 27.458 7.5 25.188 C7.361 23.291 7.361 23.291 7.219 21.355 C7.081 19.24 7 17.12 7 15 C10.224 12.851 11.267 12.617 15 13 C17 15 17 15 17.195 18.695 C17.182 20.172 17.158 21.649 17.125 23.125 C17.116 23.879 17.107 24.633 17.098 25.41 C17.074 27.274 17.038 29.137 17 31 C18.32 31 19.64 31 21 31 C20.913 28.228 20.806 25.459 20.688 22.688 C20.665 21.902 20.642 21.116 20.619 20.307 C20.566 19.171 20.566 19.171 20.512 18.012 C20.486 17.315 20.459 16.619 20.432 15.901 C19.879 13.468 18.933 12.536 17 11 C14.946 10.639 14.946 10.639 12.812 10.812 C11.554 10.874 10.296 10.936 9 11 C9 10.34 9 9.68 9 9 C6.03 8.67 3.06 8.34 0 8 C0 5.36 0 2.72 0 0 Z " fill="#ECEEEC" transform="translate(450,623)"/>
<path d="M0 0 C2.929 2.856 5.654 5.666 8.059 8.977 C8.602 9.72 9.146 10.463 9.706 11.229 C10.277 12.02 10.849 12.81 11.438 13.625 C12.684 15.332 13.93 17.039 15.176 18.746 C15.81 19.617 16.443 20.489 17.096 21.386 C21.8 27.844 26.549 34.267 31.296 40.692 C32.443 42.245 33.588 43.799 34.731 45.354 C36.378 47.594 38.032 49.829 39.688 52.062 C40.181 52.736 40.674 53.409 41.182 54.102 C43.308 56.959 45.01 59.006 48 61 C52.59 61.659 56.672 61.963 60.5 59.188 C61.68 57.467 62.843 55.736 64 54 C64.33 54.99 64.66 55.98 65 57 C60.895 62.702 60.895 62.702 57 64 C51.635 64.346 48.298 64.527 44 61 C38.549 55.619 34.269 49.289 29.875 43.051 C17.391 24.241 17.391 24.241 3 7 C2.996 7.605 2.993 8.21 2.989 8.833 C2.947 15.128 2.878 21.421 2.792 27.715 C2.764 30.065 2.743 32.414 2.729 34.763 C2.708 38.139 2.661 41.515 2.609 44.891 C2.608 46.467 2.608 46.467 2.606 48.076 C2.459 55.394 2.459 55.394 -0.038 58.694 C-1.009 59.341 -1.009 59.341 -2 60 C-1.839 59.455 -1.678 58.911 -1.513 58.35 C-0.924 55.652 -0.838 53.167 -0.795 50.405 C-0.765 48.724 -0.765 48.724 -0.734 47.009 C-0.718 45.804 -0.701 44.6 -0.684 43.359 C-0.652 41.503 -0.652 41.503 -0.621 39.61 C-0.566 36.329 -0.516 33.047 -0.468 29.766 C-0.417 26.414 -0.362 23.063 -0.307 19.711 C-0.2 13.141 -0.098 6.57 0 0 Z " fill="#DEE4E8" transform="translate(644,263)"/>
<path d="M0 0 C0.994 0.102 1.988 0.204 3.012 0.309 C3.771 0.392 4.53 0.476 5.312 0.562 C3.993 0.893 2.673 1.222 1.312 1.562 C1.312 8.822 1.312 16.082 1.312 23.562 C5.284 22.934 5.284 22.934 7.312 20.562 C7.312 22.212 7.312 23.862 7.312 25.562 C2.033 25.562 -3.248 25.562 -8.688 25.562 C-8.688 17.312 -8.688 9.062 -8.688 0.562 C-5.376 -0.541 -3.44 -0.364 0 0 Z " fill="#FBFBFA" transform="translate(387.6875,681.4375)"/>
<path d="M0 0 C2.234 1.477 2.234 1.477 4 4 C4.48 6.934 4.378 9.787 4.25 12.75 C4.232 13.543 4.214 14.336 4.195 15.152 C4.148 17.102 4.077 19.051 4 21 C3.01 21 2.02 21 1 21 C1 20.01 1 19.02 1 18 C0.319 18.495 -0.361 18.99 -1.062 19.5 C-4.672 21.343 -6.994 21.445 -11 21 C-12.938 19.875 -12.938 19.875 -14 18 C-14.5 15 -14.5 15 -14 12 C-11.625 10.188 -11.625 10.188 -9 9 C-8.34 8.67 -7.68 8.34 -7 8 C-4.667 7.96 -2.333 7.957 0 8 C-0.66 6.68 -1.32 5.36 -2 4 C-5.522 3.92 -8.62 3.96 -12 5 C-12 4.01 -12 3.02 -12 2 C-8.41 -0.394 -4.177 -0.963 0 0 Z " fill="#224D6E" transform="translate(337,633)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C4.173 3.519 3.746 4.228 2.562 7.625 C-1.783 22.725 1.14 37.532 8.34 51.207 C15.153 62.67 25.571 68.79 38 73 C53.337 76.378 69.622 74.309 83 66 C85.809 63.803 88.44 61.481 91 59 C91.66 59.66 92.32 60.32 93 61 C79.785 72.753 66.255 77.545 48.738 77.246 C33.92 76.271 21.075 69.842 11 59 C6.406 53.332 1 46.595 1 39 C0.34 38.67 -0.32 38.34 -1 38 C-1.729 34.721 -1.813 31.348 -2 28 C-2.054 27.051 -2.108 26.103 -2.164 25.125 C-2.399 17.091 -1.649 8.298 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#CFD9DE" transform="translate(329,252)"/>
<path d="M0 0 C0.919 0.001 1.837 0.002 2.783 0.003 C13.05 0.18 21.737 2.622 30.875 7.375 C27.259 8.694 25.392 8.044 21.938 6.5 C14.821 3.354 8.376 3.164 0.688 3.125 C-0.798 3.094 -0.798 3.094 -2.314 3.062 C-15.835 2.992 -28.149 7.77 -38.023 17.086 C-47.976 27.927 -51.444 39.518 -51.562 53.938 C-51.573 54.895 -51.583 55.852 -51.594 56.839 C-51.509 63.731 -50.221 69.82 -48.125 76.375 C-49.115 76.87 -49.115 76.87 -50.125 77.375 C-53.433 69.416 -54.532 62.239 -54.562 53.688 C-54.572 52.822 -54.582 51.956 -54.592 51.064 C-54.409 36.846 -48.137 24.804 -38.48 14.598 C-27.757 4.479 -14.56 -0.025 0 0 Z " fill="#CDD7DB" transform="translate(272.125,219.625)"/>
<path d="M0 0 C2.677 0.868 3.657 1.503 5.25 3.812 C6 6 6 6 6 11 C1.05 11 -3.9 11 -9 11 C-8.01 12.98 -7.02 14.96 -6 17 C-2.583 17.167 -2.583 17.167 1 17 C1.66 16.34 2.32 15.68 3 15 C3.99 15 4.98 15 6 15 C4.63 18.161 4.011 18.993 1 21 C-2.017 21.341 -4.094 21.572 -7 21 C-9.691 18.824 -11.455 17.089 -13 14 C-13.13 9.265 -12.598 5.861 -9.875 1.938 C-6.259 -0.499 -4.285 -0.545 0 0 Z " fill="#14375C" transform="translate(606,633)"/>
<path d="M0 0 C2.969 1.39 4.697 2.697 7 5 C9.211 5.243 9.211 5.243 11.625 5.125 C12.442 5.107 13.26 5.089 14.102 5.07 C14.728 5.047 15.355 5.024 16 5 C16 5.66 16 6.32 16 7 C13.36 7 10.72 7 8 7 C8.66 7.66 9.32 8.32 10 9 C10.195 12.258 10.195 12.258 10.125 16.125 C10.107 17.406 10.089 18.688 10.07 20.008 C10.047 20.995 10.024 21.983 10 23 C9.01 22.34 8.02 21.68 7 21 C6.67 21.66 6.34 22.32 6 23 C6.66 23 7.32 23 8 23 C8.66 25.64 9.32 28.28 10 31 C8.68 31 7.36 31 6 31 C3.635 31.955 1.401 33.081 -0.879 34.223 C-3 35 -3 35 -6 34 C-6 33.01 -6 32.02 -6 31 C-5.362 30.807 -4.724 30.613 -4.066 30.414 C-3.24 30.154 -2.414 29.893 -1.562 29.625 C-0.739 29.37 0.085 29.115 0.934 28.852 C3.178 28.191 3.178 28.191 4 26 C4.32 19.709 4.32 19.709 2 14 C2.33 13.34 2.66 12.68 3 12 C3.333 9 3.333 9 3 6 C-0.75 3.5 -2.559 3.464 -7 4 C-10.16 6.541 -11.715 8.146 -13 12 C-14.32 11.67 -15.64 11.34 -17 11 C-13.8 2.243 -9.606 -1.482 0 0 Z " fill="#E9EDEC" transform="translate(226,624)"/>
<path d="M0 0 C2.31 0.66 4.62 1.32 7 2 C6.34 2.66 5.68 3.32 5 4 C5 4.99 5 5.98 5 7 C4.01 7.495 4.01 7.495 3 8 C3 7.01 3 6.02 3 5 C1.68 5 0.36 5 -1 5 C-1 14.57 -1 24.14 -1 34 C0.32 34.33 1.64 34.66 3 35 C3 31.37 3 27.74 3 24 C3.99 24.33 4.98 24.66 6 25 C6 25.99 6 26.98 6 28 C6.66 28.33 7.32 28.66 8 29 C8 29.66 8 30.32 8 31 C7.34 31 6.68 31 6 31 C6.062 32.176 6.062 32.176 6.125 33.375 C6 36 6 36 4 38 C-2.323 38.339 -2.323 38.339 -5 37 C-6.575 34.374 -7 33.129 -7 30 C-6.67 29.67 -6.34 29.34 -6 29 C-5.914 27.657 -5.893 26.31 -5.902 24.965 C-5.906 24.156 -5.909 23.347 -5.912 22.514 C-5.92 21.664 -5.929 20.813 -5.938 19.938 C-5.944 18.656 -5.944 18.656 -5.951 17.35 C-5.963 15.233 -5.981 13.117 -6 11 C-6.99 11.495 -6.99 11.495 -8 12 C-7.493 8.281 -6.884 5.265 -5 2 C-3.35 2 -1.7 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E0E3E1" transform="translate(488,628)"/>
<path d="M0 0 C2.938 2.125 2.938 2.125 5 5 C5 6.98 5 8.96 5 11 C0.05 11 -4.9 11 -10 11 C-9.34 12.98 -8.68 14.96 -8 17 C-1.916 17.198 -1.916 17.198 4 16 C3.625 17.938 3.625 17.938 3 20 C-0.495 21.747 -4.187 21.552 -8 21 C-11.062 19.125 -11.062 19.125 -13 16 C-13.913 11.026 -13.787 7.285 -11.125 2.938 C-7.477 -0.389 -4.903 -0.499 0 0 Z " fill="#0B3057" transform="translate(728,633)"/>
<path d="M0 0 C-0.188 1.875 -0.188 1.875 -1 4 C-2.667 4.667 -4.333 5.333 -6 6 C-6.887 8.041 -6.887 8.041 -7 10 C-6.34 10.33 -5.68 10.66 -5 11 C-5 9.68 -5 8.36 -5 7 C-3.02 6.67 -1.04 6.34 1 6 C0.67 8.31 0.34 10.62 0 13 C0.66 13 1.32 13 2 13 C2.309 13.969 2.619 14.939 2.938 15.938 C3.695 18.841 3.695 18.841 5 20 C9.55 20.262 12.884 19.995 17 18 C17.33 19.65 17.66 21.3 18 23 C21.96 23 25.92 23 30 23 C30 21.02 30 19.04 30 17 C31.65 18.65 33.3 20.3 35 22 C30.29 24.692 25.689 24.531 20.375 24.75 C19.428 24.805 18.48 24.861 17.504 24.918 C11.047 25.209 5.269 24.565 -1 23 C-1 20.36 -1 17.72 -1 15 C-1.99 15 -2.98 15 -4 15 C-4 14.34 -4 13.68 -4 13 C-5.65 13.66 -7.3 14.32 -9 15 C-9.33 17.64 -9.66 20.28 -10 23 C-10.33 23 -10.66 23 -11 23 C-11.027 21.042 -11.046 19.083 -11.062 17.125 C-11.074 16.034 -11.086 14.944 -11.098 13.82 C-11 11 -11 11 -10 9 C-10.66 8.67 -11.32 8.34 -12 8 C-8.582 2.579 -6.689 -0.362 0 0 Z " fill="#F0F3F2" transform="translate(321,633)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.006 1.091 2.012 2.183 2.018 3.307 C2.075 13.592 2.148 23.877 2.236 34.162 C2.28 39.45 2.32 44.737 2.346 50.025 C2.372 55.128 2.413 60.231 2.463 65.334 C2.48 67.28 2.491 69.227 2.498 71.174 C2.507 73.901 2.535 76.627 2.568 79.354 C2.566 80.159 2.565 80.964 2.563 81.793 C2.64 86.363 3.124 89.389 6 93 C9.693 95.462 12.713 95.57 17 95 C20.236 93.257 20.824 92.528 22 89 C22.66 89 23.32 89 24 89 C23.454 92.819 22.054 94.669 19 97 C13.796 98.375 9.967 98.062 5 96 C0.174 90.92 -0.143 87.374 -0.114 80.603 C-0.114 79.735 -0.114 78.866 -0.114 77.972 C-0.113 75.093 -0.105 72.215 -0.098 69.336 C-0.096 67.343 -0.094 65.351 -0.093 63.358 C-0.09 58.107 -0.08 52.857 -0.069 47.606 C-0.058 42.25 -0.054 36.895 -0.049 31.539 C-0.038 21.026 -0.021 10.513 0 0 Z " fill="#DFE5E8" transform="translate(643,385)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.237 0.626 4.474 1.251 4.719 1.896 C5.79 4.723 6.864 7.549 7.938 10.375 C8.311 11.36 8.684 12.345 9.068 13.359 C9.426 14.3 9.784 15.241 10.152 16.211 C10.482 17.08 10.812 17.95 11.152 18.845 C11.964 21.002 11.964 21.002 13 23 C13.398 21.926 13.797 20.852 14.207 19.746 C14.742 18.31 15.277 16.874 15.812 15.438 C16.204 14.381 16.204 14.381 16.604 13.303 C18.281 8.809 20.077 4.394 22 0 C23.32 0 24.64 0 26 0 C23.358 10.071 19.129 19.469 15 29 C13.35 29 11.7 29 10 29 C8.572 25.193 7.148 21.384 5.728 17.574 C5.244 16.278 4.759 14.982 4.272 13.687 C3.574 11.826 2.881 9.964 2.188 8.102 C1.769 6.981 1.35 5.86 0.918 4.706 C0 2 0 2 0 0 Z " fill="#113255" transform="translate(351,676)"/>
<path d="M0 0 C2.438 1.125 2.438 1.125 4 3 C4.32 6.01 4.184 8.337 3.375 11.25 C3.251 11.827 3.127 12.405 3 13 C3.66 13.66 4.32 14.32 5 15 C5.663 21.848 5.663 21.848 3.562 25.5 C-0.273 27.745 -3.672 27.87 -8 27 C-11.062 25 -11.062 25 -13 22 C-13.394 19.193 -13.242 16.858 -13 14 C-12.01 13.67 -11.02 13.34 -10 13 C-9.867 13.638 -9.734 14.276 -9.598 14.934 C-9.421 15.76 -9.244 16.586 -9.062 17.438 C-8.888 18.261 -8.714 19.085 -8.535 19.934 C-8.192 21.977 -8.192 21.977 -7 23 C-4 23.167 -4 23.167 -1 23 C0.416 21.816 0.416 21.816 0.062 18.438 C0.042 17.303 0.021 16.169 0 15 C-3.651 13.389 -7.051 12.551 -11 12 C-11 11.67 -11 11.34 -11 11 C-7.7 10.34 -4.4 9.68 -1 9 C-1 7.02 -1 5.04 -1 3 C-6.719 4.281 -6.719 4.281 -11 8 C-11.33 6.68 -11.66 5.36 -12 4 C-8.028 0.149 -5.507 -0.701 0 0 Z " fill="#235172" transform="translate(226,627)"/>
<path d="M0 0 C0.33 1.98 0.66 3.96 1 6 C-0.98 6.66 -2.96 7.32 -5 8 C-2.69 9.32 -0.38 10.64 2 12 C2.125 15.375 2.125 15.375 2 19 C0 21 0 21 -2.938 21.375 C-6 21 -6 21 -7.875 19.438 C-9.37 16.197 -9.203 13.524 -9 10 C-9.99 10.33 -10.98 10.66 -12 11 C-14.262 10.66 -14.262 10.66 -16.688 10.062 C-17.496 9.868 -18.304 9.673 -19.137 9.473 C-19.752 9.317 -20.366 9.161 -21 9 C-21 8.34 -21 7.68 -21 7 C-16.545 6.505 -16.545 6.505 -12 6 C-11.67 5.01 -11.34 4.02 -11 3 C-7.287 0.136 -4.634 -0.323 0 0 Z " fill="#3F6E8A" transform="translate(225,630)"/>
<path d="M0 0 C2.312 1.5 2.312 1.5 4 3 C3.01 3 2.02 3 1 3 C1.33 3.66 1.66 4.32 2 5 C4.025 5.652 4.025 5.652 6 6 C6 5.01 6 4.02 6 3 C7.32 3.33 8.64 3.66 10 4 C9.988 4.664 9.977 5.328 9.965 6.012 C9.881 12.563 9.881 12.563 11 19 C8.291 20.354 5.991 20.065 3 20 C3.825 19.423 4.65 18.845 5.5 18.25 C6.325 17.508 7.15 16.765 8 16 C8 14.68 8 13.36 8 12 C7.34 12 6.68 12 6 12 C5.67 12.66 5.34 13.32 5 14 C3.68 14 2.36 14 1 14 C0.67 14.33 0.34 14.66 0 15 C-2.875 15.312 -2.875 15.312 -6 15 C-8.795 12.671 -8.985 11.12 -9.438 7.5 C-9 4 -9 4 -7.312 1.562 C-4.528 -0.319 -3.295 -0.577 0 0 Z " fill="#E3E6E2" transform="translate(366,636)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 17.82 1 35.64 1 54 C0.67 54 0.34 54 0 54 C-0.937 48.476 -1.132 43.163 -1.098 37.566 C-1.096 36.688 -1.095 35.811 -1.093 34.906 C-1.088 32.125 -1.075 29.344 -1.062 26.562 C-1.057 24.667 -1.053 22.772 -1.049 20.877 C-1.038 16.251 -1.021 11.626 -1 7 C-8.997 21.19 -8.997 21.19 -16.223 35.783 C-18.559 40.667 -21.271 44.977 -26.5 46.938 C-29.614 47.015 -31.281 46.534 -34 45 C-36.884 41.709 -39.019 38.364 -41.016 34.48 C-41.544 33.459 -42.073 32.437 -42.617 31.385 C-43.156 30.33 -43.695 29.275 -44.25 28.188 C-45.081 26.576 -45.081 26.576 -45.93 24.932 C-47.291 22.29 -48.648 19.646 -50 17 C-49.01 17 -48.02 17 -47 17 C-46.693 17.64 -46.386 18.281 -46.07 18.94 C-44.661 21.861 -43.237 24.774 -41.812 27.688 C-41.33 28.695 -40.847 29.702 -40.35 30.74 C-36.759 38.228 -36.759 38.228 -31 44 C-28.55 44.392 -28.55 44.392 -26.188 43.812 C-25.397 43.675 -24.607 43.537 -23.793 43.395 C-23.201 43.264 -22.61 43.134 -22 43 C-21.786 42.237 -21.572 41.474 -21.352 40.688 C-19.469 35.551 -16.929 30.832 -14.375 26 C-13.841 24.983 -13.307 23.966 -12.757 22.918 C-8.673 15.178 -4.443 7.54 0 0 Z " fill="#D7DFE4" transform="translate(531,261)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 0.99 4.66 1.98 5 3 C5.66 2.34 6.32 1.68 7 1 C13.975 0.325 13.975 0.325 17.242 2.477 C19.818 6.174 19.439 9.361 19.25 13.75 C19.232 14.543 19.214 15.336 19.195 16.152 C19.148 18.102 19.077 20.051 19 22 C17.68 22 16.36 22 15 22 C14.951 20.94 14.902 19.881 14.852 18.789 C14.777 17.401 14.701 16.013 14.625 14.625 C14.594 13.926 14.563 13.228 14.531 12.508 C14.405 8.298 14.405 8.298 12 5 C8.174 5.188 8.174 5.188 5 7 C4.763 9.526 4.578 12.031 4.438 14.562 C4.394 15.273 4.351 15.984 4.307 16.717 C4.2 18.478 4.1 20.239 4 22 C2.68 22 1.36 22 0 22 C0 14.74 0 7.48 0 0 Z " fill="#234766" transform="translate(457,683)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.061 1.143 4.121 2.287 4.184 3.465 C4.268 4.956 4.353 6.447 4.438 7.938 C4.477 8.692 4.516 9.447 4.557 10.225 C4.44 13.919 4.44 13.919 6 17 C8.415 17.417 8.415 17.417 11 17 C14.305 13.743 14.462 10.761 14.688 6.25 C14.753 5.08 14.819 3.909 14.887 2.703 C14.924 1.811 14.961 0.919 15 0 C16.32 0 17.64 0 19 0 C19 6.93 19 13.86 19 21 C17.68 21 16.36 21 15 21 C14.67 20.34 14.34 19.68 14 19 C13.01 19.66 12.02 20.32 11 21 C9.062 21.336 9.062 21.336 7 21.375 C6.319 21.403 5.639 21.432 4.938 21.461 C3 21 3 21 1.262 19.523 C-0.607 15.786 -0.321 12.363 -0.188 8.25 C-0.174 7.457 -0.16 6.664 -0.146 5.848 C-0.111 3.898 -0.057 1.949 0 0 Z " fill="#14395C" transform="translate(236,633)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 1.66 3.34 2.32 3 3 C3.495 2.505 3.99 2.01 4.5 1.5 C7.878 -0.527 11.151 -0.955 15 0 C17.234 1.48 17.234 1.48 19 4 C19.48 6.934 19.378 9.787 19.25 12.75 C19.232 13.543 19.214 14.336 19.195 15.152 C19.148 17.102 19.077 19.051 19 21 C17.68 21 16.36 21 15 21 C14.67 15.39 14.34 9.78 14 4 C8.515 3.502 8.515 3.502 6.168 5.051 C4.389 8.02 4.47 10.761 4.312 14.188 C4.247 15.46 4.181 16.732 4.113 18.043 C4.076 19.019 4.039 19.995 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#194365" transform="translate(812,633)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.99 3 1.98 3 3 C3.681 2.505 4.361 2.01 5.062 1.5 C8.708 -0.361 10.954 -0.515 15 0 C17.199 1.48 17.199 1.48 19 4 C19.483 6.934 19.378 9.787 19.25 12.75 C19.232 13.543 19.214 14.336 19.195 15.152 C19.148 17.102 19.077 19.051 19 21 C17.68 21 16.36 21 15 21 C14.939 19.857 14.879 18.713 14.816 17.535 C14.732 16.044 14.647 14.553 14.562 13.062 C14.523 12.308 14.484 11.553 14.443 10.775 C14.56 7.081 14.56 7.081 13 4 C8.659 4.142 8.659 4.142 5 6 C4.763 8.526 4.578 11.031 4.438 13.562 C4.394 14.273 4.351 14.984 4.307 15.717 C4.2 17.478 4.1 19.239 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#12395E" transform="translate(452,633)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.34 1 2.68 1 2 1 C2 2.98 2 4.96 2 7 C1.06 7.199 1.06 7.199 0.102 7.402 C-0.716 7.579 -1.533 7.756 -2.375 7.938 C-3.187 8.112 -3.999 8.286 -4.836 8.465 C-7.029 8.923 -7.029 8.923 -9 10 C-9 9.01 -9 8.02 -9 7 C-10.32 7 -11.64 7 -13 7 C-13 13.93 -13 20.86 -13 28 C-11.35 28 -9.7 28 -8 28 C-8 24.37 -8 20.74 -8 17 C-7.67 17 -7.34 17 -7 17 C-7 21.29 -7 25.58 -7 30 C-9.97 30 -12.94 30 -16 30 C-16.037 28.663 -16.075 27.326 -16.113 25.949 C-16.179 24.195 -16.245 22.441 -16.312 20.688 C-16.335 19.806 -16.358 18.925 -16.381 18.018 C-16.416 17.17 -16.452 16.322 -16.488 15.449 C-16.514 14.669 -16.541 13.889 -16.568 13.085 C-17.102 10.506 -17.926 9.585 -20 8 C-22.513 7.638 -22.513 7.638 -25.188 7.812 C-26.089 7.84 -26.99 7.867 -27.918 7.895 C-28.605 7.929 -29.292 7.964 -30 8 C-29.67 7.01 -29.34 6.02 -29 5 C-28.094 5.012 -27.188 5.023 -26.254 5.035 C-17.983 5.108 -17.983 5.108 -9.75 4.438 C-6 4 -6 4 -4 5 C-4 4.34 -4 3.68 -4 3 C-3.01 3 -2.02 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#F9FAF9" transform="translate(769,626)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.073 1.464 3.073 1.464 3.148 2.957 C3.223 4.229 3.298 5.502 3.375 6.812 C3.445 8.077 3.514 9.342 3.586 10.645 C3.761 14.128 3.761 14.128 6 17 C8.934 16.733 8.934 16.733 12 16 C14.348 12.478 14.377 10.492 14.625 6.312 C14.7 5.133 14.775 3.954 14.852 2.738 C14.901 1.835 14.95 0.931 15 0 C16.32 0 17.64 0 19 0 C19 6.93 19 13.86 19 21 C17.68 21 16.36 21 15 21 C14.67 20.01 14.34 19.02 14 18 C13.464 18.495 12.928 18.99 12.375 19.5 C9.318 21.431 7.562 21.445 4 21 C1.828 20.051 1.828 20.051 0 18 C-0.506 14.928 -0.375 11.916 -0.25 8.812 C-0.232 7.968 -0.214 7.123 -0.195 6.252 C-0.148 4.167 -0.076 2.084 0 0 Z " fill="#143A5D" transform="translate(638,684)"/>
<path d="M0 0 C3.037 1.098 5.118 2.347 7 5 C7.62 8.093 7.564 10.9 7 14 C6 15 6 15 2.938 15.062 C1.968 15.042 0.999 15.021 0 15 C0 17.64 0 20.28 0 23 C1.65 23 3.3 23 5 23 C4.01 23.99 3.02 24.98 2 26 C0.68 25.34 -0.64 24.68 -2 24 C-2 19.71 -2 15.42 -2 11 C-0.02 11 1.96 11 4 11 C4 10.01 4 9.02 4 8 C2.02 8 0.04 8 -2 8 C-2 6.35 -2 4.7 -2 3 C-3.32 3 -4.64 3 -6 3 C-6 4.65 -6 6.3 -6 8 C-6.99 8 -7.98 8 -9 8 C-9 7.34 -9 6.68 -9 6 C-10.65 6 -12.3 6 -14 6 C-11.425 -1.173 -7.108 -0.9 0 0 Z " fill="#F5F7F6" transform="translate(425,625)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.33 0.99 3.66 1.98 4 3 C4.516 2.505 5.031 2.01 5.562 1.5 C8.924 -0.569 11.12 -0.485 15 0 C17.203 1.48 17.203 1.48 19 4 C19.483 6.934 19.378 9.787 19.25 12.75 C19.232 13.543 19.214 14.336 19.195 15.152 C19.148 17.102 19.077 19.051 19 21 C17.68 21 16.36 21 15 21 C14.951 19.94 14.902 18.881 14.852 17.789 C14.777 16.401 14.701 15.013 14.625 13.625 C14.594 12.926 14.563 12.228 14.531 11.508 C14.405 7.298 14.405 7.298 12 4 C8.967 4.089 8.967 4.089 6 5 C3.652 8.522 3.623 10.508 3.375 14.688 C3.3 15.867 3.225 17.046 3.148 18.262 C3.099 19.165 3.05 20.069 3 21 C2.01 21 1.02 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#173D5F" transform="translate(288,633)"/>
<path d="M0 0 C3.659 4.324 5 8.349 5 14 C5.99 14.33 6.98 14.66 8 15 C7.34 15 6.68 15 6 15 C6 16.65 6 18.3 6 20 C6.66 20 7.32 20 8 20 C6.63 35.209 4.017 47.281 -5 60 C-5.525 60.763 -6.049 61.526 -6.59 62.312 C-7.055 62.869 -7.521 63.426 -8 64 C-8.66 64 -9.32 64 -10 64 C-10 64.66 -10 65.32 -10 66 C-19.715 74.512 -30.26 79.349 -43 81 C-43 80.34 -43 79.68 -43 79 C-42.273 78.853 -41.546 78.706 -40.797 78.555 C-26.025 75.259 -13.259 68.275 -4.574 55.633 C3.278 43.239 6.006 29.108 3.391 14.625 C2.259 9.916 0.86 5.473 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D6DEE1" transform="translate(504,402)"/>
<path d="M0 0 C3.084 1.199 6.061 2.48 9 4 C9.66 4.33 10.32 4.66 11 5 C11.33 6.32 11.66 7.64 12 9 C15.63 9.66 19.26 10.32 23 11 C22.01 12.32 21.02 13.64 20 15 C19.34 14.67 18.68 14.34 18 14 C13.383 13.739 10.204 14.132 6 16 C5.01 16 4.02 16 3 16 C2.505 13.525 2.505 13.525 2 11 C0.35 11 -1.3 11 -3 11 C-3.33 9.68 -3.66 8.36 -4 7 C-2.35 6.01 -0.7 5.02 1 4 C-0.32 3.34 -1.64 2.68 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E8ECEA" transform="translate(349,620)"/>
<path d="M0 0 C0.985 0.027 1.97 0.054 2.984 0.082 C3.732 0.117 4.48 0.152 5.25 0.188 C2.711 1.488 0.214 2.609 -2.438 3.688 C-5.818 4.799 -5.818 4.799 -6.75 7.188 C-5.904 6.837 -5.059 6.486 -4.188 6.125 C-0.3 5.065 2.334 5.404 6.25 6.188 C6.58 7.508 6.91 8.827 7.25 10.188 C6.237 10.334 6.237 10.334 5.203 10.484 C-0.423 11.422 -0.423 11.422 -5.438 14 C-6.959 16.129 -6.959 16.129 -6.812 18.75 C-5.438 21.903 -3.717 22.59 -0.75 24.188 C-0.75 24.847 -0.75 25.508 -0.75 26.188 C-1.348 26.023 -1.946 25.857 -2.562 25.688 C-4.863 25.076 -4.863 25.076 -7.75 25.188 C-10.22 20.807 -10.117 17.052 -10.125 12.125 C-10.149 11.31 -10.174 10.494 -10.199 9.654 C-10.228 3.776 -10.228 3.776 -8.086 1.141 C-5.225 -0.027 -3.082 -0.11 0 0 Z " fill="#F8FAF9" transform="translate(557.75,630.8125)"/>
<path d="M0 0 C2.938 1.5 2.938 1.5 5 3 C4.67 3.66 4.34 4.32 4 5 C0.7 4.67 -2.6 4.34 -6 4 C-6 4.99 -6 5.98 -6 7 C-4.935 7.416 -4.935 7.416 -3.848 7.84 C-2.929 8.202 -2.009 8.564 -1.062 8.938 C-0.146 9.297 0.771 9.657 1.715 10.027 C4 11 4 11 5 12 C5.574 17.282 5.574 17.282 3.5 19.875 C-0.042 21.469 -3.177 21.595 -7 21 C-9.5 19.625 -9.5 19.625 -11 18 C-11 17.34 -11 16.68 -11 16 C-7.37 16.33 -3.74 16.66 0 17 C0 16.01 0 15.02 0 14 C-0.626 13.746 -1.253 13.492 -1.898 13.23 C-2.716 12.886 -3.533 12.542 -4.375 12.188 C-5.187 11.851 -5.999 11.515 -6.836 11.168 C-9 10 -9 10 -11 7 C-10.812 4.438 -10.812 4.438 -10 2 C-6.274 -0.484 -4.413 -0.38 0 0 Z " fill="#234F70" transform="translate(626,633)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.052 2.155 2.052 2.155 3.125 2.312 C6.421 3.101 8.32 4.727 10.996 6.727 C17.811 11.058 26.137 11.567 34 12 C34 12.33 34 12.66 34 13 C23.236 13.449 15.944 12.614 6.875 6.75 C3.442 4.604 2.238 4.008 -1.938 3.875 C-4.966 4.802 -4.966 4.802 -6.938 6.562 C-8.492 10.129 -8.838 13.241 -8 17 C-4.644 23.397 1.47 26.54 8 29 C17.232 31.877 26.4 32.615 36 33 C36 33.33 36 33.66 36 34 C21.8 36.177 8.139 33.401 -4 25.625 C-7.988 22.136 -10.734 19.158 -11.191 13.727 C-10.963 9.553 -10.331 7.433 -7.75 4.062 C-5.185 2.139 -3.045 0.957 0 0 Z " fill="#E0E6E7" transform="translate(324,449)"/>
<path d="M0 0 C8.906 0.324 14.714 4.354 20.812 10.688 C27.88 19.382 29.037 29.089 28 40 C26.54 48.471 21.401 56.726 14.562 61.938 C13.398 62.663 12.209 63.352 11 64 C10.419 64.317 9.837 64.634 9.238 64.961 C3.074 67.778 -3.355 67.273 -10 67 C-10 66.67 -10 66.34 -10 66 C-8.877 65.853 -7.754 65.706 -6.598 65.555 C4.02 64.022 11.057 61.943 18.812 54.188 C24.698 45.612 27.147 36.728 25.594 26.262 C23.196 17.085 18.421 10.168 10.324 5.219 C6.963 3.456 3.606 2.18 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DEE5E8" transform="translate(583,395)"/>
<path d="M0 0 C0.741 0.044 1.482 0.088 2.246 0.133 C7.614 1.853 10.956 7.495 14.25 11.812 C14.679 12.374 15.107 12.935 15.549 13.514 C19.068 18.152 22.448 22.884 25.809 27.638 C28.664 31.66 31.606 35.6 34.625 39.5 C35.032 40.028 35.439 40.555 35.859 41.099 C37.528 43.259 39.2 45.416 40.892 47.559 C42.186 49.199 43.469 50.849 44.75 52.5 C45.507 53.462 46.263 54.423 47.043 55.414 C47.544 56.226 48.046 57.038 48.562 57.875 C48.232 58.865 47.903 59.855 47.562 60.875 C38.701 49.502 30.17 37.894 21.65 26.264 C20.38 24.531 19.109 22.797 17.837 21.065 C16.646 19.442 15.456 17.818 14.269 16.193 C9.279 8.572 9.279 8.572 2.562 2.875 C-3.795 2.399 -3.795 2.399 -9.438 4.875 C-11.172 6.908 -11.172 6.908 -12.438 8.875 C-12.768 8.215 -13.097 7.555 -13.438 6.875 C-9.263 1.425 -7.095 -0.566 0 0 Z " fill="#D3DCE1" transform="translate(635.4375,221.125)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 5.29 5 9.58 5 14 C4.01 13.67 3.02 13.34 2 13 C1.67 15.31 1.34 17.62 1 20 C-2.63 20 -6.26 20 -10 20 C-10 18.68 -10 17.36 -10 16 C-10.66 16 -11.32 16 -12 16 C-12 13.69 -12 11.38 -12 9 C-15.315 10.709 -15.315 10.709 -15.688 14.125 C-15.791 15.074 -15.894 16.023 -16 17 C-15.34 17.33 -14.68 17.66 -14 18 C-15.65 17.67 -17.3 17.34 -19 17 C-18.67 13.37 -18.34 9.74 -18 6 C-17.34 6 -16.68 6 -16 6 C-16 5.01 -16 4.02 -16 3 C-12.535 2.505 -12.535 2.505 -9 2 C-9 7.61 -9 13.22 -9 19 C-6.03 19 -3.06 19 0 19 C0 12.73 0 6.46 0 0 Z M-13 5 C-13 8 -13 8 -13 8 Z M3 9 C4 12 4 12 4 12 Z M-11 13 C-10 15 -10 15 -10 15 Z " fill="#E0E2DD" transform="translate(415,688)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C6.34 1.32 5.68 2.64 5 4 C4.34 4 3.68 4 3 4 C3.33 4.66 3.66 5.32 4 6 C4.516 5.835 5.031 5.67 5.562 5.5 C8.734 4.85 11.772 4.938 15 5 C15.66 6.32 16.32 7.64 17 9 C16.325 9.11 15.649 9.219 14.953 9.332 C14.061 9.491 13.169 9.649 12.25 9.812 C11.368 9.963 10.487 10.114 9.578 10.27 C6.809 11.054 5.174 12.146 3 14 C3.256 16.343 3.593 18.678 4 21 C4.66 21.33 5.32 21.66 6 22 C6.625 24.062 6.625 24.062 7 26 C1.354 24.527 1.354 24.527 -0.197 22.22 C-1.116 19.68 -1.149 17.923 -0.977 15.234 C-0.925 14.345 -0.873 13.455 -0.82 12.539 C-0.756 11.619 -0.691 10.698 -0.625 9.75 C-0.568 8.814 -0.512 7.878 -0.453 6.914 C-0.312 4.609 -0.161 2.304 0 0 Z " fill="#EEF1F1" transform="translate(520,682)"/>
<path d="M0 0 C3.375 0.938 3.375 0.938 5.375 3.938 C5.802 7.848 5.961 10.058 3.75 13.375 C0.676 15.397 -1.014 15.552 -4.625 14.938 C-7.42 12.608 -7.61 11.058 -8.062 7.438 C-7.625 3.938 -7.625 3.938 -6.062 1.438 C-3.625 -0.062 -3.625 -0.062 0 0 Z M-1.625 4.938 C-0.625 6.938 -0.625 6.938 -0.625 6.938 Z M1.375 6.938 C2.375 8.938 2.375 8.938 2.375 8.938 Z M0.375 8.938 C1.375 10.938 1.375 10.938 1.375 10.938 Z " fill="#F2F5F5" transform="translate(401.625,636.0625)"/>
<path d="M0 0 C2.5 1.375 2.5 1.375 4 3 C4 3.66 4 4.32 4 5 C3.313 4.893 2.626 4.786 1.918 4.676 C1.017 4.556 0.116 4.436 -0.812 4.312 C-1.706 4.185 -2.599 4.057 -3.52 3.926 C-6.028 3.774 -6.028 3.774 -7.805 5.262 C-9.758 8.103 -9.298 10.665 -9 14 C-8.01 15.485 -8.01 15.485 -7 17 C-4 17.417 -4 17.417 -1 17 C-0.34 16.34 0.32 15.68 1 15 C1.99 15 2.98 15 4 15 C2.25 19.875 2.25 19.875 0 21 C-7.09 21.581 -7.09 21.581 -10.938 19.125 C-14.343 15.616 -14.331 12.982 -14.293 8.262 C-13.863 4.946 -12.391 3.25 -10 1 C-6.817 -0.591 -3.465 -0.397 0 0 Z " fill="#144267" transform="translate(692,633)"/>
<path d="M0 0 C0.866 -0.028 1.732 -0.057 2.625 -0.086 C5 0.375 5 0.375 6.812 2.398 C7.204 3.051 7.596 3.703 8 4.375 C7.01 4.705 6.02 5.035 5 5.375 C4.34 5.045 3.68 4.715 3 4.375 C0 4.125 0 4.125 -3 4.375 C-5.243 6.187 -5.243 6.187 -5.195 8.758 C-5.172 9.663 -5.149 10.568 -5.125 11.5 C-5.107 12.41 -5.089 13.32 -5.07 14.258 C-5.047 14.956 -5.024 15.655 -5 16.375 C0.282 17.575 0.282 17.575 5 15.375 C5.99 15.375 6.98 15.375 8 15.375 C6.63 18.536 6.011 19.368 3 21.375 C-1.644 21.88 -4.267 21.175 -8 18.375 C-10.073 15.265 -10.274 14.484 -10.25 10.938 C-10.255 10.176 -10.26 9.414 -10.266 8.629 C-9.967 6.092 -9.37 4.526 -8 2.375 C-4.803 0.243 -3.712 0.055 0 0 Z " fill="#285272" transform="translate(555,683.625)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.98 4 3.96 4 6 C5.98 6 7.96 6 10 6 C10 6.99 10 7.98 10 9 C8.02 9 6.04 9 4 9 C4.33 13.29 4.66 17.58 5 22 C7.475 22.99 7.475 22.99 10 24 C9.67 24.99 9.34 25.98 9 27 C3.59 27.361 3.59 27.361 1.312 25.969 C-0.709 22.937 -0.315 19.821 -0.188 16.312 C-0.167 15.26 -0.167 15.26 -0.146 14.186 C-0.111 12.457 -0.057 10.728 0 9 C-1.32 9 -2.64 9 -4 9 C-4 8.01 -4 7.02 -4 6 C-2.68 6 -1.36 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#1F4E6F" transform="translate(422,678)"/>
<path d="M0 0 C2.438 2.062 2.438 2.062 4 5 C3.939 8.52 3.662 10.919 1.812 13.938 C-0.956 15.56 -2.855 15.449 -6 15 C-8.438 13.562 -8.438 13.562 -10 11 C-10.177 7.881 -9.96 4.979 -9 2 C-5.504 -0.33 -4.113 -0.588 0 0 Z M-6 4 C-6.33 4.99 -6.66 5.98 -7 7 C-6.34 7 -5.68 7 -5 7 C-5.33 6.01 -5.66 5.02 -6 4 Z M-4 4 C-3.67 5.65 -3.34 7.3 -3 9 C-2.34 7.68 -1.68 6.36 -1 5 C-1.99 4.67 -2.98 4.34 -4 4 Z M-4 9 C-4.66 9.66 -5.32 10.32 -6 11 C-5.01 11 -4.02 11 -3 11 C-3.33 10.34 -3.66 9.68 -4 9 Z " fill="#EBF0F0" transform="translate(801,636)"/>
<path d="M0 0 C3.375 0.938 3.375 0.938 5.375 3.938 C5.7 7.512 5.756 10.064 4.312 13.375 C2.375 14.938 2.375 14.938 -1.125 15.375 C-4.625 14.938 -4.625 14.938 -6.562 13.938 C-8.287 10.692 -8.249 7.47 -7.625 3.938 C-5.361 0.224 -4.37 -0.075 0 0 Z M0.375 3.938 C1.375 5.938 1.375 5.938 1.375 5.938 Z M-4.625 4.938 C-3.625 8.938 -3.625 8.938 -3.625 8.938 Z " fill="#E9EDEE" transform="translate(646.625,636.0625)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.65 4 3.3 4 5 C6.31 5 8.62 5 11 5 C11 5.99 11 6.98 11 8 C8.69 8 6.38 8 4 8 C4.141 9.959 4.288 11.917 4.438 13.875 C4.519 14.966 4.6 16.056 4.684 17.18 C4.83 19.947 4.83 19.947 6 22 C8.527 22.656 8.527 22.656 11 23 C11 23.99 11 24.98 11 26 C4.689 26.485 4.689 26.485 1.781 24.438 C-0.659 21.098 -0.401 18.838 -0.25 14.75 C-0.214 13.487 -0.178 12.223 -0.141 10.922 C-0.094 9.958 -0.048 8.993 0 8 C-0.99 8 -1.98 8 -3 8 C-3 7.01 -3 6.02 -3 5 C-2.01 5 -1.02 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#235071" transform="translate(771,628)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.65 4 3.3 4 5 C5.98 5 7.96 5 10 5 C10 5.99 10 6.98 10 8 C8.02 8 6.04 8 4 8 C4.33 12.29 4.66 16.58 5 21 C6.98 21.66 8.96 22.32 11 23 C10.67 23.99 10.34 24.98 10 26 C3.603 26.362 3.603 26.362 1.281 24.969 C-0.697 21.929 -0.315 18.809 -0.188 15.312 C-0.167 14.26 -0.167 14.26 -0.146 13.186 C-0.111 11.457 -0.057 9.728 0 8 C-1.32 8 -2.64 8 -4 8 C-4 7.01 -4 6.02 -4 5 C-2.68 5 -1.36 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#295B7B" transform="translate(419,628)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.98 4 3.96 4 6 C5.98 6 7.96 6 10 6 C10 6.99 10 7.98 10 9 C8.02 9 6.04 9 4 9 C4.33 13.29 4.66 17.58 5 22 C6.65 22.33 8.3 22.66 10 23 C9.67 24.32 9.34 25.64 9 27 C6.188 27.25 6.188 27.25 3 27 C0.368 24.563 0.013 23.153 -0.293 19.547 C-0.258 18.294 -0.223 17.041 -0.188 15.75 C-0.16 14.487 -0.133 13.223 -0.105 11.922 C-0.071 10.958 -0.036 9.993 0 9 C-0.99 9 -1.98 9 -3 9 C-3 8.01 -3 7.02 -3 6 C-2.01 6 -1.02 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#1E4B6A" transform="translate(569,678)"/>
<path d="M0 0 C2.438 2 2.438 2 4 4 C2.062 4.562 2.062 4.562 0 5 C-0.33 4.67 -0.66 4.34 -1 4 C-4.517 4.252 -4.517 4.252 -8 5 C-9.509 8.019 -9.175 10.673 -9 14 C-8.01 15.485 -8.01 15.485 -7 17 C-3.053 17.429 -0.388 17.085 3 15 C3 16.32 3 17.64 3 19 C-0.685 21.457 -3.72 21.701 -8 21 C-10.81 19.206 -12.509 17.981 -14 15 C-14.347 9.707 -14.514 6.266 -11 2 C-7.586 -0.561 -4.145 -0.444 0 0 Z " fill="#214D6E" transform="translate(367,633)"/>
<path d="M0 0 C2.938 1.5 2.938 1.5 5 3 C4.67 3.99 4.34 4.98 4 6 C3.278 5.67 2.556 5.34 1.812 5 C-1.059 3.979 -2.985 3.674 -6 4 C-8.189 5.781 -8.189 5.781 -8.266 8.094 C-8.258 9.285 -8.258 9.285 -8.25 10.5 C-8.255 11.294 -8.26 12.088 -8.266 12.906 C-8.189 15.219 -8.189 15.219 -6 17 C-1.947 17.345 -0.452 17.302 3 15 C3 16.32 3 17.64 3 19 C1 21 1 21 -2.75 21.438 C-6.483 21.308 -9.14 20.426 -11.93 17.758 C-14.158 14.098 -13.804 10.08 -13 6 C-9.454 0.208 -6.632 -0.45 0 0 Z " fill="#1E4567" transform="translate(672,633)"/>
<path d="M0 0 C0.794 -0.014 1.588 -0.028 2.406 -0.043 C4.5 0.188 4.5 0.188 6.5 2.188 C6.5 3.178 6.5 4.168 6.5 5.188 C5.51 4.857 4.52 4.528 3.5 4.188 C-0.042 3.854 -0.042 3.854 -3.5 4.188 C-5.826 7.676 -5.919 9.085 -5.5 13.188 C-4.121 15.53 -4.121 15.53 -2.5 17.188 C-1.139 16.94 -1.139 16.94 0.25 16.688 C3.5 16.188 3.5 16.188 7.5 16.188 C3.374 21.042 3.374 21.042 -0.5 21.688 C-4.557 21.011 -7.257 19.785 -9.812 16.5 C-10.843 13.035 -11.091 9.736 -10.5 6.188 C-7.493 1.476 -5.48 0.044 0 0 Z " fill="#163C5E" transform="translate(602.5,683.8125)"/>
<path d="M0 0 C2.312 1.312 2.312 1.312 4 3 C4 3.99 4 4.98 4 6 C4.66 6 5.32 6 6 6 C6 6.99 6 7.98 6 9 C4.35 9 2.7 9 1 9 C0.67 10.98 0.34 12.96 0 15 C-5.422 15.482 -5.422 15.482 -7.875 13.438 C-9.436 10.054 -9.46 7.684 -9 4 C-6.452 0.319 -4.439 -0.777 0 0 Z M-4 4 C-3 6 -3 6 -3 6 Z " fill="#F0F2F3" transform="translate(691,636)"/>
<path d="M0 0 C3.056 2.091 3.865 3.293 4.562 6.938 C3.805 11.06 2.348 12.545 -1 15 C-3.883 15 -5.355 14.378 -7.812 12.938 C-9.743 9.788 -9.538 6.586 -9 3 C-6.895 -0.742 -4.003 -0.572 0 0 Z M-3 5 C-3.33 5.99 -3.66 6.98 -4 8 C-3.01 8.495 -3.01 8.495 -2 9 C-2.33 7.68 -2.66 6.36 -3 5 Z M0 5 C-0.495 6.98 -0.495 6.98 -1 9 C-0.34 9 0.32 9 1 9 C0.67 7.68 0.34 6.36 0 5 Z M-6 7 C-5 9 -5 9 -5 9 Z " fill="#EFF3F3" transform="translate(673,687)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.65 4 3.3 4 5 C5.98 5 7.96 5 10 5 C10 5.99 10 6.98 10 8 C8.02 8 6.04 8 4 8 C4.33 12.62 4.66 17.24 5 22 C6.32 22.33 7.64 22.66 9 23 C9 23.99 9 24.98 9 26 C3.59 26.361 3.59 26.361 1.312 24.969 C-0.709 21.937 -0.315 18.821 -0.188 15.312 C-0.167 14.26 -0.167 14.26 -0.146 13.186 C-0.111 11.457 -0.057 9.728 0 8 C-1.32 8 -2.64 8 -4 8 C-4 7.01 -4 6.02 -4 5 C-2.68 5 -1.36 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#2D5776" transform="translate(378,628)"/>
<path d="M0 0 C1.938 1 1.938 1 3 3 C3.393 6.095 3.629 8.044 3 11 C0.875 13.562 0.875 13.562 -2 15 C-5.188 14.938 -5.188 14.938 -8 14 C-10.544 10.184 -10.577 7.47 -10 3 C-7.832 -1.082 -4.194 -0.449 0 0 Z M-1 6 C0 9 0 9 0 9 Z M-7 7 C-6.67 8.32 -6.34 9.64 -6 11 C-4.68 10.67 -3.36 10.34 -2 10 C-2.66 9.01 -3.32 8.02 -4 7 C-4.99 7 -5.98 7 -7 7 Z " fill="#EFF2F1" transform="translate(494,687)"/>
<path d="M0 0 C1.181 0.01 1.181 0.01 2.387 0.02 C4.312 0.188 4.312 0.188 5.312 1.188 C5.385 3.541 5.396 5.896 5.375 8.25 C5.366 9.54 5.357 10.831 5.348 12.16 C5.336 13.159 5.324 14.158 5.312 15.188 C4.982 15.188 4.653 15.188 4.312 15.188 C4.312 13.538 4.312 11.888 4.312 10.188 C-0.143 11.673 -0.143 11.673 -4.688 13.188 C-4.688 14.508 -4.688 15.827 -4.688 17.188 C-2.048 17.188 0.592 17.188 3.312 17.188 C2.25 19.188 2.25 19.188 0.312 21.188 C-3.161 21.622 -5.554 21.79 -8.688 20.125 C-10.382 16.843 -9.733 14.631 -8.688 11.188 C-4.449 8.059 -0.848 7.878 4.312 8.188 C3.653 6.867 2.992 5.548 2.312 4.188 C-0.987 4.518 -4.288 4.847 -7.688 5.188 C-8.018 4.197 -8.347 3.207 -8.688 2.188 C-5.659 0.434 -3.489 -0.063 0 0 Z " fill="#1B4265" transform="translate(744.6875,632.8125)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.67 2.33 1.34 2.66 1 3 C0.749 6.284 0.815 9.582 0.812 12.875 C0.794 14.261 0.794 14.261 0.775 15.676 C0.773 16.564 0.772 17.452 0.77 18.367 C0.763 19.591 0.763 19.591 0.757 20.839 C0.866 23.296 0.866 23.296 3 26 C3.5 29.375 3.5 29.375 3 33 C1.176 35.126 -0.661 36.392 -3 38 C-3.66 37.34 -4.32 36.68 -5 36 C-6.317 35.303 -7.649 34.63 -9 34 C-6.36 34 -3.72 34 -1 34 C-1 23.11 -1 12.22 -1 1 C-3.64 1 -6.28 1 -9 1 C-8.34 1.66 -7.68 2.32 -7 3 C-6.725 6.349 -6.914 9.635 -7 13 C-10.3 12.34 -13.6 11.68 -17 11 C-17 10.67 -17 10.34 -17 10 C-15.35 9.67 -13.7 9.34 -12 9 C-13.65 8.67 -15.3 8.34 -17 8 C-17 7.67 -17 7.34 -17 7 C-15.35 7 -13.7 7 -12 7 C-12 5.02 -12 3.04 -12 1 C-4.437 -1.521 -4.437 -1.521 0 0 Z M-11 6 C-10 8 -10 8 -10 8 Z " fill="#E5E8E6" transform="translate(684,673)"/>
<path d="M0 0 C3.5 0.5 3.5 0.5 5.438 1.5 C7.158 4.739 7.139 7.974 6.5 11.5 C4.812 14 4.812 14 2.5 15.5 C-0.351 15.987 -1.772 15.942 -4.25 14.438 C-6.165 11.469 -6.379 9.002 -6.5 5.5 C-5.08 1.328 -4.418 0.631 0 0 Z M-2.5 4.5 C-2.5 6.48 -2.5 8.46 -2.5 10.5 C-1.51 10.5 -0.52 10.5 0.5 10.5 C0.5 8.85 0.5 7.2 0.5 5.5 C-0.49 5.17 -1.48 4.84 -2.5 4.5 Z M2.5 6.5 C2.17 7.49 1.84 8.48 1.5 9.5 C2.49 9.995 2.49 9.995 3.5 10.5 C3.17 9.18 2.84 7.86 2.5 6.5 Z " fill="#ECF0F0" transform="translate(623.5,686.5)"/>
<path d="M0 0 C2.312 1.625 2.312 1.625 4 4 C4.646 7.794 4.247 9.535 2.438 12.938 C0 15 0 15 -3.625 14.938 C-7 14 -7 14 -9 12 C-9.677 5.003 -9.677 5.003 -7.312 1.5 C-4.513 -0.316 -3.271 -0.654 0 0 Z M-1 5 C-0.67 6.32 -0.34 7.64 0 9 C0.33 8.01 0.66 7.02 1 6 C0.34 5.67 -0.32 5.34 -1 5 Z M-2 9 C-1.67 9.66 -1.34 10.32 -1 11 C-0.67 10.34 -0.34 9.68 0 9 C-0.66 9 -1.32 9 -2 9 Z " fill="#EBEFF0" transform="translate(500,636)"/>
<path d="M0 0 C7.16 4.718 11.927 10.605 15.211 18.457 C14.551 19.117 13.891 19.777 13.211 20.457 C12.957 19.891 12.703 19.325 12.441 18.742 C12.097 18.009 11.753 17.275 11.398 16.52 C11.062 15.789 10.726 15.058 10.379 14.305 C9.275 12.127 9.275 12.127 6.211 11.457 C5.984 10.715 5.757 9.972 5.523 9.207 C3.519 5.008 0.369 3.353 -3.789 1.457 C-13.118 -1.366 -21.732 -1.23 -30.789 2.457 C-32.13 3.447 -33.465 4.445 -34.789 5.457 C-35.449 5.457 -36.109 5.457 -36.789 5.457 C-36.789 6.117 -36.789 6.777 -36.789 7.457 C-37.367 7.993 -37.944 8.53 -38.539 9.082 C-42.569 13.335 -45.059 18.62 -45.789 24.457 C-47.214 22.189 -47.914 21.056 -47.355 18.379 C-42.983 8.919 -37.346 2.772 -27.789 -1.543 C-19.62 -4.266 -7.766 -3.751 0 0 Z " fill="#D4DCE1" transform="translate(397.7890625,244.54296875)"/>
<path d="M0 0 C-2.836 2.836 -5.171 2.49 -9.125 2.875 C-18.074 3.907 -24.026 6.057 -30 13 C-30.866 13.969 -31.733 14.939 -32.625 15.938 C-37.26 22.691 -38.696 31.823 -37.438 39.812 C-36.256 44.822 -34.329 49.421 -32 54 C-32.99 54.495 -32.99 54.495 -34 55 C-39.878 45.595 -41.333 36.303 -39.57 25.324 C-37.309 16.35 -31.643 9.423 -24.082 4.219 C-16.298 -0.338 -8.869 -0.37 0 0 Z " fill="#C9D4DA" transform="translate(461,394)"/>
<path d="M0 0 C1.313 -0.025 1.313 -0.025 2.652 -0.051 C5.519 0.381 6.348 1.017 8.062 3.312 C9.269 6.931 9.171 10.238 9.125 14 C9.12 14.702 9.116 15.404 9.111 16.127 C9.1 17.856 9.082 19.584 9.062 21.312 C7.413 21.312 5.762 21.312 4.062 21.312 C4.002 20.169 3.941 19.026 3.879 17.848 C3.795 16.357 3.71 14.866 3.625 13.375 C3.586 12.62 3.546 11.866 3.506 11.088 C3.622 7.393 3.622 7.393 2.062 4.312 C-0.854 3.979 -0.854 3.979 -3.938 4.312 C-5.689 5.729 -5.689 5.729 -6.938 7.312 C-7.598 7.312 -8.257 7.312 -8.938 7.312 C-6.966 1.96 -5.878 0.026 0 0 Z " fill="#355B76" transform="translate(403.9375,683.6875)"/>
<path d="M0 0 C3.5 0.875 3.5 0.875 5.438 2 C7.171 5.06 6.921 7.443 6.5 10.875 C4.875 13.312 4.875 13.312 2.5 14.875 C-0.562 15.312 -0.562 15.312 -3.5 14.875 C-5.646 13.264 -6.407 12.381 -6.898 9.719 C-6.891 8.945 -6.883 8.172 -6.875 7.375 C-6.883 6.602 -6.89 5.828 -6.898 5.031 C-6.093 0.674 -4.266 -0.152 0 0 Z M-2.5 3.875 C-2.83 4.535 -3.16 5.195 -3.5 5.875 C-2.84 5.875 -2.18 5.875 -1.5 5.875 C-1.83 5.215 -2.16 4.555 -2.5 3.875 Z M0.5 3.875 C-1.062 5.187 -1.062 5.187 -2.5 6.875 C-2.5 7.865 -2.5 8.855 -2.5 9.875 C-0.52 10.37 -0.52 10.37 1.5 10.875 C2.16 9.555 2.82 8.235 3.5 6.875 C2.51 5.885 1.52 4.895 0.5 3.875 Z " fill="#E7EBEA" transform="translate(521.5,636.125)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.221 4.951 3.281 7.684 3.188 10.625 C3.174 11.427 3.16 12.229 3.146 13.055 C3.111 15.037 3.057 17.018 3 19 C1.68 19 0.36 19 -1 19 C-1 17.68 -1 16.36 -1 15 C-5.455 15.495 -5.455 15.495 -10 16 C-10.495 13.525 -10.495 13.525 -11 11 C-7.488 8.592 -5.273 7.634 -1 8 C-0.67 5.36 -0.34 2.72 0 0 Z " fill="#1F4C6E" transform="translate(750,635)"/>
<path d="M0 0 C7.806 3.962 11.231 12.791 14.991 20.198 C15.942 22.072 16.904 23.94 17.867 25.807 C22.516 34.861 22.516 34.861 23.688 38.375 C20.688 37.375 20.688 37.375 19.432 35.401 C19.025 34.548 18.618 33.695 18.199 32.816 C17.74 31.872 17.28 30.928 16.807 29.955 C16.098 28.461 16.098 28.461 15.375 26.938 C9.355 12.046 9.355 12.046 -2.035 1.93 C-7.067 0.704 -12.515 0.129 -17.348 2.281 C-19.175 3.44 -19.175 3.44 -21.312 5.375 C-21.312 4.385 -21.312 3.395 -21.312 2.375 C-15.449 -2.882 -7.027 -3.219 0 0 Z " fill="#D1D9DE" transform="translate(476.3125,222.625)"/>
<path d="M0 0 C12.87 0 25.74 0 39 0 C39 10.23 39 20.46 39 31 C38.67 31 38.34 31 38 31 C38 21.43 38 11.86 38 2 C26.12 2 14.24 2 2 2 C2 6.95 2 11.9 2 17 C1.67 17 1.34 17 1 17 C0.67 11.39 0.34 5.78 0 0 Z " fill="#507D94" transform="translate(786,270)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 0.99 4.66 1.98 5 3 C5.577 2.505 6.155 2.01 6.75 1.5 C9 0 9 0 13 0 C13 1.32 13 2.64 13 4 C12.051 4.289 11.102 4.577 10.125 4.875 C6.948 5.768 6.948 5.768 5 8 C4.535 11.122 4.535 11.122 4.375 14.625 C4.3 15.814 4.225 17.002 4.148 18.227 C4.099 19.142 4.05 20.057 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#184165" transform="translate(699,633)"/>
<path d="M0 0 C-0.188 1.875 -0.188 1.875 -1 4 C-2.667 4.667 -4.333 5.333 -6 6 C-6.887 8.041 -6.887 8.041 -7 10 C-6.34 10.33 -5.68 10.66 -5 11 C-5 9.68 -5 8.36 -5 7 C-3.02 6.67 -1.04 6.34 1 6 C0.34 8.97 -0.32 11.94 -1 15 C-1.99 15 -2.98 15 -4 15 C-4 14.34 -4 13.68 -4 13 C-5.65 13.66 -7.3 14.32 -9 15 C-9.33 17.64 -9.66 20.28 -10 23 C-10.33 23 -10.66 23 -11 23 C-11.027 21.042 -11.046 19.083 -11.062 17.125 C-11.074 16.034 -11.086 14.944 -11.098 13.82 C-11 11 -11 11 -10 9 C-10.66 8.67 -11.32 8.34 -12 8 C-8.582 2.579 -6.689 -0.362 0 0 Z " fill="#E1E4DF" transform="translate(321,633)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 9.57 4 19.14 4 29 C2.68 29 1.36 29 0 29 C0 19.43 0 9.86 0 0 Z " fill="#2D4E6E" transform="translate(536,625)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 0.99 4.66 1.98 5 3 C5.577 2.505 6.155 2.01 6.75 1.5 C9 0 9 0 13 0 C13 1.32 13 2.64 13 4 C11.866 4.289 10.731 4.577 9.562 4.875 C8.387 5.246 7.211 5.617 6 6 C4.584 8.833 4.654 11.475 4.438 14.625 C4.354 15.814 4.27 17.002 4.184 18.227 C4.123 19.142 4.062 20.057 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#376C88" transform="translate(259,633)"/>
<path d="M0 0 C0 3.547 -0.597 4.005 -2.812 6.625 C-8.536 14.053 -9.992 21.727 -9 31 C-7.01 40.056 -2.884 47.868 5 53 C7.555 54.185 10.111 55.201 12.766 56.141 C15 57 15 57 18 59 C10.481 59.8 5.628 56.879 -0.176 52.273 C-7.48 45.588 -11.09 38.282 -11.64 28.412 C-11.838 18.632 -9.838 10.888 -3.488 3.16 C-2.125 1.75 -2.125 1.75 0 0 Z " fill="#D7DFE2" transform="translate(758,248)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.217 3.227 -2.434 3.454 -3.688 3.688 C-8.011 4.801 -10.387 6.243 -13 10 C-13.689 13.65 -13.387 16.921 -12.125 20.438 C-11.069 22.159 -11.069 22.159 -9 23 C-9 23.66 -9 24.32 -9 25 C-12.403 23.941 -15.014 22.991 -18 21 C-17.859 13.169 -17.541 7.793 -12 2 C-8.092 -0.365 -4.495 -0.176 0 0 Z " fill="#F4F5F4" transform="translate(606,680)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 10.89 2 21.78 2 33 C-1.63 33 -5.26 33 -9 33 C-7.68 31.68 -6.36 30.36 -5 29 C-5 29.66 -5 30.32 -5 31 C-3.68 31 -2.36 31 -1 31 C-1.005 30.203 -1.01 29.406 -1.016 28.585 C-1.037 24.973 -1.05 21.362 -1.062 17.75 C-1.071 16.496 -1.079 15.241 -1.088 13.949 C-1.091 12.744 -1.094 11.539 -1.098 10.297 C-1.103 9.187 -1.108 8.076 -1.114 6.933 C-1.017 4.428 -0.732 2.382 0 0 Z " fill="#F9FAFA" transform="translate(502,674)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-0.603 2.159 -1.207 2.317 -1.828 2.48 C-7.024 3.997 -10.018 5.202 -13 10 C-13.33 9.67 -13.66 9.34 -14 9 C-14.33 11.31 -14.66 13.62 -15 16 C-15.33 16 -15.66 16 -16 16 C-16.061 15.408 -16.121 14.817 -16.184 14.207 C-16.309 13.022 -16.309 13.022 -16.438 11.812 C-16.519 11.035 -16.6 10.258 -16.684 9.457 C-17.008 6.94 -17.475 4.482 -18 2 C-18.773 2.012 -19.547 2.023 -20.344 2.035 C-26.339 2.087 -32.073 1.912 -38 1 C-38 0.67 -38 0.34 -38 0 C-34.563 -0.058 -31.125 -0.094 -27.688 -0.125 C-26.711 -0.142 -25.735 -0.159 -24.729 -0.176 C-23.791 -0.182 -22.853 -0.189 -21.887 -0.195 C-21.023 -0.206 -20.159 -0.216 -19.268 -0.227 C-17 0 -17 0 -14 2 C-11.599 1.19 -11.599 1.19 -9 0 C-5.365 -0.8 -3.561 -1.187 0 0 Z " fill="#EDF0ED" transform="translate(323,630)"/>
<path d="M0 0 C4.744 0.922 4.744 0.922 7.062 1.625 C9.336 2.065 10.816 1.702 13 1 C13 3.31 13 5.62 13 8 C12.01 8 11.02 8 10 8 C10 8.66 10 9.32 10 10 C4.585 13.077 4.585 13.077 1.625 12.688 C1.089 12.461 0.553 12.234 0 12 C0 11.34 0 10.68 0 10 C-0.33 9.01 -0.66 8.02 -1 7 C-0.67 6.01 -0.34 5.02 0 4 C0.99 4 1.98 4 3 4 C3 3.34 3 2.68 3 2 C1.68 2.33 0.36 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#E6E8E3" transform="translate(553,689)"/>
<path d="M0 0 C0.33 -0.66 0.66 -1.32 1 -2 C1.99 -1.34 2.98 -0.68 4 0 C6.671 -0.802 6.671 -0.802 9 -2 C9 -1.34 9 -0.68 9 0 C10.98 0 12.96 0 15 0 C15 0.33 15 0.66 15 1 C13.907 1.124 12.814 1.247 11.688 1.375 C7.926 1.755 7.926 1.755 5 4 C4.01 5.485 4.01 5.485 3 7 C1.68 6.34 0.36 5.68 -1 5 C-1.33 5.66 -1.66 6.32 -2 7 C-2.402 5.763 -2.402 5.763 -2.812 4.5 C-4.744 1.734 -4.744 1.734 -7.648 1.668 C-10.446 1.686 -13.21 1.787 -16 2 C-16 0.68 -16 -0.64 -16 -2 C-10.705 -3.708 -4.909 -2.455 0 0 Z M1 0 C1.33 0.99 1.66 1.98 2 3 C2 2.01 2 1.02 2 0 C1.67 0 1.34 0 1 0 Z " fill="#E5EAEA" transform="translate(542,683)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.958 5.323 5.483 10.033 3 15 C3.317 15.583 3.634 16.165 3.961 16.766 C5.312 19.672 5.001 21.591 4.625 24.75 C4.514 25.735 4.403 26.72 4.289 27.734 C4.194 28.482 4.098 29.23 4 30 C2.68 29.67 1.36 29.34 0 29 C0 19.43 0 9.86 0 0 Z " fill="#224667" transform="translate(487,633)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C5.625 2.167 5.625 2.167 8 2 C8.456 3.986 8.911 5.971 9.367 7.957 C9.98 10.277 9.98 10.277 12 13 C11.01 13.99 10.02 14.98 9 16 C8.34 13.03 7.68 10.06 7 7 C6.34 7.66 5.68 8.32 5 9 C3.68 9 2.36 9 1 9 C0.34 9.66 -0.32 10.32 -1 11 C-1 9.35 -1 7.7 -1 6 C-1.928 5.783 -1.928 5.783 -2.875 5.562 C-5 5 -5 5 -7 4 C-7.33 5.32 -7.66 6.64 -8 8 C-8.99 7.67 -9.98 7.34 -11 7 C-11 7.66 -11 8.32 -11 9 C-11.66 8.67 -12.32 8.34 -13 8 C-13 7.34 -13 6.68 -13 6 C-12.01 6 -11.02 6 -10 6 C-10 5.34 -10 4.68 -10 4 C-9.092 3.711 -8.185 3.423 -7.25 3.125 C-4.678 2.235 -2.387 1.285 0 0 Z " fill="#E7EAE7" transform="translate(776,622)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 0.66 4.66 1.32 5 2 C5.99 1.34 6.98 0.68 8 0 C8.99 0.33 9.98 0.66 11 1 C9 3 7 5 5 7 C4.765 9.35 4.586 11.706 4.438 14.062 C4.354 15.353 4.27 16.643 4.184 17.973 C4.123 18.972 4.062 19.971 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#1A4165" transform="translate(756,633)"/>
<path d="M0 0 C0.857 -0.017 1.714 -0.034 2.598 -0.051 C5.415 0.387 6.244 1.06 7.938 3.312 C9.144 6.931 9.046 10.238 9 14 C8.995 14.702 8.991 15.404 8.986 16.127 C8.975 17.856 8.957 19.584 8.938 21.312 C7.617 20.982 6.298 20.653 4.938 20.312 C4.91 18.375 4.891 16.438 4.875 14.5 C4.863 13.421 4.852 12.342 4.84 11.23 C4.938 8.312 4.938 8.312 5.938 5.312 C1.294 3.765 -3.388 3.817 -8.062 5.312 C-8.393 4.322 -8.722 3.332 -9.062 2.312 C-5.944 0.497 -3.597 0.016 0 0 Z " fill="#335C78" transform="translate(532.0625,683.6875)"/>
<path d="M0 0 C3 0.5 3 0.5 4.938 2.188 C6.26 5.065 5.91 6.522 5 9.5 C8.3 9.5 11.6 9.5 15 9.5 C15 9.83 15 10.16 15 10.5 C8.4 10.5 1.8 10.5 -5 10.5 C-6 4.5 -6 4.5 -4.938 2.125 C-3 0.5 -3 0.5 0 0 Z " fill="#EAECEB" transform="translate(451,671.5)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.33 2.97 4.66 5.94 5 9 C7.97 9 10.94 9 14 9 C13.412 5.976 12.773 2.982 12 0 C13.65 0 15.3 0 17 0 C18.375 3.82 17.741 7.075 17 11 C16 12 16 12 13.094 12.133 C11.908 12.13 10.722 12.128 9.5 12.125 C8.314 12.128 7.128 12.13 5.906 12.133 C3 12 3 12 2 11 C1.792 9.526 1.633 8.045 1.5 6.562 C1.209 3.155 1.209 3.155 0 0 Z " fill="#335B7A" transform="translate(573,633)"/>
<path d="M0 0 C3.726 1.427 4.681 2.347 6.438 5.938 C7 9 7 9 6.125 11.375 C5.754 11.911 5.382 12.447 5 13 C4.67 13 4.34 13 4 13 C3.897 11.886 3.794 10.773 3.688 9.625 C3.461 8.429 3.234 7.232 3 6 C-0.75 3.5 -2.559 3.464 -7 4 C-10.16 6.541 -11.715 8.146 -13 12 C-14.32 11.67 -15.64 11.34 -17 11 C-13.784 2.198 -9.619 -1.414 0 0 Z " fill="#F3F6F4" transform="translate(226,624)"/>
<path d="M0 0 C2 2 2 2 2 5 C1.385 5.133 0.77 5.266 0.137 5.402 C-1.076 5.667 -1.076 5.667 -2.312 5.938 C-3.113 6.112 -3.914 6.286 -4.738 6.465 C-7.145 6.999 -7.145 6.999 -10 8 C-11.709 11 -11.709 11 -13 14 C-13.33 12.68 -13.66 11.36 -14 10 C-20.435 10.495 -20.435 10.495 -27 11 C-27 10.34 -27 9.68 -27 9 C-22.38 8.67 -17.76 8.34 -13 8 C-13 6.68 -13 5.36 -13 4 C-4.405 -0.777 -4.405 -0.777 0 0 Z " fill="#F7F9F9" transform="translate(747,636)"/>
<path d="M0 0 C2.102 3.153 2.243 3.861 2.195 7.449 C2.189 8.297 2.182 9.144 2.176 10.018 C2.159 10.899 2.142 11.78 2.125 12.688 C2.116 13.58 2.107 14.473 2.098 15.393 C2.074 17.595 2.038 19.798 2 22 C-0.97 22 -3.94 22 -7 22 C-7 16.39 -7 10.78 -7 5 C-8.65 4.67 -10.3 4.34 -12 4 C-11.67 3.34 -11.34 2.68 -11 2 C-6.125 2.875 -6.125 2.875 -5 4 C-4.927 6.697 -4.908 9.367 -4.938 12.062 C-4.942 12.821 -4.947 13.58 -4.951 14.361 C-4.963 16.241 -4.981 18.12 -5 20 C-3.35 20 -1.7 20 0 20 C0 13.4 0 6.8 0 0 Z " fill="#FAFBF9" transform="translate(413,685)"/>
<path d="M0 0 C6.625 -0.25 6.625 -0.25 10 2 C10.312 5.5 10.312 5.5 10 9 C8 11 8 11 5 11.25 C2 11 2 11 0 9 C-0.195 6.836 -0.195 6.836 -0.125 4.375 C-0.098 3.149 -0.098 3.149 -0.07 1.898 C-0.047 1.272 -0.024 0.645 0 0 Z " fill="#EAF0F1" transform="translate(217,640)"/>
<path d="M0 0 C4 1 4 1 5 2 C5.99 1.34 6.98 0.68 8 0 C11.188 -0.125 11.188 -0.125 14 0 C13.165 0.34 13.165 0.34 12.312 0.688 C9.067 2.529 7.238 3.953 5 7 C4.577 10.704 4.708 14.289 5 18 C4.34 18 3.68 18 3 18 C3 21.63 3 25.26 3 29 C2.67 29 2.34 29 2 29 C2 24.38 2 19.76 2 15 C1.67 19.95 1.34 24.9 1 30 C0.67 30 0.34 30 0 30 C0 20.1 0 10.2 0 0 Z " fill="#0D2B4F" transform="translate(635,633)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C11.01 0.495 11.01 0.495 10 1 C10.023 2.085 10.046 3.171 10.07 4.289 C10.089 5.734 10.107 7.18 10.125 8.625 C10.142 9.338 10.159 10.051 10.176 10.785 C10.217 15.261 9.691 18.779 8 23 C7.34 22.67 6.68 22.34 6 22 C6 15.4 6 8.8 6 2 C4.35 2.33 2.7 2.66 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#FAFBF9" transform="translate(249,631)"/>
<path d="M0 0 C1 2 1 2 1 5 C-1.491 5.687 -3.379 6 -6 6 C-6 6.66 -6 7.32 -6 8 C-4.68 8.66 -3.36 9.32 -2 10 C-2.33 10.66 -2.66 11.32 -3 12 C-2.34 12 -1.68 12 -1 12 C-1.33 12.99 -1.66 13.98 -2 15 C-3.65 15.33 -5.3 15.66 -7 16 C-7.495 13.525 -7.495 13.525 -8 11 C-8.33 12.98 -8.66 14.96 -9 17 C-9.33 17 -9.66 17 -10 17 C-10 12.71 -10 8.42 -10 4 C-9.34 4 -8.68 4 -8 4 C-8 3.34 -8 2.68 -8 2 C-5.36 1.34 -2.72 0.68 0 0 Z " fill="#DBE1DF" transform="translate(210,631)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 10.89 8 21.78 8 33 C5.36 33 2.72 33 0 33 C0.33 31.68 0.66 30.36 1 29 C1.33 29.66 1.66 30.32 2 31 C3.32 31 4.64 31 6 31 C6 21.43 6 11.86 6 2 C4.35 2 2.7 2 1 2 C0.67 2.99 0.34 3.98 0 5 C0 3.35 0 1.7 0 0 Z " fill="#FAFBF9" transform="translate(675,674)"/>
<path d="M0 0 C1.675 0.286 3.344 0.618 5 1 C5.66 2.98 6.32 4.96 7 7 C7.33 7 7.66 7 8 7 C8 8.98 8 10.96 8 13 C5.03 13 2.06 13 -1 13 C-1.544 9.942 -2 7.112 -2 4 C-1.34 4 -0.68 4 0 4 C-0.33 3.01 -0.66 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D0D8DB" transform="translate(579,629)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4.037 1.759 4.075 2.519 4.113 3.301 C4.212 4.792 4.212 4.792 4.312 6.312 C4.4 7.792 4.4 7.792 4.488 9.301 C5.056 12.297 5.532 13.263 8 15 C11.61 15.73 11.61 15.73 15 16 C15.66 14.68 16.32 13.36 17 12 C17 14.64 17 17.28 17 20 C11.367 20.24 6.455 19.385 1 18 C1.012 16.952 1.023 15.904 1.035 14.824 C1.045 13.445 1.054 12.066 1.062 10.688 C1.071 9.997 1.079 9.307 1.088 8.596 C1.103 5.452 1.002 3.006 0 0 Z " fill="#F3F5F3" transform="translate(767,638)"/>
<path d="M0 0 C2.677 0.868 3.657 1.503 5.25 3.812 C6 6 6 6 6 11 C1.38 11 -3.24 11 -8 11 C-8 10.34 -8 9.68 -8 9 C-4.7 9 -1.4 9 2 9 C1.34 7.35 0.68 5.7 0 4 C-4.79 3.766 -4.79 3.766 -9 6 C-9.33 4.68 -9.66 3.36 -10 2 C-6.299 -0.468 -4.378 -0.307 0 0 Z " fill="#0C3157" transform="translate(606,633)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.66 0.34 3.32 0 4 C-1.98 4 -3.96 4 -6 4 C-6 6.64 -6 9.28 -6 12 C-2.535 11.505 -2.535 11.505 1 11 C1.33 10.01 1.66 9.02 2 8 C2.99 8.33 3.98 8.66 5 9 C5 9.66 5 10.32 5 11 C2.319 13.681 0.65 14.896 -3.125 15.5 C-6 15 -6 15 -7.875 13.438 C-9.436 10.054 -9.46 7.684 -9 4 C-6.401 0.246 -4.495 -0.499 0 0 Z " fill="#F4F6F5" transform="translate(366,636)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.99 3 1.98 3 3 C3.99 2.67 4.98 2.34 6 2 C6.66 2.66 7.32 3.32 8 4 C7.01 4.66 6.02 5.32 5 6 C4.616 8.61 4.397 11.006 4.312 13.625 C4.278 14.331 4.244 15.038 4.209 15.766 C4.126 17.51 4.062 19.255 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#234565" transform="translate(452,633)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-1.98 2 -3.96 2 -6 2 C-4.875 3.356 -4.875 3.356 -1.938 3.062 C-0.968 3.042 0.001 3.021 1 3 C-0.32 3.33 -1.64 3.66 -3 4 C-3 5.65 -3 7.3 -3 9 C-4.32 9 -5.64 9 -7 9 C-7 9.99 -7 10.98 -7 12 C-5.68 12 -4.36 12 -3 12 C-3 15.96 -3 19.92 -3 24 C-3.33 24 -3.66 24 -4 24 C-4 20.37 -4 16.74 -4 13 C-6.31 13.33 -8.62 13.66 -11 14 C-11.434 9.375 -10.286 5.026 -8 1 C-5.072 0.024 -3.044 -0.082 0 0 Z " fill="#D9E0E2" transform="translate(381,624)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 7.26 4 14.52 4 22 C2.68 22 1.36 22 0 22 C0 14.74 0 7.48 0 0 Z " fill="#133D5F" transform="translate(449,683)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 7.26 4 14.52 4 22 C2.68 22 1.36 22 0 22 C0 14.74 0 7.48 0 0 Z " fill="#133B5F" transform="translate(381,683)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 6.93 4 13.86 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#07284D" transform="translate(389,684)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C6.049 2.06 6.098 3.119 6.148 4.211 C6.223 5.599 6.299 6.987 6.375 8.375 C6.406 9.074 6.437 9.772 6.469 10.492 C6.595 14.702 6.595 14.702 9 18 C13.609 19.107 13.609 19.107 18 18 C17.67 19.32 17.34 20.64 17 22 C15.585 22.109 14.168 22.186 12.75 22.25 C11.961 22.296 11.172 22.343 10.359 22.391 C8 22 8 22 5.719 20.469 C3.381 17.11 3.557 14.18 3.75 10.25 C3.768 9.553 3.786 8.855 3.805 8.137 C3.852 6.424 3.923 4.712 4 3 C2.35 2.67 0.7 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#FAFAF8" transform="translate(416,686)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 1.66 3.34 2.32 3 3 C3.66 3.33 4.32 3.66 5 4 C4.67 9.61 4.34 15.22 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#254D6C" transform="translate(812,633)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 6.93 4 13.86 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#143759" transform="translate(444,633)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 6.93 1 13.86 1 21 C-2.3 21 -5.6 21 -9 21 C-9.163 18.982 -9.326 16.964 -9.488 14.945 C-9.874 12.605 -9.874 12.605 -13 11 C-12.01 10.67 -11.02 10.34 -10 10 C-10.99 10 -11.98 10 -13 10 C-13.33 13.3 -13.66 16.6 -14 20 C-14.33 20 -14.66 20 -15 20 C-15.027 17.521 -15.047 15.042 -15.062 12.562 C-15.075 11.502 -15.075 11.502 -15.088 10.42 C-15.097 8.613 -15.052 6.806 -15 5 C-14 4 -14 4 -11.438 3.938 C-10.231 3.968 -10.231 3.968 -9 4 C-7.498 7.003 -7.907 9.791 -7.938 13.125 C-7.947 14.406 -7.956 15.688 -7.965 17.008 C-7.976 17.995 -7.988 18.983 -8 20 C-5.36 20 -2.72 20 0 20 C0 13.4 0 6.8 0 0 Z M-14 6 C-13.67 6.66 -13.34 7.32 -13 8 C-12.34 8 -11.68 8 -11 8 C-11 7.34 -11 6.68 -11 6 C-11.99 6 -12.98 6 -14 6 Z " fill="#E6E8E3" transform="translate(833,636)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 6.93 4 13.86 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#204A6C" transform="translate(345,633)"/>
<path d="M0 0 C-2.222 2.222 -4.085 2.926 -7 4 C-7 5.32 -7 6.64 -7 8 C-4.168 8.334 -4.168 8.334 -1 8 C1.377 6.125 1.377 6.125 3 4 C2.624 6.634 1.948 8.072 0.375 10.25 C-2.904 12.666 -5.021 12.506 -9 12 C-10.938 10.875 -10.938 10.875 -12 9 C-12.5 6 -12.5 6 -12 3 C-8.276 -0.385 -4.867 -0.286 0 0 Z " fill="#1B4467" transform="translate(335,642)"/>
<path d="M0 0 C0.702 0.005 1.404 0.009 2.127 0.014 C3.856 0.025 5.584 0.043 7.312 0.062 C4.411 2.276 1.467 4.223 -1.688 6.062 C-1.688 4.742 -1.688 3.423 -1.688 2.062 C-3.008 2.062 -4.327 2.062 -5.688 2.062 C-5.688 5.362 -5.688 8.663 -5.688 12.062 C-6.678 12.558 -6.678 12.558 -7.688 13.062 C-7.688 12.403 -7.688 11.742 -7.688 11.062 C-8.347 11.062 -9.008 11.062 -9.688 11.062 C-9.688 10.403 -9.688 9.742 -9.688 9.062 C-10.347 8.732 -11.008 8.403 -11.688 8.062 C-11.028 7.732 -10.367 7.403 -9.688 7.062 C-10.018 5.082 -10.347 3.102 -10.688 1.062 C-7.069 -0.144 -3.762 -0.046 0 0 Z " fill="#FAFAF9" transform="translate(704.6875,630.9375)"/>
<path d="M0 0 C1.25 3.062 1.25 3.062 2 6 C3.32 5.01 4.64 4.02 6 3 C6 3.99 6 4.98 6 6 C5.34 6 4.68 6 4 6 C3.567 6.959 3.567 6.959 3.125 7.938 C2 10 2 10 0 11 C-0.656 13.527 -0.656 13.527 -1 16 C-1.66 15.67 -2.32 15.34 -3 15 C-2.67 11.37 -2.34 7.74 -2 4 C-4.97 3.67 -7.94 3.34 -11 3 C-10.01 2.67 -9.02 2.34 -8 2 C-8 1.34 -8 0.68 -8 0 C-5.007 -0.962 -2.878 -1.439 0 0 Z " fill="#E4E9EB" transform="translate(592,629)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4.042 3.333 4.041 5.667 4 8 C3 9 3 9 -0.062 9.062 C-1.032 9.042 -2.001 9.021 -3 9 C-3 11.64 -3 14.28 -3 17 C-1.35 17 0.3 17 2 17 C1.01 17.99 0.02 18.98 -1 20 C-2.32 19.34 -3.64 18.68 -5 18 C-5 13.71 -5 9.42 -5 5 C-3.02 5 -1.04 5 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#F0F2F1" transform="translate(428,631)"/>
<path d="M0 0 C0.25 2.812 0.25 2.812 0 6 C-1.625 7.875 -1.625 7.875 -4 9 C-7.25 9.188 -7.25 9.188 -10 9 C-10.625 6.125 -10.625 6.125 -11 3 C-7.724 -0.276 -4.489 -0.125 0 0 Z " fill="#DDE3E5" transform="translate(537,693)"/>
<path d="M0 0 C3.398 2.705 4.766 4.11 5.297 8.402 C5.311 10.627 5.221 12.788 5 15 C4.01 15.495 4.01 15.495 3 16 C3.33 13.03 3.66 10.06 4 7 C2.68 6.67 1.36 6.34 0 6 C0 5.34 0 4.68 0 4 C-2.97 4 -5.94 4 -9 4 C-9.99 6.97 -10.98 9.94 -12 13 C-12.33 13 -12.66 13 -13 13 C-13 3 -13 3 -11.562 1.312 C-7.71 -0.661 -4.23 -0.903 0 0 Z " fill="#10395E" transform="translate(629,684)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 9.57 4 19.14 4 29 C2.68 29 1.36 29 0 29 C0 27.68 0 26.36 0 25 C0.66 25 1.32 25 2 25 C1.505 17.575 1.505 17.575 1 10 C0.67 10 0.34 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#0D375B" transform="translate(677,676)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.492 3.51 -0.016 4.021 -0.539 4.547 C-9.923 14.458 -13.765 24.744 -16 38 C-16.99 38 -17.98 38 -19 38 C-15.656 22.943 -11.19 11.19 0 0 Z " fill="#D1DAE0" transform="translate(417,386)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.043 1.666 1.041 3.334 1 5 C0.67 5.33 0.34 5.66 0 6 C0.99 6.66 1.98 7.32 3 8 C1.68 8 0.36 8 -1 8 C-1 8.66 -1 9.32 -1 10 C-1.619 10.289 -2.238 10.577 -2.875 10.875 C-5.133 11.937 -5.133 11.937 -7 14 C-7.351 13.196 -7.701 12.391 -8.062 11.562 C-9.857 8.613 -9.857 8.613 -13.688 8.188 C-14.781 8.126 -15.874 8.064 -17 8 C-17 7.67 -17 7.34 -17 7 C-16.325 6.879 -15.649 6.758 -14.953 6.633 C-13.615 6.381 -13.615 6.381 -12.25 6.125 C-11.368 5.963 -10.487 5.8 -9.578 5.633 C-5.423 4.613 -2.927 3.066 0 0 Z " fill="#E7E9E7" transform="translate(419,676)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.868 2.078 5.719 4.162 6.562 6.25 C7.038 7.41 7.514 8.57 8.004 9.766 C8.998 12.992 9.069 13.952 8 17 C8.271 19.009 8.594 21.014 9 23 C5.685 19.277 4.387 15.011 2.812 10.375 C2.54 9.599 2.267 8.823 1.986 8.023 C0 2.288 0 2.288 0 0 Z " fill="#1B4160" transform="translate(351,676)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.959 1.237 1.918 2.475 1.875 3.75 C1.857 7.105 2.361 9.05 4 12 C3.67 12.99 3.34 13.98 3 15 C2.196 13.546 2.196 13.546 1.375 12.062 C-0.769 9.408 -1.911 9.016 -5.344 8.414 C-7.834 8.265 -10.327 8.161 -12.82 8.105 C-16 8 -16 8 -19 7 C-19 5.02 -19 3.04 -19 1 C-18.67 1 -18.34 1 -18 1 C-18 2.65 -18 4.3 -18 6 C-12.39 6 -6.78 6 -1 6 C-1 4.35 -1 2.7 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F9FAF9" transform="translate(592,639)"/>
<path d="M0 0 C12.15 0.054 21.253 5.575 29.859 13.82 C34.887 18.939 34.887 18.939 37 22 C36.505 23.485 36.505 23.485 36 25 C35.611 24.443 35.221 23.886 34.82 23.312 C25.821 11.037 14.637 4.659 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DFE5E9" transform="translate(587,374)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.289 1.114 3.577 2.227 3.875 3.375 C4.692 7.003 4.692 7.003 7 9 C11.053 9.345 12.548 9.302 16 7 C16 8.32 16 9.64 16 11 C14 13 14 13 10.25 13.438 C7.107 13.34 5.078 12.823 2.5 11 C-0.075 7.911 -0.362 7.431 -0.375 3.688 C-0.251 2.471 -0.128 1.254 0 0 Z " fill="#0E3459" transform="translate(659,641)"/>
<path d="M0 0 C4.75 0.75 4.75 0.75 7 3 C7.145 5.581 7.187 8.049 7.125 10.625 C7.116 11.331 7.107 12.038 7.098 12.766 C7.074 14.511 7.038 16.255 7 18 C8.65 18 10.3 18 12 18 C12 16.02 12 14.04 12 12 C12.66 12 13.32 12 14 12 C13.67 14.64 13.34 17.28 13 20 C10.36 20 7.72 20 5 20 C5 14.39 5 8.78 5 3 C3.02 3 1.04 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#FBFBF9" transform="translate(465,687)"/>
<path d="M0 0 C3.625 0.25 3.625 0.25 5.562 1.25 C7.283 4.489 7.417 7.736 6.625 11.25 C4.125 13.75 4.125 13.75 1.625 15.25 C0.305 14.59 -1.015 13.93 -2.375 13.25 C-2.375 12.92 -2.375 12.59 -2.375 12.25 C-0.725 11.92 0.925 11.59 2.625 11.25 C2.625 8.94 2.625 6.63 2.625 4.25 C-0.233 3.565 -0.233 3.565 -3.375 3.25 C-4.365 3.91 -5.355 4.57 -6.375 5.25 C-3.977 0.295 -3.977 0.295 0 0 Z " fill="#F2F4F3" transform="translate(490.375,686.75)"/>
<path d="M0 0 C1.31 2.519 2.465 5.072 3.562 7.688 C4.668 10.791 4.668 10.791 6 12 C8.03 11.968 10.061 11.932 12.09 11.84 C14 12 14 12 16 14 C12.315 16.457 9.28 16.701 5 16 C2.19 14.206 0.491 12.981 -1 10 C-1.095 8.482 -1.13 6.959 -1.125 5.438 C-1.129 4.229 -1.129 4.229 -1.133 2.996 C-1 1 -1 1 0 0 Z " fill="#12385D" transform="translate(512,638)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 6.28 5 11.56 5 17 C6.65 17 8.3 17 10 17 C10 12.05 10 7.1 10 2 C10.33 2 10.66 2 11 2 C11 7.61 11 13.22 11 19 C8.36 19 5.72 19 3 19 C2.927 17.285 2.927 17.285 2.852 15.535 C2.777 14.044 2.701 12.553 2.625 11.062 C2.579 9.93 2.579 9.93 2.531 8.775 C2.427 6.845 2.221 4.921 2 3 C1.34 2.67 0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#F9FAFA" transform="translate(462,637)"/>
<path d="M0 0 C1.667 3.333 3.333 6.667 5 10 C10.531 10.439 10.531 10.439 15.5 8.5 C15.995 8.005 16.49 7.51 17 7 C17 8.65 17 10.3 17 12 C17.99 12.33 18.98 12.66 20 13 C17.041 13.081 14.084 13.14 11.125 13.188 C9.862 13.225 9.862 13.225 8.574 13.264 C7.769 13.273 6.963 13.283 6.133 13.293 C5.389 13.309 4.646 13.324 3.879 13.341 C3.259 13.228 2.639 13.116 2 13 C-0.29 9.565 -0.178 8.015 0 4 C-0.66 4 -1.32 4 -2 4 C-1 1 -1 1 0 0 Z " fill="#F9FAF9" transform="translate(234,643)"/>
<path d="M0 0 C1.05 0.253 1.05 0.253 2.121 0.512 C4 1 4 1 6 2 C6.33 1.67 6.66 1.34 7 1 C7.66 1.33 8.32 1.66 9 2 C7.123 4.078 5.519 5.741 3 7 C2.34 7 1.68 7 1 7 C0.423 6.361 -0.155 5.721 -0.75 5.062 C-2.735 2.821 -2.735 2.821 -4.961 2.836 C-5.675 2.869 -6.389 2.903 -7.125 2.938 C-8.404 2.958 -9.683 2.979 -11 3 C-11.66 2.01 -12.32 1.02 -13 0 C-8.179 -2.712 -4.957 -1.528 0 0 Z " fill="#EEEFED" transform="translate(505,631)"/>
<path d="M0 0 C2.19 3.285 2.998 6.207 4 10 C10.084 10.198 10.084 10.198 16 9 C15.625 10.938 15.625 10.938 15 13 C12.66 14.17 11.166 14.166 8.562 14.188 C7.389 14.209 7.389 14.209 6.191 14.23 C4 14 4 14 1 12 C-0.577 8.271 -0.066 3.968 0 0 Z " fill="#163B60" transform="translate(716,640)"/>
<path d="M0 0 C2.566 3.422 2.364 6.846 2 11 C0.5 13.938 0.5 13.938 -1 16 C-1.66 15.67 -2.32 15.34 -3 15 C-2.34 11.37 -1.68 7.74 -1 4 C-1.99 3.67 -2.98 3.34 -4 3 C-4 2.34 -4 1.68 -4 1 C-6.97 0.505 -6.97 0.505 -10 0 C-9 -2 -9 -2 -7.25 -2.812 C-3.998 -3.083 -2.553 -1.948 0 0 Z " fill="#1B486B" transform="translate(654,636)"/>
<path d="M0 0 C1.582 0.007 3.164 0.019 4.746 0.035 C5.552 0.04 6.359 0.044 7.189 0.049 C9.188 0.061 11.186 0.079 13.184 0.098 C13.514 1.088 13.844 2.078 14.184 3.098 C13.52 3.063 12.856 3.028 12.172 2.992 C5.204 2.771 5.204 2.771 -0.816 6.098 C-0.816 4.778 -0.816 3.458 -0.816 2.098 C-1.806 2.098 -2.796 2.098 -3.816 2.098 C-3.816 9.028 -3.816 15.958 -3.816 23.098 C-4.146 23.098 -4.476 23.098 -4.816 23.098 C-4.845 19.452 -4.863 15.806 -4.879 12.16 C-4.887 11.119 -4.896 10.078 -4.904 9.006 C-4.909 7.522 -4.909 7.522 -4.914 6.008 C-4.919 5.091 -4.925 4.175 -4.93 3.231 C-4.73 -0.528 -3.951 0.101 0 0 Z " fill="#DBE3E5" transform="translate(291.81640625,630.90234375)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C2.67 2.99 2.34 3.98 2 5 C1.34 4.67 0.68 4.34 0 4 C-2.958 3.583 -2.958 3.583 -6 4 C-7.805 5.655 -7.805 5.655 -9 8 C-9.989 9.673 -10.986 11.342 -12 13 C-12.33 13 -12.66 13 -13 13 C-13.602 5.902 -13.602 5.902 -11 2 C-6.898 -0.735 -4.866 -0.549 0 0 Z " fill="#0E395F" transform="translate(673,684)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1.66 1.68 2.32 1 3 C0.758 4.981 0.758 4.981 0.875 7.125 C0.937 9.043 0.937 9.043 1 11 C2.98 10.67 4.96 10.34 7 10 C7 9.01 7 8.02 7 7 C7.99 6.67 8.98 6.34 10 6 C10.66 5.34 11.32 4.68 12 4 C11.499 8.381 10.218 10.956 7 14 C3.938 14.5 3.938 14.5 1 14 C-1.174 12.372 -1.915 11.514 -2.363 8.809 C-2.347 8.026 -2.33 7.244 -2.312 6.438 C-2.309 5.652 -2.305 4.867 -2.301 4.059 C-2 2 -2 2 0 0 Z " fill="#F4F6F6" transform="translate(665,637)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.33 3.96 7.66 7.92 8 12 C7.67 11.34 7.34 10.68 7 10 C4.678 9.593 2.343 9.256 0 9 C0 6.03 0 3.06 0 0 Z " fill="#E1E4DF" transform="translate(778,639)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C6 1.66 6 2.32 6 3 C7.65 3 9.3 3 11 3 C11 3.99 11 4.98 11 6 C9.35 6 7.7 6 6 6 C5.67 6.99 5.34 7.98 5 9 C3.35 9 1.7 9 0 9 C-1.125 6.75 -1.125 6.75 -2 4 C-1.125 1.688 -1.125 1.688 0 0 Z M1 1 C2 3 2 3 2 3 Z " fill="#E5E8E4" transform="translate(686,639)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.505 2.908 1.01 3.815 0.5 4.75 C-1.965 10.565 -2.165 17.366 -0.238 23.375 C1.654 27.323 4.114 30.702 7 34 C4 34 4 34 2.465 32.496 C-2.7 25.535 -5.211 18.729 -4 10 C-2.893 6.553 -1.533 3.278 0 0 Z " fill="#CFD8DC" transform="translate(317,389)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 5.61 2 11.22 2 17 C4.64 17 7.28 17 10 17 C10 15.35 10 13.7 10 12 C10.66 11.67 11.32 11.34 12 11 C12.99 12.65 13.98 14.3 15 16 C14.216 16.619 13.433 17.237 12.625 17.875 C10.032 19.899 10.032 19.899 8 22 C3.25 20.125 3.25 20.125 1 19 C0.688 16.451 0.487 13.996 0.375 11.438 C0.336 10.727 0.298 10.016 0.258 9.283 C0.163 7.523 0.081 5.761 0 4 C-0.99 3.67 -1.98 3.34 -3 3 C-2 2 -1 1 0 0 Z " fill="#DCDFDA" transform="translate(468,690)"/>
<path d="M0 0 C0.928 0.082 1.856 0.165 2.812 0.25 C2.812 0.91 2.812 1.57 2.812 2.25 C1.163 2.91 -0.487 3.57 -2.188 4.25 C-2.188 6.23 -2.188 8.21 -2.188 10.25 C1.284 11 1.284 11 4.812 11.25 C4.482 12.24 4.153 13.23 3.812 14.25 C1.832 14.58 -0.148 14.91 -2.188 15.25 C-4.736 12.543 -6.092 10.917 -6.625 7.188 C-5.921 2.458 -4.967 0.39 0 0 Z " fill="#F5F7F7" transform="translate(602.1875,686.75)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C5.34 1.66 4.68 2.32 4 3 C4.294 4.343 4.628 5.677 5 7 C4.67 7.66 4.34 8.32 4 9 C1.438 9.625 1.438 9.625 -1 10 C-1.33 12.64 -1.66 15.28 -2 18 C-2.33 18 -2.66 18 -3 18 C-3.082 15.938 -3.139 13.875 -3.188 11.812 C-3.222 10.664 -3.257 9.515 -3.293 8.332 C-2.976 4.73 -2.085 2.906 0 0 Z " fill="#E8ECEC" transform="translate(444,621)"/>
<path d="M0 0 C2 3 2 3 3.25 5.688 C4.821 8.324 4.821 8.324 7.938 8.438 C10.775 8.032 12.582 7.488 15 6 C15 7.32 15 8.64 15 10 C11.322 12.452 8.274 12.715 4 12 C1 10 1 10 -1 8 C-0.752 7.134 -0.752 7.134 -0.5 6.25 C0.145 3.733 0.145 3.733 0 0 Z " fill="#143D61" transform="translate(355,642)"/>
<path d="M0 0 C-0.36 2.664 -0.712 4.582 -2.25 6.812 C-4.633 8.43 -6.179 8.362 -9 8 C-9.66 7.34 -10.32 6.68 -11 6 C-10.875 4 -10.875 4 -10 2 C-6.82 -0.355 -3.849 -0.175 0 0 Z " fill="#EBEFF0" transform="translate(565,643)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8.66 0.99 9.32 1.98 10 3 C9.01 3.66 8.02 4.32 7 5 C7 4.01 7 3.02 7 2 C5.68 2 4.36 2 3 2 C3 11.57 3 21.14 3 31 C2.67 31 2.34 31 2 31 C1.505 23.575 1.505 23.575 1 16 C0.67 16 0.34 16 0 16 C0 10.72 0 5.44 0 0 Z " fill="#C0CFD7" transform="translate(484,631)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 9.57 1 19.14 1 29 C2.65 29.33 4.3 29.66 6 30 C6 30.33 6 30.66 6 31 C3.69 31 1.38 31 -1 31 C-1.113 20.603 -0.92 10.365 0 0 Z M6 21 C6.33 21 6.66 21 7 21 C7 23.97 7 26.94 7 30 C6.67 30 6.34 30 6 30 C6 27.03 6 24.06 6 21 Z " fill="#FAFBFA" transform="translate(485,633)"/>
<path d="M0 0 C0.36 5.4 0.36 5.4 -1.125 7.875 C-3.78 9.468 -5.968 9.202 -9 9 C-9.625 6.625 -9.625 6.625 -10 4 C-7.413 1.413 -3.745 0 0 0 Z " fill="#F3F5F5" transform="translate(337,642)"/>
<path d="M0 0 C4.75 0.75 4.75 0.75 7 3 C7.496 6.723 7.212 8.602 5.438 11.938 C3 14 3 14 -0.75 13.812 C-1.822 13.544 -2.895 13.276 -4 13 C-3.67 12.01 -3.34 11.02 -3 10 C-1.35 9.67 0.3 9.34 2 9 C2.794 7.253 2.794 7.253 3 5 C1.646 2.27 1.646 2.27 0 0 Z " fill="#F7F9F9" transform="translate(497,637)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 17.16 1.66 34.32 2 52 C1.01 52.495 1.01 52.495 0 53 C-0.732 48.15 -1.023 43.6 -0.908 38.712 C-0.888 37.851 -0.868 36.991 -0.848 36.104 C-0.815 34.716 -0.815 34.716 -0.781 33.301 C-0.759 32.351 -0.737 31.4 -0.714 30.421 C-0.643 27.385 -0.571 24.349 -0.5 21.312 C-0.452 19.255 -0.404 17.197 -0.355 15.139 C-0.237 10.092 -0.119 5.046 0 0 Z " fill="#FAFBFA" transform="translate(685,232)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.133 0.638 2.266 1.276 2.402 1.934 C2.579 2.76 2.756 3.586 2.938 4.438 C3.112 5.261 3.286 6.085 3.465 6.934 C3.808 8.977 3.808 8.977 5 10 C7.328 10.368 9.662 10.702 12 11 C11.67 11.99 11.34 12.98 11 14 C6.75 14.417 4.647 14.44 1.062 12 C-1.726 7.944 -1.484 5.838 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#174264" transform="translate(214,640)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.33 6.93 3.66 13.86 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#1D3D5F" transform="translate(544,633)"/>
<path d="M0 0 C-0.99 0.33 -1.98 0.66 -3 1 C-3 1.66 -3 2.32 -3 3 C-2.34 3 -1.68 3 -1 3 C-1 5.97 -1 8.94 -1 12 C-2.333 12 -3.667 12 -5 12 C-5 13.98 -5 15.96 -5 18 C-5.66 18.33 -6.32 18.66 -7 19 C-7.168 16.729 -7.334 14.458 -7.5 12.188 C-7.639 10.291 -7.639 10.291 -7.781 8.355 C-7.919 6.24 -8 4.12 -8 2 C-4.772 -0.152 -3.716 -0.201 0 0 Z " fill="#E4E9E7" transform="translate(465,636)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.01 4.33 -0.98 4.66 -2 5 C-3.102 7.632 -3.102 7.632 -4 11 C-4.766 13.188 -5.54 15.374 -6.316 17.559 C-8.022 23.65 -8.132 29.67 -8.062 35.938 C-8.058 36.717 -8.053 37.496 -8.049 38.299 C-8.037 40.199 -8.019 42.1 -8 44 C-12.027 37.64 -10.527 27.132 -9.438 19.938 C-8.018 13.76 -4.541 4.541 0 0 Z " fill="#1C4663" transform="translate(736,242)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.25 4.875 0.25 4.875 -2 6 C-3.477 6.095 -4.958 6.13 -6.438 6.125 C-7.611 6.129 -7.611 6.129 -8.809 6.133 C-11 6 -11 6 -14 5 C-14.99 5.99 -15.98 6.98 -17 8 C-17 7.34 -17 6.68 -17 6 C-18.98 6 -20.96 6 -23 6 C-23 5.67 -23 5.34 -23 5 C-20.69 5 -18.38 5 -16 5 C-15.67 4.01 -15.34 3.02 -15 2 C-13.753 2.035 -13.753 2.035 -12.48 2.07 C-11.394 2.088 -10.307 2.106 -9.188 2.125 C-8.109 2.148 -7.03 2.171 -5.918 2.195 C-2.806 2.23 -2.806 2.23 0 0 Z " fill="#F1F3F1" transform="translate(675,702)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.96 1.66 7.92 2 12 C2.33 8.37 2.66 4.74 3 1 C3.33 1 3.66 1 4 1 C4.33 5.29 4.66 9.58 5 14 C7.475 14.99 7.475 14.99 10 16 C9.67 16.99 9.34 17.98 9 19 C3.607 19.36 3.607 19.36 1.312 18 C-0.768 14.83 -0.312 11.467 -0.188 7.812 C-0.174 7.063 -0.16 6.313 -0.146 5.541 C-0.111 3.694 -0.057 1.847 0 0 Z " fill="#123A5D" transform="translate(422,686)"/>
<path d="M0 0 C3.7 2.467 4.104 4.192 5.625 8.312 C6.05 9.443 6.476 10.574 6.914 11.738 C8.554 16.664 9.155 20.997 9.188 26.188 C9.202 27.233 9.216 28.278 9.23 29.355 C9 32 9 32 7 34 C6.944 32.55 6.944 32.55 6.887 31.07 C6.363 20.689 4.792 12.355 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C5D1D7" transform="translate(624,398)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C2.99 2.67 3.98 2.34 5 2 C5.33 1.34 5.66 0.68 6 0 C7.32 0.66 8.64 1.32 10 2 C9.01 3.485 9.01 3.485 8 5 C5.066 5.512 5.066 5.512 1.562 5.688 C0.409 5.753 -0.745 5.819 -1.934 5.887 C-3.955 5.961 -5.978 6 -8 6 C-8 5.34 -8 4.68 -8 4 C-8.66 3.67 -9.32 3.34 -10 3 C-9.34 3 -8.68 3 -8 3 C-7.67 2.34 -7.34 1.68 -7 1 C-4.466 -0.267 -2.806 -0.281 0 0 Z " fill="#AAB39C" transform="translate(760,28)"/>
<path d="M0 0 C0.25 2.312 0.25 2.312 0 5 C-2.237 7.403 -3.463 7.963 -6.75 8.188 C-7.492 8.126 -8.235 8.064 -9 8 C-9.495 5.525 -9.495 5.525 -10 3 C-6.488 0.592 -4.273 -0.366 0 0 Z " fill="#F8F9F9" transform="translate(749,643)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-3.562 4.188 -3.562 4.188 -6 5 C-6 6.65 -6 8.3 -6 10 C-4.02 10.66 -2.04 11.32 0 12 C0 12.99 0 13.98 0 15 C-5.333 15.356 -5.333 15.356 -7.875 14 C-9.685 10.782 -9.641 7.542 -9 4 C-6.438 0.205 -4.497 -0.5 0 0 Z " fill="#F1F4F5" transform="translate(648,636)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 5.29 5 9.58 5 14 C4.01 13.67 3.02 13.34 2 13 C1.67 14.65 1.34 16.3 1 18 C0.67 18 0.34 18 0 18 C0 12.06 0 6.12 0 0 Z M3 9 C4 12 4 12 4 12 Z " fill="#EDEFEC" transform="translate(415,688)"/>
<path d="M0 0 C3.659 4.324 5 8.349 5 14 C5.99 14.33 6.98 14.66 8 15 C7.34 15 6.68 15 6 15 C6 16.65 6 18.3 6 20 C6.66 20 7.32 20 8 20 C7.34 24.29 6.68 28.58 6 33 C5.67 33 5.34 33 5 33 C4.944 31.585 4.944 31.585 4.887 30.141 C4.364 19.923 2.973 10.527 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C4D0D8" transform="translate(504,402)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C7.282 4.2 7.282 4.2 12 2 C12.99 2 13.98 2 15 2 C13.63 5.161 13.011 5.993 10 8 C5.405 8.499 2.632 7.906 -1 5 C-1.812 2.812 -1.812 2.812 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#194060" transform="translate(548,697)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.48 6.4 3.48 6.4 1.5 8.875 C-2.042 10.469 -5.177 10.595 -9 10 C-11.5 8.625 -11.5 8.625 -13 7 C-13 6.34 -13 5.68 -13 5 C-12.241 5.119 -11.481 5.237 -10.699 5.359 C-9.208 5.553 -9.208 5.553 -7.688 5.75 C-6.208 5.959 -6.208 5.959 -4.699 6.172 C-1.856 6.3 -1.856 6.3 -0.145 4.016 C0.233 3.35 0.611 2.685 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#13385C" transform="translate(628,644)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.625 4.625 1.625 4.625 1 7 C1.99 7 2.98 7 4 7 C4 8.32 4 9.64 4 11 C4.66 11 5.32 11 6 11 C5.34 12.98 4.68 14.96 4 17 C3.34 16.34 2.68 15.68 2 15 C1.01 15.495 1.01 15.495 0 16 C0.33 14.68 0.66 13.36 1 12 C-0.32 11.67 -1.64 11.34 -3 11 C-1.35 11 0.3 11 2 11 C2 10.34 2 9.68 2 9 C0.68 9 -0.64 9 -2 9 C-2.125 5.625 -2.125 5.625 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z M-2 16 C0 17 0 17 0 17 Z " fill="#DFE5E3" transform="translate(431,690)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.25 7.625 2.25 7.625 0 11 C-2.5 11.375 -2.5 11.375 -5 11 C-7 8 -7 8 -6.625 5.312 C-6.419 4.549 -6.212 3.786 -6 3 C-3 2 -3 2 0 2 C0 1.34 0 0.68 0 0 Z M-3 4 C-3.33 4.99 -3.66 5.98 -4 7 C-3.01 7.495 -3.01 7.495 -2 8 C-2.33 6.68 -2.66 5.36 -3 4 Z M0 4 C-0.495 5.98 -0.495 5.98 -1 8 C-0.34 8 0.32 8 1 8 C0.67 6.68 0.34 5.36 0 4 Z M-6 6 C-5 8 -5 8 -5 8 Z " fill="#DFE2E1" transform="translate(673,688)"/>
<path d="M0 0 C0 1.98 0 3.96 0 6 C1.32 6 2.64 6 4 6 C4 6.66 4 7.32 4 8 C2.02 8 0.04 8 -2 8 C-2 6.35 -2 4.7 -2 3 C-3.32 3 -4.64 3 -6 3 C-6 4.65 -6 6.3 -6 8 C-7.32 7.67 -8.64 7.34 -10 7 C-9.188 3.562 -9.188 3.562 -8 0 C-4.71 -1.097 -3.287 -0.8 0 0 Z " fill="#F4F6F4" transform="translate(428,675)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.34 2 2.68 2 2 2 C2 2.66 2 3.32 2 4 C0.625 5.625 0.625 5.625 -1 7 C-1.66 7 -2.32 7 -3 7 C-2.67 10.3 -2.34 13.6 -2 17 C-3.32 16.34 -4.64 15.68 -6 15 C-6.787 6.969 -6.787 6.969 -3.562 3 C-2.717 2.34 -1.871 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#275677" transform="translate(396,633)"/>
<path d="M0 0 C3.062 0.438 3.062 0.438 4.938 2.938 C5.309 3.763 5.68 4.587 6.062 5.438 C5.062 6.438 5.062 6.438 2.559 6.535 C1.045 6.518 1.045 6.518 -0.5 6.5 C-1.512 6.491 -2.524 6.482 -3.566 6.473 C-4.349 6.461 -5.131 6.449 -5.938 6.438 C-5.123 2.364 -4.391 0.654 0 0 Z " fill="#E2E8E9" transform="translate(723.9375,635.5625)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.325 5.14 3.43 7.34 1.125 10.875 C-1.671 13.671 -3.158 14.152 -7 14.188 C-8.052 14.209 -8.052 14.209 -9.125 14.23 C-11 14 -11 14 -13 12 C-11.422 11.907 -11.422 11.907 -9.812 11.812 C-5.264 10.843 -3.78 9.68 -1 6 C-0.12 2.818 -0.12 2.818 0 0 Z " fill="#10365A" transform="translate(805,640)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 2.485 3.01 2.485 2 4 C1.833 6.625 1.833 6.625 2 9 C2.99 8.01 3.98 7.02 5 6 C4.67 10.95 4.34 15.9 4 21 C2.68 21 1.36 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#0C335A" transform="translate(259,633)"/>
<path d="M0 0 C0.598 0.33 1.196 0.66 1.812 1 C4.054 2.208 4.054 2.208 7 2 C7.224 3.414 7.428 4.832 7.625 6.25 C7.741 7.039 7.857 7.828 7.977 8.641 C8.003 11.322 7.366 12.727 6 15 C4.68 15 3.36 15 2 15 C2.165 14.484 2.33 13.969 2.5 13.438 C3.386 9.121 3.865 5.406 1.5 1.562 C1.005 1.047 0.51 0.531 0 0 Z " fill="#1A4669" transform="translate(524,636)"/>
<path d="M0 0 C0.949 0.041 1.898 0.082 2.875 0.125 C3.256 2.116 3.588 4.118 3.875 6.125 C2.875 7.125 2.875 7.125 1.027 7.223 C-1.023 7.19 -3.074 7.158 -5.125 7.125 C-5.25 4.75 -5.25 4.75 -5.125 2.125 C-3.125 0.125 -3.125 0.125 0 0 Z " fill="#DEE4E6" transform="translate(222.125,629.875)"/>
<path d="M0 0 C0.34 0.866 0.34 0.866 0.688 1.75 C2.459 4.787 3.96 6.189 7 8 C10.102 8.554 12.902 8.591 16 8 C18.502 6.124 19.619 4.761 21 2 C21.66 2 22.32 2 23 2 C21.647 6.428 20.041 8.679 16 11 C11.262 11.865 7.891 11.438 3.562 9.312 C0.909 6.917 -0.181 5.457 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#CBD6DB" transform="translate(453,316)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.121 0.626 1.242 1.253 1.367 1.898 C1.535 2.716 1.702 3.533 1.875 4.375 C2.037 5.187 2.2 5.999 2.367 6.836 C2.867 9.187 2.867 9.187 5 11 C7.011 10.738 9.012 10.398 11 10 C12.32 10 13.64 10 15 10 C10.868 14.861 10.868 14.861 6.875 15.5 C4 15 4 15 2.125 13.625 C1 12 1 12 1 10 C0.67 9.67 0.34 9.34 0 9 C-0.072 7.481 -0.084 5.958 -0.062 4.438 C-0.053 3.611 -0.044 2.785 -0.035 1.934 C-0.024 1.296 -0.012 0.657 0 0 Z " fill="#153A5B" transform="translate(595,690)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.268 1.093 1.536 2.186 1.812 3.312 C2.902 6.695 3.337 7.891 6 10 C6.66 10.66 7.32 11.32 8 12 C7.67 12.66 7.34 13.32 7 14 C4.69 13.67 2.38 13.34 0 13 C-0.33 14.32 -0.66 15.64 -1 17 C-1.99 16.67 -2.98 16.34 -4 16 C-3.34 15.67 -2.68 15.34 -2 15 C-1.568 12.285 -1.568 12.285 -1.438 9.125 C-1.241 5.799 -1.056 3.169 0 0 Z " fill="#E5E9E8" transform="translate(544,693)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.125 6.875 4.125 6.875 3 8 C1.314 8.072 -0.375 8.084 -2.062 8.062 C-2.982 8.053 -3.901 8.044 -4.848 8.035 C-5.558 8.024 -6.268 8.012 -7 8 C-7 6.02 -7 4.04 -7 2 C-6.34 1.67 -5.68 1.34 -5 1 C-4.67 2.65 -4.34 4.3 -4 6 C-2.025 5.652 -2.025 5.652 0 5 C0.33 4.34 0.66 3.68 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#FAFAF8" transform="translate(208,640)"/>
<path d="M0 0 C0.782 -0.014 1.565 -0.028 2.371 -0.043 C4.562 0.188 4.562 0.188 7.562 2.188 C7.232 4.168 6.903 6.148 6.562 8.188 C5.903 6.867 5.242 5.548 4.562 4.188 C0.088 4.098 -4.043 4.33 -8.438 5.188 C-6.387 0.648 -4.905 0.04 0 0 Z " fill="#12375B" transform="translate(559.4375,632.8125)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.519 5.774 -1.185 9.959 -4 15 C-6.31 15 -8.62 15 -11 15 C-11 14.34 -11 13.68 -11 13 C-9.35 13 -7.7 13 -6 13 C-5.818 12.397 -5.636 11.793 -5.449 11.172 C-2.884 3.015 -2.884 3.015 0 0 Z " fill="#F6F6F5" transform="translate(372,692)"/>
<path d="M0 0 C1.848 0.34 1.848 0.34 4.062 0.938 C5.163 1.229 5.163 1.229 6.285 1.527 C7.134 1.761 7.134 1.761 8 2 C7.67 3.32 7.34 4.64 7 6 C6.34 6 5.68 6 5 6 C5 6.99 5 7.98 5 9 C0.125 8.125 0.125 8.125 -1 7 C-1.125 4 -1.125 4 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E2E4E0" transform="translate(600,690)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.488 7.029 2.93 13.02 2 19 C0.35 19 -1.3 19 -3 19 C-3 18.34 -3 17.68 -3 17 C-2.01 17 -1.02 17 0 17 C0 11.39 0 5.78 0 0 Z " fill="#F9FBFB" transform="translate(541,688)"/>
<path d="M0 0 C1.011 0.082 2.021 0.165 3.062 0.25 C5.062 3.25 5.062 3.25 5.062 6.25 C1.433 6.25 -2.197 6.25 -5.938 6.25 C-3.559 0.303 -3.559 0.303 0 0 Z " fill="#D8E2E5" transform="translate(602.9375,635.75)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8 5.61 8 11.22 8 17 C7.34 17 6.68 17 6 17 C6 12.05 6 7.1 6 2 C4.35 2 2.7 2 1 2 C1 4.97 1 7.94 1 11 C0.67 11 0.34 11 0 11 C0 7.37 0 3.74 0 0 Z " fill="#FBFCFA" transform="translate(234,631)"/>
<path d="M0 0 C1.897 0.957 3.739 2.022 5.566 3.105 C4.906 3.765 4.246 4.425 3.566 5.105 C2.803 4.61 2.04 4.115 1.254 3.605 C-3.72 1.582 -8.127 1.359 -13.434 2.105 C-15.889 3.19 -17.379 4.329 -19.434 6.105 C-19.434 5.115 -19.434 4.125 -19.434 3.105 C-14.1 -1.676 -6.659 -2.448 0 0 Z " fill="#BECBD2" transform="translate(474.43359375,221.89453125)"/>
<path d="M0 0 C0.557 0.268 1.114 0.536 1.688 0.812 C1.688 1.803 1.688 2.793 1.688 3.812 C0.697 3.812 -0.293 3.812 -1.312 3.812 C-1.312 4.472 -1.312 5.133 -1.312 5.812 C-1.973 5.812 -2.632 5.812 -3.312 5.812 C-3.312 6.803 -3.312 7.793 -3.312 8.812 C-4.303 8.812 -5.293 8.812 -6.312 8.812 C-6.312 11.783 -6.312 14.753 -6.312 17.812 C-6.642 17.812 -6.973 17.812 -7.312 17.812 C-8.066 6.372 -8.066 6.372 -6.34 2.801 C-4.33 0.656 -2.96 -0.24 0 0 Z " fill="#D4DEE1" transform="translate(298.3125,636.1875)"/>
<path d="M0 0 C1.274 0.427 2.544 0.867 3.812 1.312 C4.52 1.556 5.228 1.8 5.957 2.051 C8.145 3.067 9.409 4.208 11 6 C10.67 6.99 10.34 7.98 10 9 C9.34 9 8.68 9 8 9 C7.587 8.526 7.175 8.051 6.75 7.562 C4.322 5.395 2.185 5.39 -1 5 C-1.619 4.835 -2.237 4.67 -2.875 4.5 C-6.214 3.714 -9.594 3.397 -13 3 C-13 2.67 -13 2.34 -13 2 C-8.71 2 -4.42 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E5E9E6" transform="translate(822,628)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 3.97 2 6.94 2 10 C-0.31 10 -2.62 10 -5 10 C-5 12.97 -5 15.94 -5 19 C-5.33 19 -5.66 19 -6 19 C-6 15.37 -6 11.74 -6 8 C-4.02 8 -2.04 8 0 8 C0 7.01 0 6.02 0 5 C-1.98 5 -3.96 5 -6 5 C-5.67 4.01 -5.34 3.02 -5 2 C-3.68 2 -2.36 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#F8FAF9" transform="translate(782,628)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.01 4.33 -0.98 4.66 -2 5 C-2.33 11.27 -2.66 17.54 -3 24 C-2.34 24 -1.68 24 -1 24 C-0.67 25.98 -0.34 27.96 0 30 C-3.492 26.984 -4.789 23.759 -5.336 19.203 C-5.428 12.257 -4.563 7.024 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#2C5772" transform="translate(320,388)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C-2.63 4.66 -6.26 5.32 -10 6 C-10.66 4.35 -11.32 2.7 -12 1 C-7.673 -1.229 -4.716 -0.925 0 0 Z " fill="#EDF3F2" transform="translate(564,637)"/>
<path d="M0 0 C0.763 0.206 1.526 0.413 2.312 0.625 C2.643 1.945 2.972 3.265 3.312 4.625 C1.438 5.688 1.438 5.688 -0.688 6.625 C-1.347 6.295 -2.008 5.965 -2.688 5.625 C-3.347 7.275 -4.008 8.925 -4.688 10.625 C-6.688 9.625 -6.688 9.625 -7.688 7.625 C-6.697 7.625 -5.707 7.625 -4.688 7.625 C-4.688 6.305 -4.688 4.985 -4.688 3.625 C-5.347 3.625 -6.008 3.625 -6.688 3.625 C-5.079 0.409 -3.54 -0.494 0 0 Z " fill="#A3AC96" transform="translate(887.6875,592.375)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.66 10 1.32 10 2 C17.26 1.67 24.52 1.34 32 1 C25.921 5.053 18.089 4.521 11 4 C7.253 3.219 3.633 2.201 0 1 C0 0.67 0 0.34 0 0 Z " fill="#224A64" transform="translate(560,478)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.031 3.32 5.029 5.654 6 8 C7.54 9.88 7.54 9.88 9 11 C8.67 11.99 8.34 12.98 8 14 C6.35 14 4.7 14 3 14 C1.234 9.29 0.514 4.969 0 0 Z " fill="#2F5976" transform="translate(358,691)"/>
<path d="M0 0 C0 5.667 0 11.333 0 17 C-0.33 17 -0.66 17 -1 17 C-1 12.38 -1 7.76 -1 3 C-1.99 3 -2.98 3 -4 3 C-4 7.62 -4 12.24 -4 17 C-4.33 17 -4.66 17 -5 17 C-5.495 9.575 -5.495 9.575 -6 2 C-6.99 2.66 -7.98 3.32 -9 4 C-9 2.68 -9 1.36 -9 0 C-3.375 -1.125 -3.375 -1.125 0 0 Z " fill="#B2C2CA" transform="translate(642,681)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 1 2.02 1 1 1 C1.33 8.59 1.66 16.18 2 24 C6.627 24.373 6.627 24.373 9 23 C9 23.99 9 24.98 9 26 C3.59 26.361 3.59 26.361 1.312 24.969 C-0.709 21.937 -0.315 18.821 -0.188 15.312 C-0.167 14.26 -0.167 14.26 -0.146 13.186 C-0.111 11.457 -0.057 9.728 0 8 C-1.32 8 -2.64 8 -4 8 C-4 7.01 -4 6.02 -4 5 C-2.68 5 -1.36 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#082950" transform="translate(378,628)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.67 4.64 3.34 7.28 3 10 C0.625 10.188 0.625 10.188 -2 10 C-4 7 -4 7 -3.812 4.438 C-3 2 -3 2 0 0 Z M0 3 C1 5 1 5 1 5 Z " fill="#E2E4E2" transform="translate(400,638)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C-0.98 4 -2.96 4 -5 4 C-5 7.3 -5 10.6 -5 14 C-3.35 14 -1.7 14 0 14 C0 14.66 0 15.32 0 16 C-2 16.043 -4 16.041 -6 16 C-7 15 -7 15 -7.098 12.059 C-7.086 10.864 -7.074 9.669 -7.062 8.438 C-7.053 7.24 -7.044 6.042 -7.035 4.809 C-7.024 3.882 -7.012 2.955 -7 2 C-4.69 2 -2.38 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#FAFBFA" transform="translate(433,685)"/>
<path d="M0 0 C0.857 -0.028 1.714 -0.057 2.598 -0.086 C4.938 0.375 4.938 0.375 6.746 2.398 C7.139 3.051 7.532 3.703 7.938 4.375 C6 4.938 6 4.938 3.938 5.375 C3.607 5.045 3.278 4.715 2.938 4.375 C-0.899 4.23 -4.386 4.198 -8.062 5.375 C-8.393 4.385 -8.722 3.395 -9.062 2.375 C-5.958 0.574 -3.581 0.054 0 0 Z " fill="#184062" transform="translate(532.0625,683.625)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 1.33 3.34 1.66 3 2 C2.929 3.352 2.916 4.708 2.938 6.062 C2.958 7.362 2.979 8.661 3 10 C2.34 10 1.68 10 1 10 C1 14.29 1 18.58 1 23 C0.67 23 0.34 23 0 23 C0 18.38 0 13.76 0 9 C-0.99 9 -1.98 9 -3 9 C-3 8.01 -3 7.02 -3 6 C-2.01 6 -1.02 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#335F7C" transform="translate(569,678)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.133 0.638 1.266 1.276 1.402 1.934 C1.579 2.76 1.756 3.586 1.938 4.438 C2.112 5.261 2.286 6.085 2.465 6.934 C2.808 8.977 2.808 8.977 4 10 C5.347 10.231 6.704 10.412 8.062 10.562 C9.362 10.707 10.661 10.851 12 11 C12 11.66 12 12.32 12 13 C8.9 14.55 6.421 14.29 3 14 C0.062 12 0.062 12 -2 10 C-1.67 9.01 -1.34 8.02 -1 7 C-0.67 7.33 -0.34 7.66 0 8 C0 5.36 0 2.72 0 0 Z " fill="#143A5F" transform="translate(393,640)"/>
<path d="M0 0 C0 2.64 0 5.28 0 8 C0.99 8.33 1.98 8.66 3 9 C3 9.66 3 10.32 3 11 C5.97 11.495 5.97 11.495 9 12 C6.636 13.489 5.261 14.006 2.438 13.938 C0 13 0 13 -1.812 10.875 C-3.241 7.416 -3.218 4.708 -3 1 C-1 0 -1 0 0 0 Z " fill="#1B4568" transform="translate(790,639)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.66 7 1.32 7 2 C7.99 2 8.98 2 10 2 C10 2.66 10 3.32 10 4 C9.01 4 8.02 4 7 4 C7.33 5.32 7.66 6.64 8 8 C5.69 8 3.38 8 1 8 C-0.354 5.291 -0.065 2.991 0 0 Z M2 2 C1.67 2.66 1.34 3.32 1 4 C1.66 4 2.32 4 3 4 C2.67 3.34 2.34 2.68 2 2 Z M4 2 C4.33 2.66 4.66 3.32 5 4 C5.33 3.34 5.66 2.68 6 2 C5.34 2 4.68 2 4 2 Z " fill="#E1E5E1" transform="translate(665,640)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4.33 2.65 4.66 4.3 5 6 C3.02 6 1.04 6 -1 6 C-0.34 6.33 0.32 6.66 1 7 C0.67 8.32 0.34 9.64 0 11 C-0.66 11 -1.32 11 -2 11 C-2.289 10.196 -2.577 9.391 -2.875 8.562 C-3.733 5.867 -3.733 5.867 -6 5 C-5.196 4.732 -4.391 4.464 -3.562 4.188 C-2.717 3.796 -1.871 3.404 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#DFE4E4" transform="translate(574,627)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.689 3.327 3.355 5.661 4 8 C4.99 9.485 4.99 9.485 6 11 C5.01 11.99 4.02 12.98 3 14 C2.34 11.03 1.68 8.06 1 5 C0.34 5.66 -0.32 6.32 -1 7 C-2.32 7 -3.64 7 -5 7 C-5.66 7.66 -6.32 8.32 -7 9 C-6.67 6.69 -6.34 4.38 -6 2 C-3.525 2.99 -3.525 2.99 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#DFE2E0" transform="translate(782,624)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.381 5.103 -0.237 5.206 -0.875 5.312 C-3.365 5.875 -3.365 5.875 -5 9 C-5.536 8.691 -6.072 8.381 -6.625 8.062 C-9.398 6.822 -12 6.409 -15 6 C-15 5.01 -15 4.02 -15 3 C-10.05 3 -5.1 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EBEEEB" transform="translate(566,677)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C2.99 3.33 3.98 3.66 5 4 C7.006 7.695 7.664 10.849 7 15 C5.368 17.791 3.557 20.013 1 22 C-2.375 22.438 -2.375 22.438 -5 22 C-4.165 21.66 -4.165 21.66 -3.312 21.312 C-0.216 19.555 1.769 17.789 4 15 C4.662 11.427 4.678 9.199 2.867 5.996 C1.651 4.277 0.332 2.631 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CAD5DA" transform="translate(383,381)"/>
<path d="M0 0 C0.835 0.557 0.835 0.557 1.688 1.125 C4.057 2.295 4.057 2.295 6.75 1.125 C7.492 0.754 8.235 0.383 9 0 C9 0.66 9 1.32 9 2 C10.98 2 12.96 2 15 2 C15 2.33 15 2.66 15 3 C13.907 3.124 12.814 3.247 11.688 3.375 C7.926 3.755 7.926 3.755 5 6 C4.01 7.485 4.01 7.485 3 9 C0 7 0 7 -0.75 4.5 C-1 2 -1 2 0 0 Z M1 2 C1.33 2.99 1.66 3.98 2 5 C2 4.01 2 3.02 2 2 C1.67 2 1.34 2 1 2 Z " fill="#DFE4E5" transform="translate(542,681)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.66 2.31 2.32 4.62 3 7 C6.716 7.201 7.772 7.152 11 5 C10.625 7.438 10.625 7.438 10 10 C7.201 11.399 5.095 11.253 2 11 C0.125 9.938 0.125 9.938 -1 8 C-1.25 4.375 -1.25 4.375 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F4F6F6" transform="translate(516,640)"/>
<path d="M0 0 C2.818 0.434 4.264 1.188 6.625 2.875 C14.121 7.595 22.296 8.52 31 9 C31 9.33 31 9.66 31 10 C18.465 10.523 9.968 8.871 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DCE3E6" transform="translate(327,452)"/>
<path d="M0 0 C0 3.547 -0.597 4.005 -2.812 6.625 C-9.009 14.677 -9.555 23.064 -10 33 C-10.33 33 -10.66 33 -11 33 C-11.789 21.481 -11.162 12.531 -3.488 3.152 C-2.125 1.75 -2.125 1.75 0 0 Z " fill="#E4E9EC" transform="translate(758,248)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.577 1.835 1.577 1.835 0.125 2.688 C-3.195 5.145 -3.875 6.158 -5 10 C-5.938 12.75 -5.938 12.75 -7 15 C-7.99 15.33 -8.98 15.66 -10 16 C-10 17.32 -10 18.64 -10 20 C-10.99 20.33 -11.98 20.66 -13 21 C-11.052 13.791 -7.543 6.602 -2.312 1.25 C-1.549 0.838 -0.786 0.425 0 0 Z " fill="#254D69" transform="translate(533,224)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 4 2 4 1 7 C1.66 7 2.32 7 3 7 C2.67 7.66 2.34 8.32 2 9 C1.772 10.871 1.59 12.747 1.438 14.625 C1.293 16.399 1.149 18.173 1 20 C-4.94 20 -10.88 20 -17 20 C-17 19.67 -17 19.34 -17 19 C-11.39 19 -5.78 19 0 19 C-0.328 14.199 -0.328 14.199 -0.658 9.397 C-0.848 6.591 -1 3.814 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E0E3E0" transform="translate(463,688)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.423 2.018 1.846 4.036 2.27 6.055 C2.923 8.37 2.923 8.37 6 10 C8.332 10.079 10.668 10.088 13 10 C13.33 10.99 13.66 11.98 14 13 C13 14 13 14 9.25 14.438 C5.552 14.378 5.053 14.044 2 11.5 C-0.687 7.744 -0.386 4.482 0 0 Z " fill="#164166" transform="translate(489,640)"/>
<path d="M0 0 C0.812 1.688 0.812 1.688 1 4 C-1.87 7.545 -4.556 9.831 -9 11 C-9.99 10.67 -10.98 10.34 -12 10 C-12 9.01 -12 8.02 -12 7 C-11.078 6.745 -11.078 6.745 -10.137 6.484 C-5.027 5.033 -5.027 5.033 -0.938 1.812 C-0.628 1.214 -0.319 0.616 0 0 Z " fill="#F5F5F4" transform="translate(232,648)"/>
<path d="M0 0 C3 1 3 1 4 3 C4.071 5.374 4.084 7.75 4.062 10.125 C4.053 11.406 4.044 12.688 4.035 14.008 C4.024 14.995 4.012 15.983 4 17 C3.01 17 2.02 17 1 17 C0.67 11.39 0.34 5.78 0 0 Z " fill="#325A79" transform="translate(565,636)"/>
<path d="M0 0 C0.049 0.664 0.098 1.328 0.148 2.012 C0.711 8.711 0.711 8.711 3 15 C-2.23 12.656 -2.23 12.656 -4 10 C-4.433 7.15 -4.279 4.932 -4 2 C-1 0 -1 0 0 0 Z " fill="#224E6F" transform="translate(484,688)"/>
<path d="M0 0 C0.928 0.278 0.928 0.278 1.875 0.562 C4.042 1.237 4.042 1.237 6 0 C7.945 0.34 7.945 0.34 10.125 0.938 C11.212 1.229 11.212 1.229 12.32 1.527 C12.875 1.683 13.429 1.839 14 2 C14.33 3.32 14.66 4.64 15 6 C9.761 5.449 5.026 4.571 0 3 C0 2.01 0 1.02 0 0 Z " fill="#E8EBE8" transform="translate(785,652)"/>
<path d="M0 0 C2.415 1.085 3.952 2.275 6 4 C3 5 3 5 0 5 C-0.33 4.01 -0.66 3.02 -1 2 C-3.64 1.67 -6.28 1.34 -9 1 C-9 2.65 -9 4.3 -9 6 C-10.65 6 -12.3 6 -14 6 C-11.425 -1.173 -7.107 -0.837 0 0 Z " fill="#E2E4E0" transform="translate(425,625)"/>
<path d="M0 0 C2.339 0.287 4.674 0.619 7 1 C7.553 3.076 8 4.844 8 7 C5.36 6.34 2.72 5.68 0 5 C-0.33 3.68 -0.66 2.36 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F2F6F6" transform="translate(620,636)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.058 4.458 2.094 7.916 2.125 11.375 C2.142 12.36 2.159 13.345 2.176 14.359 C2.182 15.3 2.189 16.241 2.195 17.211 C2.206 18.08 2.216 18.95 2.227 19.845 C2 22 2 22 0 24 C-0.698 15.853 -1.282 8.117 0 0 Z " fill="#F9FAFB" transform="translate(442,630)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.673 0.797 3.345 1.593 3.008 2.414 C1.762 6.847 1.78 10.802 1.875 15.375 C1.884 16.206 1.893 17.038 1.902 17.895 C1.926 19.93 1.962 21.965 2 24 C1.34 23.67 0.68 23.34 0 23 C0 18.05 0 13.1 0 8 C-1.32 8 -2.64 8 -4 8 C-4 7.01 -4 6.02 -4 5 C-2.68 5 -1.36 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#0F355A" transform="translate(419,628)"/>
<path d="M0 0 C2.31 1.112 3.978 2.383 6 4 C5.34 4.66 4.68 5.32 4 6 C3.606 5.515 3.211 5.031 2.805 4.531 C-0.107 2.061 -2.872 2.472 -6.555 2.574 C-10.265 3.22 -11.805 5.05 -14 8 C-14.66 8.99 -15.32 9.98 -16 11 C-16.375 8.812 -16.375 8.812 -16 6 C-10.877 1.1 -7.144 -1.019 0 0 Z " fill="#D6DEE2" transform="translate(592,221)"/>
<path d="M0 0 C4 5 4 5 4.172 8.113 C4.033 9.169 3.893 10.225 3.75 11.312 C3.621 12.381 3.492 13.45 3.359 14.551 C3.241 15.359 3.122 16.167 3 17 C2.01 16.67 1.02 16.34 0 16 C0 10.72 0 5.44 0 0 Z " fill="#49718B" transform="translate(488,646)"/>
<path d="M0 0 C-1 3 -1 3 -4 5 C-4.801 7.647 -4.801 7.647 -5.188 10.625 C-5.346 11.628 -5.505 12.631 -5.668 13.664 C-5.778 14.435 -5.887 15.206 -6 16 C-6.33 16 -6.66 16 -7 16 C-7.988 11.926 -8.38 8.185 -8 4 C-5.357 0.319 -4.656 0 0 0 Z " fill="#F9FAF9" transform="translate(711,637)"/>
<path d="M0 0 C2.051 0.033 4.102 0.065 6.152 0.098 C6.482 2.078 6.812 4.058 7.152 6.098 C4.182 5.768 1.212 5.438 -1.848 5.098 C-2.178 3.778 -2.508 2.458 -2.848 1.098 C-1.848 0.098 -1.848 0.098 0 0 Z " fill="#2D5875" transform="translate(347.84765625,625.90234375)"/>
<path d="M0 0 C3 1 3 1 4.062 3 C4.372 3.66 4.681 4.32 5 5 C7.326 6.163 8.787 6.166 11.375 6.188 C12.146 6.202 12.917 6.216 13.711 6.23 C16.218 5.978 17.833 5.252 20 4 C20 4.99 20 5.98 20 7 C16.27 9.486 12.328 9.56 8 9 C4.308 7.359 2.111 5.445 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CCD7DB" transform="translate(622,318)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C2.67 3.99 2.34 4.98 2 6 C-2.455 6.495 -2.455 6.495 -7 7 C-7.495 4.525 -7.495 4.525 -8 2 C-6.35 1.67 -4.7 1.34 -3 1 C-2.67 2.32 -2.34 3.64 -2 5 C-1.34 5 -0.68 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D7DDDD" transform="translate(388,675)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C1.25 4.875 1.25 4.875 -1 6 C-8.267 6.586 -8.267 6.586 -11.5 4 C-11.995 3.34 -12.49 2.68 -13 2 C-12.319 2.206 -11.639 2.413 -10.938 2.625 C-7.19 3.103 -4.541 2.226 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#13375D" transform="translate(693,648)"/>
<path d="M0 0 C2.562 0.125 2.562 0.125 4.562 1.125 C4.232 3.435 3.903 5.745 3.562 8.125 C1.188 8.25 1.188 8.25 -1.438 8.125 C-3.438 6.125 -3.438 6.125 -3.625 3.562 C-3.373 0.286 -3.286 0.169 0 0 Z M-2.438 1.125 C-1.438 5.125 -1.438 5.125 -1.438 5.125 Z " fill="#E1E4E2" transform="translate(644.4375,639.875)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.188 5.125 3.188 5.125 3 8 C0.03 8 -2.94 8 -6 8 C-6.33 6.68 -6.66 5.36 -7 4 C-5.562 2.25 -5.562 2.25 -4 1 C-4 2.65 -4 4.3 -4 6 C-3.01 5.67 -2.02 5.34 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#F0F4F4" transform="translate(584,634)"/>
<path d="M0 0 C0 3.96 0 7.92 0 12 C-0.66 11.67 -1.32 11.34 -2 11 C-2 8.36 -2 5.72 -2 3 C-2.99 2.67 -3.98 2.34 -5 2 C-5 2.99 -5 3.98 -5 5 C-6.65 4.67 -8.3 4.34 -10 4 C-10 3.34 -10 2.68 -10 2 C-6.2 0.263 -4.403 0 0 0 Z " fill="#FCFDFB" transform="translate(377,637)"/>
<path d="M0 0 C5.61 0 11.22 0 17 0 C17.33 0.66 17.66 1.32 18 2 C12.39 1.67 6.78 1.34 1 1 C1 5.62 1 10.24 1 15 C0.01 15 -0.98 15 -2 15 C-1.67 14.01 -1.34 13.02 -1 12 C-1 10.886 -1 9.773 -1 8.625 C-1 5 -1 5 0 0 Z " fill="#E7EAE6" transform="translate(248,630)"/>
<path d="M0 0 C3.15 1.102 6.137 2.273 9 4 C10.562 7.25 10.562 7.25 11 10 C10.01 10.495 10.01 10.495 9 11 C8.722 10.072 8.722 10.072 8.438 9.125 C6.286 5.944 3.625 5.805 0 5 C0 4.67 0 4.34 0 4 C-1.65 3.67 -3.3 3.34 -5 3 C-5 2.67 -5 2.34 -5 2 C-3.35 2 -1.7 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EBEEEC" transform="translate(559,628)"/>
<path d="M0 0 C1.114 0.351 2.228 0.701 3.375 1.062 C6.803 2.372 6.803 2.372 9 1 C9.66 1.33 10.32 1.66 11 2 C11.594 4.649 11.742 7.292 12 10 C11.67 8.68 11.34 7.36 11 6 C10.103 6.309 10.103 6.309 9.188 6.625 C7 7 7 7 4 5 C2.345 4.615 0.678 4.272 -1 4 C-0.34 3.34 0.32 2.68 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#E9EBEA" transform="translate(400,629)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 7.92 2 15.84 2 24 C-1.192 20.808 -1.438 20.278 -1.812 16.062 C-1.901 15.208 -1.99 14.353 -2.082 13.473 C-1.991 10.741 -1.265 9.38 0 7 C0.078 4.668 0.09 2.332 0 0 Z " fill="#E6E8E4" transform="translate(377,683)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C-1.75 5.875 -1.75 5.875 -4 7 C-4.469 9.816 -4.469 9.816 -4.625 13.062 C-4.7 14.167 -4.775 15.272 -4.852 16.41 C-4.901 17.265 -4.95 18.119 -5 19 C-5.33 19 -5.66 19 -6 19 C-6 13.39 -6 7.78 -6 2 C-4.68 1.67 -3.36 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#43758F" transform="translate(764,633)"/>
<path d="M0 0 C1.5 1 1.5 1 3 3 C3.395 5.799 3.227 8.138 3 11 C2.34 11.66 1.68 12.32 1 13 C1 12.34 1 11.68 1 11 C0.01 10.67 -0.98 10.34 -2 10 C-1.86 8.52 -1.712 7.041 -1.562 5.562 C-1.481 4.739 -1.4 3.915 -1.316 3.066 C-1 1 -1 1 0 0 Z " fill="#315977" transform="translate(595,637)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.34 1.32 0.68 2.64 0 4 C-1.32 4 -2.64 4 -4 4 C-4 7.63 -4 11.26 -4 15 C-3.34 15.33 -2.68 15.66 -2 16 C-3.32 16 -4.64 16 -6 16 C-6 11.38 -6 6.76 -6 2 C-4.02 2 -2.04 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#FBFCFB" transform="translate(388,634)"/>
<path d="M0 0 C2.062 2.688 2.062 2.688 3 5 C2.34 5.66 1.68 6.32 1 7 C0.567 6.216 0.134 5.433 -0.312 4.625 C-1.892 1.783 -1.892 1.783 -5 0 C-9.184 -0.304 -12.472 -0.443 -16 2 C-16.33 2.66 -16.66 3.32 -17 4 C-17.66 4 -18.32 4 -19 4 C-17.63 0.839 -17.011 0.007 -14 -2 C-9.098 -3.874 -4.506 -2.301 0 0 Z " fill="#D3DBE1" transform="translate(725,379)"/>
<path d="M0 0 C2.51 3.765 2.615 5.543 2 10 C0.176 12.126 -1.661 13.392 -4 15 C-4.66 14.34 -5.32 13.68 -6 13 C-7.317 12.303 -8.649 11.63 -10 11 C-7.36 11 -4.72 11 -2 11 C-1.939 10.29 -1.879 9.579 -1.816 8.848 C-1.733 7.929 -1.649 7.009 -1.562 6.062 C-1.481 5.146 -1.4 4.229 -1.316 3.285 C-1 1 -1 1 0 0 Z " fill="#D8DBD6" transform="translate(685,696)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 1.65 3 3.3 3 5 C4.98 5 6.96 5 9 5 C9 5.99 9 6.98 9 8 C7.02 8 5.04 8 3 8 C3 12.62 3 17.24 3 22 C2.67 22 2.34 22 2 22 C2 15.07 2 8.14 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#0E3152" transform="translate(570,679)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.99 3 2.98 3 4 C3.66 4.33 4.32 4.66 5 5 C5 5.66 5 6.32 5 7 C4.34 7 3.68 7 3 7 C3.041 7.784 3.082 8.567 3.125 9.375 C3 12 3 12 1 14 C-1.625 14.125 -1.625 14.125 -4 14 C-4.33 13.34 -4.66 12.68 -5 12 C-3.35 11.67 -1.7 11.34 0 11 C0 7.37 0 3.74 0 0 Z " fill="#EBEDE9" transform="translate(491,652)"/>
<path d="M0 0 C2.25 -0.25 2.25 -0.25 5 0 C7.312 2 7.312 2 9 4 C8.67 4.66 8.34 5.32 8 6 C7.381 5.835 6.763 5.67 6.125 5.5 C3.076 4.783 0.988 5.203 -2 6 C-1.494 3.831 -1 2 0 0 Z " fill="#E4E8E7" transform="translate(656,650)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C4 7.61 4 13.22 4 19 C3.34 19 2.68 19 2 19 C1.67 13.39 1.34 7.78 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#FAFBF9" transform="translate(299,637)"/>
<path d="M0 0 C-0.188 1.875 -0.188 1.875 -1 4 C-3.25 5.125 -3.25 5.125 -6 6 C-6.928 6.371 -7.856 6.742 -8.812 7.125 C-9.534 7.414 -10.256 7.702 -11 8 C-9.047 1.977 -6.48 -0.54 0 0 Z " fill="#1D4365" transform="translate(321,633)"/>
<path d="M0 0 C3 1 3 1 4.75 3.875 C6 7 6 7 5 10 C4.67 9.01 4.34 8.02 4 7 C2.522 9.957 2.94 12.742 3 16 C2.34 15.67 1.68 15.34 1 15 C0.586 13.145 0.586 13.145 0.375 10.812 C0.302 10.006 0.228 9.199 0.152 8.367 C-0.028 5.572 -0.072 2.8 0 0 Z " fill="#A9B29A" transform="translate(57,472)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 3.64 3.34 6.28 3 9 C1.375 9.054 -0.25 9.093 -1.875 9.125 C-2.78 9.148 -3.685 9.171 -4.617 9.195 C-7 9 -7 9 -9 7 C-8.01 6.67 -7.02 6.34 -6 6 C-6 6.66 -6 7.32 -6 8 C-5.34 7.34 -4.68 6.68 -4 6 C-2.68 6 -1.36 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#EBEDEB" transform="translate(703,648)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 6.27 2 12.54 2 19 C1.01 19 0.02 19 -1 19 C-0.67 12.73 -0.34 6.46 0 0 Z " fill="#2E6180" transform="translate(252,634)"/>
<path d="M0 0 C12.485 -0.912 12.485 -0.912 16.043 1.23 C18.227 3.825 19 5.608 19 9 C17.68 8.67 16.36 8.34 15 8 C15 7.01 15 6.02 15 5 C14.01 4.34 13.02 3.68 12 3 C12.33 2.34 12.66 1.68 13 1 C12.34 1 11.68 1 11 1 C10.67 1.66 10.34 2.32 10 3 C6.7 2.34 3.4 1.68 0 1 C0 0.67 0 0.34 0 0 Z " fill="#DEE1DE" transform="translate(576,647)"/>
<path d="M0 0 C3.771 1.012 4.784 1.676 7 5 C6.625 7.688 6.625 7.688 6 10 C5.01 9.67 4.02 9.34 3 9 C2.691 8.196 2.381 7.391 2.062 6.562 C1.234 3.858 1.234 3.858 -1.125 2.688 C-2.053 2.347 -2.053 2.347 -3 2 C-2.01 1.34 -1.02 0.68 0 0 Z " fill="#265676" transform="translate(726,633)"/>
<path d="M0 0 C2.731 2.228 3.563 3.527 4.375 6.938 C4 9.75 4 9.75 3.375 11.938 C3.045 11.938 2.715 11.938 2.375 11.938 C2.251 10.824 2.251 10.824 2.125 9.688 C1.214 6.347 0.019 5.128 -2.625 2.938 C-4.758 1.702 -4.758 1.702 -6.625 0.938 C-3.919 -0.967 -3.148 -1.274 0 0 Z " fill="#1F4B6C" transform="translate(803.625,634.0625)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.66 2.33 1.32 2.66 2 3 C0.793 4.145 0.793 4.145 -0.438 5.312 C-3.168 7.794 -3.168 7.794 -4 11 C-5.32 10.67 -6.64 10.34 -8 10 C-4.038 0 -4.038 0 0 0 Z " fill="#EAEEED" transform="translate(217,625)"/>
<path d="M0 0 C-1.212 0.58 -1.212 0.58 -2.449 1.172 C-3.518 1.693 -4.586 2.213 -5.688 2.75 C-6.743 3.26 -7.799 3.771 -8.887 4.297 C-13.598 6.874 -17.288 10.151 -21 14 C-19.408 7.986 -14.976 4.851 -9.902 1.695 C-4.167 -1.414 -4.167 -1.414 0 0 Z " fill="#DEE5E9" transform="translate(339,376)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 6.27 2.34 12.54 2 19 C1.34 18.67 0.68 18.34 0 18 C0 12.06 0 6.12 0 0 Z " fill="#215677" transform="translate(390,685)"/>
<path d="M0 0 C1.603 -0.108 3.207 -0.186 4.812 -0.25 C5.706 -0.296 6.599 -0.343 7.52 -0.391 C10 0 10 0 11.824 2.016 C12.406 2.998 12.406 2.998 13 4 C9.368 5.211 7.935 4.628 4.312 3.562 C2.822 3.131 2.822 3.131 1.301 2.691 C0.542 2.463 -0.218 2.235 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#1F4869" transform="translate(550,684)"/>
<path d="M0 0 C1.65 1.65 3.3 3.3 5 5 C1.848 6.833 -0.576 7.288 -4.203 7.414 C-5.183 7.453 -6.163 7.491 -7.172 7.531 C-8.696 7.578 -8.696 7.578 -10.25 7.625 C-11.281 7.664 -12.312 7.702 -13.375 7.742 C-15.916 7.836 -18.458 7.922 -21 8 C-21.33 7.34 -21.66 6.68 -22 6 C-14.74 6 -7.48 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#E8EBE5" transform="translate(351,650)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C2.32 4 3.64 4 5 4 C5 4.66 5 5.32 5 6 C4.078 6.091 4.078 6.091 3.137 6.184 C2.328 6.267 1.52 6.351 0.688 6.438 C-0.113 6.519 -0.914 6.6 -1.738 6.684 C-4.161 6.953 -4.161 6.953 -7 8 C-7.33 6.68 -7.66 5.36 -8 4 C-6.866 3.526 -5.731 3.051 -4.562 2.562 C-1.243 1.364 -1.243 1.364 0 0 Z " fill="#EEF2F2" transform="translate(564,650)"/>
<path d="M0 0 C1.316 2.166 2.059 3.448 1.785 6.008 C-0.419 12.467 -3.878 17.591 -8 23 C-8.66 22.34 -9.32 21.68 -10 21 C-9.258 20.196 -8.515 19.391 -7.75 18.562 C-3.298 13.213 -0.71 6.923 0 0 Z " fill="#D3DCE2" transform="translate(609,431)"/>
<path d="M0 0 C1.156 0.234 1.156 0.234 2.336 0.473 C3.84 0.765 3.84 0.765 5.375 1.062 C6.373 1.26 7.37 1.457 8.398 1.66 C10.978 2.244 10.978 2.244 13 1 C12.67 2.32 12.34 3.64 12 5 C10.563 5.054 9.125 5.093 7.688 5.125 C6.887 5.148 6.086 5.171 5.262 5.195 C3 5 3 5 0 3 C0 2.01 0 1.02 0 0 Z " fill="#F3F5F2" transform="translate(421,703)"/>
<path d="M0 0 C1.65 1.32 3.3 2.64 5 4 C5.66 3.67 6.32 3.34 7 3 C9.672 2.866 12.323 2.957 15 3 C13.938 5 13.938 5 12 7 C8.625 7.562 8.625 7.562 5 7 C2.062 4.625 2.062 4.625 0 2 C0 1.34 0 0.68 0 0 Z " fill="#15385B" transform="translate(481,698)"/>
<path d="M0 0 C2.962 0.613 4.381 1.254 7 3 C7 3.99 7 4.98 7 6 C9.64 6 12.28 6 15 6 C15 6.33 15 6.66 15 7 C8.307 7.476 4.225 7.4 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#EEF0ED" transform="translate(635,701)"/>
<path d="M0 0 C2 2 2 2 3 4 C2.912 6.378 2.757 8.753 2.562 11.125 C2.461 12.406 2.359 13.688 2.254 15.008 C2.17 15.995 2.086 16.983 2 18 C1.67 18 1.34 18 1 18 C0.961 16.645 0.961 16.645 0.922 15.262 C0.865 14.082 0.808 12.903 0.75 11.688 C0.704 10.516 0.657 9.344 0.609 8.137 C0.299 4.936 0.299 4.936 -1.906 3.156 C-3.916 2.047 -5.918 0.967 -8 0 C-4.677 -1.108 -3.308 -0.933 0 0 Z " fill="#305F7C" transform="translate(473,686)"/>
<path d="M0 0 C1.392 4.176 -0.293 6.834 -1.938 10.688 C-2.228 11.389 -2.519 12.091 -2.818 12.814 C-3.537 14.547 -4.267 16.274 -5 18 C-6.883 12.35 -4.596 6.205 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#204D6C" transform="translate(374,677)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.078 2.273 0.078 2.273 -0.863 2.551 C-1.672 2.802 -2.48 3.054 -3.312 3.312 C-4.513 3.678 -4.513 3.678 -5.738 4.051 C-8.1 5.042 -9.339 6.081 -11 8 C-11.33 6.68 -11.66 5.36 -12 4 C-7.999 0.12 -5.519 -0.433 0 0 Z " fill="#133D5F" transform="translate(226,627)"/>
<path d="M0 0 C0.619 0.639 1.237 1.279 1.875 1.938 C7.237 7.3 12.857 12.545 19 17 C11.045 15.925 6.043 9.305 1.031 3.48 C0 2 0 2 0 0 Z " fill="#DDE4E7" transform="translate(734,304)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 7.92 1 15.84 1 24 C0.01 24.33 -0.98 24.66 -2 25 C-1.835 24.484 -1.67 23.969 -1.5 23.438 C-0.85 20.266 -0.938 17.228 -1 14 C-1.33 14 -1.66 14 -2 14 C-2 12.35 -2 10.7 -2 9 C-1.34 9 -0.68 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#386B87" transform="translate(678,677)"/>
<path d="M0 0 C2.597 0.974 4.396 2.145 6.5 3.938 C5.242 4.226 3.984 4.515 2.688 4.812 C-0.354 5.511 -1.807 6.142 -4.5 7.938 C-4.5 7.278 -4.5 6.617 -4.5 5.938 C-5.16 5.938 -5.82 5.938 -6.5 5.938 C-6.5 3.938 -6.5 3.938 -4.812 1.75 C-2.5 -0.062 -2.5 -0.062 0 0 Z " fill="#E1E5E4" transform="translate(619.5,680.0625)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C1.99 10.33 2.98 10.66 4 11 C2.68 11 1.36 11 0 11 C-0.33 10.01 -0.66 9.02 -1 8 C-1.536 8.495 -2.072 8.99 -2.625 9.5 C-5.594 11.375 -7.553 11.235 -11 11 C-11.33 10.34 -11.66 9.68 -12 9 C-11.113 9.124 -10.226 9.247 -9.312 9.375 C-5.734 9.318 -5.734 9.318 -3.188 6.188 C-0.906 3.177 -0.906 3.177 0 0 Z " fill="#133559" transform="translate(565,643)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.62 1 9.24 1 14 C2.32 14.33 3.64 14.66 5 15 C5 15.66 5 16.32 5 17 C4.34 17 3.68 17 3 17 C3 17.66 3 18.32 3 19 C3.66 19.33 4.32 19.66 5 20 C2.625 20.188 2.625 20.188 0 20 C-0.66 19.01 -1.32 18.02 -2 17 C-1.34 17 -0.68 17 0 17 C0 11.39 0 5.78 0 0 Z " fill="#E0E4E1" transform="translate(242,631)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C2.01 2.66 1.02 3.32 0 4 C-0.384 6.61 -0.603 9.006 -0.688 11.625 C-0.722 12.331 -0.756 13.038 -0.791 13.766 C-0.874 15.51 -0.938 17.255 -1 19 C-1.66 19 -2.32 19 -3 19 C-3.081 16.771 -3.139 14.542 -3.188 12.312 C-3.222 11.071 -3.257 9.83 -3.293 8.551 C-2.985 4.814 -2.181 2.994 0 0 Z " fill="#244F72" transform="translate(457,635)"/>
<path d="M0 0 C3.703 -0.337 5.395 -0.477 8.375 1.875 C10.959 5.254 10 9.818 10 14 C9.67 14 9.34 14 9 14 C8.856 12.907 8.711 11.814 8.562 10.688 C8.117 7.069 8.117 7.069 7 4 C4.69 3.67 2.38 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#123A5F" transform="translate(398,633)"/>
<path d="M0 0 C3 0.125 3 0.125 4 1.125 C4.041 3.125 4.043 5.125 4 7.125 C3.567 6.63 3.134 6.135 2.688 5.625 C1.003 3.793 1.003 3.793 -2 4.125 C-2.33 4.785 -2.66 5.445 -3 6.125 C-3.66 6.125 -4.32 6.125 -5 6.125 C-3.93 0.164 -3.93 0.164 0 0 Z " fill="#F4F7F4" transform="translate(451,671.875)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.33 1.98 3.66 3.96 4 6 C3.01 6 2.02 6 1 6 C1.141 8.271 1.288 10.542 1.438 12.812 C1.519 14.077 1.6 15.342 1.684 16.645 C1.872 19.992 1.872 19.992 3 23 C2.34 23 1.68 23 1 23 C-0.226 20.547 -0.114 18.949 -0.098 16.211 C-0.093 14.799 -0.093 14.799 -0.088 13.359 C-0.08 12.375 -0.071 11.39 -0.062 10.375 C-0.058 9.382 -0.053 8.39 -0.049 7.367 C-0.037 4.911 -0.021 2.456 0 0 Z " fill="#346683" transform="translate(379,629)"/>
<path d="M0 0 C7.333 -0.367 7.333 -0.367 10.5 1.375 C12 3 12 3 12 5 C7.916 4.257 3.981 3.171 0 2 C0 1.34 0 0.68 0 0 Z " fill="#143A5F" transform="translate(684,633)"/>
<path d="M0 0 C3.076 -0.362 5.932 -0.477 9 0 C12.398 2.705 13.766 4.11 14.297 8.402 C14.311 10.627 14.221 12.788 14 15 C13.01 15.495 13.01 15.495 12 16 C12.33 13.03 12.66 10.06 13 7 C11.68 6.67 10.36 6.34 9 6 C8.67 4.68 8.34 3.36 8 2 C5.36 1.67 2.72 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#113458" transform="translate(620,684)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 0.66 2.66 1.32 3 2 C5.64 2 8.28 2 11 2 C9.938 4 9.938 4 8 6 C4.312 6.25 4.312 6.25 1 6 C-0.062 4.125 -0.062 4.125 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#133C61" transform="translate(737,648)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C6.109 2.233 8.014 1.995 11 1 C10.125 4.875 10.125 4.875 9 6 C3.663 6.464 3.663 6.464 1.125 4.688 C0 3 0 3 0 0 Z " fill="#F4F6F6" transform="translate(792,645)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C1.66 15 2.32 15 3 15 C3 14.34 3 13.68 3 13 C5.962 13.613 7.381 14.254 10 16 C3.375 17.125 3.375 17.125 0 16 C-0.826 10.394 -1.068 5.577 0 0 Z " fill="#EEEFEB" transform="translate(300,641)"/>
<path d="M0 0 C0.598 0.206 1.196 0.413 1.812 0.625 C1.812 2.275 1.812 3.925 1.812 5.625 C1.152 5.625 0.493 5.625 -0.188 5.625 C-0.517 6.615 -0.848 7.605 -1.188 8.625 C-2.178 8.625 -3.168 8.625 -4.188 8.625 C-4.517 6.315 -4.848 4.005 -5.188 1.625 C-2.188 -0.375 -2.188 -0.375 0 0 Z " fill="#DFE1E0" transform="translate(498.1875,639.375)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 5.61 3 11.22 3 17 C2.34 17 1.68 17 1 17 C0.67 11.39 0.34 5.78 0 0 Z " fill="#204A6D" transform="translate(468,637)"/>
<path d="M0 0 C3.625 0.375 3.625 0.375 6.062 2.375 C6.836 3.365 6.836 3.365 7.625 4.375 C4.196 5.563 2.247 5.177 -1.125 3.938 C-1.924 3.65 -2.723 3.363 -3.547 3.066 C-4.15 2.838 -4.753 2.61 -5.375 2.375 C-3.375 0.375 -3.375 0.375 0 0 Z " fill="#10365B" transform="translate(363.375,632.625)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.361 2.011 1.721 3.021 1.062 4.062 C-1.022 7.654 -1.505 10.875 -2 15 C-2.99 15.495 -2.99 15.495 -4 16 C-4.252 9.712 -3.512 5.269 0 0 Z " fill="#F8FAF9" transform="translate(789,632)"/>
<path d="M0 0 C0.33 1.65 0.66 3.3 1 5 C2.32 5.33 3.64 5.66 5 6 C5 6.66 5 7.32 5 8 C2.69 8 0.38 8 -2 8 C-2 6.02 -2 4.04 -2 2 C-4.31 2.33 -6.62 2.66 -9 3 C-9 2.34 -9 1.68 -9 1 C-5.942 0.456 -3.112 0 0 0 Z " fill="#FBFCFB" transform="translate(384,625)"/>
<path d="M0 0 C6.83 0.379 12.184 2.361 18 6 C18.33 6.66 18.66 7.32 19 8 C16.69 8 14.38 8 12 8 C12.33 7.01 12.66 6.02 13 5 C10.03 4.505 10.03 4.505 7 4 C7 3.34 7 2.68 7 2 C4.69 1.67 2.38 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#29526F" transform="translate(364,376)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C0.01 10 -0.98 10 -2 10 C-2.99 10.66 -3.98 11.32 -5 12 C-7.688 12.125 -7.688 12.125 -10 12 C-10 11.01 -10 10.02 -10 9 C-8.907 8.691 -7.814 8.381 -6.688 8.062 C-4.446 7.417 -2.213 6.738 0 6 C0 4.02 0 2.04 0 0 Z " fill="#133859" transform="translate(537,693)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.835 0.66 3.67 1.32 3.5 2 C2.997 5.02 2.985 7.875 3.031 10.93 C2.985 14.016 2.507 16.958 2 20 C1.67 20 1.34 20 1 20 C0.67 13.4 0.34 6.8 0 0 Z " fill="#275674" transform="translate(449,683)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.63 1 7.26 1 11 C-1.64 10.67 -4.28 10.34 -7 10 C-6.01 9.34 -5.02 8.68 -4 8 C-6.31 8 -8.62 8 -11 8 C-7.687 5.791 -6.775 6 -3 6 C-0.861 3.861 -0.573 2.867 0 0 Z " fill="#DBE0E0" transform="translate(494,674)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 3.97 2 6.94 2 10 C0.35 10.66 -1.3 11.32 -3 12 C-2.257 7.916 -1.171 3.981 0 0 Z " fill="#295270" transform="translate(228,642)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C6.562 2.938 6.562 2.938 7 5 C6 6 6 6 2.938 6.062 C1.968 6.042 0.999 6.021 0 6 C-0.33 5.34 -0.66 4.68 -1 4 C-0.34 3.67 0.32 3.34 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#F9FAFA" transform="translate(620,645)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C2.438 2.043 2.438 4.023 2.438 6.062 C1.104 6.062 -0.229 6.062 -1.562 6.062 C-1.893 9.362 -2.222 12.663 -2.562 16.062 C-2.893 16.062 -3.222 16.062 -3.562 16.062 C-3.589 13.583 -3.609 11.104 -3.625 8.625 C-3.638 7.564 -3.638 7.564 -3.65 6.482 C-3.659 4.676 -3.615 2.869 -3.562 1.062 C-2.562 0.062 -2.562 0.062 0 0 Z M-2.562 2.062 C-2.232 2.722 -1.903 3.383 -1.562 4.062 C-0.903 4.062 -0.242 4.062 0.438 4.062 C0.438 3.403 0.438 2.742 0.438 2.062 C-0.553 2.062 -1.543 2.062 -2.562 2.062 Z " fill="#D9DDD8" transform="translate(821.5625,639.9375)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C3.66 2.34 4.32 1.68 5 1 C4.67 3.31 4.34 5.62 4 8 C3.67 7.34 3.34 6.68 3 6 C2.01 6.66 1.02 7.32 0 8 C-2.688 8.125 -2.688 8.125 -5 8 C-4.01 7.67 -3.02 7.34 -2 7 C-0.77 3.488 -0.77 3.488 0 0 Z " fill="#D8DFDD" transform="translate(800,640)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.758 4.383 1.758 4.383 1.125 7.125 C0.819 8.49 0.819 8.49 0.508 9.883 C0.256 10.931 0.256 10.931 0 12 C-0.99 12.33 -1.98 12.66 -3 13 C-4.477 5.738 -4.477 5.738 -2.062 2 C-1.042 1.01 -1.042 1.01 0 0 Z " fill="#325E7D" transform="translate(663,634)"/>
<path d="M0 0 C0.495 0.681 0.99 1.361 1.5 2.062 C4.214 5.252 7.165 7.795 10.391 10.449 C10.922 10.961 11.453 11.473 12 12 C12 12.66 12 13.32 12 14 C8.688 13.375 8.688 13.375 5 12 C3.25 9.188 3.25 9.188 2 6 C1.016 4.324 0.021 2.654 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C7D1D6" transform="translate(702,468)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.318 4.614 1.369 7.188 -0.562 11.438 C-3.631 14.664 -5.635 14.832 -10 15 C-9.67 14.01 -9.34 13.02 -9 12 C-7.866 11.629 -6.731 11.258 -5.562 10.875 C-3.619 10.236 -3.619 10.236 -2 9 C-0.784 6.059 -0.324 3.155 0 0 Z " fill="#10385D" transform="translate(677,690)"/>
<path d="M0 0 C2 -0.043 4 -0.041 6 0 C7 1 7 1 7.062 4.562 C7.042 5.697 7.021 6.831 7 8 C6.34 8 5.68 8 5 8 C4.67 7.01 4.34 6.02 4 5 C3.01 4.67 2.02 4.34 1 4 C0.312 1.938 0.312 1.938 0 0 Z " fill="#E8EBE9" transform="translate(381,672)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.33 1.34 1.66 1 2 C2.65 2.33 4.3 2.66 6 3 C6 3.33 6 3.66 6 4 C4.35 4.33 2.7 4.66 1 5 C0.67 9.62 0.34 14.24 0 19 C-0.33 19 -0.66 19 -1 19 C-1.091 17.627 -1.091 17.627 -1.184 16.227 C-1.267 15.038 -1.351 13.85 -1.438 12.625 C-1.519 11.442 -1.6 10.258 -1.684 9.039 C-1.811 6.085 -1.811 6.085 -3 4 C-2.01 2.68 -1.02 1.36 0 0 Z " fill="#E5E9E6" transform="translate(266,637)"/>
<path d="M0 0 C2.475 0.99 2.475 0.99 5 2 C4.67 2.99 4.34 3.98 4 5 C4.66 5 5.32 5 6 5 C5.67 5.99 5.34 6.98 5 8 C3.35 8.33 1.7 8.66 0 9 C0 6.03 0 3.06 0 0 Z " fill="#9BAAB5" transform="translate(203,638)"/>
<path d="M0 0 C3.032 0.444 4.405 1.217 6.562 3.375 C6.562 4.365 6.562 5.355 6.562 6.375 C5.572 6.375 4.582 6.375 3.562 6.375 C3.562 5.715 3.562 5.055 3.562 4.375 C0.592 4.045 -2.378 3.715 -5.438 3.375 C-2.438 0.375 -2.438 0.375 0 0 Z " fill="#EBF0EF" transform="translate(688.4375,635.625)"/>
<path d="M0 0 C0.967 3.122 0.99 5.521 0.562 8.75 C0.461 9.549 0.359 10.348 0.254 11.172 C0.128 12.077 0.128 12.077 0 13 C0.66 13.33 1.32 13.66 2 14 C0.35 13.67 -1.3 13.34 -3 13 C-2.67 9.37 -2.34 5.74 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DADDD7" transform="translate(399,692)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 3.63 3 7.26 3 11 C2.01 11 1.02 11 0 11 C0 7.37 0 3.74 0 0 Z " fill="#27597A" transform="translate(497,677)"/>
<path d="M0 0 C2.375 -0.125 2.375 -0.125 5 0 C5.66 0.66 6.32 1.32 7 2 C6.625 4.125 6.625 4.125 6 6 C4.35 5.67 2.7 5.34 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#E4E7E3" transform="translate(218,643)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-1.68 3.143 -3.361 4.285 -5.043 5.426 C-8.353 8.089 -10.632 11.505 -13 15 C-13.33 14.34 -13.66 13.68 -14 13 C-4.903 0 -4.903 0 0 0 Z " fill="#D6DEE3" transform="translate(439,398)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.054 2.375 1.094 4.749 1.125 7.125 C1.142 7.79 1.159 8.455 1.176 9.141 C1.218 13.495 0.702 16.988 -1 21 C-1.66 21.66 -2.32 22.32 -3 23 C-2.337 15.276 -1.235 7.652 0 0 Z " fill="#568097" transform="translate(416,268)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.313 2.11 -0.374 2.219 -1.082 2.332 C-1.983 2.491 -2.884 2.649 -3.812 2.812 C-4.706 2.963 -5.599 3.114 -6.52 3.27 C-9.234 4.069 -10.21 4.866 -12 7 C-12.66 7.66 -13.32 8.32 -14 9 C-14.33 8.34 -14.66 7.68 -15 7 C-10.618 1.279 -10.618 1.279 -8.062 0.125 C-5.368 -0.038 -2.698 -0.061 0 0 Z " fill="#C8D4DA" transform="translate(637,221)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-0.66 3.66 -1.32 4.32 -2 5 C1.3 5.33 4.6 5.66 8 6 C8 6.33 8 6.66 8 7 C4.7 7 1.4 7 -2 7 C-2.33 7.99 -2.66 8.98 -3 10 C-3.33 9.34 -3.66 8.68 -4 8 C-4.99 7.34 -5.98 6.68 -7 6 C-4.69 4.02 -2.38 2.04 0 0 Z " fill="#E2E6E3" transform="translate(496,701)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C3.438 3.062 3.438 3.062 2.5 5.25 C1.974 6.147 1.974 6.147 1.438 7.062 C0.447 6.732 -0.543 6.403 -1.562 6.062 C-1.233 5.732 -0.902 5.403 -0.562 5.062 C-1.553 4.732 -2.543 4.403 -3.562 4.062 C-3.892 4.722 -4.223 5.383 -4.562 6.062 C-3.49 0.085 -3.49 0.085 0 0 Z " fill="#DCE0DC" transform="translate(490.5625,689.9375)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-0.763 2.289 -1.526 2.577 -2.312 2.875 C-5.15 3.963 -5.15 3.963 -8 6 C-8.33 5.67 -8.66 5.34 -9 5 C-9.66 5.66 -10.32 6.32 -11 7 C-10.615 4.767 -10.105 3.163 -8.875 1.25 C-6.003 -0.665 -3.355 -0.19 0 0 Z " fill="#0F385C" transform="translate(492,684)"/>
<path d="M0 0 C1.973 0.266 1.973 0.266 4.973 2.266 C4.643 3.256 4.313 4.246 3.973 5.266 C3.643 4.276 3.313 3.286 2.973 2.266 C0.333 2.266 -2.307 2.266 -5.027 2.266 C-5.027 2.926 -5.027 3.586 -5.027 4.266 C-7.527 5.891 -7.527 5.891 -10.027 7.266 C-7.812 1.253 -6.596 0.049 0 0 Z " fill="#133A5C" transform="translate(470.02734375,683.734375)"/>
<path d="M0 0 C2.125 0.375 2.125 0.375 4 1 C3.01 2.485 3.01 2.485 2 4 C1.34 4 0.68 4 0 4 C0.66 5.65 1.32 7.3 2 9 C1.01 9.66 0.02 10.32 -1 11 C-1.222 9.544 -1.427 8.085 -1.625 6.625 C-1.741 5.813 -1.857 5.001 -1.977 4.164 C-1.984 3.45 -1.992 2.736 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#F8F9F8" transform="translate(523,682)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.64 1 5.28 1 8 C0.01 7.34 -0.98 6.68 -2 6 C-4.968 5.541 -4.968 5.541 -8.188 5.375 C-9.274 5.3 -10.361 5.225 -11.48 5.148 C-12.312 5.099 -13.143 5.05 -14 5 C-14 4.67 -14 4.34 -14 4 C-13.241 3.963 -12.481 3.925 -11.699 3.887 C-10.705 3.821 -9.711 3.755 -8.688 3.688 C-7.701 3.629 -6.715 3.571 -5.699 3.512 C-2.729 2.949 -1.884 2.274 0 0 Z " fill="#E5E9E4" transform="translate(342,626)"/>
<path d="M0 0 C1.218 0.035 1.218 0.035 2.461 0.07 C3.687 0.097 3.687 0.097 4.938 0.125 C5.877 0.16 5.877 0.16 6.836 0.195 C6.228 0.494 5.619 0.793 4.992 1.102 C3.408 1.905 1.859 2.781 0.336 3.695 C-1.795 4.974 -3.927 6.118 -6.164 7.195 C-3.634 0.328 -3.634 0.328 0 0 Z " fill="#194164" transform="translate(401.1640625,683.8046875)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 3.106 6.461 4.353 5 7 C2 6 2 6 0 4 C0 2.68 0 1.36 0 0 Z " fill="#DDE0DC" transform="translate(425,640)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 3.64 2 6.28 2 9 C3.65 9 5.3 9 7 9 C6.01 9.99 5.02 10.98 4 12 C2.68 11.34 1.36 10.68 0 10 C0 6.7 0 3.4 0 0 Z " fill="#FAFBF9" transform="translate(423,639)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.625 2.938 4.625 2.938 4 6 C3.01 6.495 3.01 6.495 2 7 C1.342 10.029 1.342 10.029 1 13 C0.67 13 0.34 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#E5E8E3" transform="translate(200,635)"/>
<path d="M0 0 C2.199 2.199 3.155 4.145 4.5 6.938 C5.881 9.796 7.234 12.351 9 15 C8.01 15 7.02 15 6 15 C4.43 12.953 4.43 12.953 2.875 10.25 C2.357 9.368 1.839 8.487 1.305 7.578 C0.166 5.329 -0.631 3.485 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CBD5DB" transform="translate(403,447)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5 1.66 5 2.32 5 3 C-0.415 6.077 -0.415 6.077 -3.375 5.688 C-3.911 5.461 -4.447 5.234 -5 5 C-3.625 3.5 -3.625 3.5 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E9EEEE" transform="translate(558,696)"/>
<path d="M0 0 C2.583 0.819 5.072 1.745 7.562 2.812 C6.902 4.133 6.243 5.452 5.562 6.812 C5.233 5.822 4.902 4.832 4.562 3.812 C3.572 4.143 2.582 4.472 1.562 4.812 C-1.176 4.357 -3.758 3.559 -6.438 2.812 C-3.139 -0.241 -3.139 -0.241 0 0 Z " fill="#DCE1E2" transform="translate(472.4375,681.1875)"/>
<path d="M0 0 C2 3 2 3 3 6.625 C3.33 7.739 3.66 8.852 4 10 C4.66 10.33 5.32 10.66 6 11 C4.02 11.99 4.02 11.99 2 13 C-0.248 8.503 -0.167 4.921 0 0 Z " fill="#295777" transform="translate(716,640)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.295 6.023 2.985 8.895 0 13 C-0.66 12.67 -1.32 12.34 -2 12 C-1.34 8.04 -0.68 4.08 0 0 Z " fill="#325F7D" transform="translate(653,639)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C6.125 3.75 6.125 3.75 5 6 C4.34 6 3.68 6 3 6 C2.67 6.99 2.34 7.98 2 9 C1.783 7.886 1.783 7.886 1.562 6.75 C1.098 4.48 0.581 2.242 0 0 Z " fill="#4A6984" transform="translate(585,633)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-3.3 3.66 -6.6 4.32 -10 5 C-10 4.01 -10 3.02 -10 2 C-6.598 -0.094 -3.947 -0.179 0 0 Z " fill="#11385E" transform="translate(335,633)"/>
<path d="M0 0 C4.29 0 8.58 0 13 0 C11.515 1.98 11.515 1.98 10 4 C9.34 3.67 8.68 3.34 8 3 C6.463 2.775 4.92 2.592 3.375 2.438 C2.558 2.354 1.74 2.27 0.898 2.184 C0.272 2.123 -0.355 2.062 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#F6F8F7" transform="translate(359,631)"/>
<path d="M0 0 C0.227 0.619 0.454 1.238 0.688 1.875 C2.379 4.614 3.992 5.05 7 6 C7 6.66 7 7.32 7 8 C6.34 8 5.68 8 5 8 C5.66 9.98 6.32 11.96 7 14 C4.667 11.861 3.213 9.833 1.812 7 C1.468 6.319 1.124 5.639 0.77 4.938 C0 3 0 3 0 0 Z " fill="#2F5772" transform="translate(729,291)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.99 0.34 3.98 0 5 C0.66 5.33 1.32 5.66 2 6 C1.01 7.98 0.02 9.96 -1 12 C-1.66 11.34 -2.32 10.68 -3 10 C-2.699 5.862 -2.321 3.482 0 0 Z " fill="#1A3E5F" transform="translate(368,690)"/>
<path d="M0 0 C2.143 3.477 2.213 6.212 2.125 10.25 C2.107 11.328 2.089 12.405 2.07 13.516 C2.036 14.745 2.036 14.745 2 16 C1.34 16.33 0.68 16.66 0 17 C-0.699 12.96 -1.141 9.931 0 6 C0.038 4 0.045 1.999 0 0 Z " fill="#D9DEDB" transform="translate(648,681)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C2.65 10.33 4.3 10.66 6 11 C6 11.33 6 11.66 6 12 C3.69 12 1.38 12 -1 12 C-1.027 10.563 -1.046 9.125 -1.062 7.688 C-1.074 6.887 -1.086 6.086 -1.098 5.262 C-1 3 -1 3 0 0 Z " fill="#FAFBFA" transform="translate(633,653)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.32 2 2.64 2 4 C1.34 4 0.68 4 0 4 C-0.33 4.99 -0.66 5.98 -1 7 C-3.31 7.33 -5.62 7.66 -8 8 C-5.871 4.806 -4.232 3.906 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#1D4265" transform="translate(250,646)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.062 4.562 -0.062 4.562 -2 6 C-5.688 5.688 -5.688 5.688 -9 5 C-8.67 4.01 -8.34 3.02 -8 2 C-7.051 2.041 -6.103 2.082 -5.125 2.125 C-1.898 2.35 -1.898 2.35 0 0 Z " fill="#F8FAF9" transform="translate(502,645)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.125 2.375 2.125 2.375 2 5 C0 7 0 7 -3.125 7.125 C-4.548 7.063 -4.548 7.063 -6 7 C-6.33 5.68 -6.66 4.36 -7 3 C-4.03 3.495 -4.03 3.495 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#F9FAF9" transform="translate(225,644)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 1.65 5 3.3 5 5 C3.35 5.66 1.7 6.32 0 7 C-1.044 3.867 -0.934 3.01 0 0 Z " fill="#E8ECEE" transform="translate(744,643)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C3.549 6.723 2.422 10.555 1 15 C0.67 15 0.34 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#31607E" transform="translate(750,635)"/>
<path d="M0 0 C3.471 1.409 4.881 2.906 6.75 6.125 C7.392 7.212 7.392 7.212 8.047 8.32 C8.519 9.152 8.519 9.152 9 10 C8.01 11.485 8.01 11.485 7 13 C6.746 12.434 6.492 11.868 6.23 11.285 C5.714 10.185 5.714 10.185 5.188 9.062 C4.851 8.332 4.515 7.601 4.168 6.848 C3.064 4.67 3.064 4.67 0 4 C-0.33 3.01 -0.66 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B4C4CE" transform="translate(404,252)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.853 1.147 -0.853 1.147 -1.723 1.297 C-7.229 2.363 -11.273 3.983 -16 7 C-16 6.01 -16 5.02 -16 4 C-14.73 3.352 -13.459 2.706 -12.188 2.062 C-11.48 1.703 -10.772 1.343 -10.043 0.973 C-6.191 -0.861 -4.095 -0.996 0 0 Z " fill="#C0CDD4" transform="translate(775,243)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.121 0.626 1.242 1.253 1.367 1.898 C1.535 2.716 1.702 3.533 1.875 4.375 C2.037 5.187 2.2 5.999 2.367 6.836 C2.867 9.187 2.867 9.187 5 11 C6.317 11.697 7.649 12.37 9 13 C6.69 13.33 4.38 13.66 2 14 C1.856 13.361 1.711 12.721 1.562 12.062 C1.19 10.025 1.19 10.025 0 9 C-0.072 7.481 -0.084 5.958 -0.062 4.438 C-0.053 3.611 -0.044 2.785 -0.035 1.934 C-0.024 1.296 -0.012 0.657 0 0 Z " fill="#0D2F53" transform="translate(595,690)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.268 0.949 3.536 1.898 3.812 2.875 C4.75 6.253 4.75 6.253 8 8 C6.25 8.75 6.25 8.75 4 9 C1.75 7.375 1.75 7.375 0 5 C-0.25 2.25 -0.25 2.25 0 0 Z " fill="#234F6F" transform="translate(522,696)"/>
<path d="M0 0 C5 2 5 2 10 4 C9.67 4.66 9.34 5.32 9 6 C5.625 6.188 5.625 6.188 2 6 C0 3 0 3 0 0 Z " fill="#235172" transform="translate(550,647)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.081 1.958 1.139 3.916 1.188 5.875 C1.222 6.966 1.257 8.056 1.293 9.18 C1.196 10.11 1.1 11.041 1 12 C0.01 12.66 -0.98 13.32 -2 14 C-2.66 13.34 -3.32 12.68 -4 12 C-3.34 10.35 -2.68 8.7 -2 7 C-1.67 7 -1.34 7 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#396884" transform="translate(505,639)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.97 1.66 5.94 2 9 C1.34 9 0.68 9 0 9 C0 12.63 0 16.26 0 20 C-0.33 20 -0.66 20 -1 20 C-1.027 17.229 -1.047 14.458 -1.062 11.688 C-1.075 10.509 -1.075 10.509 -1.088 9.307 C-1.091 8.549 -1.094 7.792 -1.098 7.012 C-1.103 6.315 -1.108 5.619 -1.114 4.901 C-1 3 -1 3 0 0 Z " fill="#1B4569" transform="translate(638,642)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.33 2.64 3.66 5.28 4 8 C2.68 8.33 1.36 8.66 0 9 C0 6.03 0 3.06 0 0 Z " fill="#E2E5E3" transform="translate(384,640)"/>
<path d="M0 0 C1.279 0.103 2.558 0.206 3.875 0.312 C4.954 0.4 4.954 0.4 6.055 0.488 C8 1 8 1 10 4 C8.375 4.75 8.375 4.75 6 5 C2.75 3.125 2.75 3.125 0 1 C0 0.67 0 0.34 0 0 Z " fill="#153B5F" transform="translate(621,633)"/>
<path d="M0 0 C0 1.32 0 2.64 0 4 C-2.64 4.66 -5.28 5.32 -8 6 C-6.65 0.602 -5.511 0 0 0 Z " fill="#184166" transform="translate(272,633)"/>
<path d="M0 0 C2.496 2.276 4.066 4.607 5.688 7.562 C6.124 8.348 6.561 9.133 7.012 9.941 C8 12 8 12 8 14 C5 13 5 13 3.613 10.82 C3.184 9.931 2.755 9.041 2.312 8.125 C1.876 7.241 1.439 6.356 0.988 5.445 C0 3 0 3 0 0 Z " fill="#CAD4D9" transform="translate(492,247)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 1.67 2.98 1.34 4 1 C4 1.66 4 2.32 4 3 C2.35 3 0.7 3 -1 3 C-1.33 3.66 -1.66 4.32 -2 5 C-3.32 5 -4.64 5 -6 5 C-6 4.34 -6 3.68 -6 3 C-6.99 3 -7.98 3 -9 3 C-8.67 2.34 -8.34 1.68 -8 1 C-5.351 0.406 -2.708 0.258 0 0 Z " fill="#9EAB97" transform="translate(767,129)"/>
<path d="M0 0 C1.455 2.91 0.972 5.604 0.84 8.809 C0.841 11.295 0.841 11.295 3 14 C2.01 14.33 1.02 14.66 0 15 C-2.335 11.352 -2.304 8.256 -2 4 C-1 1.562 -1 1.562 0 0 Z " fill="#2F5F7C" transform="translate(595,688)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 2.65 2.34 4.3 2 6 C-0.64 6 -3.28 6 -6 6 C-6 5.34 -6 4.68 -6 4 C-4.02 4 -2.04 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#F7F9F9" transform="translate(223,631)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.65 2 -2.3 2 -4 2 C-4 2.66 -4 3.32 -4 4 C-6.31 3.67 -8.62 3.34 -11 3 C-10.01 2.67 -9.02 2.34 -8 2 C-8 1.34 -8 0.68 -8 0 C-4.979 -0.971 -2.995 -1.136 0 0 Z " fill="#E6EAE6" transform="translate(592,629)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C2.34 7.29 1.68 11.58 1 16 C0.67 16 0.34 16 0 16 C0 10.72 0 5.44 0 0 Z " fill="#BDCBD2" transform="translate(509,419)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C6.66 4.3 7.32 7.6 8 11 C6.68 10.67 5.36 10.34 4 10 C4.33 7.69 4.66 5.38 5 3 C3.35 3 1.7 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#E0E3DD" transform="translate(576,696)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 3.63 2 7.26 2 11 C1.01 10.67 0.02 10.34 -1 10 C-1.027 8.521 -1.046 7.042 -1.062 5.562 C-1.074 4.739 -1.086 3.915 -1.098 3.066 C-1 1 -1 1 0 0 Z " fill="#DFE2DE" transform="translate(417,690)"/>
<path d="M0 0 C2.475 0.99 2.475 0.99 5 2 C4.814 2.536 4.629 3.072 4.438 3.625 C3.886 6.62 4.394 9.037 5 12 C1.662 8.359 1.021 4.714 0 0 Z " fill="#133959" transform="translate(355,687)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.979 1.114 2.959 2.227 2.938 3.375 C2.736 6.832 2.736 6.832 4 9 C3.34 9.66 2.68 10.32 2 11 C1.67 9.68 1.34 8.36 1 7 C0.34 7 -0.32 7 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#306180" transform="translate(614,689)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C3 4 3 4 2.25 6.562 C1 9 1 9 -2 11 C-2.25 3.375 -2.25 3.375 0 0 Z " fill="#103457" transform="translate(662,686)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 1.32 5.34 2.64 5 4 C3.68 4 2.36 4 1 4 C0.67 4.99 0.34 5.98 0 7 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#EBEDEB" transform="translate(644,654)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.019 2.454 1.278 3.796 -0.875 5.375 C-3 6 -3 6 -5.25 5.125 C-5.827 4.754 -6.405 4.383 -7 4 C-7 3.67 -7 3.34 -7 3 C-5.546 2.536 -5.546 2.536 -4.062 2.062 C-1.159 1.305 -1.159 1.305 0 0 Z " fill="#21476B" transform="translate(609,648)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 2.97 2.66 5.94 3 9 C2.34 9 1.68 9 1 9 C0.67 9.99 0.34 10.98 0 12 C-0.99 11.67 -1.98 11.34 -3 11 C-2.01 7.37 -1.02 3.74 0 0 Z " fill="#F0F2F2" transform="translate(573,636)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.98 1.34 3.96 1 6 C1.66 6.33 2.32 6.66 3 7 C3.625 10.062 3.625 10.062 4 13 C3.34 13 2.68 13 2 13 C1.67 11.02 1.34 9.04 1 7 C0.34 7 -0.32 7 -1 7 C-1.043 5 -1.041 3 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A9B29A" transform="translate(62,483)"/>
<path d="M0 0 C4.692 3.388 9.03 6.782 13 11 C7.181 9.723 3.637 6.562 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E3E7EA" transform="translate(428,448)"/>
<path d="M0 0 C2.901 0.353 3.895 0.887 5.922 3.066 C6.525 3.89 7.128 4.714 7.75 5.562 C8.364 6.389 8.977 7.215 9.609 8.066 C10.068 8.704 10.527 9.343 11 10 C10.34 10.66 9.68 11.32 9 12 C8.518 11.362 8.036 10.724 7.539 10.066 C6.907 9.24 6.276 8.414 5.625 7.562 C4.999 6.739 4.372 5.915 3.727 5.066 C2.162 2.915 2.162 2.915 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CBD6DB" transform="translate(645,267)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C6.01 1.485 6.01 1.485 5 3 C4.01 3 3.02 3 2 3 C0.312 4.438 0.312 4.438 -1 6 C-1 5.34 -1 4.68 -1 4 C-2.98 4 -4.96 4 -7 4 C-7 3.67 -7 3.34 -7 3 C-4.69 3 -2.38 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#DFE2DE" transform="translate(659,704)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.66 5 1.32 5 2 C4.01 2.495 4.01 2.495 3 3 C2.342 6.029 2.342 6.029 2 9 C1.34 9 0.68 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#FCFDFD" transform="translate(573,687)"/>
<path d="M0 0 C1 1 1 1 1.062 4.062 C1.042 5.032 1.021 6.001 1 7 C0.484 6.196 -0.031 5.391 -0.562 4.562 C-3.632 1.336 -5.649 1.251 -10 1 C-10 0.67 -10 0.34 -10 0 C-6.361 -0.744 -3.539 -1.311 0 0 Z " fill="#D5DEE3" transform="translate(412,683)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.125 4.5 -0.125 4.5 -2 6 C-5.188 6.188 -5.188 6.188 -8 6 C-3.375 1.125 -3.375 1.125 0 0 Z " fill="#F8F9FA" transform="translate(678,650)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.33 0.99 3.66 1.98 4 3 C3.34 3 2.68 3 2 3 C2 8.28 2 13.56 2 19 C1.67 19 1.34 19 1 19 C0.67 12.73 0.34 6.46 0 0 Z " fill="#3A6A87" transform="translate(700,635)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-6.75 3.125 -6.75 3.125 -9 2 C-9 1.34 -9 0.68 -9 0 C-5.522 -1.159 -3.541 -0.708 0 0 Z " fill="#305C7B" transform="translate(213,637)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.124 1.114 1.247 2.227 1.375 3.375 C1.647 7.031 1.647 7.031 4 9 C3.01 9.33 2.02 9.66 1 10 C0.01 9.01 -0.98 8.02 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z " fill="#F4F4F4" transform="translate(551,691)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C4.66 2 5.32 2 6 2 C6 3.65 6 5.3 6 7 C5.34 6.67 4.68 6.34 4 6 C3.01 6.495 3.01 6.495 2 7 C1.34 4.69 0.68 2.38 0 0 Z " fill="#DEE1DE" transform="translate(622,690)"/>
<path d="M0 0 C1.98 0.495 1.98 0.495 4 1 C4 2.32 4 3.64 4 5 C2.35 4.67 0.7 4.34 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#244E6D" transform="translate(449,675)"/>
<path d="M0 0 C3.465 1.485 3.465 1.485 7 3 C7 3.66 7 4.32 7 5 C4.69 5 2.38 5 0 5 C-0.562 3.062 -0.562 3.062 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F4F7F5" transform="translate(716,651)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5.66 2.98 6.32 4.96 7 7 C6.01 7.33 5.02 7.66 4 8 C3.67 7.34 3.34 6.68 3 6 C3.33 5.34 3.66 4.68 4 4 C3.34 4 2.68 4 2 4 C1.34 2.68 0.68 1.36 0 0 Z " fill="#D3D7CF" transform="translate(204,648)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 1.98 2.34 3.96 2 6 C1.34 6 0.68 6 0 6 C-0.33 6.66 -0.66 7.32 -1 8 C-1.625 5.125 -1.625 5.125 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#E4E5E1" transform="translate(708,640)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 2.64 3 5.28 3 8 C1.667 8 0.333 8 -1 8 C-1.042 5.667 -1.041 3.333 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#DEE1DD" transform="translate(461,640)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-4.875 2.25 -4.875 2.25 -6 0 C-8.025 -0.652 -8.025 -0.652 -10 -1 C-6.19 -2.465 -3.676 -1.552 0 0 Z " fill="#2A5375" transform="translate(529,635)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.98 1.34 4.96 0.68 7 0 C6.34 1.98 5.68 3.96 5 6 C4.01 5.34 3.02 4.68 2 4 C1.01 4.99 0.02 5.98 -1 7 C-1.33 6.34 -1.66 5.68 -2 5 C-1.34 5 -0.68 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#A4AC97" transform="translate(868,570)"/>
<path d="M0 0 C3.333 3.333 6.667 6.667 10 10 C7 10 7 10 4.957 8.25 C4.249 7.507 3.542 6.765 2.812 6 C2.097 5.257 1.382 4.515 0.645 3.75 C0.102 3.173 -0.441 2.595 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C5CFD5" transform="translate(339,307)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.982 4.053 2.082 7.839 2 12 C1.01 12.495 1.01 12.495 0 13 C-1.351 4.052 -1.351 4.052 0 0 Z " fill="#B3C4CD" transform="translate(685,272)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.188 4.562 4.188 4.562 5 8 C4.34 8.66 3.68 9.32 3 10 C-0.006 6.994 0.404 4.176 0 0 Z " fill="#275371" transform="translate(358,691)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2 2.32 2 3 2 C3 5.3 3 8.6 3 12 C2.01 12 1.02 12 0 12 C0.33 10.35 0.66 8.7 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#34607F" transform="translate(630,689)"/>
<path d="M0 0 C0.949 0.041 1.898 0.082 2.875 0.125 C1.875 2.125 1.875 2.125 -0.062 3.062 C-2.445 4.078 -2.445 4.078 -4.125 7.125 C-4.75 4.75 -4.75 4.75 -5.125 2.125 C-3.125 0.125 -3.125 0.125 0 0 Z " fill="#F7F8F8" transform="translate(531.125,693.875)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 5.61 1.66 11.22 2 17 C1.01 17 0.02 17 -1 17 C-0.67 11.39 -0.34 5.78 0 0 Z " fill="#3E718B" transform="translate(639,685)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.188 2.938 1.188 2.938 0 5 C-3.29 6.097 -4.713 5.8 -8 5 C-7.423 4.732 -6.845 4.464 -6.25 4.188 C-3.967 2.983 -2.026 1.592 0 0 Z " fill="#1A4168" transform="translate(674,648)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C6 5.75 6 5.75 6 8 C4.02 7.34 2.04 6.68 0 6 C0 4.02 0 2.04 0 0 Z " fill="#235475" transform="translate(679,644)"/>
<path d="M0 0 C2.97 0.495 2.97 0.495 6 1 C6.33 2.32 6.66 3.64 7 5 C6.01 5.33 5.02 5.66 4 6 C4 5.34 4 4.68 4 4 C2.68 3.67 1.36 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#EEF3F1" transform="translate(820,636)"/>
<path d="M0 0 C0.866 0.557 0.866 0.557 1.75 1.125 C1.42 3.105 1.09 5.085 0.75 7.125 C0.41 6.197 0.41 6.197 0.062 5.25 C-1.629 2.511 -3.242 2.075 -6.25 1.125 C-2.804 -1.09 -2.804 -1.09 0 0 Z " fill="#F6F7F7" transform="translate(802.25,637.875)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C2.68 3.32 1.36 4.64 0 6 C-0.66 5.67 -1.32 5.34 -2 5 C-2.33 5.66 -2.66 6.32 -3 7 C-2.25 2.25 -2.25 2.25 0 0 Z " fill="#1E496C" transform="translate(682,634)"/>
<path d="M0 0 C3.125 -0.188 3.125 -0.188 6 0 C5.67 1.32 5.34 2.64 5 4 C2.69 3.67 0.38 3.34 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#F2F5F5" transform="translate(600,636)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 1 2.02 1 1 1 C1 3.31 1 5.62 1 8 C-0.65 8 -2.3 8 -4 8 C-4 7.01 -4 6.02 -4 5 C-2.68 5 -1.36 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#163D63" transform="translate(378,628)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.114 3.309 -2.228 3.619 -3.375 3.938 C-6.795 4.823 -6.795 4.823 -9 6 C-8.688 4.125 -8.688 4.125 -8 2 C-4.772 -0.152 -3.716 -0.201 0 0 Z " fill="#163A5E" transform="translate(500,633)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9.33 0.99 9.66 1.98 10 3 C6.37 2.67 2.74 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#FAFBF9" transform="translate(721,631)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C11 0.33 11 0.66 11 1 C7.04 1.99 3.08 2.98 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#F9FAF9" transform="translate(514,631)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.333 3.333 -0.333 4.667 -1 6 C-1.383 7.991 -1.726 9.991 -2 12 C-2.99 12.33 -3.98 12.66 -5 13 C-4.485 7.719 -2.745 4.428 0 0 Z " fill="#264F6A" transform="translate(532,399)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C-1.312 3.688 -1.312 3.688 -5 4 C-6.938 2.562 -6.938 2.562 -8 1 C-5.261 -0.37 -3.029 -0.126 0 0 Z " fill="#A9B39C" transform="translate(782,30)"/>
<path d="M0 0 C4.95 0.99 4.95 0.99 10 2 C9.67 2.66 9.34 3.32 9 4 C2.25 4.125 2.25 4.125 0 3 C0 2.01 0 1.02 0 0 Z " fill="#FBFBFB" transform="translate(616,703)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 4.62 2 9.24 2 14 C1.67 14 1.34 14 1 14 C0.67 10.7 0.34 7.4 0 4 C-0.99 3.67 -1.98 3.34 -3 3 C-2 2 -1 1 0 0 Z " fill="#E8E7E3" transform="translate(468,690)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C2.32 6.33 3.64 6.66 5 7 C5 7.66 5 8.32 5 9 C3.35 8.67 1.7 8.34 0 8 C-0.981 4.947 -0.981 3.053 0 0 Z " fill="#F5F6F5" transform="translate(619,692)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.25 6.625 2.25 6.625 0 10 C-0.66 10 -1.32 10 -2 10 C-1.34 6.7 -0.68 3.4 0 0 Z " fill="#F9FAF9" transform="translate(675,690)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.33 0.99 7.66 1.98 8 3 C5.36 3 2.72 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#F4F5F3" transform="translate(551,680)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-3.31 3 -5.62 3 -8 3 C-8.33 2.34 -8.66 1.68 -9 1 C-5.804 -0.065 -3.343 -0.074 0 0 Z " fill="#113A5F" transform="translate(430,651)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-3.308 7.923 -3.308 7.923 -6.812 8.312 C-7.534 8.209 -8.256 8.106 -9 8 C-8.443 7.608 -7.886 7.216 -7.312 6.812 C-4.656 4.731 -2.351 2.417 0 0 Z " fill="#0F345A" transform="translate(337,646)"/>
<path d="M0 0 C0.625 1.875 0.625 1.875 1 4 C-1 6 -1 6 -3.625 6.125 C-4.409 6.084 -5.192 6.043 -6 6 C-6.33 4.68 -6.66 3.36 -7 2 C-6.67 2.66 -6.34 3.32 -6 4 C-2.274 2.881 -2.274 2.881 0 0 Z " fill="#F8F9F8" transform="translate(562,645)"/>
<path d="M0 0 C0.33 3.3 0.66 6.6 1 10 C-0.32 9.34 -1.64 8.68 -3 8 C-3 5.69 -3 3.38 -3 1 C-1 0 -1 0 0 0 Z " fill="#31627E" transform="translate(790,639)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.01 7 -0.98 7 -2 7 C-2.33 7.33 -2.66 7.66 -3 8 C-4.666 8.041 -6.334 8.043 -8 8 C-7.381 7.753 -6.763 7.505 -6.125 7.25 C-3.611 5.771 -3.147 4.621 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#E2E7E6" transform="translate(527,640)"/>
<path d="M0 0 C1.274 2.547 0.802 3.428 0.062 6.125 C-0.132 6.849 -0.327 7.574 -0.527 8.32 C-0.683 8.875 -0.839 9.429 -1 10 C-1.66 9.67 -2.32 9.34 -3 9 C-3.188 5.625 -3.188 5.625 -3 2 C-2.01 1.34 -1.02 0.68 0 0 Z " fill="#F9FAF8" transform="translate(685,638)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4 3 4 3 2 6 C1.34 5.67 0.68 5.34 0 5 C0.33 4.34 0.66 3.68 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#F0F3F3" transform="translate(208,640)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4.33 3.31 4.66 5.62 5 8 C4.01 7.67 3.02 7.34 2 7 C0.812 3.438 0.812 3.438 0 0 Z " fill="#2A5575" transform="translate(606,635)"/>
<path d="M0 0 C-2.853 2.325 -5.541 3.751 -9 5 C-9 3.68 -9 2.36 -9 1 C-6.043 -0.478 -3.258 -0.06 0 0 Z " fill="#10395D" transform="translate(801,633)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.67 1.65 4.34 3.3 4 5 C2.68 4.67 1.36 4.34 0 4 C0 2.68 0 1.36 0 0 Z " fill="#1A4266" transform="translate(444,625)"/>
<path d="M0 0 C2.475 0.99 2.475 0.99 5 2 C5 2.99 5 3.98 5 5 C3.68 5 2.36 5 1 5 C0.34 5.66 -0.32 6.32 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#A1B1BA" transform="translate(776,626)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C4 2.66 4 3.32 4 4 C4.66 4.33 5.32 4.66 6 5 C3.69 5 1.38 5 -1 5 C-1.33 4.01 -1.66 3.02 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#9EA891" transform="translate(897,587)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C4.33 3.99 4.66 4.98 5 6 C4 7 4 7 1.438 7.062 C0.231 7.032 0.231 7.032 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#9AA48D" transform="translate(913,577)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C0.306 4.847 -3.916 5 -8 5 C-5.647 2.276 -3.375 1.186 0 0 Z " fill="#CCD6DA" transform="translate(324,449)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-0.97 4.31 -3.94 6.62 -7 9 C-7 6 -7 6 -5.469 4.395 C-4.494 3.611 -4.494 3.611 -3.5 2.812 C-2.85 2.283 -2.201 1.753 -1.531 1.207 C-1.026 0.809 -0.521 0.41 0 0 Z " fill="#C7D2D7" transform="translate(420,311)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.812 1.875 1.812 1.875 1 4 C-1.562 5.25 -1.562 5.25 -4 6 C-4.66 5.01 -5.32 4.02 -6 3 C-5.196 2.876 -4.391 2.753 -3.562 2.625 C-2.717 2.419 -1.871 2.212 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#F8FAF9" transform="translate(494,696)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-2.97 1.66 -5.94 2.32 -9 3 C-9 1.68 -9 0.36 -9 -1 C-5.799 -2.6 -3.277 -1.032 0 0 Z " fill="#285574" transform="translate(672,686)"/>
<path d="M0 0 C-0.33 3.3 -0.66 6.6 -1 10 C-1.66 8.68 -2.32 7.36 -3 6 C-3.976 4.649 -4.97 3.311 -6 2 C-2.25 0 -2.25 0 0 0 Z " fill="#E2E5E0" transform="translate(633,654)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C3.66 5.64 4.32 8.28 5 11 C3.68 11 2.36 11 1 11 C1 9.02 1 7.04 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#D6DCDB" transform="translate(231,644)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C7 1.99 7 2.98 7 4 C4.69 4 2.38 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#0E3457" transform="translate(775,650)"/>
<path d="M0 0 C-3.323 3.938 -3.323 3.938 -6.75 4.25 C-7.492 4.168 -8.235 4.085 -9 4 C-9 3.34 -9 2.68 -9 2 C-5.865 0.142 -3.625 -0.201 0 0 Z " fill="#FAFBFB" transform="translate(732,647)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.31 1.66 4.62 2 7 C1.01 7.33 0.02 7.66 -1 8 C-1.66 6.02 -2.32 4.04 -3 2 C-2.34 2 -1.68 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#E3E9EC" transform="translate(677,643)"/>
<path d="M0 0 C4.875 0.875 4.875 0.875 6 2 C6.141 4.671 6.042 7.324 6 10 C5.01 10.495 5.01 10.495 4 11 C4 8.36 4 5.72 4 3 C3.01 3.495 3.01 3.495 2 4 C1.34 2.68 0.68 1.36 0 0 Z " fill="#0B2C50" transform="translate(225,640)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.688 3.062 3.688 3.062 4 5 C0.25 6.125 0.25 6.125 -2 5 C-1.34 3.35 -0.68 1.7 0 0 Z " fill="#E7ECEF" transform="translate(618,644)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 2.97 3 5.94 3 9 C2.01 9 1.02 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#356581" transform="translate(512,637)"/>
<path d="M0 0 C1.5 1 1.5 1 3 3 C3.395 5.799 3.227 8.138 3 11 C2.34 11.66 1.68 12.32 1 13 C0.939 12.385 0.879 11.77 0.816 11.137 C0.733 10.328 0.649 9.52 0.562 8.688 C0.481 7.887 0.4 7.086 0.316 6.262 C0.047 3.839 0.047 3.839 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#204E71" transform="translate(595,637)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 2.31 2.66 4.62 3 7 C1.68 7 0.36 7 -1 7 C-1.043 5 -1.041 3 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#496A82" transform="translate(581,633)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.34 2 2.68 2 2 2 C2 2.66 2 3.32 2 4 C0.02 4.99 0.02 4.99 -2 6 C-2 5.34 -2 4.68 -2 4 C-2.66 3.67 -3.32 3.34 -4 3 C-2.68 2.01 -1.36 1.02 0 0 Z " fill="#1E496C" transform="translate(396,633)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C0.37 3.31 -3.26 5.62 -7 8 C-4.984 3.968 -3.72 2.391 0 0 Z " fill="#E0E7EA" transform="translate(425,380)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.25 4.5 -0.25 4.5 -2 6 C-3.32 6 -4.64 6 -6 6 C-6 5.34 -6 4.68 -6 4 C-5.01 4 -4.02 4 -3 4 C-2.67 3.01 -2.34 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#F7F9F9" transform="translate(535,696)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.398 4.02 0.727 6.021 0 8 C-0.99 8.495 -0.99 8.495 -2 9 C-2.125 5.625 -2.125 5.625 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#D9DEDB" transform="translate(431,690)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 2.32 2 3.64 2 5 C-1.3 4.67 -4.6 4.34 -8 4 C-8 3.67 -8 3.34 -8 3 C-5.36 3 -2.72 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#0F375C" transform="translate(588,639)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.986 1.488 -3.971 1.977 -5.957 2.465 C-7.657 2.91 -9.333 3.444 -11 4 C-8.363 -1.84 -6.049 -1.008 0 0 Z " fill="#245070" transform="translate(562,634)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.99 7 1.98 7 3 C4.69 3 2.38 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#184064" transform="translate(422,633)"/>
<path d="M0 0 C-4.455 0.495 -4.455 0.495 -9 1 C-9 1.66 -9 2.32 -9 3 C-9.99 3.33 -10.98 3.66 -12 4 C-11.67 2.35 -11.34 0.7 -11 -1 C-3.375 -2.25 -3.375 -2.25 0 0 Z " fill="#E4E9E6" transform="translate(803,631)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C4 4 4 4 1.938 5.188 C1.298 5.456 0.659 5.724 0 6 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#F1F3F4" transform="translate(760,630)"/>
<path d="M0 0 C2.172 3.259 4.071 6.592 6 10 C5.01 10 4.02 10 3 10 C2.303 8.713 1.618 7.421 0.938 6.125 C0.363 5.046 0.363 5.046 -0.223 3.945 C-0.479 3.303 -0.736 2.661 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C3CFD6" transform="translate(548,440)"/>
<path d="M0 0 C4.219 0.479 6.797 1.166 10 4 C6.25 5.125 6.25 5.125 4 4 C4 3.34 4 2.68 4 2 C2.68 1.34 1.36 0.68 0 0 Z " fill="#2B5974" transform="translate(363,419)"/>
<path d="M0 0 C2.648 2.578 4.944 4.916 7 8 C4 8 4 8 2.613 6.688 C1.409 5.125 0.204 3.562 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#BBCAD0" transform="translate(317,415)"/>
<path d="M0 0 C4.068 1.849 7.009 3.664 10 7 C6.001 6.385 3.196 4.397 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CCD6DA" transform="translate(481,379)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2.33 2.32 2.66 3 3 C2.01 3.495 2.01 3.495 1 4 C0.609 6.095 0.609 6.095 0.5 8.5 C0.344 10.969 0.344 10.969 0 13 C-0.99 13.495 -0.99 13.495 -2 14 C-1.859 12.041 -1.712 10.083 -1.562 8.125 C-1.481 7.034 -1.4 5.944 -1.316 4.82 C-1 2 -1 2 0 0 Z " fill="#E7E9E6" transform="translate(590,687)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.124 0.908 1.247 1.815 1.375 2.75 C1.921 5.59 2.573 7.526 4 10 C3.01 10.33 2.02 10.66 1 11 C0 10 0 10 -0.098 7.715 C-0.086 6.798 -0.074 5.882 -0.062 4.938 C-0.053 4.018 -0.044 3.099 -0.035 2.152 C-0.024 1.442 -0.012 0.732 0 0 Z " fill="#D8DCD9" transform="translate(644,688)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.32 4 2.64 4 4 C2.35 4 0.7 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#173555" transform="translate(381,676)"/>
<path d="M0 0 C2.475 0.99 2.475 0.99 5 2 C5 3.32 5 4.64 5 6 C1.839 4.63 1.007 4.011 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#ECF0F0" transform="translate(354,651)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 2.31 2 4.62 2 7 C1.01 7 0.02 7 -1 7 C-1.33 6.34 -1.66 5.68 -2 5 C-1.062 2.375 -1.062 2.375 0 0 Z " fill="#EAEDEC" transform="translate(405,640)"/>
<path d="M0 0 C-0.99 0.33 -1.98 0.66 -3 1 C-3 1.99 -3 2.98 -3 4 C-4.65 3.67 -6.3 3.34 -8 3 C-8 2.34 -8 1.68 -8 1 C-5.072 0.024 -3.044 -0.082 0 0 Z " fill="#F5F7F5" transform="translate(375,638)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 2.98 4 4.96 4 7 C3.34 6.34 2.68 5.68 2 5 C1.01 5.495 1.01 5.495 0 6 C0 4.02 0 2.04 0 0 Z " fill="#ECEFEC" transform="translate(413,638)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4.33 2.65 4.66 4.3 5 6 C3.062 5.25 3.062 5.25 1 4 C0.25 1.875 0.25 1.875 0 0 Z " fill="#F7F9F8" transform="translate(522,637)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.68 2.32 0.36 3.64 -1 5 C-1.99 4.67 -2.98 4.34 -4 4 C-2.68 2.68 -1.36 1.36 0 0 Z " fill="#1B4567" transform="translate(764,633)"/>
<path d="M0 0 C2.438 0.75 2.438 0.75 5 2 C5.812 4.125 5.812 4.125 6 6 C5.34 5.34 4.68 4.68 4 4 C1.677 3.6 -0.657 3.26 -3 3 C-2.01 2.01 -1.02 1.02 0 0 Z " fill="#204768" transform="translate(726,633)"/>
<path d="M0 0 C0.186 0.959 0.186 0.959 0.375 1.938 C0.581 2.618 0.788 3.299 1 4 C1.66 4.33 2.32 4.66 3 5 C0 7 0 7 -3 7 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#A7AF9B" transform="translate(863,577)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.01 4.33 -0.98 4.66 -2 5 C-2.33 7.31 -2.66 9.62 -3 12 C-3.33 12 -3.66 12 -4 12 C-3.286 3.429 -3.286 3.429 0 0 Z " fill="#204C68" transform="translate(320,388)"/>
<path d="M0 0 C3.995 0.615 6.745 2.706 10 5 C9.34 5.66 8.68 6.32 8 7 C6.664 6.025 5.331 5.045 4 4.062 C3.258 3.517 2.515 2.972 1.75 2.41 C1.173 1.945 0.595 1.479 0 1 C0 0.67 0 0.34 0 0 Z " fill="#D2DADF" transform="translate(603,379)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.63 4.161 0.011 4.993 -3 7 C-3.33 6.01 -3.66 5.02 -4 4 C-2.68 2.68 -1.36 1.36 0 0 Z " fill="#355C75" transform="translate(458,225)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 3.32 -0.98 4.64 -2 6 C-4.97 5.505 -4.97 5.505 -8 5 C-7.423 4.732 -6.845 4.464 -6.25 4.188 C-3.967 2.983 -2.026 1.592 0 0 Z " fill="#103559" transform="translate(652,698)"/>
<path d="M0 0 C5.875 3.75 5.875 3.75 7 6 C4.625 6.188 4.625 6.188 2 6 C0 3 0 3 0 0 Z " fill="#0C3157" transform="translate(614,696)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4 0.68 4 0 4 C-0.33 4.66 -0.66 5.32 -1 6 C-2.32 5.34 -3.64 4.68 -5 4 C-4.01 4 -3.02 4 -2 4 C-1.67 3.01 -1.34 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#E2E6E2" transform="translate(357,695)"/>
<path d="M0 0 C1.429 2.354 2.087 3.48 1.625 6.25 C1.316 7.116 1.316 7.116 1 8 C0.567 7.103 0.567 7.103 0.125 6.188 C-1.047 3.807 -1.047 3.807 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#F0F0EB" transform="translate(643,680)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 2.98 2.34 4.96 2 7 C0.68 6.67 -0.64 6.34 -2 6 C-1.34 4.02 -0.68 2.04 0 0 Z " fill="#FCFCFC" transform="translate(420,676)"/>
<path d="M0 0 C-0.66 1.32 -1.32 2.64 -2 4 C-3.32 3.67 -4.64 3.34 -6 3 C-6 2.01 -6 1.02 -6 0 C-2.25 -1.125 -2.25 -1.125 0 0 Z " fill="#EFF1EC" transform="translate(226,655)"/>
<path d="M0 0 C2.31 0.66 4.62 1.32 7 2 C7 2.66 7 3.32 7 4 C4.625 4.625 4.625 4.625 2 5 C1.34 4.34 0.68 3.68 0 3 C0 2.01 0 1.02 0 0 Z " fill="#133D63" transform="translate(640,649)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.25 4.75 1.25 4.75 -1 7 C-1.99 6.67 -2.98 6.34 -4 6 C-3.526 5.567 -3.051 5.134 -2.562 4.688 C-0.763 2.977 -0.763 2.977 0 0 Z " fill="#F9FAF9" transform="translate(372,648)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 2.65 1.68 4.3 1 6 C0.01 6 -0.98 6 -2 6 C-2 4.35 -2 2.7 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#2D5D7C" transform="translate(737,645)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.35 2.65 0.7 4.3 -1 6 C-1.33 5.01 -1.66 4.02 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#123B60" transform="translate(553,643)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 1.65 5.34 3.3 5 5 C4.01 5 3.02 5 2 5 C1.67 4.34 1.34 3.68 1 3 C1.33 2.34 1.66 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#DEE6E7" transform="translate(559,643)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.188 3.938 2.188 3.938 1 7 C0.01 7.33 -0.98 7.66 -2 8 C-2 6.68 -2 5.36 -2 4 C-1.34 4 -0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#DDE1DC" transform="translate(796,639)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.66 2.31 2.32 4.62 3 7 C1.68 7 0.36 7 -1 7 C-1.625 5.188 -1.625 5.188 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#E1EAED" transform="translate(579,635)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C2.99 2 3.98 2 5 2 C4.67 2.99 4.34 3.98 4 5 C2.35 4.67 0.7 4.34 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#D4DFE5" transform="translate(766,634)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.33 0.66 5.66 1.32 6 2 C5.67 2.66 5.34 3.32 5 4 C3.02 4 1.04 4 -1 4 C-0.34 3.34 0.32 2.68 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#EDF2F3" transform="translate(361,636)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.99 7 1.98 7 3 C5.02 3 3.04 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#123B61" transform="translate(775,633)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 2.98 5 4.96 5 7 C4.34 5.68 3.68 4.36 3 3 C2.34 3 1.68 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#F6F8F8" transform="translate(587,630)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.33 1.98 3.66 3.96 4 6 C2.68 5.67 1.36 5.34 0 5 C0 4.01 0 3.02 0 2 C0.66 2.33 1.32 2.66 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#7494A3" transform="translate(351,626)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.625 3.062 2.625 3.062 3 5 C1.02 5.99 1.02 5.99 -1 7 C-1.33 5.02 -1.66 3.04 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#D7DEDC" transform="translate(358,624)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.65 3.98 -2.3 5.96 -4 8 C-4.66 7.67 -5.32 7.34 -6 7 C-4.196 3.735 -3.166 2.111 0 0 Z " fill="#2C5570" transform="translate(387,464)"/>
<path d="M0 0 C3.536 3.173 5.586 5.167 6 10 C5.381 9.216 4.763 8.433 4.125 7.625 C2.101 5.032 2.101 5.032 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CED7DB" transform="translate(689,451)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.004 4.824 0.004 4.824 -1.438 6.688 C-1.911 7.31 -2.384 7.933 -2.871 8.574 C-3.244 9.045 -3.616 9.515 -4 10 C-4.66 9.34 -5.32 8.68 -6 8 C-4.73 6.503 -3.461 5.005 -2.191 3.508 C-0.912 1.994 -0.912 1.994 0 0 Z " fill="#C4CFD6" transform="translate(605,444)"/>
<path d="M0 0 C0 1.98 0 3.96 0 6 C-1.65 5.67 -3.3 5.34 -5 5 C-5 4.34 -5 3.68 -5 3 C-1.423 0 -1.423 0 0 0 Z " fill="#A6B099" transform="translate(968,330)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-5.294 6 -5.294 6 -9 6 C-3.154 0 -3.154 0 0 0 Z " fill="#C9D5DA" transform="translate(245,227)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C8.33 0.99 8.66 1.98 9 3 C4.545 2.01 4.545 2.01 0 1 C0 0.67 0 0.34 0 0 Z " fill="#1F4F71" transform="translate(620,685)"/>
<path d="M0 0 C1.236 0.017 1.236 0.017 2.496 0.035 C3.322 0.044 4.149 0.053 5 0.062 C5.638 0.074 6.276 0.086 6.934 0.098 C1.807 2.715 1.807 2.715 -1.066 3.098 C-1.726 2.438 -2.386 1.778 -3.066 1.098 C-2.066 0.098 -2.066 0.098 0 0 Z " fill="#093156" transform="translate(552.06640625,683.90234375)"/>
<path d="M0 0 C3 1 3 1 7 3 C4.03 3.66 1.06 4.32 -2 5 C-1.34 3.35 -0.68 1.7 0 0 Z " fill="#D2DCE0" transform="translate(619,681)"/>
<path d="M0 0 C3.125 -0.188 3.125 -0.188 6 0 C6.33 0.66 6.66 1.32 7 2 C2.545 2.495 2.545 2.495 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#FCFDFB" transform="translate(666,681)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C1.625 2 1.625 2 0.438 4.062 C-0.553 4.393 -1.543 4.722 -2.562 5.062 C-3.125 3.125 -3.125 3.125 -3.562 1.062 C-2.562 0.062 -2.562 0.062 0 0 Z " fill="#DDE0DE" transform="translate(661.5625,679.9375)"/>
<path d="M0 0 C2.962 0.613 4.381 1.254 7 3 C0.375 4.25 0.375 4.25 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E6EAE5" transform="translate(303,654)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 2.98 2 4.96 2 7 C1.01 6.67 0.02 6.34 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#38627D" transform="translate(228,642)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C-0.98 6.66 -2.96 7.32 -5 8 C-4.192 6.664 -3.378 5.331 -2.562 4 C-2.11 3.258 -1.658 2.515 -1.191 1.75 C-0.798 1.173 -0.405 0.595 0 0 Z " fill="#E7EBEC" transform="translate(503,640)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.546 1.577 2.092 2.155 1.625 2.75 C0.159 4.78 -0.949 6.735 -2 9 C-2.125 5.625 -2.125 5.625 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#FBFCFB" transform="translate(665,637)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.68 2.32 1.36 3.64 0 5 C-0.66 4.67 -1.32 4.34 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#2E5977" transform="translate(312,636)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.99 7 1.98 7 3 C5.35 3 3.7 3 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#0E355B" transform="translate(381,633)"/>
<path d="M0 0 C0 1.65 0 3.3 0 5 C-1.32 4.67 -2.64 4.34 -4 4 C-4 4.66 -4 5.32 -4 6 C-4.66 5.67 -5.32 5.34 -6 5 C-6 4.34 -6 3.68 -6 3 C-5.01 3 -4.02 3 -3 3 C-3 2.34 -3 1.68 -3 1 C-1 0 -1 0 0 0 Z " fill="#DFE3DF" transform="translate(769,625)"/>
<path d="M0 0 C1.65 1.65 3.3 3.3 5 5 C4.67 5.66 4.34 6.32 4 7 C3.01 6.01 2.02 5.02 1 4 C0.01 4.495 0.01 4.495 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#36627C" transform="translate(665,416)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.381 1.248 1.763 1.495 1.125 1.75 C-1.351 3.206 -1.951 4.378 -3 7 C-3.33 6.01 -3.66 5.02 -4 4 C-2.68 2.68 -1.36 1.36 0 0 Z " fill="#C2CFD6" transform="translate(556,401)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C0.796 2.494 -0.454 4.681 -2 7 C-2.66 7 -3.32 7 -4 7 C-2.926 4.085 -2.222 2.222 0 0 Z " fill="#C5D0D7" transform="translate(648,378)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.625 2.5 0.625 2.5 -1 4 C-1.66 4 -2.32 4 -3 4 C-3 4.66 -3 5.32 -3 6 C-3.66 6 -4.32 6 -5 6 C-3.63 2.839 -3.011 2.007 0 0 Z " fill="#A7B19A" transform="translate(959,339)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 5.34 0.34 4.68 0 4 C-0.66 4 -1.32 4 -2 4 C-2.33 4.99 -2.66 5.98 -3 7 C-3.33 5.68 -3.66 4.36 -4 3 C-3.01 2.67 -2.02 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#A2AA95" transform="translate(879,569)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 1.65 4 3.3 4 5 C3.34 5 2.68 5 2 5 C1.34 3.35 0.68 1.7 0 0 Z " fill="#A8B19D" transform="translate(874,616)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 2.32 0.68 3.64 0 5 C-0.562 3.062 -0.562 3.062 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A1AA8F" transform="translate(55,842)"/>
<path d="M0 0 C3.875 1.75 3.875 1.75 5 4 C3.062 3.688 3.062 3.688 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#A3AD94" transform="translate(879,831)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C2.35 3.66 0.7 4.32 -1 5 C-1.33 4.34 -1.66 3.68 -2 3 C-1.34 3 -0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#AEB79E" transform="translate(783,825)"/>
<path d="M0 0 C2 3 2 3 1.625 5.188 C1.316 6.085 1.316 6.085 1 7 C0 6 0 6 -0.062 2.938 C-0.042 1.968 -0.021 0.999 0 0 Z " fill="#A9B29B" transform="translate(54,463)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 2.32 1.34 3.64 1 5 C1 4.34 1 3.68 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#A3AC93" transform="translate(894,843)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.65 2 3.3 2 5 C1.34 5 0.68 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#ABB49F" transform="translate(870,611)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 3.485 0.01 3.485 -1 5 C-1.66 3.68 -2.32 2.36 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#A7B19A" transform="translate(732,42)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.33 1.32 3.66 2.64 4 4 C2.68 2.68 1.36 1.36 0 0 Z " fill="#7D8972" transform="translate(345,912)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C6.34 0.66 5.68 1.32 5 2 C2.375 1.625 2.375 1.625 0 1 C0 0.67 0 0.34 0 0 Z " fill="#D4D9D3" transform="translate(447,709)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.01 3.33 0.02 3.66 -1 4 C-1.33 3.01 -1.66 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#A6AE99" transform="translate(876,588)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C3.68 0.99 2.36 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#A3AC96" transform="translate(878,579)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.65 2 3.3 2 5 C1.34 5 0.68 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#A8B19A" transform="translate(54,453)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.01 3.67 -0.98 3.34 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#A7B197" transform="translate(863,993)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C2.66 2.33 3.32 2.66 4 3 C3.01 3 2.02 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#A5AF94" transform="translate(911,881)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.99 2 1.98 2 3 C1.01 2.67 0.02 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#9FA98E" transform="translate(62,859)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 1.99 3.34 2.98 3 4 C3 3.34 3 2.68 3 2 C2.01 1.67 1.02 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A5AF96" transform="translate(867,829)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C3.02 0.99 3.02 0.99 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D4D9D2" transform="translate(663,709)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.01 2.495 3.01 2.495 2 3 C1.01 2.34 0.02 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D3D8D0" transform="translate(378,708)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.34 1.66 3.68 2.32 3 3 C2.01 2.01 1.02 1.02 0 0 Z " fill="#A8B19C" transform="translate(875,613)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.01 4 0.02 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#9FA993" transform="translate(894,590)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-1.33 3.01 -1.66 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#A4AD98" transform="translate(877,580)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C2.68 2.33 1.36 2.66 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9FA892" transform="translate(884,566)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 1.33 5 1.66 5 2 C3.35 2 1.7 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7594A3" transform="translate(349,438)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.34 1.66 3.68 2.32 3 3 C2.01 2.01 1.02 1.02 0 0 Z " fill="#9DA995" transform="translate(737,128)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 3.68 -0.32 2.36 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A7B19A" transform="translate(734,39)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 1.66 0.68 2.32 0 3 C-0.66 2.34 -1.32 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#A9B39A" transform="translate(823,1009)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.99 1.34 2.98 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#9FA98F" transform="translate(57,849)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 3.68 -0.32 2.36 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9EA78D" transform="translate(53,848)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C2.68 1.33 1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#ABB49C" transform="translate(124,750)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C3.02 0.99 3.02 0.99 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D6D9D3" transform="translate(601,709)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.01 -0.32 3.02 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#DADED9" transform="translate(444,691)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.67 -0.32 4.34 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#D7DDDB" transform="translate(481,651)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#A7AF9B" transform="translate(880,617)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.01 2.67 -0.98 2.34 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#ADB5A1" transform="translate(868,613)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.34 -0.32 3.68 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#A7B09B" transform="translate(871,592)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.01 2.67 -0.98 2.34 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#9EA790" transform="translate(903,579)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.99 2 1.98 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#A9B29B" transform="translate(54,459)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.99 1.34 2.98 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#6B8B9E" transform="translate(400,404)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#6A8B9D" transform="translate(728,300)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.34 2.34 -0.32 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#AAB39B" transform="translate(788,36)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 0.99 2.66 1.98 3 3 C2.01 2.67 1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A9B39B" transform="translate(743,33)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7C8872" transform="translate(347,917)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1.66 0.02 2.32 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#ADB69D" transform="translate(761,841)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1.66 1.68 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#ADB69D" transform="translate(764,839)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.34 -0.32 2.68 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#ADB69E" transform="translate(759,837)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C1.68 1.67 0.36 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#ABB49C" transform="translate(132,750)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D7DCD4" transform="translate(597,709)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 1.67 1.02 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#D4D8D1" transform="translate(407,709)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.34 -0.32 2.68 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#DDE1DC" transform="translate(467,695)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DAE0DD" transform="translate(571,672)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#D7DDDC" transform="translate(480,643)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DEE1DE" transform="translate(656,632)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.68 1.33 0.36 1.66 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#9CA58F" transform="translate(907,600)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.99 2.34 1.98 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#A6AF9A" transform="translate(875,596)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-0.66 2.34 -1.32 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#9AA38C" transform="translate(919,579)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8E9EA5" transform="translate(657,483)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2.66 0.68 3.32 0 4 C0 2.68 0 1.36 0 0 Z " fill="#6E8E9E" transform="translate(480,441)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2 0.02 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#6F8EA0" transform="translate(630,401)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.01 3.495 1.01 3.495 0 4 C0 2.68 0 1.36 0 0 Z " fill="#6F90A0" transform="translate(528,393)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#A6B099" transform="translate(974,328)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#6F8E9E" transform="translate(800,292)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.99 1.34 2.98 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#6B8C9E" transform="translate(358,291)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.01 2.67 1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#68899A" transform="translate(436,256)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.01 2.67 0.02 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#A8B29B" transform="translate(738,43)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A9B49A" transform="translate(815,1010)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A9B399" transform="translate(807,1010)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#A8B299" transform="translate(814,1007)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A8B197" transform="translate(857,996)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#A0AA90" transform="translate(928,909)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#86917A" transform="translate(331,877)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A3AD92" transform="translate(128,875)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#A2AD93" transform="translate(140,874)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A6B096" transform="translate(905,869)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#A0AA8F" transform="translate(57,845)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#A3AE94" transform="translate(887,837)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#ABB49C" transform="translate(823,817)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D6DBD4" transform="translate(641,709)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#D2D8D1" transform="translate(495,709)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#D7DCD7" transform="translate(616,708)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D7DAD3" transform="translate(568,707)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D9DCD5" transform="translate(520,706)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D4D9D5" transform="translate(372,701)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#DCE0DB" transform="translate(764,647)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#A1AA95" transform="translate(890,620)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#9DA790" transform="translate(903,596)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#A1AA95" transform="translate(883,577)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#AAB39B" transform="translate(68,492)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#95A4A8" transform="translate(336,483)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#A9B29A" transform="translate(55,480)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#A9B29A" transform="translate(54,470)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#6C8C9D" transform="translate(681,445)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#A4AD97" transform="translate(114,403)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6C8FA1" transform="translate(550,376)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A7AF99" transform="translate(996,321)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#688A9D" transform="translate(488,299)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#AAB39B" transform="translate(782,34)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A9B39B" transform="translate(742,31)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A9B399" transform="translate(818,1011)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A9B399" transform="translate(811,1009)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A5AF94" transform="translate(893,976)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A6B095" transform="translate(898,968)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A5AF95" transform="translate(915,940)"/>
<path d="" fill="#A4AD94" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7D8972" transform="translate(345,914)"/>
<path d="" fill="#A4AF95" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#858F79" transform="translate(354,880)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A3AC93" transform="translate(142,874)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#A5AF96" transform="translate(878,837)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A4AD94" transform="translate(884,835)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#AAB39A" transform="translate(828,814)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#ABB59B" transform="translate(820,814)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A3AC92" transform="translate(57,803)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#ACB59D" transform="translate(190,789)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#AEB6A0" transform="translate(152,755)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#AAB49B" transform="translate(119,753)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D8DCD7" transform="translate(621,709)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D2D8D4" transform="translate(536,709)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D4DAD3" transform="translate(487,709)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D5D9D3" transform="translate(424,709)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D3D8D3" transform="translate(383,709)"/>
<path d="" fill="#D7DBD7" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DADED7" transform="translate(417,704)"/>
<path d="" fill="#DBE0DA" transform="translate(0,0)"/>
<path d="" fill="#D8DCD7" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D8DCD8" transform="translate(583,690)"/>
<path d="" fill="#D4D9D6" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#DDE0DA" transform="translate(628,662)"/>
<path d="" fill="#DBDEDA" transform="translate(0,0)"/>
<path d="" fill="#D5DAD3" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#DEE1DC" transform="translate(589,652)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D9DEDA" transform="translate(296,651)"/>
<path d="" fill="#CAD0C9" transform="translate(0,0)"/>
<path d="" fill="#DBE0DB" transform="translate(0,0)"/>
<path d="" fill="#DADCD9" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#DDE2DD" transform="translate(368,628)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B4BBA8" transform="translate(856,621)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B3BAA7" transform="translate(853,611)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9CA58E" transform="translate(905,603)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A4AD98" transform="translate(877,584)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9FA792" transform="translate(894,583)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A1AA94" transform="translate(887,580)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A7AF9A" transform="translate(865,578)"/>
<path d="" fill="#A0A893" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#A9B29A" transform="translate(61,492)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#96A5A8" transform="translate(341,484)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A4AC97" transform="translate(966,483)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7A95A3" transform="translate(528,458)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7493A2" transform="translate(402,454)"/>
<path d="" fill="#728FA1" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#698C9D" transform="translate(473,450)"/>
<path d="" fill="#708FA0" transform="translate(0,0)"/>
<path d="" fill="#6C8DA1" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#678A9C" transform="translate(701,424)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A7B09B" transform="translate(139,421)"/>
<path d="" fill="#7192A3" transform="translate(0,0)"/>
<path d="" fill="#6B8BA0" transform="translate(0,0)"/>
<path d="" fill="#6A89A0" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6E8FA0" transform="translate(390,398)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#6A8C9F" transform="translate(464,398)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6E8DA0" transform="translate(577,397)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#688A9B" transform="translate(678,392)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6E8FA2" transform="translate(529,392)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A7B19A" transform="translate(989,327)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A6B099" transform="translate(982,327)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#6F8D9E" transform="translate(400,327)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#708FA0" transform="translate(405,325)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#A7B099" transform="translate(988,324)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#688A9D" transform="translate(673,308)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#688A9D" transform="translate(668,302)"/>
<path d="" fill="#6C8D9F" transform="translate(0,0)"/>
<path d="" fill="#708D99" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6A8A9C" transform="translate(520,234)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#688999" transform="translate(485,225)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9CA995" transform="translate(742,130)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9EAA97" transform="translate(773,128)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9EAA97" transform="translate(756,129)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9DAA96" transform="translate(748,129)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9FAC97" transform="translate(774,126)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A0AA94" transform="translate(702,87)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#95A08B" transform="translate(249,85)"/>
<path d="" fill="#A1AB94" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A8B19A" transform="translate(733,44)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9FA992" transform="translate(259,37)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A8B29A" transform="translate(733,36)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A9B49C" transform="translate(785,35)"/>
<path d="" fill="#A8B29B" transform="translate(0,0)"/>
<path d="" fill="#A9B498" transform="translate(0,0)"/>
<path d="" fill="#A9B399" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B399" transform="translate(820,1004)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B198" transform="translate(848,1003)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8B29B" transform="translate(793,1003)"/>
<path d="" fill="#A9B197" transform="translate(0,0)"/>
<path d="" fill="#A9B398" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B299" transform="translate(788,1002)"/>
<path d="" fill="#A8B198" transform="translate(0,0)"/>
<path d="" fill="#A8B098" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A7B298" transform="translate(775,999)"/>
<path d="" fill="#A5AF96" transform="translate(0,0)"/>
<path d="" fill="#A8B098" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A6B095" transform="translate(889,979)"/>
<path d="" fill="#A6B198" transform="translate(0,0)"/>
<path d="" fill="#A4AE94" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5AF94" transform="translate(734,939)"/>
<path d="" fill="#A4AD93" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9FAB8F" transform="translate(929,907)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7F8973" transform="translate(361,907)"/>
<path d="" fill="#A6AE94" transform="translate(0,0)"/>
<path d="" fill="#A6AF95" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A3AF94" transform="translate(917,892)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#848E78" transform="translate(348,892)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A7B197" transform="translate(730,888)"/>
<path d="" fill="#85907A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A0AC8F" transform="translate(110,883)"/>
<path d="" fill="#A0AA8F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#868F7B" transform="translate(351,881)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A3AD93" transform="translate(916,876)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8B197" transform="translate(732,876)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A6AF95" transform="translate(910,875)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B299" transform="translate(735,875)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A3AD93" transform="translate(135,872)"/>
<path d="" fill="#A4AE95" transform="translate(0,0)"/>
<path d="" fill="#AAB39A" transform="translate(0,0)"/>
<path d="" fill="#A9B299" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8B198" transform="translate(729,867)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5AF96" transform="translate(902,863)"/>
<path d="" fill="#A4AE95" transform="translate(0,0)"/>
<path d="" fill="#A5AF95" transform="translate(0,0)"/>
<path d="" fill="#A5AE94" transform="translate(0,0)"/>
<path d="" fill="#9EA88E" transform="translate(0,0)"/>
<path d="" fill="#A5AF95" transform="translate(0,0)"/>
<path d="" fill="#ADB59D" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A0AA90" transform="translate(52,841)"/>
<path d="" fill="#A0AA91" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5AF95" transform="translate(876,836)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A4AE95" transform="translate(878,835)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A2AC90" transform="translate(57,835)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEB79E" transform="translate(766,833)"/>
<path d="" fill="#A2AB91" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AFB79F" transform="translate(773,831)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEB79F" transform="translate(779,829)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A3AE94" transform="translate(873,827)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ADB59D" transform="translate(814,823)"/>
<path d="" fill="#ADB79F" transform="translate(0,0)"/>
<path d="" fill="#AAB39A" transform="translate(0,0)"/>
<path d="" fill="#ADB79E" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ADB79D" transform="translate(799,817)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ADB69F" transform="translate(805,816)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B29B" transform="translate(830,815)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AAB39B" transform="translate(823,815)"/>
<path d="" fill="#ABB59D" transform="translate(0,0)"/>
<path d="" fill="#A3AC92" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACB59E" transform="translate(184,781)"/>
<path d="" fill="#ACB69D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACB59F" transform="translate(142,751)"/>
<path d="" fill="#D2D7CF" transform="translate(0,0)"/>
<path d="" fill="#D1D6CF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D3D9D2" transform="translate(485,710)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D6DCD3" transform="translate(655,709)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D7DDD6" transform="translate(625,709)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D7DCD5" transform="translate(555,709)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D8DBD6" transform="translate(525,709)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D5DAD2" transform="translate(455,709)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D4D9D2" transform="translate(392,709)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D2D6D0" transform="translate(364,709)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD6CD" transform="translate(360,709)"/>
<path d="" fill="#D8DAD4" transform="translate(0,0)"/>
<path d="" fill="#D1D6D2" transform="translate(0,0)"/>
<path d="" fill="#D7DBDB" transform="translate(0,0)"/>
<path d="" fill="#DBDFDA" transform="translate(0,0)"/>
<path d="" fill="#D5DAD5" transform="translate(0,0)"/>
<path d="" fill="#DBDFDC" transform="translate(0,0)"/>
<path d="" fill="#D8DCD7" transform="translate(0,0)"/>
<path d="" fill="#DCDED9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#E1E5DE" transform="translate(554,691)"/>
<path d="" fill="#D8DDD8" transform="translate(0,0)"/>
<path d="" fill="#DBDEDB" transform="translate(0,0)"/>
<path d="" fill="#DBDFDA" transform="translate(0,0)"/>
<path d="" fill="#DADEDC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D8DEDC" transform="translate(520,680)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D9DDD9" transform="translate(638,667)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DDE1DB" transform="translate(628,660)"/>
<path d="" fill="#D4D9D1" transform="translate(0,0)"/>
<path d="" fill="#D0D5CD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D5DAD3" transform="translate(739,658)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DADDD8" transform="translate(701,658)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D9DDD8" transform="translate(428,658)"/>
<path d="" fill="#D9DDDC" transform="translate(0,0)"/>
<path d="" fill="#D9DEDA" transform="translate(0,0)"/>
<path d="" fill="#DDE2E0" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DBE0DB" transform="translate(244,643)"/>
<path d="" fill="#CFD7D1" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CCD2CA" transform="translate(827,628)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D4DAD2" transform="translate(812,628)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DEE2DC" transform="translate(636,628)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DCE1DC" transform="translate(580,628)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DAE0D9" transform="translate(556,628)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DDE0DC" transform="translate(364,628)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DCE2DC" transform="translate(336,628)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D7DDD8" transform="translate(229,624)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#DADDD8" transform="translate(540,620)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5AD99" transform="translate(883,615)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5AE99" transform="translate(880,608)"/>
<path d="" fill="#A6AE99" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B19B" transform="translate(870,598)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1A993" transform="translate(891,594)"/>
<path d="" fill="#A5AD98" transform="translate(0,0)"/>
<path d="" fill="#99A38C" transform="translate(0,0)"/>
<path d="" fill="#A8AF9D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9DA790" transform="translate(906,584)"/>
<path d="" fill="#9FA891" transform="translate(0,0)"/>
<path d="" fill="#A6AE99" transform="translate(0,0)"/>
<path d="" fill="#A8B09A" transform="translate(0,0)"/>
<path d="" fill="#9CA68F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1AA96" transform="translate(876,570)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A0A893" transform="translate(885,569)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1A993" transform="translate(882,569)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9CA58E" transform="translate(901,568)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ABB59D" transform="translate(125,510)"/>
<path d="" fill="#A9B29B" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AAB49B" transform="translate(82,507)"/>
<path d="" fill="#AAB39C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#95A6AC" transform="translate(579,486)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#93A4AB" transform="translate(441,485)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#92A3A9" transform="translate(360,484)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#90A1A8" transform="translate(441,483)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#93A3A8" transform="translate(366,483)"/>
<path d="" fill="#A9B19B" transform="translate(0,0)"/>
<path d="" fill="#859AA4" transform="translate(0,0)"/>
<path d="" fill="#A6AE98" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#849CA5" transform="translate(641,475)"/>
<path d="" fill="#839CA6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#809AA4" transform="translate(537,469)"/>
<path d="" fill="#74909F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AAB39B" transform="translate(57,467)"/>
<path d="" fill="#8B9DA4" transform="translate(0,0)"/>
<path d="" fill="#A6B09A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7291A1" transform="translate(692,462)"/>
<path d="" fill="#7D97A7" transform="translate(0,0)"/>
<path d="" fill="#A7AF98" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B39B" transform="translate(57,459)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7296A2" transform="translate(565,456)"/>
<path d="" fill="#7394A2" transform="translate(0,0)"/>
<path d="" fill="#6D8F9E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6A8D9F" transform="translate(676,439)"/>
<path d="" fill="#A9B39B" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B298" transform="translate(65,435)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A7B099" transform="translate(62,427)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6A8D9D" transform="translate(520,421)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#718FA1" transform="translate(520,419)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A6B098" transform="translate(132,414)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5AE9A" transform="translate(129,413)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6D8EA0" transform="translate(593,406)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#708EA0" transform="translate(376,405)"/>
<path d="" fill="#6C8C9F" transform="translate(0,0)"/>
<path d="" fill="#708E9F" transform="translate(0,0)"/>
<path d="" fill="#A3AC95" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6E91A3" transform="translate(349,399)"/>
<path d="" fill="#7393A3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7190A3" transform="translate(349,396)"/>
<path d="" fill="#7190A1" transform="translate(0,0)"/>
<path d="" fill="#708FA1" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7292A3" transform="translate(361,371)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8B19B" transform="translate(950,351)"/>
<path d="" fill="#A6B09A" transform="translate(0,0)"/>
<path d="" fill="#A6B19A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8B19B" transform="translate(998,327)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#72909E" transform="translate(627,327)"/>
<path d="" fill="#7694A2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7692A4" transform="translate(624,326)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8B19A" transform="translate(1006,323)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A7B099" transform="translate(990,323)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A4AD97" transform="translate(990,319)"/>
<path d="" fill="#7791A1" transform="translate(0,0)"/>
<path d="" fill="#6D8C9B" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6B8A9C" transform="translate(449,315)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6C8EA0" transform="translate(501,311)"/>
<path d="" fill="#73919C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6C90A1" transform="translate(774,304)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7493A4" transform="translate(377,304)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#7292A2" transform="translate(275,304)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#718FA0" transform="translate(267,304)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6A8B9C" transform="translate(289,299)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6B8D9D" transform="translate(798,297)"/>
<path d="" fill="#66889C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6B90A1" transform="translate(246,290)"/>
<path d="" fill="#638699" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#698A9D" transform="translate(656,286)"/>
<path d="" fill="#6B8C9D" transform="translate(0,0)"/>
<path d="" fill="#6B8C9D" transform="translate(0,0)"/>
<path d="" fill="#6C8793" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#67889B" transform="translate(409,267)"/>
<path d="" fill="#66899D" transform="translate(0,0)"/>
<path d="" fill="#6C8E9D" transform="translate(0,0)"/>
<path d="" fill="#7291A0" transform="translate(0,0)"/>
<path d="" fill="#698B9F" transform="translate(0,0)"/>
<path d="" fill="#698A9D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6C8FA1" transform="translate(391,246)"/>
<path d="" fill="#6B8C9E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#67899C" transform="translate(430,244)"/>
<path d="" fill="#6C8996" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#688B9A" transform="translate(493,242)"/>
<path d="" fill="#67899A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6A8B9E" transform="translate(646,226)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6D8A99" transform="translate(352,225)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#678899" transform="translate(645,224)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6E8D9C" transform="translate(302,224)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6B8A98" transform="translate(398,220)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#668696" transform="translate(540,219)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#688998" transform="translate(393,219)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#718E9B" transform="translate(773,218)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#6B8998" transform="translate(390,216)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9DAA96" transform="translate(745,130)"/>
<path d="" fill="#9EAA97" transform="translate(0,0)"/>
<path d="" fill="#A1AB97" transform="translate(0,0)"/>
<path d="" fill="#A3AE97" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A0AA94" transform="translate(702,83)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#96A08A" transform="translate(248,83)"/>
<path d="" fill="#96A08A" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5B098" transform="translate(806,59)"/>
<path d="" fill="#A5B098" transform="translate(0,0)"/>
<path d="" fill="#A6B199" transform="translate(0,0)"/>
<path d="" fill="#9CA790" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A7B199" transform="translate(729,40)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9DA791" transform="translate(254,40)"/>
<path d="" fill="#A9B39B" transform="translate(0,0)"/>
<path d="" fill="#A8B29B" transform="translate(0,0)"/>
<path d="" fill="#A9B39B" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ABB49D" transform="translate(772,28)"/>
<path d="" fill="#A8B399" transform="translate(0,0)"/>
<path d="" fill="#A9B398" transform="translate(0,0)"/>
<path d="" fill="#A9B49A" transform="translate(0,0)"/>
<path d="" fill="#A9B39B" transform="translate(0,0)"/>
<path d="" fill="#AAB398" transform="translate(0,0)"/>
<path d="" fill="#AAB299" transform="translate(0,0)"/>
<path d="" fill="#AAB398" transform="translate(0,0)"/>
<path d="" fill="#A7B399" transform="translate(0,0)"/>
<path d="" fill="#A9B59A" transform="translate(0,0)"/>
<path d="" fill="#A8B399" transform="translate(0,0)"/>
<path d="" fill="#AAB299" transform="translate(0,0)"/>
<path d="" fill="#A8B398" transform="translate(0,0)"/>
<path d="" fill="#A9B49A" transform="translate(0,0)"/>
<path d="" fill="#A9B49A" transform="translate(0,0)"/>
<path d="" fill="#A8B199" transform="translate(0,0)"/>
<path d="" fill="#A8B399" transform="translate(0,0)"/>
<path d="" fill="#A7B29A" transform="translate(0,0)"/>
<path d="" fill="#A9B298" transform="translate(0,0)"/>
<path d="" fill="#A7B29A" transform="translate(0,0)"/>
<path d="" fill="#A8B297" transform="translate(0,0)"/>
<path d="" fill="#AAB399" transform="translate(0,0)"/>
<path d="" fill="#A9B399" transform="translate(0,0)"/>
<path d="" fill="#A9B299" transform="translate(0,0)"/>
<path d="" fill="#A9B399" transform="translate(0,0)"/>
<path d="" fill="#A6B199" transform="translate(0,0)"/>
<path d="" fill="#A7B19A" transform="translate(0,0)"/>
<path d="" fill="#A9B399" transform="translate(0,0)"/>
<path d="" fill="#A9B299" transform="translate(0,0)"/>
<path d="" fill="#A8B297" transform="translate(0,0)"/>
<path d="" fill="#A8B399" transform="translate(0,0)"/>
<path d="" fill="#A9B199" transform="translate(0,0)"/>
<path d="" fill="#A9B39A" transform="translate(0,0)"/>
<path d="" fill="#A9B097" transform="translate(0,0)"/>
<path d="" fill="#A9B397" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="" fill="#A8B197" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="" fill="#A7B297" transform="translate(0,0)"/>
<path d="" fill="#A9B299" transform="translate(0,0)"/>
<path d="" fill="#A8B299" transform="translate(0,0)"/>
<path d="" fill="#A9B299" transform="translate(0,0)"/>
<path d="" fill="#A9B399" transform="translate(0,0)"/>
<path d="" fill="#A6B097" transform="translate(0,0)"/>
<path d="" fill="#A7B198" transform="translate(0,0)"/>
<path d="" fill="#A7B096" transform="translate(0,0)"/>
<path d="" fill="#A5B196" transform="translate(0,0)"/>
<path d="" fill="#A6B298" transform="translate(0,0)"/>
<path d="" fill="#A6B195" transform="translate(0,0)"/>
<path d="" fill="#A7B299" transform="translate(0,0)"/>
<path d="" fill="#A5AF97" transform="translate(0,0)"/>
<path d="" fill="#A8B198" transform="translate(0,0)"/>
<path d="" fill="#A4AF95" transform="translate(0,0)"/>
<path d="" fill="#A6B298" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="" fill="#A5AF94" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="" fill="#A6B095" transform="translate(0,0)"/>
<path d="" fill="#A5AF96" transform="translate(0,0)"/>
<path d="" fill="#A5B095" transform="translate(0,0)"/>
<path d="" fill="#A6AF95" transform="translate(0,0)"/>
<path d="" fill="#A7B096" transform="translate(0,0)"/>
<path d="" fill="#A7AF96" transform="translate(0,0)"/>
<path d="" fill="#A4AD94" transform="translate(0,0)"/>
<path d="" fill="#A4AE95" transform="translate(0,0)"/>
<path d="" fill="#A5AE95" transform="translate(0,0)"/>
<path d="" fill="#A6B096" transform="translate(0,0)"/>
<path d="" fill="#A4AD94" transform="translate(0,0)"/>
<path d="" fill="#A6B195" transform="translate(0,0)"/>
<path d="" fill="#A7B095" transform="translate(0,0)"/>
<path d="" fill="#A6AF96" transform="translate(0,0)"/>
<path d="" fill="#A6B197" transform="translate(0,0)"/>
<path d="" fill="#A5AE94" transform="translate(0,0)"/>
<path d="" fill="#A4AD92" transform="translate(0,0)"/>
<path d="" fill="#A5AF95" transform="translate(0,0)"/>
<path d="" fill="#A2AD92" transform="translate(0,0)"/>
<path d="" fill="#A5B096" transform="translate(0,0)"/>
<path d="" fill="#A6B095" transform="translate(0,0)"/>
<path d="" fill="#A6B096" transform="translate(0,0)"/>
<path d="" fill="#A5AF97" transform="translate(0,0)"/>
<path d="" fill="#A5AF93" transform="translate(0,0)"/>
<path d="" fill="#A1AA93" transform="translate(0,0)"/>
<path d="" fill="#A6B096" transform="translate(0,0)"/>
<path d="" fill="#A3AD96" transform="translate(0,0)"/>
<path d="" fill="#A5AF96" transform="translate(0,0)"/>
<path d="" fill="#A5AE94" transform="translate(0,0)"/>
<path d="" fill="#A3AC91" transform="translate(0,0)"/>
<path d="" fill="#A6AF94" transform="translate(0,0)"/>
<path d="" fill="#A4AF95" transform="translate(0,0)"/>
<path d="" fill="#A4AE95" transform="translate(0,0)"/>
<path d="" fill="#A5B095" transform="translate(0,0)"/>
<path d="" fill="#A3AE94" transform="translate(0,0)"/>
<path d="" fill="#A3AF94" transform="translate(0,0)"/>
<path d="" fill="#A2AC94" transform="translate(0,0)"/>
<path d="" fill="#A4AF96" transform="translate(0,0)"/>
<path d="" fill="#A2AE92" transform="translate(0,0)"/>
<path d="" fill="#79856D" transform="translate(0,0)"/>
<path d="" fill="#A7AF96" transform="translate(0,0)"/>
<path d="" fill="#A2AC93" transform="translate(0,0)"/>
<path d="" fill="#A5AE94" transform="translate(0,0)"/>
<path d="" fill="#A6B094" transform="translate(0,0)"/>
<path d="" fill="#A2AD92" transform="translate(0,0)"/>
<path d="" fill="#A2AC92" transform="translate(0,0)"/>
<path d="" fill="#A5AF96" transform="translate(0,0)"/>
<path d="" fill="#A5AF95" transform="translate(0,0)"/>
<path d="" fill="#A7B196" transform="translate(0,0)"/>
<path d="" fill="#A4AE94" transform="translate(0,0)"/>
<path d="" fill="#A3AC94" transform="translate(0,0)"/>
<path d="" fill="#A2AC93" transform="translate(0,0)"/>
<path d="" fill="#A4B095" transform="translate(0,0)"/>
<path d="" fill="#A3AD93" transform="translate(0,0)"/>
<path d="" fill="#A4AE96" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="" fill="#7C8871" transform="translate(0,0)"/>
<path d="" fill="#7E8873" transform="translate(0,0)"/>
<path d="" fill="#A5AF96" transform="translate(0,0)"/>
<path d="" fill="#A5AF95" transform="translate(0,0)"/>
<path d="" fill="#A2AC93" transform="translate(0,0)"/>
<path d="" fill="#7E8973" transform="translate(0,0)"/>
<path d="" fill="#7D8974" transform="translate(0,0)"/>
<path d="" fill="#A5AF94" transform="translate(0,0)"/>
<path d="" fill="#7C8972" transform="translate(0,0)"/>
<path d="" fill="#7D8772" transform="translate(0,0)"/>
<path d="" fill="#A6B094" transform="translate(0,0)"/>
<path d="" fill="#818A73" transform="translate(0,0)"/>
<path d="" fill="#808C76" transform="translate(0,0)"/>
<path d="" fill="#808B74" transform="translate(0,0)"/>
<path d="" fill="#A5AF94" transform="translate(0,0)"/>
<path d="" fill="#A6B197" transform="translate(0,0)"/>
<path d="" fill="#7F8A74" transform="translate(0,0)"/>
<path d="" fill="#7F8975" transform="translate(0,0)"/>
<path d="" fill="#7F8A74" transform="translate(0,0)"/>
<path d="" fill="#7E8C74" transform="translate(0,0)"/>
<path d="" fill="#7F8B75" transform="translate(0,0)"/>
<path d="" fill="#808B74" transform="translate(0,0)"/>
<path d="" fill="#A6B095" transform="translate(0,0)"/>
<path d="" fill="#A3AA93" transform="translate(0,0)"/>
<path d="" fill="#A8B299" transform="translate(0,0)"/>
<path d="" fill="#828E74" transform="translate(0,0)"/>
<path d="" fill="#838C76" transform="translate(0,0)"/>
<path d="" fill="#828C75" transform="translate(0,0)"/>
<path d="" fill="#A5B096" transform="translate(0,0)"/>
<path d="" fill="#818E78" transform="translate(0,0)"/>
<path d="" fill="#838E76" transform="translate(0,0)"/>
<path d="" fill="#A2B093" transform="translate(0,0)"/>
<path d="" fill="#A6AF96" transform="translate(0,0)"/>
<path d="" fill="#A6B095" transform="translate(0,0)"/>
<path d="" fill="#A9B095" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="" fill="#A5AE95" transform="translate(0,0)"/>
<path d="" fill="#A4AE96" transform="translate(0,0)"/>
<path d="" fill="#AAB499" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="" fill="#A6AE95" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="" fill="#A6B097" transform="translate(0,0)"/>
<path d="" fill="#859179" transform="translate(0,0)"/>
<path d="" fill="#858F79" transform="translate(0,0)"/>
<path d="" fill="#A1AA90" transform="translate(0,0)"/>
<path d="" fill="#A5B093" transform="translate(0,0)"/>
<path d="" fill="#A8AF97" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="" fill="#838F78" transform="translate(0,0)"/>
<path d="" fill="#A2AA91" transform="translate(0,0)"/>
<path d="" fill="#A1AB91" transform="translate(0,0)"/>
<path d="" fill="#A6B097" transform="translate(0,0)"/>
<path d="" fill="#A7B299" transform="translate(0,0)"/>
<path d="" fill="#A6B197" transform="translate(0,0)"/>
<path d="" fill="#869079" transform="translate(0,0)"/>
<path d="" fill="#A1AC90" transform="translate(0,0)"/>
<path d="" fill="#A0AA90" transform="translate(0,0)"/>
<path d="" fill="#A7B298" transform="translate(0,0)"/>
<path d="" fill="#868E78" transform="translate(0,0)"/>
<path d="" fill="#A1AB91" transform="translate(0,0)"/>
<path d="" fill="#A4AD93" transform="translate(0,0)"/>
<path d="" fill="#A8B299" transform="translate(0,0)"/>
<path d="" fill="#A9B099" transform="translate(0,0)"/>
<path d="" fill="#A2AD91" transform="translate(0,0)"/>
<path d="" fill="#A1AB91" transform="translate(0,0)"/>
<path d="" fill="#A7B298" transform="translate(0,0)"/>
<path d="" fill="#87927C" transform="translate(0,0)"/>
<path d="" fill="#A2AA92" transform="translate(0,0)"/>
<path d="" fill="#A5AF96" transform="translate(0,0)"/>
<path d="" fill="#A6B197" transform="translate(0,0)"/>
<path d="" fill="#A8B298" transform="translate(0,0)"/>
<path d="" fill="#A0A990" transform="translate(0,0)"/>
<path d="" fill="#A4AF96" transform="translate(0,0)"/>
<path d="" fill="#A8B098" transform="translate(0,0)"/>
<path d="" fill="#88927C" transform="translate(0,0)"/>
<path d="" fill="#A1AA92" transform="translate(0,0)"/>
<path d="" fill="#A4AE93" transform="translate(0,0)"/>
<path d="" fill="#A7B097" transform="translate(0,0)"/>
<path d="" fill="#A7B197" transform="translate(0,0)"/>
<path d="" fill="#88927B" transform="translate(0,0)"/>
<path d="" fill="#A4AD94" transform="translate(0,0)"/>
<path d="" fill="#AAB39A" transform="translate(0,0)"/>
<path d="" fill="#A7B298" transform="translate(0,0)"/>
<path d="" fill="#ABB398" transform="translate(0,0)"/>
<path d="" fill="#A7B199" transform="translate(0,0)"/>
<path d="" fill="#A8B299" transform="translate(0,0)"/>
<path d="" fill="#87907B" transform="translate(0,0)"/>
<path d="" fill="#A2AC94" transform="translate(0,0)"/>
<path d="" fill="#A2AE92" transform="translate(0,0)"/>
<path d="" fill="#A3AC93" transform="translate(0,0)"/>
<path d="" fill="#A4AE94" transform="translate(0,0)"/>
<path d="" fill="#A7B096" transform="translate(0,0)"/>
<path d="" fill="#A4AE95" transform="translate(0,0)"/>
<path d="" fill="#A5AD93" transform="translate(0,0)"/>
<path d="" fill="#A0AA8F" transform="translate(0,0)"/>
<path d="" fill="#A5B194" transform="translate(0,0)"/>
<path d="" fill="#A9B398" transform="translate(0,0)"/>
<path d="" fill="#89917B" transform="translate(0,0)"/>
<path d="" fill="#89927C" transform="translate(0,0)"/>
<path d="" fill="#A4B094" transform="translate(0,0)"/>
<path d="" fill="#A9B398" transform="translate(0,0)"/>
<path d="" fill="#87927C" transform="translate(0,0)"/>
<path d="" fill="#87937B" transform="translate(0,0)"/>
<path d="" fill="#A0A98F" transform="translate(0,0)"/>
<path d="" fill="#A4AD94" transform="translate(0,0)"/>
<path d="" fill="#A2AD94" transform="translate(0,0)"/>
<path d="" fill="#A6AF97" transform="translate(0,0)"/>
<path d="" fill="#A5AD93" transform="translate(0,0)"/>
<path d="" fill="#A6AE96" transform="translate(0,0)"/>
<path d="" fill="#A4AD94" transform="translate(0,0)"/>
<path d="" fill="#A9B298" transform="translate(0,0)"/>
<path d="" fill="#A6AC94" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="" fill="#A6B096" transform="translate(0,0)"/>
<path d="" fill="#A9B399" transform="translate(0,0)"/>
<path d="" fill="#A9B398" transform="translate(0,0)"/>
<path d="" fill="#A3AD95" transform="translate(0,0)"/>
<path d="" fill="#A5AE94" transform="translate(0,0)"/>
<path d="" fill="#A5AF96" transform="translate(0,0)"/>
<path d="" fill="#A8B29A" transform="translate(0,0)"/>
<path d="" fill="#A5AD94" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="" fill="#ABB399" transform="translate(0,0)"/>
<path d="" fill="#A3AE94" transform="translate(0,0)"/>
<path d="" fill="#A5AF96" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="" fill="#9FAA91" transform="translate(0,0)"/>
<path d="" fill="#ACB499" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="" fill="#A5B095" transform="translate(0,0)"/>
<path d="" fill="#A7AF97" transform="translate(0,0)"/>
<path d="" fill="#AAB39A" transform="translate(0,0)"/>
<path d="" fill="#A2AA94" transform="translate(0,0)"/>
<path d="" fill="#A5AC93" transform="translate(0,0)"/>
<path d="" fill="#A1A890" transform="translate(0,0)"/>
<path d="" fill="#ABB29B" transform="translate(0,0)"/>
<path d="" fill="#A3AE94" transform="translate(0,0)"/>
<path d="" fill="#AAB39A" transform="translate(0,0)"/>
<path d="" fill="#ABB49B" transform="translate(0,0)"/>
<path d="" fill="#A5AE95" transform="translate(0,0)"/>
<path d="" fill="#A0AA90" transform="translate(0,0)"/>
<path d="" fill="#ACB49C" transform="translate(0,0)"/>
<path d="" fill="#9FAA90" transform="translate(0,0)"/>
<path d="" fill="#A3AD93" transform="translate(0,0)"/>
<path d="" fill="#A4AD95" transform="translate(0,0)"/>
<path d="" fill="#ACB49B" transform="translate(0,0)"/>
<path d="" fill="#A6AE95" transform="translate(0,0)"/>
<path d="" fill="#A1AA91" transform="translate(0,0)"/>
<path d="" fill="#A4AE96" transform="translate(0,0)"/>
<path d="" fill="#ABB49A" transform="translate(0,0)"/>
<path d="" fill="#9FAA8E" transform="translate(0,0)"/>
<path d="" fill="#A1AB90" transform="translate(0,0)"/>
<path d="" fill="#A4B095" transform="translate(0,0)"/>
<path d="" fill="#A3AE92" transform="translate(0,0)"/>
<path d="" fill="#AAB49A" transform="translate(0,0)"/>
<path d="" fill="#A0A990" transform="translate(0,0)"/>
<path d="" fill="#ADB69B" transform="translate(0,0)"/>
<path d="" fill="#A4AD95" transform="translate(0,0)"/>
<path d="" fill="#A1A992" transform="translate(0,0)"/>
<path d="" fill="#9FA88E" transform="translate(0,0)"/>
<path d="" fill="#9DA78B" transform="translate(0,0)"/>
<path d="" fill="#ADB69E" transform="translate(0,0)"/>
<path d="" fill="#ADB69E" transform="translate(0,0)"/>
<path d="" fill="#A6B096" transform="translate(0,0)"/>
<path d="" fill="#A1AC93" transform="translate(0,0)"/>
<path d="" fill="#A5AE96" transform="translate(0,0)"/>
<path d="" fill="#AEB59D" transform="translate(0,0)"/>
<path d="" fill="#A5AE96" transform="translate(0,0)"/>
<path d="" fill="#ACB59C" transform="translate(0,0)"/>
<path d="" fill="#A6B095" transform="translate(0,0)"/>
<path d="" fill="#A1AB91" transform="translate(0,0)"/>
<path d="" fill="#A1AC93" transform="translate(0,0)"/>
<path d="" fill="#AEB69F" transform="translate(0,0)"/>
<path d="" fill="#AEB69E" transform="translate(0,0)"/>
<path d="" fill="#AFB79F" transform="translate(0,0)"/>
<path d="" fill="#A1AC94" transform="translate(0,0)"/>
<path d="" fill="#A5B096" transform="translate(0,0)"/>
<path d="" fill="#A2AC91" transform="translate(0,0)"/>
<path d="" fill="#A1AA91" transform="translate(0,0)"/>
<path d="" fill="#AEB79E" transform="translate(0,0)"/>
<path d="" fill="#A3AB91" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="" fill="#A1AB92" transform="translate(0,0)"/>
<path d="" fill="#A0AB91" transform="translate(0,0)"/>
<path d="" fill="#AEB69F" transform="translate(0,0)"/>
<path d="" fill="#AFB79F" transform="translate(0,0)"/>
<path d="" fill="#ADB59E" transform="translate(0,0)"/>
<path d="" fill="#A1AA92" transform="translate(0,0)"/>
<path d="" fill="#A3AF96" transform="translate(0,0)"/>
<path d="" fill="#A8B397" transform="translate(0,0)"/>
<path d="" fill="#AFB89E" transform="translate(0,0)"/>
<path d="" fill="#AEB79E" transform="translate(0,0)"/>
<path d="" fill="#AEB79E" transform="translate(0,0)"/>
<path d="" fill="#9FAA8F" transform="translate(0,0)"/>
<path d="" fill="#A2AB93" transform="translate(0,0)"/>
<path d="" fill="#A4AF95" transform="translate(0,0)"/>
<path d="" fill="#AFB89F" transform="translate(0,0)"/>
<path d="" fill="#A1AC92" transform="translate(0,0)"/>
<path d="" fill="#A7AF98" transform="translate(0,0)"/>
<path d="" fill="#A3AE95" transform="translate(0,0)"/>
<path d="" fill="#A3AC92" transform="translate(0,0)"/>
<path d="" fill="#A8AF98" transform="translate(0,0)"/>
<path d="" fill="#AEB89F" transform="translate(0,0)"/>
<path d="" fill="#AEB79E" transform="translate(0,0)"/>
<path d="" fill="#A8B197" transform="translate(0,0)"/>
<path d="" fill="#A3AC92" transform="translate(0,0)"/>
<path d="" fill="#ADB99F" transform="translate(0,0)"/>
<path d="" fill="#A8B097" transform="translate(0,0)"/>
<path d="" fill="#A3AC91" transform="translate(0,0)"/>
<path d="" fill="#A5B096" transform="translate(0,0)"/>
<path d="" fill="#AFB79E" transform="translate(0,0)"/>
<path d="" fill="#ADB69E" transform="translate(0,0)"/>
<path d="" fill="#AFB89D" transform="translate(0,0)"/>
<path d="" fill="#ABB49C" transform="translate(0,0)"/>
<path d="" fill="#AEB79F" transform="translate(0,0)"/>
<path d="" fill="#ADB59D" transform="translate(0,0)"/>
<path d="" fill="#ACB59B" transform="translate(0,0)"/>
<path d="" fill="#AAB59A" transform="translate(0,0)"/>
<path d="" fill="#ADB79E" transform="translate(0,0)"/>
<path d="" fill="#AEB59E" transform="translate(0,0)"/>
<path d="" fill="#A9B59B" transform="translate(0,0)"/>
<path d="" fill="#ACB69E" transform="translate(0,0)"/>
<path d="" fill="#AEB69E" transform="translate(0,0)"/>
<path d="" fill="#AEB79F" transform="translate(0,0)"/>
<path d="" fill="#A3AD94" transform="translate(0,0)"/>
<path d="" fill="#AAB59B" transform="translate(0,0)"/>
<path d="" fill="#ADB59E" transform="translate(0,0)"/>
<path d="" fill="#ADB69E" transform="translate(0,0)"/>
<path d="" fill="#ADB59D" transform="translate(0,0)"/>
<path d="" fill="#A2AC92" transform="translate(0,0)"/>
<path d="" fill="#A5AE95" transform="translate(0,0)"/>
<path d="" fill="#A8B199" transform="translate(0,0)"/>
<path d="" fill="#A9B29A" transform="translate(0,0)"/>
<path d="" fill="#ABB59B" transform="translate(0,0)"/>
<path d="" fill="#ACB59E" transform="translate(0,0)"/>
<path d="" fill="#ABB69E" transform="translate(0,0)"/>
<path d="" fill="#A6B298" transform="translate(0,0)"/>
<path d="" fill="#ACB39B" transform="translate(0,0)"/>
<path d="" fill="#ADB59F" transform="translate(0,0)"/>
<path d="" fill="#ABB79D" transform="translate(0,0)"/>
<path d="" fill="#ACB59E" transform="translate(0,0)"/>
<path d="" fill="#A8B198" transform="translate(0,0)"/>
<path d="" fill="#9FA890" transform="translate(0,0)"/>
<path d="" fill="#A9B19A" transform="translate(0,0)"/>
<path d="" fill="#ABB39A" transform="translate(0,0)"/>
<path d="" fill="#ACB59D" transform="translate(0,0)"/>
<path d="" fill="#A4AC94" transform="translate(0,0)"/>
<path d="" fill="#A8B39B" transform="translate(0,0)"/>
<path d="" fill="#A9B49A" transform="translate(0,0)"/>
<path d="" fill="#A3AE93" transform="translate(0,0)"/>
<path d="" fill="#A5AD93" transform="translate(0,0)"/>
<path d="" fill="#A3AB93" transform="translate(0,0)"/>
<path d="" fill="#ABB39C" transform="translate(0,0)"/>
<path d="" fill="#AAB49B" transform="translate(0,0)"/>
<path d="" fill="#AAB29A" transform="translate(0,0)"/>
<path d="" fill="#A5AE94" transform="translate(0,0)"/>
<path d="" fill="#A4AF94" transform="translate(0,0)"/>
<path d="" fill="#ACB49C" transform="translate(0,0)"/>
<path d="" fill="#A2AC93" transform="translate(0,0)"/>
<path d="" fill="#ACB59C" transform="translate(0,0)"/>
<path d="" fill="#ABB49C" transform="translate(0,0)"/>
<path d="" fill="#A3AC93" transform="translate(0,0)"/>
<path d="" fill="#ACB69D" transform="translate(0,0)"/>
<path d="" fill="#ADB59D" transform="translate(0,0)"/>
<path d="" fill="#ABB89E" transform="translate(0,0)"/>
<path d="" fill="#A4AE96" transform="translate(0,0)"/>
<path d="" fill="#ADB69F" transform="translate(0,0)"/>
<path d="" fill="#B0B79F" transform="translate(0,0)"/>
<path d="" fill="#ADB79F" transform="translate(0,0)"/>
<path d="" fill="#B0B69F" transform="translate(0,0)"/>
<path d="" fill="#A6B198" transform="translate(0,0)"/>
<path d="" fill="#AEB7A0" transform="translate(0,0)"/>
<path d="" fill="#ADB79E" transform="translate(0,0)"/>
<path d="" fill="#A8B197" transform="translate(0,0)"/>
<path d="" fill="#ADB89E" transform="translate(0,0)"/>
<path d="" fill="#AEB69F" transform="translate(0,0)"/>
<path d="" fill="#ABB49B" transform="translate(0,0)"/>
<path d="" fill="#A9B299" transform="translate(0,0)"/>
<path d="" fill="#AFB7A0" transform="translate(0,0)"/>
<path d="" fill="#ABB49B" transform="translate(0,0)"/>
<path d="" fill="#ADB69F" transform="translate(0,0)"/>
<path d="" fill="#AEB79E" transform="translate(0,0)"/>
<path d="" fill="#AAB59D" transform="translate(0,0)"/>
<path d="" fill="#AAB49B" transform="translate(0,0)"/>
<path d="" fill="#ABB39B" transform="translate(0,0)"/>
<path d="" fill="#A9B39A" transform="translate(0,0)"/>
<path d="" fill="#B4BDAA" transform="translate(0,0)"/>
<path d="" fill="#ACB69E" transform="translate(0,0)"/>
<path d="" fill="#ACB59D" transform="translate(0,0)"/>
<path d="" fill="#ABB39D" transform="translate(0,0)"/>
<path d="" fill="#AAB49C" transform="translate(0,0)"/>
<path d="" fill="#AAB59C" transform="translate(0,0)"/>
<path d="" fill="#BAC2AF" transform="translate(0,0)"/>
<path d="" fill="#D3D9D2" transform="translate(0,0)"/>
<path d="" fill="#D3D9D0" transform="translate(0,0)"/>
<path d="" fill="#C9D2C9" transform="translate(0,0)"/>
<path d="" fill="#D6DAD4" transform="translate(0,0)"/>
<path d="" fill="#D7DAD5" transform="translate(0,0)"/>
<path d="" fill="#D6DAD3" transform="translate(0,0)"/>
<path d="" fill="#D0D7D1" transform="translate(0,0)"/>
<path d="" fill="#D4D9D0" transform="translate(0,0)"/>
<path d="" fill="#D3D7CF" transform="translate(0,0)"/>
<path d="" fill="#D0D4CE" transform="translate(0,0)"/>
<path d="" fill="#D8DDD8" transform="translate(0,0)"/>
<path d="" fill="#D6DDD6" transform="translate(0,0)"/>
<path d="" fill="#DADED5" transform="translate(0,0)"/>
<path d="" fill="#D8DBD6" transform="translate(0,0)"/>
<path d="" fill="#D3D8D2" transform="translate(0,0)"/>
<path d="" fill="#D1D8D3" transform="translate(0,0)"/>
<path d="" fill="#D3D9D1" transform="translate(0,0)"/>
<path d="" fill="#D3D8D2" transform="translate(0,0)"/>
<path d="" fill="#D7DBD4" transform="translate(0,0)"/>
<path d="" fill="#D7DAD5" transform="translate(0,0)"/>
<path d="" fill="#DADDD7" transform="translate(0,0)"/>
<path d="" fill="#DADCD5" transform="translate(0,0)"/>
<path d="" fill="#DADDD7" transform="translate(0,0)"/>
<path d="" fill="#D8DBD6" transform="translate(0,0)"/>
<path d="" fill="#D8DBD5" transform="translate(0,0)"/>
<path d="" fill="#D7D9D4" transform="translate(0,0)"/>
<path d="" fill="#D9DDD6" transform="translate(0,0)"/>
<path d="" fill="#DADCD6" transform="translate(0,0)"/>
<path d="" fill="#D9DAD4" transform="translate(0,0)"/>
<path d="" fill="#DBDED9" transform="translate(0,0)"/>
<path d="" fill="#D3D8D2" transform="translate(0,0)"/>
<path d="" fill="#DADCD9" transform="translate(0,0)"/>
<path d="" fill="#D2D7D1" transform="translate(0,0)"/>
<path d="" fill="#D6D9D5" transform="translate(0,0)"/>
<path d="" fill="#D2D8D3" transform="translate(0,0)"/>
<path d="" fill="#DBDED9" transform="translate(0,0)"/>
<path d="" fill="#D8DDD7" transform="translate(0,0)"/>
<path d="" fill="#D7DBD5" transform="translate(0,0)"/>
<path d="" fill="#DBE0DB" transform="translate(0,0)"/>
<path d="" fill="#D9DED8" transform="translate(0,0)"/>
<path d="" fill="#DCDFDD" transform="translate(0,0)"/>
<path d="" fill="#DDE0D8" transform="translate(0,0)"/>
<path d="" fill="#D8D9D7" transform="translate(0,0)"/>
<path d="" fill="#D8DCD9" transform="translate(0,0)"/>
<path d="" fill="#D8DDD9" transform="translate(0,0)"/>
<path d="" fill="#D9DDD9" transform="translate(0,0)"/>
<path d="" fill="#DBE0DC" transform="translate(0,0)"/>
<path d="" fill="#DCE0D9" transform="translate(0,0)"/>
<path d="" fill="#D6DBD7" transform="translate(0,0)"/>
<path d="" fill="#D1D7D0" transform="translate(0,0)"/>
<path d="" fill="#DADCDD" transform="translate(0,0)"/>
<path d="" fill="#DEE2DF" transform="translate(0,0)"/>
<path d="" fill="#D9DEDB" transform="translate(0,0)"/>
<path d="" fill="#DCDEDD" transform="translate(0,0)"/>
<path d="" fill="#D3D7D4" transform="translate(0,0)"/>
<path d="" fill="#D8DCDA" transform="translate(0,0)"/>
<path d="" fill="#DEE0DE" transform="translate(0,0)"/>
<path d="" fill="#DBDFD9" transform="translate(0,0)"/>
<path d="" fill="#D7DBD7" transform="translate(0,0)"/>
<path d="" fill="#D6DBD9" transform="translate(0,0)"/>
<path d="" fill="#D4D8D2" transform="translate(0,0)"/>
<path d="" fill="#D7DED9" transform="translate(0,0)"/>
<path d="" fill="#DCDFDC" transform="translate(0,0)"/>
<path d="" fill="#DADEDB" transform="translate(0,0)"/>
<path d="" fill="#DCDFDB" transform="translate(0,0)"/>
<path d="" fill="#D5DEDA" transform="translate(0,0)"/>
<path d="" fill="#D9DEDC" transform="translate(0,0)"/>
<path d="" fill="#DADEDA" transform="translate(0,0)"/>
<path d="" fill="#DBDFDB" transform="translate(0,0)"/>
<path d="" fill="#D9DFDA" transform="translate(0,0)"/>
<path d="" fill="#CFD7D5" transform="translate(0,0)"/>
<path d="" fill="#DADED9" transform="translate(0,0)"/>
<path d="" fill="#DCDEDD" transform="translate(0,0)"/>
<path d="" fill="#D9DED8" transform="translate(0,0)"/>
<path d="" fill="#D9DDD9" transform="translate(0,0)"/>
<path d="" fill="#D9DFDA" transform="translate(0,0)"/>
<path d="" fill="#D6DBD9" transform="translate(0,0)"/>
<path d="" fill="#D9DEDA" transform="translate(0,0)"/>
<path d="" fill="#DADFD9" transform="translate(0,0)"/>
<path d="" fill="#DEE1DD" transform="translate(0,0)"/>
<path d="" fill="#DADEDB" transform="translate(0,0)"/>
<path d="" fill="#DCE0DA" transform="translate(0,0)"/>
<path d="" fill="#DADED8" transform="translate(0,0)"/>
<path d="" fill="#D9DDD6" transform="translate(0,0)"/>
<path d="" fill="#CFD5CC" transform="translate(0,0)"/>
<path d="" fill="#D3D9D1" transform="translate(0,0)"/>
<path d="" fill="#DADFD9" transform="translate(0,0)"/>
<path d="" fill="#DDE1DC" transform="translate(0,0)"/>
<path d="" fill="#DBDFDA" transform="translate(0,0)"/>
<path d="" fill="#DADDD9" transform="translate(0,0)"/>
<path d="" fill="#DADFD8" transform="translate(0,0)"/>
<path d="" fill="#DBDED7" transform="translate(0,0)"/>
<path d="" fill="#DBDED9" transform="translate(0,0)"/>
<path d="" fill="#D9DED9" transform="translate(0,0)"/>
<path d="" fill="#DADED9" transform="translate(0,0)"/>
<path d="" fill="#D9DDD8" transform="translate(0,0)"/>
<path d="" fill="#D5DAD3" transform="translate(0,0)"/>
<path d="" fill="#D5D9D2" transform="translate(0,0)"/>
<path d="" fill="#D1D8CD" transform="translate(0,0)"/>
<path d="" fill="#DCDED9" transform="translate(0,0)"/>
<path d="" fill="#DDE0DB" transform="translate(0,0)"/>
<path d="" fill="#D6D9D2" transform="translate(0,0)"/>
<path d="" fill="#D3D8D0" transform="translate(0,0)"/>
<path d="" fill="#E5E5DC" transform="translate(0,0)"/>
<path d="" fill="#DCDFD9" transform="translate(0,0)"/>
<path d="" fill="#DAE0D9" transform="translate(0,0)"/>
<path d="" fill="#DEE0DA" transform="translate(0,0)"/>
<path d="" fill="#E1E2DC" transform="translate(0,0)"/>
<path d="" fill="#DADED9" transform="translate(0,0)"/>
<path d="" fill="#D9DEDA" transform="translate(0,0)"/>
<path d="" fill="#D8DCD5" transform="translate(0,0)"/>
<path d="" fill="#DADED8" transform="translate(0,0)"/>
<path d="" fill="#DBE0DA" transform="translate(0,0)"/>
<path d="" fill="#DBDFDB" transform="translate(0,0)"/>
<path d="" fill="#DEE0DC" transform="translate(0,0)"/>
<path d="" fill="#DBE0D8" transform="translate(0,0)"/>
<path d="" fill="#DDE0DA" transform="translate(0,0)"/>
<path d="" fill="#D9DDD7" transform="translate(0,0)"/>
<path d="" fill="#D7DBD6" transform="translate(0,0)"/>
<path d="" fill="#D4D7D2" transform="translate(0,0)"/>
<path d="" fill="#DCDEDA" transform="translate(0,0)"/>
<path d="" fill="#DCE1DB" transform="translate(0,0)"/>
<path d="" fill="#D9DEDA" transform="translate(0,0)"/>
<path d="" fill="#DFE4DF" transform="translate(0,0)"/>
<path d="" fill="#DCE1DF" transform="translate(0,0)"/>
<path d="" fill="#CDD0CA" transform="translate(0,0)"/>
<path d="" fill="#D6DCD8" transform="translate(0,0)"/>
<path d="" fill="#DADFD9" transform="translate(0,0)"/>
<path d="" fill="#DEE1DB" transform="translate(0,0)"/>
<path d="" fill="#D9DFDC" transform="translate(0,0)"/>
<path d="" fill="#D9DDDA" transform="translate(0,0)"/>
<path d="" fill="#DBDFDA" transform="translate(0,0)"/>
<path d="" fill="#D9DEDB" transform="translate(0,0)"/>
<path d="" fill="#DBDEDA" transform="translate(0,0)"/>
<path d="" fill="#DBE0DB" transform="translate(0,0)"/>
<path d="" fill="#D7DCD8" transform="translate(0,0)"/>
<path d="" fill="#DADFDA" transform="translate(0,0)"/>
<path d="" fill="#D9DFD9" transform="translate(0,0)"/>
<path d="" fill="#DADDD8" transform="translate(0,0)"/>
<path d="" fill="#DCDFDA" transform="translate(0,0)"/>
<path d="" fill="#CBD4CD" transform="translate(0,0)"/>
<path d="" fill="#DCDFD9" transform="translate(0,0)"/>
<path d="" fill="#A1AA94" transform="translate(0,0)"/>
<path d="" fill="#D4DAD3" transform="translate(0,0)"/>
<path d="" fill="#DCDFDB" transform="translate(0,0)"/>
<path d="" fill="#DCE0DA" transform="translate(0,0)"/>
<path d="" fill="#DEDFDB" transform="translate(0,0)"/>
<path d="" fill="#DCDEDB" transform="translate(0,0)"/>
<path d="" fill="#DEE2DE" transform="translate(0,0)"/>
<path d="" fill="#DADDDA" transform="translate(0,0)"/>
<path d="" fill="#D8DCD8" transform="translate(0,0)"/>
<path d="" fill="#DBE1DE" transform="translate(0,0)"/>
<path d="" fill="#DADFD9" transform="translate(0,0)"/>
<path d="" fill="#DDE2DD" transform="translate(0,0)"/>
<path d="" fill="#D9E0DA" transform="translate(0,0)"/>
<path d="" fill="#DADCD8" transform="translate(0,0)"/>
<path d="" fill="#DBDDD9" transform="translate(0,0)"/>
<path d="" fill="#DBDED7" transform="translate(0,0)"/>
<path d="" fill="#DADFD7" transform="translate(0,0)"/>
<path d="" fill="#D0D5CE" transform="translate(0,0)"/>
<path d="" fill="#DDDFDB" transform="translate(0,0)"/>
<path d="" fill="#D4D7D6" transform="translate(0,0)"/>
<path d="" fill="#CFD6CF" transform="translate(0,0)"/>
<path d="" fill="#A7B19C" transform="translate(0,0)"/>
<path d="" fill="#B4BCA9" transform="translate(0,0)"/>
<path d="" fill="#D9DED7" transform="translate(0,0)"/>
<path d="" fill="#B2B9A7" transform="translate(0,0)"/>
<path d="" fill="#D9DCD7" transform="translate(0,0)"/>
<path d="" fill="#A7B09C" transform="translate(0,0)"/>
<path d="" fill="#A0AA93" transform="translate(0,0)"/>
<path d="" fill="#DADCD5" transform="translate(0,0)"/>
<path d="" fill="#A4AE99" transform="translate(0,0)"/>
<path d="" fill="#A5AC97" transform="translate(0,0)"/>
<path d="" fill="#A8B19C" transform="translate(0,0)"/>
<path d="" fill="#A4AD98" transform="translate(0,0)"/>
<path d="" fill="#A8B19E" transform="translate(0,0)"/>
<path d="" fill="#A7AF9B" transform="translate(0,0)"/>
<path d="" fill="#A8B09B" transform="translate(0,0)"/>
<path d="" fill="#AAB39D" transform="translate(0,0)"/>
<path d="" fill="#A0A892" transform="translate(0,0)"/>
<path d="" fill="#A4AF9A" transform="translate(0,0)"/>
<path d="" fill="#B1B9AA" transform="translate(0,0)"/>
<path d="" fill="#AAB09D" transform="translate(0,0)"/>
<path d="" fill="#A9B39F" transform="translate(0,0)"/>
<path d="" fill="#A8B19A" transform="translate(0,0)"/>
<path d="" fill="#A7B09B" transform="translate(0,0)"/>
<path d="" fill="#A7B09C" transform="translate(0,0)"/>
<path d="" fill="#A6B09B" transform="translate(0,0)"/>
<path d="" fill="#A4AD96" transform="translate(0,0)"/>
<path d="" fill="#A6AF99" transform="translate(0,0)"/>
<path d="" fill="#A0A992" transform="translate(0,0)"/>
<path d="" fill="#AAB29F" transform="translate(0,0)"/>
<path d="" fill="#ACB29F" transform="translate(0,0)"/>
<path d="" fill="#A1AA94" transform="translate(0,0)"/>
<path d="" fill="#9CA490" transform="translate(0,0)"/>
<path d="" fill="#A0A894" transform="translate(0,0)"/>
<path d="" fill="#AAB19D" transform="translate(0,0)"/>
<path d="" fill="#9EA88F" transform="translate(0,0)"/>
<path d="" fill="#9FA890" transform="translate(0,0)"/>
<path d="" fill="#9FA893" transform="translate(0,0)"/>
<path d="" fill="#A0AA94" transform="translate(0,0)"/>
<path d="" fill="#9EA792" transform="translate(0,0)"/>
<path d="" fill="#A7AF9B" transform="translate(0,0)"/>
<path d="" fill="#9BA58E" transform="translate(0,0)"/>
<path d="" fill="#A1AC94" transform="translate(0,0)"/>
<path d="" fill="#A1AA94" transform="translate(0,0)"/>
<path d="" fill="#9DA790" transform="translate(0,0)"/>
<path d="" fill="#A5AD98" transform="translate(0,0)"/>
<path d="" fill="#A9B19C" transform="translate(0,0)"/>
<path d="" fill="#AAB4A0" transform="translate(0,0)"/>
<path d="" fill="#B4B9A5" transform="translate(0,0)"/>
<path d="" fill="#9BA58C" transform="translate(0,0)"/>
<path d="" fill="#A1AA96" transform="translate(0,0)"/>
<path d="" fill="#ACB4A1" transform="translate(0,0)"/>
<path d="" fill="#9FA891" transform="translate(0,0)"/>
<path d="" fill="#A5AE98" transform="translate(0,0)"/>
<path d="" fill="#A8AF9B" transform="translate(0,0)"/>
<path d="" fill="#AAB19E" transform="translate(0,0)"/>
<path d="" fill="#AAB29F" transform="translate(0,0)"/>
<path d="" fill="#9CA690" transform="translate(0,0)"/>
<path d="" fill="#9EA690" transform="translate(0,0)"/>
<path d="" fill="#9DA78F" transform="translate(0,0)"/>
<path d="" fill="#A0A890" transform="translate(0,0)"/>
<path d="" fill="#A2AC95" transform="translate(0,0)"/>
<path d="" fill="#A6AE9A" transform="translate(0,0)"/>
<path d="" fill="#AAB39D" transform="translate(0,0)"/>
<path d="" fill="#9FA791" transform="translate(0,0)"/>
<path d="" fill="#A7B09B" transform="translate(0,0)"/>
<path d="" fill="#A8B09C" transform="translate(0,0)"/>
<path d="" fill="#ABB39F" transform="translate(0,0)"/>
<path d="" fill="#9EA891" transform="translate(0,0)"/>
<path d="" fill="#A3AE98" transform="translate(0,0)"/>
<path d="" fill="#9AA48D" transform="translate(0,0)"/>
<path d="" fill="#9AA48C" transform="translate(0,0)"/>
<path d="" fill="#A5AE98" transform="translate(0,0)"/>
<path d="" fill="#99A48E" transform="translate(0,0)"/>
<path d="" fill="#A1AB94" transform="translate(0,0)"/>
<path d="" fill="#A1AB97" transform="translate(0,0)"/>
<path d="" fill="#A3AC96" transform="translate(0,0)"/>
<path d="" fill="#A4AD97" transform="translate(0,0)"/>
<path d="" fill="#A7AE99" transform="translate(0,0)"/>
<path d="" fill="#9CA58F" transform="translate(0,0)"/>
<path d="" fill="#9DA58E" transform="translate(0,0)"/>
<path d="" fill="#9DA790" transform="translate(0,0)"/>
<path d="" fill="#9DA691" transform="translate(0,0)"/>
<path d="" fill="#A1A995" transform="translate(0,0)"/>
<path d="" fill="#A1AA94" transform="translate(0,0)"/>
<path d="" fill="#A1AA94" transform="translate(0,0)"/>
<path d="" fill="#9DA78F" transform="translate(0,0)"/>
<path d="" fill="#9FAA93" transform="translate(0,0)"/>
<path d="" fill="#A4AC97" transform="translate(0,0)"/>
<path d="" fill="#A3AE97" transform="translate(0,0)"/>
<path d="" fill="#9CA993" transform="translate(0,0)"/>
<path d="" fill="#9DA791" transform="translate(0,0)"/>
<path d="" fill="#A2A996" transform="translate(0,0)"/>
<path d="" fill="#DFC3AE" transform="translate(0,0)"/>
<path d="" fill="#D8C4B0" transform="translate(0,0)"/>
<path d="" fill="#D7C1AE" transform="translate(0,0)"/>
<path d="" fill="#ABB39D" transform="translate(0,0)"/>
<path d="" fill="#ABB49C" transform="translate(0,0)"/>
<path d="" fill="#A9B39A" transform="translate(0,0)"/>
<path d="" fill="#ACB59F" transform="translate(0,0)"/>
<path d="" fill="#ACB39E" transform="translate(0,0)"/>
<path d="" fill="#A9B19B" transform="translate(0,0)"/>
<path d="" fill="#ADB59F" transform="translate(0,0)"/>
<path d="" fill="#ABB39D" transform="translate(0,0)"/>
<path d="" fill="#ACB49D" transform="translate(0,0)"/>
<path d="" fill="#ABB39D" transform="translate(0,0)"/>
<path d="" fill="#ADB59D" transform="translate(0,0)"/>
<path d="" fill="#9FA993" transform="translate(0,0)"/>
<path d="" fill="#ADB5A1" transform="translate(0,0)"/>
<path d="" fill="#AAB49B" transform="translate(0,0)"/>
<path d="" fill="#A9B39B" transform="translate(0,0)"/>
<path d="" fill="#9FA890" transform="translate(0,0)"/>
<path d="" fill="#ABB39A" transform="translate(0,0)"/>
<path d="" fill="#A9B29A" transform="translate(0,0)"/>
<path d="" fill="#A7B19A" transform="translate(0,0)"/>
<path d="" fill="#A8B29A" transform="translate(0,0)"/>
<path d="" fill="#A9B29A" transform="translate(0,0)"/>
<path d="" fill="#A1AC93" transform="translate(0,0)"/>
<path d="" fill="#A8B09A" transform="translate(0,0)"/>
<path d="" fill="#AAB59A" transform="translate(0,0)"/>
<path d="" fill="#AAB29B" transform="translate(0,0)"/>
<path d="" fill="#A9B19A" transform="translate(0,0)"/>
<path d="" fill="#AAB39B" transform="translate(0,0)"/>
<path d="" fill="#AAB29A" transform="translate(0,0)"/>
<path d="" fill="#A8B199" transform="translate(0,0)"/>
<path d="" fill="#A5AC96" transform="translate(0,0)"/>
<path d="" fill="#AAB49C" transform="translate(0,0)"/>
<path d="" fill="#AAB39A" transform="translate(0,0)"/>
<path d="" fill="#AAB39B" transform="translate(0,0)"/>
<path d="" fill="#A9B19A" transform="translate(0,0)"/>
<path d="" fill="#ABB39B" transform="translate(0,0)"/>
<path d="" fill="#A2AD94" transform="translate(0,0)"/>
<path d="" fill="#A6AE96" transform="translate(0,0)"/>
<path d="" fill="#93A3A8" transform="translate(0,0)"/>
<path d="" fill="#A7B29A" transform="translate(0,0)"/>
<path d="" fill="#93A3A8" transform="translate(0,0)"/>
<path d="" fill="#95A5AB" transform="translate(0,0)"/>
<path d="" fill="#98A5A9" transform="translate(0,0)"/>
<path d="" fill="#99A5AA" transform="translate(0,0)"/>
<path d="" fill="#91A1A5" transform="translate(0,0)"/>
<path d="" fill="#93A6AB" transform="translate(0,0)"/>
<path d="" fill="#93A3A7" transform="translate(0,0)"/>
<path d="" fill="#97A7AC" transform="translate(0,0)"/>
<path d="" fill="#94A6AD" transform="translate(0,0)"/>
<path d="" fill="#94A4AA" transform="translate(0,0)"/>
<path d="" fill="#94A3AB" transform="translate(0,0)"/>
<path d="" fill="#8DA2A8" transform="translate(0,0)"/>
<path d="" fill="#91A3A8" transform="translate(0,0)"/>
<path d="" fill="#94A3A9" transform="translate(0,0)"/>
<path d="" fill="#A5AE97" transform="translate(0,0)"/>
<path d="" fill="#A5AE97" transform="translate(0,0)"/>
<path d="" fill="#90A1A9" transform="translate(0,0)"/>
<path d="" fill="#8F9FA6" transform="translate(0,0)"/>
<path d="" fill="#8A9FA6" transform="translate(0,0)"/>
<path d="" fill="#8B9FA4" transform="translate(0,0)"/>
<path d="" fill="#A9B29B" transform="translate(0,0)"/>
<path d="" fill="#8A9DA4" transform="translate(0,0)"/>
<path d="" fill="#ADB69E" transform="translate(0,0)"/>
<path d="" fill="#A3AF99" transform="translate(0,0)"/>
<path d="" fill="#849BA4" transform="translate(0,0)"/>
<path d="" fill="#889BA7" transform="translate(0,0)"/>
<path d="" fill="#859BA4" transform="translate(0,0)"/>
<path d="" fill="#8EA2A8" transform="translate(0,0)"/>
<path d="" fill="#8AA0AA" transform="translate(0,0)"/>
<path d="" fill="#8BA0A7" transform="translate(0,0)"/>
<path d="" fill="#94A4A8" transform="translate(0,0)"/>
<path d="" fill="#93A3AA" transform="translate(0,0)"/>
<path d="" fill="#899CA6" transform="translate(0,0)"/>
<path d="" fill="#ACB5A1" transform="translate(0,0)"/>
<path d="" fill="#859CA5" transform="translate(0,0)"/>
<path d="" fill="#849CA6" transform="translate(0,0)"/>
<path d="" fill="#869DA8" transform="translate(0,0)"/>
<path d="" fill="#8D9FA8" transform="translate(0,0)"/>
<path d="" fill="#879DA5" transform="translate(0,0)"/>
<path d="" fill="#869BA5" transform="translate(0,0)"/>
<path d="" fill="#8BA0A6" transform="translate(0,0)"/>
<path d="" fill="#92A7A5" transform="translate(0,0)"/>
<path d="" fill="#ACB4A0" transform="translate(0,0)"/>
<path d="" fill="#ADB59F" transform="translate(0,0)"/>
<path d="" fill="#A8B19B" transform="translate(0,0)"/>
<path d="" fill="#A3AC98" transform="translate(0,0)"/>
<path d="" fill="#ACB59F" transform="translate(0,0)"/>
<path d="" fill="#A4AE98" transform="translate(0,0)"/>
<path d="" fill="#7591A0" transform="translate(0,0)"/>
<path d="" fill="#7F99A5" transform="translate(0,0)"/>
<path d="" fill="#8FA0A6" transform="translate(0,0)"/>
<path d="" fill="#7594A3" transform="translate(0,0)"/>
<path d="" fill="#859BA8" transform="translate(0,0)"/>
<path d="" fill="#829BA5" transform="translate(0,0)"/>
<path d="" fill="#8D9EA6" transform="translate(0,0)"/>
<path d="" fill="#73909F" transform="translate(0,0)"/>
<path d="" fill="#829CA8" transform="translate(0,0)"/>
<path d="" fill="#7F9BA4" transform="translate(0,0)"/>
<path d="" fill="#AAB49E" transform="translate(0,0)"/>
<path d="" fill="#A7B099" transform="translate(0,0)"/>
<path d="" fill="#75919F" transform="translate(0,0)"/>
<path d="" fill="#A9B299" transform="translate(0,0)"/>
<path d="" fill="#A7B19A" transform="translate(0,0)"/>
<path d="" fill="#A6AF99" transform="translate(0,0)"/>
<path d="" fill="#A4B097" transform="translate(0,0)"/>
<path d="" fill="#7995A4" transform="translate(0,0)"/>
<path d="" fill="#A8B099" transform="translate(0,0)"/>
<path d="" fill="#7E96A3" transform="translate(0,0)"/>
<path d="" fill="#7C98A7" transform="translate(0,0)"/>
<path d="" fill="#ABB49F" transform="translate(0,0)"/>
<path d="" fill="#7190A1" transform="translate(0,0)"/>
<path d="" fill="#ABB39E" transform="translate(0,0)"/>
<path d="" fill="#7693A2" transform="translate(0,0)"/>
<path d="" fill="#7090A2" transform="translate(0,0)"/>
<path d="" fill="#7394A2" transform="translate(0,0)"/>
<path d="" fill="#A8B099" transform="translate(0,0)"/>
<path d="" fill="#7693A2" transform="translate(0,0)"/>
<path d="" fill="#6D8C9D" transform="translate(0,0)"/>
<path d="" fill="#7392A1" transform="translate(0,0)"/>
<path d="" fill="#A7B39B" transform="translate(0,0)"/>
<path d="" fill="#7191A2" transform="translate(0,0)"/>
<path d="" fill="#6F90A1" transform="translate(0,0)"/>
<path d="" fill="#A7B09A" transform="translate(0,0)"/>
<path d="" fill="#6D8DA0" transform="translate(0,0)"/>
<path d="" fill="#A8B099" transform="translate(0,0)"/>
<path d="" fill="#ABB39B" transform="translate(0,0)"/>
<path d="" fill="#A8B29D" transform="translate(0,0)"/>
<path d="" fill="#7493A2" transform="translate(0,0)"/>
<path d="" fill="#7492A3" transform="translate(0,0)"/>
<path d="" fill="#6F8FA1" transform="translate(0,0)"/>
<path d="" fill="#66879C" transform="translate(0,0)"/>
<path d="" fill="#7491A0" transform="translate(0,0)"/>
<path d="" fill="#748B9E" transform="translate(0,0)"/>
<path d="" fill="#7192A4" transform="translate(0,0)"/>
<path d="" fill="#718FA2" transform="translate(0,0)"/>
<path d="" fill="#A7B198" transform="translate(0,0)"/>
<path d="" fill="#A8B19B" transform="translate(0,0)"/>
<path d="" fill="#6C8D9E" transform="translate(0,0)"/>
<path d="" fill="#728E9F" transform="translate(0,0)"/>
<path d="" fill="#A9B19C" transform="translate(0,0)"/>
<path d="" fill="#A9B299" transform="translate(0,0)"/>
<path d="" fill="#6F90A0" transform="translate(0,0)"/>
<path d="" fill="#A9B29C" transform="translate(0,0)"/>
<path d="" fill="#6890A0" transform="translate(0,0)"/>
<path d="" fill="#6A8DA1" transform="translate(0,0)"/>
<path d="" fill="#A6B099" transform="translate(0,0)"/>
<path d="" fill="#7594A4" transform="translate(0,0)"/>
<path d="" fill="#A9B19A" transform="translate(0,0)"/>
<path d="" fill="#A8B099" transform="translate(0,0)"/>
<path d="" fill="#6B899A" transform="translate(0,0)"/>
<path d="" fill="#A8B49A" transform="translate(0,0)"/>
<path d="" fill="#A9B29A" transform="translate(0,0)"/>
<path d="" fill="#A8B099" transform="translate(0,0)"/>
<path d="" fill="#6A89A0" transform="translate(0,0)"/>
<path d="" fill="#6E90A1" transform="translate(0,0)"/>
<path d="" fill="#A7B198" transform="translate(0,0)"/>
<path d="" fill="#A7B098" transform="translate(0,0)"/>
<path d="" fill="#6D8EA1" transform="translate(0,0)"/>
<path d="" fill="#6F909F" transform="translate(0,0)"/>
<path d="" fill="#AAB29B" transform="translate(0,0)"/>
<path d="" fill="#A8B299" transform="translate(0,0)"/>
<path d="" fill="#7192A1" transform="translate(0,0)"/>
<path d="" fill="#7B96A3" transform="translate(0,0)"/>
<path d="" fill="#A8B29B" transform="translate(0,0)"/>
<path d="" fill="#A7B199" transform="translate(0,0)"/>
<path d="" fill="#6E8FA1" transform="translate(0,0)"/>
<path d="" fill="#A9B199" transform="translate(0,0)"/>
<path d="" fill="#A9B199" transform="translate(0,0)"/>
<path d="" fill="#64889C" transform="translate(0,0)"/>
<path d="" fill="#A7B098" transform="translate(0,0)"/>
<path d="" fill="#7492A3" transform="translate(0,0)"/>
<path d="" fill="#A8B099" transform="translate(0,0)"/>
<path d="" fill="#6D90A1" transform="translate(0,0)"/>
<path d="" fill="#A8B29B" transform="translate(0,0)"/>
<path d="" fill="#6C8CA0" transform="translate(0,0)"/>
<path d="" fill="#63869A" transform="translate(0,0)"/>
<path d="" fill="#6C8FA0" transform="translate(0,0)"/>
<path d="" fill="#6C8EA0" transform="translate(0,0)"/>
<path d="" fill="#A6B19A" transform="translate(0,0)"/>
<path d="" fill="#6E8F9E" transform="translate(0,0)"/>
<path d="" fill="#A4AE99" transform="translate(0,0)"/>
<path d="" fill="#7993A0" transform="translate(0,0)"/>
<path d="" fill="#6F91A0" transform="translate(0,0)"/>
<path d="" fill="#6B8C9D" transform="translate(0,0)"/>
<path d="" fill="#A4AE98" transform="translate(0,0)"/>
<path d="" fill="#6B8E9F" transform="translate(0,0)"/>
<path d="" fill="#6B8B9E" transform="translate(0,0)"/>
<path d="" fill="#748F9E" transform="translate(0,0)"/>
<path d="" fill="#A7B09A" transform="translate(0,0)"/>
<path d="" fill="#6D8CA3" transform="translate(0,0)"/>
<path d="" fill="#6D8CA0" transform="translate(0,0)"/>
<path d="" fill="#A6AF98" transform="translate(0,0)"/>
<path d="" fill="#6E8EA1" transform="translate(0,0)"/>
<path d="" fill="#7294A5" transform="translate(0,0)"/>
<path d="" fill="#A6B09A" transform="translate(0,0)"/>
<path d="" fill="#6B8B9F" transform="translate(0,0)"/>
<path d="" fill="#708FA0" transform="translate(0,0)"/>
<path d="" fill="#698BA0" transform="translate(0,0)"/>
<path d="" fill="#6F8FA2" transform="translate(0,0)"/>
<path d="" fill="#A4AF99" transform="translate(0,0)"/>
<path d="" fill="#A5B098" transform="translate(0,0)"/>
<path d="" fill="#A7AF98" transform="translate(0,0)"/>
<path d="" fill="#6D8FA2" transform="translate(0,0)"/>
<path d="" fill="#6E8C9F" transform="translate(0,0)"/>
<path d="" fill="#698CA0" transform="translate(0,0)"/>
<path d="" fill="#6A90A2" transform="translate(0,0)"/>
<path d="" fill="#6C8C9F" transform="translate(0,0)"/>
<path d="" fill="#7395A4" transform="translate(0,0)"/>
<path d="" fill="#A5AE97" transform="translate(0,0)"/>
<path d="" fill="#A6AE96" transform="translate(0,0)"/>
<path d="" fill="#A5B099" transform="translate(0,0)"/>
<path d="" fill="#6E90A3" transform="translate(0,0)"/>
<path d="" fill="#6A8BA0" transform="translate(0,0)"/>
<path d="" fill="#A4AC97" transform="translate(0,0)"/>
<path d="" fill="#6C8D9D" transform="translate(0,0)"/>
<path d="" fill="#698BA0" transform="translate(0,0)"/>
<path d="" fill="#A3AC95" transform="translate(0,0)"/>
<path d="" fill="#A4AD98" transform="translate(0,0)"/>
<path d="" fill="#67899C" transform="translate(0,0)"/>
<path d="" fill="#6D90A0" transform="translate(0,0)"/>
<path d="" fill="#6D8FA1" transform="translate(0,0)"/>
<path d="" fill="#6F8EA0" transform="translate(0,0)"/>
<path d="" fill="#6B8C9E" transform="translate(0,0)"/>
<path d="" fill="#6B8C9F" transform="translate(0,0)"/>
<path d="" fill="#7091A3" transform="translate(0,0)"/>
<path d="" fill="#A3AE96" transform="translate(0,0)"/>
<path d="" fill="#6D8AA0" transform="translate(0,0)"/>
<path d="" fill="#7496A8" transform="translate(0,0)"/>
<path d="" fill="#7693A4" transform="translate(0,0)"/>
<path d="" fill="#6E8FA0" transform="translate(0,0)"/>
<path d="" fill="#718F9D" transform="translate(0,0)"/>
<path d="" fill="#A3AD97" transform="translate(0,0)"/>
<path d="" fill="#7290A0" transform="translate(0,0)"/>
<path d="" fill="#A1AB95" transform="translate(0,0)"/>
<path d="" fill="#A2AC95" transform="translate(0,0)"/>
<path d="" fill="#6A8D9D" transform="translate(0,0)"/>
<path d="" fill="#6F8FA5" transform="translate(0,0)"/>
<path d="" fill="#7192A5" transform="translate(0,0)"/>
<path d="" fill="#9FAD96" transform="translate(0,0)"/>
<path d="" fill="#75909E" transform="translate(0,0)"/>
<path d="" fill="#748F9E" transform="translate(0,0)"/>
<path d="" fill="#6A8C9E" transform="translate(0,0)"/>
<path d="" fill="#708FA2" transform="translate(0,0)"/>
<path d="" fill="#7290A1" transform="translate(0,0)"/>
<path d="" fill="#6C8B9E" transform="translate(0,0)"/>
<path d="" fill="#6C8DA0" transform="translate(0,0)"/>
<path d="" fill="#66899C" transform="translate(0,0)"/>
<path d="" fill="#7191A0" transform="translate(0,0)"/>
<path d="" fill="#6A8A9D" transform="translate(0,0)"/>
<path d="" fill="#6A8B9A" transform="translate(0,0)"/>
<path d="" fill="#6F8D9E" transform="translate(0,0)"/>
<path d="" fill="#74929F" transform="translate(0,0)"/>
<path d="" fill="#708EA2" transform="translate(0,0)"/>
<path d="" fill="#71909F" transform="translate(0,0)"/>
<path d="" fill="#7494A2" transform="translate(0,0)"/>
<path d="" fill="#71909E" transform="translate(0,0)"/>
<path d="" fill="#6F8F9A" transform="translate(0,0)"/>
<path d="" fill="#6D8D9E" transform="translate(0,0)"/>
<path d="" fill="#7791A2" transform="translate(0,0)"/>
<path d="" fill="#6F91A1" transform="translate(0,0)"/>
<path d="" fill="#6A8B9D" transform="translate(0,0)"/>
<path d="" fill="#6E91A1" transform="translate(0,0)"/>
<path d="" fill="#6F8FA2" transform="translate(0,0)"/>
<path d="" fill="#6C8DA3" transform="translate(0,0)"/>
<path d="" fill="#6F90A1" transform="translate(0,0)"/>
<path d="" fill="#6E8FA0" transform="translate(0,0)"/>
<path d="" fill="#708FA1" transform="translate(0,0)"/>
<path d="" fill="#6C8EA1" transform="translate(0,0)"/>
<path d="" fill="#6F8FA3" transform="translate(0,0)"/>
<path d="" fill="#6B8C9E" transform="translate(0,0)"/>
<path d="" fill="#6E8EA0" transform="translate(0,0)"/>
<path d="" fill="#6D8D9E" transform="translate(0,0)"/>
<path d="" fill="#6D8D9F" transform="translate(0,0)"/>
<path d="" fill="#7090A2" transform="translate(0,0)"/>
<path d="" fill="#7092A1" transform="translate(0,0)"/>
<path d="" fill="#6D8FA1" transform="translate(0,0)"/>
<path d="" fill="#7191A2" transform="translate(0,0)"/>
<path d="" fill="#6E8C9F" transform="translate(0,0)"/>
<path d="" fill="#A9B39C" transform="translate(0,0)"/>
<path d="" fill="#A7B19C" transform="translate(0,0)"/>
<path d="" fill="#A5AF99" transform="translate(0,0)"/>
<path d="" fill="#A7B099" transform="translate(0,0)"/>
<path d="" fill="#A7B09A" transform="translate(0,0)"/>
<path d="" fill="#A8B19B" transform="translate(0,0)"/>
<path d="" fill="#A7B19A" transform="translate(0,0)"/>
<path d="" fill="#A7B098" transform="translate(0,0)"/>
<path d="" fill="#AAB29D" transform="translate(0,0)"/>
<path d="" fill="#A9B29A" transform="translate(0,0)"/>
<path d="" fill="#A6B09A" transform="translate(0,0)"/>
<path d="" fill="#A7B099" transform="translate(0,0)"/>
<path d="" fill="#A7B29A" transform="translate(0,0)"/>
<path d="" fill="#A6AE98" transform="translate(0,0)"/>
<path d="" fill="#A7B09A" transform="translate(0,0)"/>
<path d="" fill="#A9B29B" transform="translate(0,0)"/>
<path d="" fill="#A8B099" transform="translate(0,0)"/>
<path d="" fill="#A7B29B" transform="translate(0,0)"/>
<path d="" fill="#A7B19A" transform="translate(0,0)"/>
<path d="" fill="#76929D" transform="translate(0,0)"/>
<path d="" fill="#A9B199" transform="translate(0,0)"/>
<path d="" fill="#A7B099" transform="translate(0,0)"/>
<path d="" fill="#A8B19B" transform="translate(0,0)"/>
<path d="" fill="#A8B19A" transform="translate(0,0)"/>
<path d="" fill="#A6AE9A" transform="translate(0,0)"/>
<path d="" fill="#A5B099" transform="translate(0,0)"/>
<path d="" fill="#A6B098" transform="translate(0,0)"/>
<path d="" fill="#A8B09C" transform="translate(0,0)"/>
<path d="" fill="#A5AF97" transform="translate(0,0)"/>
<path d="" fill="#6F909D" transform="translate(0,0)"/>
<path d="" fill="#718FA1" transform="translate(0,0)"/>
<path d="" fill="#6F8F9F" transform="translate(0,0)"/>
<path d="" fill="#6F8D9D" transform="translate(0,0)"/>
<path d="" fill="#78959F" transform="translate(0,0)"/>
<path d="" fill="#A8B09A" transform="translate(0,0)"/>
<path d="" fill="#A8B199" transform="translate(0,0)"/>
<path d="" fill="#A8B19B" transform="translate(0,0)"/>
<path d="" fill="#738E9E" transform="translate(0,0)"/>
<path d="" fill="#708F9F" transform="translate(0,0)"/>
<path d="" fill="#7091A2" transform="translate(0,0)"/>
<path d="" fill="#7392A3" transform="translate(0,0)"/>
<path d="" fill="#A7B09A" transform="translate(0,0)"/>
<path d="" fill="#7392A1" transform="translate(0,0)"/>
<path d="" fill="#718FA0" transform="translate(0,0)"/>
<path d="" fill="#78949E" transform="translate(0,0)"/>
<path d="" fill="#A9AF9A" transform="translate(0,0)"/>
<path d="" fill="#7594A3" transform="translate(0,0)"/>
<path d="" fill="#A7B099" transform="translate(0,0)"/>
<path d="" fill="#A8B299" transform="translate(0,0)"/>
<path d="" fill="#A7B199" transform="translate(0,0)"/>
<path d="" fill="#A7AE99" transform="translate(0,0)"/>
<path d="" fill="#75929D" transform="translate(0,0)"/>
<path d="" fill="#A4AE99" transform="translate(0,0)"/>
<path d="" fill="#6C8A9E" transform="translate(0,0)"/>
<path d="" fill="#6D8C9F" transform="translate(0,0)"/>
<path d="" fill="#6D8E9E" transform="translate(0,0)"/>
<path d="" fill="#74909C" transform="translate(0,0)"/>
<path d="" fill="#A8B199" transform="translate(0,0)"/>
<path d="" fill="#A6AF98" transform="translate(0,0)"/>
<path d="" fill="#A4AE99" transform="translate(0,0)"/>
<path d="" fill="#A5AE97" transform="translate(0,0)"/>
<path d="" fill="#A4AE96" transform="translate(0,0)"/>
<path d="" fill="#A3AC96" transform="translate(0,0)"/>
<path d="" fill="#6F8C9B" transform="translate(0,0)"/>
<path d="" fill="#7C95A4" transform="translate(0,0)"/>
<path d="" fill="#A6B09A" transform="translate(0,0)"/>
<path d="" fill="#A6AF97" transform="translate(0,0)"/>
<path d="" fill="#648998" transform="translate(0,0)"/>
<path d="" fill="#77949D" transform="translate(0,0)"/>
<path d="" fill="#688B9E" transform="translate(0,0)"/>
<path d="" fill="#6F8FA2" transform="translate(0,0)"/>
<path d="" fill="#6D8E9F" transform="translate(0,0)"/>
<path d="" fill="#6C8FA1" transform="translate(0,0)"/>
<path d="" fill="#A6B097" transform="translate(0,0)"/>
<path d="" fill="#A5AF97" transform="translate(0,0)"/>
<path d="" fill="#7191A1" transform="translate(0,0)"/>
<path d="" fill="#698C9F" transform="translate(0,0)"/>
<path d="" fill="#698D9F" transform="translate(0,0)"/>
<path d="" fill="#6D91A0" transform="translate(0,0)"/>
<path d="" fill="#698A9D" transform="translate(0,0)"/>
<path d="" fill="#6B8C9D" transform="translate(0,0)"/>
<path d="" fill="#6C8C9F" transform="translate(0,0)"/>
<path d="" fill="#75909B" transform="translate(0,0)"/>
<path d="" fill="#73919C" transform="translate(0,0)"/>
<path d="" fill="#668797" transform="translate(0,0)"/>
<path d="" fill="#6F919E" transform="translate(0,0)"/>
<path d="" fill="#6F91A0" transform="translate(0,0)"/>
<path d="" fill="#6E909E" transform="translate(0,0)"/>
<path d="" fill="#73919F" transform="translate(0,0)"/>
<path d="" fill="#698B9F" transform="translate(0,0)"/>
<path d="" fill="#708EA1" transform="translate(0,0)"/>
<path d="" fill="#6B8DA0" transform="translate(0,0)"/>
<path d="" fill="#7193A2" transform="translate(0,0)"/>
<path d="" fill="#6989A0" transform="translate(0,0)"/>
<path d="" fill="#6A8BA0" transform="translate(0,0)"/>
<path d="" fill="#7090A1" transform="translate(0,0)"/>
<path d="" fill="#6E919F" transform="translate(0,0)"/>
<path d="" fill="#748F9B" transform="translate(0,0)"/>
<path d="" fill="#7290A1" transform="translate(0,0)"/>
<path d="" fill="#6F8FA1" transform="translate(0,0)"/>
<path d="" fill="#6C8C9D" transform="translate(0,0)"/>
<path d="" fill="#7391A1" transform="translate(0,0)"/>
<path d="" fill="#6D8D9F" transform="translate(0,0)"/>
<path d="" fill="#69889D" transform="translate(0,0)"/>
<path d="" fill="#6D8B9F" transform="translate(0,0)"/>
<path d="" fill="#678999" transform="translate(0,0)"/>
<path d="" fill="#7290A2" transform="translate(0,0)"/>
<path d="" fill="#6C8BA0" transform="translate(0,0)"/>
<path d="" fill="#6C8E9F" transform="translate(0,0)"/>
<path d="" fill="#6B8794" transform="translate(0,0)"/>
<path d="" fill="#698897" transform="translate(0,0)"/>
<path d="" fill="#6B8B9F" transform="translate(0,0)"/>
<path d="" fill="#6C8D9F" transform="translate(0,0)"/>
<path d="" fill="#6F8E9D" transform="translate(0,0)"/>
<path d="" fill="#66899E" transform="translate(0,0)"/>
<path d="" fill="#6C8A96" transform="translate(0,0)"/>
<path d="" fill="#718D98" transform="translate(0,0)"/>
<path d="" fill="#708FA1" transform="translate(0,0)"/>
<path d="" fill="#708B97" transform="translate(0,0)"/>
<path d="" fill="#7291A0" transform="translate(0,0)"/>
<path d="" fill="#6D8FA0" transform="translate(0,0)"/>
<path d="" fill="#668593" transform="translate(0,0)"/>
<path d="" fill="#66889B" transform="translate(0,0)"/>
<path d="" fill="#6D8FA0" transform="translate(0,0)"/>
<path d="" fill="#6E8E9F" transform="translate(0,0)"/>
<path d="" fill="#6A8EA0" transform="translate(0,0)"/>
<path d="" fill="#6A8BA0" transform="translate(0,0)"/>
<path d="" fill="#6C8A96" transform="translate(0,0)"/>
<path d="" fill="#668699" transform="translate(0,0)"/>
<path d="" fill="#6A8A9E" transform="translate(0,0)"/>
<path d="" fill="#6D8D9C" transform="translate(0,0)"/>
<path d="" fill="#698B9E" transform="translate(0,0)"/>
<path d="" fill="#718D9B" transform="translate(0,0)"/>
<path d="" fill="#69889D" transform="translate(0,0)"/>
<path d="" fill="#6A8B9E" transform="translate(0,0)"/>
<path d="" fill="#6B8C9B" transform="translate(0,0)"/>
<path d="" fill="#67889C" transform="translate(0,0)"/>
<path d="" fill="#6A8D9F" transform="translate(0,0)"/>
<path d="" fill="#608194" transform="translate(0,0)"/>
<path d="" fill="#608497" transform="translate(0,0)"/>
<path d="" fill="#6D8D9F" transform="translate(0,0)"/>
<path d="" fill="#748F9A" transform="translate(0,0)"/>
<path d="" fill="#68879A" transform="translate(0,0)"/>
<path d="" fill="#718E99" transform="translate(0,0)"/>
<path d="" fill="#6E8C9F" transform="translate(0,0)"/>
<path d="" fill="#668A9C" transform="translate(0,0)"/>
<path d="" fill="#6F8E9B" transform="translate(0,0)"/>
<path d="" fill="#67889C" transform="translate(0,0)"/>
<path d="" fill="#66869A" transform="translate(0,0)"/>
<path d="" fill="#67869B" transform="translate(0,0)"/>
<path d="" fill="#648598" transform="translate(0,0)"/>
<path d="" fill="#6B8B9E" transform="translate(0,0)"/>
<path d="" fill="#688C9C" transform="translate(0,0)"/>
<path d="" fill="#698A9D" transform="translate(0,0)"/>
<path d="" fill="#698B9D" transform="translate(0,0)"/>
<path d="" fill="#6B8C9D" transform="translate(0,0)"/>
<path d="" fill="#6C8E9E" transform="translate(0,0)"/>
<path d="" fill="#708D99" transform="translate(0,0)"/>
<path d="" fill="#688B9E" transform="translate(0,0)"/>
<path d="" fill="#6D8D9E" transform="translate(0,0)"/>
<path d="" fill="#6B8C9E" transform="translate(0,0)"/>
<path d="" fill="#6D8D9E" transform="translate(0,0)"/>
<path d="" fill="#648B9D" transform="translate(0,0)"/>
<path d="" fill="#6B8C9D" transform="translate(0,0)"/>
<path d="" fill="#698CA2" transform="translate(0,0)"/>
<path d="" fill="#7893A2" transform="translate(0,0)"/>
<path d="" fill="#70909E" transform="translate(0,0)"/>
<path d="" fill="#6A8CA0" transform="translate(0,0)"/>
<path d="" fill="#698B9E" transform="translate(0,0)"/>
<path d="" fill="#6C899F" transform="translate(0,0)"/>
<path d="" fill="#6B879C" transform="translate(0,0)"/>
<path d="" fill="#698B9B" transform="translate(0,0)"/>
<path d="" fill="#6C8CA1" transform="translate(0,0)"/>
<path d="" fill="#6F8A9A" transform="translate(0,0)"/>
<path d="" fill="#6B8B9D" transform="translate(0,0)"/>
<path d="" fill="#6F8D9D" transform="translate(0,0)"/>
<path d="" fill="#6A8797" transform="translate(0,0)"/>
<path d="" fill="#698A9D" transform="translate(0,0)"/>
<path d="" fill="#65879B" transform="translate(0,0)"/>
<path d="" fill="#6A8BA0" transform="translate(0,0)"/>
<path d="" fill="#708D9D" transform="translate(0,0)"/>
<path d="" fill="#698B9E" transform="translate(0,0)"/>
<path d="" fill="#6B8EA0" transform="translate(0,0)"/>
<path d="" fill="#6E8FA0" transform="translate(0,0)"/>
<path d="" fill="#6A8B9D" transform="translate(0,0)"/>
<path d="" fill="#6C8C9C" transform="translate(0,0)"/>
<path d="" fill="#6C8D9E" transform="translate(0,0)"/>
<path d="" fill="#69899B" transform="translate(0,0)"/>
<path d="" fill="#6B8E9F" transform="translate(0,0)"/>
<path d="" fill="#6C8CA0" transform="translate(0,0)"/>
<path d="" fill="#68899D" transform="translate(0,0)"/>
<path d="" fill="#658897" transform="translate(0,0)"/>
<path d="" fill="#678A9A" transform="translate(0,0)"/>
<path d="" fill="#6D8D9F" transform="translate(0,0)"/>
<path d="" fill="#63869A" transform="translate(0,0)"/>
<path d="" fill="#68899B" transform="translate(0,0)"/>
<path d="" fill="#678B9A" transform="translate(0,0)"/>
<path d="" fill="#5B8296" transform="translate(0,0)"/>
<path d="" fill="#6C8B9D" transform="translate(0,0)"/>
<path d="" fill="#698999" transform="translate(0,0)"/>
<path d="" fill="#688A9B" transform="translate(0,0)"/>
<path d="" fill="#678A9B" transform="translate(0,0)"/>
<path d="" fill="#6A8B9C" transform="translate(0,0)"/>
<path d="" fill="#6F8F9D" transform="translate(0,0)"/>
<path d="" fill="#6D8B9C" transform="translate(0,0)"/>
<path d="" fill="#688998" transform="translate(0,0)"/>
<path d="" fill="#658494" transform="translate(0,0)"/>
<path d="" fill="#6D8B9B" transform="translate(0,0)"/>
<path d="" fill="#6C8B9B" transform="translate(0,0)"/>
<path d="" fill="#6A8B98" transform="translate(0,0)"/>
<path d="" fill="#72909F" transform="translate(0,0)"/>
<path d="" fill="#6C8D99" transform="translate(0,0)"/>
<path d="" fill="#71919C" transform="translate(0,0)"/>
<path d="" fill="#6C8B9B" transform="translate(0,0)"/>
<path d="" fill="#678998" transform="translate(0,0)"/>
<path d="" fill="#678999" transform="translate(0,0)"/>
<path d="" fill="#698B99" transform="translate(0,0)"/>
<path d="" fill="#6A8A98" transform="translate(0,0)"/>
<path d="" fill="#668795" transform="translate(0,0)"/>
<path d="" fill="#6E8C99" transform="translate(0,0)"/>
<path d="" fill="#6B8A98" transform="translate(0,0)"/>
<path d="" fill="#698998" transform="translate(0,0)"/>
<path d="" fill="#6A8898" transform="translate(0,0)"/>
<path d="" fill="#678996" transform="translate(0,0)"/>
<path d="" fill="#6A8997" transform="translate(0,0)"/>
<path d="" fill="#678897" transform="translate(0,0)"/>
<path d="" fill="#6F8C99" transform="translate(0,0)"/>
<path d="" fill="#718D9A" transform="translate(0,0)"/>
<path d="" fill="#6F8D9A" transform="translate(0,0)"/>
<path d="" fill="#6B8B99" transform="translate(0,0)"/>
<path d="" fill="#6E8B98" transform="translate(0,0)"/>
<path d="" fill="#718E9A" transform="translate(0,0)"/>
<path d="" fill="#708E98" transform="translate(0,0)"/>
<path d="" fill="#678795" transform="translate(0,0)"/>
<path d="" fill="#72909E" transform="translate(0,0)"/>
<path d="" fill="#74919D" transform="translate(0,0)"/>
<path d="" fill="#698996" transform="translate(0,0)"/>
<path d="" fill="#668696" transform="translate(0,0)"/>
<path d="" fill="#6C8C9A" transform="translate(0,0)"/>
<path d="" fill="#9CAA96" transform="translate(0,0)"/>
<path d="" fill="#9CA996" transform="translate(0,0)"/>
<path d="" fill="#9EAB97" transform="translate(0,0)"/>
<path d="" fill="#9BA996" transform="translate(0,0)"/>
<path d="" fill="#9FAB96" transform="translate(0,0)"/>
<path d="" fill="#9EAC98" transform="translate(0,0)"/>
<path d="" fill="#9EAA97" transform="translate(0,0)"/>
<path d="" fill="#9BA796" transform="translate(0,0)"/>
<path d="" fill="#9DAB95" transform="translate(0,0)"/>
<path d="" fill="#9EAA95" transform="translate(0,0)"/>
<path d="" fill="#9DAB95" transform="translate(0,0)"/>
<path d="" fill="#A0AC98" transform="translate(0,0)"/>
<path d="" fill="#A0AC96" transform="translate(0,0)"/>
<path d="" fill="#9EAB97" transform="translate(0,0)"/>
<path d="" fill="#A1AC98" transform="translate(0,0)"/>
<path d="" fill="#A0AC97" transform="translate(0,0)"/>
<path d="" fill="#9CAA97" transform="translate(0,0)"/>
<path d="" fill="#A0AE98" transform="translate(0,0)"/>
<path d="" fill="#9BA995" transform="translate(0,0)"/>
<path d="" fill="#9FAA96" transform="translate(0,0)"/>
<path d="" fill="#A0AB96" transform="translate(0,0)"/>
<path d="" fill="#9BA996" transform="translate(0,0)"/>
<path d="" fill="#9FAA95" transform="translate(0,0)"/>
<path d="" fill="#A1AD98" transform="translate(0,0)"/>
<path d="" fill="#A1AB99" transform="translate(0,0)"/>
<path d="" fill="#A0AD97" transform="translate(0,0)"/>
<path d="" fill="#A0AB97" transform="translate(0,0)"/>
<path d="" fill="#9FAA94" transform="translate(0,0)"/>
<path d="" fill="#9FAA95" transform="translate(0,0)"/>
<path d="" fill="#A0AD96" transform="translate(0,0)"/>
<path d="" fill="#A2AE97" transform="translate(0,0)"/>
<path d="" fill="#A1AC95" transform="translate(0,0)"/>
<path d="" fill="#9EAA96" transform="translate(0,0)"/>
<path d="" fill="#A1AD95" transform="translate(0,0)"/>
<path d="" fill="#A0AC97" transform="translate(0,0)"/>
<path d="" fill="#9EA995" transform="translate(0,0)"/>
<path d="" fill="#A0AB97" transform="translate(0,0)"/>
<path d="" fill="#9EA894" transform="translate(0,0)"/>
<path d="" fill="#A1AD95" transform="translate(0,0)"/>
<path d="" fill="#A0AC96" transform="translate(0,0)"/>
<path d="" fill="#919D89" transform="translate(0,0)"/>
<path d="" fill="#A0AB95" transform="translate(0,0)"/>
<path d="" fill="#A0A893" transform="translate(0,0)"/>
<path d="" fill="#A3AD98" transform="translate(0,0)"/>
<path d="" fill="#A0AB96" transform="translate(0,0)"/>
<path d="" fill="#9FAC95" transform="translate(0,0)"/>
<path d="" fill="#93A08A" transform="translate(0,0)"/>
<path d="" fill="#94A08A" transform="translate(0,0)"/>
<path d="" fill="#9FAC96" transform="translate(0,0)"/>
<path d="" fill="#A2AD96" transform="translate(0,0)"/>
<path d="" fill="#A0AC95" transform="translate(0,0)"/>
<path d="" fill="#A1AD94" transform="translate(0,0)"/>
<path d="" fill="#A3AF97" transform="translate(0,0)"/>
<path d="" fill="#A3AE96" transform="translate(0,0)"/>
<path d="" fill="#95A08B" transform="translate(0,0)"/>
<path d="" fill="#9DA992" transform="translate(0,0)"/>
<path d="" fill="#929F8A" transform="translate(0,0)"/>
<path d="" fill="#A2AC95" transform="translate(0,0)"/>
<path d="" fill="#95A18D" transform="translate(0,0)"/>
<path d="" fill="#959F8B" transform="translate(0,0)"/>
<path d="" fill="#A1AC95" transform="translate(0,0)"/>
<path d="" fill="#96A18D" transform="translate(0,0)"/>
<path d="" fill="#93A08A" transform="translate(0,0)"/>
<path d="" fill="#9EAA93" transform="translate(0,0)"/>
<path d="" fill="#919C89" transform="translate(0,0)"/>
<path d="" fill="#A1AE94" transform="translate(0,0)"/>
<path d="" fill="#A1AD96" transform="translate(0,0)"/>
<path d="" fill="#A4AE97" transform="translate(0,0)"/>
<path d="" fill="#A1AA93" transform="translate(0,0)"/>
<path d="" fill="#97A18C" transform="translate(0,0)"/>
<path d="" fill="#A5AF99" transform="translate(0,0)"/>
<path d="" fill="#A3AF97" transform="translate(0,0)"/>
<path d="" fill="#A2AD97" transform="translate(0,0)"/>
<path d="" fill="#97A18C" transform="translate(0,0)"/>
<path d="" fill="#9FAB94" transform="translate(0,0)"/>
<path d="" fill="#939E89" transform="translate(0,0)"/>
<path d="" fill="#A2AD95" transform="translate(0,0)"/>
<path d="" fill="#A5AE97" transform="translate(0,0)"/>
<path d="" fill="#A7B099" transform="translate(0,0)"/>
<path d="" fill="#9EAB95" transform="translate(0,0)"/>
<path d="" fill="#A4AE96" transform="translate(0,0)"/>
<path d="" fill="#A5B099" transform="translate(0,0)"/>
<path d="" fill="#A7B49A" transform="translate(0,0)"/>
<path d="" fill="#A4AE99" transform="translate(0,0)"/>
<path d="" fill="#9AA48E" transform="translate(0,0)"/>
<path d="" fill="#A6B299" transform="translate(0,0)"/>
<path d="" fill="#A3B198" transform="translate(0,0)"/>
<path d="" fill="#9AA68F" transform="translate(0,0)"/>
<path d="" fill="#A8B09A" transform="translate(0,0)"/>
<path d="" fill="#A8B39B" transform="translate(0,0)"/>
<path d="" fill="#A6B19A" transform="translate(0,0)"/>
<path d="" fill="#9AA490" transform="translate(0,0)"/>
<path d="" fill="#A7B199" transform="translate(0,0)"/>
<path d="" fill="#9CA68F" transform="translate(0,0)"/>
<path d="" fill="#A8B19A" transform="translate(0,0)"/>
<path d="" fill="#9CA591" transform="translate(0,0)"/>
<path d="" fill="#A8B39A" transform="translate(0,0)"/>
<path d="" fill="#A8B19B" transform="translate(0,0)"/>
<path d="" fill="#A7B29B" transform="translate(0,0)"/>
<path d="" fill="#A6B09A" transform="translate(0,0)"/>
<path d="" fill="#9CA891" transform="translate(0,0)"/>
<path d="" fill="#9EA791" transform="translate(0,0)"/>
<path d="" fill="#A7B29A" transform="translate(0,0)"/>
<path d="" fill="#9BA58F" transform="translate(0,0)"/>
<path d="" fill="#A9B39C" transform="translate(0,0)"/>
<path d="" fill="#A9B39C" transform="translate(0,0)"/>
<path d="" fill="#9DA692" transform="translate(0,0)"/>
<path d="" fill="#9CA890" transform="translate(0,0)"/>
<path d="" fill="#ACB39D" transform="translate(0,0)"/>
<path d="" fill="#9DA791" transform="translate(0,0)"/>
<path d="" fill="#9EA891" transform="translate(0,0)"/>
<path d="" fill="#9CA68F" transform="translate(0,0)"/>
<path d="" fill="#AAB59B" transform="translate(0,0)"/>
<path d="" fill="#AAB49C" transform="translate(0,0)"/>
<path d="" fill="#AAB59D" transform="translate(0,0)"/>
<path d="" fill="#A9B39B" transform="translate(0,0)"/>
<path d="" fill="#AAB39C" transform="translate(0,0)"/>
<path d="" fill="#AAB49B" transform="translate(0,0)"/>
<path d="" fill="#A8B09A" transform="translate(0,0)"/>
<path d="" fill="#9EA992" transform="translate(0,0)"/>
<path d="" fill="#9DA993" transform="translate(0,0)"/>
<path d="" fill="#AAB49B" transform="translate(0,0)"/>
<path d="" fill="#AAB49D" transform="translate(0,0)"/>
<path d="" fill="#AAB39B" transform="translate(0,0)"/>
<path d="" fill="#A9B39D" transform="translate(0,0)"/>
<path d="" fill="#A9B39A" transform="translate(0,0)"/>
<path d="" fill="#9FAA93" transform="translate(0,0)"/>
<path d="" fill="#AAB39B" transform="translate(0,0)"/>
<path d="" fill="#ACB49D" transform="translate(0,0)"/>
<path d="" fill="#A9B39A" transform="translate(0,0)"/>
<path d="" fill="#A7B39A" transform="translate(0,0)"/>
<path d="" fill="#9EA992" transform="translate(0,0)"/>
<path d="" fill="#A9B39C" transform="translate(0,0)"/>
<path d="" fill="#AAB49B" transform="translate(0,0)"/>
<path d="" fill="#AAB49D" transform="translate(0,0)"/>
<path d="" fill="#ABB59D" transform="translate(0,0)"/>
<path d="" fill="#9FA891" transform="translate(0,0)"/>
<path d="" fill="#9EAA92" transform="translate(0,0)"/>
<path d="" fill="#ABB59D" transform="translate(0,0)"/>
<path d="" fill="#ABB39D" transform="translate(0,0)"/>
<path d="" fill="#A9B49C" transform="translate(0,0)"/>
<path d="" fill="#A8B19B" transform="translate(0,0)"/>
</svg>
