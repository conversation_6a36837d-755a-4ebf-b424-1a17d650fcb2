<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet">
  <defs>
    <style type="text/css">
      @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@700;400&display=swap');
      .title { font-family: 'Montserrat', Arial, sans-serif; font-weight: 700; font-size: 120px; fill: #FFFFFF; }
      .subtitle { font-family: 'Montserrat', Arial, sans-serif; font-weight: 400; font-size: 48px; fill: #5883A3; }
    </style>
  </defs>
  <rect width="1080" height="1080" fill="#101D32"/>
  <g>
    <text x="540" y="210" text-anchor="middle" class="title" letter-spacing="0.01em">COMING</text>
    <text x="540" y="340" text-anchor="middle" class="title" letter-spacing="0.01em">SOON</text>
    <text x="540" y="440" text-anchor="middle" class="subtitle" letter-spacing="0.01em">Innovafing Your Comection</text>
  </g>
  <g>
    <rect x="340" y="540" width="400" height="520" rx="32" fill="none" stroke="#22314A" stroke-width="16"/>
    <rect x="510" y="540" width="60" height="22" rx="8" fill="#22314A"/>
    <circle cx="530" cy="552" r="6" fill="#22314A"/>
    <circle cx="550" cy="552" r="4" fill="#22314A"/>
  </g>
</svg>