<svg
  xmlns="http://www.w3.org/2000/svg"
  width="1080" height="1080"
  viewBox="0 0 1080 1080"
>
  <style type="text/css"><![CDATA[
    @import url('https://fonts.googleapis.com/css2?family=Baloo+2:wght@800&family=Poppins:wght@300;400&display=swap');
    .bg        { fill: #0D1333; }
    .semi      { fill: url(#gradSemi); }
    .title     { font-family: 'Baloo 2', sans-serif; font-size: 120px; fill: #FFFFFF; text-anchor: middle; }
    .subtitle  { font-family: 'Poppins', sans-serif; font-size: 36px; fill: #E4E4E4; text-anchor: middle; }
    .pill-bg   { fill: #FEFDF0; }
    .btn-bg    { fill: #5182F6; }
    .pill-txt  { font-family: 'Poppins', sans-serif; font-size: 36px; fill: #A0A0A0; }
    .btn-txt   { font-family: 'Poppins', sans-serif; font-size: 36px; fill: #FFFFFF; }
    .dots      { fill: #1C2553; }
  ]]></style>

  <defs>
    <radialGradient id="gradBG" cx="50%" cy="50%" r="70%">
      <stop offset="0%" stop-color="#192047"/>
      <stop offset="100%" stop-color="#0D1333"/>
    </radialGradient>
    <linearGradient id="gradSemi" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2E3B6A"/>
      <stop offset="100%" stop-color="#1A2250"/>
    </linearGradient>
  </defs>

  <rect class="bg" width="1080" height="1080"/>

  <rect width="1080" height="1080" fill="url(#gradBG)" opacity="0.3"/>

  <path class="semi"
    d="M 0,300
       A 300,300 0 0 1 300,0
       L 0,0 Z"/>

  <text x="540" y="480" class="title">COMING SOON</text>

  <text x="540" y="560" class="subtitle">
    Gep contricatmasoniocutrivilco et une bile
  </text>
  <text x="540" y="610" class="subtitle">
    heti eresuesisiemaier ceesthe srina hnc le tevel
  </text>

  <g transform="translate(180,680)">
    <rect x="0" y="0" width="720" height="100" rx="50" ry="50" fill="#5182F6"/>
    <rect x="0" y="0" width="480" height="100" rx="50" ry="50" class="pill-bg"/>
    <text x="240" y="62" class="pill-txt" text-anchor="middle">
      Degr yenn arnan
    </text>
    <text x="600" y="62" class="btn-txt" text-anchor="middle">
      Siabtmnt
    </text>
  </g>

  <g class="dots">
    <circle cx="900" cy="900" r="10"/>
    <circle cx="960" cy="900" r="10"/>
    <circle cx="1020" cy="900" r="10"/>
    <circle cx="900" cy="960" r="10"/>
    <circle cx="960" cy="960" r="10"/>
    <circle cx="1020" cy="960" r="10"/>
  </g>

  <circle cx="940" cy="140" r="30" fill="#1C224B" opacity="0.5"/>
  <circle cx="1000" cy="150" r="25" fill="#1C224B" opacity="0.5"/>
</svg>