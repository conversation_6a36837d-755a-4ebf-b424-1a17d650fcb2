<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C11.55 0 23.1 0 35 0 C44.791 26.999 44.791 26.999 48.147 37.779 C48.71 39.537 49.348 41.272 50 43 C50.99 43.495 50.99 43.495 52 44 C52.174 46.014 52.346 48.028 52.516 50.043 C53.249 54.518 54.764 58.68 56.273 62.946 C58 67.838 58 67.838 58 70 C58.66 70 59.32 70 60 70 C60.33 70.99 60.66 71.98 61 73 C61.128 72.408 61.255 71.817 61.387 71.207 C62 69 62 69 63.812 65.688 C66.14 61.191 67.273 56.514 68.504 51.623 C69.417 48.047 70.433 44.647 71.875 41.25 C73.944 36.332 75.651 31.317 77.312 26.25 C77.591 25.433 77.87 24.616 78.158 23.774 C79.655 19.129 80.339 15.856 79 11 C79.99 11.33 80.98 11.66 82 12 C82.597 9.237 83 6.84 83 4 C84.031 1.837 84.031 1.837 86 0 C88.647 -0.357 90.872 -0.468 93.512 -0.391 C94.24 -0.385 94.968 -0.379 95.718 -0.373 C98.042 -0.351 100.364 -0.301 102.688 -0.25 C104.264 -0.23 105.84 -0.212 107.416 -0.195 C111.278 -0.151 115.139 -0.082 119 0 C119.154 14.89 119.302 29.781 119.443 44.672 C119.509 51.586 119.576 58.5 119.648 65.414 C119.718 72.086 119.782 78.758 119.843 85.43 C119.867 87.976 119.893 90.522 119.921 93.068 C119.959 96.633 119.991 100.197 120.022 103.762 C120.035 104.816 120.048 105.87 120.061 106.955 C120.096 111.76 120.025 116.277 119 121 C118.34 119.68 117.68 118.36 117 117 C111.39 117 105.78 117 100 117 C99.67 117.66 99.34 118.32 99 119 C99 118.34 99 117.68 99 117 C98.01 117 97.02 117 96 117 C94.633 114.266 94.837 112.05 94.795 108.99 C94.765 107.098 94.765 107.098 94.734 105.168 C94.717 103.787 94.7 102.405 94.684 101.023 C94.663 99.614 94.642 98.204 94.621 96.795 C94.565 93.083 94.516 89.37 94.468 85.658 C94.418 81.871 94.362 78.084 94.307 74.297 C94.199 66.865 94.098 59.432 94 52 C93.34 52 92.68 52 92 52 C91.773 53.174 91.546 54.349 91.312 55.559 C89.757 62.557 87.319 69.289 85 76.062 C82.408 83.654 79.84 91.184 78 99 C77.349 100.341 76.685 101.676 76 103 C75.623 104.657 75.279 106.323 75 108 C74.34 108 73.68 108 73 108 C72.34 110.97 71.68 113.94 71 117 C64.73 117 58.46 117 52 117 C52.33 117.99 52.66 118.98 53 120 C52.34 120 51.68 120 51 120 C50.845 119.041 50.845 119.041 50.688 118.062 C50.461 117.382 50.234 116.701 50 116 C49.01 115.67 48.02 115.34 47 115 C47.299 114.179 47.299 114.179 47.604 113.342 C48.125 110.259 47.39 108.467 46.332 105.531 C45.939 104.419 45.546 103.307 45.14 102.161 C44.702 100.953 44.264 99.745 43.812 98.5 C42.888 95.903 41.972 93.303 41.055 90.703 C40.811 90.017 40.568 89.33 40.317 88.623 C38.381 83.139 36.524 77.63 34.695 72.109 C34.423 71.287 34.151 70.465 33.87 69.618 C32.789 66.349 31.71 63.079 30.635 59.808 C29.883 57.522 29.127 55.238 28.371 52.953 C28.145 52.263 27.92 51.572 27.687 50.861 C26.8 47.676 26.8 47.676 25 45 C25.002 45.887 25.004 46.775 25.007 47.689 C25.027 56.032 25.042 64.376 25.052 72.72 C25.057 77.01 25.064 81.3 25.075 85.59 C25.086 89.727 25.092 93.863 25.095 98 C25.097 99.582 25.1 101.164 25.106 102.745 C25.113 104.953 25.114 107.161 25.114 109.369 C25.116 110.628 25.118 111.887 25.12 113.184 C25 116 25 116 24 117 C22.292 117.087 20.581 117.107 18.871 117.098 C17.319 117.093 17.319 117.093 15.736 117.088 C14.648 117.08 13.559 117.071 12.438 117.062 C10.799 117.056 10.799 117.056 9.127 117.049 C6.418 117.037 3.709 117.021 1 117 C1 116.34 1 115.68 1 115 C0.34 114.67 -0.32 114.34 -1 114 C-0.475 108.97 0.106 103.981 1 99 C0.34 99 -0.32 99 -1 99 C-1 96.333 -1 93.667 -1 91 C-1.021 89.824 -1.041 88.649 -1.062 87.438 C-1.07 83.216 -0.597 79.176 0 75 C-0.33 75 -0.66 75 -1 75 C-1 67 -1 59 -1 51 C-1.012 49.931 -1.023 48.863 -1.035 47.762 C-1.045 46.404 -1.054 45.046 -1.062 43.688 C-1.071 43.01 -1.079 42.333 -1.088 41.635 C-1.104 38.248 -0.936 35.254 0 32 C-0.144 31.423 -0.289 30.845 -0.438 30.25 C-1.301 26.796 -1.117 23.557 -1 20 C-0.67 19.67 -0.34 19.34 0 19 C0.136 15.337 0.165 12.494 -1 9 C-0.34 9 0.32 9 1 9 C0.67 6.03 0.34 3.06 0 0 Z M94 46 C93.67 47.65 93.34 49.3 93 51 C93.66 50.67 94.32 50.34 95 50 C94.67 48.68 94.34 47.36 94 46 Z M0 83 C1 87 1 87 1 87 Z " fill="#EEF1FE" transform="translate(433,109)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.32 2 3.64 2 5 2 C6.229 4.457 6.123 6.048 6.12 8.796 C6.122 10.315 6.122 10.315 6.124 11.864 C6.119 13.537 6.119 13.537 6.114 15.243 C6.114 16.413 6.114 17.584 6.114 18.79 C6.113 22.005 6.108 25.22 6.101 28.434 C6.095 31.792 6.095 35.15 6.093 38.508 C6.09 44.869 6.082 51.229 6.072 57.59 C6.061 64.83 6.055 72.071 6.05 79.312 C6.04 94.208 6.022 109.104 6 124 C2.637 125.802 0.418 126.238 -3.422 126.195 C-4.447 126.189 -5.471 126.182 -6.527 126.176 C-8.123 126.151 -8.123 126.151 -9.75 126.125 C-10.829 126.116 -11.908 126.107 -13.02 126.098 C-15.68 126.074 -18.34 126.041 -21 126 C-21.326 125.385 -21.652 124.77 -21.988 124.137 C-22.425 123.328 -22.862 122.52 -23.312 121.688 C-23.956 120.487 -23.956 120.487 -24.613 119.262 C-25.913 117.143 -27.218 115.705 -29 114 C-31 111 -31 111 -31.855 108.238 C-33.176 104.503 -35.114 101.446 -37.312 98.188 C-40.237 93.747 -43.026 89.316 -45.543 84.629 C-48.123 79.973 -50.878 75.418 -53.611 70.85 C-54.033 70.139 -54.456 69.428 -54.891 68.695 C-55.273 68.056 -55.654 67.417 -56.048 66.758 C-57 65 -57 65 -58 62 C-58.99 61.505 -58.99 61.505 -60 61 C-60.934 59.344 -61.801 57.65 -62.625 55.938 C-63.071 55.018 -63.517 54.099 -63.977 53.152 C-64.314 52.442 -64.652 51.732 -65 51 C-65.597 53.763 -66 56.16 -66 59 C-66 81 -66 103 -66 125 C-69.081 126.541 -72.31 126.143 -75.688 126.125 C-76.383 126.129 -77.078 126.133 -77.795 126.137 C-82.295 126.128 -86.56 125.733 -91 125 C-93.115 119.796 -93.279 115.293 -93.243 109.776 C-93.246 108.411 -93.246 108.411 -93.249 107.019 C-93.252 104.016 -93.242 101.014 -93.23 98.012 C-93.229 95.909 -93.229 93.806 -93.229 91.703 C-93.228 87.29 -93.219 82.878 -93.206 78.466 C-93.189 72.856 -93.185 67.246 -93.186 61.636 C-93.185 57.287 -93.18 52.938 -93.173 48.589 C-93.17 46.523 -93.168 44.458 -93.167 42.393 C-93.159 32.684 -93.102 22.983 -92.615 13.284 C-92.569 12.235 -92.569 12.235 -92.523 11.164 C-92.314 7.623 -92.02 6.03 -90 3 C-87.484 2.83 -85.19 2.811 -82.688 2.938 C-77.814 3.124 -74.432 3.055 -70 1 C-67.812 1.062 -67.812 1.062 -66 2 C-65.328 3.665 -64.663 5.332 -64 7 C-62.597 9.528 -61.167 12.018 -59.688 14.5 C-59.045 15.585 -59.045 15.585 -58.389 16.692 C-54.648 22.974 -50.835 29.212 -47 35.438 C-46.436 36.355 -45.873 37.273 -45.292 38.219 C-44.762 39.073 -44.233 39.928 -43.688 40.809 C-43.217 41.57 -42.746 42.331 -42.262 43.115 C-41.005 45.124 -41.005 45.124 -39 47 C-38.321 48.317 -37.682 49.654 -37.062 51 C-34.467 56.316 -31.461 61.334 -28.375 66.375 C-27.896 67.16 -27.416 67.945 -26.922 68.754 C-24.742 72.291 -22.529 75.7 -20 79 C-20.167 77.71 -20.335 76.421 -20.508 75.092 C-21.048 70.2 -21.041 65.436 -20.879 60.523 C-20.855 59.643 -20.832 58.762 -20.807 57.854 C-20.757 55.987 -20.704 54.119 -20.648 52.252 C-20.505 47.43 -20.381 42.608 -20.258 37.785 C-20.233 36.845 -20.209 35.905 -20.184 34.937 C-19.925 24.624 -19.897 14.316 -20 4 C-18 3 -18 3 -15.062 3.062 C-11.824 3.271 -11.824 3.271 -9 1 C-8.01 1.99 -7.02 2.98 -6 4 C-5.67 3.01 -5.34 2.02 -5 1 C-5 1.99 -5 2.98 -5 4 C-4.546 3.526 -4.092 3.051 -3.625 2.562 C-2 1 -2 1 0 0 Z M-72 3 C-68 4 -68 4 -68 4 Z M-2 3 C-2 3.33 -2 3.66 -2 4 C-0.02 4 1.96 4 4 4 C4 3.67 4 3.34 4 3 C2.02 3 0.04 3 -2 3 Z " fill="#E7ECFE" transform="translate(730,266)"/>
<path d="M0 0 C5.111 3.415 7.432 9.076 9.858 14.535 C11.78 18.684 14.201 22.528 16.562 26.438 C17.378 27.852 18.192 29.267 19.004 30.684 C20.787 33.948 20.787 33.948 23 36 C24.197 38.024 25.358 40.07 26.5 42.125 C29.987 48.336 33.683 54.391 37.498 60.405 C40.117 64.543 42.599 68.732 45 73 C45.33 72.67 45.66 72.34 46 72 C45.91 70.537 45.753 69.078 45.562 67.625 C45.046 63.611 44.966 60.23 45.562 56.188 C46.04 52.597 45.8 49.557 45 46 C45.66 46 46.32 46 47 46 C46.835 45.051 46.67 44.102 46.5 43.125 C45.929 39.21 45.981 35.386 46.035 31.438 C46.003 29.225 45.796 27.18 45.438 25 C45 22 45 22 46 20 C46.098 17.156 46.139 14.343 46.125 11.5 C46.129 10.715 46.133 9.93 46.137 9.121 C46.238 4.927 46.238 4.927 45 1 C52.46 -2.73 61.66 0 70 0 C70 38.61 70 77.22 70 117 C61.42 117 52.84 117 44 117 C40.856 113.856 38.788 110.547 36.562 106.75 C36.18 106.107 35.798 105.464 35.404 104.801 C33.509 101.602 31.655 98.386 29.84 95.141 C28.191 92.022 28.191 92.022 26 90 C25.34 90 24.68 90 24 90 C23.773 88.853 23.546 87.705 23.312 86.523 C22.086 81.624 19.751 77.814 17 73.625 C16.549 72.915 16.098 72.204 15.633 71.473 C13.881 68.742 12.301 66.301 10 64 C9.01 64.495 9.01 64.495 8 65 C8.232 64.457 8.464 63.915 8.703 63.355 C9.144 59.861 7.653 58.209 5.75 55.312 C5.126 54.339 4.502 53.366 3.859 52.363 C2.15 49.744 2.15 49.744 -1 49 C-0.67 48.01 -0.34 47.02 0 46 C-0.495 45.505 -0.495 45.505 -1 45 C-1.33 68.76 -1.66 92.52 -2 117 C-7.61 117 -13.22 117 -19 117 C-19.495 117.99 -19.495 117.99 -20 119 C-20 118.34 -20 117.68 -20 117 C-21.32 117 -22.64 117 -24 117 C-24.495 117.99 -24.495 117.99 -25 119 C-27 116 -27 116 -27 112 C-26.34 112 -25.68 112 -25 112 C-25 110.35 -25 108.7 -25 107 C-25.66 107 -26.32 107 -27 107 C-26.928 106.432 -26.856 105.863 -26.781 105.277 C-25.599 96.556 -25.599 96.556 -27 88 C-26.062 85.312 -26.062 85.312 -25 83 C-25.66 83 -26.32 83 -27 83 C-26.842 82.474 -26.683 81.949 -26.52 81.407 C-25.873 78.41 -25.781 75.549 -25.684 72.484 C-25.642 71.234 -25.6 69.984 -25.557 68.695 C-25.517 67.393 -25.478 66.091 -25.438 64.75 C-25.394 63.433 -25.351 62.115 -25.307 60.758 C-25.201 57.505 -25.099 54.253 -25 51 C-25.66 51 -26.32 51 -27 51 C-26.67 50.01 -26.34 49.02 -26 48 C-25.929 46.128 -25.916 44.253 -25.938 42.379 C-25.945 41.279 -25.953 40.178 -25.961 39.045 C-25.987 36.727 -26.013 34.41 -26.039 32.092 C-26.047 30.99 -26.055 29.889 -26.062 28.754 C-26.074 27.747 -26.086 26.739 -26.098 25.701 C-26.01 23.289 -25.696 21.3 -25 19 C-25.66 18.67 -26.32 18.34 -27 18 C-26.34 18 -25.68 18 -25 18 C-25.141 15.541 -25.287 13.083 -25.438 10.625 C-25.477 9.926 -25.516 9.228 -25.557 8.508 C-25.743 4.626 -25.743 4.626 -27 1 C-19.741 -2.63 -7.653 -1.971 0 0 Z " fill="#EBEEFD" transform="translate(643,109)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.093 1.918 3.186 1.835 4.312 1.75 C15.006 1.806 25.61 7.983 33.258 15.102 C39.934 22.286 48 32.748 48 43 C48.66 43.33 49.32 43.66 50 44 C51.329 66.296 51.695 85.584 39 105 C39.33 105.66 39.66 106.32 40 107 C38.886 107.65 38.886 107.65 37.75 108.312 C33.999 110.614 31.461 112.309 29 116 C27.199 117.031 27.199 117.031 25 118 C23.772 118.603 23.772 118.603 22.52 119.219 C21.626 119.642 20.733 120.064 19.812 120.5 C18.932 120.923 18.052 121.346 17.145 121.781 C9.379 124.791 2.023 125.294 -6.25 125.188 C-7.217 125.187 -8.184 125.186 -9.18 125.186 C-16.354 125.141 -16.354 125.141 -19.624 124.427 C-22.137 123.78 -22.137 123.78 -25 125 C-27.688 124.062 -27.688 124.062 -30 123 C-30 122.34 -30 121.68 -30 121 C-31.65 120.67 -33.3 120.34 -35 120 C-35.33 119.01 -35.66 118.02 -36 117 C-37.398 116.035 -38.803 115.077 -40.246 114.18 C-42.832 112.44 -45.083 110.361 -47.387 108.27 C-49.09 106.782 -49.09 106.782 -52 106 C-52.062 105.051 -52.124 104.103 -52.188 103.125 C-52.654 99.76 -53.784 97.242 -55.375 94.25 C-58.154 89.005 -59.588 83.735 -61 78 C-61.33 77.01 -61.66 76.02 -62 75 C-62.227 70.746 -62.228 66.49 -62.24 62.231 C-62.25 60.139 -62.281 58.049 -62.312 55.957 C-62.319 54.62 -62.324 53.283 -62.328 51.945 C-62.337 50.734 -62.347 49.522 -62.356 48.274 C-61.967 44.7 -61.027 42.92 -59 40 C-58.086 37.967 -57.191 35.924 -56.312 33.875 C-50.557 21.361 -42.123 10.632 -29.309 4.805 C-19.842 1.505 -9.957 0.48 0 0 Z M-27.309 32.496 C-32.081 38.628 -34.38 45.214 -34 53 C-34.33 53.66 -34.66 54.32 -35 55 C-35.739 68.623 -35.281 81.519 -27 93 C-22.038 98.425 -16.431 101.619 -9.098 102.434 C-7.753 102.457 -6.407 102.458 -5.062 102.438 C-4.36 102.431 -3.657 102.424 -2.932 102.417 C4.367 102.191 8.41 100.296 13.75 95.562 C14.864 94.294 14.864 94.294 16 93 C16.66 92.268 17.32 91.536 18 90.781 C25.438 80.438 25.213 64.186 24 52 C22.517 43.727 19.654 37.238 14 31 C13.01 31.33 12.02 31.66 11 32 C10.505 30.02 10.505 30.02 10 28 C8.68 28.33 7.36 28.66 6 29 C6 28.01 6 27.02 6 26 C-5.074 20.765 -19.241 23.273 -27.309 32.496 Z M-61 45 C-60 49 -60 49 -60 49 Z " fill="#E5EAFE" transform="translate(446,267)"/>
<path d="M0 0 C3.979 -0.059 7.958 -0.094 11.938 -0.125 C13.054 -0.142 14.171 -0.159 15.322 -0.176 C21.89 -0.214 27.653 0.291 34 2 C34.33 1.34 34.66 0.68 35 0 C35.495 0.99 35.495 0.99 36 2 C37.497 2.642 39.024 3.215 40.562 3.75 C50.681 7.697 57.049 14.826 64 23 C64.826 23.971 64.826 23.971 65.668 24.961 C67.347 27.531 68.113 30.066 69 33 C69.512 34.433 69.512 34.433 70.035 35.895 C71.927 41.268 73.44 46.267 73.824 51.965 C73.957 54.062 73.957 54.062 74.535 56.311 C76.634 68.45 72.387 84.146 67 95 C66.585 95.847 66.17 96.694 65.742 97.566 C62.897 102.997 59.704 108.027 54.688 111.688 C53.852 112.337 53.852 112.337 53 113 C53 113.99 53 114.98 53 116 C51.68 116 50.36 116 49 116 C46.945 116.927 44.925 117.936 42.938 119 C37.319 122 37.319 122 34 122 C33.67 122.66 33.34 123.32 33 124 C28.667 124.029 24.333 124.047 20 124.062 C18.779 124.071 17.559 124.079 16.301 124.088 C10.1 124.105 4.127 123.973 -2 123 C-2 122.34 -2 121.68 -2 121 C-2.99 121.33 -3.98 121.66 -5 122 C-5 121.34 -5 120.68 -5 120 C-5.727 119.732 -6.454 119.464 -7.203 119.188 C-19.734 113.867 -28.329 103.681 -33.711 91.469 C-36.79 83.182 -38.563 75.674 -38.875 66.812 C-38.912 65.806 -38.949 64.799 -38.987 63.762 C-39.411 45.778 -34.063 28.643 -22 15 C-15.647 8.853 -7.812 4.972 0 1 C0 0.67 0 0.34 0 0 Z M-3.312 31.188 C-11.64 41.844 -12.778 55.988 -12 69 C-11.67 69.33 -11.34 69.66 -11 70 C-10.866 71.498 -10.769 72.999 -10.688 74.5 C-9.878 83.1 -6.349 90.124 0 96 C4.809 99.803 9.895 101.062 16 101 C16.99 100.505 16.99 100.505 18 100 C18.949 100.268 19.898 100.536 20.875 100.812 C26.878 101.769 32.168 98.185 37 95 C43.129 89.614 45.31 81.763 47 74 C47.137 71.278 47.137 71.278 47 69 C47 66.022 47.627 62.938 48 60 C46.68 59.67 45.36 59.34 44 59 C44.99 58.67 45.98 58.34 47 58 C47.756 48.171 44.659 39.325 38.562 31.621 C36.831 29.825 35.079 28.379 33 27 C32.34 27 31.68 27 31 27 C30.505 26.01 30.505 26.01 30 25 C18.909 19.142 5.001 22.525 -3.312 31.188 Z " fill="#E6EBFE" transform="translate(549,268)"/>
<path d="M0 0 C1.803 0.005 1.803 0.005 3.643 0.01 C4.888 0.018 6.134 0.027 7.418 0.035 C8.683 0.04 9.948 0.044 11.252 0.049 C14.37 0.061 17.488 0.077 20.605 0.098 C21.1 1.088 21.1 1.088 21.605 2.098 C23.45 3.014 23.45 3.014 25.668 3.848 C36.945 8.719 45.526 17.546 50.707 28.656 C52.003 32.177 52.398 35.36 52.605 39.098 C47.229 39.947 42.952 40.1 37.605 39.098 C37.605 39.758 37.605 40.418 37.605 41.098 C36.698 40.891 35.79 40.685 34.855 40.473 C31.495 40.085 30.365 40.294 27.605 42.098 C27.317 41.128 27.028 40.159 26.73 39.16 C25.963 35.991 25.963 35.991 23.605 35.098 C22.646 33.613 22.646 33.613 21.668 32.098 C19.883 28.855 19.883 28.855 16.605 28.098 C15.615 27.108 15.615 27.108 14.605 26.098 C5.738 23.392 -0.786 23.851 -9.395 27.098 C-10.849 27.562 -10.849 27.562 -12.332 28.035 C-14.949 29.383 -15.274 30.409 -16.395 33.098 C-17.354 34.397 -17.354 34.397 -18.332 35.723 C-22.955 43.287 -23.899 51.122 -23.77 59.848 C-23.764 60.8 -23.758 61.752 -23.752 62.733 C-23.61 70.482 -22.536 77.628 -20.395 85.098 C-19.735 85.098 -19.075 85.098 -18.395 85.098 C-18.044 85.799 -17.693 86.5 -17.332 87.223 C-14.329 91.679 -9.583 95.016 -4.461 96.754 C3.43 98.067 12.367 97.785 19.605 94.098 C20.1 92.613 20.1 92.613 20.605 91.098 C20.935 90.768 21.265 90.438 21.605 90.098 C21.275 89.438 20.945 88.778 20.605 88.098 C21.925 88.098 23.245 88.098 24.605 88.098 C25.595 85.128 26.585 82.158 27.605 79.098 C26.329 79.037 25.053 78.976 23.738 78.914 C22.069 78.83 20.4 78.745 18.73 78.66 C17.889 78.621 17.047 78.582 16.18 78.541 C15.374 78.499 14.568 78.457 13.738 78.414 C12.995 78.377 12.251 78.341 11.485 78.303 C9.605 78.098 9.605 78.098 7.605 77.098 C7.605 73.138 7.605 69.178 7.605 65.098 C6.615 64.603 6.615 64.603 5.605 64.098 C6.265 64.098 6.925 64.098 7.605 64.098 C7.585 62.798 7.564 61.499 7.543 60.16 C7.521 58.806 7.534 57.45 7.605 56.098 C9.232 54.472 11.062 54.932 13.309 54.892 C14.31 54.872 15.311 54.852 16.342 54.832 C17.431 54.815 18.519 54.798 19.641 54.781 C20.749 54.76 21.857 54.74 22.999 54.718 C26.556 54.653 30.112 54.594 33.668 54.535 C36.072 54.492 38.477 54.448 40.881 54.404 C46.789 54.297 52.697 54.195 58.605 54.098 C58.605 54.758 58.605 55.418 58.605 56.098 C57.615 56.758 56.625 57.418 55.605 58.098 C55.956 58.655 56.307 59.211 56.668 59.785 C57.837 62.669 57.459 64.15 56.605 67.098 C56.275 67.758 55.945 68.418 55.605 69.098 C55.235 71.702 54.903 74.297 54.605 76.91 C54.092 81.342 53.565 85.735 52.605 90.098 C51.945 90.098 51.285 90.098 50.605 90.098 C50.327 91.273 50.327 91.273 50.043 92.473 C46.948 100.277 41.871 106.36 35.402 111.625 C33.578 113.065 33.578 113.065 31.988 114.754 C30.247 116.446 28.965 116.612 26.605 117.098 C24.931 117.745 23.262 118.407 21.605 119.098 C21.605 119.758 21.605 120.418 21.605 121.098 C15.157 122.417 9.118 121.793 2.605 121.098 C3.265 122.418 3.925 123.738 4.605 125.098 C3.615 124.768 2.625 124.438 1.605 124.098 C1.275 123.438 0.945 122.778 0.605 122.098 C-1.127 122.19 -1.127 122.19 -2.895 122.285 C-7.596 122.285 -11.118 120.902 -15.395 119.098 C-16.055 118.881 -16.715 118.665 -17.395 118.441 C-22.146 116.882 -25.656 114.65 -29.535 111.5 C-31.346 110.036 -31.346 110.036 -33.449 109.125 C-41.317 104.97 -46.274 92.209 -48.77 84.16 C-49.34 81.365 -49.567 78.939 -49.395 76.098 C-50.055 75.768 -50.715 75.438 -51.395 75.098 C-51.671 64.67 -50.97 54.626 -49.665 44.286 C-49.205 40.561 -48.803 36.829 -48.395 33.098 C-47.735 33.098 -47.075 33.098 -46.395 33.098 C-46.126 32.492 -45.858 31.886 -45.582 31.262 C-41.022 21.513 -35.357 14.072 -26.395 8.098 C-26.395 7.438 -26.395 6.778 -26.395 6.098 C-25.673 5.974 -24.951 5.85 -24.207 5.723 C-21.418 5.103 -19.02 4.213 -16.395 3.098 C-10.829 0.732 -6.052 -0.029 0 0 Z " fill="#E6ECFE" transform="translate(779.39453125,106.90234375)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.114 2.248 3.228 2.495 4.375 2.75 C16.221 5.505 26.219 13.415 32.812 23.438 C43.373 41.16 45.432 62.851 40.438 82.938 C38.335 89.952 35.143 95.985 31 102 C29.917 103.578 29.917 103.578 28.812 105.188 C20.639 113.361 9.91 121.548 -2.141 122.098 C-3.185 122.08 -3.185 122.08 -4.25 122.062 C-6.106 122.032 -6.106 122.032 -8 122 C-8.495 123.485 -8.495 123.485 -9 125 C-9.526 124.687 -10.052 124.374 -10.594 124.051 C-13.584 122.745 -16.255 122.52 -19.5 122.188 C-29.317 120.89 -37.606 117.109 -46 112 C-48.118 110.882 -48.118 110.882 -51 110 C-51.361 108.674 -51.689 107.339 -52 106 C-53.034 104.466 -54.124 102.968 -55.25 101.5 C-58.648 96.797 -60.87 92.062 -62.957 86.664 C-64.34 83.131 -65.893 80.161 -68 77 C-67.638 75.148 -67.638 75.148 -67 73 C-66.888 71.213 -66.823 69.423 -66.789 67.633 C-66.763 66.588 -66.736 65.543 -66.709 64.467 C-66.681 63.364 -66.654 62.261 -66.625 61.125 C-65.993 41.125 -61.673 26.385 -47 12 C-41.133 7 -41.133 7 -37 7 C-37 6.34 -37 5.68 -37 5 C-25.562 0.694 -14.138 -0.112 -2 1 C-1.01 1.495 -1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z M-34 34 C-41.799 45.815 -41.671 61.611 -39.679 75.187 C-38.308 81.467 -35.655 86.59 -31 91 C-30.34 91 -29.68 91 -29 91 C-28.814 91.598 -28.629 92.196 -28.438 92.812 C-26.042 96.457 -22.172 97.328 -18.133 98.578 C-15.468 99.105 -12.964 99.095 -10.25 99.062 C-9.265 99.053 -8.28 99.044 -7.266 99.035 C-6.518 99.024 -5.77 99.012 -5 99 C-5 98.01 -5 97.02 -5 96 C-4.67 96.66 -4.34 97.32 -4 98 C0.486 96.748 3.593 95.206 7 92 C7 91.34 7 90.68 7 90 C7.99 89.67 8.98 89.34 10 89 C17.508 78.766 17.225 63.145 15.613 51.113 C15.128 48.807 15.128 48.807 13 47 C13.495 45.515 13.495 45.515 14 44 C12.074 38.135 8.133 31.827 2.75 28.688 C2.173 28.461 1.595 28.234 1 28 C0.505 27.505 0.505 27.505 0 27 C-12.546 21.804 -24.889 23.433 -34 34 Z " fill="#E6ECFD" transform="translate(373,106)"/>
<path d="M0 0 C0.495 1.485 0.495 1.485 1 3 C2.042 2.965 3.083 2.93 4.156 2.895 C14.542 2.659 23.852 3.807 34 6 C35.998 6.345 37.997 6.683 40 7 C40 7.66 40 8.32 40 9 C40.804 9.227 41.609 9.454 42.438 9.688 C46.913 11.336 50.887 13.605 55 16 C55 16.66 55 17.32 55 18 C55.949 18.082 56.898 18.165 57.875 18.25 C58.906 18.497 59.938 18.745 61 19 C61.697 20.318 62.361 21.653 63 23 C64.521 24.48 64.521 24.48 66.277 25.785 C70.079 28.928 72.81 32.366 75.562 36.438 C76.033 37.125 76.503 37.813 76.988 38.522 C82.055 46.194 85.101 54.304 88 63 C90.015 63.733 90.015 63.733 92 64 C91.67 64.66 91.34 65.32 91 66 C90.01 65.67 89.02 65.34 88 65 C88.161 65.727 88.322 66.453 88.487 67.202 C91.688 84.673 88.49 103.131 81 119 C80.67 119.75 80.34 120.5 80 121.273 C77.731 126.208 74.705 130.048 71 134 C70.67 133.34 70.34 132.68 70 132 C69.031 132.309 68.061 132.619 67.062 132.938 C58.837 135.26 50.421 135.132 41.938 135.062 C40.881 135.058 39.825 135.053 38.736 135.049 C36.157 135.037 33.579 135.021 31 135 C31 133 31 133 33.188 130.625 C36 128 36 128 38.688 126 C41.27 123.766 41.866 122.16 43 119 C43.48 118.545 43.959 118.09 44.453 117.621 C46.286 115.701 46.841 114.148 47.625 111.625 C47.865 110.875 48.105 110.125 48.352 109.352 C49.848 103.924 50.193 98.917 50.188 93.312 C50.186 92.116 50.186 92.116 50.185 90.895 C50.126 85.224 50.053 79.361 48 74 C47.34 73.67 46.68 73.34 46 73 C45.278 71.356 44.606 69.689 44 68 C42.02 68.99 42.02 68.99 40 70 C38 68 38 68 37.875 64.875 C37.916 63.926 37.957 62.977 38 62 C38.66 62 39.32 62 40 62 C37.826 59.5 36.27 58.564 33 58 C32.874 57.095 32.874 57.095 32.746 56.172 C31.776 53.349 30.606 52.74 28.062 51.25 C27.352 50.822 26.642 50.394 25.91 49.953 C24.058 48.851 24.058 48.851 22 49 C22 48.01 22 47.02 22 46 C20.254 45.467 18.503 44.949 16.75 44.438 C15.775 44.147 14.801 43.857 13.797 43.559 C11.038 43.008 9.609 43.062 7 44 C7.33 42.68 7.66 41.36 8 40 C7.074 39.818 7.074 39.818 6.129 39.633 C4.859 39.381 4.859 39.381 3.562 39.125 C2.642 38.943 1.722 38.761 0.773 38.574 C-1.607 38.081 -3.954 37.521 -6.312 36.938 C-20.92 33.731 -36.285 35.093 -51 37 C-44.864 23.441 -32.373 13.718 -18.751 8.416 C-15.519 7.214 -12.27 6.095 -9 5 C-7.915 4.636 -6.829 4.273 -5.711 3.898 C-4.369 3.454 -4.369 3.454 -3 3 C-2.01 2.67 -1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DCE3FC" transform="translate(498,641)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.33 7 0.66 7 1 C8.016 1.098 9.032 1.196 10.078 1.297 C21.917 2.538 33.485 3.894 42 13 C42.799 13.804 42.799 13.804 43.613 14.625 C49.679 21.127 52.361 28.21 53 37 C50.045 37.834 47.299 38.179 44.234 38.316 C43.345 38.358 42.455 38.4 41.539 38.443 C40.619 38.483 39.698 38.522 38.75 38.562 C37.346 38.627 37.346 38.627 35.914 38.693 C33.61 38.799 31.305 38.901 29 39 C28.668 38.433 28.336 37.865 27.994 37.281 C27.553 36.546 27.111 35.812 26.656 35.055 C26.221 34.322 25.786 33.59 25.338 32.835 C24.097 30.768 24.097 30.768 21.5 30.125 C20.262 30.063 20.262 30.063 19 30 C17.321 29.366 15.65 28.707 14 28 C14.33 27.34 14.66 26.68 15 26 C8.413 24.954 3.408 25.167 -3 27 C-2.34 27.33 -1.68 27.66 -1 28 C-1.33 28.66 -1.66 29.32 -2 30 C-3.98 30 -5.96 30 -8 30 C-8.66 33.63 -9.32 37.26 -10 41 C-9.34 41.33 -8.68 41.66 -8 42 C-7.34 42.99 -6.68 43.98 -6 45 C-3.437 45.73 -3.437 45.73 -1 46 C-1 46.66 -1 47.32 -1 48 C3.531 48.934 3.531 48.934 8 48 C8 48.99 8 49.98 8 51 C10.31 51 12.62 51 15 51 C15 51.66 15 52.32 15 53 C15.855 53.061 16.709 53.121 17.59 53.184 C18.695 53.267 19.799 53.351 20.938 53.438 C22.04 53.519 23.142 53.6 24.277 53.684 C27 54 27 54 28 55 C28.99 55 29.98 55 31 55 C30.505 55.99 30.505 55.99 30 57 C30.557 57.147 31.114 57.294 31.688 57.445 C40.726 60.102 46.838 64.584 51.812 72.641 C56.503 81.959 55.536 91.985 53.102 101.812 C50.034 110.688 42.895 116.866 34.773 121.184 C20.768 127.753 2.344 128.662 -12.5 124.312 C-17.623 122.375 -22.321 119.825 -27 117 C-28.063 116.536 -28.063 116.536 -29.148 116.062 C-32.208 114.307 -32.739 111.181 -34 108 C-34.715 107.149 -34.715 107.149 -35.445 106.281 C-37.659 103.033 -38.394 99.663 -39.312 95.875 C-39.604 94.783 -39.604 94.783 -39.9 93.668 C-40.365 91.8 -40.693 89.9 -41 88 C-39 86 -39 86 -36.294 85.773 C-35.174 85.783 -34.053 85.794 -32.898 85.805 C-31.687 85.811 -30.475 85.818 -29.227 85.824 C-27.956 85.841 -26.685 85.858 -25.375 85.875 C-24.096 85.884 -22.817 85.893 -21.5 85.902 C-18.333 85.926 -15.167 85.959 -12 86 C-11.505 86.99 -11.505 86.99 -11 88 C-11.66 88 -12.32 88 -13 88 C-12.656 90.473 -12.656 90.473 -12 93 C-11.34 93.33 -10.68 93.66 -10 94 C-10 95.667 -10 97.333 -10 99 C-8.564 99.506 -7.126 100.004 -5.688 100.5 C-4.887 100.778 -4.086 101.057 -3.262 101.344 C-0.912 102.139 -0.912 102.139 2 102 C4.674 102.286 7.334 102.65 10 103 C10 102.67 10 102.34 10 102 C10.603 102.012 11.207 102.023 11.828 102.035 C12.627 102.044 13.427 102.053 14.25 102.062 C15.039 102.074 15.828 102.086 16.641 102.098 C18.907 102.004 20.829 101.632 23 101 C23 99.68 23 98.36 23 97 C23.66 97 24.32 97 25 97 C25 96.34 25 95.68 25 95 C24.34 94.67 23.68 94.34 23 94 C24.65 94.33 26.3 94.66 28 95 C28.324 89.496 28.533 85.563 25 81 C24.01 81.495 24.01 81.495 23 82 C23 81.01 23 80.02 23 79 C20.69 78.67 18.38 78.34 16 78 C16 77.34 16 76.68 16 76 C14.742 75.773 13.484 75.546 12.188 75.312 C5.967 74.09 -0.124 72.399 -6.222 70.677 C-10.601 69.478 -14.412 68.719 -19 69 C-18.67 68.34 -18.34 67.68 -18 67 C-20.376 65.391 -20.376 65.391 -23 64 C-23.99 64.495 -23.99 64.495 -25 65 C-25.392 64.299 -25.784 63.597 -26.188 62.875 C-27.94 59.897 -27.94 59.897 -30.5 57.312 C-33.383 53.986 -34.326 50.376 -35.422 46.172 C-35.928 43.975 -35.928 43.975 -37 42 C-36.415 31.117 -32.172 20.468 -24.688 12.457 C-18.378 7.01 -9.762 3.251 -1.438 2.812 C-0.633 2.874 0.171 2.936 1 3 C0.505 1.515 0.505 1.515 0 0 Z M-30 113 C-29 115 -29 115 -29 115 Z " fill="#E3E9FD" transform="translate(321,266)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C1.99 1.67 2.98 1.34 4 1 C4 1.66 4 2.32 4 3 C4.615 3.133 5.23 3.266 5.863 3.402 C7.076 3.667 7.076 3.667 8.312 3.938 C9.113 4.112 9.914 4.286 10.738 4.465 C13 5 13 5 16 6 C16 6.66 16 7.32 16 8 C16.58 8.266 17.16 8.531 17.758 8.805 C27.938 14.232 33.876 23.373 37.238 34.07 C38.009 37.034 38.546 39.973 39 43 C37.72 42.76 37.72 42.76 36.414 42.516 C33.027 42.004 29.799 41.798 26.375 41.75 C25.269 41.724 24.163 41.698 23.023 41.672 C19.723 42.03 18.401 42.775 16 45 C16 44.01 16 43.02 16 42 C15.402 41.959 14.804 41.917 14.188 41.875 C12 41 12 41 10.25 37.938 C9.837 36.968 9.425 35.999 9 35 C8.01 35 7.02 35 6 35 C5.505 32.03 5.505 32.03 5 29 C3.005 28.319 1.004 27.656 -1 27 C-2.114 26.629 -3.228 26.257 -4.375 25.875 C-12.677 23.871 -18.492 25.177 -26 29 C-33.449 33.779 -36.631 41.415 -38.616 49.812 C-39.295 53.684 -39.288 57.516 -39.25 61.438 C-39.258 62.232 -39.265 63.027 -39.273 63.846 C-39.25 70.958 -38.439 78.064 -34.938 84.375 C-33.707 86.631 -32.822 88.534 -32 91 C-30.201 92.067 -30.201 92.067 -28 93 C-27.237 93.495 -26.474 93.99 -25.688 94.5 C-19.711 97.836 -13.722 98.56 -7 98 C-6.01 97.67 -5.02 97.34 -4 97 C-3.237 96.897 -2.474 96.794 -1.688 96.688 C3.17 95.445 6.227 93.009 9.25 89.062 C11.227 85.767 11.227 85.767 13 81 C21.58 80.67 30.16 80.34 39 80 C39 89.026 34.545 98.982 29 106 C28.34 106 27.68 106 27 106 C26.763 106.588 26.526 107.176 26.281 107.781 C21.606 115.877 10.378 119.207 2 122 C-0.865 122.265 -3.73 122.444 -6.602 122.621 C-7.393 122.746 -8.185 122.871 -9 123 C-9.33 123.66 -9.66 124.32 -10 125 C-10.33 124.01 -10.66 123.02 -11 122 C-11.907 122.227 -12.815 122.454 -13.75 122.688 C-23.874 124.096 -35.873 118.703 -43.91 112.898 C-46.32 110.98 -48.681 109.027 -51 107 C-51 107.99 -51 108.98 -51 110 C-52.485 109.505 -52.485 109.505 -54 109 C-54.103 107.907 -54.206 106.814 -54.312 105.688 C-54.973 102.144 -55.203 101.068 -58 99 C-58.99 99.495 -58.99 99.495 -60 100 C-61.562 97.625 -61.562 97.625 -63 95 C-62.505 94.01 -62.505 94.01 -62 93 C-61.34 93.33 -60.68 93.66 -60 94 C-60.617 92.351 -61.243 90.706 -61.875 89.062 C-62.223 88.146 -62.571 87.229 -62.93 86.285 C-63.782 83.851 -63.782 83.851 -66 83 C-65.67 82.01 -65.34 81.02 -65 80 C-65.136 77.862 -65.136 77.862 -65.559 75.543 C-65.697 74.677 -65.836 73.812 -65.979 72.92 C-66.206 71.567 -66.206 71.567 -66.438 70.188 C-68.224 59.415 -68.224 59.415 -68 54 C-67.34 54 -66.68 54 -66 54 C-66.021 53.031 -66.041 52.061 -66.062 51.062 C-66.072 46.667 -65.553 42.356 -65 38 C-64.34 38 -63.68 38 -63 38 C-62.889 37.285 -62.778 36.569 -62.664 35.832 C-59.959 24.296 -51.34 15.016 -41.762 8.605 C-34.195 4.207 -26.348 1.025 -17.578 0.684 C-16.553 0.642 -15.529 0.6 -14.473 0.557 C-13.409 0.517 -12.346 0.478 -11.25 0.438 C-9.632 0.373 -9.632 0.373 -7.98 0.307 C-5.32 0.201 -2.66 0.099 0 0 Z M6 31 C7 33 7 33 7 33 Z " fill="#E4EAFE" transform="translate(255,106)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C3.218 3.179 3.218 3.179 6.688 4.125 C13.729 6.422 20.337 9.796 27 13 C27 13.99 27 14.98 27 16 C26.117 16.245 25.234 16.49 24.324 16.742 C13.9 19.989 6.031 24.595 0.023 34.141 C-3.766 41.432 -5.139 47.097 -5.062 55.312 C-5.053 56.567 -5.044 57.821 -5.035 59.113 C-5.024 60.066 -5.012 61.019 -5 62 C-4.34 62 -3.68 62 -3 62 C-0.857 68.429 -0.857 68.429 -1.625 71.5 C-2.136 74.909 -0.919 76.562 0.797 79.449 C2.096 81.293 2.096 81.293 5 83 C5 83.66 5 84.32 5 85 C10.318 90.983 16.26 95.735 24 98 C24.33 98.66 24.66 99.32 25 100 C31.601 102.371 38.057 103.351 45.039 102.91 C47 103 47 103 49 105 C51.106 105.319 51.106 105.319 53.438 105.25 C54.22 105.255 55.002 105.26 55.809 105.266 C58.29 105.132 58.29 105.132 61 103 C61.33 103.66 61.66 104.32 62 105 C62.66 105 63.32 105 64 105 C64 105.66 64 106.32 64 107 C64.99 106.67 65.98 106.34 67 106 C67 106.99 67 107.98 67 109 C66.01 109 65.02 109 64 109 C64 109.66 64 110.32 64 111 C48.381 118.318 31.515 124.53 14 121 C13.505 122.485 13.505 122.485 13 124 C12.67 123.01 12.34 122.02 12 121 C9.541 120.259 7.195 119.692 4.688 119.188 C-4.812 117.062 -12.703 113.038 -21 108 C-21.66 108.99 -22.32 109.98 -23 111 C-23.33 110.01 -23.66 109.02 -24 108 C-25.381 104.76 -27.062 102.775 -29.625 100.375 C-42.923 87.615 -50.473 70.304 -51.068 51.884 C-51.153 47.253 -51.107 42.63 -51 38 C-51.66 38 -52.32 38 -53 38 C-52.67 37.01 -52.34 36.02 -52 35 C-51.67 35 -51.34 35 -51 35 C-50.795 33.952 -50.59 32.904 -50.379 31.824 C-50.107 30.445 -49.835 29.066 -49.562 27.688 C-49.36 26.652 -49.36 26.652 -49.154 25.596 C-48.081 20.178 -46.633 16.144 -43.75 11.438 C-43.167 10.467 -42.585 9.496 -41.984 8.496 C-38.868 4.577 -35.684 4.032 -30.918 3.367 C-29.342 3.193 -27.765 3.03 -26.188 2.875 C-24.945 2.748 -24.945 2.748 -23.677 2.618 C-15.769 1.868 -7.937 1.803 0 2 C0 1.34 0 0.68 0 0 Z M-4 68 C-3 70 -3 70 -3 70 Z " fill="#DEE4FC" transform="translate(488,681)"/>
<path d="M0 0 C16.458 3.267 16.458 3.267 22.312 9.438 C23.976 11.963 25.179 14.09 26 17 C25.67 18.32 25.34 19.64 25 21 C21.7 21.33 18.4 21.66 15 22 C14.567 20.886 14.134 19.772 13.688 18.625 C12.301 14.825 12.301 14.825 9 13 C4.114 12.358 -0.445 12.081 -4.75 14.625 C-6.414 17.787 -5.922 19.62 -5 23 C-3.565 23.364 -2.127 23.716 -0.688 24.062 C0.113 24.26 0.914 24.457 1.738 24.66 C4.16 25.199 4.16 25.199 7 24 C7 24.99 7 25.98 7 27 C11.29 27.99 15.58 28.98 20 30 C20.33 29.34 20.66 28.68 21 28 C21.082 28.701 21.165 29.402 21.25 30.125 C22.173 33.665 23.936 35.997 26 39 C26.716 40.646 27.403 42.307 28 44 C29.892 42.108 29.838 38.849 30.312 36.312 C31.418 30.943 32.448 27.165 36 23 C35.505 22.01 35.505 22.01 35 21 C36.094 20.805 36.094 20.805 37.211 20.605 C39.815 20.04 42.131 19.234 44.625 18.312 C51.381 16.242 57.398 17.479 63.812 20.188 C65.888 21.907 67.238 23.567 68.875 25.688 C71.371 28.781 71.632 28.958 75.875 29.438 C76.906 29.293 77.938 29.149 79 29 C79.041 28.443 79.082 27.886 79.125 27.312 C80.927 22.549 85.597 20.12 90 18 C90.33 17.34 90.66 16.68 91 16 C94.3 16 97.6 16 101 16 C101 16.66 101 17.32 101 18 C102.516 18.062 102.516 18.062 104.062 18.125 C108.827 18.819 111.926 21.403 115 25 C118.587 30.264 119.69 34.045 119.562 40.375 C119.552 41.494 119.552 41.494 119.54 42.635 C119.293 49.606 117.225 54.469 112.516 59.625 C107.391 64.274 101.159 64.442 94.574 64.789 C92.014 64.838 92.014 64.838 90 66 C89.34 65.67 88.68 65.34 88 65 C87.67 64.01 87.34 63.02 87 62 C85.339 61.319 83.672 60.653 82 60 C81.34 59.01 80.68 58.02 80 57 C79.34 57 78.68 57 78 57 C77.856 56.113 77.711 55.226 77.562 54.312 C77.377 53.219 77.191 52.126 77 51 C76.814 49.67 76.814 49.67 76.625 48.312 C75.924 45.718 75.047 44.691 73 43 C73.103 43.804 73.206 44.609 73.312 45.438 C72.782 51.488 69.809 56.236 65.5 60.438 C61.938 62.664 59.232 64 55 64 C55.33 64.99 55.66 65.98 56 67 C55.34 67 54.68 67 54 67 C54 66.01 54 65.02 54 64 C53.074 64.104 53.074 64.104 52.129 64.211 C45.809 64.615 41.995 63.583 36.91 59.781 C32.824 55.971 30.891 51.199 29 46 C28.67 47.65 28.34 49.3 28 51 C27.34 51 26.68 51 26 51 C25.608 51.887 25.216 52.774 24.812 53.688 C22.891 57.199 21.07 59.442 18 62 C15.25 62.688 15.25 62.688 13 63 C12.67 63.66 12.34 64.32 12 65 C6.662 66.394 2.089 66.218 -3 64 C-3 64.66 -3 65.32 -3 66 C-5 65 -5 65 -8 63 C-9.176 62.546 -10.351 62.092 -11.562 61.625 C-15 60 -15 60 -16.25 58.125 C-16.497 57.424 -16.745 56.722 -17 56 C-17.536 55.154 -18.072 54.309 -18.625 53.438 C-20 51 -20 51 -20 48 C-20.66 47.67 -21.32 47.34 -22 47 C-21.34 47 -20.68 47 -20 47 C-20 46.34 -20 45.68 -20 45 C-15.05 44.505 -15.05 44.505 -10 44 C-9.546 44.949 -9.092 45.898 -8.625 46.875 C-7.198 50.003 -7.198 50.003 -5 52 C0.606 53.178 6.459 53.501 12 52 C14.551 49.005 14.172 45.795 14 42 C12.68 41.34 11.36 40.68 10 40 C9.67 40.66 9.34 41.32 9 42 C7.68 41.67 6.36 41.34 5 41 C5 40.34 5 39.68 5 39 C2.398 37.994 -0.206 36.995 -2.812 36 C-3.919 35.571 -3.919 35.571 -5.049 35.133 C-5.761 34.862 -6.473 34.591 -7.207 34.312 C-8.189 33.935 -8.189 33.935 -9.191 33.551 C-11.163 32.854 -11.163 32.854 -14 33 C-17.489 25.819 -18.545 19.959 -18 12 C-17.34 12 -16.68 12 -16 12 C-15.794 11.443 -15.587 10.886 -15.375 10.312 C-13.481 7.127 -11.188 4.844 -8 3 C-5.266 2.322 -2.835 2.147 0 2 C0 1.34 0 0.68 0 0 Z M92 17 C96 18 96 18 96 18 Z M44 29 C39.962 34.291 40.253 40.646 41 47 C42.157 50.782 42.157 50.782 45 53 C49.574 54.331 54.451 54.516 59 53 C59.66 52.34 60.32 51.68 61 51 C61 50.01 61 49.02 61 48 C60.34 47.67 59.68 47.34 59 47 C60.32 46.34 61.64 45.68 63 45 C63.446 38.75 62.792 34.146 59 29 C54.161 26.022 48.889 26.169 44 29 Z M95 27 C95 27.33 95 27.66 95 28 C96.98 28 98.96 28 101 28 C101 27.67 101 27.34 101 27 C99.02 27 97.04 27 95 27 Z M89.312 30.312 C87.781 32.951 87.781 32.951 88 36 C87.67 36 87.34 36 87 36 C87 38.97 87 41.94 87 45 C87.99 45 88.98 45 90 45 C89.34 45.66 88.68 46.32 88 47 C87.941 49.021 87.941 49.021 89 51 C91.636 52.873 93.93 53.977 97 55 C97 54.34 97 53.68 97 53 C98.114 53.309 98.114 53.309 99.25 53.625 C102.391 54.053 103.459 53.84 106 52 C108.019 48.798 108.297 46.189 108.312 42.438 C108.329 41.487 108.346 40.537 108.363 39.559 C107.942 36.588 107.256 35.871 105 34 C103 30.526 103 30.526 103 28 C95.602 27.167 95.602 27.167 89.312 30.312 Z M104 29 C104.33 29.99 104.66 30.98 105 32 C105 31.01 105 30.02 105 29 C104.67 29 104.34 29 104 29 Z M76 30 C75.34 30.66 74.68 31.32 74 32 C73.67 32 73.34 32 73 32 C73 34.31 73 36.62 73 39 C75.647 36.353 76.599 33.438 78 30 C77.34 30 76.68 30 76 30 Z M76 34 C77 36 77 36 77 36 Z M106 34 C106 34.66 106 35.32 106 36 C106.66 35.67 107.32 35.34 108 35 C107.34 34.67 106.68 34.34 106 34 Z " fill="#E0E6FD" transform="translate(594,467)"/>
<path d="M0 0 C1.075 -0.001 2.15 -0.003 3.258 -0.004 C4.39 -0 5.521 0.004 6.688 0.008 C7.819 0.004 8.951 0 10.117 -0.004 C11.192 -0.003 12.267 -0.001 13.375 0 C14.866 0.002 14.866 0.002 16.387 0.003 C18.688 0.133 18.688 0.133 19.688 1.133 C19.785 2.623 19.814 4.118 19.814 5.612 C19.817 6.58 19.82 7.547 19.823 8.544 C19.821 9.614 19.819 10.685 19.817 11.787 C19.819 12.907 19.82 14.028 19.822 15.182 C19.827 18.905 19.824 22.628 19.82 26.352 C19.821 28.926 19.822 31.501 19.823 34.075 C19.825 39.479 19.823 44.883 19.818 50.287 C19.812 56.542 19.814 62.798 19.82 69.054 C19.825 75.061 19.824 81.069 19.821 87.076 C19.821 89.637 19.821 92.199 19.824 94.76 C19.826 98.333 19.822 101.906 19.817 105.479 C19.819 106.549 19.821 107.619 19.823 108.721 C19.819 110.173 19.819 110.173 19.814 111.653 C19.814 112.499 19.814 113.345 19.813 114.217 C19.688 116.133 19.688 116.133 18.688 117.133 C16.836 117.291 14.978 117.384 13.121 117.449 C11.996 117.491 10.872 117.533 9.713 117.576 C8.529 117.615 7.345 117.655 6.125 117.695 C4.937 117.738 3.749 117.782 2.525 117.826 C-0.42 117.933 -3.366 118.035 -6.312 118.133 C-6.312 116.483 -6.312 114.833 -6.312 113.133 C-6.972 112.803 -7.633 112.473 -8.312 112.133 C-7.962 111.597 -7.611 111.06 -7.25 110.508 C-6.01 107.366 -6.748 105.456 -7.312 102.133 C-7.2 100.757 -7.043 99.384 -6.854 98.016 C-6.273 93.217 -6.185 88.511 -6.215 83.682 C-6.216 82.785 -6.218 81.888 -6.219 80.963 C-6.223 79.033 -6.229 77.104 -6.237 75.174 C-6.25 72.123 -6.257 69.073 -6.261 66.022 C-6.273 59.559 -6.292 53.096 -6.312 46.633 C-6.336 39.115 -6.356 31.596 -6.368 24.078 C-6.375 21.066 -6.387 18.054 -6.4 15.042 C-6.404 13.211 -6.407 11.38 -6.41 9.549 C-6.415 8.706 -6.42 7.864 -6.426 6.996 C-6.426 5.041 -6.375 3.086 -6.312 1.133 C-4.597 -0.583 -2.339 0.003 0 0 Z " fill="#F0F3FE" transform="translate(579.3125,108.8671875)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.846 2.665 2.691 2.33 3.562 1.984 C12.582 -1.254 12.582 -1.254 16.504 -0.551 C22.011 2.093 25.191 5.735 27.25 11.5 C27.893 14.498 28.089 16.962 28 20 C28.33 20.33 28.66 20.66 29 21 C29.072 22.853 29.084 24.708 29.062 26.562 C29.053 27.574 29.044 28.586 29.035 29.629 C29.024 30.411 29.012 31.194 29 32 C29.99 32.495 29.99 32.495 31 33 C31.649 28.826 32.09 24.716 32.375 20.5 C33.003 13.446 35.045 8.255 40 3 C44.369 0.39 48.98 0.016 54 0 C55.547 0.557 55.547 0.557 57.125 1.125 C60.84 2.256 62.495 1.312 65.875 -0.371 C68.78 -1.231 71.058 -0.553 74 0 C74.025 1.257 74.05 2.513 74.076 3.808 C74.17 8.465 74.27 13.122 74.372 17.78 C74.416 19.796 74.457 21.813 74.497 23.829 C74.555 26.726 74.619 29.623 74.684 32.52 C74.7 33.423 74.717 34.326 74.734 35.257 C74.764 36.518 74.764 36.518 74.795 37.804 C74.818 38.913 74.818 38.913 74.842 40.045 C74.939 42.215 74.939 42.215 76 45 C75.34 45 74.68 45 74 45 C73.67 47.64 73.34 50.28 73 53 C72.34 53 71.68 53 71 53 C70.773 53.577 70.546 54.155 70.312 54.75 C68.139 58.476 65.226 60.416 61.375 62.25 C53.479 64.005 46.236 63.681 39 60 C35.65 56.738 34.064 53.544 33 49 C33.375 46.625 33.375 46.625 34 45 C35.32 45 36.64 45 38 45 C38 45.66 38 46.32 38 47 C40.771 45.294 40.771 45.294 42 44 C41.258 43.443 40.515 42.886 39.75 42.312 C37 40 37 40 34.938 37.25 C34.298 36.508 33.659 35.765 33 35 C31.68 35 30.36 35 29 35 C29 38.63 29 42.26 29 46 C26.03 46 23.06 46 20 46 C19.34 47.32 18.68 48.64 18 50 C17.67 50 17.34 50 17 50 C17.012 49.241 17.023 48.481 17.035 47.699 C17.044 46.705 17.053 45.711 17.062 44.688 C17.074 43.701 17.086 42.715 17.098 41.699 C17.123 38.9 17.123 38.9 16 36 C16.66 36 17.32 36 18 36 C17.4 30.719 17.4 30.719 16.438 25.5 C15.82 21.972 15.939 18.58 16 15 C15.01 14.67 14.02 14.34 13 14 C12.67 13.01 12.34 12.02 12 11 C9.69 11 7.38 11 5 11 C4.67 11.66 4.34 12.32 4 13 C3.34 13.66 2.68 14.32 2 15 C1.497 17.525 1.497 17.525 1.312 20.375 C1.226 21.331 1.14 22.288 1.051 23.273 C1.004 25.785 1.301 27.613 2 30 C2 31.667 2 33.333 2 35 C1.34 35 0.68 35 0 35 C0 37.31 0 39.62 0 42 C0.66 42.33 1.32 42.66 2 43 C1.34 43 0.68 43 0 43 C0 44.65 0 46.3 0 48 C-3.375 48.625 -3.375 48.625 -7 49 C-11 45 -11 45 -11.243 42.134 C-11.239 40.928 -11.235 39.723 -11.23 38.48 C-11.23 37.826 -11.23 37.172 -11.229 36.497 C-11.226 35.104 -11.218 33.711 -11.206 32.319 C-11.188 30.234 -11.185 28.149 -11.186 26.064 C-11.163 17.675 -10.779 9.357 -10 1 C-6.135 -0.702 -4.116 -1.176 0 0 Z M44 15 C42.341 20.129 42.443 25.671 43.688 30.875 C45.448 33.726 46.912 34.696 50 36 C53.789 36.406 56.271 36.292 59.812 34.875 C63.1 32.057 63.746 29.274 64.219 25.043 C64.436 20.3 63.646 17.063 61 13 C58.764 10.764 57.653 10.065 54.5 9.75 C49.556 10.244 47.279 11.043 44 15 Z M61 42 C60.67 42.66 60.34 43.32 60 44 C61.32 43.34 62.64 42.68 64 42 C63.01 42 62.02 42 61 42 Z M44 45 C46.565 47.565 48.477 47.54 52 48 C51.34 48.66 50.68 49.32 50 50 C50.99 51.485 50.99 51.485 52 53 C56.75 53.564 56.75 53.564 60.938 51.625 C62.182 50.045 62.182 50.045 62 48 C61.34 48 60.68 48 60 48 C59.67 47.01 59.34 46.02 59 45 C54.05 45 49.1 45 44 45 Z M62 45 C63 48 63 48 63 48 Z M44 48 C45.485 48.99 45.485 48.99 47 50 C46.67 50.66 46.34 51.32 46 52 C46.99 52.33 47.98 52.66 49 53 C49.33 51.35 49.66 49.7 50 48 C48.02 48 46.04 48 44 48 Z " fill="#E4EAFD" transform="translate(475,484)"/>
<path d="M0 0 C2.125 2.688 2.125 2.688 4.188 5.875 C6.074 8.613 7.059 9.593 10.125 10.688 C10.455 10.028 10.785 9.367 11.125 8.688 C11.702 8.873 12.28 9.059 12.875 9.25 C15.332 9.728 16.769 9.445 19.125 8.688 C20.38 14.69 20.156 20.519 19.998 26.611 C19.929 31.728 20.461 35.84 22.125 40.688 C22.847 40.502 23.569 40.316 24.312 40.125 C27.125 39.688 27.125 39.688 32.125 40.688 C32.455 33.428 32.785 26.168 33.125 18.688 C32.465 18.688 31.805 18.688 31.125 18.688 C31.455 18.028 31.785 17.367 32.125 16.688 C31.795 16.357 31.465 16.028 31.125 15.688 C31.785 13.378 32.445 11.067 33.125 8.688 C36.425 8.688 39.725 8.688 43.125 8.688 C43.125 22.548 43.125 36.408 43.125 50.688 C41.475 51.018 39.825 51.347 38.125 51.688 C37.63 52.678 37.63 52.678 37.125 53.688 C36.465 53.688 35.805 53.688 35.125 53.688 C35.125 53.028 35.125 52.367 35.125 51.688 C33.64 51.192 33.64 51.192 32.125 50.688 C31.795 50.357 31.465 50.028 31.125 49.688 C30.135 50.183 30.135 50.183 29.125 50.688 C24.793 52.131 20.472 52.829 16.277 50.789 C14.625 49.688 14.625 49.688 12.125 47.688 C11.135 48.183 11.135 48.183 10.125 48.688 C10.125 46.378 10.125 44.067 10.125 41.688 C9.465 41.688 8.805 41.688 8.125 41.688 C7.353 36.28 7.027 31.149 7.125 25.688 C7.785 25.688 8.445 25.688 9.125 25.688 C9.125 21.727 9.125 17.768 9.125 13.688 C7.805 13.028 6.485 12.367 5.125 11.688 C4.135 12.347 3.145 13.008 2.125 13.688 C2.62 14.678 2.62 14.678 3.125 15.688 C1.805 16.018 0.485 16.347 -0.875 16.688 C-0.875 16.028 -0.875 15.367 -0.875 14.688 C-1.865 14.357 -2.855 14.028 -3.875 13.688 C-4.925 11.713 -5.925 9.712 -6.875 7.688 C-9.477 5.086 -10.699 5.389 -14.312 5.375 C-15.281 5.358 -16.249 5.341 -17.246 5.324 C-19.899 5.47 -19.899 5.47 -21.68 7.07 C-23.046 8.919 -23.487 10.437 -23.875 12.688 C-18.778 15.236 -13.797 17.016 -8.312 18.562 C-3.012 20.173 0.499 21.457 4.125 25.688 C4.875 28.938 4.875 28.938 5.125 31.688 C6.115 32.183 6.115 32.183 7.125 32.688 C5.041 44.783 5.041 44.783 1.125 48.688 C-4.667 51.621 -11.136 52.09 -17.527 52.477 C-20.027 52.61 -20.027 52.61 -22.875 53.688 C-23.535 53.028 -24.195 52.367 -24.875 51.688 C-27.625 49.72 -27.625 49.72 -29.891 48.832 C-32.41 47.379 -33.242 45.914 -34.625 43.375 C-35.053 42.616 -35.481 41.856 -35.922 41.074 C-36.974 38.439 -37.054 36.499 -36.875 33.688 C-35.273 33.465 -33.668 33.26 -32.062 33.062 C-31.169 32.946 -30.276 32.83 -29.355 32.711 C-26.875 32.688 -26.875 32.688 -23.875 34.688 C-23.215 34.688 -22.555 34.688 -21.875 34.688 C-21.215 35.018 -20.555 35.347 -19.875 35.688 C-20.205 36.678 -20.535 37.668 -20.875 38.688 C-19.063 40.931 -19.063 40.931 -16.492 40.883 C-15.135 40.848 -15.135 40.848 -13.75 40.812 C-12.84 40.794 -11.93 40.776 -10.992 40.758 C-10.294 40.735 -9.595 40.711 -8.875 40.688 C-8.875 40.028 -8.875 39.367 -8.875 38.688 C-7.885 38.688 -6.895 38.688 -5.875 38.688 C-5.875 36.378 -5.875 34.067 -5.875 31.688 C-13.61 27.997 -13.61 27.997 -21.875 28.688 C-21.875 28.028 -21.875 27.367 -21.875 26.688 C-25.761 24.253 -25.761 24.253 -30.125 24.125 C-30.702 24.311 -31.28 24.496 -31.875 24.688 C-32.535 24.028 -33.195 23.367 -33.875 22.688 C-33.581 21.345 -33.247 20.011 -32.875 18.688 C-33.205 18.028 -33.535 17.367 -33.875 16.688 C-34.528 9.708 -33.942 4.592 -29.5 -1.062 C-21.06 -7.555 -8.025 -6.455 0 0 Z M37.125 9.688 C41.125 10.688 41.125 10.688 41.125 10.688 Z M-31.875 19.688 C-31.545 20.678 -31.215 21.668 -30.875 22.688 C-30.875 21.697 -30.875 20.707 -30.875 19.688 C-31.205 19.688 -31.535 19.688 -31.875 19.688 Z " fill="#C0C8F3" transform="translate(307.875,887.3125)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C2.98 3 4.96 3 7 3 C7.956 6.998 8.104 10.708 8.062 14.812 C8.053 15.974 8.044 17.135 8.035 18.332 C8.024 19.212 8.012 20.093 8 21 C9.013 20.441 9.013 20.441 10.047 19.871 C10.939 19.398 11.831 18.925 12.75 18.438 C13.632 17.962 14.513 17.486 15.422 16.996 C18.285 15.89 19.2 15.918 22 17 C23.155 17.413 24.31 17.825 25.5 18.25 C30.154 20.577 32.18 22.591 33.871 27.559 C35.222 32.321 36.336 37.09 37 42 C37.495 41.01 37.495 41.01 38 40 C39.98 40.66 41.96 41.32 44 42 C42.96 39.759 41.94 37.926 40.375 36 C39 34 39 34 39 30 C38.34 29.67 37.68 29.34 37 29 C37.66 29 38.32 29 39 29 C39.093 27.67 39.093 27.67 39.188 26.312 C40.184 22.251 41.978 20.79 45 18 C45 17.34 45 16.68 45 16 C51 16 57 16 63 16 C63 16.66 63 17.32 63 18 C64.145 18.619 64.145 18.619 65.312 19.25 C68.593 21.386 69.499 23.446 71 27 C71.99 26.505 71.99 26.505 73 26 C73 26.99 73 27.98 73 29 C70 31 70 31 68 32 C68 31.34 68 30.68 68 30 C67.34 30.33 66.68 30.66 66 31 C64 31.04 62 31.043 60 31 C60 30.34 60 29.68 60 29 C56.38 27.684 53.904 27 50 27 C50 28.32 50 29.64 50 31 C54.376 31.593 58.575 32.138 63 32 C63 32.66 63 33.32 63 34 C65.97 35.65 68.94 37.3 72 39 C72.487 48.008 72.487 48.008 70.312 52.438 C67.204 55.882 64.903 57.873 60.188 58.188 C59.105 58.095 59.105 58.095 58 58 C58 58.66 58 59.32 58 60 C45.065 58.598 45.065 58.598 40 53 C38.812 50.812 38.812 50.812 38 49 C37.01 48.34 36.02 47.68 35 47 C35.66 46.67 36.32 46.34 37 46 C36.01 45.34 35.02 44.68 34 44 C34.064 44.739 34.129 45.477 34.195 46.238 C33.854 51.058 30.474 53.89 27 57 C23.247 58.766 21.173 59 17 59 C16.67 59.66 16.34 60.32 16 61 C15.67 60.34 15.34 59.68 15 59 C12.678 58.593 10.343 58.256 8 58 C8.33 58.66 8.66 59.32 9 60 C7.515 60.495 7.515 60.495 6 61 C5.505 59.515 5.505 59.515 5 58 C4.67 59.32 4.34 60.64 4 62 C1.625 61.312 1.625 61.312 -1 60 C-2.312 57.375 -2.312 57.375 -3 55 C-3.33 55 -3.66 55 -4 55 C-4 50.333 -4 45.667 -4 41 C-4.084 40.064 -4.168 39.128 -4.254 38.164 C-4.356 36.996 -4.458 35.828 -4.562 34.625 C-4.667 33.462 -4.771 32.3 -4.879 31.102 C-5 28 -5 28 -4 25 C-4.66 24.67 -5.32 24.34 -6 24 C-5.34 24 -4.68 24 -4 24 C-4.021 23.415 -4.042 22.83 -4.063 22.228 C-4.147 19.569 -4.199 16.91 -4.25 14.25 C-4.284 13.33 -4.317 12.409 -4.352 11.461 C-4.451 4.591 -4.451 4.591 -1.984 1.359 C-1.33 0.911 -0.675 0.462 0 0 Z M-3 6 C-2 9 -2 9 -2 9 Z M-3 16 C-3 18.64 -3 21.28 -3 24 C-2.67 24 -2.34 24 -2 24 C-2 21.36 -2 18.72 -2 16 C-2.33 16 -2.66 16 -3 16 Z M10 27.938 C7.477 31.801 7.836 33.802 8.605 38.207 C8.736 38.799 8.866 39.39 9 40 C8.01 40.495 8.01 40.495 7 41 C8.284 44.497 8.284 44.497 10 48 C10.99 48.33 11.98 48.66 13 49 C13.66 48.67 14.32 48.34 15 48 C15 48.66 15 49.32 15 50 C18.306 49.449 21.2 48.9 24 47 C25.145 43.757 25.103 40.662 25.062 37.25 C25.053 36.265 25.044 35.28 25.035 34.266 C25.024 33.518 25.012 32.77 25 32 C24.01 32 23.02 32 22 32 C22.33 31.01 22.66 30.02 23 29 C21.944 27.354 21.944 27.354 20 26 C15.682 25.36 13.692 25.553 10 27.938 Z M-3 29 C-2 32 -2 32 -2 32 Z M49 41 C48.67 43.31 48.34 45.62 48 48 C50.906 49.257 52.796 50 56 50 C56.66 50 57.32 50 58 50 C58.33 47.69 58.66 45.38 59 43 C58.01 43 57.02 43 56 43 C55.299 42.814 54.597 42.629 53.875 42.438 C53.256 42.293 52.637 42.149 52 42 C51.67 42.33 51.34 42.66 51 43 C50.34 42.34 49.68 41.68 49 41 Z M41 43 C45 44 45 44 45 44 Z M47 44 C48 46 48 46 48 46 Z M60 44 C60.66 44.66 61.32 45.32 62 46 C62 45.34 62 44.68 62 44 C61.34 44 60.68 44 60 44 Z " fill="#CFD6F9" transform="translate(359,880)"/>
<path d="M0 0 C0.808 0.102 1.616 0.204 2.449 0.309 C3.064 0.392 3.679 0.476 4.312 0.562 C3.653 1.222 2.992 1.883 2.312 2.562 C1.982 2.562 1.653 2.562 1.312 2.562 C1.312 8.173 1.312 13.783 1.312 19.562 C1.972 19.232 2.633 18.903 3.312 18.562 C2.982 18.232 2.653 17.903 2.312 17.562 C2.272 15.23 2.27 12.895 2.312 10.562 C2.643 10.562 2.972 10.562 3.312 10.562 C3.312 12.212 3.312 13.862 3.312 15.562 C4.221 15.475 4.221 15.475 5.148 15.387 C12.252 15.004 17.493 16.392 23.312 20.562 C28.025 26.194 28.397 32.358 28.312 39.562 C27.733 45.416 26.209 50.034 22.312 54.562 C20.048 56.098 17.751 57.298 15.312 58.562 C14.653 59.222 13.992 59.883 13.312 60.562 C13.312 59.903 13.312 59.242 13.312 58.562 C11.663 58.562 10.013 58.562 8.312 58.562 C8.312 58.893 8.312 59.222 8.312 59.562 C2.562 59.688 2.562 59.688 0.312 58.562 C-1.737 58.533 -3.765 58.629 -5.812 58.723 C-7.688 58.562 -7.688 58.562 -9.688 56.562 C-9.883 53.742 -9.883 53.742 -9.812 50.438 C-9.794 49.342 -9.776 48.246 -9.758 47.117 C-9.735 46.274 -9.711 45.431 -9.688 44.562 C-10.348 44.232 -11.007 43.903 -11.688 43.562 C-12.017 44.553 -12.348 45.543 -12.688 46.562 C-12.688 44.253 -12.688 41.942 -12.688 39.562 C-13.017 45.832 -13.348 52.102 -13.688 58.562 C-16.658 58.232 -19.627 57.903 -22.688 57.562 C-23.956 55.025 -23.77 53.312 -23.715 50.477 C-23.703 49.463 -23.69 48.45 -23.678 47.406 C-23.643 45.268 -23.604 43.13 -23.561 40.992 C-23.55 39.976 -23.539 38.961 -23.527 37.914 C-23.511 36.984 -23.494 36.054 -23.476 35.096 C-23.7 32.411 -24.467 30.923 -25.688 28.562 C-25.388 24.365 -24.651 21.244 -22.688 17.562 C-21.697 15.11 -21.697 15.11 -21.688 12.562 C-23.521 10.228 -23.521 10.228 -25.688 8.562 C-21.561 2.737 -21.561 2.737 -17.562 1.938 C-14.08 2.695 -13.583 3.674 -11.688 6.562 C-10.697 6.562 -9.707 6.562 -8.688 6.562 C-8.357 4.582 -8.028 2.602 -7.688 0.562 C-4.593 -0.469 -3.161 -0.412 0 0 Z M-10.688 7.562 C-10.688 8.222 -10.688 8.883 -10.688 9.562 C-11.265 9.831 -11.842 10.099 -12.438 10.375 C-14.72 11.58 -16.661 12.97 -18.688 14.562 C-15.223 16.048 -15.223 16.048 -11.688 17.562 C-12.348 17.562 -13.007 17.562 -13.688 17.562 C-13.688 20.862 -13.688 24.163 -13.688 27.562 C-13.358 27.562 -13.027 27.562 -12.688 27.562 C-12.688 24.923 -12.688 22.283 -12.688 19.562 C-12.027 19.562 -11.368 19.562 -10.688 19.562 C-9.217 15.15 -8.319 12.08 -9.688 7.562 C-10.017 7.562 -10.348 7.562 -10.688 7.562 Z M5.312 16.562 C9.312 17.562 9.312 17.562 9.312 17.562 Z M-11.688 20.562 C-10.688 22.562 -10.688 22.562 -10.688 22.562 Z M-10.688 22.562 C-12.66 24.535 -11.872 28.247 -11.875 30.938 C-11.887 31.623 -11.899 32.309 -11.912 33.016 C-11.923 36.755 -11.814 38.372 -9.688 41.562 C-8.093 38.374 -8.586 34.934 -8.625 31.438 C-8.63 30.683 -8.634 29.929 -8.639 29.152 C-8.65 27.289 -8.668 25.426 -8.688 23.562 C-9.347 23.232 -10.007 22.903 -10.688 22.562 Z M1.312 30.562 C0.893 33.274 0.893 33.274 1.312 35.562 C1.643 34.572 1.972 33.582 2.312 32.562 C2.972 32.562 3.633 32.562 4.312 32.562 C3.322 36.523 3.322 36.523 2.312 40.562 C2.972 40.562 3.633 40.562 4.312 40.562 C4.292 41.326 4.271 42.089 4.25 42.875 C4.183 45.664 4.183 45.664 5.312 48.562 C6.749 48.644 8.187 48.702 9.625 48.75 C10.426 48.785 11.226 48.82 12.051 48.855 C14.371 48.747 14.371 48.747 16.043 47.238 C19.247 43.009 18.772 37.609 18.312 32.562 C16.7 28.876 15.682 27.809 12.312 25.562 C6.827 24.728 4.615 26.191 1.312 30.562 Z M1.312 41.562 C2.312 43.562 2.312 43.562 2.312 43.562 Z M3.312 45.562 C4.312 47.562 4.312 47.562 4.312 47.562 Z M1.312 55.562 C1.643 56.222 1.972 56.883 2.312 57.562 C2.643 56.903 2.972 56.242 3.312 55.562 C2.653 55.562 1.992 55.562 1.312 55.562 Z M4.312 56.562 C5.312 58.562 5.312 58.562 5.312 58.562 Z " fill="#8491DF" transform="translate(521.6875,880.4375)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C3.702 1.876 3.702 1.876 5.438 1.75 C13.588 1.793 19.52 6.026 25.469 11.219 C27.138 13.161 27.605 14.497 28 17 C28.33 17 28.66 17 29 17 C29.66 16.34 30.32 15.68 31 15 C31.625 17.875 31.625 17.875 32 21 C31.34 21.66 30.68 22.32 30 23 C30.24 25.882 30.24 25.882 30.938 29.062 C31.329 31.035 31.693 33.013 32 35 C31.67 35.33 31.34 35.66 31 36 C30.727 37.663 30.482 39.331 30.25 41 C28.817 49.062 25.567 55.696 19 60.777 C11.331 65.466 3.668 65.425 -5 64 C-6.485 63.505 -6.485 63.505 -8 63 C-8.33 63.66 -8.66 64.32 -9 65 C-9 64.01 -9 63.02 -9 62 C-9.701 61.773 -10.402 61.546 -11.125 61.312 C-17.067 58.6 -20.545 55.107 -23.188 49.227 C-27.044 38.657 -27.557 27.166 -23.035 16.785 C-19.661 9.956 -15.137 6.068 -8.375 2.688 C-5.604 1.885 -2.856 1.398 0 1 C0 0.67 0 0.34 0 0 Z M-10.312 16.812 C-14.101 21.723 -14.317 27.128 -14.25 33.125 C-14.258 33.828 -14.265 34.53 -14.273 35.254 C-14.259 39.041 -13.998 41.764 -12 45 C-11.67 45.99 -11.34 46.98 -11 48 C-8.548 50.943 -6.907 52.738 -3.188 53.812 C-2.136 53.874 -1.084 53.936 0 54 C1.299 54.103 2.599 54.206 3.938 54.312 C8.405 54.227 11.612 52.908 15 50 C19.074 45.253 19.405 41.081 19 35 C19 32.895 19.069 30.789 19.188 28.688 C18.881 22.668 16.087 18.328 12 14 C4.561 9.661 -3.851 11.748 -10.312 16.812 Z " fill="#D9E0FB" transform="translate(273,467)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1 1.68 1 1 1 C1 4.63 1 8.26 1 12 C3.296 13.148 5.077 13.308 7.625 13.562 C8.442 13.646 9.26 13.73 10.102 13.816 C10.728 13.877 11.355 13.938 12 14 C11.825 19.328 11.825 19.328 11 22 C8.125 23.875 8.125 23.875 5 25 C4.01 25.66 3.02 26.32 2 27 C1.75 29.59 1.673 31.919 1.75 34.5 C1.758 35.534 1.758 35.534 1.766 36.59 C1.783 40.423 1.783 40.423 3 44 C5.31 44 7.62 44 10 44 C10.495 47.96 10.495 47.96 11 52 C7.453 54.295 4.126 56.013 0 57 C-3.75 54.5 -3.75 54.5 -5 52 C-5.99 51.505 -5.99 51.505 -7 51 C-7.99 50.505 -7.99 50.505 -9 50 C-8.959 49.237 -8.918 48.474 -8.875 47.688 C-8.779 44.773 -8.779 44.773 -11 42 C-11.142 39.233 -11.188 36.573 -11.125 33.812 C-11.116 33.063 -11.107 32.313 -11.098 31.541 C-11.074 29.694 -11.038 27.847 -11 26 C-11.568 26.309 -12.137 26.619 -12.723 26.938 C-16.375 28.641 -19.968 29.458 -24 29 C-25.735 27.059 -27.405 25.057 -29 23 C-33.457 22.385 -35.235 22.49 -39 25 C-38.34 25.66 -37.68 26.32 -37 27 C-37.66 27 -38.32 27 -39 27 C-39.33 28.32 -39.66 29.64 -40 31 C-40.33 30.67 -40.66 30.34 -41 30 C-41.33 31.65 -41.66 33.3 -42 35 C-40.515 34.505 -40.515 34.505 -39 34 C-38.67 36.31 -38.34 38.62 -38 41 C-38.66 41 -39.32 41 -40 41 C-38.251 43.083 -38.251 43.083 -36 45 C-32.105 45.211 -32.105 45.211 -29 44 C-28.216 43.175 -27.433 42.35 -26.625 41.5 C-23.861 38.867 -22.738 38.192 -19 38 C-16.688 38.438 -16.688 38.438 -15 39 C-15.595 43.761 -17.314 48.039 -20 52 C-22.562 53.438 -22.562 53.438 -25 54 C-26.485 54.495 -26.485 54.495 -28 55 C-34.097 55.546 -38.697 55.329 -44 52 C-45.361 51.381 -45.361 51.381 -46.75 50.75 C-49.956 48.256 -50.257 45.92 -51 42 C-51.125 39.062 -51.125 39.062 -51 37 C-51.66 36.67 -52.32 36.34 -53 36 C-52.67 32.7 -52.34 29.4 -52 26 C-51.34 26 -50.68 26 -50 26 C-49.794 25.113 -49.587 24.226 -49.375 23.312 C-47.357 18.451 -44.421 16.087 -39.746 13.988 C-35.717 12.521 -33.164 11.648 -29 13 C-27.762 13.351 -26.525 13.701 -25.25 14.062 C-24.178 14.372 -23.105 14.681 -22 15 C-22 15.66 -22 16.32 -22 17 C-21.361 17.247 -20.721 17.495 -20.062 17.75 C-18 19 -18 19 -17.25 21.125 C-17.168 21.744 -17.085 22.362 -17 23 C-16.34 23 -15.68 23 -15 23 C-15.227 22.092 -15.454 21.185 -15.688 20.25 C-16 17 -16 17 -14.562 14.625 C-14.047 14.089 -13.531 13.553 -13 13 C-12.01 13.33 -11.02 13.66 -10 14 C-10.278 13.196 -10.278 13.196 -10.562 12.375 C-11.136 9.261 -10.843 7.049 -10 4 C-7.746 1.585 -6.623 1.029 -3.312 0.875 C-2.549 0.916 -1.786 0.957 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M-15 18 C-14 21 -14 21 -14 21 Z M-9 23 C-9 24.98 -9 26.96 -9 29 C-8.67 29 -8.34 29 -8 29 C-8 27.02 -8 25.04 -8 23 C-8.33 23 -8.66 23 -9 23 Z M-16 24 C-15.34 24.66 -14.68 25.32 -14 26 C-14 25.34 -14 24.68 -14 24 C-14.66 24 -15.32 24 -16 24 Z M-9 30 C-8 34 -8 34 -8 34 Z M-41 36 C-41 37.65 -41 39.3 -41 41 C-40.67 41 -40.34 41 -40 41 C-40 39.35 -40 37.7 -40 36 C-40.33 36 -40.66 36 -41 36 Z " fill="#DCE1FC" transform="translate(485,884)"/>
<path d="M0 0 C1.671 0.062 1.671 0.062 3.375 0.125 C4.726 2.828 4.495 4.926 4.489 7.952 C4.488 9.173 4.488 10.394 4.488 11.652 C4.483 12.989 4.478 14.327 4.473 15.664 C4.471 17.029 4.469 18.394 4.468 19.758 C4.465 23.352 4.455 26.946 4.444 30.54 C4.433 34.206 4.429 37.873 4.424 41.539 C4.413 48.734 4.396 55.93 4.375 63.125 C1.075 63.125 -2.225 63.125 -5.625 63.125 C-5.955 62.465 -6.285 61.805 -6.625 61.125 C-9.587 61.738 -11.006 62.379 -13.625 64.125 C-16.105 64.539 -16.105 64.539 -18.812 64.75 C-20.164 64.862 -20.164 64.862 -21.543 64.977 C-22.23 65.026 -22.917 65.075 -23.625 65.125 C-23.625 64.465 -23.625 63.805 -23.625 63.125 C-24.202 63.084 -24.78 63.043 -25.375 63 C-28.037 61.965 -29.304 60.622 -30.816 58.215 C-32.247 54.517 -32.225 50.663 -32.352 46.734 C-32.365 43.965 -32.365 43.965 -34.625 42.125 C-34.727 38.765 -34.555 35.452 -34.402 32.094 C-34.513 30.624 -34.513 30.624 -34.625 29.125 C-35.615 28.465 -36.605 27.805 -37.625 27.125 C-37.955 27.785 -38.285 28.445 -38.625 29.125 C-38.625 28.465 -38.625 27.805 -38.625 27.125 C-39.285 27.125 -39.945 27.125 -40.625 27.125 C-40.838 22.657 -40.216 19.302 -38.625 15.125 C-38.295 16.115 -37.965 17.105 -37.625 18.125 C-35.645 18.125 -33.665 18.125 -31.625 18.125 C-32.285 17.795 -32.945 17.465 -33.625 17.125 C-33.625 16.135 -33.625 15.145 -33.625 14.125 C-32.965 14.125 -32.305 14.125 -31.625 14.125 C-31.625 11.155 -31.625 8.185 -31.625 5.125 C-30.305 5.455 -28.985 5.785 -27.625 6.125 C-25.645 5.795 -23.665 5.465 -21.625 5.125 C-21.625 9.415 -21.625 13.705 -21.625 18.125 C-18.325 17.795 -15.025 17.465 -11.625 17.125 C-10.965 18.445 -10.305 19.765 -9.625 21.125 C-8.635 20.465 -7.645 19.805 -6.625 19.125 C-6.811 18.548 -6.996 17.97 -7.188 17.375 C-7.68 14.841 -7.264 13.598 -6.625 11.125 C-6.375 8.801 -6.186 6.478 -6.004 4.148 C-5.272 0.24 -3.8 0.131 0 0 Z M-39.625 22.125 C-38.625 25.125 -38.625 25.125 -38.625 25.125 Z M-13.625 27.125 C-13.625 27.785 -13.625 28.445 -13.625 29.125 C-15.275 29.125 -16.925 29.125 -18.625 29.125 C-18.625 28.465 -18.625 27.805 -18.625 27.125 C-19.615 27.125 -20.605 27.125 -21.625 27.125 C-21.625 35.375 -21.625 43.625 -21.625 52.125 C-17.409 54.233 -15.227 54.302 -10.625 54.125 C-10.295 54.785 -9.965 55.445 -9.625 56.125 C-9.151 55.63 -8.676 55.135 -8.188 54.625 C-6.625 53.125 -6.625 53.125 -5.625 53.125 C-5.596 49.188 -5.578 45.25 -5.562 41.312 C-5.554 40.193 -5.546 39.073 -5.537 37.92 C-5.534 36.847 -5.531 35.774 -5.527 34.668 C-5.522 33.678 -5.517 32.688 -5.511 31.669 C-5.514 29.001 -5.514 29.001 -6.625 26.125 C-9.996 24.664 -10.406 24.979 -13.625 27.125 Z M-32.625 34.125 C-31.625 37.125 -31.625 37.125 -31.625 37.125 Z " fill="#7284D4" transform="translate(437.625,466.875)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C5.324 2.368 6.656 2.712 8 3 C8.66 2.01 9.32 1.02 10 0 C10.33 1.65 10.66 3.3 11 5 C13.757 4.513 16.242 3.911 18.875 2.938 C22.46 1.862 25.276 1.649 29 2 C30.562 3.188 30.562 3.188 32 5 C33.011 5.825 34.021 6.65 35.062 7.5 C38 10 38 10 40 13 C40.99 12.34 41.98 11.68 43 11 C43 12.32 43 13.64 43 15 C42.34 15.33 41.68 15.66 41 16 C41 16.99 41 17.98 41 19 C41.66 19.33 42.32 19.66 43 20 C42.865 29.179 42.707 37.36 37 45 C32.987 48.761 28.742 50.141 23.312 50.062 C22.1 50.049 22.1 50.049 20.863 50.035 C20.248 50.024 19.634 50.012 19 50 C19.33 50.99 19.66 51.98 20 53 C18.02 52.34 16.04 51.68 14 51 C14.33 50.01 14.66 49.02 15 48 C13.35 47.67 11.7 47.34 10 47 C10.084 48.181 10.168 49.362 10.254 50.578 C10.357 52.135 10.46 53.693 10.562 55.25 C10.619 56.027 10.675 56.805 10.732 57.605 C10.781 58.36 10.829 59.114 10.879 59.891 C10.926 60.582 10.973 61.273 11.022 61.985 C10.999 64.136 10.6 65.941 10 68 C7.667 68 5.333 68 3 68 C1.515 68.495 1.515 68.495 0 69 C-0.33 67.02 -0.66 65.04 -1 63 C-1.33 63 -1.66 63 -2 63 C-2 60.36 -2 57.72 -2 55 C-1.67 55 -1.34 55 -1 55 C-1 52.03 -1 49.06 -1 46 C-1.33 46 -1.66 46 -2 46 C-2 43.69 -2 41.38 -2 39 C-1.67 39 -1.34 39 -1 39 C-0.67 26.13 -0.34 13.26 0 0 Z M12 17 C9.576 22.695 9.947 27.873 11.102 33.836 C12.253 36.61 13.403 37.519 16 39 C20.375 40.171 24.492 40.867 28.5 38.562 C32.131 32.359 32.498 27.143 30.816 20.277 C29.55 16.746 28.137 15.046 25 13 C19.645 11.797 15.587 12.675 12 17 Z " fill="#DEE4FE" transform="translate(309,481)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.342 16.988 3.19 33.957 3 51 C3 52.32 3 53.64 3 55 C3 55.66 3 56.32 3 57 C-0.802 57.548 -4.222 57.783 -8 57 C-10.792 54.387 -12.501 51.504 -14 48 C-14 47.34 -14 46.68 -14 46 C-14.66 46 -15.32 46 -16 46 C-17.32 43.36 -18.64 40.72 -20 38 C-20.99 38.495 -20.99 38.495 -22 39 C-22.227 37.824 -22.454 36.649 -22.688 35.438 C-23.83 30.759 -26.03 27.751 -29 24 C-29.986 28.929 -30.151 33.624 -30.125 38.625 C-30.129 39.375 -30.133 40.125 -30.137 40.898 C-30.163 44.998 -30.163 44.998 -29.433 49.009 C-28.73 52.239 -29.996 54.927 -31 58 C-33.97 57.67 -36.94 57.34 -40 57 C-40 39.18 -40 21.36 -40 3 C-37.03 2.34 -34.06 1.68 -31 1 C-30.539 1.837 -30.077 2.673 -29.602 3.535 C-25.854 10.258 -21.953 16.886 -17.812 23.375 C-17.409 24.035 -17.006 24.695 -16.59 25.375 C-15.298 27.447 -15.298 27.447 -13 30 C-10.294 30.556 -10.294 30.556 -8 30 C-8 29.01 -8 28.02 -8 27 C-8.66 26.67 -9.32 26.34 -10 26 C-9.67 25.34 -9.34 24.68 -9 24 C-8.848 22.407 -8.751 20.809 -8.684 19.211 C-8.642 18.27 -8.6 17.329 -8.557 16.359 C-8.517 15.375 -8.478 14.39 -8.438 13.375 C-8.394 12.382 -8.351 11.39 -8.307 10.367 C-8.2 7.912 -8.098 5.456 -8 3 C-5.36 2.67 -2.72 2.34 0 2 C0 1.34 0 0.68 0 0 Z M-12 32 C-11.34 33.32 -10.68 34.64 -10 36 C-9.67 34.68 -9.34 33.36 -9 32 C-9.99 32 -10.98 32 -12 32 Z M-9 36 C-8 38 -8 38 -8 38 Z " fill="#DDE2FC" transform="translate(648,881)"/>
<path d="M0 0 C11.55 0 23.1 0 35 0 C44.791 26.999 44.791 26.999 48.147 37.779 C48.71 39.537 49.348 41.272 50 43 C50.99 43.495 50.99 43.495 52 44 C52.174 46.014 52.346 48.028 52.516 50.043 C53.249 54.518 54.764 58.68 56.273 62.946 C58 67.838 58 67.838 58 70 C58.66 70 59.32 70 60 70 C60 72.31 60 74.62 60 77 C60.66 77 61.32 77 62 77 C61.505 78.98 61.505 78.98 61 81 C58.83 77.745 57.854 74.885 56.637 71.168 C56.408 70.476 56.18 69.783 55.944 69.07 C55.448 67.565 54.954 66.06 54.462 64.554 C53.138 60.509 51.802 56.467 50.465 52.426 C50.186 51.582 49.908 50.739 49.62 49.87 C47.558 43.643 45.45 37.433 43.316 31.23 C43.017 30.359 42.717 29.487 42.408 28.589 C41.232 25.171 40.055 21.754 38.872 18.339 C38.053 15.974 37.237 13.608 36.422 11.242 C36.052 10.182 36.052 10.182 35.676 9.101 C34 4.226 34 4.226 34 2 C23.44 2 12.88 2 2 2 C2 39.29 2 76.58 2 115 C8.93 115 15.86 115 23 115 C23 88.27 23 61.54 23 34 C23.66 34 24.32 34 25 34 C25.262 34.934 25.523 35.867 25.793 36.829 C29.587 50.147 34.068 63.217 38.562 76.312 C39.701 79.641 40.839 82.97 41.976 86.299 C42.677 88.351 43.38 90.402 44.083 92.453 C45.059 95.301 46.03 98.15 47 101 C47.374 102.094 47.749 103.188 48.134 104.315 C48.501 105.397 48.868 106.479 49.246 107.594 C49.582 108.581 49.917 109.568 50.263 110.584 C51 113 51 113 51 115 C57.27 115 63.54 115 70 115 C70.66 112.36 71.32 109.72 72 107 C72.773 104.528 73.573 102.064 74.398 99.609 C74.745 98.573 74.745 98.573 75.099 97.516 C75.855 95.259 76.615 93.005 77.375 90.75 C77.904 89.172 78.433 87.595 78.962 86.017 C80.069 82.714 81.177 79.411 82.286 76.109 C84.852 68.466 87.398 60.815 89.946 53.165 C91.229 49.313 92.513 45.461 93.797 41.609 C94.228 40.317 94.658 39.025 95.089 37.733 C95.39 36.831 95.69 35.929 96 35 C96.33 35 96.66 35 97 35 C97 61.4 97 87.8 97 115 C103.93 115 110.86 115 118 115 C118 77.71 118 40.42 118 2 C107.77 2 97.54 2 87 2 C86.01 5.63 85.02 9.26 84 13 C83.436 14.858 82.859 16.712 82.254 18.556 C81.947 19.498 81.64 20.439 81.323 21.409 C80.84 22.875 80.84 22.875 80.348 24.371 C80.006 25.415 79.665 26.458 79.313 27.533 C78.232 30.836 77.147 34.137 76.062 37.438 C75.339 39.646 74.616 41.855 73.893 44.064 C70.621 54.052 67.33 64.032 64 74 C63.124 70.813 62.991 69.17 64 66 C64.325 64.915 64.65 63.83 64.985 62.711 C65.386 61.414 65.79 60.117 66.195 58.82 C66.414 58.119 66.633 57.419 66.859 56.697 C68.471 51.58 70.176 46.5 71.93 41.43 C72.419 40.005 72.909 38.581 73.398 37.156 C74.142 34.993 74.891 32.831 75.65 30.673 C76.402 28.533 77.136 26.389 77.867 24.242 C78.097 23.602 78.327 22.961 78.564 22.301 C79.952 18.164 80.051 15.227 79 11 C79.99 11.33 80.98 11.66 82 12 C82.597 9.237 83 6.84 83 4 C84.031 1.837 84.031 1.837 86 0 C88.647 -0.357 90.872 -0.468 93.512 -0.391 C94.24 -0.385 94.968 -0.379 95.718 -0.373 C98.042 -0.351 100.364 -0.301 102.688 -0.25 C104.264 -0.23 105.84 -0.212 107.416 -0.195 C111.278 -0.151 115.139 -0.082 119 0 C119.154 14.89 119.302 29.781 119.443 44.672 C119.509 51.586 119.576 58.5 119.648 65.414 C119.718 72.086 119.782 78.758 119.843 85.43 C119.867 87.976 119.893 90.522 119.921 93.068 C119.959 96.633 119.991 100.197 120.022 103.762 C120.035 104.816 120.048 105.87 120.061 106.955 C120.096 111.76 120.025 116.277 119 121 C118.34 119.68 117.68 118.36 117 117 C111.39 117 105.78 117 100 117 C99.67 117.66 99.34 118.32 99 119 C99 118.34 99 117.68 99 117 C98.01 117 97.02 117 96 117 C94.633 114.266 94.837 112.05 94.795 108.99 C94.765 107.098 94.765 107.098 94.734 105.168 C94.717 103.787 94.7 102.405 94.684 101.023 C94.663 99.614 94.642 98.204 94.621 96.795 C94.565 93.083 94.516 89.37 94.468 85.658 C94.418 81.871 94.362 78.084 94.307 74.297 C94.199 66.865 94.098 59.432 94 52 C93.34 52 92.68 52 92 52 C91.773 53.174 91.546 54.349 91.312 55.559 C89.757 62.557 87.319 69.289 85 76.062 C82.408 83.654 79.84 91.184 78 99 C77.349 100.341 76.685 101.676 76 103 C75.623 104.657 75.279 106.323 75 108 C74.34 108 73.68 108 73 108 C72.34 110.97 71.68 113.94 71 117 C64.73 117 58.46 117 52 117 C52.33 117.99 52.66 118.98 53 120 C52.34 120 51.68 120 51 120 C50.845 119.041 50.845 119.041 50.688 118.062 C50.461 117.382 50.234 116.701 50 116 C49.01 115.67 48.02 115.34 47 115 C47.299 114.179 47.299 114.179 47.604 113.342 C48.125 110.259 47.39 108.467 46.332 105.531 C45.939 104.419 45.546 103.307 45.14 102.161 C44.702 100.953 44.264 99.745 43.812 98.5 C42.888 95.903 41.972 93.303 41.055 90.703 C40.811 90.017 40.568 89.33 40.317 88.623 C38.381 83.139 36.524 77.63 34.695 72.109 C34.423 71.287 34.151 70.465 33.87 69.618 C32.789 66.349 31.71 63.079 30.635 59.808 C29.883 57.522 29.127 55.238 28.371 52.953 C28.145 52.263 27.92 51.572 27.687 50.861 C26.8 47.676 26.8 47.676 25 45 C25.002 45.887 25.004 46.775 25.007 47.689 C25.027 56.032 25.042 64.376 25.052 72.72 C25.057 77.01 25.064 81.3 25.075 85.59 C25.086 89.727 25.092 93.863 25.095 98 C25.097 99.582 25.1 101.164 25.106 102.745 C25.113 104.953 25.114 107.161 25.114 109.369 C25.116 110.628 25.118 111.887 25.12 113.184 C25 116 25 116 24 117 C22.292 117.087 20.581 117.107 18.871 117.098 C17.319 117.093 17.319 117.093 15.736 117.088 C14.648 117.08 13.559 117.071 12.438 117.062 C10.799 117.056 10.799 117.056 9.127 117.049 C6.418 117.037 3.709 117.021 1 117 C1 116.34 1 115.68 1 115 C0.34 114.67 -0.32 114.34 -1 114 C-0.475 108.97 0.106 103.981 1 99 C0.34 99 -0.32 99 -1 99 C-1 96.333 -1 93.667 -1 91 C-1.021 89.824 -1.041 88.649 -1.062 87.438 C-1.07 83.216 -0.597 79.176 0 75 C-0.33 75 -0.66 75 -1 75 C-1 67 -1 59 -1 51 C-1.012 49.931 -1.023 48.863 -1.035 47.762 C-1.045 46.404 -1.054 45.046 -1.062 43.688 C-1.071 43.01 -1.079 42.333 -1.088 41.635 C-1.104 38.248 -0.936 35.254 0 32 C-0.144 31.423 -0.289 30.845 -0.438 30.25 C-1.301 26.796 -1.117 23.557 -1 20 C-0.67 19.67 -0.34 19.34 0 19 C0.136 15.337 0.165 12.494 -1 9 C-0.34 9 0.32 9 1 9 C0.67 6.03 0.34 3.06 0 0 Z M94 46 C93.67 47.65 93.34 49.3 93 51 C93.66 50.67 94.32 50.34 95 50 C94.67 48.68 94.34 47.36 94 46 Z M0 83 C1 87 1 87 1 87 Z " fill="#6D82CE" transform="translate(433,109)"/>
<path d="M0 0 C3.932 3.658 6.041 7.433 7.789 12.473 C8.779 12.473 9.769 12.473 10.789 12.473 C11.155 19.906 11.155 19.906 9.539 22.41 C6.325 24.362 3.119 24.6 -0.582 24.605 C-1.404 24.607 -2.226 24.608 -3.072 24.609 C-3.922 24.606 -4.773 24.602 -5.648 24.598 C-6.918 24.603 -6.918 24.603 -8.213 24.609 C-12.34 24.603 -16.186 24.514 -20.211 23.473 C-20.211 26.312 -19.808 28.71 -19.211 31.473 C-18.077 31.617 -16.942 31.761 -15.773 31.91 C-12.211 32.473 -12.211 32.473 -11.211 33.473 C-9 33.045 -7.23 32.482 -5.211 31.473 C-4.902 30.833 -4.592 30.194 -4.273 29.535 C-3.211 27.473 -3.211 27.473 -0.211 25.473 C-0.211 26.463 -0.211 27.453 -0.211 28.473 C0.449 27.153 1.109 25.833 1.789 24.473 C3.769 25.133 5.749 25.793 7.789 26.473 C6.619 32.642 5.307 36.058 0.789 40.473 C-4.133 43.678 -11.388 46.178 -17.328 45.082 C-18.959 44.554 -20.585 44.015 -22.211 43.473 C-21.716 42.978 -21.716 42.978 -21.211 42.473 C-22.118 41.916 -23.026 41.359 -23.961 40.785 C-27.075 38.569 -29.065 36.609 -31.211 33.473 C-31.211 32.813 -31.211 32.153 -31.211 31.473 C-31.871 31.143 -32.531 30.813 -33.211 30.473 C-33.773 28.16 -33.773 28.16 -34.211 25.473 C-34.404 24.885 -34.598 24.297 -34.797 23.691 C-35.913 17.711 -33.647 10.857 -30.578 5.762 C-22.417 -4.183 -11.241 -7.766 0 0 Z M-19.836 8.223 C-20.29 8.965 -20.743 9.708 -21.211 10.473 C-20.881 11.793 -20.551 13.113 -20.211 14.473 C-14.271 14.473 -8.331 14.473 -2.211 14.473 C-4.06 9.186 -4.06 9.186 -8.211 6.473 C-12.667 5.8 -16.024 5.682 -19.836 8.223 Z " fill="#E2E7FE" transform="translate(388.2109375,487.52734375)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C14.348 4.697 14.407 8.255 14.656 13.418 C14.77 14.27 14.883 15.122 15 16 C15.66 16.33 16.32 16.66 17 17 C17.625 20.062 17.625 20.062 18 23 C18.74 20.094 19.417 17.178 20.062 14.25 C20.239 13.451 20.416 12.652 20.598 11.828 C20.73 11.225 20.863 10.622 21 10 C21.66 10 22.32 10 23 10 C23.124 9.092 23.247 8.185 23.375 7.25 C23.921 4.41 24.573 2.474 26 0 C27.98 0 29.96 0 32 0 C32.99 2.31 33.98 4.62 35 7 C35.66 7 36.32 7 37 7 C37.337 9.083 37.67 11.166 38 13.25 C38.186 14.41 38.371 15.57 38.562 16.766 C39 20 39 20 39 24 C39.66 24 40.32 24 41 24 C41.266 22.831 41.531 21.662 41.805 20.457 C42.161 18.909 42.518 17.361 42.875 15.812 C43.049 15.044 43.223 14.275 43.402 13.482 C45.855 2.9 45.855 2.9 48.75 0.625 C51.742 -0.206 54.006 0.32 57 1 C56.825 1.58 56.65 2.161 56.469 2.759 C54.422 9.558 52.423 16.37 50.438 23.188 C49.982 24.749 49.982 24.749 49.518 26.342 C48.935 28.342 48.353 30.344 47.773 32.345 C47.302 33.962 46.826 35.577 46.34 37.19 C45.524 39.925 45 42.123 45 45 C42 44 42 44 41 43 C40.34 43.66 39.68 44.32 39 45 C38.01 44.67 37.02 44.34 36 44 C35.125 42.19 35.125 42.19 34.43 39.852 C34.173 39.011 33.917 38.171 33.652 37.305 C33.396 36.42 33.139 35.536 32.875 34.625 C32.479 33.31 32.479 33.31 32.074 31.969 C31.583 30.337 31.096 28.704 30.617 27.069 C30.11 25.369 29.561 23.682 29 22 C28.67 22.99 28.34 23.98 28 25 C27.774 25.654 27.549 26.307 27.316 26.98 C25.62 31.945 24.207 36.894 23 42 C19.7 42 16.4 42 13 42 C11.541 38.888 10.835 36.141 10.5 32.75 C10.253 30.894 10.253 30.894 10 29 C9.34 28.67 8.68 28.34 8 28 C8.33 27.01 8.66 26.02 9 25 C8.34 24.67 7.68 24.34 7 24 C6.367 22.152 6.367 22.152 5.875 19.938 C5.624 18.837 5.624 18.837 5.367 17.715 C5.246 17.149 5.125 16.583 5 16 C4.34 16.66 3.68 17.32 3 18 C3.206 17.051 3.413 16.102 3.625 15.125 C4.324 10.324 3.504 5.59 2 1 C1.34 0.67 0.68 0.34 0 0 Z M36 11 C37 14 37 14 37 14 Z " fill="#DEE3FD" transform="translate(690,896)"/>
<path d="M0 0 C2.562 1.625 2.562 1.625 4.562 3.625 C4.893 2.305 5.222 0.985 5.562 -0.375 C8.202 -0.375 10.842 -0.375 13.562 -0.375 C13.637 5.281 13.691 10.936 13.727 16.593 C13.742 18.513 13.763 20.433 13.789 22.354 C13.826 25.129 13.842 27.904 13.855 30.68 C13.871 31.525 13.886 32.37 13.902 33.241 C13.904 39.867 13.362 47.444 8.818 52.608 C2.839 57.989 -2.927 58.056 -10.75 57.902 C-15.638 57.535 -17.745 56.814 -21.438 53.625 C-24.438 48.786 -24.438 48.786 -24.438 45.625 C-22.128 45.625 -19.817 45.625 -17.438 45.625 C-17.107 46.615 -16.778 47.605 -16.438 48.625 C-11.954 51.373 -6.521 51.318 -1.438 50.625 C2.119 48.52 3.257 47.541 4.562 43.625 C4.63 42.064 4.648 40.5 4.625 38.938 C4.616 38.129 4.607 37.321 4.598 36.488 C4.586 35.873 4.574 35.259 4.562 34.625 C3.961 35.257 3.961 35.257 3.348 35.902 C-0.575 39.683 -3.657 41.596 -9.164 41.859 C-14.424 41.317 -18.182 38.913 -21.812 35.188 C-26.329 28.065 -26.232 20.789 -25.438 12.625 C-23.93 7.127 -21.718 2.908 -16.688 0 C-10.952 -2.283 -5.701 -2.28 0 0 Z M-15.438 11.625 C-17.097 16.754 -16.994 22.296 -15.75 27.5 C-13.989 30.351 -12.526 31.321 -9.438 32.625 C-5.649 33.031 -3.167 32.917 0.375 31.5 C3.663 28.682 4.308 25.899 4.781 21.668 C4.999 16.925 4.208 13.688 1.562 9.625 C-0.674 7.389 -1.785 6.69 -4.938 6.375 C-9.881 6.869 -12.159 7.668 -15.438 11.625 Z " fill="#E0E6FE" transform="translate(534.4375,487.375)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.32 2 3.64 2 5 2 C6.229 4.457 6.123 6.048 6.12 8.796 C6.122 10.315 6.122 10.315 6.124 11.864 C6.119 13.537 6.119 13.537 6.114 15.243 C6.114 16.413 6.114 17.584 6.114 18.79 C6.113 22.005 6.108 25.22 6.101 28.434 C6.095 31.792 6.095 35.15 6.093 38.508 C6.09 44.869 6.082 51.229 6.072 57.59 C6.061 64.83 6.055 72.071 6.05 79.312 C6.04 94.208 6.022 109.104 6 124 C2.637 125.802 0.418 126.238 -3.422 126.195 C-4.447 126.189 -5.471 126.182 -6.527 126.176 C-8.123 126.151 -8.123 126.151 -9.75 126.125 C-10.829 126.116 -11.908 126.107 -13.02 126.098 C-15.68 126.074 -18.34 126.041 -21 126 C-21.326 125.385 -21.652 124.77 -21.988 124.137 C-22.425 123.328 -22.862 122.52 -23.312 121.688 C-23.956 120.487 -23.956 120.487 -24.613 119.262 C-25.913 117.143 -27.218 115.705 -29 114 C-31 111 -31 111 -31.832 108.262 C-33.486 103.641 -36.197 99.953 -38.986 95.947 C-40.064 94.345 -41.04 92.676 -42 91 C-41.505 89.515 -41.505 89.515 -41 88 C-39.594 90.269 -38.19 92.538 -36.788 94.81 C-35.589 96.739 -34.38 98.661 -33.159 100.577 C-27.944 108.818 -27.944 108.818 -26.199 113.254 C-24.645 117.353 -24.645 117.353 -20 122 C-12.08 122.33 -4.16 122.66 4 123 C4 84.06 4 45.12 4 5 C-3.26 5 -10.52 5 -18 5 C-18 31.73 -18 58.46 -18 86 C-20.469 83.531 -21.842 81.814 -23.539 78.879 C-24.028 78.041 -24.517 77.202 -25.021 76.339 C-25.815 74.965 -25.815 74.965 -26.625 73.562 C-31.778 64.745 -37.007 55.977 -42.312 47.25 C-42.841 46.379 -42.841 46.379 -43.38 45.491 C-47.437 38.811 -51.52 32.148 -55.641 25.508 C-56.39 24.298 -57.14 23.088 -57.889 21.878 C-59.313 19.581 -60.742 17.288 -62.175 14.997 C-62.81 13.975 -63.444 12.952 -64.098 11.898 C-64.66 10.998 -65.222 10.097 -65.801 9.169 C-67 7 -67 7 -67 5 C-74.59 5 -82.18 5 -90 5 C-90 43.94 -90 82.88 -90 123 C-82.74 123 -75.48 123 -68 123 C-68 96.93 -68 70.86 -68 44 C-63.833 48.167 -61.484 52.031 -58.688 57.125 C-58.22 57.949 -57.753 58.772 -57.271 59.621 C-56.835 60.409 -56.399 61.196 -55.949 62.008 C-55.55 62.726 -55.152 63.444 -54.741 64.184 C-54.496 64.783 -54.252 65.383 -54 66 C-54.33 66.66 -54.66 67.32 -55 68 C-58 64.25 -58 64.25 -58 62 C-58.99 61.505 -58.99 61.505 -60 61 C-60.934 59.344 -61.801 57.65 -62.625 55.938 C-63.071 55.018 -63.517 54.099 -63.977 53.152 C-64.314 52.442 -64.652 51.732 -65 51 C-65.597 53.763 -66 56.16 -66 59 C-66 81 -66 103 -66 125 C-69.081 126.541 -72.31 126.143 -75.688 126.125 C-76.383 126.129 -77.078 126.133 -77.795 126.137 C-82.295 126.128 -86.56 125.733 -91 125 C-93.115 119.796 -93.279 115.293 -93.243 109.776 C-93.246 108.411 -93.246 108.411 -93.249 107.019 C-93.252 104.016 -93.242 101.014 -93.23 98.012 C-93.229 95.909 -93.229 93.806 -93.229 91.703 C-93.228 87.29 -93.219 82.878 -93.206 78.466 C-93.189 72.856 -93.185 67.246 -93.186 61.636 C-93.185 57.287 -93.18 52.938 -93.173 48.589 C-93.17 46.523 -93.168 44.458 -93.167 42.393 C-93.159 32.684 -93.102 22.983 -92.615 13.284 C-92.569 12.235 -92.569 12.235 -92.523 11.164 C-92.314 7.623 -92.02 6.03 -90 3 C-87.484 2.83 -85.19 2.811 -82.688 2.938 C-77.814 3.124 -74.432 3.055 -70 1 C-67.812 1.062 -67.812 1.062 -66 2 C-65.328 3.665 -64.663 5.332 -64 7 C-62.597 9.528 -61.167 12.018 -59.688 14.5 C-59.045 15.585 -59.045 15.585 -58.389 16.692 C-54.648 22.974 -50.835 29.212 -47 35.438 C-46.436 36.355 -45.873 37.273 -45.292 38.219 C-44.762 39.073 -44.233 39.928 -43.688 40.809 C-43.217 41.57 -42.746 42.331 -42.262 43.115 C-41.005 45.124 -41.005 45.124 -39 47 C-38.321 48.317 -37.682 49.654 -37.062 51 C-34.467 56.316 -31.461 61.334 -28.375 66.375 C-27.896 67.16 -27.416 67.945 -26.922 68.754 C-24.742 72.291 -22.529 75.7 -20 79 C-20.167 77.71 -20.335 76.421 -20.508 75.092 C-21.048 70.2 -21.041 65.436 -20.879 60.523 C-20.855 59.643 -20.832 58.762 -20.807 57.854 C-20.757 55.987 -20.704 54.119 -20.648 52.252 C-20.505 47.43 -20.381 42.608 -20.258 37.785 C-20.233 36.845 -20.209 35.905 -20.184 34.937 C-19.925 24.624 -19.897 14.316 -20 4 C-18 3 -18 3 -15.062 3.062 C-11.824 3.271 -11.824 3.271 -9 1 C-8.01 1.99 -7.02 2.98 -6 4 C-5.67 3.01 -5.34 2.02 -5 1 C-5 1.99 -5 2.98 -5 4 C-4.546 3.526 -4.092 3.051 -3.625 2.562 C-2 1 -2 1 0 0 Z M-72 3 C-68 4 -68 4 -68 4 Z M-2 3 C-2 3.33 -2 3.66 -2 4 C-0.02 4 1.96 4 4 4 C4 3.67 4 3.34 4 3 C2.02 3 0.04 3 -2 3 Z " fill="#7287D3" transform="translate(730,266)"/>
<path d="M0 0 C4.308 -0.12 7.496 0.187 11.312 2.25 C14.167 3.362 14.167 3.362 17.312 1.438 C21.17 -0.066 21.554 -0.185 25.125 1.188 C26.421 1.783 27.713 2.386 29 3 C29.949 3.289 30.898 3.577 31.875 3.875 C34.728 5.385 35.005 7.016 36 10 C36.66 10.33 37.32 10.66 38 11 C37.67 22.88 37.34 34.76 37 47 C34.03 47 31.06 47 28 47 C28.33 47.99 28.66 48.98 29 50 C28.01 49.67 27.02 49.34 26 49 C25.971 45.896 25.953 42.792 25.938 39.688 C25.929 38.806 25.921 37.925 25.912 37.018 C25.907 35.746 25.907 35.746 25.902 34.449 C25.894 33.279 25.894 33.279 25.886 32.085 C26 30 26 30 27 27 C26.34 26.67 25.68 26.34 25 26 C25 21.71 25 17.42 25 13 C23.886 13.021 22.773 13.041 21.625 13.062 C18 13 18 13 16 12 C13.902 12.399 13.902 12.399 12 13 C11.211 17.232 10.835 21.304 10.719 25.605 C10.681 26.836 10.644 28.066 10.605 29.334 C10.535 31.915 10.465 34.495 10.395 37.076 C10.357 38.303 10.32 39.529 10.281 40.793 C10.236 42.472 10.236 42.472 10.189 44.184 C10 47 10 47 9 50 C8.34 49.67 7.68 49.34 7 49 C4.844 49 3.076 49.447 1 50 C-1.699 45.652 -1.025 41.626 -0.777 36.68 C-0.851 35.795 -0.924 34.911 -1 34 C-1.99 33.34 -2.98 32.68 -4 32 C-4 31.34 -4 30.68 -4 30 C-3.34 30 -2.68 30 -2 30 C-0.967 25.116 -0.967 25.116 -2.062 20.375 C-3.328 17.17 -2.307 16.121 -1 13 C-0.888 10.968 -0.938 8.972 -0.996 6.938 C-1.001 4.533 -0.582 2.327 0 0 Z M-1 15 C-1 16.98 -1 18.96 -1 21 C-0.67 21 -0.34 21 0 21 C0 19.02 0 17.04 0 15 C-0.33 15 -0.66 15 -1 15 Z M3 47 C4 49 4 49 4 49 Z " fill="#DFE6FE" transform="translate(718,483)"/>
<path d="M0 0 C3.906 2.75 7.352 5.403 9 10 C9.841 18.6 10.325 26.891 5.387 34.336 C1.331 39.203 -2.722 41.598 -9.055 42.375 C-11.177 42.538 -13.301 42.677 -15.426 42.789 C-17.986 42.838 -17.986 42.838 -20 44 C-20.66 43.67 -21.32 43.34 -22 43 C-22.495 41.515 -22.495 41.515 -23 40 C-23.798 39.704 -24.596 39.407 -25.418 39.102 C-28 38 -28 38 -29.363 35.961 C-32.672 26.948 -34.462 15.311 -30.312 6.25 C-23.036 -3.976 -10.915 -6.079 0 0 Z M-15 5 C-15 5.33 -15 5.66 -15 6 C-13.02 6 -11.04 6 -9 6 C-9 5.67 -9 5.34 -9 5 C-10.98 5 -12.96 5 -15 5 Z M-20.688 8.312 C-22.219 10.951 -22.219 10.951 -22 14 C-22.33 14 -22.66 14 -23 14 C-23 16.97 -23 19.94 -23 23 C-22.01 23 -21.02 23 -20 23 C-20.66 23.66 -21.32 24.32 -22 25 C-22.059 27.021 -22.059 27.021 -21 29 C-18.364 30.873 -16.07 31.977 -13 33 C-13 32.34 -13 31.68 -13 31 C-11.886 31.309 -11.886 31.309 -10.75 31.625 C-7.609 32.053 -6.541 31.84 -4 30 C-1.981 26.798 -1.703 24.189 -1.688 20.438 C-1.671 19.487 -1.654 18.537 -1.637 17.559 C-2.058 14.588 -2.744 13.871 -5 12 C-7 8.526 -7 8.526 -7 6 C-14.398 5.167 -14.398 5.167 -20.688 8.312 Z M-6 7 C-5.67 7.99 -5.34 8.98 -5 10 C-5 9.01 -5 8.02 -5 7 C-5.33 7 -5.66 7 -6 7 Z M-4 12 C-4 12.66 -4 13.32 -4 14 C-3.34 13.67 -2.68 13.34 -2 13 C-2.66 12.67 -3.32 12.34 -4 12 Z " fill="#CCD5F7" transform="translate(704,489)"/>
<path d="M0 0 C12.083 1.608 12.083 1.608 16 6 C20.197 12.145 21.141 16.645 21 24 C21.99 24.33 22.98 24.66 24 25 C22.35 25.66 20.7 26.32 19 27 C19.289 27.846 19.577 28.691 19.875 29.562 C20.012 33.338 19.756 33.883 17.5 36.688 C13.383 40.976 8.96 43.087 3 43.375 C-2.325 43.138 -6.351 41.151 -10.688 38.125 C-14.714 32.684 -17.393 25.997 -16.719 19.141 C-14.955 12.813 -12.203 7.744 -7 3.625 C-4 2 -4 2 0 2 C0 1.34 0 0.68 0 0 Z M-1 10 C-1.99 11.485 -1.99 11.485 -3 13 C-2.01 13.33 -1.02 13.66 0 14 C-0.66 14.66 -1.32 15.32 -2 16 C2.979 16.754 2.979 16.754 8 17 C8.33 15.35 8.66 13.7 9 12 C5.32 9.547 3.346 9.605 -1 10 Z M-3 25 C-3 25.66 -3 26.32 -3 27 C-3.66 27 -4.32 27 -5 27 C-4.733 28.985 -4.733 28.985 -4 31 C-3.01 31.33 -2.02 31.66 -1 32 C-0.67 32.66 -0.34 33.32 0 34 C5.62 33.801 5.62 33.801 10.25 31.062 C10.827 30.382 11.405 29.701 12 29 C14.25 27.75 14.25 27.75 16 27 C14.009 24.562 14.009 24.562 10.086 24.805 C8.516 24.818 6.945 24.842 5.375 24.875 C4.172 24.889 4.172 24.889 2.945 24.902 C0.963 24.926 -1.018 24.962 -3 25 Z " fill="#DFE4FD" transform="translate(568,896)"/>
<path d="M0 0 C1.803 0.005 1.803 0.005 3.643 0.01 C4.888 0.018 6.134 0.027 7.418 0.035 C8.683 0.04 9.948 0.044 11.252 0.049 C14.37 0.061 17.488 0.077 20.605 0.098 C21.1 1.088 21.1 1.088 21.605 2.098 C23.45 3.014 23.45 3.014 25.668 3.848 C36.945 8.719 45.526 17.546 50.707 28.656 C52.003 32.177 52.398 35.36 52.605 39.098 C47.229 39.947 42.952 40.1 37.605 39.098 C37.605 39.758 37.605 40.418 37.605 41.098 C36.698 40.891 35.79 40.685 34.855 40.473 C31.495 40.085 30.365 40.294 27.605 42.098 C27.317 41.128 27.028 40.159 26.73 39.16 C25.963 35.991 25.963 35.991 23.605 35.098 C22.646 33.613 22.646 33.613 21.668 32.098 C19.883 28.855 19.883 28.855 16.605 28.098 C15.615 27.108 15.615 27.108 14.605 26.098 C5.738 23.392 -0.786 23.851 -9.395 27.098 C-10.849 27.562 -10.849 27.562 -12.332 28.035 C-14.949 29.383 -15.274 30.409 -16.395 33.098 C-17.354 34.397 -17.354 34.397 -18.332 35.723 C-22.955 43.287 -23.899 51.122 -23.77 59.848 C-23.764 60.8 -23.758 61.752 -23.752 62.733 C-23.61 70.482 -22.536 77.628 -20.395 85.098 C-19.735 85.098 -19.075 85.098 -18.395 85.098 C-18.044 85.799 -17.693 86.5 -17.332 87.223 C-14.329 91.679 -9.583 95.016 -4.461 96.754 C3.43 98.067 12.367 97.785 19.605 94.098 C20.1 92.613 20.1 92.613 20.605 91.098 C20.935 90.768 21.265 90.438 21.605 90.098 C21.275 89.438 20.945 88.778 20.605 88.098 C21.925 88.098 23.245 88.098 24.605 88.098 C25.595 85.128 26.585 82.158 27.605 79.098 C26.329 79.037 25.053 78.976 23.738 78.914 C22.069 78.83 20.4 78.745 18.73 78.66 C17.889 78.621 17.047 78.582 16.18 78.541 C15.374 78.499 14.568 78.457 13.738 78.414 C12.995 78.377 12.251 78.341 11.485 78.303 C9.605 78.098 9.605 78.098 7.605 77.098 C7.605 73.138 7.605 69.178 7.605 65.098 C6.615 64.603 6.615 64.603 5.605 64.098 C6.265 64.098 6.925 64.098 7.605 64.098 C7.585 62.798 7.564 61.499 7.543 60.16 C7.521 58.806 7.534 57.45 7.605 56.098 C9.232 54.472 11.062 54.932 13.309 54.892 C14.31 54.872 15.311 54.852 16.342 54.832 C17.431 54.815 18.519 54.798 19.641 54.781 C20.749 54.76 21.857 54.74 22.999 54.718 C26.556 54.653 30.112 54.594 33.668 54.535 C36.072 54.492 38.477 54.448 40.881 54.404 C46.789 54.297 52.697 54.195 58.605 54.098 C58.605 54.758 58.605 55.418 58.605 56.098 C57.615 56.758 56.625 57.418 55.605 58.098 C55.956 58.655 56.307 59.211 56.668 59.785 C57.837 62.669 57.459 64.15 56.605 67.098 C56.275 67.758 55.945 68.418 55.605 69.098 C55.235 71.702 54.903 74.297 54.605 76.91 C54.092 81.342 53.565 85.735 52.605 90.098 C51.945 90.098 51.285 90.098 50.605 90.098 C50.327 91.273 50.327 91.273 50.043 92.473 C46.948 100.277 41.871 106.36 35.402 111.625 C33.578 113.065 33.578 113.065 31.988 114.754 C30.247 116.446 28.965 116.612 26.605 117.098 C24.931 117.745 23.262 118.407 21.605 119.098 C21.605 119.758 21.605 120.418 21.605 121.098 C15.157 122.417 9.118 121.793 2.605 121.098 C3.265 122.418 3.925 123.738 4.605 125.098 C3.615 124.768 2.625 124.438 1.605 124.098 C1.275 123.438 0.945 122.778 0.605 122.098 C-1.127 122.19 -1.127 122.19 -2.895 122.285 C-7.596 122.285 -11.118 120.902 -15.395 119.098 C-16.055 118.881 -16.715 118.665 -17.395 118.441 C-22.146 116.882 -25.656 114.65 -29.535 111.5 C-31.346 110.036 -31.346 110.036 -33.449 109.125 C-41.317 104.97 -46.274 92.209 -48.77 84.16 C-49.34 81.365 -49.567 78.939 -49.395 76.098 C-50.055 75.768 -50.715 75.438 -51.395 75.098 C-51.671 64.67 -50.97 54.626 -49.665 44.286 C-49.205 40.561 -48.803 36.829 -48.395 33.098 C-47.735 33.098 -47.075 33.098 -46.395 33.098 C-46.126 32.492 -45.858 31.886 -45.582 31.262 C-41.022 21.513 -35.357 14.072 -26.395 8.098 C-26.395 7.438 -26.395 6.778 -26.395 6.098 C-25.673 5.974 -24.951 5.85 -24.207 5.723 C-21.418 5.103 -19.02 4.213 -16.395 3.098 C-10.829 0.732 -6.052 -0.029 0 0 Z M-37.18 20.07 C-49.84 35.839 -50.623 55.401 -49.113 74.691 C-47.193 88.723 -40.454 101.73 -29.207 110.473 C-16.488 119.087 -2.056 121.894 13.055 119.566 C27.513 116.476 38.318 109.496 46.605 97.098 C54.637 84.573 53.605 70.698 53.605 56.098 C39.085 56.098 24.565 56.098 9.605 56.098 C9.605 62.698 9.605 69.298 9.605 76.098 C16.535 76.098 23.465 76.098 30.605 76.098 C24.295 92.323 24.295 92.323 16.855 96.473 C9.362 99.47 -0.26 99.318 -8.023 97.473 C-14.212 94.76 -18.831 89.395 -21.684 83.379 C-26.765 70.071 -27.473 53.755 -22.324 40.301 C-18.644 32.477 -14.038 27.989 -6.395 24.098 C1.376 21.638 10.043 22.383 17.41 25.688 C21.149 28.089 23.328 31.351 25.605 35.098 C26.265 36.088 26.925 37.078 27.605 38.098 C30.124 38.514 30.124 38.514 33.051 38.391 C34.665 38.376 34.665 38.376 36.312 38.361 C38.004 38.324 38.004 38.324 39.73 38.285 C40.866 38.272 42.002 38.258 43.172 38.244 C45.984 38.209 48.794 38.159 51.605 38.098 C47.944 24.979 41.99 14.357 29.988 7.227 C25.079 4.901 19.837 3.509 14.605 2.098 C13.939 1.902 13.273 1.706 12.586 1.504 C-6.712 -2.455 -24.521 6.082 -37.18 20.07 Z " fill="#6A7FCF" transform="translate(779.39453125,106.90234375)"/>
<path d="M0 0 C1.583 -0.027 3.167 -0.046 4.75 -0.062 C6.073 -0.08 6.073 -0.08 7.422 -0.098 C9.782 -0.008 11.745 0.331 14 1 C14 1.99 14 2.98 14 4 C16.967 5.648 19.656 6.443 23 7 C23 7.99 23 8.98 23 10 C22.67 10.33 22.34 10.66 22 11 C22.911 15.277 23.78 19.194 26 23 C25.01 23.33 24.02 23.66 23 24 C23.495 27.96 23.495 27.96 24 32 C23.01 32.33 22.02 32.66 21 33 C20.567 33.763 20.134 34.526 19.688 35.312 C17.201 39.272 14.02 41.378 9.562 42.75 C3.184 43.372 -2.418 43.417 -8 40 C-10.428 37.572 -12.446 35.068 -14 32 C-14 30.68 -14 29.36 -14 28 C-14.99 28 -15.98 28 -17 28 C-16.691 25.562 -16.378 23.125 -16.062 20.688 C-15.975 19.997 -15.888 19.307 -15.799 18.596 C-15.408 15.599 -14.958 12.875 -14 10 C-13.34 10.33 -12.68 10.66 -12 11 C-11.918 10.402 -11.835 9.804 -11.75 9.188 C-10.786 6.376 -9.4 5.673 -7 4 C-6.34 3.01 -5.68 2.02 -5 1 C-5 1.66 -5 2.32 -5 3 C-3.35 2.34 -1.7 1.68 0 1 C0 0.67 0 0.34 0 0 Z M-3 12 C-2.67 12.99 -2.34 13.98 -2 15 C-2.99 15.33 -3.98 15.66 -5 16 C-5.658 24.562 -5.658 24.562 -2 32 C-0.074 34.332 -0.074 34.332 3.312 34.438 C7.877 33.896 9.124 32.468 12 29 C12.33 28.67 12.66 28.34 13 28 C13.071 26.648 13.084 25.292 13.062 23.938 C13.042 22.638 13.021 21.339 13 20 C12.34 20 11.68 20 11 20 C11.309 18.608 11.309 18.608 11.625 17.188 C12.367 13.848 12.367 13.848 10 11 C7.682 10.342 7.682 10.342 5.062 10.188 C4.187 10.099 3.312 10.01 2.41 9.918 C-0.266 9.797 -0.266 9.797 -3 12 Z M12 16 C13 19 13 19 13 19 Z M-16 25 C-15 27 -15 27 -15 27 Z " fill="#DEE3FD" transform="translate(669,896)"/>
<path d="M0 0 C4.689 3.383 7.591 7.559 8.754 13.266 C9.208 20.103 9.395 26.883 6.754 33.266 C1.66 38.5 -3.913 41.054 -11.184 41.516 C-17.516 41.322 -22.751 39.882 -27.246 35.266 C-32.449 27.953 -32.124 19.882 -31.246 11.266 C-29.785 5.921 -26.978 2.195 -22.246 -0.734 C-14.995 -4.107 -6.933 -3.978 0 0 Z M-19.246 7.266 C-23.284 12.557 -22.994 18.912 -22.246 25.266 C-21.089 29.047 -21.089 29.047 -18.246 31.266 C-13.672 32.597 -8.795 32.782 -4.246 31.266 C-3.586 30.606 -2.926 29.946 -2.246 29.266 C-2.246 28.276 -2.246 27.286 -2.246 26.266 C-2.906 25.936 -3.566 25.606 -4.246 25.266 C-2.926 24.606 -1.606 23.946 -0.246 23.266 C0.2 17.016 -0.454 12.412 -4.246 7.266 C-9.085 4.288 -14.357 4.435 -19.246 7.266 Z " fill="#E2E7FE" transform="translate(657.24609375,488.734375)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.33 6.27 7.66 12.54 8 19 C9.32 17.68 10.64 16.36 12 15 C16.13 13.474 20.664 13.466 25 14 C30.029 16.422 31.956 18.882 34 24 C34.438 27.011 34.465 29.961 34.438 33 C34.467 34.172 34.467 34.172 34.498 35.367 C34.492 41.959 32.248 45.411 27.938 50.375 C23.384 52.894 19.072 52.999 14 52 C11.228 50.349 9.115 48.425 7 46 C7 47.98 7 49.96 7 52 C4.69 52 2.38 52 0 52 C0 34.84 0 17.68 0 0 Z M11 22.938 C8.477 26.801 8.836 28.802 9.605 33.207 C9.736 33.799 9.866 34.39 10 35 C9.01 35.495 9.01 35.495 8 36 C9.284 39.497 9.284 39.497 11 43 C11.99 43.33 12.98 43.66 14 44 C14.66 43.67 15.32 43.34 16 43 C16 43.66 16 44.32 16 45 C19.306 44.449 22.2 43.9 25 42 C26.145 38.757 26.103 35.662 26.062 32.25 C26.053 31.265 26.044 30.28 26.035 29.266 C26.024 28.518 26.012 27.77 26 27 C25.01 27 24.02 27 23 27 C23.33 26.01 23.66 25.02 24 24 C22.944 22.354 22.944 22.354 21 21 C16.682 20.36 14.692 20.553 11 22.938 Z " fill="#DFE4FE" transform="translate(358,885)"/>
<path d="M0 0 C0 3.917 -1.555 5.611 -3.711 8.77 C-8.193 16.525 -8.327 26.128 -6.383 34.73 C-3.374 42.464 2.626 47.147 10 50.562 C17.208 53.368 24.754 55.05 32.283 56.77 C40.626 58.718 48.615 61.014 55 67 C57.007 71.014 56.601 75.645 56 80 C53.71 85.143 49.76 87.386 44.688 89.438 C36.23 92.104 27.111 91.977 19.02 88.035 C14.323 85.061 11.768 81.25 10 76 C10 75.01 10 74.02 10 73 C2.74 73 -4.52 73 -12 73 C-9.856 83.718 -7.121 93.906 2.223 100.59 C15.725 109.125 31.572 111.006 47.25 108.312 C59.154 105.442 68.433 100.711 74.957 90.223 C77.579 84.634 78.364 79.178 78.312 73.062 C78.307 72.358 78.301 71.654 78.295 70.928 C78.117 63.294 76.375 57.383 71.023 51.757 C62.657 44.493 51.088 42.039 40.625 39.312 C38.483 38.747 36.343 38.177 34.204 37.6 C32.281 37.082 30.355 36.577 28.428 36.072 C22.669 34.271 18.05 32.396 15 27 C14.145 22.281 14.539 18.923 16.688 14.625 C20.72 10.048 24.664 8.397 30.594 7.691 C38.807 7.327 45.277 9.149 52 14 C53.902 16.52 53.902 16.52 55 19 C54.505 20.485 54.505 20.485 54 22 C53.443 21.031 52.886 20.061 52.312 19.062 C50.355 15.849 50.355 15.849 47.562 15.25 C46.717 15.168 45.871 15.085 45 15 C43.321 14.366 41.65 13.707 40 13 C40.33 12.34 40.66 11.68 41 11 C34.413 9.954 29.408 10.167 23 12 C23.66 12.33 24.32 12.66 25 13 C24.67 13.66 24.34 14.32 24 15 C22.02 15 20.04 15 18 15 C17.34 18.63 16.68 22.26 16 26 C16.66 26.33 17.32 26.66 18 27 C18.66 27.99 19.32 28.98 20 30 C22.563 30.73 22.563 30.73 25 31 C25 31.66 25 32.32 25 33 C29.531 33.934 29.531 33.934 34 33 C34 33.99 34 34.98 34 36 C36.31 36 38.62 36 41 36 C41 36.66 41 37.32 41 38 C41.855 38.061 42.709 38.121 43.59 38.184 C44.695 38.267 45.799 38.351 46.938 38.438 C48.04 38.519 49.142 38.6 50.277 38.684 C53 39 53 39 54 40 C54.99 40 55.98 40 57 40 C56.505 40.99 56.505 40.99 56 42 C56.557 42.147 57.114 42.294 57.688 42.445 C66.726 45.102 72.838 49.584 77.812 57.641 C82.503 66.959 81.536 76.985 79.102 86.812 C76.034 95.688 68.895 101.866 60.773 106.184 C46.768 112.753 28.344 113.662 13.5 109.312 C8.377 107.375 3.679 104.825 -1 102 C-2.063 101.536 -2.063 101.536 -3.148 101.062 C-6.208 99.307 -6.739 96.181 -8 93 C-8.715 92.149 -8.715 92.149 -9.445 91.281 C-11.659 88.033 -12.394 84.663 -13.312 80.875 C-13.604 79.783 -13.604 79.783 -13.9 78.668 C-14.365 76.8 -14.693 74.9 -15 73 C-13 71 -13 71 -10.294 70.773 C-9.174 70.783 -8.053 70.794 -6.898 70.805 C-5.687 70.811 -4.475 70.818 -3.227 70.824 C-1.956 70.841 -0.685 70.858 0.625 70.875 C1.904 70.884 3.183 70.893 4.5 70.902 C7.667 70.926 10.833 70.959 14 71 C14.495 71.99 14.495 71.99 15 73 C14.34 73 13.68 73 13 73 C13.344 75.473 13.344 75.473 14 78 C14.66 78.33 15.32 78.66 16 79 C16 80.667 16 82.333 16 84 C17.436 84.506 18.874 85.004 20.312 85.5 C21.113 85.778 21.914 86.057 22.738 86.344 C25.088 87.139 25.088 87.139 28 87 C30.674 87.286 33.334 87.65 36 88 C36 87.67 36 87.34 36 87 C36.603 87.012 37.207 87.023 37.828 87.035 C38.627 87.044 39.427 87.053 40.25 87.062 C41.039 87.074 41.828 87.086 42.641 87.098 C44.907 87.004 46.829 86.632 49 86 C49 84.68 49 83.36 49 82 C49.66 82 50.32 82 51 82 C51 81.34 51 80.68 51 80 C50.34 79.67 49.68 79.34 49 79 C50.65 79.33 52.3 79.66 54 80 C54.324 74.496 54.533 70.563 51 66 C50.01 66.495 50.01 66.495 49 67 C49 66.01 49 65.02 49 64 C46.69 63.67 44.38 63.34 42 63 C42 62.34 42 61.68 42 61 C40.742 60.773 39.484 60.546 38.188 60.312 C31.967 59.09 25.876 57.399 19.778 55.677 C15.399 54.478 11.588 53.719 7 54 C7.33 53.34 7.66 52.68 8 52 C5.624 50.391 5.624 50.391 3 49 C2.01 49.495 2.01 49.495 1 50 C0.608 49.299 0.216 48.597 -0.188 47.875 C-1.94 44.897 -1.94 44.897 -4.5 42.312 C-7.383 38.986 -8.326 35.376 -9.422 31.172 C-9.928 28.975 -9.928 28.975 -11 27 C-10.508 17.846 -7.625 8.031 -1.309 1.16 C-0.877 0.777 -0.445 0.394 0 0 Z M-4 98 C-3 100 -3 100 -3 100 Z " fill="#6E84D2" transform="translate(295,281)"/>
<path d="M0 0 C1.478 2.957 1.06 5.742 1 9 C6.206 11.225 11.308 13.146 16.812 14.5 C21.746 15.789 25.376 17.294 28.102 21.836 C30.518 27.656 29.47 35.373 27.289 41.152 C25.123 44.257 22.424 44.797 18.84 45.586 C15.015 46.144 11.206 46.556 7.348 46.789 C4.848 46.923 4.848 46.923 2 48 C1.34 47.34 0.68 46.68 0 46 C-2.75 44.032 -2.75 44.032 -5.016 43.145 C-7.535 41.692 -8.367 40.227 -9.75 37.688 C-10.178 36.928 -10.606 36.169 -11.047 35.387 C-12.099 32.751 -12.179 30.812 -12 28 C-10.398 27.778 -8.793 27.573 -7.188 27.375 C-5.848 27.201 -5.848 27.201 -4.48 27.023 C-2 27 -2 27 1 29 C1.66 29 2.32 29 3 29 C3.66 29.33 4.32 29.66 5 30 C4.67 30.99 4.34 31.98 4 33 C5.812 35.243 5.812 35.243 8.383 35.195 C9.74 35.161 9.74 35.161 11.125 35.125 C12.035 35.107 12.945 35.089 13.883 35.07 C14.581 35.047 15.28 35.024 16 35 C16 34.34 16 33.68 16 33 C16.99 33 17.98 33 19 33 C19.25 29.584 19.25 29.584 19 26 C18.01 25.34 17.02 24.68 16 24 C16 23.34 16 22.68 16 22 C15.051 21.876 14.103 21.753 13.125 21.625 C10 21 10 21 8 19 C7.01 18.67 6.02 18.34 5 18 C4.938 17.237 4.876 16.474 4.812 15.688 C4.272 12.77 4.272 12.77 1.5 11.625 C-1 10 -1 10 -1.938 6.5 C-2 3 -2 3 0 0 Z " fill="#DBE0FD" transform="translate(283,893)"/>
<path d="M0 0 C0.025 1.257 0.05 2.513 0.076 3.808 C0.17 8.465 0.27 13.122 0.372 17.78 C0.416 19.796 0.457 21.813 0.497 23.829 C0.555 26.726 0.619 29.623 0.684 32.52 C0.7 33.423 0.717 34.326 0.734 35.257 C0.764 36.518 0.764 36.518 0.795 37.804 C0.818 38.913 0.818 38.913 0.842 40.045 C0.939 42.215 0.939 42.215 2 45 C1.34 45 0.68 45 0 45 C-0.33 47.64 -0.66 50.28 -1 53 C-1.66 53 -2.32 53 -3 53 C-3.227 53.577 -3.454 54.155 -3.688 54.75 C-5.861 58.476 -8.774 60.416 -12.625 62.25 C-20.521 64.005 -27.764 63.681 -35 60 C-38.35 56.738 -39.936 53.544 -41 49 C-40.625 46.625 -40.625 46.625 -40 45 C-38.68 45 -37.36 45 -36 45 C-36 45.66 -36 46.32 -36 47 C-33.229 45.294 -33.229 45.294 -32 44 C-32.742 43.443 -33.485 42.886 -34.25 42.312 C-37 40 -37 40 -39.062 37.25 C-39.702 36.508 -40.341 35.765 -41 35 C-42.32 35 -43.64 35 -45 35 C-45 38.63 -45 42.26 -45 46 C-47.97 46 -50.94 46 -54 46 C-54.66 47.32 -55.32 48.64 -56 50 C-56.33 50 -56.66 50 -57 50 C-56.988 49.241 -56.977 48.481 -56.965 47.699 C-56.956 46.705 -56.947 45.711 -56.938 44.688 C-56.926 43.701 -56.914 42.715 -56.902 41.699 C-56.877 38.9 -56.877 38.9 -58 36 C-57.34 36 -56.68 36 -56 36 C-56.6 30.719 -56.6 30.719 -57.562 25.5 C-58.18 21.972 -58.061 18.58 -58 15 C-58.99 14.67 -59.98 14.34 -61 14 C-61.33 13.01 -61.66 12.02 -62 11 C-64.31 11 -66.62 11 -69 11 C-69.33 11.66 -69.66 12.32 -70 13 C-70 10 -70 10 -69 9 C-64.635 8.46 -61.133 8.178 -57.5 10.812 C-54.804 14.745 -54.882 17.988 -54.902 22.637 C-54.906 23.935 -54.909 25.234 -54.912 26.572 C-54.92 27.944 -54.929 29.316 -54.938 30.688 C-54.943 32.071 -54.947 33.454 -54.951 34.838 C-54.963 38.225 -54.979 41.613 -55 45 C-52.69 45 -50.38 45 -48 45 C-48.119 40.552 -48.243 36.105 -48.372 31.658 C-48.415 30.147 -48.457 28.636 -48.497 27.125 C-48.556 24.945 -48.619 22.765 -48.684 20.586 C-48.72 19.277 -48.757 17.968 -48.795 16.619 C-48.981 13.337 -49.387 10.225 -50 7 C-48 8 -48 8 -46.875 11.25 C-46.142 14.391 -45.906 16.82 -46 20 C-45.67 20.33 -45.34 20.66 -45 21 C-44.928 22.853 -44.916 24.708 -44.938 26.562 C-44.947 27.574 -44.956 28.586 -44.965 29.629 C-44.976 30.411 -44.988 31.194 -45 32 C-44.34 32.33 -43.68 32.66 -43 33 C-42.351 28.826 -41.91 24.716 -41.625 20.5 C-40.997 13.446 -38.955 8.255 -34 3 C-29.631 0.39 -25.02 0.016 -20 0 C-18.453 0.557 -18.453 0.557 -16.875 1.125 C-13.16 2.256 -11.505 1.312 -8.125 -0.371 C-5.22 -1.231 -2.942 -0.553 0 0 Z M-36.609 7.965 C-40.891 15.193 -40.986 22.815 -40 31 C-38.161 37.504 -34.525 40.943 -28.75 44.312 C-24.64 45.34 -21.065 45.226 -17 44 C-14.368 42.263 -12.17 40.282 -10 38 C-9.433 47.64 -9.433 47.64 -13 52 C-17.263 54.842 -21.001 54.391 -26 54 C-29.062 53.188 -29.062 53.188 -31 52 C-31.495 50.515 -31.495 50.515 -32 49 C-34.31 49 -36.62 49 -39 49 C-37.724 53.987 -35.914 56.659 -32 60 C-28.427 61.191 -25.454 61.205 -21.688 61.25 C-19.744 61.289 -19.744 61.289 -17.762 61.328 C-12.289 60.851 -9.145 59.119 -5.25 55.312 C-0.569 49.207 -0.716 41.597 -0.805 34.25 C-0.809 32.902 -0.809 32.902 -0.813 31.527 C-0.824 28.684 -0.849 25.842 -0.875 23 C-0.885 21.061 -0.894 19.122 -0.902 17.184 C-0.924 12.456 -0.959 7.728 -1 3 C-3.64 3 -6.28 3 -9 3 C-9.33 4.32 -9.66 5.64 -10 7 C-10.763 6.361 -11.526 5.721 -12.312 5.062 C-19.701 -0.608 -30.715 0.769 -36.609 7.965 Z M-13 42 C-13.33 42.66 -13.66 43.32 -14 44 C-12.68 43.34 -11.36 42.68 -10 42 C-10.99 42 -11.98 42 -13 42 Z M-30 45 C-27.435 47.565 -25.523 47.54 -22 48 C-22.66 48.66 -23.32 49.32 -24 50 C-23.01 51.485 -23.01 51.485 -22 53 C-17.25 53.564 -17.25 53.564 -13.062 51.625 C-11.818 50.045 -11.818 50.045 -12 48 C-12.66 48 -13.32 48 -14 48 C-14.33 47.01 -14.66 46.02 -15 45 C-19.95 45 -24.9 45 -30 45 Z M-12 45 C-11 48 -11 48 -11 48 Z M-30 48 C-28.515 48.99 -28.515 48.99 -27 50 C-27.33 50.66 -27.66 51.32 -28 52 C-27.01 52.33 -26.02 52.66 -25 53 C-24.67 51.35 -24.34 49.7 -24 48 C-25.98 48 -27.96 48 -30 48 Z " fill="#7387D9" transform="translate(549,484)"/>
<path d="M0 0 C11.55 0 23.1 0 35 0 C44.791 26.999 44.791 26.999 48.147 37.779 C48.71 39.537 49.348 41.272 50 43 C50.99 43.495 50.99 43.495 52 44 C52.174 46.014 52.346 48.028 52.516 50.043 C53.249 54.518 54.764 58.68 56.273 62.946 C58 67.838 58 67.838 58 70 C58.66 70 59.32 70 60 70 C60 72.31 60 74.62 60 77 C60.66 77 61.32 77 62 77 C61.505 78.98 61.505 78.98 61 81 C58.83 77.745 57.854 74.885 56.637 71.168 C56.408 70.476 56.18 69.783 55.944 69.07 C55.448 67.565 54.954 66.06 54.462 64.554 C53.138 60.509 51.802 56.467 50.465 52.426 C50.186 51.582 49.908 50.739 49.62 49.87 C47.558 43.643 45.45 37.433 43.316 31.23 C43.017 30.359 42.717 29.487 42.408 28.589 C41.232 25.171 40.055 21.754 38.872 18.339 C38.053 15.974 37.237 13.608 36.422 11.242 C36.052 10.182 36.052 10.182 35.676 9.101 C34 4.226 34 4.226 34 2 C23.44 2 12.88 2 2 2 C2 39.29 2 76.58 2 115 C8.93 115 15.86 115 23 115 C23 88.27 23 61.54 23 34 C23.66 34 24.32 34 25 34 C26.127 40.511 26.127 40.511 25.533 42.8 C24.787 45.878 24.889 48.789 24.902 51.956 C24.905 52.993 24.905 52.993 24.907 54.051 C24.911 55.548 24.917 57.045 24.925 58.541 C24.938 60.907 24.944 63.272 24.949 65.638 C24.96 70.654 24.98 75.671 25 80.688 C25.024 86.51 25.044 92.333 25.056 98.156 C25.062 100.492 25.075 102.828 25.088 105.164 C25.091 106.587 25.095 108.01 25.098 109.432 C25.106 111.303 25.106 111.303 25.114 113.211 C25 116 25 116 24 117 C22.292 117.087 20.581 117.107 18.871 117.098 C17.319 117.093 17.319 117.093 15.736 117.088 C14.648 117.08 13.559 117.071 12.438 117.062 C10.799 117.056 10.799 117.056 9.127 117.049 C6.418 117.037 3.709 117.021 1 117 C1 116.34 1 115.68 1 115 C0.34 114.67 -0.32 114.34 -1 114 C-0.475 108.97 0.106 103.981 1 99 C0.34 99 -0.32 99 -1 99 C-1 96.333 -1 93.667 -1 91 C-1.021 89.824 -1.041 88.649 -1.062 87.438 C-1.07 83.216 -0.597 79.176 0 75 C-0.33 75 -0.66 75 -1 75 C-1 67 -1 59 -1 51 C-1.012 49.931 -1.023 48.863 -1.035 47.762 C-1.045 46.404 -1.054 45.046 -1.062 43.688 C-1.071 43.01 -1.079 42.333 -1.088 41.635 C-1.104 38.248 -0.936 35.254 0 32 C-0.144 31.423 -0.289 30.845 -0.438 30.25 C-1.301 26.796 -1.117 23.557 -1 20 C-0.67 19.67 -0.34 19.34 0 19 C0.136 15.337 0.165 12.494 -1 9 C-0.34 9 0.32 9 1 9 C0.67 6.03 0.34 3.06 0 0 Z M0 83 C1 87 1 87 1 87 Z " fill="#8D9EDD" transform="translate(433,109)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 6.6 6 13.2 6 20 C6.66 20 7.32 20 8 20 C8.402 19.072 8.402 19.072 8.812 18.125 C10 16 10 16 13 14 C15.559 13.734 15.559 13.734 18.438 13.75 C19.385 13.745 20.332 13.74 21.309 13.734 C24.089 14.009 25.672 14.485 28 16 C32.428 21.82 33.149 25.729 33.188 32.938 C33.202 34.143 33.216 35.348 33.23 36.59 C33.009 39.86 32.408 42.068 31 45 C29.841 41.522 30.292 39.541 31 36 C30.34 36 29.68 36 29 36 C28.67 36.99 28.34 37.98 28 39 C27.34 39 26.68 39 26 39 C25.974 38.12 25.948 37.239 25.922 36.332 C25.865 35.171 25.808 34.009 25.75 32.812 C25.704 31.664 25.657 30.515 25.609 29.332 C24.9 25.454 23.788 23.748 21 21 C17.111 19.869 14.966 19.614 11.188 21.125 C8.044 23.82 7.213 25.981 6.781 30.078 C6.561 35.48 7.425 39.246 10 44 C13.468 45.734 17.218 45.613 21 45 C24.067 42.687 24.067 42.687 26 40 C25.432 43.293 24.602 44.894 22 47 C19.809 47.547 19.809 47.547 17.438 47.75 C16.652 47.827 15.867 47.905 15.059 47.984 C13 48 13 48 11 47 C11.33 47.99 11.66 48.98 12 50 C10.68 49.34 9.36 48.68 8 48 C8 47.34 8 46.68 8 46 C7.34 46 6.68 46 6 46 C6 47.98 6 49.96 6 52 C4.02 52 2.04 52 0 52 C0 34.84 0 17.68 0 0 Z " fill="#E1E6FE" transform="translate(515,885)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.025 0.797 7.05 1.594 7.076 2.415 C7.192 6.027 7.315 9.638 7.438 13.25 C7.477 14.504 7.516 15.759 7.557 17.051 C7.619 18.859 7.619 18.859 7.684 20.703 C7.72 21.813 7.757 22.924 7.795 24.067 C7.929 27.21 7.929 27.21 9 31 C12.96 31 16.92 31 21 31 C22.533 26.875 23.278 23.338 23.414 18.945 C23.453 17.837 23.491 16.728 23.531 15.586 C23.578 13.873 23.578 13.873 23.625 12.125 C23.664 10.96 23.702 9.794 23.742 8.594 C23.836 5.729 23.922 2.865 24 0 C26.31 0 28.62 0 31 0 C31 12.54 31 25.08 31 38 C28.69 38 26.38 38 24 38 C24 35.69 24 33.38 24 31 C23.34 31 22.68 31 22 31 C21.918 31.598 21.835 32.196 21.75 32.812 C20.763 35.692 19.573 36.418 17 38 C12.283 38.851 8.226 38.664 4 36.375 C0.173 31.83 -0.248 27.074 -0.195 21.289 C-0.189 20.045 -0.182 18.801 -0.176 17.52 C-0.159 16.234 -0.142 14.949 -0.125 13.625 C-0.116 12.317 -0.107 11.008 -0.098 9.66 C-0.074 6.44 -0.041 3.22 0 0 Z " fill="#D9DFFD" transform="translate(319,899)"/>
<path d="M0 0 C3.979 -0.059 7.958 -0.094 11.938 -0.125 C13.054 -0.142 14.171 -0.159 15.322 -0.176 C21.89 -0.214 27.653 0.291 34 2 C34.33 1.34 34.66 0.68 35 0 C35.495 0.99 35.495 0.99 36 2 C37.497 2.642 39.024 3.215 40.562 3.75 C50.681 7.697 57.049 14.826 64 23 C64.826 23.971 64.826 23.971 65.668 24.961 C67.347 27.531 68.113 30.066 69 33 C69.512 34.433 69.512 34.433 70.035 35.895 C71.927 41.268 73.44 46.267 73.824 51.965 C73.957 54.062 73.957 54.062 74.535 56.311 C76.634 68.45 72.387 84.146 67 95 C66.585 95.847 66.17 96.694 65.742 97.566 C62.897 102.997 59.704 108.027 54.688 111.688 C53.852 112.337 53.852 112.337 53 113 C53 113.99 53 114.98 53 116 C51.68 116 50.36 116 49 116 C46.945 116.927 44.925 117.936 42.938 119 C37.319 122 37.319 122 34 122 C33.67 122.66 33.34 123.32 33 124 C28.667 124.029 24.333 124.047 20 124.062 C18.779 124.071 17.559 124.079 16.301 124.088 C10.1 124.105 4.127 123.973 -2 123 C-2 122.34 -2 121.68 -2 121 C-2.99 121.33 -3.98 121.66 -5 122 C-5 121.34 -5 120.68 -5 120 C-5.727 119.732 -6.454 119.464 -7.203 119.188 C-19.734 113.867 -28.329 103.681 -33.711 91.469 C-36.79 83.182 -38.563 75.674 -38.875 66.812 C-38.912 65.806 -38.949 64.799 -38.987 63.762 C-39.411 45.778 -34.063 28.643 -22 15 C-15.647 8.853 -7.812 4.972 0 1 C0 0.67 0 0.34 0 0 Z M-22.969 18.504 C-33.939 31.985 -36.328 45.796 -36.438 62.688 C-36.448 63.911 -36.458 65.134 -36.469 66.394 C-36.312 82.594 -31.836 96.864 -20.492 108.742 C-14.268 114.381 -7.092 118.693 1 121 C1.683 121.204 2.366 121.407 3.07 121.617 C17.518 125.397 33.444 123.104 46.27 115.688 C59.668 107.288 67.359 95.252 71 79.938 C74.154 60.457 73.483 39.739 62 23 C53.089 11.432 42.462 4.54 28 2 C24.44 1.764 20.88 1.769 17.312 1.75 C15.898 1.719 15.898 1.719 14.455 1.688 C-0.187 1.607 -12.992 7.786 -22.969 18.504 Z " fill="#667CCD" transform="translate(549,268)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10.125 9.75 10.125 9.75 9 12 C9.66 12 10.32 12 11 12 C10.752 13.078 10.752 13.078 10.5 14.177 C10.011 16.939 9.93 19.363 10.008 22.16 C10.025 23.119 10.041 24.077 10.059 25.064 C10.103 27.067 10.155 29.07 10.215 31.072 C10.229 32.029 10.243 32.987 10.258 33.973 C10.291 35.276 10.291 35.276 10.325 36.606 C9.933 39.496 8.808 40.766 7 43 C7.265 46.004 7.833 46.833 10 49 C10.042 51.333 10.042 53.667 10 56 C10.33 56.66 10.66 57.32 11 58 C6.45 59.638 1.921 58.838 -2.438 56.938 C-2.953 56.628 -3.469 56.319 -4 56 C-3.711 54.989 -3.423 53.979 -3.125 52.938 C-2.946 52.303 -2.767 51.669 -2.582 51.016 C-2.019 49.065 -1.416 47.126 -0.812 45.188 C0.337 42.129 0.337 42.129 -1 40 C-1.181 37.519 -1.307 35.063 -1.387 32.578 C-1.425 31.472 -1.425 31.472 -1.464 30.343 C-1.516 28.78 -1.566 27.216 -1.613 25.652 C-1.687 23.273 -1.778 20.894 -1.869 18.516 C-1.918 16.995 -1.965 15.474 -2.012 13.953 C-2.041 13.247 -2.07 12.54 -2.1 11.812 C-2.209 7.458 -1.614 4.042 0 0 Z M9 13 C10 16 10 16 10 16 Z M2 45 C2.33 45.66 2.66 46.32 3 47 C3.33 46.34 3.66 45.68 4 45 C3.34 45 2.68 45 2 45 Z " fill="#7D90DE" transform="translate(764,472)"/>
<path d="M0 0 C2.5 1.812 2.5 1.812 3.156 4.426 C3.27 5.213 3.383 6.001 3.5 6.812 C2.547 6.766 1.595 6.72 0.613 6.672 C-1.268 6.618 -1.268 6.618 -3.188 6.562 C-4.429 6.516 -5.67 6.47 -6.949 6.422 C-11.084 6.877 -12.634 7.856 -15.5 10.812 C-16.601 14.116 -16.702 16.711 -16.75 20.188 C-16.776 21.309 -16.802 22.43 -16.828 23.586 C-16.462 27.188 -15.724 28.977 -13.5 31.812 C-9.853 34.339 -6.858 33.941 -2.625 33.363 C0.327 32.598 1.727 31.25 3.5 28.812 C5.838 24.137 5.337 16.544 3.828 11.621 C2.97 9.857 2.97 9.857 1.5 7.812 C4.436 8.791 5.346 9.505 6.75 12.312 C8.373 19.888 8.078 25.931 4.5 32.812 C4.211 33.534 3.923 34.256 3.625 35 C1.574 38.305 -0.783 40.298 -4.547 41.426 C-9.697 42.305 -13.259 41.665 -17.777 39.051 C-21.879 36.102 -23.938 32.518 -25.5 27.812 C-26.382 19.693 -26.371 11.972 -22.109 4.777 C-16.466 -2.113 -8.016 -2.72 0 0 Z " fill="#E4E9FE" transform="translate(534.5,487.1875)"/>
<path d="M0 0 C0 4.29 0 8.58 0 13 C3.3 13 6.6 13 10 13 C10 14.98 10 16.96 10 19 C6.7 19 3.4 19 0 19 C0.081 23.459 0.193 27.917 0.312 32.375 C0.335 33.646 0.358 34.917 0.381 36.227 C0.416 37.438 0.452 38.65 0.488 39.898 C0.514 41.019 0.541 42.14 0.568 43.294 C0.71 44.187 0.853 45.08 1 46 C4.188 48.125 6.265 48.502 10 49 C10 50.98 10 52.96 10 55 C-0.92 55.784 -0.92 55.784 -5.188 52.688 C-7.998 48.52 -8.12 45.504 -8.098 40.582 C-8.094 39.328 -8.091 38.075 -8.088 36.783 C-8.08 35.473 -8.071 34.163 -8.062 32.812 C-8.057 31.477 -8.053 30.142 -8.049 28.807 C-8.037 25.538 -8.021 22.269 -8 19 C-10.31 19 -12.62 19 -15 19 C-15 17.02 -15 15.04 -15 13 C-12.69 13 -10.38 13 -8 13 C-8 9.37 -8 5.74 -8 2 C-2.25 0 -2.25 0 0 0 Z " fill="#E3E8FE" transform="translate(415,474)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C2.98 3 4.96 3 7 3 C7.956 6.998 8.104 10.708 8.062 14.812 C8.053 15.974 8.044 17.135 8.035 18.332 C8.024 19.212 8.012 20.093 8 21 C9.013 20.441 9.013 20.441 10.047 19.871 C10.939 19.398 11.831 18.925 12.75 18.438 C13.632 17.962 14.513 17.486 15.422 16.996 C18.285 15.89 19.2 15.918 22 17 C23.155 17.413 24.31 17.825 25.5 18.25 C30.154 20.577 32.18 22.591 33.871 27.559 C35.222 32.321 36.336 37.09 37 42 C37.495 41.01 37.495 41.01 38 40 C39.98 40.66 41.96 41.32 44 42 C42.96 39.759 41.94 37.926 40.375 36 C39 34 39 34 39 30 C38.34 29.67 37.68 29.34 37 29 C37.66 29 38.32 29 39 29 C39.495 27.515 39.495 27.515 40 26 C40.147 26.922 40.147 26.922 40.297 27.863 C40.446 28.672 40.596 29.48 40.75 30.312 C40.889 31.113 41.028 31.914 41.172 32.738 C42.26 35.709 43.237 36.463 46 38 C49.189 38.95 52.358 39.728 55.6 40.461 C61.935 41.996 61.935 41.996 64 45 C63.916 47.682 63.218 48.756 61.438 50.75 C57.975 52.526 54.757 52.818 51 52 C48 49.625 48 49.625 46 47 C46 46.34 46 45.68 46 45 C43.03 45.495 43.03 45.495 40 46 C40.588 47.64 40.588 47.64 41.188 49.312 C41.408 49.927 41.628 50.542 41.855 51.176 C43.549 53.875 45.071 54.722 48 56 C50.951 56.344 53.847 56.284 56.812 56.188 C57.995 56.167 57.995 56.167 59.201 56.146 C61.134 56.111 63.067 56.058 65 56 C63 58 63 58 60.375 58.125 C59.591 58.084 58.808 58.043 58 58 C58 58.66 58 59.32 58 60 C45.065 58.598 45.065 58.598 40 53 C38.812 50.812 38.812 50.812 38 49 C37.01 48.34 36.02 47.68 35 47 C35.66 46.67 36.32 46.34 37 46 C36.01 45.34 35.02 44.68 34 44 C34.064 44.739 34.129 45.477 34.195 46.238 C33.854 51.058 30.474 53.89 27 57 C23.247 58.766 21.173 59 17 59 C16.67 59.66 16.34 60.32 16 61 C15.67 60.34 15.34 59.68 15 59 C12.678 58.593 10.343 58.256 8 58 C8.33 58.66 8.66 59.32 9 60 C7.515 60.495 7.515 60.495 6 61 C5.505 59.515 5.505 59.515 5 58 C4.67 59.32 4.34 60.64 4 62 C1.625 61.312 1.625 61.312 -1 60 C-2.312 57.375 -2.312 57.375 -3 55 C-3.33 55 -3.66 55 -4 55 C-4 50.333 -4 45.667 -4 41 C-4.084 40.064 -4.168 39.128 -4.254 38.164 C-4.356 36.996 -4.458 35.828 -4.562 34.625 C-4.667 33.462 -4.771 32.3 -4.879 31.102 C-5 28 -5 28 -4 25 C-4.66 24.67 -5.32 24.34 -6 24 C-5.34 24 -4.68 24 -4 24 C-4.021 23.415 -4.042 22.83 -4.063 22.228 C-4.147 19.569 -4.199 16.91 -4.25 14.25 C-4.284 13.33 -4.317 12.409 -4.352 11.461 C-4.451 4.591 -4.451 4.591 -1.984 1.359 C-1.33 0.911 -0.675 0.462 0 0 Z M-1 5 C-1 22.16 -1 39.32 -1 57 C1.31 57 3.62 57 6 57 C6 55.02 6 53.04 6 51 C6.378 51.433 6.755 51.866 7.145 52.312 C10.702 55.912 13.376 57.496 18.5 57.75 C23.443 57.478 26.14 56.111 29.66 52.703 C33.364 47.995 33.336 43.946 33.312 38.125 C33.329 37.335 33.345 36.545 33.361 35.73 C33.365 30.74 32.756 27.32 30 23 C25.813 19.212 22.564 18.473 17.113 18.738 C12.426 19.319 10.34 20.66 7 24 C6.67 17.73 6.34 11.46 6 5 C3.69 5 1.38 5 -1 5 Z M-3 6 C-2 9 -2 9 -2 9 Z M-3 16 C-3 18.64 -3 21.28 -3 24 C-2.67 24 -2.34 24 -2 24 C-2 21.36 -2 18.72 -2 16 C-2.33 16 -2.66 16 -3 16 Z M-3 29 C-2 32 -2 32 -2 32 Z M49 41 C48.67 43.31 48.34 45.62 48 48 C50.906 49.257 52.796 50 56 50 C56.66 50 57.32 50 58 50 C58.33 47.69 58.66 45.38 59 43 C58.01 43 57.02 43 56 43 C55.299 42.814 54.597 42.629 53.875 42.438 C53.256 42.293 52.637 42.149 52 42 C51.67 42.33 51.34 42.66 51 43 C50.34 42.34 49.68 41.68 49 41 Z M41 43 C45 44 45 44 45 44 Z M47 44 C48 46 48 46 48 46 Z M60 44 C60.66 44.66 61.32 45.32 62 46 C62 45.34 62 44.68 62 44 C61.34 44 60.68 44 60 44 Z " fill="#8F9BE4" transform="translate(359,880)"/>
<path d="M0 0 C2.017 3.025 2.259 3.706 2.351 7.117 C2.377 7.941 2.403 8.766 2.43 9.615 C2.448 10.503 2.466 11.39 2.484 12.305 C2.508 13.221 2.533 14.137 2.557 15.081 C2.606 17.019 2.649 18.958 2.688 20.896 C2.75 23.857 2.833 26.817 2.918 29.777 C2.962 31.661 3.005 33.546 3.047 35.43 C3.073 36.313 3.099 37.197 3.125 38.107 C3.199 42.543 3.115 45.967 1 50 C-1.31 49.34 -3.62 48.68 -6 48 C-6 48.66 -6 49.32 -6 50 C-5.34 50.33 -4.68 50.66 -4 51 C-5.485 51.495 -5.485 51.495 -7 52 C-7.199 51.095 -7.199 51.095 -7.402 50.172 C-7.579 49.373 -7.756 48.573 -7.938 47.75 C-8.112 46.961 -8.286 46.172 -8.465 45.359 C-8.948 43.231 -9.462 41.115 -10 39 C-9.34 39 -8.68 39 -8 39 C-8.66 37.02 -9.32 35.04 -10 33 C-9.34 33 -8.68 33 -8 33 C-8.108 32.095 -8.108 32.095 -8.219 31.172 C-8.312 30.373 -8.404 29.573 -8.5 28.75 C-8.593 27.961 -8.686 27.172 -8.781 26.359 C-9 24 -9 24 -9 20 C-9.035 18.846 -9.035 18.846 -9.07 17.668 C-9.188 12.334 -8.754 8.043 -7 3 C-6.216 2.876 -5.433 2.752 -4.625 2.625 C-1.859 2.209 -1.859 2.209 0 0 Z " fill="#E5EBFE" transform="translate(456,482)"/>
<path d="M0 0 C1.583 -0.027 3.167 -0.046 4.75 -0.062 C6.073 -0.08 6.073 -0.08 7.422 -0.098 C9.782 -0.008 11.745 0.331 14 1 C14 1.99 14 2.98 14 4 C16.967 5.648 19.656 6.443 23 7 C23 7.99 23 8.98 23 10 C22.67 10.33 22.34 10.66 22 11 C22.911 15.277 23.78 19.194 26 23 C25.01 23.33 24.02 23.66 23 24 C23.495 27.96 23.495 27.96 24 32 C23.01 32.33 22.02 32.66 21 33 C20.567 33.763 20.134 34.526 19.688 35.312 C17.201 39.272 14.02 41.378 9.562 42.75 C3.184 43.372 -2.418 43.417 -8 40 C-10.428 37.572 -12.446 35.068 -14 32 C-14 30.68 -14 29.36 -14 28 C-14.99 28 -15.98 28 -17 28 C-16.691 25.562 -16.378 23.125 -16.062 20.688 C-15.975 19.997 -15.888 19.307 -15.799 18.596 C-15.408 15.599 -14.958 12.875 -14 10 C-13.34 10.33 -12.68 10.66 -12 11 C-11.918 10.402 -11.835 9.804 -11.75 9.188 C-10.786 6.376 -9.4 5.673 -7 4 C-6.34 3.01 -5.68 2.02 -5 1 C-5 1.66 -5 2.32 -5 3 C-3.35 2.34 -1.7 1.68 0 1 C0 0.67 0 0.34 0 0 Z M-9 7 C-12.846 12.39 -14.862 17.828 -14.484 24.477 C-13.332 30.481 -10.685 35.274 -6.438 39.625 C-1.327 41.669 3.566 41.317 9 41 C9 40.67 9 40.34 9 40 C5.7 39.67 2.4 39.34 -1 39 C1.31 38.34 3.62 37.68 6 37 C6 36.67 6 36.34 6 36 C4.928 35.773 3.855 35.546 2.75 35.312 C-1.435 33.848 -2.701 32.713 -5 29 C-6.78 24.55 -6.522 19.535 -5 15.062 C-4.67 14.382 -4.34 13.701 -4 13 C-3.34 13.66 -2.68 14.32 -2 15 C-2.99 15.33 -3.98 15.66 -5 16 C-5.658 24.562 -5.658 24.562 -2 32 C-0.074 34.332 -0.074 34.332 3.312 34.438 C7.877 33.896 9.124 32.468 12 29 C12.33 28.67 12.66 28.34 13 28 C13.071 26.648 13.084 25.292 13.062 23.938 C13.042 22.638 13.021 21.339 13 20 C12.34 20 11.68 20 11 20 C11.206 19.072 11.413 18.144 11.625 17.188 C12.367 13.848 12.367 13.848 10 11 C13.445 14.296 14.137 16.389 14.562 21.125 C14.688 22.49 14.688 22.49 14.816 23.883 C14.877 24.581 14.938 25.28 15 26 C16.32 25.34 17.64 24.68 19 24 C19.495 26.475 19.495 26.475 20 29 C22.727 24.91 22.49 21.797 22 17 C20.679 10.772 18.162 7.624 13 4 C6.23 0.615 -3.479 2.169 -9 7 Z M12 16 C13 19 13 19 13 19 Z M-16 25 C-15 27 -15 27 -15 27 Z " fill="#CDD4FA" transform="translate(669,896)"/>
<path d="M0 0 C3.96 0 7.92 0 12 0 C14.348 4.697 14.407 8.255 14.656 13.418 C14.77 14.27 14.883 15.122 15 16 C15.66 16.33 16.32 16.66 17 17 C17.625 20.062 17.625 20.062 18 23 C18.74 20.094 19.417 17.178 20.062 14.25 C20.239 13.451 20.416 12.652 20.598 11.828 C20.73 11.225 20.863 10.622 21 10 C21.66 10 22.32 10 23 10 C23.124 9.092 23.247 8.185 23.375 7.25 C23.921 4.41 24.573 2.474 26 0 C27.98 0 29.96 0 32 0 C32.99 2.31 33.98 4.62 35 7 C35.66 7 36.32 7 37 7 C37.337 9.083 37.67 11.166 38 13.25 C38.186 14.41 38.371 15.57 38.562 16.766 C39 20 39 20 39 24 C39.66 24 40.32 24 41 24 C41.266 22.831 41.531 21.662 41.805 20.457 C42.161 18.909 42.518 17.361 42.875 15.812 C43.049 15.044 43.223 14.275 43.402 13.482 C45.855 2.9 45.855 2.9 48.75 0.625 C51.742 -0.206 54.006 0.32 57 1 C56.825 1.58 56.65 2.161 56.469 2.759 C54.422 9.558 52.423 16.37 50.438 23.188 C49.982 24.749 49.982 24.749 49.518 26.342 C48.935 28.342 48.353 30.344 47.773 32.345 C47.302 33.962 46.826 35.577 46.34 37.19 C45.524 39.925 45 42.123 45 45 C42 44 42 44 41 43 C40.34 43.66 39.68 44.32 39 45 C38.01 44.67 37.02 44.34 36 44 C35.125 42.19 35.125 42.19 34.43 39.852 C34.173 39.011 33.917 38.171 33.652 37.305 C33.396 36.42 33.139 35.536 32.875 34.625 C32.479 33.31 32.479 33.31 32.074 31.969 C31.583 30.337 31.096 28.704 30.617 27.069 C30.11 25.369 29.561 23.682 29 22 C28.67 22.99 28.34 23.98 28 25 C27.774 25.654 27.549 26.307 27.316 26.98 C25.62 31.945 24.207 36.894 23 42 C19.7 42 16.4 42 13 42 C11.541 38.888 10.835 36.141 10.5 32.75 C10.253 30.894 10.253 30.894 10 29 C9.34 28.67 8.68 28.34 8 28 C8.33 27.01 8.66 26.02 9 25 C8.34 24.67 7.68 24.34 7 24 C6.367 22.152 6.367 22.152 5.875 19.938 C5.624 18.837 5.624 18.837 5.367 17.715 C5.246 17.149 5.125 16.583 5 16 C4.34 16.66 3.68 17.32 3 18 C3.206 17.051 3.413 16.102 3.625 15.125 C4.324 10.324 3.504 5.59 2 1 C1.34 0.67 0.68 0.34 0 0 Z M4 3 C6.39 15.928 10.223 28.424 14 41 C16.64 41 19.28 41 22 41 C22.177 40.278 22.353 39.556 22.535 38.813 C23.332 35.562 24.135 32.312 24.938 29.062 C25.215 27.926 25.493 26.79 25.779 25.619 C26.047 24.54 26.314 23.46 26.59 22.348 C26.836 21.347 27.082 20.347 27.336 19.317 C28 17 28 17 29 16 C31.31 24.25 33.62 32.5 36 41 C38.64 41 41.28 41 44 41 C45.431 36.233 46.855 31.464 48.272 26.693 C48.756 25.071 49.241 23.449 49.728 21.828 C50.428 19.496 51.121 17.162 51.812 14.828 C52.031 14.105 52.25 13.382 52.475 12.637 C53.475 9.24 54 6.577 54 3 C52.02 3 50.04 3 48 3 C46.053 8.549 44.354 14.138 42.812 19.812 C42.468 21.077 42.124 22.342 41.77 23.645 C40.851 27.076 40.851 27.076 41 31 C40.34 31 39.68 31 39 31 C38.887 30.047 38.773 29.095 38.656 28.113 C37.978 23.374 36.742 18.805 35.5 14.188 C35.274 13.33 35.049 12.472 34.816 11.588 C33.702 7.186 33.702 7.186 32 3 C30.02 3 28.04 3 26 3 C25.207 5.52 24.416 8.041 23.625 10.562 C23.403 11.267 23.182 11.971 22.953 12.697 C22.295 14.796 21.646 16.898 21 19 C20.424 20.87 20.424 20.87 19.836 22.777 C18.833 26.09 18.833 26.09 19 30 C18.34 30 17.68 30 17 30 C16.888 28.886 16.776 27.773 16.66 26.625 C16.035 21.623 14.768 16.854 13.438 12 C13.204 11.13 12.97 10.26 12.729 9.363 C12.157 7.241 11.581 5.12 11 3 C8.69 3 6.38 3 4 3 Z M36 11 C37 14 37 14 37 14 Z " fill="#7D8BDA" transform="translate(690,896)"/>
<path d="M0 0 C0 3.96 0 7.92 0 12 C2.97 12 5.94 12 9 12 C9.33 13.98 9.66 15.96 10 18 C6.7 18 3.4 18 0 18 C0.33 25.92 0.66 33.84 1 42 C3.31 42.66 5.62 43.32 8 44 C8 45.98 8 47.96 8 50 C0.034 50.625 0.034 50.625 -4 48.188 C-7.196 44.692 -7.121 41.787 -7.098 37.238 C-7.093 35.561 -7.093 35.561 -7.088 33.85 C-7.08 32.682 -7.071 31.515 -7.062 30.312 C-7.056 28.544 -7.056 28.544 -7.049 26.74 C-7.037 23.827 -7.021 20.913 -7 18 C-8.98 18 -10.96 18 -13 18 C-13 16.02 -13 14.04 -13 12 C-11.02 12 -9.04 12 -7 12 C-7 8.37 -7 4.74 -7 1 C-2 0 -2 0 0 0 Z " fill="#E0E4FD" transform="translate(485,887)"/>
<path d="M0 0 C1.075 -0.001 2.15 -0.003 3.258 -0.004 C4.39 -0 5.521 0.004 6.688 0.008 C7.819 0.004 8.951 0 10.117 -0.004 C11.192 -0.003 12.267 -0.001 13.375 0 C14.866 0.002 14.866 0.002 16.387 0.003 C18.688 0.133 18.688 0.133 19.688 1.133 C19.785 2.623 19.814 4.118 19.814 5.612 C19.817 6.58 19.82 7.547 19.823 8.544 C19.821 9.614 19.819 10.685 19.817 11.787 C19.819 12.907 19.82 14.028 19.822 15.182 C19.827 18.905 19.824 22.628 19.82 26.352 C19.821 28.926 19.822 31.501 19.823 34.075 C19.825 39.479 19.823 44.883 19.818 50.287 C19.812 56.542 19.814 62.798 19.82 69.054 C19.825 75.061 19.824 81.069 19.821 87.076 C19.821 89.637 19.821 92.199 19.824 94.76 C19.826 98.333 19.822 101.906 19.817 105.479 C19.819 106.549 19.821 107.619 19.823 108.721 C19.819 110.173 19.819 110.173 19.814 111.653 C19.814 112.499 19.814 113.345 19.813 114.217 C19.688 116.133 19.688 116.133 18.688 117.133 C16.836 117.291 14.978 117.384 13.121 117.449 C11.996 117.491 10.872 117.533 9.713 117.576 C8.529 117.615 7.345 117.655 6.125 117.695 C4.937 117.738 3.749 117.782 2.525 117.826 C-0.42 117.933 -3.366 118.035 -6.312 118.133 C-6.312 116.483 -6.312 114.833 -6.312 113.133 C-6.972 112.803 -7.633 112.473 -8.312 112.133 C-7.962 111.597 -7.611 111.06 -7.25 110.508 C-6.01 107.366 -6.748 105.456 -7.312 102.133 C-7.2 100.757 -7.043 99.384 -6.854 98.016 C-6.273 93.217 -6.185 88.511 -6.215 83.682 C-6.216 82.785 -6.218 81.888 -6.219 80.963 C-6.223 79.033 -6.229 77.104 -6.237 75.174 C-6.25 72.123 -6.257 69.073 -6.261 66.022 C-6.273 59.559 -6.292 53.096 -6.312 46.633 C-6.336 39.115 -6.356 31.596 -6.368 24.078 C-6.375 21.066 -6.387 18.054 -6.4 15.042 C-6.404 13.211 -6.407 11.38 -6.41 9.549 C-6.415 8.706 -6.42 7.864 -6.426 6.996 C-6.426 5.041 -6.375 3.086 -6.312 1.133 C-4.597 -0.583 -2.339 0.003 0 0 Z M-4.312 2.133 C-4.312 39.423 -4.312 76.713 -4.312 115.133 C2.947 115.133 10.207 115.133 17.688 115.133 C17.688 77.843 17.688 40.553 17.688 2.133 C10.428 2.133 3.168 2.133 -4.312 2.133 Z " fill="#7388D2" transform="translate(579.3125,108.8671875)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.66 10 1.32 10 2 C11.516 2.062 11.516 2.062 13.062 2.125 C17.859 2.761 20.675 5.695 24 9 C23.67 9.66 23.34 10.32 23 11 C22.134 10.216 21.268 9.433 20.375 8.625 C15.214 4.388 10.091 3.529 3.531 3.688 C-2.185 4.393 -5.878 6.59 -9.438 11.062 C-11.92 15.729 -12.183 19.576 -12.188 24.75 C-12.2 25.441 -12.212 26.132 -12.225 26.844 C-12.238 31.595 -11.494 35.488 -10 40 C-11.485 40.495 -11.485 40.495 -13 41 C-13.144 40.113 -13.289 39.226 -13.438 38.312 C-13.623 37.219 -13.809 36.126 -14 35 C-14.186 33.67 -14.186 33.67 -14.375 32.312 C-15.076 29.718 -15.953 28.691 -18 27 C-17.938 27.908 -17.876 28.815 -17.812 29.75 C-18.017 33.29 -18.646 34.449 -21 37 C-20.835 36.258 -20.67 35.515 -20.5 34.75 C-19.441 26.811 -18.596 18.082 -23 11 C-26.629 6.922 -30.782 4.368 -36.219 3.746 C-42.507 3.445 -47.712 4.471 -53 8 C-59.143 15.525 -58.644 23.78 -58 33 C-56.776 37.819 -54.868 40.28 -51.062 43.438 C-45.376 46.339 -39.116 47.033 -32.883 45.469 C-28.608 43.985 -25.387 42.07 -22 39 C-23.512 43.233 -26.157 44.857 -30 47 C-33.375 47.75 -33.375 47.75 -36 48 C-35.67 48.99 -35.34 49.98 -35 51 C-35.66 51 -36.32 51 -37 51 C-37 50.01 -37 49.02 -37 48 C-37.926 48.104 -37.926 48.104 -38.871 48.211 C-45.191 48.615 -49.005 47.583 -54.09 43.781 C-58.176 39.971 -60.109 35.199 -62 30 C-62.33 31.65 -62.66 33.3 -63 35 C-63.99 35 -64.98 35 -66 35 C-66.086 34.12 -66.173 33.239 -66.262 32.332 C-66.402 31.171 -66.543 30.009 -66.688 28.812 C-66.815 27.664 -66.943 26.515 -67.074 25.332 C-68.266 21.042 -69.406 19.654 -73 17 C-76.673 15.488 -80.43 14.39 -84.25 13.312 C-86.257 12.732 -88.262 12.145 -90.266 11.551 C-91.15 11.3 -92.035 11.049 -92.946 10.79 C-95 10 -95 10 -96 8 C-95.385 8.084 -94.77 8.168 -94.137 8.254 C-93.328 8.356 -92.52 8.458 -91.688 8.562 C-90.887 8.667 -90.086 8.771 -89.262 8.879 C-86.832 9.148 -86.832 9.148 -84 8 C-84 8.99 -84 9.98 -84 11 C-79.71 11.99 -75.42 12.98 -71 14 C-70.67 13.34 -70.34 12.68 -70 12 C-69.876 13.052 -69.876 13.052 -69.75 14.125 C-68.827 17.665 -67.064 19.997 -65 23 C-64.284 24.646 -63.597 26.307 -63 28 C-61.108 26.108 -61.162 22.849 -60.688 20.312 C-59.582 14.943 -58.552 11.165 -55 7 C-55.495 6.01 -55.495 6.01 -56 5 C-54.906 4.805 -54.906 4.805 -53.789 4.605 C-51.185 4.04 -48.869 3.234 -46.375 2.312 C-39.619 0.242 -33.602 1.479 -27.188 4.188 C-25.112 5.907 -23.762 7.567 -22.125 9.688 C-19.629 12.781 -19.368 12.958 -15.125 13.438 C-14.094 13.293 -13.062 13.149 -12 13 C-11.959 12.443 -11.918 11.886 -11.875 11.312 C-10.073 6.549 -5.403 4.12 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z M1 1 C5 2 5 2 5 2 Z M-15 14 C-15.66 14.66 -16.32 15.32 -17 16 C-17.33 16 -17.66 16 -18 16 C-18 18.31 -18 20.62 -18 23 C-15.353 20.353 -14.401 17.438 -13 14 C-13.66 14 -14.32 14 -15 14 Z M-15 18 C-14 20 -14 20 -14 20 Z " fill="#7F91DD" transform="translate(685,483)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1 1.68 1 1 1 C1 4.63 1 8.26 1 12 C3.296 13.148 5.077 13.308 7.625 13.562 C8.442 13.646 9.26 13.73 10.102 13.816 C10.728 13.877 11.355 13.938 12 14 C11.825 19.328 11.825 19.328 11 22 C8.125 23.875 8.125 23.875 5 25 C4.01 25.66 3.02 26.32 2 27 C1.75 29.59 1.673 31.919 1.75 34.5 C1.758 35.534 1.758 35.534 1.766 36.59 C1.783 40.423 1.783 40.423 3 44 C5.31 44 7.62 44 10 44 C10.495 47.96 10.495 47.96 11 52 C7.453 54.295 4.126 56.013 0 57 C-3.75 54.5 -3.75 54.5 -5 52 C-5.99 51.505 -5.99 51.505 -7 51 C-7.99 50.505 -7.99 50.505 -9 50 C-8.959 49.237 -8.918 48.474 -8.875 47.688 C-8.779 44.773 -8.779 44.773 -11 42 C-11.142 39.233 -11.188 36.573 -11.125 33.812 C-11.116 33.063 -11.107 32.313 -11.098 31.541 C-11.074 29.694 -11.038 27.847 -11 26 C-11.568 26.309 -12.137 26.619 -12.723 26.938 C-16.375 28.641 -19.968 29.458 -24 29 C-25.735 27.059 -27.405 25.057 -29 23 C-33.457 22.385 -35.235 22.49 -39 25 C-38.34 25.66 -37.68 26.32 -37 27 C-37.66 27 -38.32 27 -39 27 C-39.33 28.32 -39.66 29.64 -40 31 C-40.33 30.67 -40.66 30.34 -41 30 C-41.33 31.65 -41.66 33.3 -42 35 C-40.515 34.505 -40.515 34.505 -39 34 C-38.67 36.31 -38.34 38.62 -38 41 C-38.66 41 -39.32 41 -40 41 C-38.251 43.083 -38.251 43.083 -36 45 C-32.105 45.211 -32.105 45.211 -29 44 C-28.01 44 -27.02 44 -26 44 C-28 47 -28 47 -31.25 47.938 C-35.91 48.015 -37.573 47.109 -41 44 C-43.569 39.095 -43.739 33.427 -43 28 C-41.674 24.891 -40.098 22.549 -37 21 C-32.556 20.477 -29.129 20.174 -25.438 22.875 C-24 25 -24 25 -24 27 C-20.535 26.505 -20.535 26.505 -17 26 C-19.485 21.215 -21.48 18.139 -26 15 C-24.68 15 -23.36 15 -22 15 C-22 15.66 -22 16.32 -22 17 C-21.361 17.247 -20.721 17.495 -20.062 17.75 C-18 19 -18 19 -17.25 21.125 C-17.168 21.744 -17.085 22.362 -17 23 C-16.34 23 -15.68 23 -15 23 C-15.227 22.092 -15.454 21.185 -15.688 20.25 C-16 17 -16 17 -14.562 14.625 C-14.047 14.089 -13.531 13.553 -13 13 C-12.01 13.33 -11.02 13.66 -10 14 C-10.278 13.196 -10.278 13.196 -10.562 12.375 C-11.136 9.261 -10.843 7.049 -10 4 C-7.746 1.585 -6.623 1.029 -3.312 0.875 C-2.549 0.916 -1.786 0.957 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M-7 4 C-7 7.63 -7 11.26 -7 15 C-8.98 15 -10.96 15 -13 15 C-13 16.98 -13 18.96 -13 21 C-11.02 21 -9.04 21 -7 21 C-7.016 21.731 -7.031 22.462 -7.048 23.216 C-7.11 26.539 -7.149 29.863 -7.188 33.188 C-7.213 34.338 -7.238 35.488 -7.264 36.674 C-7.273 37.786 -7.283 38.897 -7.293 40.043 C-7.309 41.064 -7.324 42.085 -7.341 43.137 C-6.942 46.487 -5.998 48.303 -4 51 C-0.796 53.136 0.019 53.231 3.688 53.125 C4.496 53.107 5.304 53.089 6.137 53.07 C6.752 53.047 7.366 53.024 8 53 C8 51.02 8 49.04 8 47 C5.69 46.34 3.38 45.68 1 45 C0.67 37.08 0.34 29.16 0 21 C3.3 21 6.6 21 10 21 C9.67 19.02 9.34 17.04 9 15 C6.03 15 3.06 15 0 15 C0 11.04 0 7.08 0 3 C-2.523 2.935 -2.523 2.935 -7 4 Z M-15 18 C-14 21 -14 21 -14 21 Z M-9 23 C-9 24.98 -9 26.96 -9 29 C-8.67 29 -8.34 29 -8 29 C-8 27.02 -8 25.04 -8 23 C-8.33 23 -8.66 23 -9 23 Z M-16 24 C-15.34 24.66 -14.68 25.32 -14 26 C-14 25.34 -14 24.68 -14 24 C-14.66 24 -15.32 24 -16 24 Z M-9 30 C-8 34 -8 34 -8 34 Z M-41 36 C-41 37.65 -41 39.3 -41 41 C-40.67 41 -40.34 41 -40 41 C-40 39.35 -40 37.7 -40 36 C-40.33 36 -40.66 36 -41 36 Z " fill="#7B8ADB" transform="translate(485,884)"/>
<path d="M0 0 C6 0 6 0 8.688 1.688 C13.576 6.576 16.645 13.226 18 20 C16.68 20 15.36 20 14 20 C13.67 20.99 13.34 21.98 13 23 C12.161 21.879 11.329 20.753 10.5 19.625 C10.036 18.999 9.572 18.372 9.094 17.727 C8 16 8 16 8 14 C7.34 14 6.68 14 6 14 C6 26.54 6 39.08 6 52 C4.02 52 2.04 52 0 52 C0 34.84 0 17.68 0 0 Z " fill="#E4E8FC" transform="translate(610,885)"/>
<path d="M0 0 C2.053 2.053 4.102 4.105 6.125 6.188 C7.879 8.065 7.879 8.065 10 9 C10.33 8.34 10.66 7.68 11 7 C11.577 7.186 12.155 7.371 12.75 7.562 C15.207 8.04 16.644 7.757 19 7 C20.255 13.003 20.031 18.832 19.873 24.924 C19.804 30.04 20.336 34.153 22 39 C22.722 38.814 23.444 38.629 24.188 38.438 C27 38 27 38 32 39 C32.33 31.74 32.66 24.48 33 17 C32.34 17 31.68 17 31 17 C31.33 16.34 31.66 15.68 32 15 C31.505 14.505 31.505 14.505 31 14 C31.66 11.69 32.32 9.38 33 7 C36.3 7 39.6 7 43 7 C43 20.86 43 34.72 43 49 C40.525 49.495 40.525 49.495 38 50 C37.505 50.99 37.505 50.99 37 52 C36.34 52 35.68 52 35 52 C35 51.34 35 50.68 35 50 C34.01 49.67 33.02 49.34 32 49 C31.67 48.67 31.34 48.34 31 48 C30.01 48.495 30.01 48.495 29 49 C24.668 50.444 20.347 51.141 16.152 49.102 C14.5 48 14.5 48 12 46 C11.01 46.495 11.01 46.495 10 47 C10 44.69 10 42.38 10 40 C9.34 40 8.68 40 8 40 C7.228 34.593 6.902 29.461 7 24 C7.66 24 8.32 24 9 24 C9 20.04 9 16.08 9 12 C7.68 11.34 6.36 10.68 5 10 C4.01 10.66 3.02 11.32 2 12 C2.33 12.66 2.66 13.32 3 14 C1.68 14.33 0.36 14.66 -1 15 C-1 14.34 -1 13.68 -1 13 C-1.99 12.67 -2.98 12.34 -4 12 C-5.068 10.035 -6.066 8.031 -7 6 C-7.66 5.01 -8.32 4.02 -9 3 C-5.839 4.37 -5.007 4.989 -3 8 C0.084 8.25 0.084 8.25 3 8 C2.505 7.051 2.01 6.102 1.5 5.125 C0 2 0 2 0 0 Z M37 8 C41 9 41 9 41 9 Z M11 10 C10.912 14.521 10.859 19.041 10.812 23.562 C10.787 24.839 10.762 26.116 10.736 27.432 C10.494 38.111 10.494 38.111 15.879 46.91 C19.773 48.911 23.747 48.632 28 48 C31.149 46.404 31.149 46.404 33 44 C33 43.01 33 42.02 33 41 C33.66 41 34.32 41 35 41 C35 43.31 35 45.62 35 48 C37.31 48 39.62 48 42 48 C42 35.46 42 22.92 42 10 C39.69 10 37.38 10 35 10 C34.98 10.726 34.96 11.452 34.94 12.2 C34.844 15.508 34.735 18.817 34.625 22.125 C34.594 23.267 34.563 24.409 34.531 25.586 C34.493 26.695 34.454 27.803 34.414 28.945 C34.383 29.961 34.351 30.977 34.319 32.024 C33.974 35.242 33.124 37.974 32 41 C28.04 41 24.08 41 20 41 C19.093 37.371 18.806 34.417 18.684 30.703 C18.642 29.498 18.6 28.293 18.557 27.051 C18.517 25.797 18.478 24.542 18.438 23.25 C18.394 21.98 18.351 20.711 18.307 19.402 C18.201 16.268 18.099 13.134 18 10 C15.69 10 13.38 10 11 10 Z " fill="#8F9BE5" transform="translate(308,889)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 19.47 7 38.94 7 59 C4.69 59 2.38 59 0 59 C0 39.53 0 20.06 0 0 Z " fill="#E5EAFE" transform="translate(433,470)"/>
<path d="M0 0 C0.728 0.006 1.456 0.011 2.207 0.017 C4.53 0.04 6.853 0.09 9.176 0.141 C10.752 0.161 12.328 0.179 13.904 0.195 C17.766 0.239 21.627 0.308 25.488 0.391 C25.642 15.281 25.79 30.172 25.931 45.062 C25.997 51.976 26.064 58.891 26.136 65.805 C26.206 72.477 26.27 79.148 26.331 85.82 C26.355 88.367 26.381 90.913 26.409 93.459 C26.447 97.023 26.479 100.588 26.51 104.153 C26.523 105.207 26.536 106.26 26.549 107.346 C26.584 112.15 26.513 116.668 25.488 121.391 C24.828 120.071 24.168 118.751 23.488 117.391 C17.878 117.391 12.268 117.391 6.488 117.391 C6.158 118.051 5.828 118.711 5.488 119.391 C5.488 118.731 5.488 118.071 5.488 117.391 C4.498 117.391 3.508 117.391 2.488 117.391 C2.488 102.871 2.488 88.351 2.488 73.391 C2.818 73.391 3.148 73.391 3.488 73.391 C3.488 87.251 3.488 101.111 3.488 115.391 C10.418 115.391 17.348 115.391 24.488 115.391 C24.488 78.101 24.488 40.811 24.488 2.391 C14.258 2.391 4.028 2.391 -6.512 2.391 C-7.502 6.021 -8.492 9.651 -9.512 13.391 C-10.075 15.248 -10.653 17.102 -11.258 18.947 C-11.565 19.888 -11.872 20.83 -12.188 21.8 C-12.671 23.266 -12.671 23.266 -13.164 24.762 C-13.505 25.805 -13.847 26.849 -14.199 27.924 C-15.28 31.226 -16.364 34.527 -17.449 37.828 C-18.173 40.037 -18.896 42.246 -19.619 44.455 C-22.891 54.442 -26.181 64.423 -29.512 74.391 C-30.388 71.204 -30.52 69.56 -29.512 66.391 C-29.187 65.305 -28.862 64.22 -28.527 63.102 C-28.125 61.804 -27.722 60.507 -27.316 59.211 C-26.988 58.16 -26.988 58.16 -26.653 57.087 C-25.041 51.97 -23.336 46.89 -21.582 41.82 C-21.092 40.396 -20.603 38.971 -20.113 37.547 C-19.369 35.383 -18.621 33.222 -17.861 31.063 C-17.11 28.924 -16.376 26.779 -15.645 24.633 C-15.299 23.672 -15.299 23.672 -14.947 22.691 C-13.559 18.555 -13.461 15.618 -14.512 11.391 C-13.522 11.721 -12.532 12.051 -11.512 12.391 C-10.914 9.628 -10.512 7.23 -10.512 4.391 C-8.204 -0.454 -4.829 -0.141 0 0 Z " fill="#556FC4" transform="translate(526.51171875,108.609375)"/>
<path d="M0 0 C2.671 2.063 4.627 4.336 6.438 7.145 C7.015 8.031 7.592 8.918 8.188 9.832 C8.847 9.832 9.507 9.832 10.188 9.832 C10.387 11.151 10.387 11.151 10.59 12.496 C11.128 15.501 11.884 18.285 12.75 21.207 C16.38 35.637 14.788 51.558 7.621 64.68 C3.238 71.26 -3.205 74.962 -10.812 76.832 C-19.743 77.787 -26.968 77.241 -34.344 72.051 C-42.342 65.558 -46.071 57.895 -47.812 47.832 C-48.053 43.959 -48.044 40.086 -48.062 36.207 C-48.083 35.177 -48.104 34.147 -48.125 33.086 C-48.177 22.668 -46.203 12.326 -39.812 3.832 C-28.673 -7.092 -13.128 -8.46 0 0 Z M-39.121 5.328 C-43.893 11.46 -46.192 18.046 -45.812 25.832 C-46.142 26.492 -46.473 27.152 -46.812 27.832 C-47.551 41.455 -47.093 54.351 -38.812 65.832 C-33.85 71.257 -28.244 74.451 -20.91 75.266 C-19.565 75.289 -18.22 75.29 -16.875 75.27 C-16.172 75.263 -15.469 75.256 -14.745 75.249 C-7.446 75.023 -3.402 73.128 1.938 68.395 C3.051 67.126 3.051 67.126 4.188 65.832 C4.847 65.1 5.507 64.368 6.188 63.613 C13.625 53.27 13.401 37.018 12.188 24.832 C10.704 16.559 7.841 10.07 2.188 3.832 C1.197 4.162 0.207 4.492 -0.812 4.832 C-1.308 2.852 -1.308 2.852 -1.812 0.832 C-3.132 1.162 -4.452 1.492 -5.812 1.832 C-5.812 0.842 -5.812 -0.148 -5.812 -1.168 C-16.886 -6.403 -31.054 -3.895 -39.121 5.328 Z " fill="#6379CB" transform="translate(457.8125,294.16796875)"/>
<path d="M0 0 C4.308 -0.12 7.496 0.187 11.312 2.25 C14.167 3.362 14.167 3.362 17.312 1.438 C21.17 -0.066 21.554 -0.185 25.125 1.188 C26.421 1.783 27.713 2.386 29 3 C29.949 3.289 30.898 3.577 31.875 3.875 C34.728 5.385 35.005 7.016 36 10 C36.66 10.33 37.32 10.66 38 11 C37.67 22.88 37.34 34.76 37 47 C34.03 47 31.06 47 28 47 C28.33 47.99 28.66 48.98 29 50 C28.01 49.67 27.02 49.34 26 49 C25.971 45.896 25.953 42.792 25.938 39.688 C25.929 38.806 25.921 37.925 25.912 37.018 C25.907 35.746 25.907 35.746 25.902 34.449 C25.894 33.279 25.894 33.279 25.886 32.085 C26 30 26 30 27 27 C26.34 26.67 25.68 26.34 25 26 C25 21.71 25 17.42 25 13 C23.886 13.021 22.773 13.041 21.625 13.062 C18 13 18 13 16 12 C13.902 12.399 13.902 12.399 12 13 C13 11.5 13 11.5 15 10 C20.466 9.295 20.466 9.295 23 10 C26.352 12.682 27.853 14.117 28.568 18.413 C28.541 19.686 28.515 20.958 28.488 22.27 C28.481 22.951 28.474 23.632 28.467 24.333 C28.439 26.494 28.376 28.652 28.312 30.812 C28.287 32.283 28.265 33.754 28.244 35.225 C28.189 38.817 28.103 42.408 28 46 C30.64 46 33.28 46 36 46 C35.956 41.731 35.885 37.463 35.792 33.195 C35.764 31.746 35.743 30.297 35.729 28.848 C35.643 17.356 35.643 17.356 31 7 C27.594 3.973 24.537 3.522 19.996 3.711 C16.86 4.165 14.62 5.254 12 7 C10.595 9.051 10.595 9.051 10 11 C9.34 11 8.68 11 8 11 C8 9.02 8 7.04 8 5 C5.69 4.67 3.38 4.34 1 4 C1 17.86 1 31.72 1 46 C3.31 46 5.62 46 8 46 C7.99 45.203 7.979 44.406 7.968 43.585 C7.927 39.973 7.901 36.362 7.875 32.75 C7.858 31.496 7.841 30.241 7.824 28.949 C7.818 27.744 7.811 26.539 7.805 25.297 C7.794 24.187 7.784 23.076 7.773 21.933 C7.995 19.06 8.507 17.428 10 15 C11.118 18.539 11.047 21.717 10.914 25.41 C10.87 26.647 10.826 27.884 10.781 29.158 C10.73 30.447 10.678 31.735 10.625 33.062 C10.581 34.355 10.537 35.647 10.492 36.979 C10.13 46.609 10.13 46.609 9 50 C8.34 49.67 7.68 49.34 7 49 C4.844 49 3.076 49.447 1 50 C-1.699 45.652 -1.025 41.626 -0.777 36.68 C-0.851 35.795 -0.924 34.911 -1 34 C-1.99 33.34 -2.98 32.68 -4 32 C-4 31.34 -4 30.68 -4 30 C-3.34 30 -2.68 30 -2 30 C-0.967 25.116 -0.967 25.116 -2.062 20.375 C-3.328 17.17 -2.307 16.121 -1 13 C-0.888 10.968 -0.938 8.972 -0.996 6.938 C-1.001 4.533 -0.582 2.327 0 0 Z M-1 15 C-1 16.98 -1 18.96 -1 21 C-0.67 21 -0.34 21 0 21 C0 19.02 0 17.04 0 15 C-0.33 15 -0.66 15 -1 15 Z M3 47 C4 49 4 49 4 49 Z " fill="#7386D8" transform="translate(718,483)"/>
<path d="M0 0 C-2.544 4.705 -2.544 4.705 -4.938 6.25 C-8.731 9.469 -10.542 14.267 -11.355 19.121 C-11.561 24.141 -10.364 27.695 -8 32 C-5.93 33.783 -5.93 33.783 -4 35 C-4 35.66 -4 36.32 -4 37 C-3.34 37.33 -2.68 37.66 -2 38 C-2.66 38.66 -3.32 39.32 -4 40 C-2.02 40.66 -0.04 41.32 2 42 C2 42.33 2 42.66 2 43 C-0.167 42.809 -2.322 42.584 -4.48 42.312 C-6.061 42.116 -7.648 41.964 -9.238 41.875 C-11.555 41.596 -13.013 41.238 -15 40 C-20.276 32.331 -19.932 21.9 -19 13 C-17.527 7.637 -14.712 3.988 -10 1 C-6.241 -0.973 -4.081 -1.166 0 0 Z " fill="#DEE5FD" transform="translate(691,487)"/>
<path d="M0 0 C1.812 0.5 1.812 0.5 4 2 C8.106 9.626 8.35 18.941 6.133 27.25 C4.591 30.993 2.65 33.315 -1 35 C-5.704 35.722 -10.122 36.08 -14.336 33.656 C-14.885 33.11 -15.434 32.563 -16 32 C-16 31.01 -16 30.02 -16 29 C-15.01 29 -14.02 29 -13 29 C-13.928 28.691 -13.928 28.691 -14.875 28.375 C-17 27 -17 27 -18.25 24.562 C-19.512 18.569 -20.62 11.773 -17.125 6.375 C-14.97 3.967 -14.13 3.133 -10.977 2.762 C-4.633 2.562 -4.633 2.562 -1.562 4.688 C0.041 7.061 1.095 9.286 2 12 C1.562 9.374 1.127 7.24 -0.125 4.875 C-1 3 -1 3 0 0 Z M-15 5.938 C-17.523 9.801 -17.164 11.802 -16.395 16.207 C-16.264 16.799 -16.134 17.39 -16 18 C-16.99 18.495 -16.99 18.495 -18 19 C-16.716 22.497 -16.716 22.497 -15 26 C-14.01 26.33 -13.02 26.66 -12 27 C-11.34 26.67 -10.68 26.34 -10 26 C-10 26.66 -10 27.32 -10 28 C-6.694 27.449 -3.8 26.9 -1 25 C0.145 21.757 0.103 18.662 0.062 15.25 C0.053 14.265 0.044 13.28 0.035 12.266 C0.024 11.518 0.012 10.77 0 10 C-0.99 10 -1.98 10 -3 10 C-2.67 9.01 -2.34 8.02 -2 7 C-3.056 5.354 -3.056 5.354 -5 4 C-9.318 3.36 -11.308 3.553 -15 5.938 Z " fill="#D7DDFD" transform="translate(384,902)"/>
<path d="M0 0 C0.985 0.009 1.97 0.018 2.984 0.027 C4.106 0.045 4.106 0.045 5.25 0.062 C5.25 0.393 5.25 0.722 5.25 1.062 C3.856 1.209 3.856 1.209 2.434 1.359 C-4.614 2.212 -9.871 3.054 -15.75 7.062 C-16.41 7.062 -17.07 7.062 -17.75 7.062 C-17.997 7.599 -18.245 8.135 -18.5 8.688 C-19.75 11.062 -19.75 11.062 -21.688 13.688 C-26.31 21.252 -27.255 29.087 -27.125 37.812 C-27.119 38.765 -27.113 39.717 -27.107 40.698 C-26.966 48.447 -25.892 55.592 -23.75 63.062 C-23.09 63.062 -22.43 63.062 -21.75 63.062 C-21.399 63.764 -21.049 64.465 -20.688 65.188 C-17.684 69.644 -12.938 72.98 -7.816 74.719 C0.075 76.031 9.011 75.75 16.25 72.062 C16.745 70.577 16.745 70.577 17.25 69.062 C17.58 68.733 17.91 68.403 18.25 68.062 C17.92 67.403 17.59 66.743 17.25 66.062 C18.57 66.062 19.89 66.062 21.25 66.062 C22.24 63.093 23.23 60.123 24.25 57.062 C22.974 57.002 21.698 56.941 20.383 56.879 C18.714 56.795 17.044 56.71 15.375 56.625 C14.112 56.566 14.112 56.566 12.824 56.506 C12.019 56.464 11.213 56.422 10.383 56.379 C9.267 56.324 9.267 56.324 8.129 56.268 C6.25 56.062 6.25 56.062 4.25 55.062 C4.25 51.102 4.25 47.142 4.25 43.062 C3.26 42.567 3.26 42.567 2.25 42.062 C2.91 42.062 3.57 42.062 4.25 42.062 C4.229 40.763 4.209 39.464 4.188 38.125 C4.166 36.771 4.179 35.415 4.25 34.062 C5.25 33.062 5.25 33.062 7.117 32.942 C8.339 32.946 8.339 32.946 9.586 32.949 C10.968 32.949 10.968 32.949 12.377 32.949 C13.378 32.954 14.379 32.96 15.41 32.965 C16.43 32.966 17.449 32.968 18.5 32.969 C21.771 32.975 25.042 32.987 28.312 33 C30.524 33.005 32.736 33.01 34.947 33.014 C40.382 33.025 45.816 33.041 51.25 33.062 C51.25 33.392 51.25 33.722 51.25 34.062 C36.4 34.062 21.55 34.062 6.25 34.062 C6.25 40.662 6.25 47.262 6.25 54.062 C13.18 54.062 20.11 54.062 27.25 54.062 C24.901 63.459 22.238 69.52 14.25 75.062 C6.512 78.28 -3.208 78.301 -11.355 76.48 C-17.879 73.629 -23.044 67.568 -26.105 61.277 C-31.131 48.088 -31.897 31.556 -26.645 18.266 C-22.586 9.825 -18.318 4.985 -9.75 1.062 C-6.47 -0.024 -3.443 -0.041 0 0 Z " fill="#647BCC" transform="translate(782.75,128.9375)"/>
<path d="M0 0 C0 38.61 0 77.22 0 117 C-8.58 117 -17.16 117 -26 117 C-28.829 114.171 -29.376 113.619 -30 110 C-28.5 111.375 -28.5 111.375 -27 113 C-27 113.66 -27 114.32 -27 115 C-18.42 115 -9.84 115 -1 115 C-1 77.71 -1 40.42 -1 2 C-7.93 2 -14.86 2 -22 2 C-22 28.07 -22 54.14 -22 81 C-22.99 80.01 -23.98 79.02 -25 78 C-24.713 75.993 -24.369 73.994 -24 72 C-24.148 69.662 -24.43 67.371 -24.746 65.051 C-25.133 61.927 -24.899 59.318 -24.438 56.188 C-23.96 52.597 -24.2 49.557 -25 46 C-24.34 46 -23.68 46 -23 46 C-23.165 45.051 -23.33 44.102 -23.5 43.125 C-24.071 39.21 -24.019 35.386 -23.965 31.438 C-23.997 29.225 -24.204 27.18 -24.562 25 C-25 22 -25 22 -24 20 C-23.902 17.156 -23.861 14.343 -23.875 11.5 C-23.871 10.715 -23.867 9.93 -23.863 9.121 C-23.762 4.927 -23.762 4.927 -25 1 C-17.54 -2.73 -8.34 0 0 0 Z " fill="#566FC7" transform="translate(713,109)"/>
<path d="M0 0 C8.477 6.351 11.823 15.116 14.148 25.312 C14.21 26.447 14.272 27.581 14.336 28.75 C14.386 29.606 14.436 30.462 14.488 31.344 C14.868 42.883 13.334 55.977 5.43 64.902 C-0.763 70.931 -6.67 73.215 -15.164 74.125 C-24.327 73.143 -32.234 70.218 -38.164 62.898 C-46.21 50.877 -47.382 35.414 -44.914 21.438 C-42.725 12.214 -38.725 3.599 -30.48 -1.637 C-20.375 -6.366 -9.402 -5.956 0 0 Z M-37.664 6.75 C-45.463 18.565 -45.335 34.361 -43.343 47.937 C-41.972 54.217 -39.319 59.34 -34.664 63.75 C-34.004 63.75 -33.344 63.75 -32.664 63.75 C-32.478 64.348 -32.293 64.946 -32.102 65.562 C-29.706 69.207 -25.836 70.078 -21.797 71.328 C-19.133 71.855 -16.628 71.845 -13.914 71.812 C-12.929 71.803 -11.944 71.794 -10.93 71.785 C-10.182 71.774 -9.434 71.762 -8.664 71.75 C-8.664 70.76 -8.664 69.77 -8.664 68.75 C-8.334 69.41 -8.004 70.07 -7.664 70.75 C-3.178 69.498 -0.071 67.956 3.336 64.75 C3.336 64.09 3.336 63.43 3.336 62.75 C4.326 62.42 5.316 62.09 6.336 61.75 C13.844 51.516 13.561 35.895 11.949 23.863 C11.464 21.557 11.464 21.557 9.336 19.75 C9.831 18.265 9.831 18.265 10.336 16.75 C8.41 10.885 4.469 4.577 -0.914 1.438 C-1.492 1.211 -2.069 0.984 -2.664 0.75 C-3.159 0.255 -3.159 0.255 -3.664 -0.25 C-16.21 -5.446 -28.553 -3.817 -37.664 6.75 Z " fill="#6C81CE" transform="translate(376.6640625,133.25)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.467 7.233 4.467 7.233 2.25 10.875 C-1.741 14.644 -6.02 14.381 -11.258 14.367 C-15.471 13.803 -17.162 12.068 -20 9 C-20 9.99 -20 10.98 -20 12 C-12.761 15.235 -6.771 15.091 1 14 C1 17 1 17 0 19 C3.619 17.273 6.876 15.523 10 13 C9.625 15.812 9.625 15.812 8 19 C2.29 21.985 -4.323 22.407 -10.652 22.789 C-13.152 22.923 -13.152 22.923 -16 24 C-16.66 23.34 -17.32 22.68 -18 22 C-20.75 20.032 -20.75 20.032 -23.016 19.145 C-25.535 17.692 -26.367 16.227 -27.75 13.688 C-28.178 12.928 -28.606 12.169 -29.047 11.387 C-30.099 8.751 -30.179 6.812 -30 4 C-28.398 3.778 -26.793 3.573 -25.188 3.375 C-24.294 3.259 -23.401 3.143 -22.48 3.023 C-20 3 -20 3 -17 5 C-16.34 5 -15.68 5 -15 5 C-14.34 5.33 -13.68 5.66 -13 6 C-13.33 6.99 -13.66 7.98 -14 9 C-12.188 11.243 -12.188 11.243 -9.617 11.195 C-8.26 11.161 -8.26 11.161 -6.875 11.125 C-5.965 11.107 -5.055 11.089 -4.117 11.07 C-3.419 11.047 -2.72 11.024 -2 11 C-2 10.34 -2 9.68 -2 9 C-1.01 9 -0.02 9 1 9 C1.269 4.349 1.269 4.349 0 0 Z M1 13 C3 14 3 14 3 14 Z " fill="#B0B9F0" transform="translate(301,917)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 38.61 1 77.22 1 117 C8.59 117 16.18 117 24 117 C24 90.93 24 64.86 24 38 C28.167 42.167 30.516 46.031 33.312 51.125 C33.78 51.949 34.247 52.772 34.729 53.621 C35.165 54.409 35.601 55.196 36.051 56.008 C36.45 56.726 36.848 57.444 37.259 58.184 C37.504 58.783 37.748 59.383 38 60 C37.505 60.99 37.505 60.99 37 62 C34 58.25 34 58.25 34 56 C33.01 55.505 33.01 55.505 32 55 C31.066 53.344 30.199 51.65 29.375 49.938 C28.929 49.018 28.483 48.099 28.023 47.152 C27.686 46.442 27.348 45.732 27 45 C26.403 47.763 26 50.16 26 53 C26 75 26 97 26 119 C22.919 120.541 19.69 120.143 16.312 120.125 C15.617 120.129 14.922 120.133 14.205 120.137 C9.705 120.128 5.44 119.733 1 119 C-1.115 113.796 -1.279 109.293 -1.243 103.776 C-1.246 102.411 -1.246 102.411 -1.249 101.019 C-1.252 98.016 -1.242 95.014 -1.23 92.012 C-1.229 89.909 -1.229 87.806 -1.229 85.703 C-1.228 81.29 -1.219 76.878 -1.206 72.466 C-1.189 66.856 -1.185 61.246 -1.186 55.636 C-1.185 51.287 -1.18 46.938 -1.173 42.589 C-1.17 40.523 -1.168 38.458 -1.167 36.393 C-1.158 24.224 -0.882 12.138 0 0 Z " fill="#95A4E2" transform="translate(638,272)"/>
<path d="M0 0 C-0.66 0.33 -1.32 0.66 -2 1 C-4.009 0.018 -6.008 -0.984 -8 -2 C-16.445 -4.445 -23.35 -2.896 -31 1 C-38.449 5.779 -41.631 13.415 -43.616 21.812 C-44.295 25.684 -44.288 29.516 -44.25 33.438 C-44.258 34.232 -44.265 35.027 -44.273 35.846 C-44.25 42.958 -43.439 50.064 -39.938 56.375 C-38.707 58.631 -37.822 60.534 -37 63 C-35.201 64.067 -35.201 64.067 -33 65 C-32.237 65.495 -31.474 65.99 -30.688 66.5 C-24.711 69.836 -18.722 70.56 -12 70 C-11.01 69.67 -10.02 69.34 -9 69 C-8.237 68.897 -7.474 68.794 -6.688 68.688 C-1.83 67.445 1.227 65.009 4.25 61.062 C6.227 57.767 6.227 57.767 8 53 C16.58 52.67 25.16 52.34 34 52 C34 61.442 29.166 70.372 24 78 C23.34 77.67 22.68 77.34 22 77 C22.387 76.334 22.773 75.667 23.172 74.98 C26.849 68.472 29.609 62.082 32 55 C24.41 55.33 16.82 55.66 9 56 C7.68 58.64 6.36 61.28 5 64 C0.125 70.173 -6.703 72.256 -14.254 73.316 C-22.066 73.75 -29.841 71.057 -35.938 66.188 C-43.843 58.66 -46.596 48.038 -47.203 37.457 C-47.374 24.143 -45.521 12.35 -36.336 2.152 C-25.826 -7.613 -11.824 -7.473 0 0 Z " fill="#6B82D0" transform="translate(260,134)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.093 1.918 3.186 1.835 4.312 1.75 C15.057 1.807 26.007 8.039 33.469 15.453 C35.378 17.591 37.221 19.753 39 22 C38.67 22.66 38.34 23.32 38 24 C36.824 22.484 36.824 22.484 35.625 20.938 C28.105 12.241 17.228 6.369 6 4 C2.295 3.752 -1.413 3.769 -5.125 3.75 C-6.12 3.729 -7.115 3.709 -8.141 3.688 C-21.968 3.615 -32.683 8.252 -42.676 17.648 C-54.73 29.844 -59.211 45.556 -59.375 62.438 C-59.15 79.692 -54.942 96.692 -42.609 109.449 C-42.078 109.961 -41.547 110.473 -41 111 C-40.49 111.522 -39.979 112.044 -39.453 112.582 C-38.319 113.738 -37.163 114.873 -36 116 C-40.687 114.722 -43.104 112.058 -46.254 108.559 C-48.177 106.842 -49.484 106.402 -52 106 C-52.062 105.051 -52.124 104.103 -52.188 103.125 C-52.654 99.76 -53.784 97.242 -55.375 94.25 C-58.154 89.005 -59.588 83.735 -61 78 C-61.33 77.01 -61.66 76.02 -62 75 C-62.227 70.746 -62.228 66.49 -62.24 62.231 C-62.25 60.139 -62.281 58.049 -62.312 55.957 C-62.319 54.62 -62.324 53.283 -62.328 51.945 C-62.337 50.734 -62.347 49.522 -62.356 48.274 C-61.967 44.7 -61.027 42.92 -59 40 C-58.086 37.967 -57.191 35.924 -56.312 33.875 C-50.557 21.361 -42.123 10.632 -29.309 4.805 C-19.842 1.505 -9.957 0.48 0 0 Z M-61 45 C-60 49 -60 49 -60 49 Z " fill="#8195DB" transform="translate(446,267)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C5.324 2.368 6.656 2.712 8 3 C8.66 2.01 9.32 1.02 10 0 C10.33 1.65 10.66 3.3 11 5 C13.757 4.513 16.242 3.911 18.875 2.938 C22.46 1.862 25.276 1.649 29 2 C30.562 3.188 30.562 3.188 32 5 C33.011 5.825 34.021 6.65 35.062 7.5 C38 10 38 10 40 13 C40.99 12.34 41.98 11.68 43 11 C43 12.32 43 13.64 43 15 C42.34 15.33 41.68 15.66 41 16 C41 16.99 41 17.98 41 19 C41.66 19.33 42.32 19.66 43 20 C42.865 29.179 42.707 37.36 37 45 C32.987 48.761 28.742 50.141 23.312 50.062 C22.1 50.049 22.1 50.049 20.863 50.035 C20.248 50.024 19.634 50.012 19 50 C19.33 50.99 19.66 51.98 20 53 C18.02 52.34 16.04 51.68 14 51 C14.33 50.01 14.66 49.02 15 48 C13.35 47.67 11.7 47.34 10 47 C10.084 48.181 10.168 49.362 10.254 50.578 C10.357 52.135 10.46 53.693 10.562 55.25 C10.619 56.027 10.675 56.805 10.732 57.605 C10.781 58.36 10.829 59.114 10.879 59.891 C10.926 60.582 10.973 61.273 11.022 61.985 C10.999 64.136 10.6 65.941 10 68 C7.667 68 5.333 68 3 68 C1.515 68.495 1.515 68.495 0 69 C-0.33 67.02 -0.66 65.04 -1 63 C-1.33 63 -1.66 63 -2 63 C-2 60.36 -2 57.72 -2 55 C-1.67 55 -1.34 55 -1 55 C-1 52.03 -1 49.06 -1 46 C-1.33 46 -1.66 46 -2 46 C-2 43.69 -2 41.38 -2 39 C-1.67 39 -1.34 39 -1 39 C-0.67 26.13 -0.34 13.26 0 0 Z M11.125 10.188 C10.568 11.085 10.568 11.085 10 12 C9.67 10.02 9.34 8.04 9 6 C6.36 6 3.72 6 1 6 C1 25.14 1 44.28 1 64 C3.64 64 6.28 64 9 64 C9.33 57.07 9.66 50.14 10 43 C11.98 44.65 13.96 46.3 16 48 C21.388 49.814 26.813 49.195 32 47 C36.898 42.881 40.268 38.444 41 32 C41.538 23.242 40.574 16.05 35 9 C30.701 5.222 26.832 4.709 21.285 4.781 C16.883 5.203 13.967 6.872 11.125 10.188 Z " fill="#8B9BE5" transform="translate(309,481)"/>
<path d="M0 0 C1.15 -0.046 2.3 -0.092 3.484 -0.139 C11.78 -0.25 11.78 -0.25 14.306 1.804 C15.414 3.465 15.414 3.465 16.625 6.438 C16.295 7.097 15.965 7.757 15.625 8.438 C12.625 4.688 12.625 4.688 12.625 2.438 C4.705 2.438 -3.215 2.438 -11.375 2.438 C-11.375 39.727 -11.375 77.017 -11.375 115.438 C-4.445 115.438 2.485 115.438 9.625 115.438 C9.625 90.358 9.625 65.278 9.625 39.438 C10.285 39.767 10.945 40.097 11.625 40.438 C11.295 65.847 10.965 91.257 10.625 117.438 C5.015 117.438 -0.595 117.438 -6.375 117.438 C-6.87 118.428 -6.87 118.428 -7.375 119.438 C-7.375 118.778 -7.375 118.118 -7.375 117.438 C-8.695 117.438 -10.015 117.438 -11.375 117.438 C-11.87 118.428 -11.87 118.428 -12.375 119.438 C-14.375 116.438 -14.375 116.438 -14.375 112.438 C-13.715 112.438 -13.055 112.438 -12.375 112.438 C-12.375 110.787 -12.375 109.137 -12.375 107.438 C-13.035 107.438 -13.695 107.438 -14.375 107.438 C-14.303 106.869 -14.231 106.301 -14.156 105.715 C-12.974 96.994 -12.974 96.994 -14.375 88.438 C-13.438 85.75 -13.438 85.75 -12.375 83.438 C-13.035 83.438 -13.695 83.438 -14.375 83.438 C-14.217 82.912 -14.058 82.386 -13.895 81.845 C-13.248 78.848 -13.156 75.987 -13.059 72.922 C-13.017 71.671 -12.975 70.421 -12.932 69.133 C-12.892 67.831 -12.853 66.529 -12.812 65.188 C-12.769 63.87 -12.726 62.553 -12.682 61.195 C-12.576 57.943 -12.474 54.69 -12.375 51.438 C-13.035 51.438 -13.695 51.438 -14.375 51.438 C-14.045 50.447 -13.715 49.458 -13.375 48.438 C-13.304 46.565 -13.291 44.69 -13.312 42.816 C-13.32 41.716 -13.328 40.616 -13.336 39.482 C-13.362 37.165 -13.388 34.847 -13.414 32.529 C-13.422 31.428 -13.43 30.326 -13.438 29.191 C-13.449 28.184 -13.461 27.177 -13.473 26.139 C-13.385 23.727 -13.071 21.737 -12.375 19.438 C-13.035 19.108 -13.695 18.778 -14.375 18.438 C-13.715 18.438 -13.055 18.438 -12.375 18.438 C-12.516 15.979 -12.662 13.521 -12.812 11.062 C-12.852 10.364 -12.891 9.665 -12.932 8.945 C-13.118 5.064 -13.118 5.064 -14.375 1.438 C-10.137 -0.681 -4.656 0.075 0 0 Z " fill="#5670C6" transform="translate(630.375,108.5625)"/>
<path d="M0 0 C3.124 2.556 4.032 5.127 5 9 C5.075 11.332 5.093 13.669 5 16 C5.99 16.33 6.98 16.66 8 17 C6.35 17.66 4.7 18.32 3 19 C3.289 19.846 3.577 20.691 3.875 21.562 C4.012 25.338 3.756 25.883 1.5 28.688 C-2.617 32.976 -7.04 35.087 -13 35.375 C-17.208 35.211 -20.295 33.966 -24 32 C-24.33 31.34 -24.66 30.68 -25 30 C-24.01 30 -23.02 30 -22 30 C-23.65 28.35 -25.3 26.7 -27 25 C-26.67 24.34 -26.34 23.68 -26 23 C-23.299 24.003 -21.012 24.992 -18.625 26.625 C-15.255 28.39 -12.751 28.289 -9 28 C-8.67 27.67 -8.34 27.34 -8 27 C-8.675 27.046 -9.351 27.093 -10.047 27.141 C-17.289 27.434 -17.289 27.434 -20.562 25.438 C-22 23 -22 23 -23 16 C-14.42 16 -5.84 16 3 16 C1.712 7.956 1.712 7.956 0 0 Z M-19 17 C-19 17.66 -19 18.32 -19 19 C-19.66 19 -20.32 19 -21 19 C-20.733 20.985 -20.733 20.985 -20 23 C-19.01 23.33 -18.02 23.66 -17 24 C-16.67 24.66 -16.34 25.32 -16 26 C-10.38 25.801 -10.38 25.801 -5.75 23.062 C-5.173 22.382 -4.595 21.701 -4 21 C-1.75 19.75 -1.75 19.75 0 19 C-1.991 16.562 -1.991 16.562 -5.914 16.805 C-7.484 16.818 -9.055 16.842 -10.625 16.875 C-11.828 16.889 -11.828 16.889 -13.055 16.902 C-15.037 16.926 -17.018 16.962 -19 17 Z " fill="#BFC8F5" transform="translate(584,904)"/>
<path d="M0 0 C6 0 12 0 18 0 C18 0.66 18 1.32 18 2 C18.804 2.371 19.609 2.742 20.438 3.125 C23 5 23 5 23.555 6.973 C23.788 8.974 23.901 10.988 24 13 C21.25 13.25 21.25 13.25 18 13 C17.381 12.34 16.762 11.68 16.125 11 C13.708 8.725 13.122 8.683 9.938 8.625 C5.95 8.691 5.95 8.691 3 11 C3.27 13.437 3.27 13.437 4 16 C4.99 16.66 5.98 17.32 7 18 C6.34 18 5.68 18 5 18 C5 18.66 5 19.32 5 20 C4.34 20 3.68 20 3 20 C3 20.66 3 21.32 3 22 C8.94 22.99 8.94 22.99 15 24 C15 24.33 15 24.66 15 25 C8.105 25.44 1.927 24.356 -3.562 19.938 C-5.669 15.633 -5.935 11.698 -5 7 C-2.5 4 -2.5 4 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DFE4FD" transform="translate(404,896)"/>
<path d="M0 0 C3 1 3 1 4.625 3.312 C7.587 11.255 7.934 19.991 5 28 C3.151 31.632 1.989 33.482 -1.625 35.375 C-6.067 36.198 -10.501 36.55 -15 36 C-15.66 35.34 -16.32 34.68 -17 34 C-17 33.01 -17 32.02 -17 31 C-15.783 31.062 -14.566 31.124 -13.312 31.188 C-9.422 31.208 -7.164 30.373 -4 28 C-4.33 28 -4.66 28 -5 28 C-5.66 28.66 -6.32 29.32 -7 30 C-10.057 30.571 -12.938 30.524 -16 30 C-19.574 27.319 -21.204 25.333 -22 21 C-22.458 15.042 -22.168 10.373 -18.875 5.25 C-15.8 3.2 -12.616 2.638 -9 3 C-5.918 4.673 -3.588 6.824 -2 10 C-1.929 12.207 -1.916 14.417 -1.938 16.625 C-1.947 17.814 -1.956 19.002 -1.965 20.227 C-1.976 21.142 -1.988 22.057 -2 23 C-1.34 23 -0.68 23 0 23 C0.33 21.68 0.66 20.36 1 19 C1.66 19.33 2.32 19.66 3 20 C3 22.31 3 24.62 3 27 C5.107 20.678 5.286 11.989 2.562 5.812 C2.047 4.884 1.531 3.956 1 3 C0.67 2.01 0.34 1.02 0 0 Z M-20 10 C-20.42 12.712 -20.42 12.712 -20 15 C-19.67 14.01 -19.34 13.02 -19 12 C-18.34 12 -17.68 12 -17 12 C-17.99 15.96 -17.99 15.96 -19 20 C-18.34 20 -17.68 20 -17 20 C-17.021 20.763 -17.041 21.526 -17.062 22.312 C-17.129 25.102 -17.129 25.102 -16 28 C-14.563 28.081 -13.126 28.139 -11.688 28.188 C-10.887 28.222 -10.086 28.257 -9.262 28.293 C-6.942 28.184 -6.942 28.184 -5.27 26.676 C-2.065 22.446 -2.541 17.046 -3 12 C-4.613 8.314 -5.63 7.246 -9 5 C-14.486 4.165 -16.697 5.629 -20 10 Z M-20 21 C-19 23 -19 23 -19 23 Z M-18 25 C-17 27 -17 27 -17 27 Z " fill="#D2D9FD" transform="translate(543,901)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.846 2.665 2.691 2.33 3.562 1.984 C12.605 -1.263 12.605 -1.263 16.574 -0.516 C20.467 1.355 22.276 2.991 24 7 C23.186 6.567 23.186 6.567 22.355 6.125 C21.64 5.754 20.925 5.382 20.188 5 C19.48 4.629 18.772 4.257 18.043 3.875 C15.911 2.803 15.911 2.803 13 3 C13.554 3.193 14.109 3.387 14.68 3.586 C15.404 3.846 16.129 4.107 16.875 4.375 C17.594 4.63 18.314 4.885 19.055 5.148 C21 6 21 6 23 8 C25.294 15.693 25.235 23.083 25.125 31.062 C25.116 32.31 25.107 33.557 25.098 34.842 C25.074 37.895 25.041 40.947 25 44 C23.35 44 21.7 44 20 44 C19.996 43.123 19.992 42.245 19.988 41.341 C19.96 38.064 19.911 34.789 19.847 31.512 C19.824 30.098 19.81 28.684 19.804 27.27 C20.121 18.188 20.121 18.188 16.75 10.094 C13.957 7.967 12.645 7.228 9.188 7.125 C8.397 7.086 7.607 7.048 6.793 7.008 C6.201 7.005 5.61 7.003 5 7 C5.66 6.01 6.32 5.02 7 4 C4.046 5.372 1.56 6.989 -1 9 C-1 7.02 -1 5.04 -1 3 C-3.64 3 -6.28 3 -9 3 C-9 6.63 -9 10.26 -9 14 C-9.33 14 -9.66 14 -10 14 C-10 9.71 -10 5.42 -10 1 C-4.375 -1.25 -4.375 -1.25 0 0 Z " fill="#DAE2FE" transform="translate(475,484)"/>
<path d="M0 0 C6.93 0 13.86 0 21 0 C20.67 0.66 20.34 1.32 20 2 C18.784 1.988 17.569 1.977 16.316 1.965 C14.732 1.955 13.147 1.946 11.562 1.938 C10.76 1.929 9.958 1.921 9.131 1.912 C7.087 1.903 5.043 1.948 3 2 C1.675 3.098 1.675 3.098 1.902 5.826 C1.914 7.122 1.926 8.419 1.938 9.754 C1.946 11.293 1.955 12.832 1.963 14.372 C1.969 15.212 1.975 16.052 1.982 16.918 C2.136 39.157 1.826 61.389 1.5 83.625 C1.476 85.289 1.451 86.954 1.427 88.618 C1.289 98.079 1.146 107.539 1 117 C7.6 117 14.2 117 21 117 C20.836 114.324 20.673 111.648 20.504 108.891 C19.747 94.852 19.892 80.804 19.938 66.75 C19.943 64.047 19.947 61.344 19.951 58.641 C19.962 52.094 19.979 45.547 20 39 C20.66 39 21.32 39 22 39 C22 65.07 22 91.14 22 118 C14.74 118 7.48 118 0 118 C0 79.06 0 40.12 0 0 Z " fill="#F9FBFE" transform="translate(640,271)"/>
<path d="M0 0 C0 3.553 -0.816 4.522 -2.75 7.438 C-11.97 22.367 -13.192 41.471 -9.75 58.438 C-9.201 60.634 -8.619 62.822 -8 65 C-7.6 66.446 -7.6 66.446 -7.191 67.922 C-3.361 79.976 4.181 88.609 15 95 C18.265 96.638 21.516 97.911 25 99 C25.681 99.224 26.361 99.449 27.062 99.68 C39.919 103.235 55.855 101.89 67.645 95.586 C69.45 94.421 71.229 93.216 73 92 C73.66 92.33 74.32 92.66 75 93 C66 99.667 57.381 103.312 46.117 103.727 C45.419 103.817 44.72 103.907 44 104 C43.67 104.66 43.34 105.32 43 106 C42.67 105.01 42.34 104.02 42 103 C41.093 103.227 40.185 103.454 39.25 103.688 C29.126 105.096 17.127 99.703 9.09 93.898 C6.68 91.98 4.319 90.027 2 88 C2 88.99 2 89.98 2 91 C0.515 90.505 0.515 90.505 -1 90 C-1.103 88.907 -1.206 87.814 -1.312 86.688 C-1.973 83.144 -2.203 82.068 -5 80 C-5.99 80.495 -5.99 80.495 -7 81 C-8.562 78.625 -8.562 78.625 -10 76 C-9.505 75.01 -9.505 75.01 -9 74 C-8.34 74.33 -7.68 74.66 -7 75 C-7.617 73.351 -8.243 71.706 -8.875 70.062 C-9.223 69.146 -9.571 68.229 -9.93 67.285 C-10.782 64.851 -10.782 64.851 -13 64 C-12.67 63.01 -12.34 62.02 -12 61 C-12.136 58.862 -12.136 58.862 -12.559 56.543 C-12.697 55.677 -12.836 54.812 -12.979 53.92 C-13.13 53.018 -13.281 52.117 -13.438 51.188 C-15.224 40.415 -15.224 40.415 -15 35 C-14.34 35 -13.68 35 -13 35 C-13.021 34.031 -13.041 33.061 -13.062 32.062 C-13.072 27.667 -12.553 23.356 -12 19 C-11.34 19 -10.68 19 -10 19 C-9.894 18.291 -9.789 17.582 -9.68 16.852 C-8.92 13.664 -7.711 11.238 -6.125 8.375 C-5.612 7.434 -5.099 6.493 -4.57 5.523 C-3.201 3.324 -1.921 1.714 0 0 Z " fill="#6A80D2" transform="translate(202,125)"/>
<path d="M0 0 C3.932 3.658 6.041 7.433 7.789 12.473 C8.779 12.473 9.769 12.473 10.789 12.473 C11.155 19.906 11.155 19.906 9.539 22.41 C6.325 24.362 3.119 24.6 -0.582 24.605 C-1.404 24.607 -2.226 24.608 -3.072 24.609 C-3.922 24.606 -4.773 24.602 -5.648 24.598 C-6.918 24.603 -6.918 24.603 -8.213 24.609 C-12.34 24.603 -16.186 24.514 -20.211 23.473 C-20.211 26.585 -19.755 29.414 -19.211 32.473 C-20.201 32.473 -21.191 32.473 -22.211 32.473 C-24.182 28.284 -24.211 26.474 -24.211 21.473 C-13.981 21.473 -3.751 21.473 6.789 21.473 C4.752 9.749 4.752 9.749 -2.211 1.473 C-8.362 -1.547 -15.141 -1.551 -21.586 0.598 C-26.34 3.994 -29.419 7.93 -31.211 13.473 C-32.021 21.352 -32.239 28.988 -27.211 35.473 C-22.687 39.996 -18.02 41.565 -11.711 41.66 C-8.136 41.582 -4.72 41.192 -1.211 40.473 C-1.706 41.463 -1.706 41.463 -2.211 42.473 C-5.012 43.359 -5.012 43.359 -8.523 44.16 C-9.675 44.432 -10.826 44.704 -12.012 44.984 C-14.984 45.438 -16.453 45.477 -19.211 44.473 C-20.201 44.143 -21.191 43.813 -22.211 43.473 C-21.716 42.978 -21.716 42.978 -21.211 42.473 C-22.118 41.916 -23.026 41.359 -23.961 40.785 C-27.075 38.569 -29.065 36.609 -31.211 33.473 C-31.211 32.813 -31.211 32.153 -31.211 31.473 C-31.871 31.143 -32.531 30.813 -33.211 30.473 C-33.773 28.16 -33.773 28.16 -34.211 25.473 C-34.404 24.885 -34.598 24.297 -34.797 23.691 C-35.913 17.711 -33.647 10.857 -30.578 5.762 C-22.417 -4.183 -11.241 -7.766 0 0 Z " fill="#9AA8E7" transform="translate(388.2109375,487.52734375)"/>
<path d="M0 0 C6.055 5.19 8.054 11.276 9 19 C9.66 19.33 10.32 19.66 11 20 C12.329 42.296 12.695 61.584 0 81 C0.33 81.66 0.66 82.32 1 83 C-0.114 83.65 -0.114 83.65 -1.25 84.312 C-5.001 86.614 -7.539 88.309 -10 92 C-11.801 93.031 -11.801 93.031 -14 94 C-15.228 94.603 -15.228 94.603 -16.48 95.219 C-17.374 95.642 -18.267 96.064 -19.188 96.5 C-20.068 96.923 -20.948 97.346 -21.855 97.781 C-29.621 100.791 -36.977 101.294 -45.25 101.188 C-46.217 101.187 -47.184 101.186 -48.18 101.186 C-55.354 101.141 -55.354 101.141 -58.624 100.427 C-61.137 99.78 -61.137 99.78 -64 101 C-66.688 100.062 -66.688 100.062 -69 99 C-69 98.34 -69 97.68 -69 97 C-70.65 96.67 -72.3 96.34 -74 96 C-74.33 94.68 -74.66 93.36 -75 92 C-71.281 93.174 -67.711 94.541 -64.125 96.062 C-59.674 97.847 -55.776 98.787 -51 99 C-49.822 99.077 -49.822 99.077 -48.621 99.156 C-33.405 99.701 -19.52 96.08 -8 86 C-7.34 86 -6.68 86 -6 86 C-5.728 85.423 -5.456 84.845 -5.176 84.25 C-4.002 82.003 -2.664 80.058 -1.188 78 C10.019 61.258 11.527 39.909 7.875 20.438 C6.44 14.636 4.079 9.554 1.207 4.328 C0 2 0 2 0 0 Z " fill="#6E83D0" transform="translate(485,291)"/>
<path d="M0 0 C8.044 6.497 11.775 16.953 13 27 C13.893 41.308 13.284 55.302 4 67 C0.282 70.514 -3.971 73.743 -9 75 C-10.343 74.708 -11.679 74.38 -13 74 C-11.044 72.995 -9.085 71.996 -7.125 71 C-6.034 70.443 -4.944 69.886 -3.82 69.312 C-1 68 -1 68 1 68 C1.247 67.464 1.495 66.928 1.75 66.375 C3 64 3 64 4.938 61.5 C8.089 56.944 8.861 51.33 10 46 C9.67 46 9.34 46 9 46 C9 44.35 9 42.7 9 41 C9.66 41 10.32 41 11 41 C11 38.36 11 35.72 11 33 C10.01 32.67 9.02 32.34 8 32 C8.495 31.01 8.495 31.01 9 30 C9.99 30.495 9.99 30.495 11 31 C10.719 28.519 10.426 26.041 10.125 23.562 C10.046 22.855 9.968 22.148 9.887 21.42 C9.661 19.603 9.337 17.799 9 16 C8.34 15.67 7.68 15.34 7 15 C6.773 14.154 6.546 13.309 6.312 12.438 C4.541 7.798 1.72 4.288 -2 1 C-4.125 0.235 -4.125 0.235 -6 0 C-6 -0.66 -6 -1.32 -6 -2 C-14.061 -3.906 -24.68 -4.825 -32.188 -0.875 C-40.526 4.816 -43.974 13.009 -46.188 22.562 C-47.527 29.945 -47.67 37.518 -48 45 C-48.33 45 -48.66 45 -49 45 C-49.587 29.092 -49.831 13.792 -38.441 1.289 C-27.326 -9.236 -12.133 -8.176 0 0 Z " fill="#6D81CE" transform="translate(585,295)"/>
<path d="M0 0 C0.233 0.513 0.467 1.026 0.707 1.555 C4.706 9.949 9.632 17.761 14.536 25.646 C15.985 27.976 17.427 30.312 18.867 32.648 C19.765 34.099 20.664 35.55 21.562 37 C21.98 37.676 22.397 38.353 22.827 39.049 C24.411 41.59 25.875 43.875 28 46 C28.72 47.4 29.402 48.82 30.062 50.25 C33.448 57.204 37.741 63.561 42 70 C41.34 70.66 40.68 71.32 40 72 C39.441 71.04 38.881 70.079 38.305 69.09 C33.403 60.702 28.427 52.361 23.375 44.062 C22.798 43.115 22.222 42.168 21.628 41.192 C18.29 35.721 14.928 30.265 11.547 24.82 C10.436 23.028 9.325 21.235 8.215 19.443 C7.139 17.71 6.059 15.981 4.978 14.252 C4.328 13.203 3.678 12.155 3.008 11.074 C2.427 10.142 1.845 9.209 1.247 8.249 C0 6 0 6 0 4 C-7.59 4 -15.18 4 -23 4 C-23 42.94 -23 81.88 -23 122 C-23.33 122 -23.66 122 -24 122 C-24 82.4 -24 42.8 -24 2 C-18.72 2 -13.44 2 -8 2 C-4.772 -0.152 -3.716 -0.201 0 0 Z M-5 2 C-1 3 -1 3 -1 3 Z " fill="#546DC3" transform="translate(663,267)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.342 16.988 3.19 33.957 3 51 C3 52.32 3 53.64 3 55 C3 55.66 3 56.32 3 57 C-0.802 57.548 -4.222 57.783 -8 57 C-10.792 54.387 -12.501 51.504 -14 48 C-14 47.34 -14 46.68 -14 46 C-14.66 46 -15.32 46 -16 46 C-17.32 43.36 -18.64 40.72 -20 38 C-20.99 38.495 -20.99 38.495 -22 39 C-23.125 33.25 -23.125 33.25 -22 31 C-19.682 34.601 -17.372 38.206 -15.062 41.812 C-14.403 42.837 -13.744 43.861 -13.064 44.916 C-12.436 45.899 -11.808 46.882 -11.16 47.895 C-10.579 48.801 -9.998 49.706 -9.399 50.64 C-8 53 -8 53 -7 56 C-4.69 56 -2.38 56 0 56 C0 38.84 0 21.68 0 4 C-2.31 4 -4.62 4 -7 4 C-7 16.54 -7 29.08 -7 42 C-11.514 37.486 -14.484 32.777 -17.812 27.375 C-18.423 26.408 -19.033 25.441 -19.662 24.445 C-20.234 23.522 -20.806 22.599 -21.395 21.648 C-21.919 20.805 -22.443 19.961 -22.984 19.091 C-23.319 18.401 -23.654 17.711 -24 17 C-23.67 16.34 -23.34 15.68 -23 15 C-22.336 16.083 -21.672 17.166 -20.988 18.281 C-20.097 19.688 -19.205 21.094 -18.312 22.5 C-17.877 23.215 -17.442 23.931 -16.994 24.668 C-15.755 26.595 -14.587 28.347 -13 30 C-10.271 30.63 -10.271 30.63 -8 30 C-8 29.01 -8 28.02 -8 27 C-8.66 26.67 -9.32 26.34 -10 26 C-9.67 25.34 -9.34 24.68 -9 24 C-8.848 22.407 -8.751 20.809 -8.684 19.211 C-8.621 17.799 -8.621 17.799 -8.557 16.359 C-8.498 14.882 -8.498 14.882 -8.438 13.375 C-8.394 12.382 -8.351 11.39 -8.307 10.367 C-8.2 7.912 -8.098 5.456 -8 3 C-5.36 2.67 -2.72 2.34 0 2 C0 1.34 0 0.68 0 0 Z M-12 32 C-11.34 33.32 -10.68 34.64 -10 36 C-9.67 34.68 -9.34 33.36 -9 32 C-9.99 32 -10.98 32 -12 32 Z M-9 36 C-8 38 -8 38 -8 38 Z " fill="#808EDB" transform="translate(648,881)"/>
<path d="M0 0 C3.979 -0.059 7.958 -0.094 11.938 -0.125 C13.054 -0.142 14.171 -0.159 15.322 -0.176 C21.89 -0.214 27.653 0.291 34 2 C34.33 1.34 34.66 0.68 35 0 C35.495 0.99 35.495 0.99 36 2 C37.497 2.642 39.024 3.215 40.562 3.75 C48.531 6.819 54.176 11.885 60 18 C59.67 18.66 59.34 19.32 59 20 C58.416 19.346 58.416 19.346 57.82 18.68 C49.317 9.627 38.096 3.67 25.572 2.839 C22.818 2.793 20.066 2.765 17.312 2.75 C15.898 2.719 15.898 2.719 14.455 2.688 C1.175 2.614 -10.549 7.622 -20.312 16.688 C-34.148 31.702 -35.51 48.554 -35.029 67.917 C-35.001 69.189 -34.974 70.46 -34.945 71.77 C-34.909 72.92 -34.873 74.071 -34.836 75.257 C-35 78 -35 78 -37 80 C-41.979 61.904 -38.57 42.346 -30 26 C-22.53 13.597 -12.619 7.417 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7E92D7" transform="translate(549,268)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.111 13.046 7.057 25.991 6 39 C4.02 39 2.04 39 0 39 C-0.168 34.41 -0.329 29.821 -0.482 25.23 C-0.535 23.671 -0.591 22.111 -0.648 20.552 C-0.731 18.303 -0.806 16.054 -0.879 13.805 C-0.906 13.114 -0.933 12.423 -0.961 11.711 C-1.084 7.674 -0.8 3.959 0 0 Z " fill="#E7EDFE" transform="translate(765,474)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 12.54 7 25.08 7 38 C4.69 38 2.38 38 0 38 C0 25.46 0 12.92 0 0 Z " fill="#E0E5FE" transform="translate(500,899)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C2.671 2.371 2.671 2.371 4.375 2.75 C16.221 5.505 26.219 13.415 32.812 23.438 C40.017 35.528 42.394 46.002 42.375 60.062 C42.374 61.847 42.374 61.847 42.372 63.668 C42.224 74.746 41.206 85.504 35 95 C33.797 91.391 34.278 90.841 35.812 87.5 C42.628 71.038 42.212 49.713 35.488 33.312 C30.183 21.682 23.491 13.797 12 8 C7.724 6.406 3.42 5.128 -1 4 C-1.663 3.807 -2.325 3.613 -3.008 3.414 C-16.765 0.555 -31.308 4.807 -43 12 C-45.859 14.162 -48.458 16.476 -51 19 C-51.825 19.804 -52.65 20.609 -53.5 21.438 C-54.243 22.211 -54.243 22.211 -55 23 C-53.608 18.558 -51.557 16.335 -48.062 13.312 C-47.187 12.546 -46.312 11.779 -45.41 10.988 C-40.576 7 -40.576 7 -37 7 C-37 6.34 -37 5.68 -37 5 C-25.562 0.694 -14.138 -0.112 -2 1 C-1.34 1.33 -0.68 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#5E74C7" transform="translate(373,106)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C4.868 7.967 3.642 11.762 -1 16 C-6.705 18.092 -11.969 18.251 -18 18 C-18 17.34 -18 16.68 -18 16 C-19.32 15.34 -20.64 14.68 -22 14 C-21.01 14 -20.02 14 -19 14 C-18.67 13.01 -18.34 12.02 -18 11 C-18.99 10.01 -19.98 9.02 -21 8 C-18.667 7.958 -16.333 7.959 -14 8 C-13.67 8.33 -13.34 8.66 -13 9 C-10.789 8.572 -9.019 8.009 -7 7 C-6.691 6.361 -6.381 5.721 -6.062 5.062 C-5 3 -5 3 -2 1 C-2 1.99 -2 2.98 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#DBE2FD" transform="translate(390,512)"/>
<path d="M0 0 C0.808 0.102 1.616 0.204 2.449 0.309 C3.064 0.392 3.679 0.476 4.312 0.562 C3.653 1.222 2.992 1.883 2.312 2.562 C1.982 2.562 1.653 2.562 1.312 2.562 C1.312 8.173 1.312 13.783 1.312 19.562 C1.972 19.232 2.633 18.903 3.312 18.562 C2.982 18.232 2.653 17.903 2.312 17.562 C2.272 15.23 2.27 12.895 2.312 10.562 C2.643 10.562 2.972 10.562 3.312 10.562 C3.312 12.212 3.312 13.862 3.312 15.562 C3.942 15.516 4.571 15.47 5.219 15.422 C11.798 15.144 15.652 16.208 21.312 19.562 C20.653 19.893 19.992 20.222 19.312 20.562 C18.653 20.232 17.992 19.903 17.312 19.562 C13.599 19.144 10.019 19.059 6.312 19.562 C3.245 21.875 3.245 21.875 1.312 24.562 C0.653 24.562 -0.008 24.562 -0.688 24.562 C-0.688 17.962 -0.688 11.362 -0.688 4.562 C-2.668 4.562 -4.648 4.562 -6.688 4.562 C-6.688 21.722 -6.688 38.883 -6.688 56.562 C-4.707 56.562 -2.727 56.562 -0.688 56.562 C-0.688 54.582 -0.688 52.602 -0.688 50.562 C-0.028 50.562 0.633 50.562 1.312 50.562 C1.911 51.388 2.509 52.212 3.125 53.062 C5.255 55.368 6.327 55.564 9.531 55.895 C12.804 55.877 16.047 55.778 19.312 55.562 C17.663 57.212 16.013 58.862 14.312 60.562 C13.982 59.903 13.653 59.242 13.312 58.562 C11.663 58.562 10.013 58.562 8.312 58.562 C8.312 58.893 8.312 59.222 8.312 59.562 C2.562 59.688 2.562 59.688 0.312 58.562 C-2.327 58.562 -4.967 58.562 -7.688 58.562 C-7.688 39.423 -7.688 20.283 -7.688 0.562 C-4.593 -0.469 -3.161 -0.412 0 0 Z M5.312 16.562 C9.312 17.562 9.312 17.562 9.312 17.562 Z M1.312 55.562 C1.643 56.222 1.972 56.883 2.312 57.562 C2.643 56.903 2.972 56.242 3.312 55.562 C2.653 55.562 1.992 55.562 1.312 55.562 Z M4.312 56.562 C5.312 58.562 5.312 58.562 5.312 58.562 Z " fill="#929EE4" transform="translate(521.6875,880.4375)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C1.99 1.67 2.98 1.34 4 1 C4 1.66 4 2.32 4 3 C4.615 3.133 5.23 3.266 5.863 3.402 C7.076 3.667 7.076 3.667 8.312 3.938 C9.113 4.112 9.914 4.286 10.738 4.465 C13 5 13 5 16 6 C16 6.66 16 7.32 16 8 C16.58 8.266 17.16 8.531 17.758 8.805 C27.938 14.232 33.876 23.373 37.238 34.07 C38.009 37.034 38.546 39.973 39 43 C37.72 42.76 37.72 42.76 36.414 42.516 C33.027 42.004 29.799 41.798 26.375 41.75 C25.269 41.724 24.163 41.698 23.023 41.672 C19.723 42.03 18.401 42.775 16 45 C16 44.01 16 43.02 16 42 C15.402 41.959 14.804 41.917 14.188 41.875 C12 41 12 41 10.25 37.938 C9.837 36.968 9.425 35.999 9 35 C8.01 35 7.02 35 6 35 C5.505 32.03 5.505 32.03 5 29 C8.523 30.404 9.524 31.645 11.043 35.062 C12.656 37.795 13.385 38.814 16.459 39.746 C19.436 40.012 22.325 40.057 25.312 40 C26.339 40.012 27.366 40.023 28.424 40.035 C30.951 40.059 33.473 40.046 36 40 C35.552 38.428 35.098 36.858 34.641 35.289 C34.262 33.977 34.262 33.977 33.876 32.639 C30.444 22.3 23.512 13.627 14 8.32 C-0.42 1.974 -16.281 1.014 -31.25 6.105 C-36.495 8.194 -40.68 10.315 -45 14 C-45.66 13.67 -46.32 13.34 -47 13 C-38.66 5.862 -28.622 1.113 -17.578 0.684 C-16.553 0.642 -15.529 0.6 -14.473 0.557 C-13.409 0.517 -12.346 0.478 -11.25 0.438 C-9.632 0.373 -9.632 0.373 -7.98 0.307 C-5.32 0.201 -2.66 0.099 0 0 Z M6 31 C7 33 7 33 7 33 Z " fill="#6E84D3" transform="translate(255,106)"/>
<path d="M0 0 C16.458 3.267 16.458 3.267 22.312 9.438 C23.976 11.963 25.179 14.09 26 17 C25.67 18.32 25.34 19.64 25 21 C21.7 21.33 18.4 21.66 15 22 C14.567 20.886 14.134 19.772 13.688 18.625 C12.301 14.825 12.301 14.825 9 13 C4.137 12.362 -0.427 12.106 -4.75 14.562 C-6.507 17.989 -5.847 20.33 -5 24 C-5.66 24 -6.32 24 -7 24 C-8.444 21.111 -8.377 19.204 -8 16 C-5.45 12.898 -3.823 11.274 0 10 C6.121 9.498 9.117 9.912 14 14 C17 17.4 17 17.4 17 20 C19.31 19.67 21.62 19.34 24 19 C21.895 13.351 19.769 9.095 14.25 6.027 C8.185 3.399 1.81 2.801 -4.531 4.797 C-8.276 6.378 -11.343 7.851 -14 11 C-15.551 15.654 -15.584 20.144 -15 25 C-11.257 31.862 -4.944 33.457 2.203 35.688 C3.794 36.153 5.396 36.582 7 37 C3.187 38.445 0.806 37.494 -2.938 36.125 C-7.253 34.55 -7.253 34.55 -11.734 33.641 C-14 33 -14 33 -15.363 31.102 C-17.867 24.569 -18.477 18.971 -18 12 C-17.34 12 -16.68 12 -16 12 C-15.794 11.443 -15.587 10.886 -15.375 10.312 C-13.481 7.127 -11.188 4.844 -8 3 C-5.266 2.322 -2.835 2.147 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7C90DB" transform="translate(594,467)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C3.702 1.876 3.702 1.876 5.438 1.75 C13.588 1.793 19.52 6.026 25.469 11.219 C27.138 13.161 27.605 14.497 28 17 C28.33 17 28.66 17 29 17 C29.66 16.34 30.32 15.68 31 15 C31.625 17.875 31.625 17.875 32 21 C31.34 21.66 30.68 22.32 30 23 C30.24 25.882 30.24 25.882 30.938 29.062 C31.329 31.035 31.693 33.013 32 35 C31.67 35.33 31.34 35.66 31 36 C30.502 38.738 30.1 41.485 29.695 44.238 C28.961 47.155 27.956 48.75 26 51 C26.33 49.845 26.66 48.69 27 47.5 C29.56 35.859 29.03 24.119 23.125 13.688 C18.675 8.059 13.919 5.518 7 4 C-1.306 3.035 -8.341 4.584 -15.312 9.188 C-22.987 17.219 -23.372 26.188 -23.215 36.789 C-22.919 45.136 -19.672 51.223 -15 58 C-20.203 55.875 -21.847 51.976 -24 47 C-27.467 35.94 -27.009 24.826 -21.875 14.438 C-18.363 8.349 -12.79 3.965 -6 2 C-4.008 1.624 -2.008 1.28 0 1 C0 0.67 0 0.34 0 0 Z " fill="#7388D8" transform="translate(273,467)"/>
<path d="M0 0 C0.99 1.32 1.98 2.64 3 4 C2.67 4.66 2.34 5.32 2 6 C1.34 5.34 0.68 4.68 0 4 C-2.97 3.505 -2.97 3.505 -6 3 C-6 20.16 -6 37.32 -6 55 C-4.02 55 -2.04 55 0 55 C0 42.46 0 29.92 0 17 C0.66 17 1.32 17 2 17 C3.324 18.68 3.324 18.68 4.688 20.875 C5.145 21.594 5.603 22.314 6.074 23.055 C6.38 23.697 6.685 24.339 7 25 C6.67 25.66 6.34 26.32 6 27 C5.01 26.01 4.02 25.02 3 24 C1.861 28.545 1.851 32.911 1.875 37.562 C1.871 38.317 1.867 39.072 1.863 39.85 C1.836 43.975 1.836 43.975 2.567 48.011 C3.27 51.24 2.004 53.929 1 57 C-1.97 56.67 -4.94 56.34 -8 56 C-8 38.18 -8 20.36 -8 2 C-5.36 1.34 -2.72 0.68 0 0 Z " fill="#8E9CE0" transform="translate(616,882)"/>
<path d="M0 0 C1 0.005 2 0.01 3.031 0.016 C4.111 0.019 5.19 0.022 6.302 0.026 C8.007 0.038 8.007 0.038 9.746 0.051 C11.456 0.058 11.456 0.058 13.201 0.065 C16.028 0.077 18.856 0.093 21.683 0.114 C21.353 0.774 21.023 1.434 20.683 2.114 C13.423 2.114 6.163 2.114 -1.317 2.114 C-0.987 39.404 -0.657 76.694 -0.317 115.114 C6.943 115.444 14.203 115.774 21.683 116.114 C21.683 116.444 21.683 116.774 21.683 117.114 C9.308 117.609 9.308 117.609 -3.317 118.114 C-3.317 116.464 -3.317 114.814 -3.317 113.114 C-3.977 112.784 -4.637 112.454 -5.317 112.114 C-4.966 111.577 -4.615 111.041 -4.254 110.489 C-3.014 107.347 -3.752 105.436 -4.317 102.114 C-4.204 100.738 -4.047 99.365 -3.858 97.997 C-3.277 93.198 -3.189 88.492 -3.219 83.662 C-3.22 82.765 -3.222 81.868 -3.223 80.944 C-3.227 79.014 -3.233 77.084 -3.241 75.154 C-3.254 72.104 -3.261 69.053 -3.266 66.003 C-3.277 59.54 -3.296 53.077 -3.317 46.614 C-3.34 39.095 -3.361 31.577 -3.373 24.059 C-3.379 21.047 -3.392 18.035 -3.404 15.023 C-3.408 13.192 -3.411 11.361 -3.414 9.53 C-3.419 8.687 -3.425 7.844 -3.43 6.976 C-3.43 5.022 -3.379 3.067 -3.317 1.114 C-2.317 0.114 -2.317 0.114 0 0 Z " fill="#4260C8" transform="translate(576.316650390625,108.886474609375)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.32 2 3.64 2 5 2 C4.67 2.66 4.34 3.32 4 4 C1.03 3.505 1.03 3.505 -2 3 C-2 3.66 -2 4.32 -2 5 C-7.28 5 -12.56 5 -18 5 C-18 31.73 -18 58.46 -18 86 C-20.564 83.436 -22.015 81.534 -23.75 78.438 C-24.199 77.652 -24.647 76.867 -25.109 76.059 C-25.403 75.379 -25.697 74.7 -26 74 C-25.505 73.01 -25.505 73.01 -25 72 C-22.525 75.465 -22.525 75.465 -20 79 C-20.167 77.71 -20.335 76.421 -20.508 75.092 C-21.048 70.2 -21.041 65.436 -20.879 60.523 C-20.855 59.643 -20.832 58.762 -20.807 57.854 C-20.757 55.987 -20.704 54.119 -20.648 52.252 C-20.505 47.43 -20.381 42.608 -20.258 37.785 C-20.233 36.845 -20.209 35.905 -20.184 34.937 C-19.925 24.624 -19.897 14.316 -20 4 C-18 3 -18 3 -15.062 3.062 C-11.824 3.271 -11.824 3.271 -9 1 C-8.01 1.99 -7.02 2.98 -6 4 C-5.67 3.01 -5.34 2.02 -5 1 C-5 1.99 -5 2.98 -5 4 C-4.546 3.526 -4.092 3.051 -3.625 2.562 C-2 1 -2 1 0 0 Z " fill="#7D8ED5" transform="translate(730,266)"/>
<path d="M0 0 C1.583 -0.027 3.167 -0.046 4.75 -0.062 C6.073 -0.08 6.073 -0.08 7.422 -0.098 C9.782 -0.008 11.745 0.331 14 1 C14 1.99 14 2.98 14 4 C16.967 5.648 19.656 6.443 23 7 C23 7.99 23 8.98 23 10 C22.67 10.33 22.34 10.66 22 11 C22.911 15.277 23.78 19.194 26 23 C25.01 23.33 24.02 23.66 23 24 C23.495 27.96 23.495 27.96 24 32 C22.02 32.495 22.02 32.495 20 33 C20.165 32.031 20.33 31.061 20.5 30.062 C21.514 22.665 21.836 15.675 18 9 C12.564 3.829 8.593 3.637 1.273 3.734 C-3.488 4.121 -5.377 4.839 -9 8 C-12.933 13.782 -14.214 20.133 -13 27 C-11.382 31.669 -9.699 35.66 -5.762 38.766 C-2.332 40.298 0.253 40.365 4 40.312 C5.794 40.307 5.794 40.307 7.625 40.301 C10.839 40.014 13.105 39.376 16 38 C12.109 42.467 9.419 42.817 3.688 43.25 C-1.674 42.886 -5.94 42.06 -9.84 38.16 C-12.777 34.449 -14 32.885 -14 28 C-14.99 28 -15.98 28 -17 28 C-16.691 25.562 -16.378 23.125 -16.062 20.688 C-15.975 19.997 -15.888 19.307 -15.799 18.596 C-15.408 15.599 -14.958 12.875 -14 10 C-13.34 10.33 -12.68 10.66 -12 11 C-11.918 10.402 -11.835 9.804 -11.75 9.188 C-10.786 6.376 -9.4 5.673 -7 4 C-6.34 3.01 -5.68 2.02 -5 1 C-5 1.66 -5 2.32 -5 3 C-3.35 2.34 -1.7 1.68 0 1 C0 0.67 0 0.34 0 0 Z M-16 25 C-15 27 -15 27 -15 27 Z " fill="#7E8BDA" transform="translate(669,896)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.64 1.66 5.28 2 8 C3.485 8.495 3.485 8.495 5 9 C5.725 16.844 6.211 24.197 5 32 C2.472 28.208 2.776 25.191 2.875 20.812 C2.884 20.063 2.893 19.313 2.902 18.541 C2.926 16.694 2.962 14.847 3 13 C2.432 13.309 1.863 13.619 1.277 13.938 C-2.375 15.641 -5.968 16.458 -10 16 C-11.735 14.059 -13.405 12.057 -15 10 C-19.457 9.385 -21.235 9.49 -25 12 C-24.34 12.66 -23.68 13.32 -23 14 C-23.66 14 -24.32 14 -25 14 C-25.33 15.32 -25.66 16.64 -26 18 C-26.33 17.67 -26.66 17.34 -27 17 C-27.33 18.65 -27.66 20.3 -28 22 C-27.01 21.67 -26.02 21.34 -25 21 C-24.67 23.31 -24.34 25.62 -24 28 C-24.66 28 -25.32 28 -26 28 C-24.251 30.083 -24.251 30.083 -22 32 C-18.105 32.211 -18.105 32.211 -15 31 C-14.01 31 -13.02 31 -12 31 C-14 34 -14 34 -17.25 34.938 C-21.91 35.015 -23.573 34.109 -27 31 C-29.569 26.095 -29.739 20.427 -29 15 C-27.674 11.891 -26.098 9.549 -23 8 C-18.556 7.477 -15.129 7.174 -11.438 9.875 C-10 12 -10 12 -10 14 C-6.535 13.505 -6.535 13.505 -3 13 C-5.485 8.215 -7.48 5.139 -12 2 C-10.68 2 -9.36 2 -8 2 C-8 2.66 -8 3.32 -8 4 C-7.361 4.247 -6.721 4.495 -6.062 4.75 C-4 6 -4 6 -3.25 8.125 C-3.126 9.053 -3.126 9.053 -3 10 C-2.34 10 -1.68 10 -1 10 C-1.206 9.092 -1.413 8.185 -1.625 7.25 C-2.013 3.89 -1.804 2.759 0 0 Z M-1 5 C0 8 0 8 0 8 Z M-2 11 C-1.34 11.66 -0.68 12.32 0 13 C0 12.34 0 11.68 0 11 C-0.66 11 -1.32 11 -2 11 Z M-27 23 C-27 24.65 -27 26.3 -27 28 C-26.67 28 -26.34 28 -26 28 C-26 26.35 -26 24.7 -26 23 C-26.33 23 -26.66 23 -27 23 Z " fill="#8A97E1" transform="translate(471,897)"/>
<path d="M0 0 C5.816 5.816 7.211 10.993 7.266 18.949 C7 21 7 21 5 24 C4.67 22.35 4.34 20.7 4 19 C2.02 19.99 2.02 19.99 0 21 C-0.049 20.325 -0.098 19.649 -0.148 18.953 C-0.223 18.061 -0.298 17.169 -0.375 16.25 C-0.445 15.368 -0.514 14.487 -0.586 13.578 C-0.999 11.009 -1.712 9.242 -3 7 C-4.578 7.031 -4.578 7.031 -6.188 7.062 C-9.345 7.011 -11.946 6.679 -15 6 C-15.99 6 -16.98 6 -18 6 C-17.031 5.402 -16.061 4.804 -15.062 4.188 C-11.769 2.289 -11.769 2.289 -11 -1 C-6.688 -2.268 -4.056 -1.943 0 0 Z " fill="#DDE2FD" transform="translate(684,901)"/>
<path d="M0 0 C0 3.917 -1.555 5.611 -3.711 8.77 C-8.193 16.525 -8.327 26.128 -6.383 34.73 C-3.374 42.464 2.626 47.147 10 50.562 C17.208 53.368 24.754 55.05 32.283 56.77 C40.626 58.718 48.615 61.014 55 67 C56.961 70.923 56.486 75.723 56 80 C54 82.688 54 82.688 52 84 C52.526 82.546 52.526 82.546 53.062 81.062 C54.327 76.669 54.117 72.421 53 68 C52.01 67.01 52.01 67.01 51 66 C50.01 66.495 50.01 66.495 49 67 C49 66.01 49 65.02 49 64 C46.69 63.67 44.38 63.34 42 63 C42 62.34 42 61.68 42 61 C40.742 60.773 39.484 60.546 38.188 60.312 C31.967 59.09 25.876 57.399 19.778 55.677 C15.399 54.478 11.588 53.719 7 54 C7.33 53.34 7.66 52.68 8 52 C5.624 50.391 5.624 50.391 3 49 C2.01 49.495 2.01 49.495 1 50 C0.608 49.299 0.216 48.597 -0.188 47.875 C-1.94 44.897 -1.94 44.897 -4.5 42.312 C-7.383 38.986 -8.326 35.376 -9.422 31.172 C-9.928 28.975 -9.928 28.975 -11 27 C-10.508 17.846 -7.625 8.031 -1.309 1.16 C-0.877 0.777 -0.445 0.394 0 0 Z " fill="#6D82D1" transform="translate(295,281)"/>
<path d="M0 0 C2 1 2 1 3.125 4.25 C3.858 7.391 4.094 9.82 4 13 C4.33 13.33 4.66 13.66 5 14 C5.072 15.853 5.084 17.708 5.062 19.562 C5.053 20.574 5.044 21.586 5.035 22.629 C5.024 23.411 5.012 24.194 5 25 C5.66 25.33 6.32 25.66 7 26 C7.33 24.68 7.66 23.36 8 22 C9.062 23.875 9.062 23.875 10 26 C9.67 26.66 9.34 27.32 9 28 C7.68 28 6.36 28 5 28 C5 31.63 5 35.26 5 39 C2.03 39 -0.94 39 -4 39 C-4.66 40.32 -5.32 41.64 -6 43 C-6.33 43 -6.66 43 -7 43 C-6.988 42.241 -6.977 41.481 -6.965 40.699 C-6.956 39.705 -6.947 38.711 -6.938 37.688 C-6.926 36.701 -6.914 35.715 -6.902 34.699 C-6.877 31.9 -6.877 31.9 -8 29 C-7.34 29 -6.68 29 -6 29 C-6.6 23.719 -6.6 23.719 -7.562 18.5 C-8.18 14.972 -8.061 11.58 -8 8 C-8.99 7.67 -9.98 7.34 -11 7 C-11.33 6.01 -11.66 5.02 -12 4 C-14.31 4 -16.62 4 -19 4 C-19.33 4.66 -19.66 5.32 -20 6 C-20 3 -20 3 -19 2 C-14.635 1.46 -11.133 1.178 -7.5 3.812 C-4.804 7.745 -4.882 10.988 -4.902 15.637 C-4.906 16.935 -4.909 18.234 -4.912 19.572 C-4.92 20.944 -4.929 22.316 -4.938 23.688 C-4.943 25.071 -4.947 26.454 -4.951 27.838 C-4.963 31.225 -4.979 34.613 -5 38 C-2.69 38 -0.38 38 2 38 C1.881 33.552 1.757 29.105 1.628 24.658 C1.585 23.147 1.543 21.636 1.503 20.125 C1.444 17.945 1.381 15.765 1.316 13.586 C1.28 12.277 1.243 10.968 1.205 9.619 C1.019 6.337 0.613 3.225 0 0 Z " fill="#9DACEA" transform="translate(499,491)"/>
<path d="M0 0 C2.797 2.018 4.104 3.655 5.496 6.801 C5.166 7.791 4.836 8.781 4.496 9.801 C3.939 8.831 3.382 7.862 2.809 6.863 C0.851 3.65 0.851 3.65 -1.941 3.051 C-2.787 2.968 -3.633 2.886 -4.504 2.801 C-6.183 2.166 -7.854 1.508 -9.504 0.801 C-9.174 0.141 -8.844 -0.519 -8.504 -1.199 C-15.091 -2.245 -20.096 -2.032 -26.504 -0.199 C-25.844 0.131 -25.184 0.461 -24.504 0.801 C-24.834 1.461 -25.164 2.121 -25.504 2.801 C-27.484 2.801 -29.464 2.801 -31.504 2.801 C-32.494 8.246 -32.494 8.246 -33.504 13.801 C-32.844 14.131 -32.184 14.461 -31.504 14.801 C-30.844 15.791 -30.184 16.781 -29.504 17.801 C-26.941 18.53 -26.941 18.53 -24.504 18.801 C-24.504 19.461 -24.504 20.121 -24.504 20.801 C-19.972 21.735 -19.972 21.735 -15.504 20.801 C-15.504 21.791 -15.504 22.781 -15.504 23.801 C-13.194 23.801 -10.884 23.801 -8.504 23.801 C-8.504 24.461 -8.504 25.121 -8.504 25.801 C-7.649 25.861 -6.795 25.922 -5.914 25.984 C-4.809 26.068 -3.705 26.152 -2.566 26.238 C-1.464 26.319 -0.362 26.401 0.773 26.484 C3.496 26.801 3.496 26.801 4.496 27.801 C5.486 27.801 6.476 27.801 7.496 27.801 C7.001 28.791 7.001 28.791 6.496 29.801 C7.156 29.801 7.816 29.801 8.496 29.801 C8.001 30.791 8.001 30.791 7.496 31.801 C3.046 30.582 -1.401 29.355 -5.846 28.12 C-7.357 27.702 -8.868 27.286 -10.379 26.872 C-12.561 26.275 -14.739 25.669 -16.918 25.062 C-17.587 24.881 -18.256 24.7 -18.945 24.513 C-25.351 22.712 -31.107 20.811 -34.504 14.801 C-35.359 10.082 -34.965 6.724 -32.816 2.426 C-24.001 -7.581 -10.786 -5.543 0 0 Z " fill="#7B8DD4" transform="translate(344.50390625,293.19921875)"/>
<path d="M0 0 C0.728 0.006 1.456 0.011 2.207 0.017 C4.53 0.04 6.853 0.09 9.176 0.141 C10.752 0.161 12.328 0.179 13.904 0.195 C17.766 0.239 21.627 0.308 25.488 0.391 C25.158 1.051 24.828 1.711 24.488 2.391 C14.258 2.391 4.028 2.391 -6.512 2.391 C-7.502 6.021 -8.492 9.651 -9.512 13.391 C-10.075 15.248 -10.653 17.102 -11.258 18.947 C-11.565 19.888 -11.872 20.83 -12.188 21.8 C-12.671 23.266 -12.671 23.266 -13.164 24.762 C-13.505 25.805 -13.847 26.849 -14.199 27.924 C-15.28 31.226 -16.364 34.527 -17.449 37.828 C-18.173 40.037 -18.896 42.246 -19.619 44.455 C-22.891 54.442 -26.181 64.423 -29.512 74.391 C-30.388 71.204 -30.52 69.56 -29.512 66.391 C-29.187 65.305 -28.862 64.22 -28.527 63.102 C-28.125 61.804 -27.722 60.507 -27.316 59.211 C-26.988 58.16 -26.988 58.16 -26.653 57.087 C-25.041 51.97 -23.336 46.89 -21.582 41.82 C-21.092 40.396 -20.603 38.971 -20.113 37.547 C-19.369 35.383 -18.621 33.222 -17.861 31.063 C-17.11 28.924 -16.376 26.779 -15.645 24.633 C-15.299 23.672 -15.299 23.672 -14.947 22.691 C-13.559 18.555 -13.461 15.618 -14.512 11.391 C-13.522 11.721 -12.532 12.051 -11.512 12.391 C-10.914 9.628 -10.512 7.23 -10.512 4.391 C-8.204 -0.454 -4.829 -0.141 0 0 Z " fill="#758AD4" transform="translate(526.51171875,108.609375)"/>
<path d="M0 0 C2.017 3.025 2.259 3.706 2.351 7.117 C2.377 7.941 2.403 8.766 2.43 9.615 C2.448 10.503 2.466 11.39 2.484 12.305 C2.508 13.221 2.533 14.137 2.557 15.081 C2.606 17.019 2.649 18.958 2.688 20.896 C2.75 23.857 2.833 26.817 2.918 29.777 C2.962 31.661 3.005 33.546 3.047 35.43 C3.073 36.313 3.099 37.197 3.125 38.107 C3.199 42.543 3.115 45.967 1 50 C-1.31 49.34 -3.62 48.68 -6 48 C-6 48.66 -6 49.32 -6 50 C-5.34 50.33 -4.68 50.66 -4 51 C-5.485 51.495 -5.485 51.495 -7 52 C-7.199 51.095 -7.199 51.095 -7.402 50.172 C-7.579 49.373 -7.756 48.573 -7.938 47.75 C-8.112 46.961 -8.286 46.172 -8.465 45.359 C-8.948 43.231 -9.462 41.115 -10 39 C-9.34 39 -8.68 39 -8 39 C-8.66 37.02 -9.32 35.04 -10 33 C-9.34 33 -8.68 33 -8 33 C-8.108 32.095 -8.108 32.095 -8.219 31.172 C-8.312 30.373 -8.404 29.573 -8.5 28.75 C-8.593 27.961 -8.686 27.172 -8.781 26.359 C-9 24 -9 24 -9 20 C-9.035 18.846 -9.035 18.846 -9.07 17.668 C-9.188 12.334 -8.754 8.043 -7 3 C-6.216 2.876 -5.433 2.752 -4.625 2.625 C-1.859 2.209 -1.859 2.209 0 0 Z M-6 5 C-6 18.86 -6 32.72 -6 47 C-3.69 47 -1.38 47 1 47 C1 33.14 1 19.28 1 5 C-1.31 5 -3.62 5 -6 5 Z " fill="#909FE5" transform="translate(456,482)"/>
<path d="M0 0 C5.61 0 11.22 0 17 0 C16.67 0.66 16.34 1.32 16 2 C11.38 2 6.76 2 2 2 C2 27.74 2 53.48 2 80 C1.01 80.495 1.01 80.495 0 81 C0 54.27 0 27.54 0 0 Z " fill="#F7F8FE" transform="translate(712,271)"/>
<path d="M0 0 C3.342 1.366 4.659 2.55 6.188 5.812 C6.511 6.479 6.835 7.145 7.168 7.832 C10.123 15.532 10.248 24.72 6.906 32.285 C4.498 36.843 1.989 39.659 -2.949 41.488 C-8.748 42.935 -14.07 42.924 -19.375 40 C-24.122 36.624 -26.225 32.173 -27.582 26.617 C-28.811 18.92 -28.766 11.905 -25 5 C-24 8 -24 8 -25 11 C-25.122 13.493 -25.185 15.945 -25.188 18.438 C-25.202 19.756 -25.216 21.075 -25.23 22.434 C-25.019 25.71 -24.683 27.28 -23 30 C-22.377 32.117 -22 33.794 -22 36 C-16.343 39.264 -10.352 39.208 -4 39 C-0.722 38.248 -0.722 38.248 1 35 C1.823 34.177 2.66 33.368 3.5 32.562 C5.917 30.045 6.137 28.323 6.125 24.938 C6.457 21.229 6.457 21.229 5 20 C4.863 17.473 4.836 15.461 5.688 13.062 C6.387 8.444 2.353 3.809 0 0 Z " fill="#8294DF" transform="translate(285,481)"/>
<path d="M0 0 C0.566 10.797 0.566 10.797 -3 15 C-6.835 18.419 -9.612 18.444 -14.586 18.297 C-17.671 17.917 -19.623 16.993 -22 15 C-22.33 14.34 -22.66 13.68 -23 13 C-25.322 12.593 -27.657 12.256 -30 12 C-29.67 13.32 -29.34 14.64 -29 16 C-29.66 16 -30.32 16 -31 16 C-31.33 13.69 -31.66 11.38 -32 9 C-30.35 8.67 -28.7 8.34 -27 8 C-27 8.66 -27 9.32 -27 10 C-24.229 8.294 -24.229 8.294 -23 7 C-24.65 5.68 -26.3 4.36 -28 3 C-27.67 2.34 -27.34 1.68 -27 1 C-25.917 1.928 -25.917 1.928 -24.812 2.875 C-19.9 6.587 -16.346 7.492 -10.289 6.691 C-6.903 5.669 -4.801 3.726 -2.312 1.277 C-1 0 -1 0 0 0 Z M-4 5 C-4.33 5.66 -4.66 6.32 -5 7 C-3.68 6.34 -2.36 5.68 -1 5 C-1.99 5 -2.98 5 -4 5 Z M-21 8 C-18.435 10.565 -16.523 10.54 -13 11 C-13.66 11.66 -14.32 12.32 -15 13 C-14.01 14.485 -14.01 14.485 -13 16 C-8.25 16.564 -8.25 16.564 -4.062 14.625 C-2.818 13.045 -2.818 13.045 -3 11 C-3.66 11 -4.32 11 -5 11 C-5.33 10.01 -5.66 9.02 -6 8 C-10.95 8 -15.9 8 -21 8 Z M-3 8 C-2 11 -2 11 -2 11 Z M-21 11 C-19.515 11.99 -19.515 11.99 -18 13 C-18.33 13.66 -18.66 14.32 -19 15 C-18.01 15.33 -17.02 15.66 -16 16 C-15.67 14.35 -15.34 12.7 -15 11 C-16.98 11 -18.96 11 -21 11 Z " fill="#8898DF" transform="translate(540,521)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 0.66 10 1.32 10 2 C11.516 2.062 11.516 2.062 13.062 2.125 C17.859 2.761 20.675 5.695 24 9 C23.67 9.66 23.34 10.32 23 11 C22.134 10.216 21.268 9.433 20.375 8.625 C15.214 4.388 10.091 3.529 3.531 3.688 C-2.185 4.393 -5.878 6.59 -9.438 11.062 C-11.92 15.729 -12.183 19.576 -12.188 24.75 C-12.2 25.441 -12.212 26.132 -12.225 26.844 C-12.238 31.595 -11.494 35.488 -10 40 C-11.485 40.495 -11.485 40.495 -13 41 C-13.144 40.113 -13.289 39.226 -13.438 38.312 C-13.623 37.219 -13.809 36.126 -14 35 C-14.186 33.67 -14.186 33.67 -14.375 32.312 C-15.076 29.718 -15.953 28.691 -18 27 C-17.938 27.908 -17.876 28.815 -17.812 29.75 C-18.017 33.29 -18.646 34.449 -21 37 C-20.835 36.192 -20.67 35.384 -20.5 34.551 C-19.913 30.385 -19.928 26.384 -20 22.188 C-20.045 19.452 -20.047 16.736 -20 14 C-19 13 -19 13 -15.438 12.938 C-14.303 12.958 -13.169 12.979 -12 13 C-11.959 12.443 -11.918 11.886 -11.875 11.312 C-10.073 6.549 -5.403 4.12 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z M1 1 C5 2 5 2 5 2 Z M-15 14 C-15.66 14.66 -16.32 15.32 -17 16 C-17.33 16 -17.66 16 -18 16 C-18 18.31 -18 20.62 -18 23 C-15.353 20.353 -14.401 17.438 -13 14 C-13.66 14 -14.32 14 -15 14 Z M-15 18 C-14 20 -14 20 -14 20 Z " fill="#7F91DD" transform="translate(685,483)"/>
<path d="M0 0 C3.199 -0.089 5.927 0.089 9 1 C9 1.99 9 2.98 9 4 C6.36 4 3.72 4 1 4 C1 17.86 1 31.72 1 46 C3.31 46 5.62 46 8 46 C7.99 45.203 7.979 44.406 7.968 43.585 C7.927 39.973 7.901 36.362 7.875 32.75 C7.858 31.496 7.841 30.241 7.824 28.949 C7.818 27.744 7.811 26.539 7.805 25.297 C7.794 24.187 7.784 23.076 7.773 21.933 C7.995 19.06 8.507 17.428 10 15 C11.118 18.539 11.047 21.717 10.914 25.41 C10.87 26.647 10.826 27.884 10.781 29.158 C10.73 30.447 10.678 31.735 10.625 33.062 C10.581 34.355 10.537 35.647 10.492 36.979 C10.13 46.609 10.13 46.609 9 50 C8.34 49.67 7.68 49.34 7 49 C4.844 49 3.076 49.447 1 50 C-1.699 45.652 -1.025 41.626 -0.777 36.68 C-0.851 35.795 -0.924 34.911 -1 34 C-1.99 33.34 -2.98 32.68 -4 32 C-4 31.34 -4 30.68 -4 30 C-3.34 30 -2.68 30 -2 30 C-0.967 25.116 -0.967 25.116 -2.062 20.375 C-3.328 17.17 -2.307 16.121 -1 13 C-0.888 10.968 -0.938 8.972 -0.996 6.938 C-1.001 4.533 -0.582 2.327 0 0 Z M-1 15 C-1 16.98 -1 18.96 -1 21 C-0.67 21 -0.34 21 0 21 C0 19.02 0 17.04 0 15 C-0.33 15 -0.66 15 -1 15 Z M3 47 C4 49 4 49 4 49 Z " fill="#8195E4" transform="translate(718,483)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.15 26.684 0.753 53.327 0 80 C-0.33 80 -0.66 80 -1 80 C-1.33 59.21 -1.66 38.42 -2 17 C-2.66 17 -3.32 17 -4 17 C-4.258 18.27 -4.516 19.539 -4.781 20.848 C-5.97 26.189 -7.674 31.285 -9.5 36.438 C-9.962 37.782 -9.962 37.782 -10.434 39.154 C-10.734 40.01 -11.034 40.865 -11.344 41.746 C-11.614 42.516 -11.884 43.286 -12.162 44.08 C-13 46 -13 46 -15 48 C-13.77 41.969 -12.198 36.176 -10.215 30.352 C-9.823 29.183 -9.823 29.183 -9.423 27.99 C-8.599 25.534 -7.768 23.079 -6.938 20.625 C-6.377 18.957 -5.816 17.289 -5.256 15.621 C-4.234 12.581 -3.21 9.541 -2.183 6.502 C-1.451 4.336 -0.723 2.169 0 0 Z M-2 11 C-2.33 12.65 -2.66 14.3 -3 16 C-2.34 15.67 -1.68 15.34 -1 15 C-1.33 13.68 -1.66 12.36 -2 11 Z " fill="#8696D9" transform="translate(529,144)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7.02 0.817 7.04 1.634 7.06 2.475 C7.156 6.192 7.265 9.908 7.375 13.625 C7.406 14.91 7.437 16.195 7.469 17.52 C7.507 18.763 7.546 20.007 7.586 21.289 C7.617 22.431 7.649 23.572 7.681 24.749 C8.014 28.144 8.627 30.876 10 34 C13.162 35.054 15.869 35.313 19.188 35.562 C20.274 35.646 21.361 35.73 22.48 35.816 C23.312 35.877 24.143 35.938 25 36 C25 34.02 25 32.04 25 30 C23.02 30 21.04 30 19 30 C19.33 29.01 19.66 28.02 20 27 C22.64 27 25.28 27 28 27 C28 27.66 28 28.32 28 29 C28.99 28.01 29.98 27.02 31 26 C31.845 29.378 32.108 31.675 31 35 C30.402 35.124 29.804 35.247 29.188 35.375 C26.738 35.955 26.738 35.955 24 38 C21.52 38.414 21.52 38.414 18.812 38.625 C17.911 38.7 17.01 38.775 16.082 38.852 C15.395 38.901 14.708 38.95 14 39 C14 38.34 14 37.68 14 37 C13.423 36.959 12.845 36.918 12.25 36.875 C9.588 35.84 8.321 34.497 6.809 32.09 C5.378 28.392 5.4 24.538 5.273 20.609 C5.26 17.84 5.26 17.84 3 16 C2.898 12.672 3.07 9.392 3.223 6.066 C3.288 2.998 3.288 2.998 1.496 1.09 C1.002 0.73 0.509 0.371 0 0 Z M5 8 C6 11 6 11 6 11 Z " fill="#7587D7" transform="translate(400,493)"/>
<path d="M0 0 C4.414 4.414 3.22 11.275 3.25 17.125 C3.271 18.12 3.291 19.115 3.312 20.141 C3.358 28.855 1.855 36.672 -2.75 44.188 C-3.199 44.944 -3.647 45.701 -4.109 46.48 C-6.744 49.992 -9.84 52.963 -13 56 C6.691 57.505 6.691 57.505 24.887 51.688 C26.461 50.248 26.461 50.248 28 48 C28 52 28 52 25.625 54.688 C23 57 23 57 21 58 C21 57.34 21 56.68 21 56 C20.031 56.309 19.061 56.619 18.062 56.938 C9.837 59.26 1.421 59.132 -7.062 59.062 C-8.119 59.058 -9.175 59.053 -10.264 59.049 C-12.843 59.037 -15.421 59.021 -18 59 C-18 57 -18 57 -15.812 54.625 C-13 52 -13 52 -10.312 50 C-7.73 47.766 -7.134 46.16 -6 43 C-5.52 42.545 -5.041 42.09 -4.547 41.621 C-2.714 39.701 -2.159 38.148 -1.375 35.625 C-1.135 34.875 -0.895 34.125 -0.648 33.352 C0.864 27.865 1.153 22.85 1.125 17.188 C1.129 16.419 1.133 15.65 1.137 14.857 C1.128 9.821 0.716 4.986 0 0 Z " fill="#8192DB" transform="translate(547,717)"/>
<path d="M0 0 C10.89 0 21.78 0 33 0 C42.791 26.999 42.791 26.999 46.147 37.779 C46.71 39.537 47.348 41.272 48 43 C48.99 43.495 48.99 43.495 50 44 C50.174 46.014 50.346 48.028 50.516 50.043 C51.249 54.518 52.764 58.68 54.273 62.946 C56 67.838 56 67.838 56 70 C56.66 70 57.32 70 58 70 C58 72.31 58 74.62 58 77 C58.66 77 59.32 77 60 77 C59.505 78.98 59.505 78.98 59 81 C56.83 77.745 55.854 74.885 54.637 71.168 C54.408 70.476 54.18 69.783 53.944 69.07 C53.188 66.777 52.438 64.482 51.688 62.188 C50.616 58.931 49.541 55.676 48.465 52.422 C48.186 51.578 47.908 50.735 47.62 49.865 C45.562 43.65 43.452 37.454 41.316 31.266 C41.017 30.396 40.717 29.527 40.408 28.631 C39.233 25.225 38.057 21.818 36.872 18.415 C36.051 16.058 35.236 13.699 34.422 11.34 C34.052 10.288 34.052 10.288 33.676 9.214 C32.687 6.337 32 4.067 32 1 C21.44 1 10.88 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#5F76C9" transform="translate(435,109)"/>
<path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.062 C4.789 2.765 4.557 4.863 4.551 7.889 C4.551 9.11 4.551 10.331 4.551 11.59 C4.546 12.927 4.54 14.264 4.535 15.602 C4.533 16.966 4.532 18.331 4.531 19.696 C4.527 23.29 4.517 26.883 4.506 30.477 C4.496 34.144 4.491 37.81 4.486 41.477 C4.476 48.672 4.459 55.867 4.438 63.062 C3.777 62.732 3.118 62.403 2.438 62.062 C2.438 42.592 2.438 23.123 2.438 3.062 C0.127 3.062 -2.183 3.062 -4.562 3.062 C-4.562 9.002 -4.562 14.942 -4.562 21.062 C-7.562 17.062 -7.562 17.062 -7.707 14.949 C-7.597 14.223 -7.488 13.498 -7.375 12.75 C-7.258 11.97 -7.14 11.19 -7.02 10.387 C-4.994 0.088 -4.994 0.088 0 0 Z " fill="#9CAAEB" transform="translate(437.5625,466.9375)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C0.01 2.66 -0.98 3.32 -2 4 C-1.649 4.557 -1.299 5.114 -0.938 5.688 C0.232 8.572 -0.147 10.052 -1 13 C-1.33 13.66 -1.66 14.32 -2 15 C-2.37 17.604 -2.702 20.2 -3 22.812 C-3.513 27.244 -4.04 31.638 -5 36 C-5.66 36 -6.32 36 -7 36 C-7.186 36.784 -7.371 37.567 -7.562 38.375 C-10.657 46.179 -15.735 52.262 -22.203 57.527 C-24.028 58.968 -24.028 58.968 -25.617 60.656 C-27.359 62.349 -28.64 62.514 -31 63 C-32.674 63.647 -34.343 64.31 -36 65 C-36 65.66 -36 66.32 -36 67 C-40.316 67.762 -43.744 68.161 -48 67 C-48 66.34 -48 65.68 -48 65 C-47.154 64.83 -46.309 64.66 -45.438 64.484 C-31.349 61.475 -20.592 55.972 -12.5 43.609 C-6.671 33.469 -4.782 22.991 -4.375 11.438 C-4.336 10.524 -4.298 9.611 -4.258 8.67 C-4.165 6.447 -4.079 4.224 -4 2 C-2.68 1.34 -1.36 0.68 0 0 Z " fill="#6E82D2" transform="translate(837,161)"/>
<path d="M0 0 C7.92 0 15.84 0 24 0 C24 0.33 24 0.66 24 1 C17.07 1 10.14 1 3 1 C3 27.07 3 53.14 3 80 C2.01 79.01 1.02 78.02 0 77 C0.287 74.993 0.631 72.994 1 71 C0.852 68.662 0.57 66.371 0.254 64.051 C-0.133 60.927 0.101 58.318 0.562 55.188 C1.04 51.597 0.8 48.557 0 45 C0.66 45 1.32 45 2 45 C1.835 44.051 1.67 43.102 1.5 42.125 C0.929 38.21 0.981 34.386 1.035 30.438 C1.003 28.225 0.796 26.18 0.438 24 C0 21 0 21 1 19 C1.098 16.156 1.139 13.343 1.125 10.5 C1.129 9.715 1.133 8.93 1.137 8.121 C1.238 3.927 1.238 3.927 0 0 Z " fill="#889ADC" transform="translate(688,110)"/>
<path d="M0 0 C1.225 1.837 2.404 3.708 3.5 5.625 C7.261 9.198 10.96 9.037 16 9 C19.041 8.798 21.979 8.438 25 8 C22.537 10.697 20.683 11.833 16.949 12.203 C11.856 12.309 8.354 11.937 4 9 C0 4.684 0 4.684 0 2 C-0.99 2.495 -0.99 2.495 -2 3 C-4 3.04 -6 3.043 -8 3 C-5.559 8.787 -3.232 12.442 2 16 C9.35 18.926 17.912 19.313 25.5 16.875 C29.624 14.666 31.286 12.752 34 9 C34.66 8.34 35.32 7.68 36 7 C35.731 11.573 33.822 13.766 30.629 16.781 C28.513 18.365 26.597 18.703 24 19 C23.67 19.66 23.34 20.32 23 21 C17.662 22.394 13.089 22.218 8 20 C8 20.66 8 21.32 8 22 C6 21 6 21 3 19 C1.824 18.546 0.649 18.092 -0.562 17.625 C-4 16 -4 16 -5.25 14.125 C-5.621 13.073 -5.621 13.073 -6 12 C-6.536 11.154 -7.072 10.309 -7.625 9.438 C-9 7 -9 7 -9 4 C-9.66 3.67 -10.32 3.34 -11 3 C-10.34 3 -9.68 3 -9 3 C-9 2.34 -9 1.68 -9 1 C-4.545 0.505 -4.545 0.505 0 0 Z " fill="#8395DE" transform="translate(583,511)"/>
<path d="M0 0 C-2.551 8.505 -6.061 16.591 -11 24 C-11.66 23.67 -12.32 23.34 -13 23 C-12.42 22 -12.42 22 -11.828 20.98 C-8.151 14.472 -5.391 8.082 -3 1 C-10.59 1.33 -18.18 1.66 -26 2 C-27.32 4.64 -28.64 7.28 -30 10 C-34.361 15.521 -40.226 17.771 -47 19 C-51.969 19.489 -55.584 19.485 -60 17 C-59.01 16.34 -58.02 15.68 -57 15 C-56.67 15.33 -56.34 15.66 -56 16 C-54.544 16.099 -53.084 16.13 -51.625 16.125 C-50.438 16.129 -50.438 16.129 -49.227 16.133 C-46.827 16.086 -46.827 16.086 -44 15 C-43.237 14.897 -42.474 14.794 -41.688 14.688 C-36.83 13.445 -33.773 11.009 -30.75 7.062 C-29.23 4.402 -28.099 1.849 -27 -1 C-22.876 -1.225 -18.751 -1.427 -14.625 -1.625 C-13.449 -1.689 -12.274 -1.754 -11.062 -1.82 C-9.941 -1.872 -8.82 -1.923 -7.664 -1.977 C-6.109 -2.055 -6.109 -2.055 -4.522 -2.135 C-2 -2 -2 -2 0 0 Z " fill="#677DD1" transform="translate(295,188)"/>
<path d="M0 0 C-0.175 0.58 -0.35 1.161 -0.531 1.759 C-2.578 8.558 -4.577 15.37 -6.562 22.188 C-7.018 23.749 -7.018 23.749 -7.482 25.342 C-8.065 27.342 -8.647 29.344 -9.227 31.345 C-9.698 32.962 -10.174 34.577 -10.66 36.19 C-11.476 38.925 -12 41.123 -12 44 C-15 43 -15 43 -16 42 C-16.66 42.66 -17.32 43.32 -18 44 C-21 43 -21 43 -21.891 41.222 C-22.245 40.083 -22.245 40.083 -22.605 38.922 C-22.997 37.675 -22.997 37.675 -23.396 36.402 C-23.658 35.527 -23.919 34.652 -24.188 33.75 C-24.46 32.89 -24.733 32.03 -25.014 31.145 C-26.308 26.933 -27.151 23.439 -27 19 C-25.161 22.379 -24.011 25.83 -22.875 29.5 C-22.522 30.624 -22.169 31.748 -21.805 32.906 C-21 36 -21 36 -21 40 C-18.36 40 -15.72 40 -13 40 C-12.878 38.999 -12.755 37.998 -12.629 36.967 C-12.067 33.045 -11.054 29.337 -9.934 25.535 C-9.636 24.518 -9.636 24.518 -9.333 23.48 C-8.705 21.34 -8.072 19.201 -7.438 17.062 C-7.008 15.601 -6.579 14.139 -6.15 12.678 C-5.105 9.117 -4.054 5.558 -3 2 C-4.98 2.33 -6.96 2.66 -9 3 C-10.98 10.26 -12.96 17.52 -15 25 C-16.289 19.846 -15.192 16.481 -13.875 11.438 C-13.674 10.574 -13.473 9.711 -13.266 8.822 C-11.648 2.553 -11.648 2.553 -8.527 -0.109 C-5.334 -1.235 -3.242 -0.829 0 0 Z " fill="#7987D8" transform="translate(747,897)"/>
<path d="M0 0 C3.124 2.556 4.032 5.127 5 9 C5.075 11.332 5.093 13.669 5 16 C5.99 16.33 6.98 16.66 8 17 C6.35 17.66 4.7 18.32 3 19 C3.247 19.908 3.495 20.815 3.75 21.75 C4 25 4 25 2.062 27.375 C1.382 27.911 0.701 28.447 0 29 C0.33 27.02 0.66 25.04 1 23 C-0.65 22.67 -2.3 22.34 -4 22 C-4.289 22.639 -4.577 23.279 -4.875 23.938 C-6 26 -6 26 -8 27 C-12.536 27.482 -16.596 27.856 -20.562 25.438 C-22 23 -22 23 -23 16 C-14.42 16 -5.84 16 3 16 C1.712 7.956 1.712 7.956 0 0 Z M-19 17 C-19 17.66 -19 18.32 -19 19 C-19.66 19 -20.32 19 -21 19 C-20.733 20.985 -20.733 20.985 -20 23 C-19.01 23.33 -18.02 23.66 -17 24 C-16.67 24.66 -16.34 25.32 -16 26 C-10.38 25.801 -10.38 25.801 -5.75 23.062 C-5.173 22.382 -4.595 21.701 -4 21 C-1.75 19.75 -1.75 19.75 0 19 C-1.991 16.562 -1.991 16.562 -5.914 16.805 C-7.484 16.818 -9.055 16.842 -10.625 16.875 C-11.828 16.889 -11.828 16.889 -13.055 16.902 C-15.037 16.926 -17.018 16.962 -19 17 Z " fill="#919DE5" transform="translate(584,904)"/>
<path d="M0 0 C0.124 0.619 0.247 1.237 0.375 1.875 C0.872 4.191 0.872 4.191 3 6 C3.042 8.333 3.042 10.667 3 13 C3.33 13.66 3.66 14.32 4 15 C-0.55 16.638 -5.079 15.838 -9.438 13.938 C-9.953 13.628 -10.469 13.319 -11 13 C-10.528 11.316 -10.055 9.632 -9.566 7.953 C-9.052 6.181 -8.556 4.403 -8.062 2.625 C-7.712 1.759 -7.361 0.893 -7 0 C-3.867 -1.044 -3.01 -0.934 0 0 Z M-5 2 C-4.67 2.66 -4.34 3.32 -4 4 C-3.67 3.34 -3.34 2.68 -3 2 C-3.66 2 -4.32 2 -5 2 Z " fill="#798DDD" transform="translate(771,515)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.564 1.413 1.129 1.825 0.68 2.25 C-3.725 6.521 -7.429 11.02 -11 16 C-9.742 15.676 -8.484 15.353 -7.188 15.02 C-1.767 13.822 3.465 13.655 9 13.688 C10.007 13.693 11.015 13.699 12.053 13.705 C28.36 13.966 45.054 17.058 59 26 C57 27 57 27 54.977 26.406 C53.413 25.804 51.851 25.2 50.289 24.594 C47.762 23.938 46.436 24.155 44 25 C44.33 23.68 44.66 22.36 45 21 C44.383 20.879 43.765 20.758 43.129 20.633 C42.282 20.465 41.435 20.298 40.562 20.125 C39.642 19.943 38.722 19.761 37.773 19.574 C35.393 19.081 33.046 18.521 30.688 17.938 C16.08 14.731 0.715 16.093 -14 18 C-11.8 13 -9.033 9.513 -5.312 5.625 C-4.801 5.08 -4.29 4.534 -3.764 3.973 C-2.515 2.643 -1.259 1.32 0 0 Z " fill="#7F90DA" transform="translate(461,660)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C2.66 1.67 3.32 1.34 4 1 C4 13.54 4 26.08 4 39 C2.68 39 1.36 39 0 39 C0 26.13 0 13.26 0 0 Z " fill="#D5DDFE" transform="translate(611,897)"/>
<path d="M0 0 C2.8 2.145 3.72 2.877 5 6 C5.887 12.73 6.056 18.343 2 24 C-0.37 26.37 -1.063 26.345 -4.312 26.438 C-8.045 26.359 -8.863 26.103 -12 23.75 C-15.54 18.883 -15.419 13.851 -15 8 C-14.015 4.516 -12.84 2.272 -10 0 C-6.123 -1.292 -3.955 -1.023 0 0 Z M-13 6 C-13.42 8.712 -13.42 8.712 -13 11 C-12.67 10.01 -12.34 9.02 -12 8 C-11.34 8 -10.68 8 -10 8 C-10.99 11.96 -10.99 11.96 -12 16 C-11.34 16 -10.68 16 -10 16 C-10.021 16.763 -10.041 17.526 -10.062 18.312 C-10.129 21.102 -10.129 21.102 -9 24 C-7.563 24.081 -6.126 24.139 -4.688 24.188 C-3.887 24.222 -3.086 24.257 -2.262 24.293 C0.058 24.184 0.058 24.184 1.73 22.676 C4.935 18.446 4.459 13.046 4 8 C2.387 4.314 1.37 3.246 -2 1 C-7.486 0.165 -9.697 1.629 -13 6 Z M-13 17 C-12 19 -12 19 -12 19 Z M-11 21 C-10 23 -10 23 -10 23 Z " fill="#8795E0" transform="translate(536,905)"/>
<path d="M0 0 C5.247 5.247 5.323 14.885 5.375 21.938 C5.251 33.41 3.085 45.795 -4.527 54.809 C-9.384 59.391 -15.801 62.911 -22.57 63.055 C-25.648 62.748 -28.224 62.388 -31 61 C-30.505 60.505 -30.505 60.505 -30 60 C-28.145 60.033 -26.291 60.102 -24.438 60.188 C-18.455 60.266 -15.055 59.111 -10 56 C-10 55.34 -10 54.68 -10 54 C-9.34 54 -8.68 54 -8 54 C-7.67 53.34 -7.34 52.68 -7 52 C-6.154 51.196 -5.309 50.391 -4.438 49.562 C-1.655 47.227 -1.655 47.227 -2 44 C-1.34 44 -0.68 44 0 44 C2.913 29.747 3.643 14.181 0 0 Z " fill="#768BD5" transform="translate(467,308)"/>
<path d="M0 0 C2 3 2 3 1.758 5.102 C1.549 5.934 1.34 6.767 1.125 7.625 C-2.767 26.933 0.566 46.354 11 63 C13.134 65.923 15.397 68.492 18 71 C18.51 71.522 19.021 72.044 19.547 72.582 C20.681 73.738 21.837 74.873 23 76 C18.313 74.722 15.896 72.058 12.746 68.559 C10.823 66.842 9.516 66.402 7 66 C6.938 65.051 6.876 64.103 6.812 63.125 C6.346 59.76 5.216 57.242 3.625 54.25 C0.846 49.005 -0.588 43.735 -2 38 C-2.495 36.515 -2.495 36.515 -3 35 C-3.227 30.746 -3.228 26.49 -3.24 22.231 C-3.25 20.139 -3.281 18.049 -3.312 15.957 C-3.319 14.62 -3.324 13.283 -3.328 11.945 C-3.337 10.734 -3.347 9.522 -3.356 8.274 C-2.972 4.745 -2.047 2.859 0 0 Z M-2 5 C-1 9 -1 9 -1 9 Z " fill="#788DD7" transform="translate(387,307)"/>
<path d="M0 0 C14.52 0 29.04 0 44 0 C44 5.61 44 11.22 44 17 C43.67 17 43.34 17 43 17 C43 12.05 43 7.1 43 2 C29.47 2 15.94 2 2 2 C2 6.95 2 11.9 2 17 C1.67 17.66 1.34 18.32 1 19 C3.97 19 6.94 19 10 19 C10 19.33 10 19.66 10 20 C6.7 20 3.4 20 0 20 C0 13.4 0 6.8 0 0 Z " fill="#EFF2FC" transform="translate(789,163)"/>
<path d="M0 0 C4.16 2.34 5.542 4.555 7 9 C7.66 15.366 7.96 21.675 4 27 C0.398 30.179 -3.127 30.585 -7.781 30.414 C-11.735 29.676 -13.327 27.999 -16 25 C-18.719 19.315 -18.752 13.751 -16.812 7.812 C-13.51 0.011 -7.939 -1.293 0 0 Z M-13 3 C-17.038 8.291 -16.747 14.646 -16 21 C-14.843 24.782 -14.843 24.782 -12 27 C-7.426 28.331 -2.549 28.516 2 27 C2.66 26.34 3.32 25.68 4 25 C4 24.01 4 23.02 4 22 C3.34 21.67 2.68 21.34 2 21 C3.32 20.34 4.64 19.68 6 19 C6.446 12.75 5.792 8.146 2 3 C-2.839 0.022 -8.111 0.169 -13 3 Z " fill="#8295DF" transform="translate(651,493)"/>
<path d="M0 0 C-3 1 -3 1 -6.25 0.062 C-13.621 -2.026 -20.044 -0.963 -27 2 C-34.15 6.363 -37.411 12.965 -39.588 20.856 C-40.466 25.422 -40.38 30.053 -40.375 34.688 C-40.377 35.754 -40.379 36.82 -40.381 37.918 C-40.291 44.919 -39.603 50.573 -36.777 57.023 C-36.521 57.676 -36.264 58.328 -36 59 C-36.33 59.66 -36.66 60.32 -37 61 C-43.933 48.751 -44.922 33.947 -41.684 20.301 C-39.2 11.943 -35.379 4.416 -27.816 -0.387 C-18.936 -4.542 -8.549 -4.883 0 0 Z " fill="#5E74C8" transform="translate(374,132)"/>
<path d="M0 0 C6.57 0.137 10.468 1.384 15.625 5.625 C17.943 8.473 18.908 10.778 19.812 14.312 C16.562 14.625 16.562 14.625 12.812 14.312 C10.688 11.938 10.688 11.938 8.812 9.312 C4.714 7.035 0.41 6.78 -4.188 6.312 C-4.188 5.982 -4.188 5.653 -4.188 5.312 C-0.723 4.817 -0.723 4.817 2.812 4.312 C2.812 3.653 2.812 2.992 2.812 2.312 C-0.488 2.312 -3.788 2.312 -7.188 2.312 C-4.625 -0.25 -3.556 0.015 0 0 Z " fill="#E3E6FD" transform="translate(292.1875,883.6875)"/>
<path d="M0 0 C10.545 0.409 10.545 0.409 15 5 C15.534 5.422 16.067 5.843 16.617 6.277 C18.6 8.747 18.297 10.572 18.188 13.688 C18.16 14.681 18.133 15.675 18.105 16.699 C18.071 17.458 18.036 18.218 18 19 C14.917 19.029 11.833 19.047 8.75 19.062 C7.877 19.071 7.005 19.079 6.105 19.088 C5.261 19.091 4.417 19.094 3.547 19.098 C2.384 19.106 2.384 19.106 1.198 19.114 C-0.993 19 -2.896 18.606 -5 18 C-5 17.34 -5 16.68 -5 16 C0.94 15.67 6.88 15.34 13 15 C12.235 9.138 12.235 9.138 9 5 C7.074 4.248 7.074 4.248 5 4 C2.812 3.625 2.812 3.625 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D8DFFE" transform="translate(568,900)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C10 13.86 10 27.72 10 42 C7 43 7 43 4.812 42.062 C3.915 41.537 3.915 41.537 3 41 C4.98 41 6.96 41 9 41 C9 28.46 9 15.92 9 3 C6.69 3 4.38 3 2 3 C2.01 3.736 2.021 4.473 2.032 5.231 C2.073 8.571 2.099 11.91 2.125 15.25 C2.142 16.409 2.159 17.568 2.176 18.762 C2.182 19.877 2.189 20.992 2.195 22.141 C2.206 23.167 2.216 24.193 2.227 25.251 C1.998 28.02 1.424 29.644 0 32 C-1.226 29.547 -1.034 28.157 -0.879 25.43 C-0.831 24.534 -0.782 23.638 -0.732 22.715 C-0.676 21.778 -0.62 20.841 -0.562 19.875 C-0.51 18.93 -0.458 17.985 -0.404 17.012 C-0.274 14.674 -0.139 12.337 0 10 C-0.66 10 -1.32 10 -2 10 C-1.67 9.34 -1.34 8.68 -1 8 C-1.33 7.67 -1.66 7.34 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z M4 1 C8 2 8 2 8 2 Z " fill="#7D8CDC" transform="translate(341,896)"/>
<path d="M0 0 C0.71 0.545 0.71 0.545 1.434 1.102 C3.608 2.755 5.801 4.38 8 6 C8.697 6.526 9.395 7.052 10.113 7.594 C22.248 16.29 37.6 18.877 52.227 16.469 C56.984 15.361 61.285 14.002 65.625 11.75 C66.739 11.173 67.853 10.595 69 10 C69.99 10.495 69.99 10.495 71 11 C62.871 16.745 54.991 19.555 45 19 C44.505 20.485 44.505 20.485 44 22 C43.211 21.53 43.211 21.53 42.406 21.051 C39.416 19.745 36.745 19.52 33.5 19.188 C23.683 17.89 15.394 14.109 7 9 C4.882 7.882 4.882 7.882 2 7 C1.375 5.125 1.375 5.125 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#7084D3" transform="translate(320,209)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 17.82 1 35.64 1 54 C0.34 53.67 -0.32 53.34 -1 53 C-1 49.04 -1 45.08 -1 41 C-1.66 40.67 -2.32 40.34 -3 40 C-3.33 40.99 -3.66 41.98 -4 43 C-4.197 40 -4.382 37.001 -4.562 34 C-4.619 33.161 -4.675 32.322 -4.732 31.457 C-5.042 26.141 -4.822 21.264 -4 16 C-3.34 16 -2.68 16 -2 16 C-1.062 10.95 -1.062 10.95 -2 6 C-2.66 5.67 -3.32 5.34 -4 5 C-3.67 4.34 -3.34 3.68 -3 3 C-2.01 3 -1.02 3 0 3 C0 2.01 0 1.02 0 0 Z M-3 17 C-2 19 -2 19 -2 19 Z M-2 19 C-3.973 20.973 -3.185 24.685 -3.188 27.375 C-3.2 28.061 -3.212 28.747 -3.225 29.453 C-3.236 33.193 -3.127 34.81 -1 38 C0.594 34.812 0.102 31.371 0.062 27.875 C0.058 27.121 0.053 26.367 0.049 25.59 C0.037 23.727 0.019 21.863 0 20 C-0.66 19.67 -1.32 19.34 -2 19 Z " fill="#AFB7F0" transform="translate(513,884)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.467 7.233 4.467 7.233 2.25 10.875 C-1.74 14.643 -6.023 14.393 -11.262 14.352 C-15.598 13.795 -17.168 12.279 -20 9 C-20.875 6.688 -20.875 6.688 -21 5 C-23.31 5.33 -25.62 5.66 -28 6 C-27.01 8.97 -26.02 11.94 -25 15 C-28.06 12.96 -28.772 12.307 -30 9 C-30.032 7.334 -30.036 5.666 -30 4 C-28.398 3.778 -26.793 3.573 -25.188 3.375 C-24.294 3.259 -23.401 3.143 -22.48 3.023 C-20 3 -20 3 -17 5 C-16.34 5 -15.68 5 -15 5 C-14.34 5.33 -13.68 5.66 -13 6 C-13.33 6.99 -13.66 7.98 -14 9 C-12.188 11.243 -12.188 11.243 -9.617 11.195 C-8.26 11.161 -8.26 11.161 -6.875 11.125 C-5.965 11.107 -5.055 11.089 -4.117 11.07 C-3.419 11.047 -2.72 11.024 -2 11 C-2 10.34 -2 9.68 -2 9 C-1.01 9 -0.02 9 1 9 C1.269 4.349 1.269 4.349 0 0 Z " fill="#808FDD" transform="translate(301,917)"/>
<path d="M0 0 C3.87 3.182 6.649 6.275 7.301 11.332 C7.334 18.314 6.655 23.124 1.75 28.438 C-1.846 30.48 -3.916 30.573 -8 30 C-11.541 28.23 -13.541 26.627 -15.875 23.438 C-17.856 17.385 -17.895 11.505 -15.75 5.5 C-11.537 -0.519 -7.078 -1.709 0 0 Z M-14 5 C-16.424 10.695 -16.053 15.873 -14.898 21.836 C-13.747 24.61 -12.597 25.519 -10 27 C-5.625 28.171 -1.508 28.867 2.5 26.562 C6.131 20.359 6.498 15.143 4.816 8.277 C3.55 4.746 2.137 3.046 -1 1 C-6.355 -0.203 -10.413 0.675 -14 5 Z " fill="#7E90DC" transform="translate(335,493)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.025 1.166 1.05 2.331 1.076 3.532 C1.17 7.854 1.27 12.176 1.372 16.498 C1.416 18.369 1.457 20.24 1.497 22.111 C1.555 24.799 1.619 27.488 1.684 30.176 C1.7 31.013 1.717 31.851 1.734 32.714 C1.819 36.037 1.943 38.828 3 42 C2.34 42 1.68 42 1 42 C0.67 44.64 0.34 47.28 0 50 C-0.66 50 -1.32 50 -2 50 C-2.227 50.577 -2.454 51.155 -2.688 51.75 C-4.861 55.476 -7.774 57.416 -11.625 59.25 C-19.521 61.005 -26.764 60.681 -34 57 C-36.5 54.312 -36.5 54.312 -38 52 C-37.67 51.34 -37.34 50.68 -37 50 C-36.752 50.619 -36.505 51.237 -36.25 51.875 C-34.264 55.25 -31.606 55.798 -28 57 C-25.566 57.147 -23.126 57.221 -20.688 57.25 C-18.744 57.289 -18.744 57.289 -16.762 57.328 C-11.668 56.884 -8.402 55.363 -4.75 51.812 C-0.818 45.494 -0.684 38.485 -0.586 31.25 C-0.567 30.351 -0.547 29.453 -0.527 28.527 C-0.468 25.685 -0.421 22.842 -0.375 20 C-0.337 18.061 -0.298 16.122 -0.258 14.184 C-0.162 9.456 -0.078 4.728 0 0 Z " fill="#7185D8" transform="translate(548,487)"/>
<path d="M0 0 C1.625 1.188 1.625 1.188 3 3 C4.011 3.825 5.021 4.65 6.062 5.5 C9 8 9 8 11 11 C11.99 10.34 12.98 9.68 14 9 C14 10.32 14 11.64 14 13 C13.34 13.33 12.68 13.66 12 14 C12 14.99 12 15.98 12 17 C12.66 17.33 13.32 17.66 14 18 C13.86 27.566 13.264 34.744 8 43 C7.34 42.67 6.68 42.34 6 42 C6.639 40.948 7.279 39.896 7.938 38.812 C11.777 32.016 11.886 24.37 10.25 16.812 C8.809 11.79 6.655 8.154 2.312 5.125 C-3.188 3.257 -7.865 3.194 -13.312 5.25 C-15.918 6.947 -17.929 8.69 -20 11 C-20 8.69 -20 6.38 -20 4 C-19.01 4.495 -19.01 4.495 -18 5 C-18 4.34 -18 3.68 -18 3 C-17.278 2.876 -16.556 2.752 -15.812 2.625 C-13.022 2.108 -13.022 2.108 -10.688 0.938 C-6.878 -0.392 -4.038 -0.326 0 0 Z " fill="#7F91DE" transform="translate(338,483)"/>
<path d="M0 0 C3.346 2.527 4.68 4.039 6 8 C6.146 10.122 6.221 12.249 6.25 14.375 C6.276 15.496 6.302 16.618 6.328 17.773 C5.964 21.358 5.178 23.157 3 26 C-0.765 28.172 -3.271 29 -7.625 29 C-11.653 27.806 -13.343 26.204 -16 23 C-17.401 20.199 -17.208 17.811 -17.25 14.688 C-17.289 12.992 -17.289 12.992 -17.328 11.262 C-16.875 6.756 -15.375 4.304 -12.121 1.219 C-8.281 -0.988 -4.278 -0.755 0 0 Z M-15 6 C-16.659 11.129 -16.557 16.671 -15.312 21.875 C-13.552 24.726 -12.088 25.696 -9 27 C-5.211 27.406 -2.729 27.292 0.812 25.875 C4.1 23.057 4.746 20.274 5.219 16.043 C5.436 11.3 4.646 8.063 2 4 C-0.236 1.764 -1.347 1.065 -4.5 0.75 C-9.444 1.244 -11.721 2.043 -15 6 Z " fill="#7A8DDA" transform="translate(534,493)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 0.33 7 0.66 7 1 C8.016 1.098 9.032 1.196 10.078 1.297 C32.101 3.611 32.101 3.611 36.824 9.133 C37.212 9.749 37.6 10.365 38 11 C35.124 10.071 32.303 9.131 29.527 7.93 C17.579 3.064 1.993 3.208 -10.336 6.867 C-15.275 8.967 -19.654 11.877 -24 15 C-24 14.01 -24 13.02 -24 12 C-17.097 5.882 -8.227 2.63 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#8A9CDF" transform="translate(321,266)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 6.6 7 13.2 7 20 C6.67 20 6.34 20 6 20 C6 14.06 6 8.12 6 2 C4.68 2 3.36 2 2 2 C2 18.17 2 34.34 2 51 C3.32 51.33 4.64 51.66 6 52 C4.02 52 2.04 52 0 52 C0 34.84 0 17.68 0 0 Z " fill="#ECEEFE" transform="translate(358,885)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-2.114 1.288 -2.114 1.288 -3.25 0.562 C-8.834 -2.61 -14.7 -2.539 -21 -2 C-24.42 -0.71 -24.42 -0.71 -27 1 C-27.99 1.33 -28.98 1.66 -30 2 C-31.98 6.378 -32.433 10.232 -32 15 C-30.592 17.764 -30.592 17.764 -29 20 C-29 20.66 -29 21.32 -29 22 C-28.394 22.182 -27.788 22.364 -27.164 22.551 C-25.406 23.078 -23.648 23.605 -21.891 24.133 C-20.46 24.562 -20.46 24.562 -19 25 C-16.279 25.816 -13.557 26.633 -10.836 27.449 C-10.23 27.631 -9.624 27.813 -9 28 C-12.678 29.08 -15.228 28.642 -19 28 C-20.485 28.495 -20.485 28.495 -22 29 C-22 28.34 -22 27.68 -22 27 C-25.886 24.566 -25.886 24.566 -30.25 24.438 C-30.827 24.623 -31.405 24.809 -32 25 C-32.66 24.34 -33.32 23.68 -34 23 C-33.706 21.657 -33.372 20.323 -33 19 C-33.33 18.34 -33.66 17.68 -34 17 C-34.653 10.021 -34.067 4.904 -29.625 -0.75 C-21.017 -7.371 -8.556 -5.704 0 0 Z M-32 20 C-31.67 20.99 -31.34 21.98 -31 23 C-31 22.01 -31 21.02 -31 20 C-31.33 20 -31.66 20 -32 20 Z " fill="#7D8CDB" transform="translate(308,887)"/>
<path d="M0 0 C3.947 1.258 7.67 2.722 11.438 4.438 C29.45 11.945 50.683 12.588 68.928 5.222 C71.628 3.991 74.226 2.623 76.812 1.172 C79 0 79 0 81 0 C81 0.66 81 1.32 81 2 C65.381 9.318 48.515 15.53 31 12 C30.67 12.99 30.34 13.98 30 15 C29.67 14.01 29.34 13.02 29 12 C26.541 11.259 24.195 10.692 21.688 10.188 C14.243 8.522 7.5 6.114 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#7386D6" transform="translate(471,790)"/>
<path d="M0 0 C1.773 1.527 1.773 1.527 3 3 C2.01 4.32 1.02 5.64 0 7 C0.33 5.35 0.66 3.7 1 2 C0.01 2.495 0.01 2.495 -1 3 C-2.48 2.944 -3.96 2.857 -5.438 2.75 C-7.824 2.656 -7.824 2.656 -10 3 C-12.526 5.832 -13 7.216 -13 11 C-13.33 11 -13.66 11 -14 11 C-14.33 13.64 -14.66 16.28 -15 19 C-14.01 19 -13.02 19 -12 19 C-12.33 20.65 -12.66 22.3 -13 24 C-8.489 27.383 -5.444 27.346 0 27 C2.355 26.583 2.355 26.583 4 26 C2.272 28.404 0.887 29.81 -2.059 30.441 C-6.139 30.655 -9.127 30.389 -12.438 27.875 C-16.525 23.289 -17.438 19.149 -17.25 13.02 C-16.678 8.4 -14.897 5.029 -11.875 1.562 C-7.998 -0.544 -4.334 -0.901 0 0 Z M-7 1 C-7 1.33 -7 1.66 -7 2 C-5.02 2 -3.04 2 -1 2 C-1 1.67 -1 1.34 -1 1 C-2.98 1 -4.96 1 -7 1 Z " fill="#8D9DE2" transform="translate(696,493)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-3.315 2.729 -5.651 3.392 -8 4 C-8.99 4.33 -9.98 4.66 -11 5 C-10.336 4.965 -9.672 4.93 -8.988 4.895 C0.248 4.611 0.248 4.611 4 8 C4.621 10.109 4.621 10.109 5 12 C4.01 11.154 3.02 10.309 2 9.438 C-0.825 7.431 -2.645 6.604 -6.125 6.875 C-10.247 7.707 -12.402 9.693 -15 13 C-16.893 18.678 -16.789 24.83 -14.75 30.438 C-13.035 32.949 -11.262 34.972 -9 37 C-11.812 36.875 -11.812 36.875 -15 36 C-17.912 31.524 -18.515 27.478 -18.438 22.25 C-18.431 21.549 -18.425 20.848 -18.419 20.126 C-18.245 14.307 -17.262 8.943 -14 4 C-9.511 0.094 -5.838 -0.11 0 0 Z " fill="#EEF1FE" transform="translate(335,486)"/>
<path d="M0 0 C2.502 1.876 3.619 3.239 5 6 C5.123 8.289 5.176 10.583 5.188 12.875 C5.202 14.11 5.216 15.345 5.23 16.617 C5.015 19.785 4.795 21.436 3 24 C-0.11 26.073 -0.891 26.274 -4.438 26.25 C-5.199 26.255 -5.961 26.26 -6.746 26.266 C-9.265 25.969 -10.862 25.355 -13 24 C-15.679 18.776 -15.715 12.737 -15 7 C-12.066 -0.115 -7.082 -0.945 0 0 Z M-11 2.938 C-13.523 6.801 -13.164 8.802 -12.395 13.207 C-12.264 13.799 -12.134 14.39 -12 15 C-12.99 15.495 -12.99 15.495 -14 16 C-12.716 19.497 -12.716 19.497 -11 23 C-10.01 23.33 -9.02 23.66 -8 24 C-7.34 23.67 -6.68 23.34 -6 23 C-6 23.66 -6 24.32 -6 25 C-2.694 24.449 0.2 23.9 3 22 C4.145 18.757 4.103 15.662 4.062 12.25 C4.053 11.265 4.044 10.28 4.035 9.266 C4.024 8.518 4.012 7.77 4 7 C3.01 7 2.02 7 1 7 C1.33 6.01 1.66 5.02 2 4 C0.944 2.354 0.944 2.354 -1 1 C-5.318 0.36 -7.308 0.553 -11 2.938 Z " fill="#8090DD" transform="translate(380,905)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 17.49 1 34.98 1 53 C2.98 53 4.96 53 7 53 C7 51.02 7 49.04 7 47 C7.66 47 8.32 47 9 47 C9.598 47.825 10.196 48.65 10.812 49.5 C12.943 51.806 14.014 52.001 17.219 52.332 C20.492 52.314 23.735 52.215 27 52 C25.35 53.65 23.7 55.3 22 57 C21.67 56.34 21.34 55.68 21 55 C19.35 55 17.7 55 16 55 C16 55.33 16 55.66 16 56 C10.25 56.125 10.25 56.125 8 55 C5.36 55 2.72 55 0 55 C0 36.85 0 18.7 0 0 Z M9 52 C9.33 52.66 9.66 53.32 10 54 C10.33 53.34 10.66 52.68 11 52 C10.34 52 9.68 52 9 52 Z M12 53 C13 55 13 55 13 55 Z " fill="#7D8EDD" transform="translate(514,884)"/>
<path d="M0 0 C2 3 2 3 1.812 5.438 C0.941 8.186 -0.088 9.858 -2 12 C-2.99 12.495 -2.99 12.495 -4 13 C-4 12.34 -4 11.68 -4 11 C-5.176 10.907 -5.176 10.907 -6.375 10.812 C-9 10 -9 10 -10.312 7.625 C-11 5 -11 5 -11 2 C-7.202 -1.128 -4.668 -1.452 0 0 Z " fill="#C4CEF3" transform="translate(458,469)"/>
<path d="M0 0 C1.612 0.006 1.612 0.006 3.256 0.012 C4.933 0.048 4.933 0.048 6.645 0.086 C8.293 0.088 8.293 0.088 9.975 0.09 C16.427 0.159 22.218 0.814 28.457 2.461 C28.787 1.801 29.117 1.141 29.457 0.461 C29.952 1.451 29.952 1.451 30.457 2.461 C31.954 3.103 33.481 3.675 35.02 4.211 C42.988 7.28 48.633 12.345 54.457 18.461 C54.127 19.121 53.797 19.781 53.457 20.461 C52.873 19.807 52.873 19.807 52.277 19.141 C43.835 10.153 32.518 3.997 20.029 3.332 C19.197 3.33 18.365 3.329 17.508 3.328 C16.576 3.327 15.645 3.326 14.686 3.324 C13.723 3.328 12.761 3.332 11.77 3.336 C10.827 3.332 9.884 3.328 8.912 3.324 C2.703 3.333 -3.378 3.723 -9.543 4.461 C-6.481 0.811 -4.746 -0.04 0 0 Z " fill="#8FA0E3" transform="translate(554.54296875,267.5390625)"/>
<path d="M0 0 C3.664 1.575 6.299 3.842 9.25 6.5 C10.142 7.294 11.034 8.088 11.953 8.906 C14 11 14 11 14 13 C14.66 13 15.32 13 16 13 C17.543 15.012 17.543 15.012 19.188 17.688 C20.013 18.993 20.013 18.993 20.855 20.324 C22.178 23.415 21.985 24.841 21 28 C20.67 27.34 20.34 26.68 20 26 C19.34 25.67 18.68 25.34 18 25 C17.278 23.356 16.606 21.689 16 20 C14.02 20.99 14.02 20.99 12 22 C10 20 10 20 9.875 16.875 C9.916 15.926 9.957 14.977 10 14 C10.66 14 11.32 14 12 14 C9.92 11.576 9.92 11.576 6.938 10.688 C6.298 10.461 5.659 10.234 5 10 C3.938 7.188 3.938 7.188 3 4 C1.484 1.698 1.484 1.698 0 0 Z " fill="#C9D1F0" transform="translate(526,689)"/>
<path d="M0 0 C-0.887 0.371 -1.774 0.743 -2.688 1.125 C-8.021 4.144 -11.129 8.213 -13 14 C-13.81 21.879 -14.028 29.515 -9 36 C-4.476 40.524 0.191 42.092 6.5 42.188 C10.075 42.109 13.491 41.72 17 41 C16.505 41.99 16.505 41.99 16 43 C13.199 43.887 13.199 43.887 9.688 44.688 C8.536 44.959 7.385 45.231 6.199 45.512 C3.227 45.965 1.758 46.005 -1 45 C-1.99 44.67 -2.98 44.34 -4 44 C-3.505 43.505 -3.505 43.505 -3 43 C-3.908 42.443 -4.815 41.886 -5.75 41.312 C-8.864 39.097 -10.854 37.136 -13 34 C-13 33.34 -13 32.68 -13 32 C-13.66 31.67 -14.32 31.34 -15 31 C-15.562 28.688 -15.562 28.688 -16 26 C-16.193 25.412 -16.387 24.824 -16.586 24.219 C-17.717 18.16 -15.474 11.169 -12.086 6.184 C-5.239 -1.572 -5.239 -1.572 0 0 Z " fill="#798CDA" transform="translate(370,487)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.99 2.31 7.98 4.62 9 7 C9.66 7 10.32 7 11 7 C11.337 9.083 11.67 11.166 12 13.25 C12.186 14.41 12.371 15.57 12.562 16.766 C13 20 13 20 13 24 C13.66 24 14.32 24 15 24 C16.044 27.133 15.934 27.99 15 31 C14.34 31 13.68 31 13 31 C11.829 26.709 10.664 22.417 9.5 18.125 C9.166 16.902 8.832 15.678 8.488 14.418 C8.015 12.668 8.015 12.668 7.531 10.883 C7.238 9.804 6.945 8.725 6.643 7.614 C6 5 6 5 6 3 C4.02 3 2.04 3 0 3 C-1.32 8.28 -2.64 13.56 -4 19 C-4.99 19 -5.98 19 -7 19 C-6.34 16.03 -5.68 13.06 -5 10 C-4.34 10 -3.68 10 -3 10 C-2.814 8.639 -2.814 8.639 -2.625 7.25 C-2.079 4.41 -1.427 2.474 0 0 Z M10 11 C11 14 11 14 11 14 Z " fill="#8C98E1" transform="translate(716,896)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.745 4.786 0.544 6.579 0.375 8.375 C0.28 9.331 0.184 10.288 0.086 11.273 C0.006 13.804 0.291 15.597 1 18 C1 19.667 1 21.333 1 23 C0.34 23 -0.32 23 -1 23 C-1 25.31 -1 27.62 -1 30 C-0.34 30.33 0.32 30.66 1 31 C0.34 31 -0.32 31 -1 31 C-1 32.65 -1 34.3 -1 36 C-4.375 36.625 -4.375 36.625 -8 37 C-12 33 -12 33 -12.23 29.797 C-12.228 28.448 -12.213 27.099 -12.188 25.75 C-12.181 25.044 -12.175 24.337 -12.168 23.609 C-12.07 16.718 -11.601 9.865 -11 3 C-10.67 3 -10.34 3 -10 3 C-10 12.9 -10 22.8 -10 33 C-7.36 33 -4.72 33 -2 33 C-2.01 32.142 -2.021 31.285 -2.032 30.401 C-2.066 27.211 -2.091 24.02 -2.11 20.83 C-2.12 19.45 -2.134 18.071 -2.151 16.692 C-2.175 14.706 -2.185 12.72 -2.195 10.734 C-2.206 9.54 -2.216 8.346 -2.227 7.116 C-2.01 4.142 -1.562 2.497 0 0 Z " fill="#8999E1" transform="translate(476,496)"/>
<path d="M0 0 C1.158 0.88 2.301 1.78 3.438 2.688 C4.395 3.436 4.395 3.436 5.371 4.199 C7.41 6.453 7.546 8.025 8 11 C8.619 12.697 9.267 14.385 9.938 16.062 C11.815 21.09 12.841 25.971 13.605 31.273 C13.951 33.949 13.951 33.949 14.555 36.332 C15.037 39.223 14.885 41.512 14.512 44.414 C14.317 45.926 14.317 45.926 14.119 47.469 C13.977 48.51 13.834 49.552 13.688 50.625 C13.557 51.674 13.426 52.724 13.291 53.805 C12.254 61.619 12.254 61.619 10 65 C8.654 62.308 9.282 61 9.914 58.078 C13.429 39.881 11.88 21.23 2.27 5.172 C1 3 1 3 0 0 Z " fill="#7186D2" transform="translate(609,288)"/>
<path d="M0 0 C0.268 0.804 0.536 1.609 0.812 2.438 C1.204 3.283 1.596 4.129 2 5 C2.99 5.33 3.98 5.66 5 6 C5 6.66 5 7.32 5 8 C7.97 8 10.94 8 14 8 C14 8.99 14 9.98 14 11 C2.178 12.76 2.178 12.76 -1.625 10.25 C-4 8 -4 8 -6 5 C-6 3.68 -6 2.36 -6 1 C-4 0 -4 0 0 0 Z " fill="#D3D9FD" transform="translate(405,925)"/>
<path d="M0 0 C0.495 0.99 0.495 0.99 1 2 C4.029 2.658 4.029 2.658 7 3 C6.01 3.66 5.02 4.32 4 5 C1.9 4.526 -0.199 4.045 -2.289 3.527 C-16.484 0.766 -30.863 4.534 -43 12 C-45.859 14.162 -48.458 16.476 -51 19 C-51.825 19.804 -52.65 20.609 -53.5 21.438 C-54.243 22.211 -54.243 22.211 -55 23 C-53.608 18.558 -51.557 16.335 -48.062 13.312 C-47.187 12.546 -46.312 11.779 -45.41 10.988 C-40.576 7 -40.576 7 -37 7 C-37 6.34 -37 5.68 -37 5 C-25.562 0.694 -14.138 -0.112 -2 1 C-1.34 1.33 -0.68 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6C82D1" transform="translate(373,106)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.33 1.99 2.66 2.98 3 4 C3.66 3.67 4.32 3.34 5 3 C5 3.99 5 4.98 5 6 C2 8 2 8 0 9 C0 8.34 0 7.68 0 7 C-0.99 7.495 -0.99 7.495 -2 8 C-4 8.04 -6 8.043 -8 8 C-8 7.34 -8 6.68 -8 6 C-11.62 4.684 -14.096 4 -18 4 C-18 5.32 -18 6.64 -18 8 C-13.624 8.593 -9.425 9.138 -5 9 C-5 9.66 -5 10.32 -5 11 C-4.34 11.66 -3.68 12.32 -3 13 C-9.135 12.588 -14.461 11.769 -20 9 C-21 6.312 -21 6.312 -21 4 C-18.474 1.474 -16.485 0.725 -13 0.562 C-9.893 0.666 -9.262 0.761 -6.875 2.938 C-6.256 3.618 -5.637 4.299 -5 5 C-1.855 5.803 -1.855 5.803 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#939EE5" transform="translate(427,903)"/>
<path d="M0 0 C3 1 3 1 4.188 2.562 C6.142 8.427 5.176 14.342 3 20 C1.5 22.438 1.5 22.438 -1 24 C-4.475 24.676 -7.534 24.752 -11 24 C-13.915 21.955 -15.435 19.348 -16.812 16.062 C-17.356 10.08 -17.086 6.271 -14 1 C-13.01 2.32 -12.02 3.64 -11 5 C-12.32 5 -13.64 5 -15 5 C-14.737 14.299 -14.737 14.299 -10 22 C-6.097 22.26 -3.303 22.202 0 20 C1.268 17.851 1.268 17.851 0.938 14.5 C1 11 1 11 3 9 C2.34 9 1.68 9 1 9 C1.021 8.072 1.041 7.144 1.062 6.188 C1.158 2.977 1.158 2.977 0 0 Z M2 5 C3 8 3 8 3 8 Z " fill="#8B99E0" transform="translate(679,907)"/>
<path d="M0 0 C11.799 -0.687 11.799 -0.687 15.688 1.625 C18.424 4.435 18.824 6.133 19 10 C16.25 10.25 16.25 10.25 13 10 C12.381 9.34 11.762 8.68 11.125 8 C8.708 5.725 8.122 5.683 4.938 5.625 C0.95 5.691 0.95 5.691 -2 8 C-2.73 10.563 -2.73 10.563 -3 13 C-3.66 12.34 -4.32 11.68 -5 11 C-4.646 8.737 -4.025 7.049 -3 5 C0.015 3.995 2.042 3.897 5.188 3.938 C6.089 3.947 6.99 3.956 7.918 3.965 C8.605 3.976 9.292 3.988 10 4 C10 3.34 10 2.68 10 2 C6.7 1.67 3.4 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E6E9FE" transform="translate(409,899)"/>
<path d="M0 0 C3.028 1.179 4.284 2.935 5.836 5.715 C5.836 7.035 5.836 8.355 5.836 9.715 C1.969 10.981 -1.896 10.827 -5.914 10.777 C-6.611 10.773 -7.309 10.768 -8.027 10.764 C-9.74 10.752 -11.452 10.734 -13.164 10.715 C-12.665 6.576 -12.423 5.012 -9.602 1.777 C-6.384 -0.618 -3.876 -0.78 0 0 Z M-7.164 1.715 C-8.154 3.2 -8.154 3.2 -9.164 4.715 C-8.174 5.045 -7.184 5.375 -6.164 5.715 C-6.824 6.375 -7.484 7.035 -8.164 7.715 C-3.185 8.469 -3.185 8.469 1.836 8.715 C2.166 7.065 2.496 5.415 2.836 3.715 C-0.844 1.262 -2.818 1.32 -7.164 1.715 Z " fill="#B1B9F0" transform="translate(574.1640625,904.28515625)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.869 4.488 2.135 8.744 2.125 13.312 C2.129 15.163 2.129 15.163 2.133 17.051 C2 20 2 20 1 21 C-0.708 21.087 -2.419 21.107 -4.129 21.098 C-5.681 21.093 -5.681 21.093 -7.264 21.088 C-8.352 21.08 -9.441 21.071 -10.562 21.062 C-12.201 21.056 -12.201 21.056 -13.873 21.049 C-16.582 21.037 -19.291 21.021 -22 21 C-22 20.34 -22 19.68 -22 19 C-22.66 18.67 -23.32 18.34 -24 18 C-23.34 14.04 -22.68 10.08 -22 6 C-21.67 6 -21.34 6 -21 6 C-21 10.29 -21 14.58 -21 19 C-14.07 19 -7.14 19 0 19 C0 12.73 0 6.46 0 0 Z " fill="#788BD5" transform="translate(456,205)"/>
<path d="M0 0 C3.369 0.032 4.383 0.092 7.188 2.125 C9.884 6.057 9.805 9.301 9.785 13.949 C9.782 15.248 9.779 16.547 9.775 17.885 C9.767 19.257 9.759 20.628 9.75 22 C9.745 23.383 9.74 24.767 9.736 26.15 C9.725 29.538 9.708 32.925 9.688 36.312 C12.327 36.312 14.967 36.312 17.688 36.312 C17.688 36.643 17.688 36.972 17.688 37.312 C15.377 37.312 13.067 37.312 10.688 37.312 C10.027 38.633 9.368 39.952 8.688 41.312 C8.358 41.312 8.027 41.312 7.688 41.312 C7.699 40.553 7.711 39.794 7.723 39.012 C7.732 38.018 7.741 37.024 7.75 36 C7.762 35.014 7.773 34.028 7.785 33.012 C7.811 30.213 7.811 30.213 6.688 27.312 C7.348 27.312 8.007 27.312 8.688 27.312 C8.087 22.032 8.087 22.032 7.125 16.812 C6.508 13.284 6.627 9.893 6.688 6.312 C5.697 5.983 4.707 5.652 3.688 5.312 C3.358 4.322 3.027 3.332 2.688 2.312 C0.377 2.312 -1.933 2.312 -4.312 2.312 C-4.642 2.973 -4.973 3.632 -5.312 4.312 C-5.312 -0.223 -4.13 0.128 0 0 Z " fill="#768ADB" transform="translate(484.3125,492.6875)"/>
<path d="M0 0 C4.167 4.167 6.516 8.031 9.312 13.125 C9.78 13.949 10.247 14.772 10.729 15.621 C11.383 16.803 11.383 16.803 12.051 18.008 C12.45 18.726 12.848 19.444 13.259 20.184 C13.504 20.783 13.748 21.383 14 22 C13.67 22.66 13.34 23.32 13 24 C10 20.25 10 20.25 10 18 C9.34 17.67 8.68 17.34 8 17 C7.066 15.344 6.199 13.65 5.375 11.938 C4.929 11.018 4.483 10.099 4.023 9.152 C3.686 8.442 3.348 7.732 3 7 C2.134 11.007 1.996 14.555 2.035 18.66 C2.092 26.519 1.992 34.188 1 42 C0.67 42 0.34 42 0 42 C0 28.14 0 14.28 0 0 Z " fill="#5D72C5" transform="translate(662,310)"/>
<path d="M0 0 C3.058 2.489 5.816 4.793 8.438 7.75 C13.157 11.894 18.989 11.123 25 11 C25 11.33 25 11.66 25 12 C27.475 12.495 27.475 12.495 30 13 C30 12.01 30 11.02 30 10 C31.485 10.495 31.485 10.495 33 11 C35.502 9.699 35.502 9.699 38 8 C38.743 7.649 39.485 7.299 40.25 6.938 C42.181 6.12 42.181 6.12 43 4 C42.61 6.32 42.296 7.689 40.652 9.414 C34.211 13.963 27.924 15.683 20 15 C11.754 13.389 4.885 10.148 0 3.125 C-0.495 2.073 -0.495 2.073 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#7F92D9" transform="translate(338,192)"/>
<path d="M0 0 C4.78 3.559 4.972 4.832 6 11 C-1.26 11 -8.52 11 -16 11 C-14.849 2.943 -14.849 2.943 -11 0 C-7.139 -1.287 -3.866 -1.093 0 0 Z M-12.625 2.75 C-13.079 3.493 -13.533 4.235 -14 5 C-13.67 6.32 -13.34 7.64 -13 9 C-7.06 9 -1.12 9 5 9 C3.151 3.714 3.151 3.714 -1 1 C-5.456 0.327 -8.813 0.209 -12.625 2.75 Z " fill="#99A7E6" transform="translate(381,493)"/>
<path d="M0 0 C-2.784 1.856 -4.529 2.532 -7.688 3.375 C-20.128 7.048 -30.255 14.745 -37 26 C-38.397 28.829 -39.692 31.676 -40.922 34.582 C-42 37 -42 37 -44 40 C-43.308 29.395 -37.209 20.503 -30 13 C-21.577 5.686 -11.449 -0.318 0 0 Z " fill="#6D82CE" transform="translate(432,269)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.295 3.697 -0.414 5.395 -1.211 7.051 C-3.113 11.75 -3.109 16.29 -3.062 21.312 C-3.053 22.567 -3.044 23.821 -3.035 25.113 C-3.024 26.066 -3.012 27.019 -3 28 C-2.34 28 -1.68 28 -1 28 C1.143 34.429 1.143 34.429 0.375 37.5 C-0.136 40.909 1.081 42.562 2.797 45.449 C4.096 47.293 4.096 47.293 7 49 C7 50.667 7 52.333 7 54 C-2.98 43.787 -5.245 31.999 -5.328 18.252 C-5.251 11.96 -4.726 4.726 0 0 Z M-2 34 C-1 36 -1 36 -1 36 Z " fill="#7E8FD8" transform="translate(486,715)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C5.98 0.67 7.96 0.34 10 0 C10 4.29 10 8.58 10 13 C12.64 13 15.28 13 18 13 C18 13.33 18 13.66 18 14 C15.03 14 12.06 14 9 14 C9 10.04 9 6.08 9 2 C7.35 2.66 5.7 3.32 4 4 C3.01 4 2.02 4 1 4 C1 7.63 1 11.26 1 15 C-1.31 15 -3.62 15 -6 15 C-6 17.97 -6 20.94 -6 24 C-6.33 23.34 -6.66 22.68 -7 22 C-7.66 22 -8.32 22 -9 22 C-9.213 17.532 -8.591 14.177 -7 10 C-6.67 10.99 -6.34 11.98 -6 13 C-4.02 13 -2.04 13 0 13 C-0.66 12.67 -1.32 12.34 -2 12 C-2 11.01 -2 10.02 -2 9 C-1.34 9 -0.68 9 0 9 C0 6.03 0 3.06 0 0 Z M-8 17 C-7 20 -7 20 -7 20 Z " fill="#8A9BE2" transform="translate(406,472)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.32 2 3.64 2 5 2 C4.67 2.66 4.34 3.32 4 4 C1.03 3.505 1.03 3.505 -2 3 C-2 3.66 -2 4.32 -2 5 C-7.28 5 -12.56 5 -18 5 C-18 14.9 -18 24.8 -18 35 C-18.66 35 -19.32 35 -20 35 C-20 24.77 -20 14.54 -20 4 C-18 3 -18 3 -15.062 3.062 C-11.824 3.271 -11.824 3.271 -9 1 C-8.01 1.99 -7.02 2.98 -6 4 C-5.67 3.01 -5.34 2.02 -5 1 C-5 1.99 -5 2.98 -5 4 C-4.546 3.526 -4.092 3.051 -3.625 2.562 C-2 1 -2 1 0 0 Z " fill="#8394DA" transform="translate(730,266)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 2.64 1.66 5.28 2 8 C2.99 8.33 3.98 8.66 5 9 C5.725 16.844 6.211 24.197 5 32 C2.472 28.208 2.776 25.191 2.875 20.812 C2.884 20.063 2.893 19.313 2.902 18.541 C2.926 16.694 2.962 14.847 3 13 C2.407 13.338 1.814 13.675 1.203 14.023 C-1.804 15.356 -3.858 14.714 -7 14 C-5.68 13.67 -4.36 13.34 -3 13 C-5.485 8.215 -7.48 5.139 -12 2 C-10.68 2 -9.36 2 -8 2 C-8 2.66 -8 3.32 -8 4 C-7.361 4.247 -6.721 4.495 -6.062 4.75 C-4 6 -4 6 -3.25 8.125 C-3.126 9.053 -3.126 9.053 -3 10 C-2.34 10 -1.68 10 -1 10 C-1.206 9.092 -1.413 8.185 -1.625 7.25 C-2.013 3.89 -1.804 2.759 0 0 Z M-1 5 C0 8 0 8 0 8 Z M-2 11 C-1.34 11.66 -0.68 12.32 0 13 C0 12.34 0 11.68 0 11 C-0.66 11 -1.32 11 -2 11 Z " fill="#A6AFEE" transform="translate(471,897)"/>
<path d="M0 0 C1.583 -0.027 3.167 -0.046 4.75 -0.062 C6.073 -0.08 6.073 -0.08 7.422 -0.098 C9.782 -0.008 11.745 0.331 14 1 C14 1.99 14 2.98 14 4 C16.967 5.648 19.656 6.443 23 7 C23 7.99 23 8.98 23 10 C22.67 10.33 22.34 10.66 22 11 C22.911 15.277 23.78 19.194 26 23 C25.01 23.33 24.02 23.66 23 24 C23.33 26.64 23.66 29.28 24 32 C22.02 32.495 22.02 32.495 20 33 C20.165 32.031 20.33 31.061 20.5 30.062 C21.514 22.665 21.836 15.675 18 9 C14.028 5.221 11.246 3.919 5.875 3.438 C4.965 3.354 4.055 3.27 3.117 3.184 C2.419 3.123 1.72 3.062 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#8A97E2" transform="translate(669,896)"/>
<path d="M0 0 C0 11.55 0 23.1 0 35 C7.26 35.33 14.52 35.66 22 36 C22 36.33 22 36.66 22 37 C16.39 37 10.78 37 5 37 C4.67 37.66 4.34 38.32 4 39 C4 38.34 4 37.68 4 37 C2.68 37 1.36 37 0 37 C-0.33 37.66 -0.66 38.32 -1 39 C-3 36 -3 36 -3 32 C-2.34 32 -1.68 32 -1 32 C-1 30.35 -1 28.7 -1 27 C-1.66 27 -2.32 27 -3 27 C-2.928 26.432 -2.856 25.863 -2.781 25.277 C-1.599 16.556 -1.599 16.556 -3 8 C-2.062 5.312 -2.062 5.312 -1 3 C-1.66 3 -2.32 3 -3 3 C-3 2.34 -3 1.68 -3 1 C-1 0 -1 0 0 0 Z " fill="#8899DC" transform="translate(619,189)"/>
<path d="M0 0 C0.724 0.133 1.449 0.266 2.195 0.402 C4.188 0.766 6.182 1.128 8.176 1.488 C10.223 1.859 12.268 2.239 14.312 2.625 C17.084 3.084 17.084 3.084 20.25 2.938 C23 3 23 3 25 5 C27.106 5.319 27.106 5.319 29.438 5.25 C30.22 5.255 31.002 5.26 31.809 5.266 C34.29 5.132 34.29 5.132 37 3 C37.495 3.99 37.495 3.99 38 5 C38.66 5 39.32 5 40 5 C40 5.66 40 6.32 40 7 C40.99 6.67 41.98 6.34 43 6 C43 6.99 43 7.98 43 9 C11.186 6.593 11.186 6.593 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8F9DE3" transform="translate(512,781)"/>
<path d="M0 0 C1.118 3.539 1.047 6.717 0.914 10.41 C0.87 11.647 0.826 12.884 0.781 14.158 C0.73 15.447 0.678 16.735 0.625 18.062 C0.581 19.355 0.537 20.647 0.492 21.979 C0.13 31.609 0.13 31.609 -1 35 C-1.66 34.67 -2.32 34.34 -3 34 C-5.156 34 -6.924 34.447 -9 35 C-9.66 33.68 -10.32 32.36 -11 31 C-7.931 29.466 -5.299 30.45 -2 31 C-2.01 30.203 -2.021 29.406 -2.032 28.585 C-2.073 24.973 -2.099 21.362 -2.125 17.75 C-2.142 16.496 -2.159 15.241 -2.176 13.949 C-2.182 12.744 -2.189 11.539 -2.195 10.297 C-2.206 9.187 -2.216 8.076 -2.227 6.933 C-2.005 4.06 -1.493 2.428 0 0 Z M-7 32 C-6 34 -6 34 -6 34 Z " fill="#92A2E4" transform="translate(728,498)"/>
<path d="M0 0 C1.222 0.003 1.222 0.003 2.47 0.007 C3.851 0.007 3.851 0.007 5.26 0.007 C6.261 0.012 7.262 0.017 8.294 0.023 C9.313 0.024 10.333 0.025 11.383 0.027 C14.654 0.033 17.925 0.045 21.196 0.058 C23.407 0.063 25.619 0.067 27.831 0.071 C33.265 0.082 38.699 0.099 44.133 0.12 C44.133 0.45 44.133 0.78 44.133 1.12 C29.283 1.12 14.433 1.12 -0.867 1.12 C-0.867 8.05 -0.867 14.98 -0.867 22.12 C-1.527 22.12 -2.187 22.12 -2.867 22.12 C-2.867 18.16 -2.867 14.2 -2.867 10.12 C-3.527 9.79 -4.187 9.46 -4.867 9.12 C-4.207 9.12 -3.547 9.12 -2.867 9.12 C-2.887 7.821 -2.908 6.522 -2.929 5.183 C-2.951 3.829 -2.938 2.473 -2.867 1.12 C-1.867 0.12 -1.867 0.12 0 0 Z " fill="#5067BF" transform="translate(789.8665924072266,161.87974548339844)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.324 1.68 3.324 1.68 4.688 3.875 C5.145 4.594 5.603 5.314 6.074 6.055 C6.38 6.697 6.685 7.339 7 8 C6.67 8.66 6.34 9.32 6 10 C5.01 9.01 4.02 8.02 3 7 C1.861 11.545 1.851 15.911 1.875 20.562 C1.871 21.317 1.867 22.072 1.863 22.85 C1.836 26.975 1.836 26.975 2.567 31.011 C3.27 34.24 2.004 36.929 1 40 C-1 38 -1 38 -1.135 34.285 C-1.093 32.687 -1.039 31.089 -0.977 29.492 C-0.952 28.652 -0.927 27.811 -0.901 26.945 C-0.818 24.254 -0.722 21.565 -0.625 18.875 C-0.567 17.053 -0.509 15.232 -0.453 13.41 C-0.313 8.94 -0.16 4.47 0 0 Z " fill="#959FE7" transform="translate(616,899)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.41 5.508 0.433 10.649 -1 16 C-2.98 16 -4.96 16 -7 16 C-7.309 15.216 -7.619 14.433 -7.938 13.625 C-8.929 11.077 -8.929 11.077 -10 9 C-9.34 8.01 -8.68 7.02 -8 6 C-6.35 6.33 -4.7 6.66 -3 7 C-2.01 4.69 -1.02 2.38 0 0 Z " fill="#CFD7FC" transform="translate(712,919)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 9.24 7 18.48 7 28 C6.67 28 6.34 28 6 28 C6 19.42 6 10.84 6 2 C4.68 2 3.36 2 2 2 C1.902 12.384 1.844 22.662 3 33 C1 32 1 32 0.381 30.345 C-0.104 27.362 -0.111 24.507 -0.098 21.484 C-0.093 19.609 -0.093 19.609 -0.088 17.695 C-0.08 16.393 -0.071 15.091 -0.062 13.75 C-0.056 11.774 -0.056 11.774 -0.049 9.758 C-0.037 6.505 -0.021 3.253 0 0 Z " fill="#EFF2FE" transform="translate(319,899)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.846 2.665 2.691 2.33 3.562 1.984 C12.605 -1.263 12.605 -1.263 16.574 -0.516 C20.467 1.355 22.276 2.991 24 7 C23.515 6.711 23.031 6.423 22.531 6.125 C17.377 3.27 13.888 2.324 8 3 C5.233 4.21 5.233 4.21 3 6 C1.855 6.835 1.855 6.835 0.688 7.688 C0.131 8.121 -0.426 8.554 -1 9 C-1 7.02 -1 5.04 -1 3 C-3.64 3 -6.28 3 -9 3 C-9 6.63 -9 10.26 -9 14 C-9.33 14 -9.66 14 -10 14 C-10 9.71 -10 5.42 -10 1 C-4.375 -1.25 -4.375 -1.25 0 0 Z " fill="#92A1E5" transform="translate(475,484)"/>
<path d="M0 0 C3.719 1.174 7.289 2.541 10.875 4.062 C21.137 8.177 32.202 7.579 43.004 6.023 C43.663 6.016 44.321 6.008 45 6 C45.66 6.66 46.32 7.32 47 8 C41.311 9.213 35.723 9.148 29.938 9.125 C28.467 9.131 28.467 9.131 26.967 9.137 C26.017 9.135 25.068 9.134 24.09 9.133 C22.804 9.131 22.804 9.131 21.492 9.129 C19 9 19 9 16.369 8.429 C13.862 7.779 13.862 7.779 11 9 C8.312 8.062 8.312 8.062 6 7 C6 6.34 6 5.68 6 5 C4.35 4.67 2.7 4.34 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#798CD7" transform="translate(410,383)"/>
<path d="M0 0 C1.682 3.106 2.692 6.179 3.688 9.562 C6.657 18.593 11.159 26.454 19 32 C17.515 32.495 17.515 32.495 16 33 C13.812 31.562 13.812 31.562 12 30 C12.33 30.99 12.66 31.98 13 33 C12.01 32.67 11.02 32.34 10 32 C9.897 30.907 9.794 29.814 9.688 28.688 C9.027 25.144 8.797 24.068 6 22 C5.34 22.33 4.68 22.66 4 23 C2.438 20.625 2.438 20.625 1 18 C1.33 17.34 1.66 16.68 2 16 C2.66 16.33 3.32 16.66 4 17 C3.383 15.351 2.757 13.706 2.125 12.062 C1.777 11.146 1.429 10.229 1.07 9.285 C0.218 6.851 0.218 6.851 -2 6 C-1.34 4.02 -0.68 2.04 0 0 Z " fill="#7E91DA" transform="translate(191,183)"/>
<path d="M0 0 C11.706 1.558 11.706 1.558 16 6 C15.67 6.66 15.34 7.32 15 8 C14.258 7.34 13.515 6.68 12.75 6 C7.978 2.819 2.49 3.276 -3 4 C-7.637 5.987 -10.236 8.783 -12.32 13.297 C-13.846 19.364 -13.271 25.796 -13 32 C-15.894 29.106 -16.676 25.978 -17 22 C-15.823 14.569 -12.966 8.348 -7 3.625 C-4 2 -4 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8191DE" transform="translate(568,896)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.125 6.75 2.125 6.75 1 9 C-0.328 9.354 -1.662 9.685 -3 10 C-3.99 10.66 -4.98 11.32 -6 12 C-8.48 12.414 -8.48 12.414 -11.188 12.625 C-12.089 12.7 -12.99 12.775 -13.918 12.852 C-14.605 12.901 -15.292 12.95 -16 13 C-16 12.34 -16 11.68 -16 11 C-13.595 9.797 -12.05 9.899 -9.375 9.938 C-8.149 9.951 -8.149 9.951 -6.898 9.965 C-5.959 9.982 -5.959 9.982 -5 10 C-5 8.02 -5 6.04 -5 4 C-6.98 4 -8.96 4 -11 4 C-10.67 3.01 -10.34 2.02 -10 1 C-7.36 1 -4.72 1 -2 1 C-2 1.66 -2 2.32 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#8393E1" transform="translate(430,519)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.405 5.761 -0.314 10.039 -3 14 C-5.562 15.438 -5.562 15.438 -8 16 C-8.99 16.33 -9.98 16.66 -11 17 C-17.006 17.538 -21.829 17.447 -27 14 C-27.33 13.34 -27.66 12.68 -28 12 C-27.319 12.324 -26.639 12.647 -25.938 12.98 C-22.485 14.179 -19.65 14.301 -16 14.312 C-14.804 14.329 -13.608 14.346 -12.375 14.363 C-8.713 13.969 -6.878 13.264 -4 11 C-2.801 9.234 -2.801 9.234 -2.188 7.312 C-1.796 6.219 -1.404 5.126 -1 4 C-4.716 3.799 -5.772 3.848 -9 6 C-8.188 3.562 -8.188 3.562 -7 1 C-4 0 -4 0 0 0 Z " fill="#808EDD" transform="translate(468,922)"/>
<path d="M0 0 C2.188 0.312 2.188 0.312 4 1 C5.463 6.559 5.02 12.181 4.873 17.875 C4.804 23.009 5.329 27.137 7 32 C7.722 31.814 8.444 31.629 9.188 31.438 C12.128 30.98 14.141 31.265 17 32 C17 32.66 17 33.32 17 34 C13.825 35.588 10.458 35.411 7 35 C4.825 33.457 4.182 32.363 3 30 C2.915 27.968 2.893 25.933 2.902 23.898 C2.906 22.687 2.909 21.475 2.912 20.227 C2.92 18.956 2.929 17.685 2.938 16.375 C2.942 15.096 2.947 13.817 2.951 12.5 C2.963 9.333 2.979 6.167 3 3 C1.02 3 -0.96 3 -3 3 C-1.75 1.438 -1.75 1.438 0 0 Z " fill="#7C8BDA" transform="translate(323,896)"/>
<path d="M0 0 C-0.557 0.401 -1.114 0.802 -1.688 1.215 C-10.617 7.867 -18.318 14.515 -20 26 C-20.201 29.684 -20.189 33.316 -20 37 C-22.616 34.384 -22.328 33.146 -22.375 29.5 C-22.193 20.012 -18.63 11.819 -12 5 C-4.175 -1.392 -4.175 -1.392 0 0 Z " fill="#F4F5FD" transform="translate(309,275)"/>
<path d="M0 0 C3.206 1.282 4.955 3.281 7.188 5.875 C8 8.188 8 8.188 8.188 9.875 C5 10.75 5 10.75 1.188 10.875 C-1.75 8.188 -1.75 8.188 -3.812 4.875 C-3.812 3.555 -3.812 2.235 -3.812 0.875 C-1.812 -0.125 -1.812 -0.125 0 0 Z " fill="#E1E6FE" transform="translate(459.8125,900.125)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C2.98 3 4.96 3 7 3 C7.956 6.998 8.104 10.708 8.062 14.812 C8.053 15.974 8.044 17.135 8.035 18.332 C8.024 19.212 8.012 20.093 8 21 C8.99 21 9.98 21 11 21 C9.35 22.32 7.7 23.64 6 25 C6 18.4 6 11.8 6 5 C3.69 5 1.38 5 -1 5 C-1 13.91 -1 22.82 -1 32 C-1.33 32 -1.66 32 -2 32 C-2 21.77 -2 11.54 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#798BDD" transform="translate(359,880)"/>
<path d="M0 0 C5.271 3.597 7.942 8.067 10 14 C10.99 14 11.98 14 13 14 C13.125 21.75 13.125 21.75 12 24 C11.01 24 10.02 24 9 24 C8.853 22.805 8.853 22.805 8.703 21.586 C7.623 13.929 6.338 9.012 0.438 3.688 C-3.519 0.949 -6.98 0.764 -11.688 0.438 C-12.867 0.354 -14.046 0.27 -15.262 0.184 C-16.165 0.123 -17.069 0.062 -18 0 C-18 -0.33 -18 -0.66 -18 -1 C-11.9 -4.219 -6.079 -2.453 0 0 Z " fill="#7E91DE" transform="translate(386,486)"/>
<path d="M0 0 C3.894 2.057 6.721 4.202 9 8 C9 8.66 9 9.32 9 10 C13.607 8.79 13.607 8.79 18 7 C17.67 8.32 17.34 9.64 17 11 C13.7 11.33 10.4 11.66 7 12 C6.567 10.886 6.134 9.772 5.688 8.625 C4.301 4.825 4.301 4.825 1 3 C-3.863 2.362 -8.427 2.106 -12.75 4.562 C-14.507 7.989 -13.847 10.33 -13 14 C-13.66 14 -14.32 14 -15 14 C-16.444 11.111 -16.377 9.204 -16 6 C-11.477 0.499 -7.035 -0.973 0 0 Z " fill="#7D8FDB" transform="translate(602,477)"/>
<path d="M0 0 C1.359 0.965 2.696 1.962 4 3 C2.697 3.039 2.697 3.039 1.367 3.078 C-7.635 3.33 -7.635 3.33 -15 8 C-20.678 14.789 -19.362 23.708 -19 32 C-21 30 -21 30 -21.125 27.375 C-21.084 26.591 -21.043 25.808 -21 25 C-21.66 24.67 -22.32 24.34 -23 24 C-22.67 20.7 -22.34 17.4 -22 14 C-21.34 14 -20.68 14 -20 14 C-19.794 13.113 -19.587 12.226 -19.375 11.312 C-17.128 5.9 -13.463 3.744 -8.438 1.188 C-5.245 0.085 -3.307 -0.536 0 0 Z " fill="#8191DE" transform="translate(455,896)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4.054 2.709 4.094 5.416 4.125 8.125 C4.142 8.886 4.159 9.646 4.176 10.43 C4.215 15.033 3.759 18.665 2 23 C1.67 23 1.34 23 1 23 C0.132 17.798 -0.125 12.83 -0.062 7.562 C-0.058 6.831 -0.053 6.099 -0.049 5.346 C-0.037 3.564 -0.019 1.782 0 0 Z " fill="#DCE2FE" transform="translate(501,901)"/>
<path d="M0 0 C3 0.25 3 0.25 5 2.25 C5.25 4.75 5.25 4.75 5 7.25 C3 9.25 3 9.25 0 9.5 C-3 9.25 -3 9.25 -5 7.25 C-5.25 4.75 -5.25 4.75 -5 2.25 C-3 0.25 -3 0.25 0 0 Z " fill="#D2DBFC" transform="translate(768,519.75)"/>
<path d="M0 0 C2.707 2.707 2.976 5.325 4 9 C7.04 17.252 11.151 22.221 19 26 C19.99 26 20.98 26 22 26 C22 26.66 22 27.32 22 28 C25.96 28.33 29.92 28.66 34 29 C34 29.33 34 29.66 34 30 C25.339 30.593 18.147 29.782 10.996 24.527 C3.698 17.86 0.47 9.721 0 0 Z " fill="#657DCD" transform="translate(537,341)"/>
<path d="M0 0 C6.93 0 13.86 0 21 0 C18.719 9.123 18.719 9.123 16.625 12.438 C16.225 13.096 15.826 13.755 15.414 14.434 C13.704 16.328 12.485 16.625 10 17 C11 15 11 15 12 14 C11.67 13.34 11.34 12.68 11 12 C12.32 12 13.64 12 15 12 C15.99 9.03 16.98 6.06 18 3 C12.06 2.67 6.12 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7184D1" transform="translate(789,183)"/>
<path d="M0 0 C0.227 0.557 0.454 1.114 0.688 1.688 C2.506 4.891 4.023 6.767 7 9 C11.383 9.767 15.356 9.984 19.246 7.656 C21.256 6.02 21.256 6.02 22 3 C22.66 3 23.32 3 24 3 C24.33 5.97 24.66 8.94 25 12 C25.66 12 26.32 12 27 12 C26.67 12.66 26.34 13.32 26 14 C25.34 14 24.68 14 24 14 C24 13.34 24 12.68 24 12 C23.01 11.67 22.02 11.34 21 11 C20.67 10.67 20.34 10.34 20 10 C19.01 10.495 19.01 10.495 18 11 C13.668 12.444 9.347 13.141 5.152 11.102 C3.5 10 3.5 10 1 8 C0.01 8.495 0.01 8.495 -1 9 C-1.098 2.848 -1.098 2.848 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#818EDC" transform="translate(319,927)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.051 5.116 0.287 6.739 -1.441 8.312 C-3.415 10.449 -4.146 12.454 -5.125 15.188 C-5.458 16.089 -5.79 16.99 -6.133 17.918 C-8.066 24.787 -8.23 31.909 -8.562 39 C-8.606 39.87 -8.649 40.74 -8.693 41.637 C-8.798 43.758 -8.9 45.879 -9 48 C-9.33 48 -9.66 48 -10 48 C-10.804 16.091 -10.804 16.091 -2.938 4.375 C-2.39 3.558 -1.842 2.74 -1.277 1.898 C-0.856 1.272 -0.434 0.645 0 0 Z " fill="#637ACC" transform="translate(317,129)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.01 1.495 4.01 1.495 3 2 C2.959 2.763 2.918 3.526 2.875 4.312 C2 7 2 7 0.301 8.184 C-8.471 12.61 -16.44 12.627 -26 11 C-26.33 10.01 -26.66 9.02 -27 8 C-26.092 8.186 -25.185 8.371 -24.25 8.562 C-21.209 8.972 -19.849 8.76 -17 8 C-14.75 8.438 -14.75 8.438 -13 9 C-13 8.67 -13 8.34 -13 8 C-12.397 8.012 -11.793 8.023 -11.172 8.035 C-10.373 8.044 -9.573 8.053 -8.75 8.062 C-7.961 8.074 -7.172 8.086 -6.359 8.098 C-4.093 8.004 -2.171 7.632 0 7 C0 5.68 0 4.36 0 3 C0.66 3 1.32 3 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#97A7E3" transform="translate(344,360)"/>
<path d="M0 0 C-3 1 -3 1 -6 0 C-15.743 -0.761 -23.725 -0.009 -31.375 6.375 C-34.165 8.993 -36.314 11.523 -38 15 C-38.33 14.01 -38.66 13.02 -39 12 C-34.968 4.878 -28.9 -0.134 -20.938 -2.375 C-12.922 -3.647 -7.485 -2.911 0 0 Z " fill="#F5F6FD" transform="translate(579,290)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-1 2.34 -1 1.68 -1 1 C-2.98 1.33 -4.96 1.66 -7 2 C-6.412 3.64 -6.412 3.64 -5.812 5.312 C-5.592 5.927 -5.372 6.542 -5.145 7.176 C-3.451 9.875 -1.929 10.722 1 12 C3.951 12.344 6.847 12.284 9.812 12.188 C10.995 12.167 10.995 12.167 12.201 12.146 C14.134 12.111 16.067 12.058 18 12 C16 14 16 14 13.375 14.125 C12.591 14.084 11.808 14.043 11 14 C11 14.66 11 15.32 11 16 C-1.935 14.598 -1.935 14.598 -7 9 C-8.188 6.812 -8.188 6.812 -9 5 C-9.99 4.34 -10.98 3.68 -12 3 C-8.239 -0.761 -5.027 -0.251 0 0 Z " fill="#7E8DDD" transform="translate(406,924)"/>
<path d="M0 0 C2.614 2.385 4.323 4.948 6.145 7.973 C6.739 8.956 7.334 9.938 7.947 10.951 C8.563 11.978 9.178 13.005 9.812 14.062 C10.416 15.062 11.02 16.062 11.643 17.092 C12.845 19.084 14.043 21.078 15.237 23.075 C16.415 25.029 17.61 26.973 18.822 28.906 C19.658 30.25 19.658 30.25 20.512 31.621 C21.267 32.817 21.267 32.817 22.038 34.037 C22.355 34.685 22.673 35.333 23 36 C22.67 36.66 22.34 37.32 22 38 C21.381 37.175 20.763 36.35 20.125 35.5 C19.424 34.675 18.722 33.85 18 33 C17.34 33 16.68 33 16 33 C15.773 31.853 15.546 30.705 15.312 29.523 C14.086 24.624 11.751 20.814 9 16.625 C8.549 15.915 8.098 15.204 7.633 14.473 C5.881 11.742 4.301 9.301 2 7 C1.01 7.495 1.01 7.495 0 8 C0.278 7.134 0.278 7.134 0.562 6.25 C1.04 3.793 0.757 2.356 0 0 Z " fill="#6C80CD" transform="translate(651,166)"/>
<path d="M0 0 C2.125 0.375 2.125 0.375 4 1 C3.435 2.32 2.847 3.631 2.25 4.938 C1.925 5.668 1.6 6.399 1.266 7.152 C0 9 0 9 -2.016 9.691 C-2.67 9.793 -3.325 9.895 -4 10 C-4.99 10.495 -4.99 10.495 -6 11 C-8 11.04 -10 11.043 -12 11 C-12 10.01 -12 9.02 -12 8 C-10.721 7.753 -9.442 7.505 -8.125 7.25 C-4.797 6.512 -4.797 6.512 -2.625 4 C-2.419 3.34 -2.212 2.68 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#D0D7FD" transform="translate(464,925)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C4.99 1.34 5.98 0.68 7 0 C10.137 0.242 10.137 0.242 13.688 0.875 C14.867 1.079 16.046 1.282 17.262 1.492 C18.165 1.66 19.069 1.827 20 2 C17.353 3.461 16.106 4 13 4 C13.66 5.65 14.32 7.3 15 9 C11.32 8.365 11.32 8.365 9.688 6.5 C7.102 4.202 4.352 4.316 1 4 C1.33 5.32 1.66 6.64 2 8 C1.34 8 0.68 8 0 8 C-0.381 5.674 -0.713 3.339 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A2ADEC" transform="translate(509,529)"/>
<path d="M0 0 C2.31 1.65 4.62 3.3 7 5 C12.616 6.814 17.482 5.876 23 4 C23.99 4 24.98 4 26 4 C22.092 7.099 19.29 8.135 14.312 8.062 C13.1 8.049 13.1 8.049 11.863 8.035 C10.941 8.018 10.941 8.018 10 8 C10.33 8.99 10.66 9.98 11 11 C9.02 10.34 7.04 9.68 5 9 C5.33 8.01 5.66 7.02 6 6 C4.35 5.67 2.7 5.34 1 5 C1 10.28 1 15.56 1 21 C0.67 21 0.34 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#7689D8" transform="translate(318,523)"/>
<path d="M0 0 C3.752 2.015 5.636 3.455 8 7 C7.67 7.99 7.34 8.98 7 10 C6.587 9.216 6.175 8.433 5.75 7.625 C3.937 4.906 3.086 3.935 0 3 C-2.197 3.298 -2.197 3.298 -4 4 C-2.68 4.99 -1.36 5.98 0 7 C-0.33 7.66 -0.66 8.32 -1 9 C-1.66 8.67 -2.32 8.34 -3 8 C-4.537 7.775 -6.08 7.592 -7.625 7.438 C-8.442 7.354 -9.26 7.27 -10.102 7.184 C-10.728 7.123 -11.355 7.062 -12 7 C-11.01 6.01 -10.02 5.02 -9 4 C-9.66 3.67 -10.32 3.34 -11 3 C-11 2.34 -11 1.68 -11 1 C-7.276 -0.241 -3.868 -0.541 0 0 Z " fill="#E9EDFD" transform="translate(744,486)"/>
<path d="M0 0 C2.386 2.303 3.947 4.817 5.688 7.629 C6.348 7.629 7.007 7.629 7.688 7.629 C7.027 8.949 6.368 10.269 5.688 11.629 C5.278 10.991 4.868 10.353 4.445 9.695 C3.906 8.869 3.368 8.043 2.812 7.191 C2.279 6.368 1.745 5.544 1.195 4.695 C-0.151 2.519 -0.151 2.519 -2.312 1.629 C-2.642 2.289 -2.973 2.949 -3.312 3.629 C-3.642 1.979 -3.973 0.329 -4.312 -1.371 C-5.632 -1.041 -6.952 -0.711 -8.312 -0.371 C-8.312 -1.361 -8.312 -2.351 -8.312 -3.371 C-12.767 -4.361 -12.767 -4.361 -17.312 -5.371 C-17.642 -4.711 -17.973 -4.051 -18.312 -3.371 C-20.293 -4.031 -22.272 -4.691 -24.312 -5.371 C-24.312 -5.701 -24.312 -6.031 -24.312 -6.371 C-14.929 -8.282 -7.446 -5.99 0 0 Z " fill="#91A1DE" transform="translate(460.3125,296.37109375)"/>
<path d="M0 0 C2.384 2.208 4.016 4.455 5.672 7.246 C6.168 8.079 6.665 8.911 7.177 9.769 C7.696 10.649 8.215 11.53 8.75 12.438 C9.282 13.332 9.814 14.227 10.362 15.149 C14.278 21.745 18.165 28.357 22 35 C17.304 33.047 16.281 30.4 14 26 C13.34 26 12.68 26 12 26 C11.536 24.206 11.536 24.206 11.062 22.375 C9.292 16.925 6.269 12.471 3.049 7.787 C0 3.293 0 3.293 0 0 Z " fill="#697ECF" transform="translate(688,354)"/>
<path d="M0 0 C-0.918 0.485 -1.836 0.969 -2.781 1.469 C-16.101 8.665 -22.988 16.722 -30 30 C-31 27 -31 27 -29.742 24.293 C-24.677 15.868 -12.253 -4.084 0 0 Z " fill="#677ECB" transform="translate(547,273)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.564 1.413 1.129 1.825 0.68 2.25 C-3.721 6.517 -7.456 11.002 -11 16 C-9.909 15.766 -9.909 15.766 -8.797 15.527 C-1.264 14.069 5.403 14.003 13 15 C13 15.33 13 15.66 13 16 C11.805 16.061 10.61 16.121 9.379 16.184 C-2.335 16.792 -2.335 16.792 -14 18 C-11.8 13 -9.033 9.513 -5.312 5.625 C-4.801 5.08 -4.29 4.534 -3.764 3.973 C-2.515 2.643 -1.259 1.32 0 0 Z " fill="#8799E0" transform="translate(461,660)"/>
<path d="M0 0 C2.251 2.251 3.318 4.606 4.665 7.46 C7.997 14.515 11.324 20.53 17 26 C17.51 26.522 18.021 27.044 18.547 27.582 C19.681 28.738 20.837 29.873 22 31 C17.313 29.722 14.896 27.058 11.746 23.559 C9.823 21.842 8.516 21.402 6 21 C5.938 20.051 5.876 19.103 5.812 18.125 C5.218 13.701 3.206 10.194 1.059 6.332 C0 4 0 4 0 0 Z " fill="#6077CA" transform="translate(388,352)"/>
<path d="M0 0 C1.454 0.031 1.454 0.031 2.938 0.062 C1.947 0.558 1.947 0.558 0.938 1.062 C-0.115 3.694 -0.115 3.694 -0.062 5.062 C0.268 5.062 0.597 5.062 0.938 5.062 C0.938 8.692 0.938 12.322 0.938 16.062 C-1.062 15.062 -1.062 15.062 -2.062 14.062 C-2.001 14.97 -1.939 15.877 -1.875 16.812 C-2.079 20.352 -2.708 21.512 -5.062 24.062 C-4.898 23.254 -4.732 22.446 -4.562 21.613 C-3.976 17.448 -3.991 13.447 -4.062 9.25 C-4.108 6.515 -4.11 3.799 -4.062 1.062 C-3.062 0.062 -3.062 0.062 0 0 Z " fill="#91A1E7" transform="translate(669.0625,495.9375)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 6.27 4 12.54 4 19 C3.01 19.33 2.02 19.66 1 20 C0.67 13.4 0.34 6.8 0 0 Z " fill="#E0E7FE" transform="translate(451,488)"/>
<path d="M0 0 C4.43 2.215 6.613 4.669 9 9 C9 10.32 9 11.64 9 13 C6.69 13.33 4.38 13.66 2 14 C1.685 13.518 1.371 13.036 1.047 12.539 C-2.079 7.664 -2.079 7.664 -7 5 C-9.668 4.568 -12.307 4.269 -15 4 C-15 3.67 -15 3.34 -15 3 C-8.386 2.46 -4.394 3.008 1 7 C2.043 8.64 3.056 10.301 4 12 C4.99 12 5.98 12 7 12 C6.406 10.704 5.799 9.413 5.188 8.125 C4.683 7.046 4.683 7.046 4.168 5.945 C3.005 3.695 3.005 3.695 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EFF2FD" transform="translate(609,473)"/>
<path d="M0 0 C0.94 2.821 1.668 5.613 2.375 8.5 C3.826 14.36 5.386 20.184 7 26 C7.66 26 8.32 26 9 26 C9 28.31 9 30.62 9 33 C9.66 33 10.32 33 11 33 C10.67 34.32 10.34 35.64 10 37 C6.785 32.177 5.344 26.59 3.522 21.121 C2.814 19.004 2.092 16.893 1.369 14.781 C0.918 13.438 0.468 12.094 0.02 10.75 C-0.392 9.528 -0.803 8.306 -1.227 7.047 C-2 4 -2 4 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#7083CD" transform="translate(484,153)"/>
<path d="M0 0 C6.137 1.677 10.517 3.413 15 8 C15.606 9.239 16.19 10.49 16.75 11.75 C18.607 15.178 18.607 15.178 21.469 15.668 C23.997 15.913 26.461 16.034 29 16 C29 16.99 29 17.98 29 19 C28.092 18.794 27.185 18.588 26.25 18.375 C22.89 17.987 21.759 18.196 19 20 C18.711 19.031 18.423 18.061 18.125 17.062 C17.358 13.893 17.358 13.893 15 13 C14.361 12.01 13.721 11.02 13.062 10 C11.277 6.758 11.277 6.758 8 6 C7.34 5.34 6.68 4.68 6 4 C4.033 3.327 4.033 3.327 1.875 2.875 C0.596 2.586 -0.683 2.298 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#7285D0" transform="translate(788,129)"/>
<path d="M0 0 C-0.66 0.33 -1.32 0.66 -2 1 C-4.009 0.018 -6.008 -0.984 -8 -2 C-16.402 -4.432 -23.399 -2.941 -31 1 C-34.74 3.534 -37.874 6.754 -41 10 C-39.633 4.359 -35.517 1.261 -31 -2 C-21.057 -7.991 -9.353 -5.911 0 0 Z " fill="#5E73C8" transform="translate(260,134)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.541 10.047 -2.654 17.744 -10.062 23.5 C-12.917 25.419 -15.634 27.158 -19 28 C-20.343 27.708 -21.679 27.38 -23 27 C-21.044 25.995 -19.085 24.996 -17.125 24 C-16.034 23.443 -14.944 22.886 -13.82 22.312 C-11 21 -11 21 -9 21 C-8.753 20.464 -8.505 19.928 -8.25 19.375 C-7 17 -7 17 -5.062 14.438 C-2.094 10.135 -1.179 5.02 0 0 Z " fill="#7186D1" transform="translate(595,342)"/>
<path d="M0 0 C6 0 12 0 18 0 C18 0.66 18 1.32 18 2 C20.463 3.645 20.463 3.645 23 5 C22.67 5.66 22.34 6.32 22 7 C21.318 6.514 20.636 6.028 19.934 5.527 C14.635 2.769 8.823 3.365 3 4 C-0.409 5.303 -2.433 7.433 -5 10 C-4.453 6.169 -2.718 4.631 0 2 C0 1.34 0 0.68 0 0 Z " fill="#95A0E6" transform="translate(404,896)"/>
<path d="M0 0 C0.577 0.928 1.155 1.856 1.75 2.812 C4.328 6.465 5.824 7.503 10 9 C12.994 9.5 15.972 9.805 19 10 C19 10.33 19 10.66 19 11 C7.102 11.572 7.102 11.572 2 8 C-1 4.7 -1 4.7 -1 2 C-2.32 2.33 -3.64 2.66 -5 3 C-4.67 3.763 -4.34 4.526 -4 5.312 C-3 8 -3 8 -3 11 C-5.912 7.712 -7.497 5.401 -8 1 C-2.25 0 -2.25 0 0 0 Z " fill="#F1F3FE" transform="translate(583,513)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.598 0.825 3.196 1.65 3.812 2.5 C5.943 4.806 7.014 5.001 10.219 5.332 C13.492 5.314 16.735 5.215 20 5 C18.35 6.65 16.7 8.3 15 10 C14.67 9.34 14.34 8.68 14 8 C12.35 8 10.7 8 9 8 C9 8.33 9 8.66 9 9 C3.585 9.369 3.585 9.369 0.625 7.5 C0.089 7.005 -0.447 6.51 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z M2 5 C2.33 5.66 2.66 6.32 3 7 C3.33 6.34 3.66 5.68 4 5 C3.34 5 2.68 5 2 5 Z M5 6 C6 8 6 8 6 8 Z " fill="#95A0E6" transform="translate(521,931)"/>
<path d="M0 0 C2 2 2 2 2.227 5.348 C2.227 6.776 2.215 8.205 2.195 9.633 C2.192 10.385 2.19 11.136 2.187 11.911 C2.176 14.316 2.15 16.72 2.125 19.125 C2.115 20.754 2.106 22.383 2.098 24.012 C2.076 28.008 2.041 32.004 2 36 C1.01 36.495 1.01 36.495 0 37 C-0.195 34.897 -0.381 32.792 -0.562 30.688 C-0.667 29.516 -0.771 28.344 -0.879 27.137 C-1 24 -1 24 0 21 C-0.66 20.67 -1.32 20.34 -2 20 C-1.34 20 -0.68 20 0 20 C0 13.4 0 6.8 0 0 Z " fill="#AEB6F2" transform="translate(355,884)"/>
<path d="M0 0 C0.808 0.102 1.616 0.204 2.449 0.309 C3.064 0.392 3.679 0.476 4.312 0.562 C3.653 1.222 2.992 1.883 2.312 2.562 C1.982 2.562 1.653 2.562 1.312 2.562 C1.312 9.822 1.312 17.082 1.312 24.562 C0.653 24.562 -0.008 24.562 -0.688 24.562 C-0.688 17.962 -0.688 11.362 -0.688 4.562 C-2.668 4.562 -4.648 4.562 -6.688 4.562 C-7.018 3.242 -7.347 1.923 -7.688 0.562 C-4.593 -0.469 -3.161 -0.412 0 0 Z " fill="#A1ADEC" transform="translate(521.6875,880.4375)"/>
<path d="M0 0 C3.342 1.366 4.659 2.55 6.188 5.812 C6.511 6.479 6.835 7.145 7.168 7.832 C10.062 15.373 10.21 24.577 7 32 C6.077 29.232 5.916 27.711 6 24.875 C6 23.596 6 22.317 6 21 C5.67 20.67 5.34 20.34 5 20 C4.863 17.473 4.836 15.461 5.688 13.062 C6.387 8.444 2.353 3.809 0 0 Z " fill="#7A8DD9" transform="translate(285,481)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 5.28 2.66 10.56 3 16 C10.26 16.33 17.52 16.66 25 17 C25 17.33 25 17.66 25 18 C12.625 18.495 12.625 18.495 0 19 C0 17.35 0 15.7 0 14 C-0.66 13.67 -1.32 13.34 -2 13 C-1.649 12.464 -1.299 11.928 -0.938 11.375 C0.291 8.264 -0.38 6.239 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#91A0E2" transform="translate(573,208)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.492 4.318 4.803 8.033 3 12 C1.875 8.25 1.875 8.25 3 6 C2.01 6.33 1.02 6.66 0 7 C-1.225 10.008 -1.225 10.008 -2 13 C-3.188 9.571 -2.802 7.622 -1.562 4.25 C-1.275 3.451 -0.988 2.652 -0.691 1.828 C-0.463 1.225 -0.235 0.622 0 0 Z " fill="#E3E6FD" transform="translate(738,899)"/>
<path d="M0 0 C0.958 0.005 1.917 0.01 2.904 0.016 C4.456 0.021 4.456 0.021 6.039 0.026 C7.127 0.034 8.216 0.042 9.338 0.051 C10.976 0.058 10.976 0.058 12.648 0.065 C15.357 0.077 18.066 0.093 20.775 0.114 C20.481 1.54 20.18 2.966 19.877 4.391 C19.71 5.185 19.543 5.979 19.371 6.797 C18.595 9.815 17.302 12.407 15.775 15.114 C15.445 14.454 15.115 13.794 14.775 13.114 C15.442 11.447 16.108 9.78 16.775 8.114 C17.137 5.784 17.473 3.451 17.775 1.114 C15.003 1.2 12.234 1.307 9.463 1.426 C8.677 1.449 7.891 1.471 7.082 1.494 C5.946 1.548 5.946 1.548 4.787 1.602 C3.742 1.641 3.742 1.641 2.676 1.681 C0.245 2.234 -0.616 3.249 -2.225 5.114 C-2.885 5.774 -3.545 6.434 -4.225 7.114 C-3.19 0.163 -3.19 0.163 0 0 Z " fill="#F0F3FD" transform="translate(271.224853515625,188.886474609375)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 2.66 -0.98 3.32 -2 4 C-1.649 4.557 -1.299 5.114 -0.938 5.688 C0.232 8.572 -0.147 10.052 -1 13 C-1.33 13.66 -1.66 14.32 -2 15 C-2.392 17.709 -2.746 20.408 -3.062 23.125 C-3.153 23.879 -3.244 24.633 -3.338 25.41 C-3.562 27.273 -3.781 29.136 -4 31 C-6.174 27.546 -5.846 25.889 -5 22 C-4.862 20.381 -4.757 18.76 -4.684 17.137 C-4.642 16.25 -4.6 15.364 -4.557 14.451 C-4.517 13.539 -4.478 12.627 -4.438 11.688 C-4.373 10.29 -4.373 10.29 -4.307 8.865 C-4.201 6.577 -4.099 4.289 -4 2 C-2.68 1.34 -1.36 0.68 0 0 Z " fill="#6B81D1" transform="translate(837,161)"/>
<path d="M0 0 C3 1 3 1 4.625 3.312 C7.578 11.232 7.961 20.015 5 28 C3.844 30.365 2.727 31.959 1 34 C0.01 34 -0.98 34 -2 34 C-1.381 33.278 -0.763 32.556 -0.125 31.812 C4.958 25.086 4.638 18.159 4 10 C3.274 7.322 2.36 5.448 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#7889DA" transform="translate(543,901)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.083 11.601 2.083 11.601 1.812 16 C1.691 20.787 3.56 25.038 5.223 29.461 C6 32 6 32 5 34 C-0.914 23.551 -1.72 12.894 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#6B82D1" transform="translate(332,159)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 8.58 3 17.16 3 26 C2.34 26.33 1.68 26.66 1 27 C0.67 18.09 0.34 9.18 0 0 Z " fill="#FAFBFE" transform="translate(731,271)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.394 1.147 -1.394 1.147 -2.816 1.297 C-9.864 2.149 -15.121 2.991 -21 7 C-21.66 7 -22.32 7 -23 7 C-23.276 7.572 -23.552 8.145 -23.836 8.734 C-24.995 10.99 -26.269 13.109 -27.625 15.25 C-28.071 15.956 -28.517 16.663 -28.977 17.391 C-29.483 18.187 -29.483 18.187 -30 19 C-31 16 -31 16 -30.031 13.914 C-25.773 7.549 -21.884 3.569 -14.75 0.688 C-9.801 -0.22 -5.016 -0.163 0 0 Z " fill="#576DC2" transform="translate(788,129)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 6.27 1.66 12.54 2 19 C0.02 19 -1.96 19 -4 19 C-3.34 18.67 -2.68 18.34 -2 18 C-2.023 16.868 -2.046 15.736 -2.07 14.57 C-2.089 13.089 -2.107 11.607 -2.125 10.125 C-2.142 9.379 -2.159 8.632 -2.176 7.863 C-2.193 5.908 -2.103 3.953 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#D1DBFD" transform="translate(769,494)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 8.91 1 17.82 1 27 C0.67 26.01 0.34 25.02 0 24 C-0.66 24 -1.32 24 -2 24 C-2 21.333 -2 18.667 -2 16 C-2.041 14.824 -2.082 13.649 -2.125 12.438 C-2.141 8.018 -1.211 4.238 0 0 Z M-1 8 C0 12 0 12 0 12 Z " fill="#8B9FDE" transform="translate(434,184)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-1.516 6.794 -4.111 9.262 -10 11 C-13.351 11.22 -16.648 11.182 -20 11 C-17.548 8.073 -17.548 8.073 -15.062 7.742 C-14.382 7.786 -13.701 7.83 -13 7.875 C-10.147 8.037 -8.811 7.937 -6 7 C-5.34 6.01 -4.68 5.02 -4 4 C-2.667 2.667 -1.333 1.333 0 0 Z " fill="#8193DB" transform="translate(290,512)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1 1.68 1 1 1 C1 2.98 1 4.96 1 7 C0.67 5.68 0.34 4.36 0 3 C-2.31 3.33 -4.62 3.66 -7 4 C-7 7.63 -7 11.26 -7 15 C-8.98 15 -10.96 15 -13 15 C-12.01 14.67 -11.02 14.34 -10 14 C-10.186 13.464 -10.371 12.928 -10.562 12.375 C-11.136 9.261 -10.843 7.049 -10 4 C-7.746 1.585 -6.623 1.029 -3.312 0.875 C-2.168 0.937 -2.168 0.937 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8F9CE4" transform="translate(485,884)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 1.99 4 2.98 4 4 C3.117 4.245 2.234 4.49 1.324 4.742 C-8.263 7.729 -14.062 11.708 -21 19 C-21 16 -21 16 -19.199 13.914 C-13.234 8.478 -7.552 4.857 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7E8FD9" transform="translate(511,693)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.33 4.63 2.66 8.26 3 12 C-7.56 12 -18.12 12 -29 12 C-29 11.67 -29 11.34 -29 11 C-14.15 10.505 -14.15 10.505 1 10 C0.67 9.01 0.34 8.02 0 7 C-0.04 4.667 -0.044 2.333 0 0 Z " fill="#F8FAFE" transform="translate(392,497)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C0.202 6.697 -4.59 9.095 -11.062 10.812 C-15.21 11.077 -19.24 10.88 -23 9 C-22.67 8.67 -22.34 8.34 -22 8 C-20.145 8.033 -18.291 8.102 -16.438 8.188 C-10.455 8.266 -7.055 7.111 -2 4 C-2 3.34 -2 2.68 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8E9FDF" transform="translate(459,360)"/>
<path d="M0 0 C6.605 5.662 7.94 11.516 9 20 C3.39 19.67 -2.22 19.34 -8 19 C-8 18.67 -8 18.34 -8 18 C-3.05 18 1.9 18 7 18 C5.824 14.937 5.824 14.937 4.625 11.812 C4.184 10.664 3.743 9.515 3.289 8.332 C2.211 5.547 1.116 2.77 0 0 Z " fill="#F5F6FE" transform="translate(282,126)"/>
<path d="M0 0 C7.92 0 15.84 0 24 0 C23.67 0.66 23.34 1.32 23 2 C15.74 2 8.48 2 1 2 C1 2.66 1 3.32 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#788CD9" transform="translate(574,109)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.195 2.051 2.391 4.102 2.586 6.152 C2.723 6.762 2.859 7.372 3 8 C3.66 8.33 4.32 8.66 5 9 C5.625 12.062 5.625 12.062 6 15 C6.99 13.68 7.98 12.36 9 11 C8 18.429 8 18.429 7 22 C6.34 22 5.68 22 5 22 C4.163 18.709 3.331 15.417 2.5 12.125 C2.142 10.719 2.142 10.719 1.777 9.285 C1.552 8.389 1.326 7.493 1.094 6.57 C0.884 5.743 0.675 4.915 0.459 4.063 C0 2 0 2 0 0 Z " fill="#7A8AD8" transform="translate(702,904)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.258 5.008 -0.258 5.008 -2.125 7.125 C-2.726 7.829 -3.326 8.533 -3.945 9.258 C-6.324 11.274 -7.976 11.501 -11 12 C-11.33 12.66 -11.66 13.32 -12 14 C-17.338 15.394 -21.911 15.218 -27 13 C-27 12.67 -27 12.34 -27 12 C-26.154 11.963 -25.309 11.925 -24.438 11.887 C-13.655 11.547 -13.655 11.547 -4.441 6.539 C-2.68 4.493 -1.208 2.416 0 0 Z " fill="#778AD9" transform="translate(618,518)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C4.828 8.18 3.332 11.44 -1 16 C-1.66 15.67 -2.32 15.34 -3 15 C-1.73 13.503 -0.461 12.005 0.809 10.508 C2.088 8.994 2.088 8.994 3 7 C0.69 6.67 -1.62 6.34 -4 6 C-3.67 4.35 -3.34 2.7 -3 1 C-2.67 1.99 -2.34 2.98 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#9BA9EB" transform="translate(390,512)"/>
<path d="M0 0 C3.497 0.503 5.152 1.7 7.445 4.352 C9.276 8.089 9.67 11.024 9.57 15.164 C9.552 16.14 9.534 17.116 9.516 18.121 C9.492 18.857 9.469 19.593 9.445 20.352 C9.115 20.352 8.785 20.352 8.445 20.352 C8.408 19.783 8.371 19.215 8.332 18.629 C7.877 13.288 7.122 9.036 4.445 4.352 C0.034 1.143 -5.459 2.619 -10.555 3.352 C-7.461 -0.46 -4.655 -0.149 0 0 Z " fill="#7589D6" transform="translate(648.5546875,492.6484375)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-1.949 1.959 -2.898 1.918 -3.875 1.875 C-7.102 1.65 -7.102 1.65 -9 4 C-6.69 4 -4.38 4 -2 4 C-1.67 4.66 -1.34 5.32 -1 6 C-2.052 5.938 -3.104 5.876 -4.188 5.812 C-9.014 6.05 -11.339 7.454 -14.812 10.688 C-15.204 11.121 -15.596 11.554 -16 12 C-15.108 7.985 -14.165 4.352 -11.438 1.188 C-7.715 -0.626 -4.069 -0.235 0 0 Z " fill="#EBEEFE" transform="translate(537,899)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C4.975 5.702 5.822 10.581 5 17 C3.5 20.625 3.5 20.625 2 23 C1.67 22.34 1.34 21.68 1 21 C1.33 20.34 1.66 19.68 2 19 C2.228 17.129 2.41 15.253 2.562 13.375 C2.688 11.871 2.688 11.871 2.816 10.336 C2.907 9.18 2.907 9.18 3 8 C2.01 7.67 1.02 7.34 0 7 C-1.125 2.25 -1.125 2.25 0 0 Z M1 5 C1 5.66 1 6.32 1 7 C1.66 6.67 2.32 6.34 3 6 C2.34 5.67 1.68 5.34 1 5 Z " fill="#8394DE" transform="translate(699,496)"/>
<path d="M0 0 C1.134 0.021 2.269 0.041 3.438 0.062 C3.438 1.053 3.438 2.043 3.438 3.062 C0.798 3.062 -1.842 3.062 -4.562 3.062 C-4.562 9.002 -4.562 14.942 -4.562 21.062 C-7.562 17.062 -7.562 17.062 -7.707 14.949 C-7.597 14.223 -7.488 13.498 -7.375 12.75 C-7.258 11.97 -7.14 11.19 -7.02 10.387 C-4.994 0.088 -4.994 0.088 0 0 Z " fill="#8999E2" transform="translate(437.5625,466.9375)"/>
<path d="M0 0 C0 3.582 -0.938 4.882 -2.75 7.938 C-6.021 13.735 -8.112 19.635 -10 26 C-10.33 26 -10.66 26 -11 26 C-10.663 15.883 -6.473 7.694 0 0 Z " fill="#F3F4FD" transform="translate(203,126)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.908 4.857 4.316 7.219 2.812 10.938 C-1.705 14.251 -6.583 13.383 -12 13 C-12 12.34 -12 11.68 -12 11 C-8.7 11 -5.4 11 -2 11 C-2 10.34 -2 9.68 -2 9 C-1.01 9 -0.02 9 1 9 C1.269 4.349 1.269 4.349 0 0 Z " fill="#94A0E5" transform="translate(301,917)"/>
<path d="M0 0 C3.28 0.042 4.403 0.121 7.188 2 C10.756 7.501 9.472 13.097 8.688 19.312 C6.688 17.312 6.688 17.312 6.492 14.711 C6.515 13.713 6.539 12.715 6.562 11.688 C6.581 10.685 6.599 9.682 6.617 8.648 C6.652 7.492 6.652 7.492 6.688 6.312 C5.697 5.983 4.707 5.652 3.688 5.312 C3.358 4.322 3.027 3.332 2.688 2.312 C0.377 2.312 -1.933 2.312 -4.312 2.312 C-4.642 2.973 -4.973 3.632 -5.312 4.312 C-5.312 -0.223 -4.13 0.128 0 0 Z " fill="#93A2E4" transform="translate(484.3125,492.6875)"/>
<path d="M0 0 C-2 1 -2 1 -5.125 0.5 C-11.318 -0.299 -15.811 -0.13 -21 3.562 C-21.99 4.367 -22.98 5.171 -24 6 C-22.504 2.192 -20.339 0.306 -17 -2 C-11.157 -4.418 -4.964 -3.723 0 0 Z " fill="#7A8ED9" transform="translate(285,480)"/>
<path d="M0 0 C2 -0.043 4 -0.041 6 0 C7 1 7 1 7.125 3.5 C7 6 7 6 6 7 C3.5 7.125 3.5 7.125 1 7 C0 6 0 6 -0.062 2.938 C-0.042 1.968 -0.021 0.999 0 0 Z " fill="#E8EBFD" transform="translate(500,885)"/>
<path d="M0 0 C2 2 2 2 3 4.062 C3.846 6.169 3.846 6.169 6 7 C5.67 15.58 5.34 24.16 5 33 C4.67 33 4.34 33 4 33 C3.986 32.063 3.986 32.063 3.972 31.107 C3.918 28.238 3.834 25.369 3.75 22.5 C3.736 21.518 3.722 20.535 3.707 19.523 C3.486 12.885 2.163 8.803 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#778BDC" transform="translate(750,487)"/>
<path d="M0 0 C10.769 0.85 18.06 7.306 25 15 C24.67 15.66 24.34 16.32 24 17 C23.416 16.346 23.416 16.346 22.82 15.68 C16.359 8.802 8.739 4.42 0 1 C0 0.67 0 0.34 0 0 Z " fill="#6C81CE" transform="translate(584,271)"/>
<path d="M0 0 C3.028 1.179 4.284 2.935 5.836 5.715 C5.836 7.035 5.836 8.355 5.836 9.715 C5.176 9.385 4.516 9.055 3.836 8.715 C3.692 8.075 3.547 7.436 3.398 6.777 C2.974 4.333 2.974 4.333 -0.164 2.715 C-4.849 2.207 -4.849 2.207 -9.039 3.965 C-10.269 5.878 -10.779 7.482 -11.164 9.715 C-11.824 9.715 -12.484 9.715 -13.164 9.715 C-12.951 5.979 -12.139 4.686 -9.602 1.84 C-6.38 -0.613 -3.913 -0.787 0 0 Z " fill="#8694DF" transform="translate(574.1640625,904.28515625)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.42 1.315 1.84 1.629 1.242 1.953 C-6.514 6.202 -12.898 10.573 -19 17 C-19.66 16.67 -20.32 16.34 -21 16 C-15.182 9.744 -9.793 4.611 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#F6F7FE" transform="translate(419,273)"/>
<path d="M0 0 C2.744 1.041 3.979 1.973 5.812 4.312 C7 7 7 7 6 12 C-0.6 12 -7.2 12 -14 12 C-14 11.67 -14 11.34 -14 11 C-7.73 10.67 -1.46 10.34 5 10 C2.467 3.504 2.467 3.504 0 0 Z " fill="#EBEEFE" transform="translate(575,904)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.324 1.68 3.324 1.68 4.688 3.875 C5.145 4.594 5.603 5.314 6.074 6.055 C6.38 6.697 6.685 7.339 7 8 C6.67 8.66 6.34 9.32 6 10 C5.01 9.01 4.02 8.02 3 7 C2.34 11.62 1.68 16.24 1 21 C0.67 21 0.34 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#8B98E1" transform="translate(616,899)"/>
<path d="M0 0 C2.012 0.391 2.012 0.391 5.074 2.141 C8.059 7.147 6.912 11.8 6.012 17.391 C5.352 17.061 4.692 16.731 4.012 16.391 C4.012 12.101 4.012 7.811 4.012 3.391 C2.898 3.411 1.784 3.432 0.637 3.453 C-2.988 3.391 -2.988 3.391 -4.988 2.391 C-7.086 2.79 -7.086 2.79 -8.988 3.391 C-6.704 -0.036 -3.961 -0.205 0 0 Z " fill="#8A9BDF" transform="translate(738.98828125,492.609375)"/>
<path d="M0 0 C2.838 0.083 4.54 0.572 6.633 2.52 C11.421 8.013 12.145 11.932 12.062 19.188 C12.058 19.937 12.053 20.687 12.049 21.459 C12.037 23.306 12.019 25.153 12 27 C11.67 27 11.34 27 11 27 C10.961 25.623 10.961 25.623 10.922 24.219 C10.577 13.936 10.577 13.936 6 5 C4.072 3.392 2.197 2.248 0 1 C0 0.67 0 0.34 0 0 Z " fill="#F2F5FD" transform="translate(699,488)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 1.65 2.66 3.3 3 5 C0.059 7.941 -1.999 9.299 -6.125 9.375 C-10 9 -10 9 -13 7 C-12.67 6.01 -12.34 5.02 -12 4 C-10.515 4.99 -10.515 4.99 -9 6 C-8.01 6 -7.02 6 -6 6 C-4.333 6 -2.667 6 -1 6 C-0.297 3.024 -0.297 3.024 0 0 Z " fill="#909DE3" transform="translate(420,923)"/>
<path d="M0 0 C3.447 1.396 4.678 2.727 6.375 6 C8.251 9.226 8.749 9.886 12.312 11.5 C13.199 11.665 14.086 11.83 15 12 C13.68 13.32 12.36 14.64 11 16 C11 15.01 11 14.02 11 13 C10.402 12.959 9.804 12.917 9.188 12.875 C7 12 7 12 5.25 8.938 C4.837 7.968 4.425 6.999 4 6 C3.01 6 2.02 6 1 6 C0.67 4.02 0.34 2.04 0 0 Z M1 2 C2 4 2 4 2 4 Z " fill="#8C9CDE" transform="translate(260,135)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C15 0.66 15 1.32 15 2 C15.66 2.33 16.32 2.66 17 3 C11.72 3 6.44 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D2DCFC" transform="translate(235,224)"/>
<path d="M0 0 C1.339 4.016 0.189 5.523 -1.438 9.375 C-3.186 13.519 -4.194 16.512 -4 21 C-5 23.812 -5 23.812 -6 26 C-6.33 26 -6.66 26 -7 26 C-7.85 16.861 -4.183 7.971 0 0 Z " fill="#F7F8FE" transform="translate(738,133)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 6.94 2 12.88 2 19 C2.66 19.33 3.32 19.66 4 20 C4 21.32 4 22.64 4 24 C3.01 24 2.02 24 1 24 C0.67 24.99 0.34 25.98 0 27 C-0.197 24 -0.382 21.001 -0.562 18 C-0.619 17.161 -0.675 16.322 -0.732 15.457 C-1.042 10.141 -0.822 5.264 0 0 Z " fill="#B6BCF6" transform="translate(509,900)"/>
<path d="M0 0 C0.835 0.495 0.835 0.495 1.688 1 C4.303 2.131 6.181 2.157 9 2 C9.33 4.64 9.66 7.28 10 10 C9.01 10.66 8.02 11.32 7 12 C7 9.69 7 7.38 7 5 C6.051 5.062 5.103 5.124 4.125 5.188 C3.094 5.126 2.062 5.064 1 5 C0.34 4.01 -0.32 3.02 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#95A0E6" transform="translate(486,926)"/>
<path d="M0 0 C1.774 3.338 2.58 6.22 3 10 C2.67 10.33 2.34 10.66 2 11 C1.502 13.738 1.1 16.485 0.695 19.238 C-0.039 22.155 -1.044 23.75 -3 26 C-2.691 24.866 -2.381 23.731 -2.062 22.562 C-0.387 15.173 -0.329 7.544 0 0 Z " fill="#7085D5" transform="translate(302,492)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.025 0.761 1.05 1.523 1.076 2.307 C1.192 5.768 1.315 9.228 1.438 12.688 C1.477 13.886 1.516 15.084 1.557 16.318 C1.599 17.475 1.64 18.632 1.684 19.824 C1.72 20.887 1.757 21.95 1.795 23.046 C1.985 25.782 2.393 28.329 3 31 C2.01 31 1.02 31 0 31 C0 20.77 0 10.54 0 0 Z " fill="#8C96E0" transform="translate(648,907)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.66 2.31 2.32 4.62 3 7 C3.722 6.814 4.444 6.629 5.188 6.438 C8.128 5.98 10.141 6.265 13 7 C13 7.66 13 8.32 13 9 C9.822 10.589 6.461 10.404 3 10 C0.326 8.188 0.041 7.122 -1 4 C-0.562 1.812 -0.562 1.812 0 0 Z " fill="#8B98DF" transform="translate(327,921)"/>
<path d="M0 0 C2 3 2 3 2 5 C-1.799 8.186 -5.173 8.35 -9.914 8.398 C-12.469 7.91 -13.364 6.969 -15 5 C-12.667 4.958 -10.333 4.959 -8 5 C-7.67 5.33 -7.34 5.66 -7 6 C-4.789 5.572 -3.019 5.009 -1 4 C-0.348 1.975 -0.348 1.975 0 0 Z " fill="#8C9AE0" transform="translate(384,515)"/>
<path d="M0 0 C3.008 2.461 4.725 4.049 5.812 7.812 C5.874 8.534 5.936 9.256 6 10 C4 9 4 9 2.688 6.5 C1.252 3.798 1.252 3.798 -1.375 3.312 C-2.674 3.158 -2.674 3.158 -4 3 C-5.679 2.366 -7.35 1.707 -9 1 C-5.654 -1.107 -3.732 -1.365 0 0 Z " fill="#96A5E0" transform="translate(344,293)"/>
<path d="M0 0 C11.22 0.33 22.44 0.66 34 1 C34 1.33 34 1.66 34 2 C22.78 2.33 11.56 2.66 0 3 C0 2.01 0 1.02 0 0 Z " fill="#A0B0E8" transform="translate(433,109)"/>
<path d="M0 0 C4.29 0.66 8.58 1.32 13 2 C12.67 2.66 12.34 3.32 12 4 C5.004 5.376 5.004 5.376 1.5 4.062 C1.005 3.712 0.51 3.361 0 3 C0 2.01 0 1.02 0 0 Z M13 1 C15 2 15 2 15 2 Z " fill="#D0D9FC" transform="translate(519,539)"/>
<path d="M0 0 C7.592 1.326 13.541 8.102 18 14 C17.67 14.66 17.34 15.32 17 16 C16.237 15.01 15.474 14.02 14.688 13 C11.083 8.744 6.762 5.756 2.195 2.602 C1.471 2.073 0.746 1.545 0 1 C0 0.67 0 0.34 0 0 Z " fill="#6980CE" transform="translate(467,275)"/>
<path d="M0 0 C-2.664 1.776 -4.781 2.729 -7.75 3.812 C-12.445 5.637 -16.043 7.903 -20 11 C-20 10.01 -20 9.02 -20 8 C-14.272 3.227 -7.715 -0.723 0 0 Z " fill="#7288D3" transform="translate(317,270)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.66 0.34 3.32 0 4 C-7.59 4 -15.18 4 -23 4 C-23 3.67 -23 3.34 -23 3 C-21.952 2.951 -20.904 2.902 -19.824 2.852 C-18.445 2.777 -17.066 2.701 -15.688 2.625 C-14.997 2.594 -14.307 2.563 -13.596 2.531 C-8.434 2.232 -5.136 -0.278 0 0 Z M-5 2 C-1 3 -1 3 -1 3 Z " fill="#6E82D2" transform="translate(663,267)"/>
<path d="M0 0 C0.846 0.495 1.691 0.99 2.562 1.5 C6.395 3.172 8.862 3.39 13 3 C13.99 2.34 14.98 1.68 16 1 C16.99 1.33 17.98 1.66 19 2 C15.816 5.184 12.593 5.768 8.262 6.141 C4.679 5.918 2.61 4.343 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8C9EE4" transform="translate(519,516)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 4.3 2 7.6 2 11 C1.01 10.67 0.02 10.34 -1 10 C-1.33 11.65 -1.66 13.3 -2 15 C-4.152 11.772 -4.201 10.716 -4 7 C-3.01 7 -2.02 7 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#A2AFEA" transform="translate(624,503)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-2.97 3 -5.94 3 -9 3 C-9.33 3.99 -9.66 4.98 -10 6 C-10 5.34 -10 4.68 -10 4 C-10.99 3.67 -11.98 3.34 -13 3 C-12 1 -12 1 -9.312 -0.188 C-5.764 -1.058 -3.52 -0.838 0 0 Z " fill="#9CABEC" transform="translate(549,484)"/>
<path d="M0 0 C1.262 2.148 1.262 2.148 2 4 C1.515 3.711 1.031 3.423 0.531 3.125 C-6.177 -0.591 -11.475 -0.881 -19 1 C-13.777 -5.759 -6.244 -4.349 0 0 Z " fill="#7D91DE" transform="translate(497,487)"/>
<path d="M0 0 C2 3 2 3 1.539 6.137 C1.237 7.308 0.936 8.48 0.625 9.688 C0.329 10.867 0.032 12.046 -0.273 13.262 C-0.513 14.165 -0.753 15.069 -1 16 C-3.464 12.304 -3.576 9.29 -3 5 C-1.5 2 -1.5 2 0 0 Z M-2 5 C-1 9 -1 9 -1 9 Z " fill="#9CABE5" transform="translate(387,307)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 5.28 4 10.56 4 16 C3.01 16 2.02 16 1 16 C1.012 15.013 1.023 14.025 1.035 13.008 C1.044 11.726 1.053 10.445 1.062 9.125 C1.074 7.849 1.086 6.573 1.098 5.258 C1.232 2.113 1.232 2.113 0 0 Z " fill="#BBC2F5" transform="translate(402,494)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-0.743 3.66 -1.485 4.32 -2.25 5 C-6.25 8.681 -7.418 13.952 -9 19 C-9.33 19 -9.66 19 -10 19 C-10.773 12.968 -8.372 8.802 -5 4 C-2.188 1.438 -2.188 1.438 0 0 Z " fill="#EDF0FD" transform="translate(260,474)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C3.477 1.095 4.958 1.13 6.438 1.125 C7.22 1.128 8.002 1.13 8.809 1.133 C11.179 1.084 11.179 1.084 14 0 C14.33 0.99 14.66 1.98 15 3 C12.113 3.82 9.5 4.162 6.5 4.188 C5.747 4.202 4.994 4.216 4.219 4.23 C1.751 3.974 0.136 3.228 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#8699DE" transform="translate(237,203)"/>
<path d="M0 0 C2.571 2.245 3.493 3.975 4.375 7.25 C5.812 11.945 7.974 15.156 11 19 C10.67 19.66 10.34 20.32 10 21 C4.899 14.76 0 8.314 0 0 Z " fill="#F1F4FE" transform="translate(733,189)"/>
<path d="M0 0 C3.344 0.557 6.033 1.352 9 3 C6.605 5.899 4.688 6.502 1 7 C1.206 6.216 1.413 5.433 1.625 4.625 C1.749 3.759 1.873 2.893 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#F0F3FE" transform="translate(503,691)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2.99 0.67 3.98 0.34 5 0 C6 1 6 1 6.098 2.848 C6.065 4.898 6.033 6.949 6 9 C5.34 8.67 4.68 8.34 4 8 C4.33 7.34 4.66 6.68 5 6 C4.01 6.33 3.02 6.66 2 7 C1.34 6.67 0.68 6.34 0 6 C0 4.02 0 2.04 0 0 Z " fill="#B5BEF6" transform="translate(426,487)"/>
<path d="M0 0 C2.75 -0.398 2.75 -0.398 6 -0.375 C7.609 -0.387 7.609 -0.387 9.25 -0.398 C10.158 -0.267 11.065 -0.135 12 0 C12.66 0.99 13.32 1.98 14 3 C8.72 3 3.44 3 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#CCD6FC" transform="translate(323,388)"/>
<path d="M0 0 C4.332 2.104 6.371 4.617 8.887 8.574 C9.254 9.045 9.621 9.515 10 10 C10.66 10 11.32 10 12 10 C11.34 11.32 10.68 12.64 10 14 C9.59 13.362 9.18 12.724 8.758 12.066 C8.219 11.24 7.68 10.414 7.125 9.562 C6.324 8.327 6.324 8.327 5.508 7.066 C4.162 4.89 4.162 4.89 2 4 C1.67 4.66 1.34 5.32 1 6 C0 5 0 5 -0.062 2.438 C-0.042 1.633 -0.021 0.829 0 0 Z " fill="#8191D3" transform="translate(456,294)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-1.93 8.755 -6.154 12.463 -12 15 C-12.99 14.67 -13.98 14.34 -15 14 C-14.336 13.66 -13.672 13.319 -12.988 12.969 C-6.755 9.548 -3.102 6.433 0 0 Z " fill="#5E76C9" transform="translate(267,190)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.795 1.808 1.59 2.616 1.379 3.449 C1.109 4.518 0.84 5.586 0.562 6.688 C0.296 7.743 0.029 8.799 -0.246 9.887 C-0.899 12.583 -1.479 15.276 -2 18 C-2.99 17.34 -3.98 16.68 -5 16 C-4.545 14.27 -4.086 12.541 -3.625 10.812 C-3.37 9.85 -3.115 8.887 -2.852 7.895 C-2.052 5.178 -1.097 2.609 0 0 Z " fill="#8596D8" transform="translate(513,192)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.66 3 2.32 3 3 C-0.11 5.073 -0.891 5.274 -4.438 5.25 C-5.199 5.255 -5.961 5.26 -6.746 5.266 C-9.214 4.975 -10.858 4.23 -13 3 C-13 2.34 -13 1.68 -13 1 C-10.69 1.33 -8.38 1.66 -6 2 C-6 2.66 -6 3.32 -6 4 C-5.01 4 -4.02 4 -3 4 C-2.67 3.34 -2.34 2.68 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#919FE4" transform="translate(380,926)"/>
<path d="M0 0 C11.706 1.558 11.706 1.558 16 6 C15.67 6.66 15.34 7.32 15 8 C13.824 7.041 13.824 7.041 12.625 6.062 C8.446 3.22 3.924 3.195 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#919EE4" transform="translate(568,896)"/>
<path d="M0 0 C2.707 2.707 2.976 5.325 4 9 C4.522 10.258 5.064 11.509 5.625 12.75 C7 16 7 16 7 19 C2.464 14.151 0.217 8.537 -0.188 1.938 C-0.126 1.298 -0.064 0.659 0 0 Z " fill="#7085D1" transform="translate(537,341)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.213 4.461 0.42 5.918 -0.375 7.375 C-0.816 8.187 -1.257 8.999 -1.711 9.836 C-3 12 -3 12 -5 14 C-5.99 13.67 -6.98 13.34 -8 13 C-7.01 12.67 -6.02 12.34 -5 12 C-5.66 11.67 -6.32 11.34 -7 11 C-7 10.34 -7 9.68 -7 9 C-5.68 9 -4.36 9 -3 9 C-2.01 6.03 -1.02 3.06 0 0 Z " fill="#8093D8" transform="translate(807,186)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.336 1.592 0.672 2.183 -0.012 2.793 C-4.449 6.764 -8.858 10.72 -13 15 C-11.617 10.762 -9.689 8.335 -6.5 5.25 C-5.706 4.471 -4.912 3.693 -4.094 2.891 C-2 1 -2 1 0 0 Z " fill="#F8F8FE" transform="translate(753,117)"/>
<path d="M0 0 C6.711 0.292 11.598 3.098 17 7 C17.33 7.66 17.66 8.32 18 9 C13.64 7.652 9.687 5.951 5.625 3.875 C4.565 3.336 3.506 2.797 2.414 2.242 C1.617 1.832 0.821 1.422 0 1 C0 0.67 0 0.34 0 0 Z " fill="#F2F4FD" transform="translate(376,111)"/>
<path d="M0 0 C0.75 2.75 0.75 2.75 1 6 C-0.938 8.375 -0.938 8.375 -3 10 C-2.67 8.02 -2.34 6.04 -2 4 C-2.99 3.67 -3.98 3.34 -5 3 C-5.66 2.34 -6.32 1.68 -7 1 C-4.509 0.313 -2.621 0 0 0 Z " fill="#99A4EA" transform="translate(587,923)"/>
<path d="M0 0 C0.99 1.32 1.98 2.64 3 4 C2.67 4.66 2.34 5.32 2 6 C1.34 5.34 0.68 4.68 0 4 C-2.323 3.6 -4.657 3.26 -7 3 C-7 6.63 -7 10.26 -7 14 C-7.33 14 -7.66 14 -8 14 C-8 10.04 -8 6.08 -8 2 C-5.36 1.34 -2.72 0.68 0 0 Z " fill="#7687D8" transform="translate(616,882)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C1.66 6.33 2.32 6.66 3 7 C2.34 7 1.68 7 1 7 C1 8.65 1 10.3 1 12 C-1.31 12.33 -3.62 12.66 -6 13 C-6.66 11.68 -7.32 10.36 -8 9 C-5.36 9 -2.72 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#8191DF" transform="translate(474,520)"/>
<path d="M0 0 C5.425 0.482 5.425 0.482 7.875 2.688 C9.337 5.693 9.422 7.697 9 11 C7.5 12.875 7.5 12.875 6 14 C4.997 10.878 5.193 9.126 6 6 C5.67 5.01 5.34 4.02 5 3 C2.496 1.781 2.496 1.781 0 1 C0 0.67 0 0.34 0 0 Z " fill="#8394DC" transform="translate(602,505)"/>
<path d="M0 0 C0.866 0.464 0.866 0.464 1.75 0.938 C3.843 1.926 5.745 2.517 8 3 C8.66 2.01 9.32 1.02 10 0 C10 1.98 10 3.96 10 6 C6.7 6 3.4 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#9AA9EC" transform="translate(309,481)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C9.67 0.66 9.34 1.32 9 2 C6.36 2 3.72 2 1 2 C1 3.98 1 5.96 1 8 C0.01 7.34 -0.98 6.68 -2 6 C-1.34 4.02 -0.68 2.04 0 0 Z " fill="#90A0E4" transform="translate(764,472)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-3.332 2.629 -5.693 3.155 -8.062 3.625 C-9.353 3.885 -10.643 4.146 -11.973 4.414 C-12.972 4.607 -13.971 4.801 -15 5 C-14.67 4.01 -14.34 3.02 -14 2 C-9.25 -0.201 -5.119 -0.177 0 0 Z " fill="#EDF0FD" transform="translate(600,470)"/>
<path d="M0 0 C1.257 3.771 1.615 6.524 0.062 10.25 C-1.42 14.088 -1.662 17.922 -2 22 C-2.33 22 -2.66 22 -3 22 C-3.224 14.651 -2.979 8.103 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F6F7FE" transform="translate(390,305)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C8.452 4.537 9.235 8.181 9 13 C6.88 9.466 6.468 6.056 6 2 C4.68 2 3.36 2 2 2 C1.67 2.66 1.34 3.32 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#F0F2FD" transform="translate(694,899)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.625 3.562 2.625 3.562 3 6 C1.35 6.33 -0.3 6.66 -2 7 C-2 6.34 -2 5.68 -2 5 C-3.32 4.34 -4.64 3.68 -6 3 C-5.01 3 -4.02 3 -3 3 C-3 2.34 -3 1.68 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#D0D8FB" transform="translate(374,523)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 6.93 1 13.86 1 21 C0.67 21 0.34 21 0 21 C-0.037 20.229 -0.075 19.458 -0.113 18.664 C-0.179 17.661 -0.245 16.658 -0.312 15.625 C-0.371 14.627 -0.429 13.63 -0.488 12.602 C-0.657 11.743 -0.826 10.885 -1 10 C-1.99 9.34 -2.98 8.68 -4 8 C-4 7.34 -4 6.68 -4 6 C-3.34 6 -2.68 6 -2 6 C-1.34 4.02 -0.68 2.04 0 0 Z " fill="#7E92E4" transform="translate(718,507)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C4.25 6.875 4.25 6.875 2 8 C2 6.35 2 4.7 2 3 C1.01 3 0.02 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#E6EBFE" transform="translate(387,517)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C-0.709 5.354 -3.009 5.065 -6 5 C-6 7.97 -6 10.94 -6 14 C-6.33 14 -6.66 14 -7 14 C-7 10.37 -7 6.74 -7 3 C-5.35 2.67 -3.7 2.34 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#96A5E9" transform="translate(456,482)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 2.485 1.01 2.485 0 4 C5.61 4 11.22 4 17 4 C17 4.33 17 4.66 17 5 C10.07 5 3.14 5 -4 5 C-2.68 3.35 -1.36 1.7 0 0 Z " fill="#F4F6FF" transform="translate(537,769)"/>
<path d="M0 0 C-2.619 1.746 -4.038 2.387 -7 3 C-7.103 3.536 -7.206 4.072 -7.312 4.625 C-8.181 7.625 -9.556 10.238 -11 13 C-12 10 -12 10 -11.328 8.027 C-10.972 7.338 -10.617 6.648 -10.25 5.938 C-9.905 5.245 -9.559 4.553 -9.203 3.84 C-6.718 0.04 -4.218 -0.228 0 0 Z " fill="#F5F6FE" transform="translate(456,687)"/>
<path d="M0 0 C0.687 0.072 1.374 0.144 2.082 0.219 C2.983 0.312 3.884 0.404 4.812 0.5 C5.706 0.593 6.599 0.686 7.52 0.781 C10.083 1.044 10.083 1.044 13 1 C10.357 3.538 9.117 3.992 5.375 4.25 C2 4 2 4 0 3 C0 2.01 0 1.02 0 0 Z " fill="#94A2E2" transform="translate(325,519)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.125 6.75 2.125 6.75 1 9 C0.01 9 -0.98 9 -2 9 C-2.688 6.75 -2.688 6.75 -3 4 C-1.562 1.688 -1.562 1.688 0 0 Z " fill="#B3BCF6" transform="translate(430,519)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C3.238 4.939 2.783 8 2 12 C2 12.99 2 13.98 2 15 C1.34 15.66 0.68 16.32 0 17 C0 11.39 0 5.78 0 0 Z " fill="#7182CB" transform="translate(662,310)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C1.68 2.33 0.36 2.66 -1 3 C-0.34 3.33 0.32 3.66 1 4 C0.67 4.66 0.34 5.32 0 6 C-1.98 6 -3.96 6 -6 6 C-6.33 7.32 -6.66 8.64 -7 10 C-7.66 9.67 -8.32 9.34 -9 9 C-6.466 4.777 -4.434 2.217 0 0 Z " fill="#8A99DA" transform="translate(319,290)"/>
<path d="M0 0 C4.29 0 8.58 0 13 0 C13 0.99 13 1.98 13 3 C8.71 3 4.42 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#D5DEFC" transform="translate(776,224)"/>
<path d="M0 0 C-2.698 1.349 -4.653 1.016 -7.664 0.879 C-8.786 0.831 -9.907 0.782 -11.062 0.732 C-12.826 0.648 -12.826 0.648 -14.625 0.562 C-16.4 0.484 -16.4 0.484 -18.211 0.404 C-21.141 0.274 -24.07 0.139 -27 0 C-27 -0.33 -27 -0.66 -27 -1 C-22.876 -1.225 -18.751 -1.427 -14.625 -1.625 C-13.449 -1.689 -12.274 -1.754 -11.062 -1.82 C-9.941 -1.872 -8.82 -1.923 -7.664 -1.977 C-6.109 -2.055 -6.109 -2.055 -4.522 -2.135 C-2 -2 -2 -2 0 0 Z " fill="#A4B2EA" transform="translate(295,188)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.99 2 2.98 2 4 C0.014 3.935 -1.971 3.87 -3.957 3.805 C-6.314 3.867 -6.314 3.867 -9 6 C-8.188 3.562 -8.188 3.562 -7 1 C-4 0 -4 0 0 0 Z " fill="#99A4EA" transform="translate(468,922)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C4 3.32 4 4.64 4 6 C0.875 5.479 -2.25 4.958 -5.375 4.438 C-6.674 4.221 -6.674 4.221 -8 4 C-8 3.67 -8 3.34 -8 3 C-3.545 2.505 -3.545 2.505 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#EFF3FE" transform="translate(534,488)"/>
<path d="M0 0 C5.373 -0.44 5.373 -0.44 8 0 C10 2.5 10 2.5 11 5 C3.492 4.508 3.492 4.508 1 1.938 C0.67 1.298 0.34 0.659 0 0 Z " fill="#C5CFFB" transform="translate(368,931)"/>
<path d="M0 0 C1.667 0 3.333 0 5 0 C4.67 2.31 4.34 4.62 4 7 C3.34 7 2.68 7 2 7 C1.34 7.66 0.68 8.32 0 9 C0 6.03 0 3.06 0 0 Z " fill="#9FA9ED" transform="translate(521,913)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.492 4.318 4.803 8.033 3 12 C1.533 8.185 2.567 5.71 4 2 C2.68 2 1.36 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EFF0FD" transform="translate(738,899)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-2.728 2.959 -5.274 2.883 -9 2 C-9.99 2 -10.98 2 -12 2 C-8.16 -2 -5.052 -1.077 0 0 Z " fill="#8593E0" transform="translate(678,905)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C5.32 2.33 6.64 2.66 8 3 C5.69 3.33 3.38 3.66 1 4 C1.33 5.32 1.66 6.64 2 8 C1.34 8 0.68 8 0 8 C-0.381 5.674 -0.713 3.339 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#97A6EA" transform="translate(509,529)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.198 3.229 1.381 6.458 1.562 9.688 C1.619 10.6 1.675 11.512 1.732 12.451 C1.781 13.337 1.829 14.224 1.879 15.137 C1.926 15.948 1.973 16.76 2.022 17.596 C1.998 20.179 1.58 22.488 1 25 C0.67 25 0.34 25 0 25 C0 16.75 0 8.5 0 0 Z " fill="#AEB8F3" transform="translate(756,503)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.054 1.604 1.093 3.208 1.125 4.812 C1.148 5.706 1.171 6.599 1.195 7.52 C1 10 1 10 -1 13 C-3 11 -3 11 -3.125 8.375 C-3.084 7.591 -3.043 6.808 -3 6 C-2.34 6 -1.68 6 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#8699DD" transform="translate(597,330)"/>
<path d="M0 0 C0.598 0.227 1.196 0.454 1.812 0.688 C0.493 1.678 -0.827 2.668 -2.188 3.688 C-1.197 4.018 -0.207 4.347 0.812 4.688 C-3.938 7.688 -3.938 7.688 -6.188 7.688 C-5.82 4.971 -5.46 3.077 -3.875 0.812 C-2.188 -0.312 -2.188 -0.312 0 0 Z " fill="#EBEEFE" transform="translate(372.1875,900.3125)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C3 2.66 3 3.32 3 4 C6.505 5.963 10.134 6.959 14 8 C14 8.33 14 8.66 14 9 C10.166 9.365 8.212 9.11 4.75 7.312 C1.922 4.934 0.974 3.508 0 0 Z " fill="#7589D8" transform="translate(510,537)"/>
<path d="M0 0 C3.199 -0.089 5.927 0.089 9 1 C9 1.99 9 2.98 9 4 C6.36 4 3.72 4 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#92A2E9" transform="translate(718,483)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.319 1.959 1.319 1.959 0.625 2.938 C-1.445 6.839 -2.201 10.684 -3 15 C-3.33 15 -3.66 15 -4 15 C-4.323 9.505 -4.546 5.539 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#F3F4FD" transform="translate(582,475)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C13.35 1.65 11.7 3.3 10 5 C9.67 4.34 9.34 3.68 9 3 C7.135 2.61 7.135 2.61 5 2.5 C2.812 2.344 2.812 2.344 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#7F8BDB" transform="translate(526,936)"/>
<path d="M0 0 C-2.31 0.33 -4.62 0.66 -7 1 C-7 1.66 -7 2.32 -7 3 C-9.31 2.67 -11.62 2.34 -14 2 C-14 1.34 -14 0.68 -14 0 C-12.418 -0.196 -10.834 -0.382 -9.25 -0.562 C-8.368 -0.667 -7.487 -0.771 -6.578 -0.879 C-4.116 -0.995 -2.335 -0.736 0 0 Z " fill="#8A96E2" transform="translate(424,937)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 5.28 1.66 10.56 2 16 C0 14 0 14 -0.125 11.375 C-0.084 10.591 -0.043 9.808 0 9 C-0.66 8.67 -1.32 8.34 -2 8 C-1.34 5.36 -0.68 2.72 0 0 Z " fill="#808EDD" transform="translate(434,912)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C7.34 2.64 6.68 5.28 6 8 C5.67 8 5.34 8 5 8 C5 6.35 5 4.7 5 3 C3.02 3 1.04 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#A8B4ED" transform="translate(420,520)"/>
<path d="M0 0 C1.392 0.093 1.392 0.093 2.812 0.188 C3.142 1.178 3.473 2.168 3.812 3.188 C0.512 3.188 -2.788 3.188 -6.188 3.188 C-3.188 0.188 -3.188 0.188 0 0 Z " fill="#97A4E4" transform="translate(276.1875,519.8125)"/>
<path d="M0 0 C1 2 1 2 0.004 5.039 C-0.472 6.222 -0.947 7.406 -1.438 8.625 C-1.911 9.814 -2.384 11.002 -2.871 12.227 C-3.244 13.142 -3.616 14.057 -4 15 C-4.33 15 -4.66 15 -5 15 C-4.568 8.848 -3.346 5.181 0 0 Z " fill="#F3F4FD" transform="translate(315,136)"/>
<path d="M0 0 C3.683 0.6 7.345 1.248 11 2 C11 2.66 11 3.32 11 4 C3.512 4.368 3.512 4.368 1 2.562 C0 1 0 1 0 0 Z " fill="#BAC0F6" transform="translate(499,894)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.625 2.5 0.625 2.5 -1 4 C-1.66 4 -2.32 4 -3 4 C-3.289 4.949 -3.577 5.897 -3.875 6.875 C-5 10 -5 10 -7 12 C-6.547 7.133 -5.355 4.558 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#EDF0FE" transform="translate(634,489)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-2.823 4.423 -5.913 5.24 -10 5 C-9.34 3.68 -8.68 2.36 -8 1 C-8 1.66 -8 2.32 -8 3 C-3.668 2.14 -3.668 2.14 0 0 Z " fill="#9AA8E9" transform="translate(619,484)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.625 2.875 1.625 2.875 2 6 C1.34 6.66 0.68 7.32 0 8 C-0.33 8.99 -0.66 9.98 -1 11 C-1.33 11 -1.66 11 -2 11 C-2.66 8.36 -3.32 5.72 -4 3 C-2 2 -2 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#9BAAEA" transform="translate(303,482)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.125 5.75 1.125 5.75 0 8 C-1.65 7.67 -3.3 7.34 -5 7 C-4.01 6.67 -3.02 6.34 -2 6 C-1.328 4.064 -1.328 4.064 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#B3BEEE" transform="translate(458,471)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C9.93 2.495 9.93 2.495 17 3 C17 3.33 17 3.66 17 4 C12.05 4 7.1 4 2 4 C1.34 2.68 0.68 1.36 0 0 Z " fill="#CED8FD" transform="translate(709,384)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C11 0.99 11 1.98 11 3 C10.092 2.794 9.185 2.588 8.25 2.375 C4.89 1.987 3.759 2.196 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#8494DA" transform="translate(806,145)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C0.434 4.308 -1.683 8.062 -4 12 C-5.403 7.792 -3.906 5.883 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#F0F3FD" transform="translate(338,136)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.66 2.66 1.32 3.32 2 4 C-1.3 4 -4.6 4 -8 4 C-4.5 0 -4.5 0 0 0 Z " fill="#CBD2FA" transform="translate(376,901)"/>
<path d="M0 0 C1.65 1.32 3.3 2.64 5 4 C4.67 5.32 4.34 6.64 4 8 C2.68 7.34 1.36 6.68 0 6 C0 4.02 0 2.04 0 0 Z " fill="#D5DAFD" transform="translate(536,703)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C3 2.66 3 3.32 3 4 C3.577 4.289 4.155 4.577 4.75 4.875 C6.833 5.917 8.917 6.958 11 8 C10.01 8.33 9.02 8.66 8 9 C4.48 7.132 2.426 5.851 0.625 2.25 C0.419 1.508 0.212 0.765 0 0 Z " fill="#7489D8" transform="translate(260,513)"/>
<path d="M0 0 C-0.33 1.32 -0.66 2.64 -1 4 C-1.99 3.67 -2.98 3.34 -4 3 C-4.33 3.33 -4.66 3.66 -5 4 C-5.66 4 -6.32 4 -7 4 C-7 2.68 -7 1.36 -7 0 C-3.867 -1.044 -3.01 -0.934 0 0 Z " fill="#A6B1F1" transform="translate(771,515)"/>
<path d="M0 0 C0.901 0.027 1.802 0.054 2.73 0.082 C3.761 0.134 3.761 0.134 4.812 0.188 C5.143 1.507 5.472 2.827 5.812 4.188 C5.261 3.85 4.709 3.512 4.141 3.164 C1.309 1.976 -0.415 2.165 -3.438 2.562 C-4.33 2.673 -5.222 2.784 -6.141 2.898 C-6.816 2.994 -7.492 3.089 -8.188 3.188 C-5.678 0.01 -3.961 -0.154 0 0 Z " fill="#8596DD" transform="translate(646.1875,492.8125)"/>
<path d="M0 0 C3.048 0.327 4.005 1.005 6.188 3.25 C7.983 5.974 8.634 7.795 9 11 C6 9 6 9 5.062 6.625 C3.863 3.661 2.356 2.12 0 0 Z " fill="#EFF3FE" transform="translate(339,488)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C7.01 1.485 7.01 1.485 6 3 C3.18 3.293 3.18 3.293 -0.125 3.188 C-1.769 3.147 -1.769 3.147 -3.445 3.105 C-4.288 3.071 -5.131 3.036 -6 3 C-6 2.67 -6 2.34 -6 2 C-4.02 2 -2.04 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CBD7FB" transform="translate(565,388)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C10.67 0.99 10.34 1.98 10 3 C6.688 3.188 6.688 3.188 3 3 C2.01 2.01 1.02 1.02 0 0 Z " fill="#D4DEFD" transform="translate(435,388)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.045 3.46 0.086 4.918 -0.875 6.375 C-1.409 7.187 -1.942 7.999 -2.492 8.836 C-4 11 -4 11 -6 13 C-5.517 7.845 -2.814 4.184 0 0 Z " fill="#F6F7FD" transform="translate(397,291)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.66 3 2.32 3 3 C3.928 3.371 3.928 3.371 4.875 3.75 C7.351 5.206 7.951 6.378 9 9 C8.423 8.526 7.845 8.051 7.25 7.562 C5.074 6.052 3.574 5.46 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#94A3E1" transform="translate(530,694)"/>
<path d="M0 0 C1.392 0.093 1.392 0.093 2.812 0.188 C2.812 1.178 2.812 2.168 2.812 3.188 C-0.158 3.188 -3.128 3.188 -6.188 3.188 C-3.188 0.188 -3.188 0.188 0 0 Z " fill="#9BA9E7" transform="translate(647.1875,519.8125)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.63 1 7.26 1 11 C0.34 10.67 -0.32 10.34 -1 10 C-2.331 6.008 -1.815 3.737 0 0 Z " fill="#BEC6F6" transform="translate(669,501)"/>
<path d="M0 0 C2.64 1.98 5.28 3.96 8 6 C7.67 6.99 7.34 7.98 7 9 C6.34 8.67 5.68 8.34 5 8 C4.01 7.67 3.02 7.34 2 7 C1.375 5.125 1.375 5.125 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#8597DA" transform="translate(320,209)"/>
<path d="M0 0 C2.846 0.66 5.523 1.517 8.25 2.562 C8.956 2.832 9.663 3.101 10.391 3.379 C10.922 3.584 11.453 3.789 12 4 C11.67 4.66 11.34 5.32 11 6 C9.538 5.576 8.08 5.135 6.625 4.688 C5.813 4.444 5.001 4.2 4.164 3.949 C2 3 2 3 0 0 Z " fill="#7086D2" transform="translate(765,200)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3 6.625 3 6.625 3 10 C2.01 10 1.02 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#798ACE" transform="translate(456,143)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C4.029 2.658 4.029 2.658 7 3 C4 5 4 5 1.738 4.758 C0.938 4.549 0.137 4.34 -0.688 4.125 C-1.496 3.921 -2.304 3.718 -3.137 3.508 C-3.752 3.34 -4.366 3.173 -5 3 C-5 2.34 -5 1.68 -5 1 C-2.525 1.495 -2.525 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8698DD" transform="translate(373,106)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 2.97 2.66 5.94 3 9 C3.66 9 4.32 9 5 9 C4.67 9.66 4.34 10.32 4 11 C3.34 11 2.68 11 2 11 C2 10.34 2 9.68 2 9 C1.01 8.67 0.02 8.34 -1 8 C-0.34 7.67 0.32 7.34 1 7 C0.67 4.69 0.34 2.38 0 0 Z " fill="#9EA8EB" transform="translate(341,930)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 2.485 2.01 2.485 1 4 C-2.625 4.688 -2.625 4.688 -6 5 C-6 3.68 -6 2.36 -6 1 C-3.03 1.495 -3.03 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#95A1E6" transform="translate(456,927)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-0.639 2.247 -1.279 2.495 -1.938 2.75 C-4.187 3.816 -4.187 3.816 -4.75 6.125 C-4.832 6.744 -4.915 7.362 -5 8 C-6.461 5.353 -7 4.106 -7 1 C-2.25 0 -2.25 0 0 0 Z " fill="#EAEBFE" transform="translate(280,922)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C4.33 1.34 4.66 0.68 5 0 C6.044 3.133 5.934 3.99 5 7 C2.031 5.516 0.847 3.77 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9EA6EA" transform="translate(314,896)"/>
<path d="M0 0 C3.3 0 6.6 0 10 0 C9.01 0.495 9.01 0.495 8 1 C8 1.66 8 2.32 8 3 C5.69 3 3.38 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#A5ADE9" transform="translate(669,896)"/>
<path d="M0 0 C2.312 1.5 2.312 1.5 4 3 C2.542 3.054 1.084 3.093 -0.375 3.125 C-1.187 3.148 -1.999 3.171 -2.836 3.195 C-5 3 -5 3 -7 1 C-4.436 -0.282 -2.848 -0.271 0 0 Z " fill="#9FAAE9" transform="translate(455,896)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-3.285 2.414 -3.285 2.414 -6.062 2.625 C-6.982 2.7 -7.901 2.775 -8.848 2.852 C-9.558 2.901 -10.268 2.95 -11 3 C-7.684 -0.316 -4.508 -1.67 0 0 Z " fill="#9AA5E6" transform="translate(381,897)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C2.358 4.021 0.691 6.02 -1 8 C-1.33 8 -1.66 8 -2 8 C-2 5.69 -2 3.38 -2 1 C-1.01 1.495 -1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#7689D7" transform="translate(320,486)"/>
<path d="M0 0 C2 1 2 1 3 3 C2.237 3.454 1.474 3.908 0.688 4.375 C-1.236 5.538 -3.129 6.753 -5 8 C-5.33 7.34 -5.66 6.68 -6 6 C-5.34 6 -4.68 6 -4 6 C-3.711 5.381 -3.423 4.762 -3.125 4.125 C-2 2 -2 2 0 0 Z " fill="#8596DD" transform="translate(483,371)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C-1.383 4.128 -2.768 3.818 -6.188 3.062 C-7.089 2.868 -7.99 2.673 -8.918 2.473 C-9.949 2.239 -9.949 2.239 -11 2 C-11 1.67 -11 1.34 -11 1 C-9.542 0.973 -8.083 0.954 -6.625 0.938 C-5.813 0.926 -5.001 0.914 -4.164 0.902 C-2 1 -2 1 0 2 C0 1.34 0 0.68 0 0 Z " fill="#879ADE" transform="translate(583,268)"/>
<path d="M0 0 C4.254 2.127 4.824 3.044 7 7 C7.66 7.66 8.32 8.32 9 9 C7.68 9 6.36 9 5 9 C3.35 6.03 1.7 3.06 0 0 Z " fill="#F3F5FD" transform="translate(263,136)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.601 4.002 2.293 7.046 1 11 C0.67 11 0.34 11 0 11 C0 7.37 0 3.74 0 0 Z " fill="#A4AFED" transform="translate(392,912)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C4.069 2.643 4.069 2.643 6 3 C6 3.33 6 3.66 6 4 C1.545 4.495 1.545 4.495 -3 5 C-1 1 -1 1 0 0 Z " fill="#EAEEFD" transform="translate(453,671)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 3.31 1.68 5.62 1 8 C0.34 8 -0.32 8 -1 8 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#8596DF" transform="translate(727,486)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-3.395 7.004 -3.395 7.004 -5 8 C-5.99 7.67 -6.98 7.34 -8 7 C-2.25 2 -2.25 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#778CD4" transform="translate(380,196)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 5.95 2 10.9 2 16 C1.67 16 1.34 16 1 16 C0.83 14.271 0.664 12.542 0.5 10.812 C0.407 9.85 0.314 8.887 0.219 7.895 C0.019 5.248 -0.039 2.652 0 0 Z " fill="#C9D2FC" transform="translate(304,153)"/>
<path d="M0 0 C-0.33 0.99 -0.66 1.98 -1 3 C-0.67 3.99 -0.34 4.98 0 6 C-3.736 4.755 -4.188 3.366 -6 0 C-2.25 -1.125 -2.25 -1.125 0 0 Z " fill="#A7AFF0" transform="translate(288,923)"/>
<path d="M0 0 C0.808 0.111 1.616 0.222 2.449 0.336 C3.372 0.479 3.372 0.479 4.312 0.625 C2.174 2.764 1.18 3.052 -1.688 3.625 C-1.688 2.965 -1.688 2.305 -1.688 1.625 C-3.668 1.625 -5.648 1.625 -7.688 1.625 C-4.334 -0.611 -3.78 -0.548 0 0 Z " fill="#B3BAF5" transform="translate(521.6875,880.375)"/>
<path d="M0 0 C4.29 0 8.58 0 13 0 C9.017 2.655 9.017 2.655 6 3 C2.75 2.062 2.75 2.062 0 1 C0 0.67 0 0.34 0 0 Z " fill="#6F82D6" transform="translate(591,530)"/>
<path d="M0 0 C2 3 2 3 2 5 C0.02 5.99 -1.96 6.98 -4 8 C-4.99 7.01 -5.98 6.02 -7 5 C-5.35 5 -3.7 5 -2 5 C-1.34 3.35 -0.68 1.7 0 0 Z " fill="#9EABE9" transform="translate(384,515)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2.125 8.75 2.125 8.75 1 11 C0.34 11 -0.32 11 -1 11 C-0.67 7.37 -0.34 3.74 0 0 Z " fill="#B9C2F7" transform="translate(397,499)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.99 9 1.98 9 3 C7.35 3 5.7 3 4 3 C4 2.34 4 1.68 4 1 C2.68 1.33 1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8A9ADF" transform="translate(415,493)"/>
<path d="M0 0 C2 2 2 2 3 4.062 C3.846 6.169 3.846 6.169 6 7 C5.67 7.66 5.34 8.32 5 9 C2 8 2 8 0.25 5.625 C-1 3 -1 3 0 0 Z " fill="#92A3E7" transform="translate(750,487)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.052 2.752 -1.052 2.752 -2.125 2.5 C-5.139 1.976 -7.947 1.929 -11 2 C-7.375 -1.625 -4.817 -1.019 0 0 Z " fill="#BDC7F4" transform="translate(573,368)"/>
<path d="M0 0 C3 4 3 4 2.812 6.625 C2 9 2 9 0 11 C0 7.37 0 3.74 0 0 Z " fill="#A0AEEA" transform="translate(834,165)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.96 1 7.92 1 12 C-1.425 9.575 -1.686 8.379 -2 5 C-1.062 2.188 -1.062 2.188 0 0 Z " fill="#90A3E2" transform="translate(390,162)"/>
<path d="M0 0 C6.27 0 12.54 0 19 0 C19 0.33 19 0.66 19 1 C12.73 1 6.46 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C1C8F1" transform="translate(805,160)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.044 4.133 3.934 4.99 3 8 C2.34 8 1.68 8 1 8 C0.67 5.36 0.34 2.72 0 0 Z " fill="#8797DE" transform="translate(728,919)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C7.02 0.99 7.02 0.99 5 2 C3.649 2.976 2.311 3.97 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#B3BAF1" transform="translate(486,906)"/>
<path d="M0 0 C3 1 3 1 4.75 3.875 C6 7 6 7 5 10 C4.732 9.423 4.464 8.845 4.188 8.25 C3.049 5.943 3.049 5.943 1.375 3.875 C0 2 0 2 0 0 Z " fill="#8391DE" transform="translate(543,901)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C-0.65 4.33 -2.3 4.66 -4 5 C-4.66 3.68 -5.32 2.36 -6 1 C-3.924 0.447 -2.156 0 0 0 Z " fill="#B5BEF3" transform="translate(539,527)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2.414 3.066 2.414 3.066 2.625 5.562 C2.7 6.389 2.775 7.215 2.852 8.066 C2.925 9.024 2.925 9.024 3 10 C2.01 9.67 1.02 9.34 0 9 C0 6.03 0 3.06 0 0 Z " fill="#EFF2FE" transform="translate(392,497)"/>
<path d="M0 0 C-3.235 2.489 -6.593 4.754 -10 7 C-7.655 1.769 -5.983 -0.485 0 0 Z " fill="#F0F3FE" transform="translate(485,487)"/>
<path d="M0 0 C-3.96 1.98 -3.96 1.98 -8 4 C-8 3.01 -8 2.02 -8 1 C-4.594 -1.068 -4.188 -1.047 0 0 Z " fill="#8394DE" transform="translate(523,486)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.33 2.65 3.66 4.3 4 6 C3.01 6 2.02 6 1 6 C0.447 3.924 0 2.156 0 0 Z " fill="#8FA0E1" transform="translate(394,367)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.99 3 2.98 3 4 C3.33 4.66 3.66 5.32 4 6 C2.68 6.33 1.36 6.66 0 7 C0 4.69 0 2.38 0 0 Z " fill="#929FE3" transform="translate(365,931)"/>
<path d="M0 0 C2.544 1.842 4.833 3.725 7 6 C6.01 6.33 5.02 6.66 4 7 C2 5.188 2 5.188 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8493DE" transform="translate(562,924)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2.33 2.32 2.66 3 3 C-0.073 3.911 -2.801 4.089 -6 4 C-4.608 1.216 -2.844 1.021 0 0 Z " fill="#8494E1" transform="translate(772,527)"/>
<path d="M0 0 C0.929 3.434 1.233 6.428 1 10 C0.34 10.66 -0.32 11.32 -1 12 C-2.456 7.479 -1.27 4.484 0 0 Z " fill="#7387D3" transform="translate(412,310)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.34 4.32 0.68 5.64 0 7 C-0.66 7 -1.32 7 -2 7 C-2.125 4.625 -2.125 4.625 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#ADBAE4" transform="translate(317,129)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C-0.195 13.063 -0.381 11.126 -0.562 9.188 C-0.667 8.109 -0.771 7.03 -0.879 5.918 C-1 3 -1 3 0 0 Z " fill="#B6C1F7" transform="translate(307,488)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.62 1 9.24 1 14 C0.67 14 0.34 14 0 14 C-1.333 4.59 -1.333 4.59 0 0 Z " fill="#B5C0F7" transform="translate(307,504)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.95 1 9.9 1 15 C0.67 15 0.34 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#BCC4F8" transform="translate(550,494)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.66 0.99 4.32 1.98 5 3 C3.68 3.33 2.36 3.66 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B4BAF5" transform="translate(372,907)"/>
<path d="M0 0 C4.75 0.75 4.75 0.75 7 3 C5.68 3.33 4.36 3.66 3 4 C2.01 2.68 1.02 1.36 0 0 Z " fill="#BFC8FA" transform="translate(477,796)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-3.3 1 -6.6 1 -10 1 C-5.645 -1.178 -4.534 -1.03 0 0 Z " fill="#BCC5F8" transform="translate(512,642)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.977 6.055 1.977 6.055 2 8 C1.34 8.66 0.68 9.32 0 10 C0 6.7 0 3.4 0 0 Z " fill="#B9C2F8" transform="translate(550,515)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 3.3 2 6.6 2 10 C1.67 10 1.34 10 1 10 C0.67 6.7 0.34 3.4 0 0 Z " fill="#B1BCF4" transform="translate(760,480)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C0.35 3 -1.3 3 -3 3 C-2 1 -2 1 0 0 Z " fill="#BAC3F9" transform="translate(606,509)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C-0.65 3 -2.3 3 -4 3 C-3 1 -3 1 0 0 Z " fill="#C8D1FA" transform="translate(580,265)"/>
<path d="M0 0 C2.64 0 5.28 0 8 0 C6.25 1.125 6.25 1.125 4 2 C1.75 1.125 1.75 1.125 0 0 Z " fill="#A7ADEF" transform="translate(447,940)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4.33 2.32 4.66 3.64 5 5 C3.35 3.35 1.7 1.7 0 0 Z " fill="#B6BDF5" transform="translate(533,908)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C6.67 0.66 6.34 1.32 6 2 C3.03 1.505 3.03 1.505 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B6C2F7" transform="translate(247,229)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 0.99 5.34 1.98 5 3 C4.67 2.34 4.34 1.68 4 1 C1.975 0.348 1.975 0.348 0 0 Z " fill="#B1BEF6" transform="translate(231,229)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.97 1 5.94 1 9 C0.67 9 0.34 9 0 9 C0 6.03 0 3.06 0 0 Z " fill="#CAD2FB" transform="translate(305,170)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B9C0EF" transform="translate(825,160)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9 0.33 9 0.66 9 1 C6.03 1 3.06 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C5CAF4" transform="translate(791,160)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C0.02 2.99 0.02 2.99 -2 4 C-1 1 -1 1 0 0 Z " fill="#B7BEF4" transform="translate(527,908)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 1.67 2.98 1.34 4 1 C4 1.66 4 2.32 4 3 C2.68 3.33 1.36 3.66 0 4 C0 2.68 0 1.36 0 0 Z " fill="#DADFFE" transform="translate(484,724)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.66 5 1.32 5 2 C3.35 1.67 1.7 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#D3D9FB" transform="translate(480,677)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 2.32 1.02 3.64 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BCC4F7" transform="translate(477,499)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.66 2 2.32 2 3 C2.66 3.33 3.32 3.66 4 4 C3.01 4 2.02 4 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#BFC7F7" transform="translate(588,485)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#B1B7F4" transform="translate(509,929)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 1.98 1.66 3.96 2 6 C1.34 6 0.68 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#B1B9F2" transform="translate(550,910)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 2.65 2 4.3 2 6 C0 2.25 0 2.25 0 0 Z " fill="#B4BBF6" transform="translate(588,907)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D0D7FC" transform="translate(547,736)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.98 2 -2.96 2 -5 2 C-5 1.67 -5 1.34 -5 1 C-3.35 0.67 -1.7 0.34 0 0 Z " fill="#BFC8F9" transform="translate(522,642)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 5 -0.32 5 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#B5BFF6" transform="translate(549,531)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.67 0.66 4.34 1.32 4 2 C2.68 1.34 1.36 0.68 0 0 Z " fill="#AAB4F1" transform="translate(691,532)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#ACB8F3" transform="translate(307,528)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#BCC5F8" transform="translate(537,504)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#BAC4F8" transform="translate(550,486)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 1.32 2.66 2.64 3 4 C2.34 4 1.68 4 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#C0CAFA" transform="translate(283,313)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.67 0.99 3.34 1.98 3 3 C2.01 2.01 1.02 1.02 0 0 Z " fill="#B5C2F8" transform="translate(776,229)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 1.32 2.66 2.64 3 4 C2.34 4 1.68 4 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#C9D0FA" transform="translate(476,212)"/>
<path d="M0 0 C2 2 2 2 2 5 C1.34 4.67 0.68 4.34 0 4 C0 2.68 0 1.36 0 0 Z " fill="#D4D9FC" transform="translate(496,167)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#DCDEFB" transform="translate(572,153)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.01 0.66 3.02 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#A5AAEE" transform="translate(409,940)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.01 0.66 3.02 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#A5AAEC" transform="translate(704,939)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.01 4.495 0.01 4.495 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#ADB4F2" transform="translate(717,923)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#B6BEF4" transform="translate(497,923)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#B1B8F4" transform="translate(652,916)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 1.66 2 2.32 2 3 C1.01 2.67 0.02 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#B9C1F7" transform="translate(457,909)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.68 1.33 0.36 1.66 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#A9B1EF" transform="translate(291,880)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 3.485 1.01 3.485 0 5 C0 3.35 0 1.7 0 0 Z " fill="#CBD2FB" transform="translate(588,723)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#CAD0FA" transform="translate(582,691)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.32 2.67 -1.64 2.34 -3 2 C-2.01 2 -1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D3D8FC" transform="translate(484,680)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.68 1.33 0.36 1.66 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C2CBF9" transform="translate(476,648)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.01 0.66 3.02 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#A9B4F2" transform="translate(747,531)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.99 2 1.98 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BAC4F9" transform="translate(337,501)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.99 2.34 1.98 2 3 C1.34 2.01 0.68 1.02 0 0 Z " fill="#B5C0F7" transform="translate(509,489)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C1.35 2 -0.3 2 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#ADB9F3" transform="translate(743,482)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.67 2.99 1.34 3.98 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#D0D6FB" transform="translate(373,292)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.66 0.02 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#C5CEFA" transform="translate(314,204)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.67 2.99 1.34 3.98 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#C7CFFA" transform="translate(730,193)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C0.68 2.33 -0.64 2.66 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#D1D6F9" transform="translate(349,133)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#AAAFF0" transform="translate(519,939)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A8AFF0" transform="translate(442,939)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#AEB4F2" transform="translate(634,930)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B2B9F4" transform="translate(550,926)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BAC1F8" transform="translate(538,914)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.34 -0.32 2.68 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#B0B7F3" transform="translate(730,913)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.99 2.34 1.98 2 3 C2 2.34 2 1.68 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#B5BBF5" transform="translate(569,907)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 1.33 2.02 1.66 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B6BCF6" transform="translate(528,907)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.66 2 2.32 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#A8B0EF" transform="translate(272,903)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.33 0.02 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#BABFF8" transform="translate(497,892)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#B3BAF6" transform="translate(524,884)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C2.68 2 1.36 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B2BAF5" transform="translate(501,880)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C9D0FD" transform="translate(528,768)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D2D8FC" transform="translate(486,739)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B3BCF6" transform="translate(507,535)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#ACB6F2" transform="translate(379,532)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 1.32 1.34 2.64 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#B7C1F8" transform="translate(396,513)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#B3BEF6" transform="translate(756,497)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BBC4F5" transform="translate(643,495)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B6C0F5" transform="translate(716,492)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B6C0F7" transform="translate(638,483)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#B4C0F7" transform="translate(636,387)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C4CDF9" transform="translate(467,340)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#CDD5F9" transform="translate(623,314)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 1.33 2.02 1.66 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D2D7FB" transform="translate(427,294)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C5CDF8" transform="translate(312,267)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C8CFF7" transform="translate(435,266)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.32 1.67 -1.64 1.34 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#C8D0F9" transform="translate(564,265)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B7C2F7" transform="translate(785,229)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C3CCF8" transform="translate(784,202)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2 0.02 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#C1CCF7" transform="translate(253,200)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#B3C0F5" transform="translate(187,176)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CAD1FA" transform="translate(757,176)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D5DAFD" transform="translate(498,164)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#CAD1FA" transform="translate(759,147)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#C4CCF9" transform="translate(265,144)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CDD6FB" transform="translate(368,133)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B6C1F3" transform="translate(822,119)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BBC5F4" transform="translate(534,107)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2.33 2.32 2.66 3 3 C2.01 3 1.02 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BAC3F5" transform="translate(464,105)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A8ABF1" transform="translate(483,941)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A6ACF0" transform="translate(371,939)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#ABB1F0" transform="translate(722,931)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#B2BAF3" transform="translate(497,931)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#A4ABEC" transform="translate(272,930)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B0B7EF" transform="translate(670,927)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B0B6F4" transform="translate(629,922)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#A5ABEE" transform="translate(741,919)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B3BAF5" transform="translate(299,918)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B9C0F8" transform="translate(444,915)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#AFB6F2" transform="translate(693,908)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B9C0F7" transform="translate(549,904)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B8BCF3" transform="translate(491,896)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B4BAF3" transform="translate(444,896)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B4BBF4" transform="translate(299,894)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BCC4F8" transform="translate(548,794)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CBD2FB" transform="translate(436,737)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D4DAFD" transform="translate(485,736)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C8CFFB" transform="translate(435,731)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C8CEFA" transform="translate(589,719)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D7DBFD" transform="translate(501,702)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D2D8FC" transform="translate(533,700)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#D2D8FB" transform="translate(495,683)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#D0D7FB" transform="translate(486,678)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CFD4F9" transform="translate(457,677)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BFC8F9" transform="translate(524,642)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BFC8F9" transform="translate(490,641)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#ADB7F3" transform="translate(377,532)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#AAB4F2" transform="translate(335,532)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#A6B1F1" transform="translate(268,532)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B4BCF6" transform="translate(432,531)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BBC4F7" transform="translate(512,525)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BEC7F9" transform="translate(637,512)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BBC4F8" transform="translate(668,513)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BBC4F7" transform="translate(550,510)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BEC6F8" transform="translate(519,506)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BCC3F8" transform="translate(716,506)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BEC3F6" transform="translate(483,495)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B6C0F4" transform="translate(330,495)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#B9C2F6" transform="translate(502,488)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B6C0F7" transform="translate(484,481)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B9C2F9" transform="translate(460,477)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#B7C0F5" transform="translate(404,476)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#B5C0F6" transform="translate(605,468)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B8C1F6" transform="translate(600,467)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B2BFF6" transform="translate(307,390)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BCC7FA" transform="translate(400,377)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BDC7F8" transform="translate(611,372)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C1CAFA" transform="translate(517,366)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C2CAF8" transform="translate(690,362)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C8D2FC" transform="translate(385,349)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CBD3FB" transform="translate(540,344)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#C8D1F9" transform="translate(327,341)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D0D5FC" transform="translate(373,336)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C8D0FB" transform="translate(468,332)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CDD4FD" transform="translate(412,329)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D4DAFC" transform="translate(667,323)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D0D7FC" transform="translate(593,316)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CFD6FC" transform="translate(592,312)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CCD3FB" transform="translate(465,312)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CFD5FA" transform="translate(322,313)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#BFC7F8" transform="translate(283,304)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CBD3FC" transform="translate(437,294)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#CED4FD" transform="translate(434,292)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D2D9FB" transform="translate(606,281)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#C6CEF8" transform="translate(329,266)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#B2BDF6" transform="translate(222,226)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#D4D9FB" transform="translate(572,204)"/>
<path d="M0 0 C1.485 0.99 1.485 0.99 3 2 C2.01 2 1.02 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C5CFFA" transform="translate(778,202)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CCD4FB" transform="translate(341,192)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CFD7FC" transform="translate(335,179)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#D8DDFD" transform="translate(685,175)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CCD4FB" transform="translate(334,166)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#DEE0FC" transform="translate(572,146)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#C8D0F9" transform="translate(223,141)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CCD4FA" transform="translate(793,133)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#DDE0FC" transform="translate(572,132)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#CED4FB" transform="translate(358,131)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#CED5F9" transform="translate(406,128)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B9C3F6" transform="translate(196,129)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BEC6F5" transform="translate(377,106)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#B7C0F3" transform="translate(596,106)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#B8C2F3" transform="translate(594,106)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B9C2F3" transform="translate(787,105)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#BBC4F4" transform="translate(770,105)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A4A8ED" transform="translate(531,940)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A2A7EB" transform="translate(329,940)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A2A9EE" transform="translate(293,940)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A1A7ED" transform="translate(289,940)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A7ACEE" transform="translate(646,939)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#A7AFEF" transform="translate(502,939)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A9AEF1" transform="translate(406,939)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A4ACF0" transform="translate(337,938)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#ADB4F3" transform="translate(657,933)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B1B7F3" transform="translate(548,929)"/>
<path d="" fill="#ACB6F1" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B1B9F7" transform="translate(456,925)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B6BBF7" transform="translate(415,925)"/>
<path d="" fill="#B2B8F5" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B6BBF7" transform="translate(574,922)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B4BBF7" transform="translate(459,921)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#AFB9F2" transform="translate(695,918)"/>
<path d="" fill="#BCC3FA" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#AFB5F4" transform="translate(290,915)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B9BFF4" transform="translate(422,913)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B7BDF4" transform="translate(379,908)"/>
<path d="" fill="#B9C0F6" transform="translate(0,0)"/>
<path d="" fill="#B7BDF7" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B4BBF6" transform="translate(427,900)"/>
<path d="" fill="#ACB1F3" transform="translate(0,0)"/>
<path d="" fill="#B2B7F5" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B4BAF4" transform="translate(440,898)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B5BBF7" transform="translate(424,897)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A8AEF0" transform="translate(725,896)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B4BBF3" transform="translate(537,896)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B5BCF4" transform="translate(296,893)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A6ADEE" transform="translate(285,881)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#A9B1F1" transform="translate(354,880)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BFC6F9" transform="translate(518,803)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BEC8F9" transform="translate(484,799)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BFC7FA" transform="translate(474,795)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C4CBF8" transform="translate(539,785)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C2C8F7" transform="translate(565,774)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D7DCFC" transform="translate(483,738)"/>
<path d="" fill="#D1D8FD" transform="translate(0,0)"/>
<path d="" fill="#C6CDF9" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D9DDFE" transform="translate(485,722)"/>
<path d="" fill="#D5DCFE" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C9D1FB" transform="translate(436,713)"/>
<path d="" fill="#CAD0FA" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C5CDF9" transform="translate(440,691)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C8CFFA" transform="translate(581,688)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D1D7FB" transform="translate(514,686)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C5CCFA" transform="translate(577,681)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CFD5FA" transform="translate(492,679)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C5CEFA" transform="translate(572,673)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B0B9F4" transform="translate(524,548)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B1BAF5" transform="translate(534,547)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B0BBF5" transform="translate(518,547)"/>
<path d="" fill="#AFB9F5" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#AFB9F5" transform="translate(601,532)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B1BAF7" transform="translate(469,533)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#AFB8F3" transform="translate(644,532)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B0BBF6" transform="translate(478,531)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#AEB7F3" transform="translate(331,532)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#ABB3F3" transform="translate(281,532)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#AFB8F3" transform="translate(277,532)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#ADB6F2" transform="translate(655,530)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B2BBF5" transform="translate(446,530)"/>
<path d="" fill="#B0B8F4" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B2BDF4" transform="translate(615,526)"/>
<path d="" fill="#B0BAF3" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B9C2F8" transform="translate(667,516)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B7C0F8" transform="translate(652,516)"/>
<path d="" fill="#BCC5FA" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B9C2FA" transform="translate(534,512)"/>
<path d="" fill="#BAC4FA" transform="translate(0,0)"/>
<path d="" fill="#B6C1F8" transform="translate(0,0)"/>
<path d="" fill="#BDC6F8" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BAC4F7" transform="translate(352,499)"/>
<path d="" fill="#B4BFF9" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B2BBF6" transform="translate(731,497)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C1C7F7" transform="translate(372,495)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B7C0F8" transform="translate(260,494)"/>
<path d="" fill="#B8C2F8" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B4BFF7" transform="translate(653,484)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B6C1F7" transform="translate(533,484)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B3BEF7" transform="translate(696,483)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B5BFF7" transform="translate(520,483)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B5BDF6" transform="translate(422,483)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B6BFF8" transform="translate(368,483)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B5BFF7" transform="translate(326,483)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B7C0F8" transform="translate(529,482)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B9C1F8" transform="translate(280,481)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B4BFF4" transform="translate(293,472)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B1BBF6" transform="translate(278,467)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B5C0F8" transform="translate(453,464)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BAC6FA" transform="translate(548,391)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B6C0F7" transform="translate(705,387)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BEC7F8" transform="translate(371,374)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C3CAF9" transform="translate(520,370)"/>
<path d="" fill="#C1CAF9" transform="translate(0,0)"/>
<path d="" fill="#C3CEFB" transform="translate(0,0)"/>
<path d="" fill="#C4CEFC" transform="translate(0,0)"/>
<path d="" fill="#C6CFFB" transform="translate(0,0)"/>
<path d="" fill="#C6CFFA" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C6CEF9" transform="translate(331,342)"/>
<path d="" fill="#CAD1FC" transform="translate(0,0)"/>
<path d="" fill="#D0D7FC" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D2D8FC" transform="translate(354,321)"/>
<path d="" fill="#CCD4FB" transform="translate(0,0)"/>
<path d="" fill="#CFD5F8" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CFD4FA" transform="translate(620,305)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D2D8FB" transform="translate(345,299)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D2D9FD" transform="translate(515,296)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D1D8FA" transform="translate(554,294)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#D0D5FB" transform="translate(569,292)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#D1D6FC" transform="translate(597,273)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C4CCF9" transform="translate(301,272)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C6CFF8" transform="translate(647,267)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CCD4FA" transform="translate(558,266)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CED5FB" transform="translate(450,267)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C8D0FA" transform="translate(340,267)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BDC8F8" transform="translate(352,229)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C0C7FA" transform="translate(599,227)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B2BFF6" transform="translate(811,221)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#CACFFA" transform="translate(680,219)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C1CBF7" transform="translate(320,212)"/>
<path d="" fill="#CFD8FD" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C8D2FA" transform="translate(736,207)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#CED5FC" transform="translate(474,205)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BBC6F7" transform="translate(240,203)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C2CAF5" transform="translate(789,202)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BFCAF8" transform="translate(234,202)"/>
<path d="" fill="#C2CCF9" transform="translate(0,0)"/>
<path d="" fill="#CBD2FB" transform="translate(0,0)"/>
<path d="" fill="#DADDFC" transform="translate(0,0)"/>
<path d="" fill="#C6D0FB" transform="translate(0,0)"/>
<path d="" fill="#C6CEF7" transform="translate(0,0)"/>
<path d="" fill="#DADEFD" transform="translate(0,0)"/>
<path d="" fill="#D4DAFD" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C0C9F7" transform="translate(284,185)"/>
<path d="" fill="#CCD3FB" transform="translate(0,0)"/>
<path d="" fill="#B3BEF3" transform="translate(0,0)"/>
<path d="" fill="#DDE2FF" transform="translate(0,0)"/>
<path d="" fill="#D1D6FB" transform="translate(0,0)"/>
<path d="" fill="#D3D9FB" transform="translate(0,0)"/>
<path d="" fill="#DADFFD" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B3BEF6" transform="translate(186,152)"/>
<path d="" fill="#CBD3FC" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BBC2F4" transform="translate(826,147)"/>
<path d="" fill="#D2D9FE" transform="translate(0,0)"/>
<path d="" fill="#C5CDF9" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C7CFFA" transform="translate(801,140)"/>
<path d="" fill="#C4CCF8" transform="translate(0,0)"/>
<path d="" fill="#D3D8FA" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#CBD2FA" transform="translate(773,133)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C9D1FA" transform="translate(782,131)"/>
<path d="" fill="#E0E2FE" transform="translate(0,0)"/>
<path d="" fill="#D9DDFC" transform="translate(0,0)"/>
<path d="" fill="#CACFF8" transform="translate(0,0)"/>
<path d="" fill="#D1D7FC" transform="translate(0,0)"/>
<path d="" fill="#D5DAFC" transform="translate(0,0)"/>
<path d="" fill="#C6CFF5" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B6C1F2" transform="translate(704,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BBC4F4" transform="translate(696,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B6C1F3" transform="translate(640,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BBC4F3" transform="translate(632,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B7C0F3" transform="translate(574,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B9C3F4" transform="translate(544,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BEC7F4" transform="translate(528,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BCC6F3" transform="translate(520,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BDC6F4" transform="translate(440,107)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BBC5F4" transform="translate(773,105)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BCC7F2" transform="translate(245,105)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B8C3F4" transform="translate(236,104)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A7ACEF" transform="translate(524,943)"/>
<path d="" fill="#A6AAF0" transform="translate(0,0)"/>
<path d="" fill="#A9ADF2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8ADEF" transform="translate(415,941)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A4A7EB" transform="translate(669,940)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A6ACEE" transform="translate(569,940)"/>
<path d="" fill="#A9AEF0" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5A9EE" transform="translate(488,940)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A7A9ED" transform="translate(710,939)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACB1F2" transform="translate(614,939)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AAB0F0" transform="translate(499,939)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A6ABEF" transform="translate(494,939)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AAAEF2" transform="translate(422,939)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8AEF0" transform="translate(382,939)"/>
<path d="" fill="#A7ACF0" transform="translate(0,0)"/>
<path d="" fill="#A5ADEF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5ADEA" transform="translate(685,935)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACB3EE" transform="translate(388,935)"/>
<path d="" fill="#ADB2F4" transform="translate(0,0)"/>
<path d="" fill="#B0B6F3" transform="translate(0,0)"/>
<path d="" fill="#AEB3F3" transform="translate(0,0)"/>
<path d="" fill="#ABB1F3" transform="translate(0,0)"/>
<path d="" fill="#9FA5EB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACB2F1" transform="translate(588,929)"/>
<path d="" fill="#B2B9F3" transform="translate(0,0)"/>
<path d="" fill="#AFB6F3" transform="translate(0,0)"/>
<path d="" fill="#AFB4F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1B8F4" transform="translate(564,924)"/>
<path d="" fill="#B0B7F3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEB4F5" transform="translate(300,924)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2B9F6" transform="translate(566,923)"/>
<path d="" fill="#B5BBF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7BCF7" transform="translate(579,922)"/>
<path d="" fill="#ADB4F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3B7F4" transform="translate(627,921)"/>
<path d="" fill="#B4BBF4" transform="translate(0,0)"/>
<path d="" fill="#B7BDF5" transform="translate(0,0)"/>
<path d="" fill="#B8BEF5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5BAF4" transform="translate(466,921)"/>
<path d="" fill="#B5BBF5" transform="translate(0,0)"/>
<path d="" fill="#ADB6F3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5BAF6" transform="translate(297,917)"/>
<path d="" fill="#B6BEF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B0B5F3" transform="translate(292,916)"/>
<path d="" fill="#B7BCF4" transform="translate(0,0)"/>
<path d="" fill="#B5BDF6" transform="translate(0,0)"/>
<path d="" fill="#B4B9F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2B9F5" transform="translate(672,911)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3BAF6" transform="translate(669,911)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEB5F3" transform="translate(278,911)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BCF6" transform="translate(652,910)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3BBF4" transform="translate(369,909)"/>
<path d="" fill="#B1B7F3" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B0B5F4" transform="translate(676,908)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2B9F2" transform="translate(668,908)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5BBF5" transform="translate(448,908)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9BFF7" transform="translate(454,907)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BDF6" transform="translate(450,907)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC1F7" transform="translate(377,907)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7BDF5" transform="translate(300,905)"/>
<path d="" fill="#ACB1F1" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AFB6F3" transform="translate(315,901)"/>
<path d="" fill="#B1B7F4" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B0B7F4" transform="translate(336,899)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9AFF0" transform="translate(726,898)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3BBF5" transform="translate(458,896)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6BCF1" transform="translate(381,896)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3B8F1" transform="translate(371,896)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2B8F5" transform="translate(291,893)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BFF6" transform="translate(508,892)"/>
<path d="" fill="#B8BDF5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6BCF5" transform="translate(508,884)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3BBF6" transform="translate(506,881)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9B0EE" transform="translate(362,881)"/>
<path d="" fill="#C3CBFC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C1C7FA" transform="translate(503,803)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2C9F9" transform="translate(472,793)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9D1FB" transform="translate(508,777)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9D2FC" transform="translate(506,776)"/>
<path d="" fill="#C9CDFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8CFFC" transform="translate(530,769)"/>
<path d="" fill="#CDD4FD" transform="translate(0,0)"/>
<path d="" fill="#CED5FB" transform="translate(0,0)"/>
<path d="" fill="#C3CBFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4CAF9" transform="translate(580,759)"/>
<path d="" fill="#C3CBFB" transform="translate(0,0)"/>
<path d="" fill="#D5DAFD" transform="translate(0,0)"/>
<path d="" fill="#C9D0FB" transform="translate(0,0)"/>
<path d="" fill="#D9DEFD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6D0FC" transform="translate(432,725)"/>
<path d="" fill="#CCD1FA" transform="translate(0,0)"/>
<path d="" fill="#CAD0FB" transform="translate(0,0)"/>
<path d="" fill="#D5DBF9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8D0FB" transform="translate(448,679)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CED3FB" transform="translate(452,678)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C9F8" transform="translate(448,667)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0CAFB" transform="translate(544,651)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C9F7" transform="translate(542,650)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C7F9" transform="translate(470,650)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFC8F8" transform="translate(525,644)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AFB9F6" transform="translate(532,550)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B0B9F3" transform="translate(529,548)"/>
<path d="" fill="#B1BAF0" transform="translate(0,0)"/>
<path d="" fill="#B4BDF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A2AFF0" transform="translate(757,535)"/>
<path d="" fill="#AFBAF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ABB4F5" transform="translate(684,534)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AAB5F3" transform="translate(688,533)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AFB9F5" transform="translate(604,532)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACB5F4" transform="translate(418,532)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1BAF6" transform="translate(274,532)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A2ADF0" transform="translate(763,531)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5BDF7" transform="translate(503,531)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B4BEF5" transform="translate(438,531)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ADB8F5" transform="translate(384,531)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEB9F5" transform="translate(364,530)"/>
<path d="" fill="#B4BEF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2BCF7" transform="translate(356,526)"/>
<path d="" fill="#A3AFEE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7C1F5" transform="translate(576,524)"/>
<path d="" fill="#B3BCF6" transform="translate(0,0)"/>
<path d="" fill="#B3BDF6" transform="translate(0,0)"/>
<path d="" fill="#A5B1F3" transform="translate(0,0)"/>
<path d="" fill="#B5C0F6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC4F8" transform="translate(459,521)"/>
<path d="" fill="#B5BEF5" transform="translate(0,0)"/>
<path d="" fill="#B3BDF3" transform="translate(0,0)"/>
<path d="" fill="#B7C1F8" transform="translate(0,0)"/>
<path d="" fill="#B8C2F8" transform="translate(0,0)"/>
<path d="" fill="#AAB6F6" transform="translate(0,0)"/>
<path d="" fill="#B9C3F7" transform="translate(0,0)"/>
<path d="" fill="#B8C1FA" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="" fill="#B9C2F7" transform="translate(0,0)"/>
<path d="" fill="#B7C0F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9C2F9" transform="translate(534,515)"/>
<path d="" fill="#B4BEF5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9C1F7" transform="translate(302,515)"/>
<path d="" fill="#A9B3F2" transform="translate(0,0)"/>
<path d="" fill="#B8C2F8" transform="translate(0,0)"/>
<path d="" fill="#AAB7F3" transform="translate(0,0)"/>
<path d="" fill="#BCC4F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEC7FA" transform="translate(462,507)"/>
<path d="" fill="#BBC5F9" transform="translate(0,0)"/>
<path d="" fill="#B8C1F8" transform="translate(0,0)"/>
<path d="" fill="#B9C0F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC3F8" transform="translate(620,505)"/>
<path d="" fill="#B4BFF7" transform="translate(0,0)"/>
<path d="" fill="#ACB7F3" transform="translate(0,0)"/>
<path d="" fill="#B8C1F8" transform="translate(0,0)"/>
<path d="" fill="#C0C8F9" transform="translate(0,0)"/>
<path d="" fill="#BDC6F8" transform="translate(0,0)"/>
<path d="" fill="#C2CAFA" transform="translate(0,0)"/>
<path d="" fill="#B2BBF5" transform="translate(0,0)"/>
<path d="" fill="#B7C2F5" transform="translate(0,0)"/>
<path d="" fill="#BAC3F6" transform="translate(0,0)"/>
<path d="" fill="#B9C1F8" transform="translate(0,0)"/>
<path d="" fill="#BBC3F6" transform="translate(0,0)"/>
<path d="" fill="#BFC6FB" transform="translate(0,0)"/>
<path d="" fill="#B3BCF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9C3F9" transform="translate(622,499)"/>
<path d="" fill="#BCC6F9" transform="translate(0,0)"/>
<path d="" fill="#BCC3F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDC6F9" transform="translate(396,497)"/>
<path d="" fill="#A7B2F5" transform="translate(0,0)"/>
<path d="" fill="#BDC6FB" transform="translate(0,0)"/>
<path d="" fill="#C2CBF8" transform="translate(0,0)"/>
<path d="" fill="#B7C1F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BCC4F6" transform="translate(353,496)"/>
<path d="" fill="#B6C0F8" transform="translate(0,0)"/>
<path d="" fill="#B6C0F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC3F8" transform="translate(626,491)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BCC6FA" transform="translate(596,491)"/>
<path d="" fill="#BAC4F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9C3F8" transform="translate(347,489)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6BFF3" transform="translate(390,488)"/>
<path d="" fill="#B7C1F6" transform="translate(0,0)"/>
<path d="" fill="#AFBBF5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7BFF3" transform="translate(287,487)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEB9F3" transform="translate(732,484)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1BBF6" transform="translate(700,484)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3BFF8" transform="translate(550,484)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2BDF6" transform="translate(735,483)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7C1F8" transform="translate(647,483)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6BFF7" transform="translate(490,483)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6BEF6" transform="translate(481,483)"/>
<path d="" fill="#B6C0F8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B4BFF7" transform="translate(472,483)"/>
<path d="" fill="#B7BCF5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8BFF5" transform="translate(417,483)"/>
<path d="" fill="#B2BDF7" transform="translate(0,0)"/>
<path d="" fill="#B8C0F9" transform="translate(0,0)"/>
<path d="" fill="#BDC5FA" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5C0F7" transform="translate(449,467)"/>
<path d="" fill="#ADBAF5" transform="translate(0,0)"/>
<path d="" fill="#B2BFF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC5F8" transform="translate(460,390)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8C3F8" transform="translate(350,390)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6BFF6" transform="translate(353,389)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B4BFF4" transform="translate(304,389)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC3F9" transform="translate(540,388)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AFBAF3" transform="translate(301,388)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8C2F6" transform="translate(538,387)"/>
<path d="" fill="#AFBDF5" transform="translate(0,0)"/>
<path d="" fill="#B9C3F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6C2F8" transform="translate(400,383)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC6FB" transform="translate(403,381)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BAC7FA" transform="translate(400,381)"/>
<path d="" fill="#B3BFF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0CAF9" transform="translate(614,368)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFC9F5" transform="translate(328,368)"/>
<path d="" fill="#C3CCFB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0CBFB" transform="translate(551,363)"/>
<path d="" fill="#C3CCF8" transform="translate(0,0)"/>
<path d="" fill="#BFC9FA" transform="translate(0,0)"/>
<path d="" fill="#B3BEF6" transform="translate(0,0)"/>
<path d="" fill="#C7CFFA" transform="translate(0,0)"/>
<path d="" fill="#C4CCF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3CCFB" transform="translate(421,359)"/>
<path d="" fill="#C7CDF9" transform="translate(0,0)"/>
<path d="" fill="#C3CDFB" transform="translate(0,0)"/>
<path d="" fill="#C3CCFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4CDF8" transform="translate(589,353)"/>
<path d="" fill="#C7CFF8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8CFFA" transform="translate(680,351)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8D2FC" transform="translate(344,351)"/>
<path d="" fill="#C9D0F9" transform="translate(0,0)"/>
<path d="" fill="#C6CFFA" transform="translate(0,0)"/>
<path d="" fill="#CBD2FB" transform="translate(0,0)"/>
<path d="" fill="#D3D7FB" transform="translate(0,0)"/>
<path d="" fill="#D0D5FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6CDF9" transform="translate(319,339)"/>
<path d="" fill="#CDD4FC" transform="translate(0,0)"/>
<path d="" fill="#C5CDFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3CEFB" transform="translate(305,335)"/>
<path d="" fill="#D0D5F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C2C9F9" transform="translate(298,332)"/>
<path d="" fill="#C3CCFC" transform="translate(0,0)"/>
<path d="" fill="#CED5FD" transform="translate(0,0)"/>
<path d="" fill="#DDDFFB" transform="translate(0,0)"/>
<path d="" fill="#D1D7FB" transform="translate(0,0)"/>
<path d="" fill="#C2CCFB" transform="translate(0,0)"/>
<path d="" fill="#C7D0FA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D3D7FA" transform="translate(333,316)"/>
<path d="" fill="#D5DAFC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D0D6FC" transform="translate(330,315)"/>
<path d="" fill="#D0D8FC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD7FC" transform="translate(464,310)"/>
<path d="" fill="#D7DBFC" transform="translate(0,0)"/>
<path d="" fill="#CBD2F9" transform="translate(0,0)"/>
<path d="" fill="#CFD6FC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD5FC" transform="translate(346,302)"/>
<path d="" fill="#C9D2FB" transform="translate(0,0)"/>
<path d="" fill="#D0D8FC" transform="translate(0,0)"/>
<path d="" fill="#D6D8FD" transform="translate(0,0)"/>
<path d="" fill="#C5CDFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDD5FD" transform="translate(577,295)"/>
<path d="" fill="#CED6FC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CAD1FC" transform="translate(564,292)"/>
<path d="" fill="#CAD2FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDD4FB" transform="translate(598,275)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CFD6FB" transform="translate(468,273)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CAD4FB" transform="translate(466,272)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDD3FB" transform="translate(408,272)"/>
<path d="" fill="#C8CFFA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7CFF9" transform="translate(569,266)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8D1F8" transform="translate(566,266)"/>
<path d="" fill="#B4C1F9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2BEF6" transform="translate(792,229)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC5FA" transform="translate(369,229)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEC7F8" transform="translate(356,229)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1BCF4" transform="translate(229,228)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFC8FA" transform="translate(694,227)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3CAF9" transform="translate(630,227)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C1CAF9" transform="translate(486,227)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B6C1F8" transform="translate(262,227)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFCCFA" transform="translate(753,222)"/>
<path d="" fill="#CAD1FD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BDC8F9" transform="translate(751,221)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BAC4F9" transform="translate(278,221)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BBC6F7" transform="translate(273,221)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9C4F8" transform="translate(275,220)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C1CBFA" transform="translate(393,219)"/>
<path d="" fill="#B3C0F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDD3F9" transform="translate(508,215)"/>
<path d="" fill="#D1D7FF" transform="translate(0,0)"/>
<path d="" fill="#CED5FB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4CCFB" transform="translate(312,203)"/>
<path d="" fill="#ADBAF2" transform="translate(0,0)"/>
<path d="" fill="#C5CDF6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8D0F8" transform="translate(371,201)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CACFF8" transform="translate(795,200)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C7CFF8" transform="translate(368,200)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4CCF7" transform="translate(256,200)"/>
<path d="" fill="#B0BDF2" transform="translate(0,0)"/>
<path d="" fill="#C1CBFA" transform="translate(0,0)"/>
<path d="" fill="#CCD2FC" transform="translate(0,0)"/>
<path d="" fill="#C7CFFA" transform="translate(0,0)"/>
<path d="" fill="#DBE0FE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D2D8FD" transform="translate(469,191)"/>
<path d="" fill="#D7DCFA" transform="translate(0,0)"/>
<path d="" fill="#CACFFA" transform="translate(0,0)"/>
<path d="" fill="#B1BCF4" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BFC7F7" transform="translate(290,185)"/>
<path d="" fill="#C3CCF9" transform="translate(0,0)"/>
<path d="" fill="#CFD5FA" transform="translate(0,0)"/>
<path d="" fill="#CFD4FA" transform="translate(0,0)"/>
<path d="" fill="#DBDEFC" transform="translate(0,0)"/>
<path d="" fill="#C5CEFA" transform="translate(0,0)"/>
<path d="" fill="#D5DAFD" transform="translate(0,0)"/>
<path d="" fill="#CCD5FB" transform="translate(0,0)"/>
<path d="" fill="#B2BFF5" transform="translate(0,0)"/>
<path d="" fill="#C9D2FA" transform="translate(0,0)"/>
<path d="" fill="#D2D8FC" transform="translate(0,0)"/>
<path d="" fill="#ACB6EF" transform="translate(0,0)"/>
<path d="" fill="#CAD2FB" transform="translate(0,0)"/>
<path d="" fill="#DBE0FB" transform="translate(0,0)"/>
<path d="" fill="#DFE2FF" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C5CCF5" transform="translate(802,160)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D5DAFE" transform="translate(486,158)"/>
<path d="" fill="#CFD7FD" transform="translate(0,0)"/>
<path d="" fill="#DADFFC" transform="translate(0,0)"/>
<path d="" fill="#B4BFF6" transform="translate(0,0)"/>
<path d="" fill="#C1CAFB" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5C0F1" transform="translate(829,148)"/>
<path d="" fill="#D1D7FC" transform="translate(0,0)"/>
<path d="" fill="#D7DEFB" transform="translate(0,0)"/>
<path d="" fill="#C9D2F9" transform="translate(0,0)"/>
<path d="" fill="#B3BEF2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8D2F9" transform="translate(768,135)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBD3FC" transform="translate(356,135)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CDD2F8" transform="translate(229,135)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9D0F9" transform="translate(254,134)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CBD1F9" transform="translate(365,132)"/>
<path d="" fill="#B8C2F5" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9C4F3" transform="translate(812,113)"/>
<path d="" fill="#CED6FA" transform="translate(0,0)"/>
<path d="" fill="#C5CCF7" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C8F5" transform="translate(380,108)"/>
<path d="" fill="#B4BEF2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BEC7F7" transform="translate(343,107)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8C3F4" transform="translate(229,106)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9C3F4" transform="translate(778,105)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0C8F3" transform="translate(358,105)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B9C4F3" transform="translate(238,105)"/>
<path d="" fill="#7077CC" transform="translate(0,0)"/>
<path d="" fill="#6169C1" transform="translate(0,0)"/>
<path d="" fill="#6C73C8" transform="translate(0,0)"/>
<path d="" fill="#6168C0" transform="translate(0,0)"/>
<path d="" fill="#A3A8EE" transform="translate(0,0)"/>
<path d="" fill="#A4AAEE" transform="translate(0,0)"/>
<path d="" fill="#A6ABF1" transform="translate(0,0)"/>
<path d="" fill="#A7ACF2" transform="translate(0,0)"/>
<path d="" fill="#A5AAEF" transform="translate(0,0)"/>
<path d="" fill="#A1A7EF" transform="translate(0,0)"/>
<path d="" fill="#A5A9EE" transform="translate(0,0)"/>
<path d="" fill="#A2A8EC" transform="translate(0,0)"/>
<path d="" fill="#A6ABEF" transform="translate(0,0)"/>
<path d="" fill="#A7ADF2" transform="translate(0,0)"/>
<path d="" fill="#A8ADF2" transform="translate(0,0)"/>
<path d="" fill="#A6ACF0" transform="translate(0,0)"/>
<path d="" fill="#A7ADF0" transform="translate(0,0)"/>
<path d="" fill="#A1A8EE" transform="translate(0,0)"/>
<path d="" fill="#9AA1E9" transform="translate(0,0)"/>
<path d="" fill="#A2A8ED" transform="translate(0,0)"/>
<path d="" fill="#A4A8EC" transform="translate(0,0)"/>
<path d="" fill="#A5A8EC" transform="translate(0,0)"/>
<path d="" fill="#A4ABEE" transform="translate(0,0)"/>
<path d="" fill="#AAAEEF" transform="translate(0,0)"/>
<path d="" fill="#A8AEF0" transform="translate(0,0)"/>
<path d="" fill="#A7ABEF" transform="translate(0,0)"/>
<path d="" fill="#A4A9EC" transform="translate(0,0)"/>
<path d="" fill="#A2A8EE" transform="translate(0,0)"/>
<path d="" fill="#A0A6EE" transform="translate(0,0)"/>
<path d="" fill="#A1A6EC" transform="translate(0,0)"/>
<path d="" fill="#A4A7EF" transform="translate(0,0)"/>
<path d="" fill="#A5A9F0" transform="translate(0,0)"/>
<path d="" fill="#A9AEF4" transform="translate(0,0)"/>
<path d="" fill="#A8ACF0" transform="translate(0,0)"/>
<path d="" fill="#A3ABED" transform="translate(0,0)"/>
<path d="" fill="#A3A9EE" transform="translate(0,0)"/>
<path d="" fill="#A3A9EE" transform="translate(0,0)"/>
<path d="" fill="#A1A5ED" transform="translate(0,0)"/>
<path d="" fill="#AAB0EE" transform="translate(0,0)"/>
<path d="" fill="#A3A7ED" transform="translate(0,0)"/>
<path d="" fill="#AFB3F2" transform="translate(0,0)"/>
<path d="" fill="#ACB2F2" transform="translate(0,0)"/>
<path d="" fill="#ABB0F1" transform="translate(0,0)"/>
<path d="" fill="#ACB5F1" transform="translate(0,0)"/>
<path d="" fill="#A9ACEF" transform="translate(0,0)"/>
<path d="" fill="#AAAEF2" transform="translate(0,0)"/>
<path d="" fill="#ABB1F3" transform="translate(0,0)"/>
<path d="" fill="#A2A9ED" transform="translate(0,0)"/>
<path d="" fill="#ABAAEF" transform="translate(0,0)"/>
<path d="" fill="#AAAEEF" transform="translate(0,0)"/>
<path d="" fill="#AAB1F0" transform="translate(0,0)"/>
<path d="" fill="#A9AFF0" transform="translate(0,0)"/>
<path d="" fill="#A0A5ED" transform="translate(0,0)"/>
<path d="" fill="#A2A8ED" transform="translate(0,0)"/>
<path d="" fill="#A1A6EE" transform="translate(0,0)"/>
<path d="" fill="#A6AAEF" transform="translate(0,0)"/>
<path d="" fill="#AAB1F1" transform="translate(0,0)"/>
<path d="" fill="#A9AFF1" transform="translate(0,0)"/>
<path d="" fill="#ACB1F2" transform="translate(0,0)"/>
<path d="" fill="#A5A9F1" transform="translate(0,0)"/>
<path d="" fill="#A2A8EE" transform="translate(0,0)"/>
<path d="" fill="#AAB0F0" transform="translate(0,0)"/>
<path d="" fill="#ACB4F1" transform="translate(0,0)"/>
<path d="" fill="#A8ADEF" transform="translate(0,0)"/>
<path d="" fill="#AFB7F0" transform="translate(0,0)"/>
<path d="" fill="#AAB3ED" transform="translate(0,0)"/>
<path d="" fill="#AEB6F3" transform="translate(0,0)"/>
<path d="" fill="#A9AFF1" transform="translate(0,0)"/>
<path d="" fill="#A6AEEB" transform="translate(0,0)"/>
<path d="" fill="#AEB4EF" transform="translate(0,0)"/>
<path d="" fill="#ACB3F3" transform="translate(0,0)"/>
<path d="" fill="#A7ACEE" transform="translate(0,0)"/>
<path d="" fill="#ADB3F6" transform="translate(0,0)"/>
<path d="" fill="#ACB3F4" transform="translate(0,0)"/>
<path d="" fill="#A9ADED" transform="translate(0,0)"/>
<path d="" fill="#ABB1F2" transform="translate(0,0)"/>
<path d="" fill="#ADB4F4" transform="translate(0,0)"/>
<path d="" fill="#ACB4ED" transform="translate(0,0)"/>
<path d="" fill="#A7ACF1" transform="translate(0,0)"/>
<path d="" fill="#ACB0F3" transform="translate(0,0)"/>
<path d="" fill="#B0B6F5" transform="translate(0,0)"/>
<path d="" fill="#B0B8F5" transform="translate(0,0)"/>
<path d="" fill="#A8ACEF" transform="translate(0,0)"/>
<path d="" fill="#A7ADEE" transform="translate(0,0)"/>
<path d="" fill="#AEB5F3" transform="translate(0,0)"/>
<path d="" fill="#AAAFF1" transform="translate(0,0)"/>
<path d="" fill="#AFB5F1" transform="translate(0,0)"/>
<path d="" fill="#B2B6F2" transform="translate(0,0)"/>
<path d="" fill="#AFB5F3" transform="translate(0,0)"/>
<path d="" fill="#AEB5F0" transform="translate(0,0)"/>
<path d="" fill="#B1B8F4" transform="translate(0,0)"/>
<path d="" fill="#A9AFF3" transform="translate(0,0)"/>
<path d="" fill="#AFB2EF" transform="translate(0,0)"/>
<path d="" fill="#BEC1F6" transform="translate(0,0)"/>
<path d="" fill="#B1B8EF" transform="translate(0,0)"/>
<path d="" fill="#B2B9F1" transform="translate(0,0)"/>
<path d="" fill="#ADB4F4" transform="translate(0,0)"/>
<path d="" fill="#ABB4F2" transform="translate(0,0)"/>
<path d="" fill="#B2B7F4" transform="translate(0,0)"/>
<path d="" fill="#B6BCF5" transform="translate(0,0)"/>
<path d="" fill="#9EA5EE" transform="translate(0,0)"/>
<path d="" fill="#6F76CB" transform="translate(0,0)"/>
<path d="" fill="#A3A9EE" transform="translate(0,0)"/>
<path d="" fill="#ADB5F4" transform="translate(0,0)"/>
<path d="" fill="#B0B7F5" transform="translate(0,0)"/>
<path d="" fill="#B0B8F2" transform="translate(0,0)"/>
<path d="" fill="#AEB8F4" transform="translate(0,0)"/>
<path d="" fill="#AFB5F5" transform="translate(0,0)"/>
<path d="" fill="#B2B6F6" transform="translate(0,0)"/>
<path d="" fill="#AEB4F3" transform="translate(0,0)"/>
<path d="" fill="#B1B6F3" transform="translate(0,0)"/>
<path d="" fill="#B3B9F5" transform="translate(0,0)"/>
<path d="" fill="#B6BCF7" transform="translate(0,0)"/>
<path d="" fill="#B1B7F3" transform="translate(0,0)"/>
<path d="" fill="#A8AFF3" transform="translate(0,0)"/>
<path d="" fill="#9EA6EF" transform="translate(0,0)"/>
<path d="" fill="#B1B7F5" transform="translate(0,0)"/>
<path d="" fill="#AFB7F7" transform="translate(0,0)"/>
<path d="" fill="#B4BBF5" transform="translate(0,0)"/>
<path d="" fill="#B2B8F5" transform="translate(0,0)"/>
<path d="" fill="#B3BBF8" transform="translate(0,0)"/>
<path d="" fill="#B7BDF7" transform="translate(0,0)"/>
<path d="" fill="#AEB5F2" transform="translate(0,0)"/>
<path d="" fill="#B6BCF5" transform="translate(0,0)"/>
<path d="" fill="#B4BBF5" transform="translate(0,0)"/>
<path d="" fill="#B5BCF4" transform="translate(0,0)"/>
<path d="" fill="#B4BCF8" transform="translate(0,0)"/>
<path d="" fill="#B6BDF7" transform="translate(0,0)"/>
<path d="" fill="#B0B5F2" transform="translate(0,0)"/>
<path d="" fill="#A9B1F5" transform="translate(0,0)"/>
<path d="" fill="#B2B9F8" transform="translate(0,0)"/>
<path d="" fill="#BBBFF6" transform="translate(0,0)"/>
<path d="" fill="#BBC1F8" transform="translate(0,0)"/>
<path d="" fill="#B3BDF6" transform="translate(0,0)"/>
<path d="" fill="#B6BBF6" transform="translate(0,0)"/>
<path d="" fill="#A7AFEF" transform="translate(0,0)"/>
<path d="" fill="#B2B7F4" transform="translate(0,0)"/>
<path d="" fill="#B1B9F5" transform="translate(0,0)"/>
<path d="" fill="#B8BCF7" transform="translate(0,0)"/>
<path d="" fill="#B9BEF3" transform="translate(0,0)"/>
<path d="" fill="#B5BEF8" transform="translate(0,0)"/>
<path d="" fill="#B5BCF7" transform="translate(0,0)"/>
<path d="" fill="#B6BDF5" transform="translate(0,0)"/>
<path d="" fill="#B9C0F7" transform="translate(0,0)"/>
<path d="" fill="#ADB3F0" transform="translate(0,0)"/>
<path d="" fill="#B7BEF7" transform="translate(0,0)"/>
<path d="" fill="#B8BDF7" transform="translate(0,0)"/>
<path d="" fill="#B4BCF9" transform="translate(0,0)"/>
<path d="" fill="#B5BCF5" transform="translate(0,0)"/>
<path d="" fill="#B3BDF4" transform="translate(0,0)"/>
<path d="" fill="#AFB8F2" transform="translate(0,0)"/>
<path d="" fill="#A5AFED" transform="translate(0,0)"/>
<path d="" fill="#B2B8F7" transform="translate(0,0)"/>
<path d="" fill="#BDC0F4" transform="translate(0,0)"/>
<path d="" fill="#B1B6F6" transform="translate(0,0)"/>
<path d="" fill="#B7BEF9" transform="translate(0,0)"/>
<path d="" fill="#B3BAF2" transform="translate(0,0)"/>
<path d="" fill="#B1BAF3" transform="translate(0,0)"/>
<path d="" fill="#B9BEF9" transform="translate(0,0)"/>
<path d="" fill="#AFB6F4" transform="translate(0,0)"/>
<path d="" fill="#B8BFF5" transform="translate(0,0)"/>
<path d="" fill="#AFB5F6" transform="translate(0,0)"/>
<path d="" fill="#B0B8F4" transform="translate(0,0)"/>
<path d="" fill="#BBC0F7" transform="translate(0,0)"/>
<path d="" fill="#B7BCF8" transform="translate(0,0)"/>
<path d="" fill="#B6BDF7" transform="translate(0,0)"/>
<path d="" fill="#B9BFF8" transform="translate(0,0)"/>
<path d="" fill="#B6BCF7" transform="translate(0,0)"/>
<path d="" fill="#B4BAF2" transform="translate(0,0)"/>
<path d="" fill="#BABDF7" transform="translate(0,0)"/>
<path d="" fill="#B3BAF6" transform="translate(0,0)"/>
<path d="" fill="#AFB7F3" transform="translate(0,0)"/>
<path d="" fill="#B6BAF3" transform="translate(0,0)"/>
<path d="" fill="#AFB6F1" transform="translate(0,0)"/>
<path d="" fill="#BDC0F7" transform="translate(0,0)"/>
<path d="" fill="#B8BDF8" transform="translate(0,0)"/>
<path d="" fill="#B8BDF8" transform="translate(0,0)"/>
<path d="" fill="#B7BDF5" transform="translate(0,0)"/>
<path d="" fill="#B5BBF7" transform="translate(0,0)"/>
<path d="" fill="#B7C0F7" transform="translate(0,0)"/>
<path d="" fill="#B6BDF6" transform="translate(0,0)"/>
<path d="" fill="#B7BCF7" transform="translate(0,0)"/>
<path d="" fill="#B7BEF7" transform="translate(0,0)"/>
<path d="" fill="#B0B8F4" transform="translate(0,0)"/>
<path d="" fill="#B0B6F3" transform="translate(0,0)"/>
<path d="" fill="#B4BAF5" transform="translate(0,0)"/>
<path d="" fill="#B9BEF7" transform="translate(0,0)"/>
<path d="" fill="#B7BDF7" transform="translate(0,0)"/>
<path d="" fill="#B3BAF6" transform="translate(0,0)"/>
<path d="" fill="#AFB6F2" transform="translate(0,0)"/>
<path d="" fill="#B4B8F5" transform="translate(0,0)"/>
<path d="" fill="#B4BAF6" transform="translate(0,0)"/>
<path d="" fill="#B6BBF6" transform="translate(0,0)"/>
<path d="" fill="#B7BDF8" transform="translate(0,0)"/>
<path d="" fill="#B7BEF4" transform="translate(0,0)"/>
<path d="" fill="#B0B9F6" transform="translate(0,0)"/>
<path d="" fill="#B5BFF5" transform="translate(0,0)"/>
<path d="" fill="#B3BAF3" transform="translate(0,0)"/>
<path d="" fill="#B6BFF7" transform="translate(0,0)"/>
<path d="" fill="#B4BBF5" transform="translate(0,0)"/>
<path d="" fill="#AFB8F5" transform="translate(0,0)"/>
<path d="" fill="#B4BBF7" transform="translate(0,0)"/>
<path d="" fill="#B0B7F6" transform="translate(0,0)"/>
<path d="" fill="#B3BAF6" transform="translate(0,0)"/>
<path d="" fill="#B3B9F5" transform="translate(0,0)"/>
<path d="" fill="#B8BFF8" transform="translate(0,0)"/>
<path d="" fill="#AEB6F2" transform="translate(0,0)"/>
<path d="" fill="#ADB4F1" transform="translate(0,0)"/>
<path d="" fill="#B6BEF5" transform="translate(0,0)"/>
<path d="" fill="#B2BAF4" transform="translate(0,0)"/>
<path d="" fill="#B6BEF6" transform="translate(0,0)"/>
<path d="" fill="#B3BBF2" transform="translate(0,0)"/>
<path d="" fill="#B4BCF9" transform="translate(0,0)"/>
<path d="" fill="#B4BBF6" transform="translate(0,0)"/>
<path d="" fill="#B5BAF4" transform="translate(0,0)"/>
<path d="" fill="#ADB3F3" transform="translate(0,0)"/>
<path d="" fill="#B1B8F4" transform="translate(0,0)"/>
<path d="" fill="#B3BAF5" transform="translate(0,0)"/>
<path d="" fill="#B1B8F3" transform="translate(0,0)"/>
<path d="" fill="#B2BAF6" transform="translate(0,0)"/>
<path d="" fill="#B4B8F2" transform="translate(0,0)"/>
<path d="" fill="#B4BCF3" transform="translate(0,0)"/>
<path d="" fill="#B7BDF5" transform="translate(0,0)"/>
<path d="" fill="#B7BEF4" transform="translate(0,0)"/>
<path d="" fill="#B5BBF6" transform="translate(0,0)"/>
<path d="" fill="#B3BCF6" transform="translate(0,0)"/>
<path d="" fill="#ADB6F2" transform="translate(0,0)"/>
<path d="" fill="#AEB5F5" transform="translate(0,0)"/>
<path d="" fill="#B3BBF3" transform="translate(0,0)"/>
<path d="" fill="#A9B2F0" transform="translate(0,0)"/>
<path d="" fill="#B5B9F0" transform="translate(0,0)"/>
<path d="" fill="#B8BDF7" transform="translate(0,0)"/>
<path d="" fill="#B2BCF6" transform="translate(0,0)"/>
<path d="" fill="#B2BBF3" transform="translate(0,0)"/>
<path d="" fill="#B2B6F2" transform="translate(0,0)"/>
<path d="" fill="#B1B7EF" transform="translate(0,0)"/>
<path d="" fill="#B5BCF8" transform="translate(0,0)"/>
<path d="" fill="#B6BDF2" transform="translate(0,0)"/>
<path d="" fill="#B5BCF6" transform="translate(0,0)"/>
<path d="" fill="#B4BBF5" transform="translate(0,0)"/>
<path d="" fill="#B1B8F7" transform="translate(0,0)"/>
<path d="" fill="#B6BAF5" transform="translate(0,0)"/>
<path d="" fill="#B0BAF0" transform="translate(0,0)"/>
<path d="" fill="#B4BAF6" transform="translate(0,0)"/>
<path d="" fill="#AFB6F5" transform="translate(0,0)"/>
<path d="" fill="#BABFF8" transform="translate(0,0)"/>
<path d="" fill="#B8BEF7" transform="translate(0,0)"/>
<path d="" fill="#B9C0F7" transform="translate(0,0)"/>
<path d="" fill="#B3BBF3" transform="translate(0,0)"/>
<path d="" fill="#A7AFEE" transform="translate(0,0)"/>
<path d="" fill="#B9C0F6" transform="translate(0,0)"/>
<path d="" fill="#BFC4F8" transform="translate(0,0)"/>
<path d="" fill="#B9BFF7" transform="translate(0,0)"/>
<path d="" fill="#AFB5F5" transform="translate(0,0)"/>
<path d="" fill="#B9C0F4" transform="translate(0,0)"/>
<path d="" fill="#B1B9F4" transform="translate(0,0)"/>
<path d="" fill="#B3B9F0" transform="translate(0,0)"/>
<path d="" fill="#B5BDF5" transform="translate(0,0)"/>
<path d="" fill="#BABEF9" transform="translate(0,0)"/>
<path d="" fill="#B9BDF3" transform="translate(0,0)"/>
<path d="" fill="#B3BBF3" transform="translate(0,0)"/>
<path d="" fill="#B3BCF6" transform="translate(0,0)"/>
<path d="" fill="#B1B8F3" transform="translate(0,0)"/>
<path d="" fill="#ACB7F5" transform="translate(0,0)"/>
<path d="" fill="#B4BCF5" transform="translate(0,0)"/>
<path d="" fill="#B2B8F4" transform="translate(0,0)"/>
<path d="" fill="#ABB0ED" transform="translate(0,0)"/>
<path d="" fill="#ACB3F2" transform="translate(0,0)"/>
<path d="" fill="#B5BAF7" transform="translate(0,0)"/>
<path d="" fill="#B8BDF7" transform="translate(0,0)"/>
<path d="" fill="#A7AFF3" transform="translate(0,0)"/>
<path d="" fill="#A8B0F1" transform="translate(0,0)"/>
<path d="" fill="#AFB7F4" transform="translate(0,0)"/>
<path d="" fill="#B5BDF6" transform="translate(0,0)"/>
<path d="" fill="#ACB4F1" transform="translate(0,0)"/>
<path d="" fill="#A9B1F2" transform="translate(0,0)"/>
<path d="" fill="#ADB4F3" transform="translate(0,0)"/>
<path d="" fill="#B1BAF6" transform="translate(0,0)"/>
<path d="" fill="#AAB1F3" transform="translate(0,0)"/>
<path d="" fill="#ABB4F1" transform="translate(0,0)"/>
<path d="" fill="#A6ADEE" transform="translate(0,0)"/>
<path d="" fill="#A9B1EF" transform="translate(0,0)"/>
<path d="" fill="#A9AFF0" transform="translate(0,0)"/>
<path d="" fill="#AFB6F1" transform="translate(0,0)"/>
<path d="" fill="#B1BAF7" transform="translate(0,0)"/>
<path d="" fill="#B4BCF7" transform="translate(0,0)"/>
<path d="" fill="#6477CB" transform="translate(0,0)"/>
<path d="" fill="#BCC3F9" transform="translate(0,0)"/>
<path d="" fill="#BBC2F8" transform="translate(0,0)"/>
<path d="" fill="#C5CCFD" transform="translate(0,0)"/>
<path d="" fill="#BFC6FB" transform="translate(0,0)"/>
<path d="" fill="#B7BFF7" transform="translate(0,0)"/>
<path d="" fill="#C6CCFA" transform="translate(0,0)"/>
<path d="" fill="#BFC5F8" transform="translate(0,0)"/>
<path d="" fill="#C0C6FA" transform="translate(0,0)"/>
<path d="" fill="#BBC2F8" transform="translate(0,0)"/>
<path d="" fill="#BAC2F7" transform="translate(0,0)"/>
<path d="" fill="#BBC6FA" transform="translate(0,0)"/>
<path d="" fill="#BEC8F8" transform="translate(0,0)"/>
<path d="" fill="#C5CCF6" transform="translate(0,0)"/>
<path d="" fill="#B8C1F7" transform="translate(0,0)"/>
<path d="" fill="#C3C8FB" transform="translate(0,0)"/>
<path d="" fill="#C5CEF8" transform="translate(0,0)"/>
<path d="" fill="#CAD2F8" transform="translate(0,0)"/>
<path d="" fill="#C6CFFA" transform="translate(0,0)"/>
<path d="" fill="#C4CDFA" transform="translate(0,0)"/>
<path d="" fill="#C5CBFA" transform="translate(0,0)"/>
<path d="" fill="#C4CBF6" transform="translate(0,0)"/>
<path d="" fill="#BBC3F8" transform="translate(0,0)"/>
<path d="" fill="#C6CCFD" transform="translate(0,0)"/>
<path d="" fill="#C0C9F9" transform="translate(0,0)"/>
<path d="" fill="#CCD5FB" transform="translate(0,0)"/>
<path d="" fill="#C7CEFA" transform="translate(0,0)"/>
<path d="" fill="#C7CFFA" transform="translate(0,0)"/>
<path d="" fill="#C4CAFA" transform="translate(0,0)"/>
<path d="" fill="#CFD6FD" transform="translate(0,0)"/>
<path d="" fill="#CCD5FC" transform="translate(0,0)"/>
<path d="" fill="#C2CCF9" transform="translate(0,0)"/>
<path d="" fill="#CAD1FA" transform="translate(0,0)"/>
<path d="" fill="#BFC4EF" transform="translate(0,0)"/>
<path d="" fill="#C0C9F9" transform="translate(0,0)"/>
<path d="" fill="#CDD4F9" transform="translate(0,0)"/>
<path d="" fill="#C3CBFB" transform="translate(0,0)"/>
<path d="" fill="#C4CDFB" transform="translate(0,0)"/>
<path d="" fill="#BEC7F8" transform="translate(0,0)"/>
<path d="" fill="#C2C9F8" transform="translate(0,0)"/>
<path d="" fill="#D1D8FD" transform="translate(0,0)"/>
<path d="" fill="#C7D0FD" transform="translate(0,0)"/>
<path d="" fill="#C5CDF8" transform="translate(0,0)"/>
<path d="" fill="#C4CCFB" transform="translate(0,0)"/>
<path d="" fill="#C7D0F8" transform="translate(0,0)"/>
<path d="" fill="#CED1F7" transform="translate(0,0)"/>
<path d="" fill="#D8DFFF" transform="translate(0,0)"/>
<path d="" fill="#D1D7FD" transform="translate(0,0)"/>
<path d="" fill="#D5DBFF" transform="translate(0,0)"/>
<path d="" fill="#D5DBFC" transform="translate(0,0)"/>
<path d="" fill="#D5DBFE" transform="translate(0,0)"/>
<path d="" fill="#C8CFFC" transform="translate(0,0)"/>
<path d="" fill="#C8CFFC" transform="translate(0,0)"/>
<path d="" fill="#C6CFFB" transform="translate(0,0)"/>
<path d="" fill="#D3D9FD" transform="translate(0,0)"/>
<path d="" fill="#C7CEFA" transform="translate(0,0)"/>
<path d="" fill="#C3C8FB" transform="translate(0,0)"/>
<path d="" fill="#C6CEF9" transform="translate(0,0)"/>
<path d="" fill="#C7CEFB" transform="translate(0,0)"/>
<path d="" fill="#D2D8FE" transform="translate(0,0)"/>
<path d="" fill="#8190DC" transform="translate(0,0)"/>
<path d="" fill="#DBE0FF" transform="translate(0,0)"/>
<path d="" fill="#CACFFB" transform="translate(0,0)"/>
<path d="" fill="#D5DDFF" transform="translate(0,0)"/>
<path d="" fill="#D5DBFD" transform="translate(0,0)"/>
<path d="" fill="#C8CEFA" transform="translate(0,0)"/>
<path d="" fill="#D5DBFE" transform="translate(0,0)"/>
<path d="" fill="#D3DAFE" transform="translate(0,0)"/>
<path d="" fill="#C9CFF9" transform="translate(0,0)"/>
<path d="" fill="#D9DFFF" transform="translate(0,0)"/>
<path d="" fill="#CBD4FD" transform="translate(0,0)"/>
<path d="" fill="#D6DCFF" transform="translate(0,0)"/>
<path d="" fill="#D7DEFF" transform="translate(0,0)"/>
<path d="" fill="#DAE1FE" transform="translate(0,0)"/>
<path d="" fill="#D9DFFE" transform="translate(0,0)"/>
<path d="" fill="#CFD6FB" transform="translate(0,0)"/>
<path d="" fill="#C9CFF8" transform="translate(0,0)"/>
<path d="" fill="#E5E9FF" transform="translate(0,0)"/>
<path d="" fill="#C7CDF9" transform="translate(0,0)"/>
<path d="" fill="#C6CDFA" transform="translate(0,0)"/>
<path d="" fill="#DCE0FC" transform="translate(0,0)"/>
<path d="" fill="#D4DAFF" transform="translate(0,0)"/>
<path d="" fill="#CAD0FD" transform="translate(0,0)"/>
<path d="" fill="#DADDFC" transform="translate(0,0)"/>
<path d="" fill="#D3D9FB" transform="translate(0,0)"/>
<path d="" fill="#D2DAFE" transform="translate(0,0)"/>
<path d="" fill="#D5D9FA" transform="translate(0,0)"/>
<path d="" fill="#D6DBFA" transform="translate(0,0)"/>
<path d="" fill="#CAD3F9" transform="translate(0,0)"/>
<path d="" fill="#D3D9FD" transform="translate(0,0)"/>
<path d="" fill="#D0D6FD" transform="translate(0,0)"/>
<path d="" fill="#D4D7FF" transform="translate(0,0)"/>
<path d="" fill="#CDD5FD" transform="translate(0,0)"/>
<path d="" fill="#D0D6FC" transform="translate(0,0)"/>
<path d="" fill="#D8DCFE" transform="translate(0,0)"/>
<path d="" fill="#D4D9FE" transform="translate(0,0)"/>
<path d="" fill="#CED3FC" transform="translate(0,0)"/>
<path d="" fill="#D8DBFD" transform="translate(0,0)"/>
<path d="" fill="#CFD5FF" transform="translate(0,0)"/>
<path d="" fill="#D4D9FB" transform="translate(0,0)"/>
<path d="" fill="#CCD5FA" transform="translate(0,0)"/>
<path d="" fill="#C8CEFD" transform="translate(0,0)"/>
<path d="" fill="#D2D8FF" transform="translate(0,0)"/>
<path d="" fill="#C4CBFA" transform="translate(0,0)"/>
<path d="" fill="#CAD2F9" transform="translate(0,0)"/>
<path d="" fill="#CBD3FA" transform="translate(0,0)"/>
<path d="" fill="#D3D7FC" transform="translate(0,0)"/>
<path d="" fill="#D4D9FE" transform="translate(0,0)"/>
<path d="" fill="#D1D6FD" transform="translate(0,0)"/>
<path d="" fill="#D2D7FC" transform="translate(0,0)"/>
<path d="" fill="#CAD0FA" transform="translate(0,0)"/>
<path d="" fill="#D1D6FD" transform="translate(0,0)"/>
<path d="" fill="#D3D8FC" transform="translate(0,0)"/>
<path d="" fill="#D8DDFB" transform="translate(0,0)"/>
<path d="" fill="#D3D9FA" transform="translate(0,0)"/>
<path d="" fill="#D0D6FB" transform="translate(0,0)"/>
<path d="" fill="#D1D7FE" transform="translate(0,0)"/>
<path d="" fill="#D1D6FB" transform="translate(0,0)"/>
<path d="" fill="#CBD1FB" transform="translate(0,0)"/>
<path d="" fill="#CBD2FD" transform="translate(0,0)"/>
<path d="" fill="#BDC8FA" transform="translate(0,0)"/>
<path d="" fill="#D4DAFC" transform="translate(0,0)"/>
<path d="" fill="#D5DAFE" transform="translate(0,0)"/>
<path d="" fill="#D2D8FC" transform="translate(0,0)"/>
<path d="" fill="#CFD5FC" transform="translate(0,0)"/>
<path d="" fill="#C8CFF9" transform="translate(0,0)"/>
<path d="" fill="#C5CEFD" transform="translate(0,0)"/>
<path d="" fill="#BECAF4" transform="translate(0,0)"/>
<path d="" fill="#BEC7F9" transform="translate(0,0)"/>
<path d="" fill="#BFC9F9" transform="translate(0,0)"/>
<path d="" fill="#C8CFFA" transform="translate(0,0)"/>
<path d="" fill="#C8D0FC" transform="translate(0,0)"/>
<path d="" fill="#C4CDFB" transform="translate(0,0)"/>
<path d="" fill="#C4CBFB" transform="translate(0,0)"/>
<path d="" fill="#C4CCF8" transform="translate(0,0)"/>
<path d="" fill="#7889D6" transform="translate(0,0)"/>
<path d="" fill="#C0C9F9" transform="translate(0,0)"/>
<path d="" fill="#C6CBF9" transform="translate(0,0)"/>
<path d="" fill="#C1CCFA" transform="translate(0,0)"/>
<path d="" fill="#C2CAF8" transform="translate(0,0)"/>
<path d="" fill="#C7CDF8" transform="translate(0,0)"/>
<path d="" fill="#C1CCF8" transform="translate(0,0)"/>
<path d="" fill="#BEC7FA" transform="translate(0,0)"/>
<path d="" fill="#C2CAFA" transform="translate(0,0)"/>
<path d="" fill="#C1CAFA" transform="translate(0,0)"/>
<path d="" fill="#C9CEFA" transform="translate(0,0)"/>
<path d="" fill="#5E75CC" transform="translate(0,0)"/>
<path d="" fill="#BDC6F9" transform="translate(0,0)"/>
<path d="" fill="#C0C8FA" transform="translate(0,0)"/>
<path d="" fill="#BEC7FA" transform="translate(0,0)"/>
<path d="" fill="#5D74CB" transform="translate(0,0)"/>
<path d="" fill="#B0BAF6" transform="translate(0,0)"/>
<path d="" fill="#AFB9F6" transform="translate(0,0)"/>
<path d="" fill="#AFBBF5" transform="translate(0,0)"/>
<path d="" fill="#AFBAF5" transform="translate(0,0)"/>
<path d="" fill="#B0BAF5" transform="translate(0,0)"/>
<path d="" fill="#A9B4F2" transform="translate(0,0)"/>
<path d="" fill="#B2BCF7" transform="translate(0,0)"/>
<path d="" fill="#B2BBF6" transform="translate(0,0)"/>
<path d="" fill="#B1BCF7" transform="translate(0,0)"/>
<path d="" fill="#A5B1F3" transform="translate(0,0)"/>
<path d="" fill="#A4AFF0" transform="translate(0,0)"/>
<path d="" fill="#ADB6F4" transform="translate(0,0)"/>
<path d="" fill="#AAB8F5" transform="translate(0,0)"/>
<path d="" fill="#B6BFF9" transform="translate(0,0)"/>
<path d="" fill="#B0B8F6" transform="translate(0,0)"/>
<path d="" fill="#B1BDF6" transform="translate(0,0)"/>
<path d="" fill="#B1BBF6" transform="translate(0,0)"/>
<path d="" fill="#B1B9F6" transform="translate(0,0)"/>
<path d="" fill="#ADBAF4" transform="translate(0,0)"/>
<path d="" fill="#AEB9F4" transform="translate(0,0)"/>
<path d="" fill="#AEB6F6" transform="translate(0,0)"/>
<path d="" fill="#ACB5F3" transform="translate(0,0)"/>
<path d="" fill="#A6B2F2" transform="translate(0,0)"/>
<path d="" fill="#B1BAF7" transform="translate(0,0)"/>
<path d="" fill="#B6BEF7" transform="translate(0,0)"/>
<path d="" fill="#B7C0F8" transform="translate(0,0)"/>
<path d="" fill="#AEB8F5" transform="translate(0,0)"/>
<path d="" fill="#B2BDF7" transform="translate(0,0)"/>
<path d="" fill="#B1BBF7" transform="translate(0,0)"/>
<path d="" fill="#AFB9F7" transform="translate(0,0)"/>
<path d="" fill="#AEB6F4" transform="translate(0,0)"/>
<path d="" fill="#ABB6F5" transform="translate(0,0)"/>
<path d="" fill="#A9B4F3" transform="translate(0,0)"/>
<path d="" fill="#A9B5F3" transform="translate(0,0)"/>
<path d="" fill="#ADB7F4" transform="translate(0,0)"/>
<path d="" fill="#B2BBF7" transform="translate(0,0)"/>
<path d="" fill="#B9C1F8" transform="translate(0,0)"/>
<path d="" fill="#B8C2FA" transform="translate(0,0)"/>
<path d="" fill="#ADB9F7" transform="translate(0,0)"/>
<path d="" fill="#AEBBF5" transform="translate(0,0)"/>
<path d="" fill="#B2BCF5" transform="translate(0,0)"/>
<path d="" fill="#AEB9F5" transform="translate(0,0)"/>
<path d="" fill="#ADB9F7" transform="translate(0,0)"/>
<path d="" fill="#ADB8F6" transform="translate(0,0)"/>
<path d="" fill="#ADB7F4" transform="translate(0,0)"/>
<path d="" fill="#A7B2F2" transform="translate(0,0)"/>
<path d="" fill="#A4B1F2" transform="translate(0,0)"/>
<path d="" fill="#A0ADEF" transform="translate(0,0)"/>
<path d="" fill="#9FABF1" transform="translate(0,0)"/>
<path d="" fill="#ABB4F3" transform="translate(0,0)"/>
<path d="" fill="#AEB7F2" transform="translate(0,0)"/>
<path d="" fill="#AEB7F4" transform="translate(0,0)"/>
<path d="" fill="#AFB9F5" transform="translate(0,0)"/>
<path d="" fill="#B8C0F9" transform="translate(0,0)"/>
<path d="" fill="#B5BDF7" transform="translate(0,0)"/>
<path d="" fill="#B1B9F6" transform="translate(0,0)"/>
<path d="" fill="#ADB9F2" transform="translate(0,0)"/>
<path d="" fill="#AEB7F3" transform="translate(0,0)"/>
<path d="" fill="#AEB9F4" transform="translate(0,0)"/>
<path d="" fill="#9FABEF" transform="translate(0,0)"/>
<path d="" fill="#A4B0F2" transform="translate(0,0)"/>
<path d="" fill="#A4ADF1" transform="translate(0,0)"/>
<path d="" fill="#AAB4F2" transform="translate(0,0)"/>
<path d="" fill="#B2BCF6" transform="translate(0,0)"/>
<path d="" fill="#AEB6F4" transform="translate(0,0)"/>
<path d="" fill="#AFBAF8" transform="translate(0,0)"/>
<path d="" fill="#B4BDF7" transform="translate(0,0)"/>
<path d="" fill="#B3BDF7" transform="translate(0,0)"/>
<path d="" fill="#B5C0F8" transform="translate(0,0)"/>
<path d="" fill="#B3BBF7" transform="translate(0,0)"/>
<path d="" fill="#B1B9F7" transform="translate(0,0)"/>
<path d="" fill="#B9C2F6" transform="translate(0,0)"/>
<path d="" fill="#B5BEFA" transform="translate(0,0)"/>
<path d="" fill="#B0BAF4" transform="translate(0,0)"/>
<path d="" fill="#B0B9F5" transform="translate(0,0)"/>
<path d="" fill="#A8B2F2" transform="translate(0,0)"/>
<path d="" fill="#BFC7F9" transform="translate(0,0)"/>
<path d="" fill="#A5AFF0" transform="translate(0,0)"/>
<path d="" fill="#B1B8F5" transform="translate(0,0)"/>
<path d="" fill="#B5C0F7" transform="translate(0,0)"/>
<path d="" fill="#B0BAF5" transform="translate(0,0)"/>
<path d="" fill="#B4BCF4" transform="translate(0,0)"/>
<path d="" fill="#B4BCF5" transform="translate(0,0)"/>
<path d="" fill="#B6BDF6" transform="translate(0,0)"/>
<path d="" fill="#ADB7F4" transform="translate(0,0)"/>
<path d="" fill="#AAB3F3" transform="translate(0,0)"/>
<path d="" fill="#AAB5F3" transform="translate(0,0)"/>
<path d="" fill="#ABB7F6" transform="translate(0,0)"/>
<path d="" fill="#B6C2F9" transform="translate(0,0)"/>
<path d="" fill="#A9B5F4" transform="translate(0,0)"/>
<path d="" fill="#B1BDF5" transform="translate(0,0)"/>
<path d="" fill="#B9C1F6" transform="translate(0,0)"/>
<path d="" fill="#B3BEF7" transform="translate(0,0)"/>
<path d="" fill="#B1BDF6" transform="translate(0,0)"/>
<path d="" fill="#AFBEF0" transform="translate(0,0)"/>
<path d="" fill="#B7C1F4" transform="translate(0,0)"/>
<path d="" fill="#AEB9EE" transform="translate(0,0)"/>
<path d="" fill="#AEB9EE" transform="translate(0,0)"/>
<path d="" fill="#AEB6F2" transform="translate(0,0)"/>
<path d="" fill="#AAB4F4" transform="translate(0,0)"/>
<path d="" fill="#B0B9F4" transform="translate(0,0)"/>
<path d="" fill="#AFB8F2" transform="translate(0,0)"/>
<path d="" fill="#B5BDF7" transform="translate(0,0)"/>
<path d="" fill="#B4BFF5" transform="translate(0,0)"/>
<path d="" fill="#B7BFF6" transform="translate(0,0)"/>
<path d="" fill="#BBC4F7" transform="translate(0,0)"/>
<path d="" fill="#AFBAF6" transform="translate(0,0)"/>
<path d="" fill="#B3BFF6" transform="translate(0,0)"/>
<path d="" fill="#B2BCF6" transform="translate(0,0)"/>
<path d="" fill="#B3BCF7" transform="translate(0,0)"/>
<path d="" fill="#B3BDF6" transform="translate(0,0)"/>
<path d="" fill="#BAC2F9" transform="translate(0,0)"/>
<path d="" fill="#B3BCF8" transform="translate(0,0)"/>
<path d="" fill="#B4BEF6" transform="translate(0,0)"/>
<path d="" fill="#B5BEF8" transform="translate(0,0)"/>
<path d="" fill="#B5BDF7" transform="translate(0,0)"/>
<path d="" fill="#BCC4F8" transform="translate(0,0)"/>
<path d="" fill="#B2BDF7" transform="translate(0,0)"/>
<path d="" fill="#B2BDF5" transform="translate(0,0)"/>
<path d="" fill="#B5BEF7" transform="translate(0,0)"/>
<path d="" fill="#BBC4F9" transform="translate(0,0)"/>
<path d="" fill="#B3BBF5" transform="translate(0,0)"/>
<path d="" fill="#B3BFF6" transform="translate(0,0)"/>
<path d="" fill="#BAC2F7" transform="translate(0,0)"/>
<path d="" fill="#A6B3EF" transform="translate(0,0)"/>
<path d="" fill="#B9C2F9" transform="translate(0,0)"/>
<path d="" fill="#B5BEF7" transform="translate(0,0)"/>
<path d="" fill="#B3BEF6" transform="translate(0,0)"/>
<path d="" fill="#A9B3F3" transform="translate(0,0)"/>
<path d="" fill="#BDC4F9" transform="translate(0,0)"/>
<path d="" fill="#B3BFF4" transform="translate(0,0)"/>
<path d="" fill="#BAC5F8" transform="translate(0,0)"/>
<path d="" fill="#B9C2F6" transform="translate(0,0)"/>
<path d="" fill="#BBC4FB" transform="translate(0,0)"/>
<path d="" fill="#B5C1F6" transform="translate(0,0)"/>
<path d="" fill="#BEC6F7" transform="translate(0,0)"/>
<path d="" fill="#BBC7F9" transform="translate(0,0)"/>
<path d="" fill="#B2BCFA" transform="translate(0,0)"/>
<path d="" fill="#B6C0F6" transform="translate(0,0)"/>
<path d="" fill="#BCC5FA" transform="translate(0,0)"/>
<path d="" fill="#BEC6F9" transform="translate(0,0)"/>
<path d="" fill="#BBC4FA" transform="translate(0,0)"/>
<path d="" fill="#BDC7FA" transform="translate(0,0)"/>
<path d="" fill="#B6C0F7" transform="translate(0,0)"/>
<path d="" fill="#B0BEF8" transform="translate(0,0)"/>
<path d="" fill="#B0B8F5" transform="translate(0,0)"/>
<path d="" fill="#B5BFF5" transform="translate(0,0)"/>
<path d="" fill="#B9C3F8" transform="translate(0,0)"/>
<path d="" fill="#B9C3F9" transform="translate(0,0)"/>
<path d="" fill="#B7C0F9" transform="translate(0,0)"/>
<path d="" fill="#B7C1F4" transform="translate(0,0)"/>
<path d="" fill="#BBC5F9" transform="translate(0,0)"/>
<path d="" fill="#B4C0F8" transform="translate(0,0)"/>
<path d="" fill="#B0B8F5" transform="translate(0,0)"/>
<path d="" fill="#C2CAF9" transform="translate(0,0)"/>
<path d="" fill="#B9BFFA" transform="translate(0,0)"/>
<path d="" fill="#C2C9FD" transform="translate(0,0)"/>
<path d="" fill="#B8C2FA" transform="translate(0,0)"/>
<path d="" fill="#B7BFF8" transform="translate(0,0)"/>
<path d="" fill="#B9C3F8" transform="translate(0,0)"/>
<path d="" fill="#BCC3F9" transform="translate(0,0)"/>
<path d="" fill="#BBC5FA" transform="translate(0,0)"/>
<path d="" fill="#BAC3F7" transform="translate(0,0)"/>
<path d="" fill="#B2BDF6" transform="translate(0,0)"/>
<path d="" fill="#A6B3F4" transform="translate(0,0)"/>
<path d="" fill="#BAC4F9" transform="translate(0,0)"/>
<path d="" fill="#BBC5FB" transform="translate(0,0)"/>
<path d="" fill="#BAC3F9" transform="translate(0,0)"/>
<path d="" fill="#BDC6FB" transform="translate(0,0)"/>
<path d="" fill="#BDC4FA" transform="translate(0,0)"/>
<path d="" fill="#B7C0F8" transform="translate(0,0)"/>
<path d="" fill="#AEBAF0" transform="translate(0,0)"/>
<path d="" fill="#B2BBF6" transform="translate(0,0)"/>
<path d="" fill="#B9C4F9" transform="translate(0,0)"/>
<path d="" fill="#BAC2F7" transform="translate(0,0)"/>
<path d="" fill="#B5BFF6" transform="translate(0,0)"/>
<path d="" fill="#B9C4F4" transform="translate(0,0)"/>
<path d="" fill="#B4BFF7" transform="translate(0,0)"/>
<path d="" fill="#A8B6F3" transform="translate(0,0)"/>
<path d="" fill="#B0BCF7" transform="translate(0,0)"/>
<path d="" fill="#BAC2F8" transform="translate(0,0)"/>
<path d="" fill="#BCC4FC" transform="translate(0,0)"/>
<path d="" fill="#B4C0F8" transform="translate(0,0)"/>
<path d="" fill="#B9C2FA" transform="translate(0,0)"/>
<path d="" fill="#B7C3F8" transform="translate(0,0)"/>
<path d="" fill="#BBC5FA" transform="translate(0,0)"/>
<path d="" fill="#BBC4F8" transform="translate(0,0)"/>
<path d="" fill="#BBC2F7" transform="translate(0,0)"/>
<path d="" fill="#B2BCF4" transform="translate(0,0)"/>
<path d="" fill="#B7C3FB" transform="translate(0,0)"/>
<path d="" fill="#BDC5FA" transform="translate(0,0)"/>
<path d="" fill="#BAC4FA" transform="translate(0,0)"/>
<path d="" fill="#B6C2F8" transform="translate(0,0)"/>
<path d="" fill="#BAC4F9" transform="translate(0,0)"/>
<path d="" fill="#B8C2F9" transform="translate(0,0)"/>
<path d="" fill="#C0C6F7" transform="translate(0,0)"/>
<path d="" fill="#BBC4FB" transform="translate(0,0)"/>
<path d="" fill="#B9C2F7" transform="translate(0,0)"/>
<path d="" fill="#B9C2F9" transform="translate(0,0)"/>
<path d="" fill="#B7C2F9" transform="translate(0,0)"/>
<path d="" fill="#BCC7F9" transform="translate(0,0)"/>
<path d="" fill="#B8C2FA" transform="translate(0,0)"/>
<path d="" fill="#BAC3F8" transform="translate(0,0)"/>
<path d="" fill="#B1BBF5" transform="translate(0,0)"/>
<path d="" fill="#B8C1F8" transform="translate(0,0)"/>
<path d="" fill="#BFC6FA" transform="translate(0,0)"/>
<path d="" fill="#B9C2F9" transform="translate(0,0)"/>
<path d="" fill="#C1CAF9" transform="translate(0,0)"/>
<path d="" fill="#B7C0F8" transform="translate(0,0)"/>
<path d="" fill="#ACB7F4" transform="translate(0,0)"/>
<path d="" fill="#B0BDF6" transform="translate(0,0)"/>
<path d="" fill="#B9C0F9" transform="translate(0,0)"/>
<path d="" fill="#B8C2F8" transform="translate(0,0)"/>
<path d="" fill="#B8C4FA" transform="translate(0,0)"/>
<path d="" fill="#BBC2F9" transform="translate(0,0)"/>
<path d="" fill="#B7C2F9" transform="translate(0,0)"/>
<path d="" fill="#A6B3F4" transform="translate(0,0)"/>
<path d="" fill="#B2BDF5" transform="translate(0,0)"/>
<path d="" fill="#B2BCF5" transform="translate(0,0)"/>
<path d="" fill="#B2BCF6" transform="translate(0,0)"/>
<path d="" fill="#B0BBF7" transform="translate(0,0)"/>
<path d="" fill="#B9C3F6" transform="translate(0,0)"/>
<path d="" fill="#C0C8FB" transform="translate(0,0)"/>
<path d="" fill="#BBC6FC" transform="translate(0,0)"/>
<path d="" fill="#BDC3FB" transform="translate(0,0)"/>
<path d="" fill="#B8C2F9" transform="translate(0,0)"/>
<path d="" fill="#B6C0F8" transform="translate(0,0)"/>
<path d="" fill="#AEB9F3" transform="translate(0,0)"/>
<path d="" fill="#C1C8FC" transform="translate(0,0)"/>
<path d="" fill="#BCC3FB" transform="translate(0,0)"/>
<path d="" fill="#BEC4F9" transform="translate(0,0)"/>
<path d="" fill="#BEC8F9" transform="translate(0,0)"/>
<path d="" fill="#BCC3F7" transform="translate(0,0)"/>
<path d="" fill="#B1BEF5" transform="translate(0,0)"/>
<path d="" fill="#B8C1F6" transform="translate(0,0)"/>
<path d="" fill="#C2C6FA" transform="translate(0,0)"/>
<path d="" fill="#B8C2F9" transform="translate(0,0)"/>
<path d="" fill="#C2CAF8" transform="translate(0,0)"/>
<path d="" fill="#BAC3FA" transform="translate(0,0)"/>
<path d="" fill="#BAC1F6" transform="translate(0,0)"/>
<path d="" fill="#B2BCF5" transform="translate(0,0)"/>
<path d="" fill="#B8C3F8" transform="translate(0,0)"/>
<path d="" fill="#B7BEFA" transform="translate(0,0)"/>
<path d="" fill="#B9C0FA" transform="translate(0,0)"/>
<path d="" fill="#B8C1F7" transform="translate(0,0)"/>
<path d="" fill="#AEBAF5" transform="translate(0,0)"/>
<path d="" fill="#BCC6F8" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="" fill="#BBC4F7" transform="translate(0,0)"/>
<path d="" fill="#BBC7F8" transform="translate(0,0)"/>
<path d="" fill="#B7C0F6" transform="translate(0,0)"/>
<path d="" fill="#B8C4F9" transform="translate(0,0)"/>
<path d="" fill="#BCC5FA" transform="translate(0,0)"/>
<path d="" fill="#B8C3F9" transform="translate(0,0)"/>
<path d="" fill="#BBC5F9" transform="translate(0,0)"/>
<path d="" fill="#B9C3F7" transform="translate(0,0)"/>
<path d="" fill="#B6BFF8" transform="translate(0,0)"/>
<path d="" fill="#B0BBF4" transform="translate(0,0)"/>
<path d="" fill="#BAC3F8" transform="translate(0,0)"/>
<path d="" fill="#B9C5FA" transform="translate(0,0)"/>
<path d="" fill="#B6C1F9" transform="translate(0,0)"/>
<path d="" fill="#B7C2F7" transform="translate(0,0)"/>
<path d="" fill="#B2BBF6" transform="translate(0,0)"/>
<path d="" fill="#BBC3F6" transform="translate(0,0)"/>
<path d="" fill="#B9C1F7" transform="translate(0,0)"/>
<path d="" fill="#BCC7F9" transform="translate(0,0)"/>
<path d="" fill="#BCC7FA" transform="translate(0,0)"/>
<path d="" fill="#B8C1F9" transform="translate(0,0)"/>
<path d="" fill="#BAC4FA" transform="translate(0,0)"/>
<path d="" fill="#BBC3FC" transform="translate(0,0)"/>
<path d="" fill="#B6C0F6" transform="translate(0,0)"/>
<path d="" fill="#B8C2F8" transform="translate(0,0)"/>
<path d="" fill="#BAC5F7" transform="translate(0,0)"/>
<path d="" fill="#BDC4F7" transform="translate(0,0)"/>
<path d="" fill="#C2CAF7" transform="translate(0,0)"/>
<path d="" fill="#B6C1FA" transform="translate(0,0)"/>
<path d="" fill="#B2BDF4" transform="translate(0,0)"/>
<path d="" fill="#B0BCF6" transform="translate(0,0)"/>
<path d="" fill="#B4BEF5" transform="translate(0,0)"/>
<path d="" fill="#B6C0F7" transform="translate(0,0)"/>
<path d="" fill="#BEC5F6" transform="translate(0,0)"/>
<path d="" fill="#B9C3F5" transform="translate(0,0)"/>
<path d="" fill="#BAC4F7" transform="translate(0,0)"/>
<path d="" fill="#C0C8FD" transform="translate(0,0)"/>
<path d="" fill="#B9C1FA" transform="translate(0,0)"/>
<path d="" fill="#B6BFF5" transform="translate(0,0)"/>
<path d="" fill="#B9C2F7" transform="translate(0,0)"/>
<path d="" fill="#AFBAF4" transform="translate(0,0)"/>
<path d="" fill="#B5BFF7" transform="translate(0,0)"/>
<path d="" fill="#B5C0F7" transform="translate(0,0)"/>
<path d="" fill="#BFC6F6" transform="translate(0,0)"/>
<path d="" fill="#B8C2F7" transform="translate(0,0)"/>
<path d="" fill="#BCC5F8" transform="translate(0,0)"/>
<path d="" fill="#B6C3F7" transform="translate(0,0)"/>
<path d="" fill="#B1BDEE" transform="translate(0,0)"/>
<path d="" fill="#B9C3F5" transform="translate(0,0)"/>
<path d="" fill="#B4BEF7" transform="translate(0,0)"/>
<path d="" fill="#BDC6F9" transform="translate(0,0)"/>
<path d="" fill="#B9C3FB" transform="translate(0,0)"/>
<path d="" fill="#BEC5FB" transform="translate(0,0)"/>
<path d="" fill="#B6C0FA" transform="translate(0,0)"/>
<path d="" fill="#B1BDF8" transform="translate(0,0)"/>
<path d="" fill="#B2BDF4" transform="translate(0,0)"/>
<path d="" fill="#BAC4F9" transform="translate(0,0)"/>
<path d="" fill="#B9C3FA" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="" fill="#B4C1F7" transform="translate(0,0)"/>
<path d="" fill="#B6BFF7" transform="translate(0,0)"/>
<path d="" fill="#B1BDF4" transform="translate(0,0)"/>
<path d="" fill="#B8C1F7" transform="translate(0,0)"/>
<path d="" fill="#B1BCF8" transform="translate(0,0)"/>
<path d="" fill="#BAC1FA" transform="translate(0,0)"/>
<path d="" fill="#BDC5F8" transform="translate(0,0)"/>
<path d="" fill="#B3BDF8" transform="translate(0,0)"/>
<path d="" fill="#B7C1F6" transform="translate(0,0)"/>
<path d="" fill="#BDC6FA" transform="translate(0,0)"/>
<path d="" fill="#B3BDF8" transform="translate(0,0)"/>
<path d="" fill="#B0BDF6" transform="translate(0,0)"/>
<path d="" fill="#B6BFF7" transform="translate(0,0)"/>
<path d="" fill="#B9C1F7" transform="translate(0,0)"/>
<path d="" fill="#BBC4F9" transform="translate(0,0)"/>
<path d="" fill="#BCC5F9" transform="translate(0,0)"/>
<path d="" fill="#B7BFF6" transform="translate(0,0)"/>
<path d="" fill="#B9C2FA" transform="translate(0,0)"/>
<path d="" fill="#B7C1F9" transform="translate(0,0)"/>
<path d="" fill="#B6C0F6" transform="translate(0,0)"/>
<path d="" fill="#B5BDF9" transform="translate(0,0)"/>
<path d="" fill="#B3BEF9" transform="translate(0,0)"/>
<path d="" fill="#B7C0F8" transform="translate(0,0)"/>
<path d="" fill="#B3BEF8" transform="translate(0,0)"/>
<path d="" fill="#B7C2F9" transform="translate(0,0)"/>
<path d="" fill="#B8C2F9" transform="translate(0,0)"/>
<path d="" fill="#BCC4F6" transform="translate(0,0)"/>
<path d="" fill="#B4BFF8" transform="translate(0,0)"/>
<path d="" fill="#B3BFF8" transform="translate(0,0)"/>
<path d="" fill="#B6C1F6" transform="translate(0,0)"/>
<path d="" fill="#B9C1F8" transform="translate(0,0)"/>
<path d="" fill="#BAC3F5" transform="translate(0,0)"/>
<path d="" fill="#BAC3FA" transform="translate(0,0)"/>
<path d="" fill="#BBC3F8" transform="translate(0,0)"/>
<path d="" fill="#B6C1F8" transform="translate(0,0)"/>
<path d="" fill="#B6C1F7" transform="translate(0,0)"/>
<path d="" fill="#B4C0F8" transform="translate(0,0)"/>
<path d="" fill="#B5C0FB" transform="translate(0,0)"/>
<path d="" fill="#B9C3F8" transform="translate(0,0)"/>
<path d="" fill="#B2BCF5" transform="translate(0,0)"/>
<path d="" fill="#B9C3FA" transform="translate(0,0)"/>
<path d="" fill="#BAC2F8" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="" fill="#B5BFF6" transform="translate(0,0)"/>
<path d="" fill="#B6C1F6" transform="translate(0,0)"/>
<path d="" fill="#B6C1F6" transform="translate(0,0)"/>
<path d="" fill="#B3BDF4" transform="translate(0,0)"/>
<path d="" fill="#B0BBF6" transform="translate(0,0)"/>
<path d="" fill="#AFBCF6" transform="translate(0,0)"/>
<path d="" fill="#B0BDF6" transform="translate(0,0)"/>
<path d="" fill="#B5BFF7" transform="translate(0,0)"/>
<path d="" fill="#BDC5F9" transform="translate(0,0)"/>
<path d="" fill="#B6C1F9" transform="translate(0,0)"/>
<path d="" fill="#B2BDF7" transform="translate(0,0)"/>
<path d="" fill="#B6BFF8" transform="translate(0,0)"/>
<path d="" fill="#B7C3F9" transform="translate(0,0)"/>
<path d="" fill="#B5C0F9" transform="translate(0,0)"/>
<path d="" fill="#B5BFF8" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="" fill="#B9C3F6" transform="translate(0,0)"/>
<path d="" fill="#B4BFF8" transform="translate(0,0)"/>
<path d="" fill="#B6C0F9" transform="translate(0,0)"/>
<path d="" fill="#B8BFF6" transform="translate(0,0)"/>
<path d="" fill="#BAC3F8" transform="translate(0,0)"/>
<path d="" fill="#B9C4FA" transform="translate(0,0)"/>
<path d="" fill="#B8C1FA" transform="translate(0,0)"/>
<path d="" fill="#B9C5F9" transform="translate(0,0)"/>
<path d="" fill="#BBC3F9" transform="translate(0,0)"/>
<path d="" fill="#B3BEF9" transform="translate(0,0)"/>
<path d="" fill="#B7C1F9" transform="translate(0,0)"/>
<path d="" fill="#AEBAF6" transform="translate(0,0)"/>
<path d="" fill="#B6C1F8" transform="translate(0,0)"/>
<path d="" fill="#B8C2F7" transform="translate(0,0)"/>
<path d="" fill="#ACB7F3" transform="translate(0,0)"/>
<path d="" fill="#A9B6F0" transform="translate(0,0)"/>
<path d="" fill="#B9C2F4" transform="translate(0,0)"/>
<path d="" fill="#BDC5F9" transform="translate(0,0)"/>
<path d="" fill="#BBC3F9" transform="translate(0,0)"/>
<path d="" fill="#B4BEF6" transform="translate(0,0)"/>
<path d="" fill="#B8C0F4" transform="translate(0,0)"/>
<path d="" fill="#BCC4FA" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="" fill="#ACBAEF" transform="translate(0,0)"/>
<path d="" fill="#7589D8" transform="translate(0,0)"/>
<path d="" fill="#B8C1F9" transform="translate(0,0)"/>
<path d="" fill="#B6C0F7" transform="translate(0,0)"/>
<path d="" fill="#B2BDF7" transform="translate(0,0)"/>
<path d="" fill="#B5BEF5" transform="translate(0,0)"/>
<path d="" fill="#B7C2FA" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="" fill="#B4BEF7" transform="translate(0,0)"/>
<path d="" fill="#B0BBF5" transform="translate(0,0)"/>
<path d="" fill="#B2BEF7" transform="translate(0,0)"/>
<path d="" fill="#B4BFFA" transform="translate(0,0)"/>
<path d="" fill="#B6C1F4" transform="translate(0,0)"/>
<path d="" fill="#B5BFF7" transform="translate(0,0)"/>
<path d="" fill="#B5C0F7" transform="translate(0,0)"/>
<path d="" fill="#B3C0F8" transform="translate(0,0)"/>
<path d="" fill="#B4C0F7" transform="translate(0,0)"/>
<path d="" fill="#B5C0F8" transform="translate(0,0)"/>
<path d="" fill="#B6C0FA" transform="translate(0,0)"/>
<path d="" fill="#B7BFF9" transform="translate(0,0)"/>
<path d="" fill="#B8C3F8" transform="translate(0,0)"/>
<path d="" fill="#B4C1F7" transform="translate(0,0)"/>
<path d="" fill="#B8C3FC" transform="translate(0,0)"/>
<path d="" fill="#B2BDF7" transform="translate(0,0)"/>
<path d="" fill="#AEBDF4" transform="translate(0,0)"/>
<path d="" fill="#BAC3F9" transform="translate(0,0)"/>
<path d="" fill="#B5C0F8" transform="translate(0,0)"/>
<path d="" fill="#BEC8F4" transform="translate(0,0)"/>
<path d="" fill="#B8C5FA" transform="translate(0,0)"/>
<path d="" fill="#B5C0F5" transform="translate(0,0)"/>
<path d="" fill="#B7C2F6" transform="translate(0,0)"/>
<path d="" fill="#B8C3FA" transform="translate(0,0)"/>
<path d="" fill="#B7C2FA" transform="translate(0,0)"/>
<path d="" fill="#B6C0F9" transform="translate(0,0)"/>
<path d="" fill="#B5C3F8" transform="translate(0,0)"/>
<path d="" fill="#B7C1FA" transform="translate(0,0)"/>
<path d="" fill="#BCC5FA" transform="translate(0,0)"/>
<path d="" fill="#B8C2F9" transform="translate(0,0)"/>
<path d="" fill="#B8C2FA" transform="translate(0,0)"/>
<path d="" fill="#B1BCF7" transform="translate(0,0)"/>
<path d="" fill="#BAC4F6" transform="translate(0,0)"/>
<path d="" fill="#BDC6F8" transform="translate(0,0)"/>
<path d="" fill="#BAC4F9" transform="translate(0,0)"/>
<path d="" fill="#B9C4F9" transform="translate(0,0)"/>
<path d="" fill="#BAC4F5" transform="translate(0,0)"/>
<path d="" fill="#B8C5F7" transform="translate(0,0)"/>
<path d="" fill="#BAC4FA" transform="translate(0,0)"/>
<path d="" fill="#BBC7F8" transform="translate(0,0)"/>
<path d="" fill="#B7C5F7" transform="translate(0,0)"/>
<path d="" fill="#B7C3F4" transform="translate(0,0)"/>
<path d="" fill="#ADBCF2" transform="translate(0,0)"/>
<path d="" fill="#BCC5F5" transform="translate(0,0)"/>
<path d="" fill="#BDC7F7" transform="translate(0,0)"/>
<path d="" fill="#B8C4F6" transform="translate(0,0)"/>
<path d="" fill="#BBC4F8" transform="translate(0,0)"/>
<path d="" fill="#BBC7FC" transform="translate(0,0)"/>
<path d="" fill="#BBC5FB" transform="translate(0,0)"/>
<path d="" fill="#BCC5F9" transform="translate(0,0)"/>
<path d="" fill="#B9C6F9" transform="translate(0,0)"/>
<path d="" fill="#BBC5F8" transform="translate(0,0)"/>
<path d="" fill="#BDC9F8" transform="translate(0,0)"/>
<path d="" fill="#BCC4F8" transform="translate(0,0)"/>
<path d="" fill="#BDC9F7" transform="translate(0,0)"/>
<path d="" fill="#BAC3F8" transform="translate(0,0)"/>
<path d="" fill="#BFCAFB" transform="translate(0,0)"/>
<path d="" fill="#BDC5FB" transform="translate(0,0)"/>
<path d="" fill="#BDC8FA" transform="translate(0,0)"/>
<path d="" fill="#BCC9FA" transform="translate(0,0)"/>
<path d="" fill="#BDCAFB" transform="translate(0,0)"/>
<path d="" fill="#C0CAFB" transform="translate(0,0)"/>
<path d="" fill="#B2BDF4" transform="translate(0,0)"/>
<path d="" fill="#BEC9F8" transform="translate(0,0)"/>
<path d="" fill="#BFC9FB" transform="translate(0,0)"/>
<path d="" fill="#C4CEFA" transform="translate(0,0)"/>
<path d="" fill="#C2CCF9" transform="translate(0,0)"/>
<path d="" fill="#BDC6F6" transform="translate(0,0)"/>
<path d="" fill="#AFBBF5" transform="translate(0,0)"/>
<path d="" fill="#BFC9F8" transform="translate(0,0)"/>
<path d="" fill="#B5C2F3" transform="translate(0,0)"/>
<path d="" fill="#C0C9FB" transform="translate(0,0)"/>
<path d="" fill="#C1CAFC" transform="translate(0,0)"/>
<path d="" fill="#C1CCFA" transform="translate(0,0)"/>
<path d="" fill="#C0C8F7" transform="translate(0,0)"/>
<path d="" fill="#BDC7F6" transform="translate(0,0)"/>
<path d="" fill="#BEC8F9" transform="translate(0,0)"/>
<path d="" fill="#BDC7F8" transform="translate(0,0)"/>
<path d="" fill="#C3CCF7" transform="translate(0,0)"/>
<path d="" fill="#BFCAFA" transform="translate(0,0)"/>
<path d="" fill="#BCC9FA" transform="translate(0,0)"/>
<path d="" fill="#C4CCF9" transform="translate(0,0)"/>
<path d="" fill="#BEC9FC" transform="translate(0,0)"/>
<path d="" fill="#C0C8FA" transform="translate(0,0)"/>
<path d="" fill="#C1CBFA" transform="translate(0,0)"/>
<path d="" fill="#C1C8F7" transform="translate(0,0)"/>
<path d="" fill="#C8D0F8" transform="translate(0,0)"/>
<path d="" fill="#BBC7F8" transform="translate(0,0)"/>
<path d="" fill="#BDC8FA" transform="translate(0,0)"/>
<path d="" fill="#C7CFF9" transform="translate(0,0)"/>
<path d="" fill="#C7CEFA" transform="translate(0,0)"/>
<path d="" fill="#CAD2FC" transform="translate(0,0)"/>
<path d="" fill="#C3CBF6" transform="translate(0,0)"/>
<path d="" fill="#C4CEFC" transform="translate(0,0)"/>
<path d="" fill="#C5D1F9" transform="translate(0,0)"/>
<path d="" fill="#C2CDF7" transform="translate(0,0)"/>
<path d="" fill="#C8D2FE" transform="translate(0,0)"/>
<path d="" fill="#C4CBF9" transform="translate(0,0)"/>
<path d="" fill="#C4CDFB" transform="translate(0,0)"/>
<path d="" fill="#C4CEF8" transform="translate(0,0)"/>
<path d="" fill="#C2CEFA" transform="translate(0,0)"/>
<path d="" fill="#C6CFFB" transform="translate(0,0)"/>
<path d="" fill="#C8D1F9" transform="translate(0,0)"/>
<path d="" fill="#C1C9FA" transform="translate(0,0)"/>
<path d="" fill="#C0C9F7" transform="translate(0,0)"/>
<path d="" fill="#C5CFF8" transform="translate(0,0)"/>
<path d="" fill="#C4CCF9" transform="translate(0,0)"/>
<path d="" fill="#C3CDFA" transform="translate(0,0)"/>
<path d="" fill="#C6D0FA" transform="translate(0,0)"/>
<path d="" fill="#C7D1F8" transform="translate(0,0)"/>
<path d="" fill="#C6CEFB" transform="translate(0,0)"/>
<path d="" fill="#7188D9" transform="translate(0,0)"/>
<path d="" fill="#C9D1FB" transform="translate(0,0)"/>
<path d="" fill="#CBD1FB" transform="translate(0,0)"/>
<path d="" fill="#C6D2F9" transform="translate(0,0)"/>
<path d="" fill="#C7CDFC" transform="translate(0,0)"/>
<path d="" fill="#C9D3FE" transform="translate(0,0)"/>
<path d="" fill="#C6CFF9" transform="translate(0,0)"/>
<path d="" fill="#C6CFFB" transform="translate(0,0)"/>
<path d="" fill="#C9D1FD" transform="translate(0,0)"/>
<path d="" fill="#CBD3FB" transform="translate(0,0)"/>
<path d="" fill="#CAD1FB" transform="translate(0,0)"/>
<path d="" fill="#CBD1F8" transform="translate(0,0)"/>
<path d="" fill="#C6CEF8" transform="translate(0,0)"/>
<path d="" fill="#C6CFFC" transform="translate(0,0)"/>
<path d="" fill="#CBD0FB" transform="translate(0,0)"/>
<path d="" fill="#CBD2F9" transform="translate(0,0)"/>
<path d="" fill="#C3CDFB" transform="translate(0,0)"/>
<path d="" fill="#C8D0FB" transform="translate(0,0)"/>
<path d="" fill="#C7CFFA" transform="translate(0,0)"/>
<path d="" fill="#C9D0FA" transform="translate(0,0)"/>
<path d="" fill="#CAD1F8" transform="translate(0,0)"/>
<path d="" fill="#C6CFFA" transform="translate(0,0)"/>
<path d="" fill="#C9D2F9" transform="translate(0,0)"/>
<path d="" fill="#CCD3FB" transform="translate(0,0)"/>
<path d="" fill="#C6CEF7" transform="translate(0,0)"/>
<path d="" fill="#C2CBF8" transform="translate(0,0)"/>
<path d="" fill="#D4D9FE" transform="translate(0,0)"/>
<path d="" fill="#CCD2FE" transform="translate(0,0)"/>
<path d="" fill="#C9CFF7" transform="translate(0,0)"/>
<path d="" fill="#D8DCFE" transform="translate(0,0)"/>
<path d="" fill="#CDD4FB" transform="translate(0,0)"/>
<path d="" fill="#C9D0FA" transform="translate(0,0)"/>
<path d="" fill="#C7D0FA" transform="translate(0,0)"/>
<path d="" fill="#CDD4FB" transform="translate(0,0)"/>
<path d="" fill="#C9CEF9" transform="translate(0,0)"/>
<path d="" fill="#CED4FA" transform="translate(0,0)"/>
<path d="" fill="#D4D9FC" transform="translate(0,0)"/>
<path d="" fill="#BFCAFA" transform="translate(0,0)"/>
<path d="" fill="#CBD3FB" transform="translate(0,0)"/>
<path d="" fill="#D3D7FB" transform="translate(0,0)"/>
<path d="" fill="#C7D0FB" transform="translate(0,0)"/>
<path d="" fill="#CAD2FF" transform="translate(0,0)"/>
<path d="" fill="#CFD6FD" transform="translate(0,0)"/>
<path d="" fill="#D1D8FD" transform="translate(0,0)"/>
<path d="" fill="#C8D1FC" transform="translate(0,0)"/>
<path d="" fill="#CBD2FB" transform="translate(0,0)"/>
<path d="" fill="#CED6F9" transform="translate(0,0)"/>
<path d="" fill="#D1D8FF" transform="translate(0,0)"/>
<path d="" fill="#C8CFFA" transform="translate(0,0)"/>
<path d="" fill="#CFD7FC" transform="translate(0,0)"/>
<path d="" fill="#C8CFFB" transform="translate(0,0)"/>
<path d="" fill="#CCD3FC" transform="translate(0,0)"/>
<path d="" fill="#D3DAFD" transform="translate(0,0)"/>
<path d="" fill="#C8CEFA" transform="translate(0,0)"/>
<path d="" fill="#C6CDF8" transform="translate(0,0)"/>
<path d="" fill="#D2D7FC" transform="translate(0,0)"/>
<path d="" fill="#CCD2F7" transform="translate(0,0)"/>
<path d="" fill="#CBD0FB" transform="translate(0,0)"/>
<path d="" fill="#D3DCFA" transform="translate(0,0)"/>
<path d="" fill="#D5D8FD" transform="translate(0,0)"/>
<path d="" fill="#D1D7FB" transform="translate(0,0)"/>
<path d="" fill="#D2D7FD" transform="translate(0,0)"/>
<path d="" fill="#CAD1FA" transform="translate(0,0)"/>
<path d="" fill="#CBD6FD" transform="translate(0,0)"/>
<path d="" fill="#CED7FB" transform="translate(0,0)"/>
<path d="" fill="#CCD3FC" transform="translate(0,0)"/>
<path d="" fill="#D1D7FB" transform="translate(0,0)"/>
<path d="" fill="#D1D8FD" transform="translate(0,0)"/>
<path d="" fill="#D0D9FD" transform="translate(0,0)"/>
<path d="" fill="#CED4FE" transform="translate(0,0)"/>
<path d="" fill="#D0D6FB" transform="translate(0,0)"/>
<path d="" fill="#D1D8FA" transform="translate(0,0)"/>
<path d="" fill="#D3D7FD" transform="translate(0,0)"/>
<path d="" fill="#C6CEFA" transform="translate(0,0)"/>
<path d="" fill="#CBD4FB" transform="translate(0,0)"/>
<path d="" fill="#C0C9F9" transform="translate(0,0)"/>
<path d="" fill="#D4D8FD" transform="translate(0,0)"/>
<path d="" fill="#CFD4F9" transform="translate(0,0)"/>
<path d="" fill="#DBE2FF" transform="translate(0,0)"/>
<path d="" fill="#CED7FB" transform="translate(0,0)"/>
<path d="" fill="#D2D5FC" transform="translate(0,0)"/>
<path d="" fill="#CDD4FA" transform="translate(0,0)"/>
<path d="" fill="#D2D8FC" transform="translate(0,0)"/>
<path d="" fill="#D3DBFD" transform="translate(0,0)"/>
<path d="" fill="#D1D6FE" transform="translate(0,0)"/>
<path d="" fill="#CAD2FA" transform="translate(0,0)"/>
<path d="" fill="#CCD3FB" transform="translate(0,0)"/>
<path d="" fill="#CAD3FC" transform="translate(0,0)"/>
<path d="" fill="#BFC8F9" transform="translate(0,0)"/>
<path d="" fill="#D0D8FD" transform="translate(0,0)"/>
<path d="" fill="#CDD4FC" transform="translate(0,0)"/>
<path d="" fill="#CCD4FA" transform="translate(0,0)"/>
<path d="" fill="#CFD6FB" transform="translate(0,0)"/>
<path d="" fill="#D6DBFB" transform="translate(0,0)"/>
<path d="" fill="#D3DAFE" transform="translate(0,0)"/>
<path d="" fill="#D0D5FD" transform="translate(0,0)"/>
<path d="" fill="#D0D6FD" transform="translate(0,0)"/>
<path d="" fill="#C9D1FC" transform="translate(0,0)"/>
<path d="" fill="#D0D8FC" transform="translate(0,0)"/>
<path d="" fill="#CED5FA" transform="translate(0,0)"/>
<path d="" fill="#CFD3FB" transform="translate(0,0)"/>
<path d="" fill="#CBD3FC" transform="translate(0,0)"/>
<path d="" fill="#CDD5FA" transform="translate(0,0)"/>
<path d="" fill="#CBD2FB" transform="translate(0,0)"/>
<path d="" fill="#D6DAFD" transform="translate(0,0)"/>
<path d="" fill="#CDD5FA" transform="translate(0,0)"/>
<path d="" fill="#CBD5FB" transform="translate(0,0)"/>
<path d="" fill="#D2D9FB" transform="translate(0,0)"/>
<path d="" fill="#CBD3F9" transform="translate(0,0)"/>
<path d="" fill="#D3D8FC" transform="translate(0,0)"/>
<path d="" fill="#CDD6FE" transform="translate(0,0)"/>
<path d="" fill="#CFD3FA" transform="translate(0,0)"/>
<path d="" fill="#D0D5FB" transform="translate(0,0)"/>
<path d="" fill="#D2DBFC" transform="translate(0,0)"/>
<path d="" fill="#CED2FA" transform="translate(0,0)"/>
<path d="" fill="#D0D7FC" transform="translate(0,0)"/>
<path d="" fill="#CCD3FA" transform="translate(0,0)"/>
<path d="" fill="#CED5F9" transform="translate(0,0)"/>
<path d="" fill="#CCD3FB" transform="translate(0,0)"/>
<path d="" fill="#CED5FA" transform="translate(0,0)"/>
<path d="" fill="#D0D7FC" transform="translate(0,0)"/>
<path d="" fill="#D3D9FC" transform="translate(0,0)"/>
<path d="" fill="#C5CDF9" transform="translate(0,0)"/>
<path d="" fill="#D6DBFB" transform="translate(0,0)"/>
<path d="" fill="#D4D9FA" transform="translate(0,0)"/>
<path d="" fill="#D7DBFC" transform="translate(0,0)"/>
<path d="" fill="#DADEFB" transform="translate(0,0)"/>
<path d="" fill="#D9DFFB" transform="translate(0,0)"/>
<path d="" fill="#CFD7FD" transform="translate(0,0)"/>
<path d="" fill="#CCD4F5" transform="translate(0,0)"/>
<path d="" fill="#D1D7FB" transform="translate(0,0)"/>
<path d="" fill="#CBD1FC" transform="translate(0,0)"/>
<path d="" fill="#C7D0F9" transform="translate(0,0)"/>
<path d="" fill="#CED4FC" transform="translate(0,0)"/>
<path d="" fill="#CAD1FC" transform="translate(0,0)"/>
<path d="" fill="#D1D7F8" transform="translate(0,0)"/>
<path d="" fill="#D0D7F9" transform="translate(0,0)"/>
<path d="" fill="#C7D1FA" transform="translate(0,0)"/>
<path d="" fill="#D2DAFD" transform="translate(0,0)"/>
<path d="" fill="#CDD4F8" transform="translate(0,0)"/>
<path d="" fill="#CFD4F7" transform="translate(0,0)"/>
<path d="" fill="#D2D8F7" transform="translate(0,0)"/>
<path d="" fill="#CDD3FC" transform="translate(0,0)"/>
<path d="" fill="#CBD5FA" transform="translate(0,0)"/>
<path d="" fill="#CED3FC" transform="translate(0,0)"/>
<path d="" fill="#D3D8FD" transform="translate(0,0)"/>
<path d="" fill="#CBD1F9" transform="translate(0,0)"/>
<path d="" fill="#CDD4FA" transform="translate(0,0)"/>
<path d="" fill="#D0D7FB" transform="translate(0,0)"/>
<path d="" fill="#CAD3F8" transform="translate(0,0)"/>
<path d="" fill="#C8CFFC" transform="translate(0,0)"/>
<path d="" fill="#CFD5FD" transform="translate(0,0)"/>
<path d="" fill="#D1D7FA" transform="translate(0,0)"/>
<path d="" fill="#C4CDFB" transform="translate(0,0)"/>
<path d="" fill="#CED5FC" transform="translate(0,0)"/>
<path d="" fill="#CBD3F9" transform="translate(0,0)"/>
<path d="" fill="#CFD6FD" transform="translate(0,0)"/>
<path d="" fill="#C9D0FC" transform="translate(0,0)"/>
<path d="" fill="#C9D1FC" transform="translate(0,0)"/>
<path d="" fill="#CBD2FB" transform="translate(0,0)"/>
<path d="" fill="#C7CFFB" transform="translate(0,0)"/>
<path d="" fill="#C5CFFB" transform="translate(0,0)"/>
<path d="" fill="#CDD5FB" transform="translate(0,0)"/>
<path d="" fill="#C7CFF8" transform="translate(0,0)"/>
<path d="" fill="#CED4FE" transform="translate(0,0)"/>
<path d="" fill="#CCD3F9" transform="translate(0,0)"/>
<path d="" fill="#C8D0FA" transform="translate(0,0)"/>
<path d="" fill="#C8D0F9" transform="translate(0,0)"/>
<path d="" fill="#BFCAF7" transform="translate(0,0)"/>
<path d="" fill="#C7CEFB" transform="translate(0,0)"/>
<path d="" fill="#C9D1FA" transform="translate(0,0)"/>
<path d="" fill="#C9D0FD" transform="translate(0,0)"/>
<path d="" fill="#CBD0FA" transform="translate(0,0)"/>
<path d="" fill="#D8DBFE" transform="translate(0,0)"/>
<path d="" fill="#CBD2F9" transform="translate(0,0)"/>
<path d="" fill="#C9D0FA" transform="translate(0,0)"/>
<path d="" fill="#D3D9FC" transform="translate(0,0)"/>
<path d="" fill="#CCD2FB" transform="translate(0,0)"/>
<path d="" fill="#C5CDF9" transform="translate(0,0)"/>
<path d="" fill="#B9C5F6" transform="translate(0,0)"/>
<path d="" fill="#BCC7F8" transform="translate(0,0)"/>
<path d="" fill="#C7D0F8" transform="translate(0,0)"/>
<path d="" fill="#C7CDF7" transform="translate(0,0)"/>
<path d="" fill="#C5D0FD" transform="translate(0,0)"/>
<path d="" fill="#C7CFFA" transform="translate(0,0)"/>
<path d="" fill="#C4CEF9" transform="translate(0,0)"/>
<path d="" fill="#C8D0FA" transform="translate(0,0)"/>
<path d="" fill="#C7CFFB" transform="translate(0,0)"/>
<path d="" fill="#C9D1FA" transform="translate(0,0)"/>
<path d="" fill="#C4CDFB" transform="translate(0,0)"/>
<path d="" fill="#C4CCF9" transform="translate(0,0)"/>
<path d="" fill="#B9C4F7" transform="translate(0,0)"/>
<path d="" fill="#BEC8F7" transform="translate(0,0)"/>
<path d="" fill="#C7CFFA" transform="translate(0,0)"/>
<path d="" fill="#C8D1F8" transform="translate(0,0)"/>
<path d="" fill="#C3CCFA" transform="translate(0,0)"/>
<path d="" fill="#B5C0F7" transform="translate(0,0)"/>
<path d="" fill="#AFBCF7" transform="translate(0,0)"/>
<path d="" fill="#ACB9F6" transform="translate(0,0)"/>
<path d="" fill="#C2CAFC" transform="translate(0,0)"/>
<path d="" fill="#BDC9FB" transform="translate(0,0)"/>
<path d="" fill="#BCC7FB" transform="translate(0,0)"/>
<path d="" fill="#BBC8FA" transform="translate(0,0)"/>
<path d="" fill="#BDC7F9" transform="translate(0,0)"/>
<path d="" fill="#B3BDF5" transform="translate(0,0)"/>
<path d="" fill="#B7C1F7" transform="translate(0,0)"/>
<path d="" fill="#C0CAFA" transform="translate(0,0)"/>
<path d="" fill="#C5CCFB" transform="translate(0,0)"/>
<path d="" fill="#C0C9FC" transform="translate(0,0)"/>
<path d="" fill="#C2CBF9" transform="translate(0,0)"/>
<path d="" fill="#BEC6FA" transform="translate(0,0)"/>
<path d="" fill="#BBC6F8" transform="translate(0,0)"/>
<path d="" fill="#B5C1F9" transform="translate(0,0)"/>
<path d="" fill="#B6C3F8" transform="translate(0,0)"/>
<path d="" fill="#AFBCF6" transform="translate(0,0)"/>
<path d="" fill="#AEBDF7" transform="translate(0,0)"/>
<path d="" fill="#C5CAF9" transform="translate(0,0)"/>
<path d="" fill="#BDC7F9" transform="translate(0,0)"/>
<path d="" fill="#C3CBF7" transform="translate(0,0)"/>
<path d="" fill="#C0C9F9" transform="translate(0,0)"/>
<path d="" fill="#BFCAFA" transform="translate(0,0)"/>
<path d="" fill="#B6C2F8" transform="translate(0,0)"/>
<path d="" fill="#B8C2F9" transform="translate(0,0)"/>
<path d="" fill="#C0C9FA" transform="translate(0,0)"/>
<path d="" fill="#C1CAFA" transform="translate(0,0)"/>
<path d="" fill="#C1C9FA" transform="translate(0,0)"/>
<path d="" fill="#C0C8F9" transform="translate(0,0)"/>
<path d="" fill="#C4CAFA" transform="translate(0,0)"/>
<path d="" fill="#C0C8F8" transform="translate(0,0)"/>
<path d="" fill="#C1CAF9" transform="translate(0,0)"/>
<path d="" fill="#C4CBFB" transform="translate(0,0)"/>
<path d="" fill="#C1C9FA" transform="translate(0,0)"/>
<path d="" fill="#C6CDFC" transform="translate(0,0)"/>
<path d="" fill="#C6CCF9" transform="translate(0,0)"/>
<path d="" fill="#C5CBFA" transform="translate(0,0)"/>
<path d="" fill="#C4CCFC" transform="translate(0,0)"/>
<path d="" fill="#BEC5F7" transform="translate(0,0)"/>
<path d="" fill="#C3CAF8" transform="translate(0,0)"/>
<path d="" fill="#C3CBFA" transform="translate(0,0)"/>
<path d="" fill="#C4CBF8" transform="translate(0,0)"/>
<path d="" fill="#C4CBF9" transform="translate(0,0)"/>
<path d="" fill="#C1C9FA" transform="translate(0,0)"/>
<path d="" fill="#C0C8FB" transform="translate(0,0)"/>
<path d="" fill="#BCC4F8" transform="translate(0,0)"/>
<path d="" fill="#BDC8F7" transform="translate(0,0)"/>
<path d="" fill="#BFCAF9" transform="translate(0,0)"/>
<path d="" fill="#B2BDF5" transform="translate(0,0)"/>
<path d="" fill="#BFC9FA" transform="translate(0,0)"/>
<path d="" fill="#C2CAFA" transform="translate(0,0)"/>
<path d="" fill="#B2BFF7" transform="translate(0,0)"/>
<path d="" fill="#C0CAFB" transform="translate(0,0)"/>
<path d="" fill="#BAC3F6" transform="translate(0,0)"/>
<path d="" fill="#C2CCF9" transform="translate(0,0)"/>
<path d="" fill="#C1CAFB" transform="translate(0,0)"/>
<path d="" fill="#BDCAF9" transform="translate(0,0)"/>
<path d="" fill="#BDC8F7" transform="translate(0,0)"/>
<path d="" fill="#B0BCF6" transform="translate(0,0)"/>
<path d="" fill="#BCC6F9" transform="translate(0,0)"/>
<path d="" fill="#C3C9F9" transform="translate(0,0)"/>
<path d="" fill="#C5CFFA" transform="translate(0,0)"/>
<path d="" fill="#C1CCFD" transform="translate(0,0)"/>
<path d="" fill="#C8D1FB" transform="translate(0,0)"/>
<path d="" fill="#C3CEFC" transform="translate(0,0)"/>
<path d="" fill="#ACB7EE" transform="translate(0,0)"/>
<path d="" fill="#B0BBF3" transform="translate(0,0)"/>
<path d="" fill="#B2BEF7" transform="translate(0,0)"/>
<path d="" fill="#BAC4F5" transform="translate(0,0)"/>
<path d="" fill="#C1CAFB" transform="translate(0,0)"/>
<path d="" fill="#C3CCFA" transform="translate(0,0)"/>
<path d="" fill="#C6CCF8" transform="translate(0,0)"/>
<path d="" fill="#D0D7FE" transform="translate(0,0)"/>
<path d="" fill="#C3CDFB" transform="translate(0,0)"/>
<path d="" fill="#C3CCF7" transform="translate(0,0)"/>
<path d="" fill="#C7CFFA" transform="translate(0,0)"/>
<path d="" fill="#C7CFFA" transform="translate(0,0)"/>
<path d="" fill="#C1CCF8" transform="translate(0,0)"/>
<path d="" fill="#D0D6FD" transform="translate(0,0)"/>
<path d="" fill="#CBD0F9" transform="translate(0,0)"/>
<path d="" fill="#C4CDFB" transform="translate(0,0)"/>
<path d="" fill="#AEBCF1" transform="translate(0,0)"/>
<path d="" fill="#C7CFFD" transform="translate(0,0)"/>
<path d="" fill="#CCD3FC" transform="translate(0,0)"/>
<path d="" fill="#C0CAFC" transform="translate(0,0)"/>
<path d="" fill="#C4CBF9" transform="translate(0,0)"/>
<path d="" fill="#C4CCFB" transform="translate(0,0)"/>
<path d="" fill="#C9D0FA" transform="translate(0,0)"/>
<path d="" fill="#D0D6FC" transform="translate(0,0)"/>
<path d="" fill="#BEC9F7" transform="translate(0,0)"/>
<path d="" fill="#B6C1F5" transform="translate(0,0)"/>
<path d="" fill="#C0CAF8" transform="translate(0,0)"/>
<path d="" fill="#C6D0FA" transform="translate(0,0)"/>
<path d="" fill="#CED6FB" transform="translate(0,0)"/>
<path d="" fill="#D0D9FB" transform="translate(0,0)"/>
<path d="" fill="#CCD3FA" transform="translate(0,0)"/>
<path d="" fill="#CDD6FE" transform="translate(0,0)"/>
<path d="" fill="#ACBAF6" transform="translate(0,0)"/>
<path d="" fill="#B1BEF5" transform="translate(0,0)"/>
<path d="" fill="#AEBCF7" transform="translate(0,0)"/>
<path d="" fill="#B0BEF3" transform="translate(0,0)"/>
<path d="" fill="#C5CEF9" transform="translate(0,0)"/>
<path d="" fill="#D0D9FB" transform="translate(0,0)"/>
<path d="" fill="#CED4FD" transform="translate(0,0)"/>
<path d="" fill="#C9CFF9" transform="translate(0,0)"/>
<path d="" fill="#B3BEF3" transform="translate(0,0)"/>
<path d="" fill="#D3DAFE" transform="translate(0,0)"/>
<path d="" fill="#D4D8FB" transform="translate(0,0)"/>
<path d="" fill="#BCC7FA" transform="translate(0,0)"/>
<path d="" fill="#BAC5F8" transform="translate(0,0)"/>
<path d="" fill="#B0BCF4" transform="translate(0,0)"/>
<path d="" fill="#C3CBF7" transform="translate(0,0)"/>
<path d="" fill="#B0BCF8" transform="translate(0,0)"/>
<path d="" fill="#C2CBF9" transform="translate(0,0)"/>
<path d="" fill="#D3DAFC" transform="translate(0,0)"/>
<path d="" fill="#CFD3FA" transform="translate(0,0)"/>
<path d="" fill="#CED4F8" transform="translate(0,0)"/>
<path d="" fill="#C6CEFA" transform="translate(0,0)"/>
<path d="" fill="#CCD2F3" transform="translate(0,0)"/>
<path d="" fill="#CCD2F9" transform="translate(0,0)"/>
<path d="" fill="#C8CEF8" transform="translate(0,0)"/>
<path d="" fill="#BBC9F8" transform="translate(0,0)"/>
<path d="" fill="#C3CEFC" transform="translate(0,0)"/>
<path d="" fill="#CBD0F8" transform="translate(0,0)"/>
<path d="" fill="#C8CFF9" transform="translate(0,0)"/>
<path d="" fill="#CDD4F9" transform="translate(0,0)"/>
<path d="" fill="#D4DAFD" transform="translate(0,0)"/>
<path d="" fill="#C0CAFA" transform="translate(0,0)"/>
<path d="" fill="#CBD3F9" transform="translate(0,0)"/>
<path d="" fill="#DEE0FE" transform="translate(0,0)"/>
<path d="" fill="#BFC8F8" transform="translate(0,0)"/>
<path d="" fill="#C5CDF8" transform="translate(0,0)"/>
<path d="" fill="#C7D0F9" transform="translate(0,0)"/>
<path d="" fill="#D7DDFF" transform="translate(0,0)"/>
<path d="" fill="#CBD4FE" transform="translate(0,0)"/>
<path d="" fill="#C8D0F9" transform="translate(0,0)"/>
<path d="" fill="#C9D2F9" transform="translate(0,0)"/>
<path d="" fill="#C5CEFA" transform="translate(0,0)"/>
<path d="" fill="#CBD1F7" transform="translate(0,0)"/>
<path d="" fill="#D5DCFE" transform="translate(0,0)"/>
<path d="" fill="#CED3FA" transform="translate(0,0)"/>
<path d="" fill="#C6CFFC" transform="translate(0,0)"/>
<path d="" fill="#C2C8F5" transform="translate(0,0)"/>
<path d="" fill="#C3CEFA" transform="translate(0,0)"/>
<path d="" fill="#B1BEF5" transform="translate(0,0)"/>
<path d="" fill="#C5CCFB" transform="translate(0,0)"/>
<path d="" fill="#CFD6FE" transform="translate(0,0)"/>
<path d="" fill="#D0D6F9" transform="translate(0,0)"/>
<path d="" fill="#C2CDFA" transform="translate(0,0)"/>
<path d="" fill="#CCD3FA" transform="translate(0,0)"/>
<path d="" fill="#CFD4FD" transform="translate(0,0)"/>
<path d="" fill="#D1D7FF" transform="translate(0,0)"/>
<path d="" fill="#DBE1FF" transform="translate(0,0)"/>
<path d="" fill="#CCD2FA" transform="translate(0,0)"/>
<path d="" fill="#C7CFFE" transform="translate(0,0)"/>
<path d="" fill="#C1C9FA" transform="translate(0,0)"/>
<path d="" fill="#C5D0FE" transform="translate(0,0)"/>
<path d="" fill="#CAD1FC" transform="translate(0,0)"/>
<path d="" fill="#D4D9FA" transform="translate(0,0)"/>
<path d="" fill="#D0D7FC" transform="translate(0,0)"/>
<path d="" fill="#DCDFFF" transform="translate(0,0)"/>
<path d="" fill="#C3CAF8" transform="translate(0,0)"/>
<path d="" fill="#BEC7F7" transform="translate(0,0)"/>
<path d="" fill="#C0C9F6" transform="translate(0,0)"/>
<path d="" fill="#C0CAF9" transform="translate(0,0)"/>
<path d="" fill="#BEC9F7" transform="translate(0,0)"/>
<path d="" fill="#C3CEFA" transform="translate(0,0)"/>
<path d="" fill="#CDD4F9" transform="translate(0,0)"/>
<path d="" fill="#D2D8FC" transform="translate(0,0)"/>
<path d="" fill="#D0D6FD" transform="translate(0,0)"/>
<path d="" fill="#D9DDFD" transform="translate(0,0)"/>
<path d="" fill="#B5C0F8" transform="translate(0,0)"/>
<path d="" fill="#D2D8FE" transform="translate(0,0)"/>
<path d="" fill="#D6D9FD" transform="translate(0,0)"/>
<path d="" fill="#CFD7F9" transform="translate(0,0)"/>
<path d="" fill="#AEB7F2" transform="translate(0,0)"/>
<path d="" fill="#C9D1FA" transform="translate(0,0)"/>
<path d="" fill="#D9DEFD" transform="translate(0,0)"/>
<path d="" fill="#DCDEFE" transform="translate(0,0)"/>
<path d="" fill="#D7DAFD" transform="translate(0,0)"/>
<path d="" fill="#E1E1FF" transform="translate(0,0)"/>
<path d="" fill="#C8D2F8" transform="translate(0,0)"/>
<path d="" fill="#D1D7FB" transform="translate(0,0)"/>
<path d="" fill="#D4D7F7" transform="translate(0,0)"/>
<path d="" fill="#D9DCFE" transform="translate(0,0)"/>
<path d="" fill="#CAD3FB" transform="translate(0,0)"/>
<path d="" fill="#D9DBFF" transform="translate(0,0)"/>
<path d="" fill="#D6DCFD" transform="translate(0,0)"/>
<path d="" fill="#AFB8F3" transform="translate(0,0)"/>
<path d="" fill="#C2CEF9" transform="translate(0,0)"/>
<path d="" fill="#D5DBFC" transform="translate(0,0)"/>
<path d="" fill="#CFD5FD" transform="translate(0,0)"/>
<path d="" fill="#CED6FD" transform="translate(0,0)"/>
<path d="" fill="#D0D5FC" transform="translate(0,0)"/>
<path d="" fill="#CCD3F9" transform="translate(0,0)"/>
<path d="" fill="#D4D9FC" transform="translate(0,0)"/>
<path d="" fill="#D1D6FD" transform="translate(0,0)"/>
<path d="" fill="#D9DEFE" transform="translate(0,0)"/>
<path d="" fill="#CFD6FA" transform="translate(0,0)"/>
<path d="" fill="#D2D8FA" transform="translate(0,0)"/>
<path d="" fill="#C8D1F9" transform="translate(0,0)"/>
<path d="" fill="#CAD2FA" transform="translate(0,0)"/>
<path d="" fill="#CCD3FB" transform="translate(0,0)"/>
<path d="" fill="#DCE0FF" transform="translate(0,0)"/>
<path d="" fill="#D3D8FC" transform="translate(0,0)"/>
<path d="" fill="#D3D7FC" transform="translate(0,0)"/>
<path d="" fill="#CAD1F8" transform="translate(0,0)"/>
<path d="" fill="#D7DEFF" transform="translate(0,0)"/>
<path d="" fill="#D5D9FB" transform="translate(0,0)"/>
<path d="" fill="#CCD3FA" transform="translate(0,0)"/>
<path d="" fill="#C5CEF8" transform="translate(0,0)"/>
<path d="" fill="#D1D6FA" transform="translate(0,0)"/>
<path d="" fill="#D0D6FD" transform="translate(0,0)"/>
<path d="" fill="#ACB7F1" transform="translate(0,0)"/>
<path d="" fill="#DADDFE" transform="translate(0,0)"/>
<path d="" fill="#E0E2FF" transform="translate(0,0)"/>
<path d="" fill="#B7C2F4" transform="translate(0,0)"/>
<path d="" fill="#C9D1F9" transform="translate(0,0)"/>
<path d="" fill="#D9DDFC" transform="translate(0,0)"/>
<path d="" fill="#C5CCF9" transform="translate(0,0)"/>
<path d="" fill="#E0E0FF" transform="translate(0,0)"/>
<path d="" fill="#C9D2FB" transform="translate(0,0)"/>
<path d="" fill="#CBD2FA" transform="translate(0,0)"/>
<path d="" fill="#D8DAFE" transform="translate(0,0)"/>
<path d="" fill="#D7DBFC" transform="translate(0,0)"/>
<path d="" fill="#B4C1F5" transform="translate(0,0)"/>
<path d="" fill="#B5BFF6" transform="translate(0,0)"/>
<path d="" fill="#DFDFFC" transform="translate(0,0)"/>
<path d="" fill="#DBDCF9" transform="translate(0,0)"/>
<path d="" fill="#CCD5FA" transform="translate(0,0)"/>
<path d="" fill="#CAD2FB" transform="translate(0,0)"/>
<path d="" fill="#CCD4FC" transform="translate(0,0)"/>
<path d="" fill="#D8DDFF" transform="translate(0,0)"/>
<path d="" fill="#D3D7FC" transform="translate(0,0)"/>
<path d="" fill="#D1D5FA" transform="translate(0,0)"/>
<path d="" fill="#B0BBF1" transform="translate(0,0)"/>
<path d="" fill="#C2C7F4" transform="translate(0,0)"/>
<path d="" fill="#D4DBFC" transform="translate(0,0)"/>
<path d="" fill="#D8DCFD" transform="translate(0,0)"/>
<path d="" fill="#B1BCF1" transform="translate(0,0)"/>
<path d="" fill="#D4D9FD" transform="translate(0,0)"/>
<path d="" fill="#D5DDFD" transform="translate(0,0)"/>
<path d="" fill="#CDD2F8" transform="translate(0,0)"/>
<path d="" fill="#B2BCF5" transform="translate(0,0)"/>
<path d="" fill="#D4D8FA" transform="translate(0,0)"/>
<path d="" fill="#CED5FA" transform="translate(0,0)"/>
<path d="" fill="#D8DCFD" transform="translate(0,0)"/>
<path d="" fill="#C1CAF7" transform="translate(0,0)"/>
<path d="" fill="#B5BFF4" transform="translate(0,0)"/>
<path d="" fill="#BCC3F7" transform="translate(0,0)"/>
<path d="" fill="#BFC8FB" transform="translate(0,0)"/>
<path d="" fill="#D2D9FB" transform="translate(0,0)"/>
<path d="" fill="#B3BEF2" transform="translate(0,0)"/>
<path d="" fill="#BAC3F5" transform="translate(0,0)"/>
<path d="" fill="#BEC7F8" transform="translate(0,0)"/>
<path d="" fill="#C0CCF9" transform="translate(0,0)"/>
<path d="" fill="#C3CBFA" transform="translate(0,0)"/>
<path d="" fill="#C1CCFA" transform="translate(0,0)"/>
<path d="" fill="#D3D9FB" transform="translate(0,0)"/>
<path d="" fill="#CED5FA" transform="translate(0,0)"/>
<path d="" fill="#C4CAF9" transform="translate(0,0)"/>
<path d="" fill="#BFC7F9" transform="translate(0,0)"/>
<path d="" fill="#C2C9F6" transform="translate(0,0)"/>
<path d="" fill="#C0C9F8" transform="translate(0,0)"/>
<path d="" fill="#D8DCFE" transform="translate(0,0)"/>
<path d="" fill="#C1CCFC" transform="translate(0,0)"/>
<path d="" fill="#C6CDF6" transform="translate(0,0)"/>
<path d="" fill="#DFE0FF" transform="translate(0,0)"/>
<path d="" fill="#D4D8FE" transform="translate(0,0)"/>
<path d="" fill="#C3CAF9" transform="translate(0,0)"/>
<path d="" fill="#CFD4FB" transform="translate(0,0)"/>
<path d="" fill="#CDD5FB" transform="translate(0,0)"/>
<path d="" fill="#DBE2FF" transform="translate(0,0)"/>
<path d="" fill="#C8D1FB" transform="translate(0,0)"/>
<path d="" fill="#C4CCFB" transform="translate(0,0)"/>
<path d="" fill="#B1BCF5" transform="translate(0,0)"/>
<path d="" fill="#ACB7F3" transform="translate(0,0)"/>
<path d="" fill="#CED5FA" transform="translate(0,0)"/>
<path d="" fill="#D2D8FA" transform="translate(0,0)"/>
<path d="" fill="#CFD6FC" transform="translate(0,0)"/>
<path d="" fill="#CCD3FC" transform="translate(0,0)"/>
<path d="" fill="#CFD7F9" transform="translate(0,0)"/>
<path d="" fill="#CCD5FC" transform="translate(0,0)"/>
<path d="" fill="#C6CFF7" transform="translate(0,0)"/>
<path d="" fill="#DCDDFC" transform="translate(0,0)"/>
<path d="" fill="#CCD2FB" transform="translate(0,0)"/>
<path d="" fill="#D2DBFD" transform="translate(0,0)"/>
<path d="" fill="#C5CEFD" transform="translate(0,0)"/>
<path d="" fill="#DBDFFC" transform="translate(0,0)"/>
<path d="" fill="#DDDFFD" transform="translate(0,0)"/>
<path d="" fill="#D2D7FC" transform="translate(0,0)"/>
<path d="" fill="#DBDFFD" transform="translate(0,0)"/>
<path d="" fill="#CED4FD" transform="translate(0,0)"/>
<path d="" fill="#C9D0FB" transform="translate(0,0)"/>
<path d="" fill="#C9D3FB" transform="translate(0,0)"/>
<path d="" fill="#CCD3FC" transform="translate(0,0)"/>
<path d="" fill="#CFD4FC" transform="translate(0,0)"/>
<path d="" fill="#D0D7FA" transform="translate(0,0)"/>
<path d="" fill="#CBD1FA" transform="translate(0,0)"/>
<path d="" fill="#DCDEFA" transform="translate(0,0)"/>
<path d="" fill="#CBD2FC" transform="translate(0,0)"/>
<path d="" fill="#C7CEFA" transform="translate(0,0)"/>
<path d="" fill="#D4DAFB" transform="translate(0,0)"/>
<path d="" fill="#CFD5FC" transform="translate(0,0)"/>
<path d="" fill="#D5DBFB" transform="translate(0,0)"/>
<path d="" fill="#B8C1F2" transform="translate(0,0)"/>
<path d="" fill="#C4CEF8" transform="translate(0,0)"/>
<path d="" fill="#CBD5FA" transform="translate(0,0)"/>
<path d="" fill="#CCD2F7" transform="translate(0,0)"/>
<path d="" fill="#CAD1FD" transform="translate(0,0)"/>
<path d="" fill="#CBD2F9" transform="translate(0,0)"/>
<path d="" fill="#D2D8FD" transform="translate(0,0)"/>
<path d="" fill="#D0D7FC" transform="translate(0,0)"/>
<path d="" fill="#CBD3FD" transform="translate(0,0)"/>
<path d="" fill="#CBD3FD" transform="translate(0,0)"/>
<path d="" fill="#CFD7FC" transform="translate(0,0)"/>
<path d="" fill="#C5CEFB" transform="translate(0,0)"/>
<path d="" fill="#DEE2FC" transform="translate(0,0)"/>
<path d="" fill="#CAD3FA" transform="translate(0,0)"/>
<path d="" fill="#C9D2FC" transform="translate(0,0)"/>
<path d="" fill="#C7CFF9" transform="translate(0,0)"/>
<path d="" fill="#C6CEF6" transform="translate(0,0)"/>
<path d="" fill="#C7CEFA" transform="translate(0,0)"/>
<path d="" fill="#C8D2FA" transform="translate(0,0)"/>
<path d="" fill="#CAD1FA" transform="translate(0,0)"/>
<path d="" fill="#CDD4FB" transform="translate(0,0)"/>
<path d="" fill="#B1BDF1" transform="translate(0,0)"/>
<path d="" fill="#C7CBF7" transform="translate(0,0)"/>
<path d="" fill="#C1C9F6" transform="translate(0,0)"/>
<path d="" fill="#C5CEFB" transform="translate(0,0)"/>
<path d="" fill="#CED5FA" transform="translate(0,0)"/>
<path d="" fill="#C8D1FA" transform="translate(0,0)"/>
<path d="" fill="#C8CEF9" transform="translate(0,0)"/>
<path d="" fill="#CDD1FA" transform="translate(0,0)"/>
<path d="" fill="#D2D7FD" transform="translate(0,0)"/>
<path d="" fill="#D3D7FC" transform="translate(0,0)"/>
<path d="" fill="#B2BDF3" transform="translate(0,0)"/>
<path d="" fill="#CBD3FB" transform="translate(0,0)"/>
<path d="" fill="#B2BCF5" transform="translate(0,0)"/>
<path d="" fill="#B1BEF5" transform="translate(0,0)"/>
<path d="" fill="#C9D2F9" transform="translate(0,0)"/>
<path d="" fill="#D4D9FC" transform="translate(0,0)"/>
<path d="" fill="#C2CBF6" transform="translate(0,0)"/>
<path d="" fill="#CAD2FC" transform="translate(0,0)"/>
<path d="" fill="#D3D7FC" transform="translate(0,0)"/>
<path d="" fill="#C9CEFA" transform="translate(0,0)"/>
<path d="" fill="#C5CFF8" transform="translate(0,0)"/>
<path d="" fill="#D0D4FA" transform="translate(0,0)"/>
<path d="" fill="#CBD2F8" transform="translate(0,0)"/>
<path d="" fill="#D6DBF9" transform="translate(0,0)"/>
<path d="" fill="#C3CBF8" transform="translate(0,0)"/>
<path d="" fill="#C1CAF7" transform="translate(0,0)"/>
<path d="" fill="#BBC4F4" transform="translate(0,0)"/>
<path d="" fill="#B4BEF0" transform="translate(0,0)"/>
<path d="" fill="#CFD7FC" transform="translate(0,0)"/>
<path d="" fill="#C5CCFB" transform="translate(0,0)"/>
<path d="" fill="#C7CEF8" transform="translate(0,0)"/>
<path d="" fill="#C9D0F5" transform="translate(0,0)"/>
<path d="" fill="#C2CCF9" transform="translate(0,0)"/>
<path d="" fill="#BDC8F5" transform="translate(0,0)"/>
<path d="" fill="#D8DEFC" transform="translate(0,0)"/>
<path d="" fill="#C7CDF7" transform="translate(0,0)"/>
<path d="" fill="#C8D1F7" transform="translate(0,0)"/>
<path d="" fill="#D1D7FA" transform="translate(0,0)"/>
<path d="" fill="#C2CCF8" transform="translate(0,0)"/>
<path d="" fill="#D0D5F9" transform="translate(0,0)"/>
<path d="" fill="#C9CFFA" transform="translate(0,0)"/>
<path d="" fill="#BDC8F5" transform="translate(0,0)"/>
<path d="" fill="#D1D5F9" transform="translate(0,0)"/>
<path d="" fill="#C6CFFA" transform="translate(0,0)"/>
<path d="" fill="#BDC7F6" transform="translate(0,0)"/>
<path d="" fill="#B6C3F2" transform="translate(0,0)"/>
<path d="" fill="#C8CFF7" transform="translate(0,0)"/>
<path d="" fill="#C2C9F6" transform="translate(0,0)"/>
<path d="" fill="#CDD6FD" transform="translate(0,0)"/>
<path d="" fill="#CED4FA" transform="translate(0,0)"/>
<path d="" fill="#D5DAFE" transform="translate(0,0)"/>
<path d="" fill="#C7D0F5" transform="translate(0,0)"/>
<path d="" fill="#C4CCF7" transform="translate(0,0)"/>
<path d="" fill="#BCC5F4" transform="translate(0,0)"/>
<path d="" fill="#B7C3F4" transform="translate(0,0)"/>
<path d="" fill="#C0C8F5" transform="translate(0,0)"/>
<path d="" fill="#C3CDF7" transform="translate(0,0)"/>
<path d="" fill="#C0C8F5" transform="translate(0,0)"/>
<path d="" fill="#BAC4F5" transform="translate(0,0)"/>
<path d="" fill="#C1C7F6" transform="translate(0,0)"/>
<path d="" fill="#C3CDF9" transform="translate(0,0)"/>
<path d="" fill="#BFC8F8" transform="translate(0,0)"/>
<path d="" fill="#B4BEF3" transform="translate(0,0)"/>
<path d="" fill="#BBC5F3" transform="translate(0,0)"/>
<path d="" fill="#C0CAF6" transform="translate(0,0)"/>
<path d="" fill="#B9C5F4" transform="translate(0,0)"/>
<path d="" fill="#B3BCF1" transform="translate(0,0)"/>
<path d="" fill="#B7C0F3" transform="translate(0,0)"/>
<path d="" fill="#BCC5F3" transform="translate(0,0)"/>
<path d="" fill="#BEC5F5" transform="translate(0,0)"/>
<path d="" fill="#BAC3F3" transform="translate(0,0)"/>
<path d="" fill="#BDC7F5" transform="translate(0,0)"/>
<path d="" fill="#BDC6F7" transform="translate(0,0)"/>
<path d="" fill="#BDC5F4" transform="translate(0,0)"/>
<path d="" fill="#BEC7F5" transform="translate(0,0)"/>
<path d="" fill="#BCC7F5" transform="translate(0,0)"/>
<path d="" fill="#B8C1F3" transform="translate(0,0)"/>
<path d="" fill="#B8C2F4" transform="translate(0,0)"/>
<path d="" fill="#B5BFF4" transform="translate(0,0)"/>
<path d="" fill="#BAC2F5" transform="translate(0,0)"/>
<path d="" fill="#BBC4F5" transform="translate(0,0)"/>
<path d="" fill="#C0C9F5" transform="translate(0,0)"/>
<path d="" fill="#B9C3F2" transform="translate(0,0)"/>
<path d="" fill="#BAC4F4" transform="translate(0,0)"/>
<path d="" fill="#B8C4F4" transform="translate(0,0)"/>
<path d="" fill="#B9C5F6" transform="translate(0,0)"/>
<path d="" fill="#BDC7F5" transform="translate(0,0)"/>
<path d="" fill="#C2CAF5" transform="translate(0,0)"/>
<path d="" fill="#BFC7F5" transform="translate(0,0)"/>
<path d="" fill="#C1C8F5" transform="translate(0,0)"/>
<path d="" fill="#B8C4F7" transform="translate(0,0)"/>
<path d="" fill="#B8C5F5" transform="translate(0,0)"/>
<path d="" fill="#B8C5F1" transform="translate(0,0)"/>
<path d="" fill="#B4BFF3" transform="translate(0,0)"/>
<path d="" fill="#B5BFF3" transform="translate(0,0)"/>
</svg>
