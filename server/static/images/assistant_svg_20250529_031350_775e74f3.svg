<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: visioncortex VTracer 0.6.4 -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="1024" height="1024">
<path d="M0 0 C9.57 0.33 19.14 0.66 29 1 C29.88 2.905 30.761 4.81 31.668 6.773 C32.538 8.654 33.409 10.534 34.28 12.414 C34.876 13.703 35.473 14.993 36.069 16.283 C39.594 23.916 43.176 31.51 47 39 C47.454 38.526 47.908 38.051 48.375 37.562 C50 36 50 36 52 35 C52.751 33.212 52.751 33.212 53.344 31.043 C54.964 25.899 57.146 21.156 59.5 16.312 C59.891 15.497 60.281 14.682 60.684 13.842 C62.749 9.54 64.865 5.269 67 1 C77.23 1 87.46 1 98 1 C98.045 13.222 98.082 25.443 98.104 37.665 C98.114 43.339 98.128 49.014 98.151 54.688 C98.173 60.16 98.185 65.633 98.19 71.105 C98.193 73.197 98.201 75.288 98.211 77.38 C98.226 80.301 98.228 83.223 98.227 86.144 C98.234 87.016 98.241 87.888 98.249 88.786 C98.228 94.772 98.228 94.772 96 97 C95.67 96.01 95.34 95.02 95 94 C88.07 94 81.14 94 74 94 C73.67 77.17 73.34 60.34 73 43 C71.389 44.611 70.678 46.656 69.707 48.707 C69.013 50.169 69.013 50.169 68.304 51.66 C67.812 52.7 67.32 53.741 66.812 54.812 C55.583 78.417 55.583 78.417 54 80 C54.99 80.495 54.99 80.495 56 81 C55.67 81.66 55.34 82.32 55 83 C53.36 82.722 53.36 82.722 51.688 82.438 C48.064 81.742 48.064 81.742 45 83 C44.01 82.34 43.02 81.68 42 81 C42 80.34 42 79.68 42 79 C41.47 77.773 40.891 76.567 40.285 75.375 C39.929 74.669 39.572 73.962 39.205 73.234 C38.828 72.497 38.451 71.76 38.062 71 C37.689 70.263 37.316 69.525 36.932 68.766 C36.239 67.398 35.544 66.03 34.848 64.664 C34.227 63.445 33.612 62.223 33 61 C32.01 60.67 31.02 60.34 30 60 C28.603 58.044 27.262 56.046 26 54 C26.01 55.135 26.021 56.271 26.032 57.441 C26.068 61.651 26.091 65.861 26.11 70.071 C26.12 71.893 26.134 73.716 26.151 75.538 C26.175 78.157 26.186 80.776 26.195 83.395 C26.206 84.21 26.216 85.026 26.227 85.866 C26.227 89.771 26.217 91.675 24 95 C21.785 95.325 21.785 95.325 19.062 95.258 C17.597 95.237 17.597 95.237 16.102 95.215 C15.078 95.185 14.055 95.156 13 95.125 C10.979 95.082 8.958 95.042 6.938 95.008 C6.039 94.983 5.141 94.959 4.215 94.934 C1.958 94.846 1.958 94.846 0 96 C0 95.34 0 94.68 0 94 C-0.66 93.67 -1.32 93.34 -2 93 C-1.505 92.505 -1.505 92.505 -1 92 C-0.875 89.14 -0.814 86.303 -0.795 83.441 C-0.785 82.544 -0.775 81.646 -0.765 80.722 C-0.733 77.74 -0.708 74.759 -0.684 71.777 C-0.663 69.716 -0.642 67.655 -0.621 65.594 C-0.565 60.157 -0.516 54.72 -0.468 49.284 C-0.418 43.74 -0.362 38.197 -0.307 32.654 C-0.199 21.77 -0.098 10.885 0 0 Z M25 44 C25 46.31 25 48.62 25 51 C26.32 51.33 27.64 51.66 29 52 C28.01 49.36 27.02 46.72 26 44 C25.67 44 25.34 44 25 44 Z M29 53 C30 55 30 55 30 55 Z M30 55 C31 57 31 57 31 57 Z M31 57 C32 59 32 59 32 59 Z " fill="#D1766A" transform="translate(446,300)"/>
<path d="M0 0 C26 1 26 1 31.398 5.645 C33.011 8.609 33.011 8.609 34.375 11.812 C35.211 13.419 35.211 13.419 36.063 15.059 C37.207 17.258 38.314 19.477 39.383 21.713 C41.902 26.833 44.892 31.642 47.875 36.5 C49.26 38.847 50.644 41.195 52.023 43.545 C52.345 44.025 52.668 44.505 53 45 C53.33 45 53.66 45 54 45 C53.995 43.905 53.99 42.811 53.984 41.683 C53.966 37.611 53.955 33.539 53.945 29.467 C53.94 27.706 53.933 25.946 53.925 24.185 C53.912 21.651 53.907 19.116 53.902 16.582 C53.897 15.799 53.892 15.015 53.887 14.208 C53.886 10.344 54.056 6.755 55 3 C54.01 2.67 53.02 2.34 52 2 C55.111 0.963 57.495 0.8 60.762 0.684 C61.88 0.642 62.998 0.6 64.15 0.557 C65.901 0.498 65.901 0.498 67.688 0.438 C68.866 0.394 70.045 0.351 71.26 0.307 C74.173 0.2 77.086 0.098 80 0 C79.996 1.094 79.991 2.187 79.987 3.314 C79.946 13.614 79.916 23.914 79.896 34.214 C79.886 39.51 79.872 44.805 79.849 50.101 C79.827 55.21 79.815 60.319 79.81 65.428 C79.807 67.379 79.799 69.329 79.789 71.28 C79.774 74.009 79.772 76.738 79.773 79.468 C79.762 80.68 79.762 80.68 79.751 81.918 C79.765 85.752 79.818 88.754 82 92 C77.647 92.292 73.297 92.468 68.938 92.625 C67.708 92.709 66.479 92.793 65.213 92.879 C56.205 93.122 56.205 93.122 52.637 89.8 C50.882 87.27 49.427 84.724 48 82 C47.01 81.01 47.01 81.01 46 80 C45.34 80 44.68 80 44 80 C44 79.34 44 78.68 44 78 C43.01 77.505 43.01 77.505 42 77 C42.071 76.296 42.142 75.592 42.215 74.867 C41.936 71.146 40.598 69.243 38.562 66.125 C35.348 61.156 32.245 56.137 29.215 51.055 C28.806 50.371 28.397 49.688 27.976 48.984 C26.983 47.323 25.992 45.662 25 44 C24.975 50.514 24.957 57.029 24.945 63.543 C24.94 65.761 24.933 67.978 24.925 70.196 C24.912 73.378 24.907 76.56 24.902 79.742 C24.897 80.738 24.892 81.734 24.887 82.76 C24.887 83.681 24.887 84.602 24.886 85.55 C24.883 86.769 24.883 86.769 24.88 88.013 C24.874 90.075 24.874 90.075 26 92 C17.42 92 8.84 92 0 92 C0 61.64 0 31.28 0 0 Z M44 75 C45 77 45 77 45 77 Z " fill="#CB766A" transform="translate(614,428)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.99 2.475 3.99 2.475 5 5 C4.67 5.33 4.34 5.66 4 6 C3.899 8.697 3.86 11.369 3.867 14.066 C3.866 15.306 3.866 15.306 3.864 16.57 C3.864 18.333 3.865 20.096 3.87 21.859 C3.875 24.499 3.87 27.139 3.863 29.779 C2.785 46.851 2.785 46.851 6 63 C5.34 63 4.68 63 4 63 C4.33 73.89 4.66 84.78 5 96 C4.34 96 3.68 96 3 96 C3 96.66 3 97.32 3 98 C1.515 98.495 1.515 98.495 0 99 C-0.33 98.34 -0.66 97.68 -1 97 C-3.549 96.688 -6.004 96.487 -8.562 96.375 C-9.273 96.336 -9.984 96.298 -10.717 96.258 C-12.477 96.163 -14.239 96.081 -16 96 C-16 96.66 -16 97.32 -16 98 C-19.795 96.735 -20.143 95.483 -22.062 92.062 C-24.196 88.279 -24.196 88.279 -27 85 C-27.822 83.537 -28.607 82.054 -29.375 80.562 C-32.797 74.166 -36.673 68.088 -40.597 61.99 C-41.34 60.831 -41.34 60.831 -42.098 59.648 C-42.536 58.97 -42.974 58.291 -43.426 57.591 C-45.732 53.795 -47.843 49.883 -50 46 C-50.33 61.84 -50.66 77.68 -51 94 C-50.34 94.33 -49.68 94.66 -49 95 C-49.99 95.495 -49.99 95.495 -51 96 C-52.134 98.017 -52.134 98.017 -53 100 C-53.495 99.01 -53.495 99.01 -54 98 C-53.67 97.34 -53.34 96.68 -53 96 C-63.89 96.495 -63.89 96.495 -75 97 C-75 65.98 -75 34.96 -75 3 C-50 3 -50 3 -44.227 8.527 C-43.005 10.432 -43.005 10.432 -42.297 12.123 C-41.88 13.067 -41.88 13.067 -41.455 14.029 C-41.181 14.659 -40.907 15.289 -40.625 15.938 C-40.022 17.147 -39.41 18.352 -38.789 19.553 C-36.84 23.359 -34.921 27.18 -33 31 C-29.37 36.61 -25.74 42.22 -22 48 C-22 33.15 -22 18.3 -22 3 C-17.432 2.086 -13.301 1.884 -8.688 1.938 C-7.947 1.942 -7.206 1.947 -6.443 1.951 C-4.629 1.963 -2.814 1.981 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z M-22 49 C-21 51 -21 51 -21 51 Z " fill="#D3786B" transform="translate(673,298)"/>
<path d="M0 0 C11.307 7.849 18.045 18.069 21.344 31.305 C23.678 46.398 20.784 60.528 11.746 72.906 C9.243 75.495 6.598 77.717 3.746 79.906 C3.086 80.566 2.426 81.226 1.746 81.906 C-12.778 89.575 -26.164 92.21 -42.254 87.906 C-42.918 87.735 -43.582 87.563 -44.266 87.387 C-54.882 84.049 -63.252 76.233 -68.5 66.625 C-69.459 64.739 -70.363 62.825 -71.254 60.906 C-71.914 60.906 -72.574 60.906 -73.254 60.906 C-73.584 59.916 -73.914 58.926 -74.254 57.906 C-73.594 57.906 -72.934 57.906 -72.254 57.906 C-72.571 57.263 -72.888 56.62 -73.215 55.957 C-74.568 51.983 -74.711 48.264 -74.879 44.094 C-74.937 42.911 -74.937 42.911 -74.996 41.705 C-75.09 39.772 -75.173 37.839 -75.254 35.906 C-75.914 35.576 -76.574 35.246 -77.254 34.906 C-75.274 34.411 -75.274 34.411 -73.254 33.906 C-73.213 32.669 -73.171 31.431 -73.129 30.156 C-72.036 20.517 -66.429 11.307 -59.254 4.906 C-42.517 -8.177 -18.62 -10.776 0 0 Z M-43.629 23.031 C-48.501 29.351 -49.997 37.1 -49.254 44.906 C-48.418 47.976 -47.407 50.942 -46.254 53.906 C-45.264 53.906 -44.274 53.906 -43.254 53.906 C-43.584 55.226 -43.914 56.546 -44.254 57.906 C-43.264 58.566 -42.274 59.226 -41.254 59.906 C-40.573 60.731 -39.893 61.556 -39.191 62.406 C-35.15 65.846 -31.496 66.61 -26.254 66.906 C-18.23 66.171 -12.532 63.018 -7.254 56.906 C-5.191 52.851 -4.254 50.478 -4.254 45.906 C-4.914 45.906 -5.574 45.906 -6.254 45.906 C-5.879 43.969 -5.879 43.969 -5.254 41.906 C-4.594 41.576 -3.934 41.246 -3.254 40.906 C-3.749 39.421 -3.749 39.421 -4.254 37.906 C-4.171 37.019 -4.089 36.132 -4.004 35.219 C-4.355 30.569 -6.601 27.64 -9.254 23.906 C-9.914 24.896 -10.574 25.886 -11.254 26.906 C-11.254 26.246 -11.254 25.586 -11.254 24.906 C-11.914 24.906 -12.574 24.906 -13.254 24.906 C-13.522 23.937 -13.79 22.967 -14.066 21.969 C-14.654 20.453 -14.654 20.453 -15.254 18.906 C-16.244 18.576 -17.234 18.246 -18.254 17.906 C-18.254 18.896 -18.254 19.886 -18.254 20.906 C-18.914 20.576 -19.574 20.246 -20.254 19.906 C-19.924 18.916 -19.594 17.926 -19.254 16.906 C-28.163 13.937 -37.19 16.289 -43.629 23.031 Z M-12.254 21.906 C-12.254 22.566 -12.254 23.226 -12.254 23.906 C-11.594 23.576 -10.934 23.246 -10.254 22.906 C-10.914 22.576 -11.574 22.246 -12.254 21.906 Z " fill="#C37166" transform="translate(414.25390625,306.09375)"/>
<path d="M0 0 C6.918 6.606 12.396 16.398 13.275 26.004 C13.303 29.378 13.322 32.751 13.312 36.125 C13.329 37.304 13.345 38.484 13.361 39.699 C13.362 40.832 13.363 41.965 13.363 43.133 C13.366 44.163 13.369 45.193 13.372 46.254 C12.948 49.384 12.059 50.662 10 53 C9.213 54.354 8.461 55.729 7.75 57.125 C7.394 57.808 7.038 58.491 6.672 59.195 C6.45 59.791 6.228 60.386 6 61 C6.33 61.66 6.66 62.32 7 63 C6.237 63.413 5.474 63.825 4.688 64.25 C2.018 65.988 0.863 67.489 -1 70 C-2.322 70.689 -3.657 71.353 -5 72 C-6.236 72.802 -7.465 73.615 -8.688 74.438 C-20.255 81.74 -34.383 82.519 -47.562 79.938 C-57.83 77.284 -67.617 71.605 -74 63 C-81.801 49.417 -82.298 37.21 -82 22 C-81.34 22 -80.68 22 -80 22 C-79.83 20.679 -79.83 20.679 -79.656 19.332 C-77.789 9.85 -71.494 1.843 -64 -4 C-43.523 -17.37 -18.41 -15.772 0 0 Z M-50.812 16.75 C-56.061 24.548 -57.065 31.71 -56 41 C-54.99 44.22 -53.668 47.073 -52 50 C-51.67 50.66 -51.34 51.32 -51 52 C-50.34 52 -49.68 52 -49 52 C-48.67 52.99 -48.34 53.98 -48 55 C-41.334 58.537 -35.226 59.815 -27.875 57.875 C-22.279 55.984 -18.791 53.431 -15.25 48.625 C-12.633 43.13 -11.532 38.085 -12 32 C-12.66 31.01 -13.32 30.02 -14 29 C-13.722 28.103 -13.722 28.103 -13.438 27.188 C-12.748 23.738 -14.205 21.933 -16 19 C-18.313 16.103 -18.313 16.103 -21 14 C-22.32 14 -23.64 14 -25 14 C-24.67 13.34 -24.34 12.68 -24 12 C-33.813 7.58 -43.226 9.332 -50.812 16.75 Z " fill="#C37268" transform="translate(490,441)"/>
<path d="M0 0 C2.117 1.704 2.117 1.704 5.129 1.426 C5.665 2.395 6.201 3.365 6.754 4.363 C8.415 7.302 10.248 10.042 12.191 12.801 C18.925 22.781 20.454 34.556 18.48 46.277 C15.778 59.263 9.938 68.461 -0.871 76.426 C-11.011 82.83 -21.998 85.057 -33.871 84.426 C-34.201 85.086 -34.531 85.746 -34.871 86.426 C-35.201 85.766 -35.531 85.106 -35.871 84.426 C-37.664 83.915 -39.483 83.492 -41.309 83.113 C-47.38 81.695 -52.432 79.411 -57.871 76.426 C-60.223 75.202 -62.341 74.269 -64.871 73.426 C-64.912 72.766 -64.954 72.106 -64.996 71.426 C-65.879 68.399 -66.732 67.189 -68.684 64.801 C-76.006 55.524 -77.631 43.908 -76.871 32.426 C-76.133 27.692 -75.295 23.005 -73.871 18.426 C-73.211 18.096 -72.551 17.766 -71.871 17.426 C-71.438 16.343 -71.438 16.343 -70.996 15.238 C-66.696 4.488 -58.122 -1.939 -47.871 -6.574 C-31.917 -12.418 -13.352 -10.352 0 0 Z M-46.246 21.113 C-50.518 27.371 -51.762 34.95 -50.871 42.426 C-49.513 49.437 -46.391 54.849 -40.871 59.426 C-34.787 62.891 -28.588 62.96 -21.871 61.426 C-18.384 60.021 -18.384 60.021 -15.871 58.426 C-15.871 57.766 -15.871 57.106 -15.871 56.426 C-15.256 56.168 -14.641 55.91 -14.008 55.645 C-10.363 53.566 -9.22 50.28 -7.871 46.426 C-6.41 39.324 -5.848 30.779 -9.871 24.426 C-10.737 22.941 -10.737 22.941 -11.621 21.426 C-14.887 17.072 -18.524 14.46 -23.871 13.426 C-33.2 12.542 -40.097 13.69 -46.246 21.113 Z " fill="#CA756A" transform="translate(585.87109375,437.57421875)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C3.972 0.977 5.945 0.948 7.916 0.883 C21.141 0.494 32.951 2.668 43 12 C44.898 14.473 44.898 14.473 46 17 C45.092 21.342 40.979 23.745 37.625 26.312 C36.759 27.013 36.759 27.013 35.875 27.729 C31.58 31.048 31.58 31.048 28.793 30.816 C28.201 30.547 27.61 30.278 27 30 C25.907 29.876 24.814 29.752 23.688 29.625 C19.648 28.94 16.689 27.732 13 26 C13 25.34 13 24.68 13 24 C6.654 24.255 0.901 24.611 -4 29 C-10.726 37.665 -11.226 47.373 -10 58 C-8.785 61.709 -7.243 64.812 -5 68 C-3.35 68 -1.7 68 0 68 C0 68.99 0 69.98 0 71 C8.609 74.625 16.574 73.419 25 70 C24.67 69.34 24.34 68.68 24 68 C23.769 62.462 23.769 62.462 25 60 C18.565 60.495 18.565 60.495 12 61 C12.495 60.01 12.495 60.01 13 59 C13.123 56.669 13.176 54.334 13.188 52 C13.202 50.742 13.216 49.484 13.23 48.188 C13.343 44.906 13.343 44.906 11 43 C11.375 40.875 11.375 40.875 12 39 C12.66 39 13.32 39 14 39 C14 38.34 14 37.68 14 37 C14.99 37.33 15.98 37.66 17 38 C18.426 38.07 19.854 38.085 21.281 38.062 C22.546 38.051 22.546 38.051 23.836 38.039 C25.581 38.013 27.326 37.987 29.07 37.961 C35.437 37.903 41.671 38.32 48 39 C48.124 45.032 48.214 51.064 48.275 57.097 C48.3 59.148 48.334 61.2 48.377 63.251 C48.438 66.203 48.466 69.153 48.488 72.105 C48.527 73.478 48.527 73.478 48.566 74.877 C48.568 79.419 48.518 81.344 45.619 85.012 C32.61 94.885 20.227 98.017 4 97 C-8.853 95.099 -19.927 89.288 -28 79 C-31.937 72.588 -34.349 66.294 -36 59 C-36.66 58.67 -37.32 58.34 -38 58 C-37.67 57.34 -37.34 56.68 -37 56 C-36.906 54.147 -36.817 52.294 -36.761 50.44 C-36.297 36.266 -33.146 22.893 -22.605 12.68 C-15.654 6.892 -9.096 2.758 0 2 C0 1.34 0 0.68 0 0 Z M20 26 C20.66 26.66 21.32 27.32 22 28 C22 27.34 22 26.68 22 26 C21.34 26 20.68 26 20 26 Z " fill="#CC766A" transform="translate(720,299)"/>
<path d="M0 0 C2.058 2.011 2.058 2.011 4.281 1.805 C5.219 4.43 5.219 4.43 5.281 7.805 C3.751 9.617 2.081 10.925 0.191 12.355 C-1.95 13.906 -1.95 13.906 -4.406 16.555 C-6.482 18.574 -8.065 19.724 -10.719 20.805 C-11.214 19.815 -11.214 19.815 -11.719 18.805 C-12.379 18.805 -13.039 18.805 -13.719 18.805 C-13.719 18.145 -13.719 17.485 -13.719 16.805 C-21.251 13.688 -28.582 12.738 -36.719 13.805 C-38.663 15.069 -38.663 15.069 -39.719 16.805 C-40.031 18.895 -40.031 18.895 -38.719 20.805 C-33.441 23.413 -28.109 24.81 -22.406 26.18 C-12.723 28.626 -1.069 31.685 4.496 40.793 C8.016 47.645 9.665 53.797 7.562 61.406 C3.992 70.938 -1.519 76.538 -10.719 80.805 C-24.834 85.693 -39.828 85.173 -53.719 79.805 C-57.371 77.79 -60.554 75.513 -63.719 72.805 C-64.57 72.083 -64.57 72.083 -65.438 71.348 C-66.844 70.055 -66.844 70.055 -68.719 67.805 C-68.719 66.815 -68.719 65.825 -68.719 64.805 C-67.145 63.375 -67.145 63.375 -65.031 61.93 C-62.41 60.074 -60.052 58.264 -57.844 55.93 C-57.142 55.228 -56.441 54.527 -55.719 53.805 C-55.059 53.805 -54.399 53.805 -53.719 53.805 C-53.389 53.145 -53.059 52.485 -52.719 51.805 C-52.121 52.465 -51.522 53.125 -50.906 53.805 C-48.844 56.142 -48.844 56.142 -45.719 55.805 C-45.274 56.266 -44.829 56.728 -44.371 57.203 C-42.177 59.33 -40.229 59.702 -37.281 60.43 C-36.334 60.669 -35.386 60.909 -34.41 61.156 C-33.522 61.37 -32.634 61.584 -31.719 61.805 C-30.385 62.137 -29.052 62.471 -27.719 62.805 C-27.389 62.145 -27.059 61.485 -26.719 60.805 C-25.399 60.805 -24.079 60.805 -22.719 60.805 C-21.377 60.507 -20.04 60.182 -18.719 59.805 C-19.049 57.825 -19.379 55.845 -19.719 53.805 C-21.039 53.475 -22.359 53.145 -23.719 52.805 C-23.719 52.145 -23.719 51.485 -23.719 50.805 C-26.007 50.249 -28.3 49.711 -30.594 49.18 C-31.87 48.878 -33.146 48.576 -34.461 48.266 C-35.536 48.114 -36.611 47.961 -37.719 47.805 C-38.379 48.465 -39.039 49.125 -39.719 49.805 C-41.039 49.805 -42.359 49.805 -43.719 49.805 C-43.783 49.189 -43.848 48.572 -43.914 47.938 C-44.532 45.6 -44.532 45.6 -46.898 44.609 C-47.788 44.303 -48.677 43.996 -49.594 43.68 C-57.592 40.858 -60.988 36.323 -64.719 28.805 C-66.648 21.332 -66.023 14.42 -62.598 7.559 C-57.988 -0.185 -51.273 -5.273 -42.719 -8.07 C-27.507 -11.879 -12.513 -10.104 0 0 Z " fill="#C6756A" transform="translate(395.71875,438.1953125)"/>
<path d="M0 0 C14.934 0.335 28.687 1.05 40 12 C42.125 14.266 44.063 16.566 46 19 C44.275 22.789 42.313 24.457 39 27 C38.196 27.846 37.391 28.691 36.562 29.562 C33.876 32.118 31.374 33.509 28 35 C27.752 34.381 27.505 33.762 27.25 33.125 C25.745 30.567 24.652 30.223 22 29 C21.375 28.658 20.75 28.317 20.105 27.965 C13.327 24.334 7.641 23.318 0.137 25.293 C-5.106 27.028 -8.067 29.575 -10.875 34.25 C-14.331 41.799 -14.32 50.875 -12.266 58.852 C-10.401 63.421 -8.062 67.123 -4 70 C3.602 73.17 9.632 73.25 17.504 71.055 C20.017 69.993 21.26 69.059 23 67 C24.562 65.312 24.562 65.312 26 64 C26.66 64 27.32 64 28 64 C28.33 63.01 28.66 62.02 29 61 C29.474 61.364 29.948 61.727 30.437 62.102 C32.579 63.739 34.727 65.37 36.875 67 C37.995 67.859 37.995 67.859 39.137 68.734 C39.852 69.276 40.568 69.817 41.305 70.375 C42.294 71.129 42.294 71.129 43.304 71.898 C44.943 73.182 44.943 73.182 47 73 C46.663 78.389 43.599 82.209 40 86 C37.904 87.497 35.808 88.777 33.539 89.992 C32.777 90.491 32.777 90.491 32 91 C32 91.66 32 92.32 32 93 C31.237 93.103 30.474 93.206 29.688 93.312 C26.605 93.887 23.803 94.654 20.812 95.562 C12.486 97.875 4.481 97.221 -4.004 96.121 C-6.074 95.828 -6.074 95.828 -8 97 C-8.99 96.505 -8.99 96.505 -10 96 C-10 95.01 -10 94.02 -10 93 C-10.846 92.773 -11.691 92.546 -12.562 92.312 C-16.05 90.981 -18.467 89.383 -21.387 87.121 C-23.122 85.803 -23.122 85.803 -26 85 C-26.33 84.01 -26.66 83.02 -27 82 C-27.897 81.134 -27.897 81.134 -28.812 80.25 C-31.696 77.284 -35 73.29 -35 69 C-35.99 69 -36.98 69 -38 69 C-37.67 68.01 -37.34 67.02 -37 66 C-37.606 64.311 -38.278 62.644 -39 61 C-39.165 60.257 -39.33 59.515 -39.5 58.75 C-39.665 58.173 -39.83 57.595 -40 57 C-40.66 56.67 -41.32 56.34 -42 56 C-41.01 55.505 -41.01 55.505 -40 55 C-39.642 52.094 -39.642 52.094 -39.562 48.625 C-38.802 35.729 -35.011 25.12 -27 15 C-26.34 15 -25.68 15 -25 15 C-25 14.34 -25 13.68 -25 13 C-19.294 7.425 -10.716 2.188 -2.664 1.867 C-1.345 1.933 -1.345 1.933 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C8756A" transform="translate(290,299)"/>
<path d="M0 0 C1.742 1.317 3.471 2.65 5.188 4 C6.027 4.617 6.866 5.235 7.73 5.871 C18.122 14.875 25.05 29.8 26.425 43.339 C27.542 63.009 22.317 79.818 10 95.312 C3.137 102.648 -7.562 109.995 -17.125 113.062 C-20.117 113.803 -20.117 113.803 -21.812 117 C-21.812 116.01 -21.812 115.02 -21.812 114 C-22.372 114.159 -22.932 114.319 -23.509 114.483 C-39.587 118.092 -57.921 114.905 -71.875 106.289 C-72.514 105.864 -73.154 105.438 -73.812 105 C-74.473 104.595 -75.132 104.19 -75.812 103.773 C-84.176 98.187 -89.762 89.198 -94.742 80.621 C-95.773 78.9 -95.773 78.9 -97.508 77.535 C-97.938 77.029 -98.369 76.522 -98.812 76 C-98.655 75.393 -98.498 74.786 -98.337 74.161 C-97.728 71.652 -98.042 70.36 -98.73 67.895 C-99.97 62.788 -100.159 57.926 -100.125 52.688 C-100.119 51.764 -100.114 50.84 -100.108 49.889 C-99.8 32.531 -92.968 18.495 -80.945 6.07 C-59.413 -14.032 -24.143 -17.305 0 0 Z M-69.812 5 C-69.812 5.66 -69.812 6.32 -69.812 7 C-70.654 7.364 -70.654 7.364 -71.512 7.734 C-82.341 13.692 -88.191 25.144 -91.953 36.418 C-95.478 51.109 -94.176 66.315 -87.812 80 C-86.327 79.505 -86.327 79.505 -84.812 79 C-84.812 79.66 -84.812 80.32 -84.812 81 C-85.803 81.495 -85.803 81.495 -86.812 82 C-77.278 94.678 -67.295 103.427 -51.812 108 C-49.813 108.038 -47.812 108.046 -45.812 108 C-45.483 108.33 -45.153 108.66 -44.812 109 C-27.637 110.318 -12.808 106.14 0.812 95.25 C3.321 93.04 3.321 93.04 5.188 90 C6.308 88.601 7.434 87.205 8.562 85.812 C11.741 81.731 14.187 77.763 16.188 73 C16.475 72.361 16.762 71.721 17.059 71.062 C19.018 66.114 19.615 61.156 20 55.875 C20.053 55.206 20.106 54.537 20.161 53.848 C20.423 49.075 19.688 45.462 18.188 41 C17.197 41.495 17.197 41.495 16.188 42 C17.178 44.475 17.178 44.475 18.188 47 C17.528 47 16.868 47 16.188 47 C15.857 46.34 15.528 45.68 15.188 45 C14.957 45.643 14.726 46.286 14.488 46.949 C12.154 53.416 9.726 59.841 7.238 66.25 C6.512 68.151 5.831 70.07 5.188 72 C1.593 77.797 1.593 77.797 -0.812 79 C-3.485 79.134 -6.135 79.043 -8.812 79 C-8.812 77.02 -8.812 75.04 -8.812 73 C-6.503 72.01 -4.192 71.02 -1.812 70 C-3.823 60.531 -7.063 51.881 -10.812 43 C-11.472 43 -12.132 43 -12.812 43 C-11.688 41.5 -11.688 41.5 -9.812 40 C-6.625 39.812 -6.625 39.812 -3.812 40 C-3.379 41.176 -2.946 42.351 -2.5 43.562 C-1.052 47.43 0.532 51.217 2.188 55 C2.847 55 3.507 55 4.188 55 C6.093 49.897 7.613 45.537 7.188 40 C7.814 40.023 8.44 40.046 9.086 40.07 C9.903 40.088 10.72 40.106 11.562 40.125 C12.375 40.148 13.187 40.171 14.023 40.195 C16.401 40.214 16.401 40.214 18.188 38 C18.181 34.96 18.181 34.96 17.188 32 C15.029 30.468 13.825 30 11.188 30 C11.847 29.01 12.507 28.02 13.188 27 C13.518 26.34 13.847 25.68 14.188 25 C4.246 10.143 -7.376 0.071 -25.258 -3.531 C-41.714 -5.701 -55.2 -3.006 -69.812 5 Z M14.188 27 C15.188 29 15.188 29 15.188 29 Z M-96.812 75 C-95.812 77 -95.812 77 -95.812 77 Z " fill="#BDBCBB" transform="translate(548.8125,121)"/>
<path d="M0 0 C7.92 0 15.84 0 24 0 C24 30.69 24 61.38 24 93 C16.08 93 8.16 93 0 93 C0 62.31 0 31.62 0 0 Z " fill="#BC6B60" transform="translate(560,301)"/>
<path d="M0 0 C12.771 8.184 22.626 18.984 26.871 33.84 C30.461 51.139 28.296 68.588 18.594 83.5 C13.01 91.085 7.085 97.174 -1.129 101.84 C-1.786 102.218 -2.444 102.595 -3.121 102.984 C-16.817 110.327 -33.143 111.648 -48.129 107.84 C-56.191 105.247 -63.73 101.461 -70.129 95.84 C-70.129 95.18 -70.129 94.52 -70.129 93.84 C-70.718 93.601 -71.307 93.363 -71.914 93.117 C-80.02 88.442 -84.663 75.998 -87.102 67.418 C-90.958 50.986 -88.95 35.473 -80.879 20.582 C-63.774 -6.221 -27.852 -15.457 0 0 Z M-63.129 3.84 C-63.129 4.5 -63.129 5.16 -63.129 5.84 C-63.97 6.203 -63.97 6.203 -64.828 6.574 C-75.658 12.531 -81.507 23.984 -85.27 35.258 C-88.794 49.949 -87.493 65.154 -81.129 78.84 C-79.644 78.345 -79.644 78.345 -78.129 77.84 C-78.129 78.5 -78.129 79.16 -78.129 79.84 C-79.119 80.335 -79.119 80.335 -80.129 80.84 C-70.595 93.517 -60.612 102.267 -45.129 106.84 C-43.129 106.878 -41.128 106.886 -39.129 106.84 C-38.799 107.17 -38.469 107.5 -38.129 107.84 C-20.953 109.158 -6.124 104.98 7.496 94.09 C10.005 91.88 10.005 91.88 11.871 88.84 C12.992 87.44 14.117 86.045 15.246 84.652 C18.424 80.571 20.871 76.603 22.871 71.84 C23.159 71.2 23.446 70.561 23.742 69.902 C25.701 64.953 26.299 59.995 26.684 54.715 C26.737 54.046 26.79 53.377 26.844 52.688 C27.106 47.915 26.372 44.301 24.871 39.84 C23.881 40.335 23.881 40.335 22.871 40.84 C23.861 43.315 23.861 43.315 24.871 45.84 C24.211 45.84 23.551 45.84 22.871 45.84 C22.541 45.18 22.211 44.52 21.871 43.84 C21.64 44.483 21.41 45.126 21.172 45.789 C18.838 52.256 16.41 58.681 13.922 65.09 C13.196 66.991 12.515 68.909 11.871 70.84 C8.277 76.637 8.277 76.637 5.871 77.84 C3.199 77.974 0.549 77.883 -2.129 77.84 C-2.129 75.86 -2.129 73.88 -2.129 71.84 C0.181 70.85 2.491 69.86 4.871 68.84 C2.861 59.371 -0.38 50.721 -4.129 41.84 C-4.789 41.84 -5.449 41.84 -6.129 41.84 C-5.004 40.34 -5.004 40.34 -3.129 38.84 C0.059 38.652 0.059 38.652 2.871 38.84 C3.304 40.015 3.737 41.191 4.184 42.402 C5.631 46.27 7.216 50.057 8.871 53.84 C9.531 53.84 10.191 53.84 10.871 53.84 C12.776 48.737 14.297 44.376 13.871 38.84 C14.498 38.863 15.124 38.886 15.77 38.91 C16.587 38.928 17.404 38.946 18.246 38.965 C19.058 38.988 19.87 39.011 20.707 39.035 C23.085 39.054 23.085 39.054 24.871 36.84 C24.865 33.8 24.865 33.8 23.871 30.84 C21.712 29.308 20.508 28.84 17.871 28.84 C18.531 27.85 19.191 26.86 19.871 25.84 C20.201 25.18 20.531 24.52 20.871 23.84 C10.93 8.983 -0.692 -1.089 -18.574 -4.691 C-35.03 -6.861 -48.517 -4.166 -63.129 3.84 Z M20.871 25.84 C21.871 27.84 21.871 27.84 21.871 27.84 Z " fill="#424040" transform="translate(542.12890625,122.16015625)"/>
<path d="M0 0 C2.562 2.625 2.562 2.625 2.562 5.625 C5.007 5.437 5.007 5.437 7.562 4.625 C8.671 2.742 8.671 2.742 9.562 0.625 C12.593 -2.648 15.977 -3.103 20.254 -3.434 C24.618 -3.323 28.009 -1.924 31.105 1.164 C32.32 2.6 33.45 4.108 34.562 5.625 C34.232 5.955 33.903 6.285 33.562 6.625 C33.74 9.609 33.74 9.609 34.562 12.625 C35.553 13.285 36.543 13.945 37.562 14.625 C36.572 15.12 36.572 15.12 35.562 15.625 C34.903 15.295 34.242 14.965 33.562 14.625 C33.748 15.244 33.934 15.863 34.125 16.5 C34.562 18.625 34.562 18.625 33.562 20.625 C32.903 20.625 32.242 20.625 31.562 20.625 C31.336 21.223 31.109 21.821 30.875 22.438 C28.808 25.883 25.972 27.077 22.312 28.5 C18.018 28.695 14.378 27.578 10.562 25.625 C8.596 23.758 7.176 21.826 5.562 19.625 C4.902 19.625 4.243 19.625 3.562 19.625 C3.222 20.77 3.222 20.77 2.875 21.938 C1.203 25.361 0.063 26.135 -3.438 27.625 C-7.699 28.63 -11.13 29.148 -15.25 27.5 C-17.7 25.4 -19.6 23.279 -21.438 20.625 C-21.438 19.965 -21.438 19.305 -21.438 18.625 C-22.098 18.295 -22.757 17.965 -23.438 17.625 C-23.2 10.953 -23.018 5.86 -18.438 0.625 C-12.898 -4.395 -6.016 -4.722 0 0 Z M-11.438 3.625 C-9.952 4.615 -9.952 4.615 -8.438 5.625 C-8.438 4.965 -8.438 4.305 -8.438 3.625 C-9.428 3.625 -10.418 3.625 -11.438 3.625 Z M-12.438 4.625 C-12.767 5.285 -13.098 5.945 -13.438 6.625 C-12.777 6.625 -12.118 6.625 -11.438 6.625 C-11.767 5.965 -12.098 5.305 -12.438 4.625 Z M-7.438 4.625 C-6.777 5.285 -6.118 5.945 -5.438 6.625 C-5.438 5.965 -5.438 5.305 -5.438 4.625 C-6.098 4.625 -6.757 4.625 -7.438 4.625 Z M14.688 6.188 C13.104 9.619 13.038 11.885 13.562 15.625 C15.585 18.731 15.585 18.731 18.562 20.625 C21.323 20.575 21.323 20.575 23.562 19.625 C25.53 14.88 25.719 11.635 24.562 6.625 C22.681 4.336 22.681 4.336 19.625 4.25 C16.554 4.345 16.554 4.345 14.688 6.188 Z M-5.438 6.625 C-4.438 8.625 -4.438 8.625 -4.438 8.625 Z M3.562 6.625 C3.562 7.945 3.562 9.265 3.562 10.625 C4.223 10.625 4.882 10.625 5.562 10.625 C5.233 9.305 4.902 7.985 4.562 6.625 C4.233 6.625 3.902 6.625 3.562 6.625 Z M3.562 11.625 C4.562 13.625 4.562 13.625 4.562 13.625 Z M4.562 14.625 C3.572 15.285 2.582 15.945 1.562 16.625 C1.562 17.285 1.562 17.945 1.562 18.625 C2.882 18.625 4.202 18.625 5.562 18.625 C5.233 17.305 4.902 15.985 4.562 14.625 Z M-10.438 16.625 C-10.438 17.285 -10.438 17.945 -10.438 18.625 C-8.127 18.955 -5.817 19.285 -3.438 19.625 C-3.767 18.305 -4.098 16.985 -4.438 15.625 C-6.462 15.625 -8.462 16.22 -10.438 16.625 Z " fill="#807F7D" transform="translate(504.4375,163.375)"/>
<path d="M0 0 C1.441 0.595 2.878 1.202 4.312 1.812 C5.513 2.317 5.513 2.317 6.738 2.832 C9.038 4.019 10.426 4.954 12 7 C12.67 9.673 12.774 12.238 13 15 C12.977 14.313 12.954 13.626 12.93 12.918 C12.912 12.017 12.894 11.116 12.875 10.188 C12.852 9.294 12.829 8.401 12.805 7.48 C13 5 13 5 15 2 C15.99 2.33 16.98 2.66 18 3 C18.66 7.95 19.32 12.9 20 18 C21.32 18 22.64 18 24 18 C23.977 17.12 23.954 16.239 23.93 15.332 C23.903 13.59 23.903 13.59 23.875 11.812 C23.852 10.664 23.829 9.515 23.805 8.332 C23.995 5.083 24.606 2.913 26 0 C27.953 1.145 27.953 1.145 30 3 C30.609 5.699 30.609 5.699 30.75 8.688 C30.807 9.681 30.863 10.675 30.922 11.699 C30.948 12.458 30.973 13.218 31 14 C34.96 11.525 34.96 11.525 39 9 C37.02 8.67 35.04 8.34 33 8 C33.266 6.121 33.266 6.121 34 4 C36.457 2.307 39.061 1.464 42 1 C45 2.312 45 2.312 47 4 C47 3.34 47 2.68 47 2 C54.743 1.702 54.743 1.702 58 4 C58.828 6.277 58.828 6.277 59.25 8.938 C59.4 9.813 59.549 10.688 59.703 11.59 C60.05 14.409 60 17.161 60 20 C57.43 22.775 56.088 22.996 52.25 23.188 C51.178 23.126 50.105 23.064 49 23 C48.67 23.66 48.34 24.32 48 25 C48 24.34 48 23.68 48 23 C47.096 23 46.193 23 45.262 23 C40.841 23 36.421 23 32 23 C30.867 23.023 29.734 23.046 28.566 23.07 C27.232 23.089 25.897 23.107 24.562 23.125 C23.244 23.148 21.925 23.171 20.566 23.195 C17.243 23.013 15.801 22.578 13 21 C12.278 21.289 11.556 21.577 10.812 21.875 C6.837 23.465 3.227 23.6 -1 23 C-3.5 21.625 -3.5 21.625 -5 19 C-6.127 14.325 -6.64 10.464 -4.688 6 C-3 4 -3 4 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z M1 7 C0.67 7.99 0.34 8.98 0 10 C2.31 10 4.62 10 7 10 C6.67 9.01 6.34 8.02 6 7 C4.35 7 2.7 7 1 7 Z M40 7 C40.33 7.99 40.66 8.98 41 10 C41.66 10 42.32 10 43 10 C42.67 9.01 42.34 8.02 42 7 C41.34 7 40.68 7 40 7 Z M48 9 C49 11 49 11 49 11 Z M1 14 C0.34 14.66 -0.32 15.32 -1 16 C0.559 17.777 0.559 17.777 3 19 C6.623 18.263 6.623 18.263 10 17 C10.66 17 11.32 17 12 17 C12 16.34 12 15.68 12 15 C10.354 14.833 8.708 14.666 7.062 14.5 C6.146 14.407 5.229 14.314 4.285 14.219 C2.645 14.057 2.645 14.057 1 14 Z M41 14 C38.892 14.319 38.892 14.319 37 15 C37 16.32 37 17.64 37 19 C39.473 18.656 39.473 18.656 42 18 C42.33 17.34 42.66 16.68 43 16 C42.34 15.34 41.68 14.68 41 14 Z M49 15 C49.66 16.32 50.32 17.64 51 19 C51.66 18.67 52.32 18.34 53 18 C52.67 17.01 52.34 16.02 52 15 C51.01 15 50.02 15 49 15 Z " fill="#8A8A89" transform="translate(375,738)"/>
<path d="M0 0 C26 1 26 1 31.398 5.645 C33.011 8.609 33.011 8.609 34.375 11.812 C35.211 13.419 35.211 13.419 36.063 15.059 C37.207 17.258 38.314 19.477 39.383 21.713 C41.902 26.833 44.892 31.642 47.875 36.5 C49.26 38.847 50.644 41.195 52.023 43.545 C52.345 44.025 52.668 44.505 53 45 C53.33 45 53.66 45 54 45 C53.995 43.905 53.99 42.811 53.984 41.683 C53.966 37.611 53.955 33.539 53.945 29.467 C53.94 27.706 53.933 25.946 53.925 24.185 C53.912 21.651 53.907 19.116 53.902 16.582 C53.897 15.799 53.892 15.015 53.887 14.208 C53.886 10.344 54.056 6.755 55 3 C54.01 2.67 53.02 2.34 52 2 C55.111 0.963 57.495 0.8 60.762 0.684 C61.88 0.642 62.998 0.6 64.15 0.557 C65.901 0.498 65.901 0.498 67.688 0.438 C68.866 0.394 70.045 0.351 71.26 0.307 C74.173 0.2 77.086 0.098 80 0 C79.996 1.094 79.991 2.187 79.987 3.314 C79.946 13.614 79.916 23.914 79.896 34.214 C79.886 39.51 79.872 44.805 79.849 50.101 C79.827 55.21 79.815 60.319 79.81 65.428 C79.807 67.379 79.799 69.329 79.789 71.28 C79.774 74.009 79.772 76.738 79.773 79.468 C79.762 80.68 79.762 80.68 79.751 81.918 C79.765 85.752 79.818 88.754 82 92 C77.647 92.292 73.297 92.468 68.938 92.625 C67.708 92.709 66.479 92.793 65.213 92.879 C56.205 93.122 56.205 93.122 52.637 89.8 C50.882 87.27 49.427 84.724 48 82 C47.01 81.01 47.01 81.01 46 80 C45.34 80 44.68 80 44 80 C44 79.34 44 78.68 44 78 C43.01 77.505 43.01 77.505 42 77 C42.071 76.296 42.142 75.592 42.215 74.867 C41.936 71.146 40.598 69.243 38.562 66.125 C35.348 61.156 32.245 56.137 29.215 51.055 C28.806 50.371 28.397 49.688 27.976 48.984 C26.983 47.323 25.992 45.662 25 44 C24.975 50.514 24.957 57.029 24.945 63.543 C24.94 65.761 24.933 67.978 24.925 70.196 C24.912 73.378 24.907 76.56 24.902 79.742 C24.897 80.738 24.892 81.734 24.887 82.76 C24.887 83.681 24.887 84.602 24.886 85.55 C24.883 86.769 24.883 86.769 24.88 88.013 C24.874 90.075 24.874 90.075 26 92 C17.42 92 8.84 92 0 92 C0 61.64 0 31.28 0 0 Z M2 3 C2 32.04 2 61.08 2 91 C8.93 91 15.86 91 23 91 C23 73.18 23 55.36 23 37 C26.96 44.425 26.96 44.425 31 52 C33.218 55.421 33.218 55.421 35.503 58.796 C39.207 64.251 42.485 69.997 45.877 75.648 C46.465 76.623 47.054 77.597 47.66 78.602 C48.182 79.471 48.704 80.341 49.242 81.237 C51.367 84.576 53.7 87.779 56 91 C63.26 91 70.52 91 78 91 C78 61.96 78 32.92 78 3 C71.07 3 64.14 3 57 3 C56.67 19.5 56.34 36 56 53 C45 35 45 35 40 26 C39.004 24.357 37.994 22.723 36.957 21.105 C36.441 20.295 35.924 19.485 35.393 18.65 C34.871 17.838 34.35 17.025 33.812 16.188 C32.755 14.536 31.699 12.884 30.645 11.23 C29.954 10.154 29.954 10.154 29.249 9.055 C28.06 7.098 27.024 5.049 26 3 C18.08 3 10.16 3 2 3 Z M44 75 C45 77 45 77 45 77 Z " fill="#3F2928" transform="translate(614,428)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.99 3 2.98 3 4 C3.99 4 4.98 4 6 4 C6 4.66 6 5.32 6 6 C7.803 6.702 7.803 6.702 10 7 C10.598 6.67 11.196 6.34 11.812 6 C15.121 4.487 17.53 5.18 21 6 C23.56 7.707 23.92 8.687 24.688 11.688 C24.842 12.832 24.842 12.832 25 14 C25.33 14 25.66 14 26 14 C26 17.96 26 21.92 26 26 C23.562 26.054 21.126 26.094 18.688 26.125 C17.652 26.15 17.652 26.15 16.596 26.176 C12.829 26.212 11.216 26.144 8 24 C7.67 24.66 7.34 25.32 7 26 C3.625 26.125 3.625 26.125 0 26 C-2.36 23.64 -2.491 22.221 -3 19 C-3.66 19.99 -4.32 20.98 -5 22 C-6.74 24.342 -7.981 25.788 -10.898 26.395 C-14.64 26.582 -16.44 26.42 -19.5 24.125 C-21 22 -21 22 -21 20 C-19.02 19.67 -17.04 19.34 -15 19 C-15 18.34 -15 17.68 -15 17 C-16.32 16.34 -17.64 15.68 -19 15 C-19 14.34 -19 13.68 -19 13 C-19.99 12.67 -20.98 12.34 -22 12 C-22.99 11.67 -23.98 11.34 -25 11 C-26.941 12.941 -26.399 16.473 -26.562 19.062 C-26.646 20.353 -26.73 21.643 -26.816 22.973 C-26.877 23.972 -26.938 24.971 -27 26 C-28.65 26 -30.3 26 -32 26 C-32.959 21.38 -32.961 17.319 -32.562 12.625 C-32.461 11.38 -32.359 10.135 -32.254 8.852 C-32.17 7.911 -32.086 6.97 -32 6 C-28.446 4.815 -25.73 4.734 -22 5 C-20.062 6.5 -20.062 6.5 -19 8 C-18.216 7.361 -17.433 6.721 -16.625 6.062 C-14 4 -14 4 -12 3 C-11.67 3.66 -11.34 4.32 -11 5 C-9.025 6.048 -7.025 7.052 -5 8 C-5 7.34 -5 6.68 -5 6 C-4.01 6 -3.02 6 -2 6 C-2 4.35 -2 2.7 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z M-15 10 C-14.67 10.66 -14.34 11.32 -14 12 C-13.01 11.34 -12.02 10.68 -11 10 C-12.32 10 -13.64 10 -15 10 Z M7 8 C7 8.66 7 9.32 7 10 C5.68 10.33 4.36 10.66 3 11 C3.186 11.557 3.371 12.114 3.562 12.688 C4.078 15.411 3.634 17.322 3 20 C3 20.66 3 21.32 3 22 C3.66 22 4.32 22 5 22 C5.33 20.02 5.66 18.04 6 16 C6.33 17.98 6.66 19.96 7 22 C9.264 18.075 10.395 14.142 9.562 9.625 C9.377 9.089 9.191 8.553 9 8 C8.34 8 7.68 8 7 8 Z M11 12 C11.33 12.99 11.66 13.98 12 15 C12.99 14.67 13.98 14.34 15 14 C15.733 11.985 15.733 11.985 16 10 C13.676 9.815 13.676 9.815 11 12 Z M-10 12 C-10.33 12.66 -10.66 13.32 -11 14 C-9.68 13.67 -8.36 13.34 -7 13 C-7.99 12.67 -8.98 12.34 -10 12 Z M-6 13 C-6.66 13.66 -7.32 14.32 -8 15 C-7.01 15.33 -6.02 15.66 -5 16 C-5.33 15.01 -5.66 14.02 -6 13 Z M16 17 C15.34 17.66 14.68 18.32 14 19 C15.98 19.99 15.98 19.99 18 21 C18.33 19.68 18.66 18.36 19 17 C18.01 17 17.02 17 16 17 Z M-14 18 C-13.01 19.485 -13.01 19.485 -12 21 C-11.34 21 -10.68 21 -10 21 C-10 20.34 -10 19.68 -10 19 C-11.32 18.67 -12.64 18.34 -14 18 Z M13 19 C13 19.99 13 20.98 13 22 C13.66 21.67 14.32 21.34 15 21 C14.34 20.34 13.68 19.68 13 19 Z " fill="#90908E" transform="translate(549,735)"/>
<path d="M0 0 C0.958 0.047 1.917 0.094 2.904 0.143 C3.939 0.191 4.973 0.24 6.039 0.289 C7.127 0.345 8.216 0.401 9.338 0.459 C10.43 0.511 11.523 0.564 12.648 0.617 C15.357 0.748 18.066 0.883 20.775 1.022 C20.775 1.352 20.775 1.682 20.775 2.022 C13.515 2.022 6.255 2.022 -1.225 2.022 C-0.895 19.182 -0.565 36.342 -0.225 54.022 C-4.722 49.525 -4.722 49.525 -6.557 46.299 C-6.959 45.602 -7.361 44.904 -7.775 44.186 C-8.192 43.451 -8.608 42.716 -9.037 41.959 C-9.929 40.414 -10.822 38.87 -11.717 37.326 C-12.149 36.581 -12.58 35.836 -13.025 35.068 C-14.511 32.534 -16.07 30.053 -17.662 27.584 C-20.887 22.577 -24.037 17.528 -27.162 12.459 C-27.66 11.66 -28.157 10.861 -28.67 10.038 C-29.13 9.289 -29.59 8.54 -30.065 7.768 C-30.481 7.094 -30.897 6.421 -31.326 5.727 C-32.225 4.022 -32.225 4.022 -32.225 2.022 C-39.485 2.022 -46.745 2.022 -54.225 2.022 C-54.225 32.052 -54.225 62.082 -54.225 93.022 C-47.625 93.022 -41.025 93.022 -34.225 93.022 C-34.225 74.212 -34.225 55.402 -34.225 36.022 C-31.799 38.448 -30.601 39.869 -29.061 42.76 C-28.666 43.49 -28.271 44.22 -27.864 44.972 C-27.447 45.752 -27.03 46.531 -26.6 47.334 C-22.15 55.485 -17.349 63.275 -12.225 71.022 C-10.468 73.686 -8.714 76.353 -6.964 79.022 C-5.777 80.833 -4.586 82.642 -3.39 84.447 C-2.849 85.271 -2.309 86.095 -1.752 86.944 C-1.265 87.682 -0.778 88.42 -0.275 89.181 C0.775 91.022 0.775 91.022 0.775 93.022 C7.375 93.022 13.975 93.022 20.775 93.022 C20.775 63.982 20.775 34.942 20.775 5.022 C21.105 5.022 21.435 5.022 21.775 5.022 C22.607 13.303 22.869 21.453 22.775 29.772 C21.702 45.767 21.702 45.767 24.775 61.022 C24.115 61.022 23.455 61.022 22.775 61.022 C23.105 71.912 23.435 82.802 23.775 94.022 C23.115 94.022 22.455 94.022 21.775 94.022 C21.775 94.682 21.775 95.342 21.775 96.022 C20.29 96.517 20.29 96.517 18.775 97.022 C18.445 96.362 18.115 95.702 17.775 95.022 C15.226 94.709 12.772 94.509 10.213 94.397 C9.502 94.358 8.791 94.319 8.058 94.28 C6.298 94.185 4.536 94.102 2.775 94.022 C2.775 94.682 2.775 95.342 2.775 96.022 C-1.02 94.757 -1.368 93.505 -3.287 90.084 C-5.421 86.301 -5.421 86.301 -8.225 83.022 C-9.046 81.559 -9.832 80.076 -10.6 78.584 C-14.022 72.188 -17.898 66.11 -21.822 60.012 C-22.565 58.853 -22.565 58.853 -23.323 57.67 C-23.761 56.991 -24.199 56.312 -24.651 55.613 C-26.957 51.817 -29.068 47.905 -31.225 44.022 C-31.555 59.862 -31.885 75.702 -32.225 92.022 C-31.565 92.352 -30.905 92.682 -30.225 93.022 C-31.215 93.517 -31.215 93.517 -32.225 94.022 C-33.358 96.038 -33.358 96.038 -34.225 98.022 C-34.72 97.032 -34.72 97.032 -35.225 96.022 C-34.895 95.362 -34.565 94.702 -34.225 94.022 C-45.115 94.517 -45.115 94.517 -56.225 95.022 C-56.225 64.002 -56.225 32.982 -56.225 1.022 C-31.225 1.022 -31.225 1.022 -25.451 6.549 C-24.23 8.454 -24.23 8.454 -23.522 10.145 C-23.105 11.088 -23.105 11.088 -22.68 12.051 C-22.406 12.681 -22.132 13.31 -21.85 13.959 C-21.247 15.169 -20.635 16.374 -20.014 17.574 C-19.071 19.405 -18.139 21.237 -17.245 23.092 C-13.323 31.185 -8.224 38.566 -3.225 46.022 C-3.227 45.46 -3.229 44.899 -3.232 44.32 C-3.254 38.497 -3.269 32.674 -3.28 26.851 C-3.285 24.675 -3.292 22.499 -3.3 20.323 C-3.312 17.203 -3.318 14.083 -3.323 10.963 C-3.328 9.984 -3.333 9.005 -3.338 7.996 C-3.338 7.095 -3.338 6.194 -3.338 5.266 C-3.341 4.469 -3.343 3.672 -3.345 2.85 C-3.165 0.104 -2.75 0.027 0 0 Z M-3.225 47.022 C-2.225 49.022 -2.225 49.022 -2.225 49.022 Z " fill="#482D2A" transform="translate(654.224853515625,299.978271484375)"/>
<path d="M0 0 C4.413 2.292 7.073 5.028 10 9 C10.66 9.66 11.32 10.32 12 11 C11.977 13.277 11.977 13.277 11.625 15.938 C11.278 18.679 11 21.232 11 24 C9.704 30.216 8.279 35.071 3 39 C-1.635 41.217 -5.046 41.327 -10 40 C-15.986 36.362 -18.216 31.686 -20 25 C-21.2 17.912 -19.203 11.334 -16 5 C-11.406 0.294 -6.478 -0.885 0 0 Z M-8.617 9.723 C-11.801 14.965 -11.348 21.312 -10.062 27.125 C-9.724 28.421 -9.376 29.714 -9 31 C-8.01 31 -7.02 31 -6 31 C-3.812 32 -3.812 32 -2 33 C1.6 28.461 2.155 25.192 2.188 19.438 C2.209 17.703 2.209 17.703 2.23 15.934 C2.31 12.877 2.31 12.877 0 11 C-1 10 -1 10 -2 8 C-5.753 7.533 -5.753 7.533 -8.617 9.723 Z " fill="#BEBDBD" transform="translate(571,643)"/>
<path d="M0 0 C9.57 0.33 19.14 0.66 29 1 C29.88 2.905 30.761 4.81 31.668 6.773 C32.538 8.654 33.409 10.534 34.28 12.414 C34.876 13.703 35.473 14.993 36.069 16.283 C39.594 23.916 43.176 31.51 47 39 C47.454 38.526 47.908 38.051 48.375 37.562 C50 36 50 36 52 35 C52.751 33.212 52.751 33.212 53.344 31.043 C54.964 25.899 57.146 21.156 59.5 16.312 C59.891 15.497 60.281 14.682 60.684 13.842 C62.749 9.54 64.865 5.269 67 1 C77.23 1 87.46 1 98 1 C98.045 13.222 98.082 25.443 98.104 37.665 C98.114 43.339 98.128 49.014 98.151 54.688 C98.173 60.16 98.185 65.633 98.19 71.105 C98.193 73.197 98.201 75.288 98.211 77.38 C98.226 80.301 98.228 83.223 98.227 86.144 C98.234 87.016 98.241 87.888 98.249 88.786 C98.228 94.772 98.228 94.772 96 97 C95.67 96.01 95.34 95.02 95 94 C88.07 94 81.14 94 74 94 C73.67 77.17 73.34 60.34 73 43 C71.389 44.611 70.678 46.656 69.707 48.707 C69.013 50.169 69.013 50.169 68.304 51.66 C67.812 52.7 67.32 53.741 66.812 54.812 C62.959 62.919 59.076 71.002 55 79 C52.03 79 49.06 79 46 79 C46 78.67 46 78.34 46 78 C47.859 77.969 47.859 77.969 49.755 77.938 C51.97 77.721 51.97 77.721 54 77 C55.838 74.518 56.81 72.79 57.922 70 C58.234 69.29 58.546 68.581 58.868 67.849 C59.859 65.578 60.805 63.291 61.75 61 C62.399 59.49 63.052 57.98 63.707 56.473 C64.952 53.595 66.181 50.712 67.393 47.82 C69.196 43.517 71.085 39.255 73 35 C73.66 35 74.32 35 75 35 C75 54.14 75 73.28 75 93 C81.93 93 88.86 93 96 93 C96 62.97 96 32.94 96 2 C82.635 2.495 82.635 2.495 69 3 C66.989 7.269 66.989 7.269 64.938 11.625 C61.556 18.786 58.076 25.884 54.508 32.953 C52.545 36.919 50.751 40.937 49 45 C48.34 45 47.68 45 47 45 C46.536 44.012 46.073 43.024 45.595 42.006 C43.859 38.308 42.121 34.61 40.384 30.913 C39.635 29.319 38.886 27.724 38.138 26.13 C37.056 23.826 35.974 21.522 34.891 19.219 C34.561 18.516 34.232 17.814 33.893 17.09 C31.664 12.354 29.342 7.685 27 3 C14.13 2.505 14.13 2.505 1 2 C1 32.03 1 62.06 1 93 C8.26 93 15.52 93 23 93 C23 74.52 23 56.04 23 37 C23.66 37.33 24.32 37.66 25 38 C24.67 56.81 24.34 75.62 24 95 C16.74 95 9.48 95 2 95 C1.01 95.495 1.01 95.495 0 96 C0 95.34 0 94.68 0 94 C-0.66 93.67 -1.32 93.34 -2 93 C-1.505 92.505 -1.505 92.505 -1 92 C-0.875 89.14 -0.814 86.303 -0.795 83.441 C-0.785 82.544 -0.775 81.646 -0.765 80.722 C-0.733 77.74 -0.708 74.759 -0.684 71.777 C-0.663 69.716 -0.642 67.655 -0.621 65.594 C-0.565 60.157 -0.516 54.72 -0.468 49.284 C-0.418 43.74 -0.362 38.197 -0.307 32.654 C-0.199 21.77 -0.098 10.885 0 0 Z " fill="#462B29" transform="translate(446,300)"/>
<path d="M0 0 C3.688 0.562 3.688 0.562 4.688 1.562 C4.775 3.197 4.795 4.836 4.785 6.473 C4.782 7.462 4.779 8.451 4.775 9.471 C4.767 10.512 4.759 11.553 4.75 12.625 C4.745 13.67 4.741 14.715 4.736 15.791 C4.724 18.382 4.708 20.972 4.688 23.562 C6.337 23.232 7.987 22.903 9.688 22.562 C9.688 25.862 9.688 29.163 9.688 32.562 C8.697 32.562 7.707 32.562 6.688 32.562 C5.603 35.445 5.603 35.445 5.438 38.125 C4.688 40.562 4.688 40.562 2.375 42 C-0.312 42.562 -0.312 42.562 -2.625 41.688 C-3.182 41.316 -3.739 40.945 -4.312 40.562 C-4.312 37.923 -4.312 35.283 -4.312 32.562 C-5.303 33.883 -6.293 35.202 -7.312 36.562 C-8.303 36.232 -9.293 35.903 -10.312 35.562 C-10.312 34.572 -10.312 33.582 -10.312 32.562 C-15.482 32.968 -15.482 32.968 -20.312 34.562 C-20.312 33.572 -20.312 32.582 -20.312 31.562 C-20.972 31.232 -21.633 30.903 -22.312 30.562 C-21.653 30.562 -20.992 30.562 -20.312 30.562 C-20.043 25.912 -20.043 25.912 -21.312 21.562 C-20.722 21.338 -20.132 21.114 -19.523 20.883 C-16.659 19.172 -15.634 17.297 -13.938 14.438 C-13.33 13.459 -12.723 12.481 -12.098 11.473 C-10.994 9.673 -9.896 7.869 -8.809 6.059 C-6.269 2.191 -4.834 0.086 0 0 Z M-6.312 15.562 C-5.312 17.562 -5.312 17.562 -5.312 17.562 Z M-8.312 18.562 C-8.312 19.883 -8.312 21.202 -8.312 22.562 C-7.653 21.903 -6.992 21.242 -6.312 20.562 C-6.972 19.903 -7.633 19.242 -8.312 18.562 Z M-5.312 19.562 C-5.643 20.553 -5.972 21.543 -6.312 22.562 C-7.962 22.893 -9.612 23.222 -11.312 23.562 C-11.312 23.893 -11.312 24.222 -11.312 24.562 C-9.003 24.562 -6.692 24.562 -4.312 24.562 C-4.312 22.913 -4.312 21.263 -4.312 19.562 C-4.643 19.562 -4.972 19.562 -5.312 19.562 Z " fill="#7C7C7A" transform="translate(634.3125,642.4375)"/>
<path d="M0 0 C1.742 1.317 3.471 2.65 5.188 4 C6.027 4.617 6.866 5.235 7.73 5.871 C18.122 14.875 25.05 29.8 26.425 43.339 C27.542 63.009 22.317 79.818 10 95.312 C3.137 102.648 -7.562 109.995 -17.125 113.062 C-20.117 113.803 -20.117 113.803 -21.812 117 C-21.812 116.01 -21.812 115.02 -21.812 114 C-22.372 114.159 -22.932 114.319 -23.509 114.483 C-39.587 118.092 -57.921 114.905 -71.875 106.289 C-72.514 105.864 -73.154 105.438 -73.812 105 C-74.473 104.595 -75.132 104.19 -75.812 103.773 C-84.176 98.187 -89.762 89.198 -94.742 80.621 C-95.773 78.9 -95.773 78.9 -97.508 77.535 C-97.938 77.029 -98.369 76.522 -98.812 76 C-98.655 75.393 -98.498 74.786 -98.337 74.161 C-97.728 71.652 -98.042 70.36 -98.73 67.895 C-99.97 62.788 -100.159 57.926 -100.125 52.688 C-100.119 51.764 -100.114 50.84 -100.108 49.889 C-99.8 32.531 -92.968 18.495 -80.945 6.07 C-59.413 -14.032 -24.143 -17.305 0 0 Z M-84.812 12.656 C-97.644 27.693 -98.735 45.125 -97.812 64 C-96.189 77.359 -88.566 90.614 -78.282 99.292 C-65.598 109.141 -53.456 113.36 -37.438 113.312 C-35.829 113.337 -35.829 113.337 -34.188 113.361 C-18.116 113.371 -4.769 106.886 6.562 95.723 C18.346 83.501 24.767 68.534 24.562 51.562 C24.574 50.603 24.586 49.643 24.598 48.654 C24.531 32.177 17.681 18.646 6.246 7.027 C-1.027 0.342 -9.245 -4.501 -18.812 -7 C-19.558 -7.205 -20.303 -7.41 -21.07 -7.621 C-45.418 -13.447 -67.863 -5.019 -84.812 12.656 Z M-96.812 75 C-95.812 77 -95.812 77 -95.812 77 Z " fill="#434242" transform="translate(548.8125,121)"/>
<path d="M0 0 C3.371 1.124 5.492 2.492 8 5 C8 5.33 8 5.66 8 6 C8.66 6 9.32 6 10 6 C10 5.34 10 4.68 10 4 C11.32 4.33 12.64 4.66 14 5 C14 5.99 14 6.98 14 8 C14.928 7.814 15.856 7.629 16.812 7.438 C20 7 20 7 23 8 C25.638 7.606 25.638 7.606 28 7 C28.495 8.485 28.495 8.485 29 10 C29.66 10 30.32 10 31 10 C31 10.66 31 11.32 31 12 C31.66 11.67 32.32 11.34 33 11 C33 10.01 33 9.02 33 8 C34.32 7.67 35.64 7.34 37 7 C37 7.66 37 8.32 37 9 C38.65 8.34 40.3 7.68 42 7 C42.99 7.99 43.98 8.98 45 10 C44.34 11.32 43.68 12.64 43 14 C41.68 13.67 40.36 13.34 39 13 C38.67 16.63 38.34 20.26 38 24 C34 23 34 23 32 21 C32.66 21 33.32 21 34 21 C33.505 19.02 33.505 19.02 33 17 C32.567 17.928 32.134 18.856 31.688 19.812 C30.281 22.468 29.33 24.177 27 26 C26.34 26 25.68 26 25 26 C24.67 27.32 24.34 28.64 24 30 C23.01 30 22.02 30 21 30 C20.01 30.495 20.01 30.495 19 31 C18.505 27.535 18.505 27.535 18 24 C17.34 24 16.68 24 16 24 C12.298 23.525 10.401 22.906 8 20 C7.67 20.99 7.34 21.98 7 23 C5.02 23 3.04 23 1 23 C1.33 22.34 1.66 21.68 2 21 C1.814 20.113 1.629 19.226 1.438 18.312 C1 15 1 15 2.062 11.312 C2.372 10.219 2.681 9.126 3 8 C2.34 7.01 1.68 6.02 1 5 C0.567 6.114 0.567 6.114 0.125 7.25 C-1 10 -1 10 -2.031 11.875 C-3.109 14.239 -3.397 16.17 -3.625 18.75 C-3.7 19.549 -3.775 20.348 -3.852 21.172 C-3.901 21.775 -3.95 22.378 -4 23 C-5.938 23.562 -5.938 23.562 -8 24 C-9.517 22.483 -9.187 20.987 -9.312 18.875 C-9.779 14.005 -11.407 11.099 -14 7 C-14 6.34 -14 5.68 -14 5 C-14.66 4.67 -15.32 4.34 -16 4 C-14.35 3.67 -12.7 3.34 -11 3 C-9.02 5.97 -9.02 5.97 -7 9 C-5.396 7.815 -5.396 7.815 -4 6 C-3.749 3.374 -3.749 3.374 -4 1 C-2.68 1.33 -1.36 1.66 0 2 C0 1.34 0 0.68 0 0 Z M17 10 C17 11.65 17 13.3 17 15 C17.33 15 17.66 15 18 15 C18 13.35 18 11.7 18 10 C17.67 10 17.34 10 17 10 Z M7 11 C7 13.31 7 15.62 7 18 C7.66 17.01 8.32 16.02 9 15 C8.34 13.68 7.68 12.36 7 11 Z M9 12 C10 14 10 14 10 14 Z M13 12 C13.33 14.64 13.66 17.28 14 20 C14.66 19.67 15.32 19.34 16 19 C16 16.69 16 14.38 16 12 C15.01 12 14.02 12 13 12 Z M24 12 C22.644 13.125 22.644 13.125 22.938 16.062 C22.958 17.032 22.979 18.001 23 19 C24.32 19.33 25.64 19.66 27 20 C27.981 16.947 27.981 15.053 27 12 C26.01 12 25.02 12 24 12 Z M33 12 C32.67 12.99 32.34 13.98 32 15 C32.66 15 33.32 15 34 15 C33.67 14.01 33.34 13.02 33 12 Z M17 16 C18 19 18 19 18 19 Z " fill="#929191" transform="translate(533,881)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5 1.66 5 2.32 5 3 C5.736 2.954 6.472 2.907 7.23 2.859 C15.371 2.558 15.371 2.558 18.938 5.062 C19.958 6.022 19.958 6.022 21 7 C24.052 6.826 24.813 6.187 27 4 C30.686 3.096 33.127 2.702 36.75 3.938 C39.828 6.759 41.229 9.904 42 14 C41.57 16.254 41.57 16.254 41 18 C41.99 18 42.98 18 44 18 C43.67 19.32 43.34 20.64 43 22 C42.196 22.144 41.391 22.289 40.562 22.438 C38.079 22.741 38.079 22.741 37 24 C33.966 24.375 31.031 24.408 28 24 C25.438 22 25.438 22 24 20 C23.67 20.99 23.34 21.98 23 23 C20.69 23.66 18.38 24.32 16 25 C15.67 20.05 15.34 15.1 15 10 C15 10.33 15 10.66 15 11 C15 12.32 15 13.64 15 15 C13.68 15.33 12.36 15.66 11 16 C10.67 18.64 10.34 21.28 10 24 C8.35 24 6.7 24 5 24 C4.505 16.575 4.505 16.575 4 9 C2.68 9 1.36 9 0 9 C-0.027 11.125 -0.046 13.25 -0.062 15.375 C-0.074 16.558 -0.086 17.742 -0.098 18.961 C-0.215 21.919 -0.215 21.919 1 24 C-1 24.043 -3 24.041 -5 24 C-6 23 -6 23 -6.114 21.142 C-6.108 20.352 -6.103 19.561 -6.098 18.746 C-6.094 17.892 -6.091 17.038 -6.088 16.158 C-6.08 15.26 -6.071 14.363 -6.062 13.438 C-6.058 12.536 -6.053 11.634 -6.049 10.705 C-6.037 8.47 -6.021 6.235 -6 4 C-4 3 -4 3 0 3 C0 2.01 0 1.02 0 0 Z M33 8 C33.66 8.66 34.32 9.32 35 10 C35 9.34 35 8.68 35 8 C34.34 8 33.68 8 33 8 Z M11 9 C12 11 12 11 12 11 Z M23 9 C24 11 24 11 24 11 Z M29 9 C29 9.66 29 10.32 29 11 C30.32 10.67 31.64 10.34 33 10 C31.68 9.67 30.36 9.34 29 9 Z M21 15 C21 16.98 21 18.96 21 21 C21.33 21 21.66 21 22 21 C22 19.02 22 17.04 22 15 C21.67 15 21.34 15 21 15 Z M28 15 C28.33 16.32 28.66 17.64 29 19 C31.97 19.495 31.97 19.495 35 20 C34.34 19.67 33.68 19.34 33 19 C33.33 18.01 33.66 17.02 34 16 C31.03 15.505 31.03 15.505 28 15 Z M36 16 C37 18 37 18 37 18 Z M38 16 C39 18 39 18 39 18 Z " fill="#898987" transform="translate(582,737)"/>
<path d="M0 0 C3.561 3.13 4.92 5.503 5.438 10.25 C4.741 16.87 1.769 20.341 -2.938 24.812 C-3.474 25.555 -4.01 26.298 -4.562 27.062 C-5.938 28.812 -5.938 28.812 -8.938 29.812 C-2.008 30.308 -2.008 30.308 5.062 30.812 C5.062 33.452 5.062 36.092 5.062 38.812 C-3.847 38.812 -12.758 38.812 -21.938 38.812 C-21.938 31.812 -21.938 31.812 -20.561 29.673 C-19.932 29.094 -19.304 28.515 -18.656 27.918 C-17.979 27.273 -17.303 26.628 -16.605 25.963 C-15.89 25.315 -15.175 24.667 -14.438 24 C-13.027 22.685 -11.621 21.366 -10.219 20.043 C-9.59 19.47 -8.962 18.896 -8.314 18.305 C-6.712 16.906 -6.712 16.906 -6.938 14.812 C-6.278 14.482 -5.617 14.153 -4.938 13.812 C-4.938 12.822 -4.938 11.832 -4.938 10.812 C-4.938 10.482 -4.938 10.153 -4.938 9.812 C-5.597 9.812 -6.258 9.812 -6.938 9.812 C-6.938 8.822 -6.938 7.832 -6.938 6.812 C-7.268 7.472 -7.597 8.133 -7.938 8.812 C-12.839 11.921 -16.322 12.103 -21.938 11.812 C-21.449 5.465 -21.449 5.465 -19.125 2.5 C-13.357 -1.95 -6.814 -2.754 0 0 Z M-11.938 6.812 C-12.268 7.472 -12.597 8.133 -12.938 8.812 C-12.278 8.812 -11.617 8.812 -10.938 8.812 C-11.268 8.153 -11.597 7.492 -11.938 6.812 Z " fill="#A09F9E" transform="translate(605.9375,644.1875)"/>
<path d="M0 0 C3.01 2.079 4.241 3.429 5.625 6.812 C6.468 11.681 5.537 14.404 2.938 18.438 C0.435 21.914 -2.213 24.93 -5.375 27.812 C-5.045 28.472 -4.715 29.133 -4.375 29.812 C-1.075 29.812 2.225 29.812 5.625 29.812 C5.625 32.783 5.625 35.753 5.625 38.812 C-3.285 38.812 -12.195 38.812 -21.375 38.812 C-21.375 36.173 -21.375 33.533 -21.375 30.812 C-19.5 29.062 -19.5 29.062 -17.375 27.812 C-16.123 25.729 -16.123 25.729 -15.375 23.812 C-14.715 23.812 -14.055 23.812 -13.375 23.812 C-11.605 22.363 -11.605 22.363 -9.812 20.5 C-8.882 19.578 -8.882 19.578 -7.934 18.637 C-6.159 16.791 -6.159 16.791 -5.375 13.812 C-5.045 13.812 -4.715 13.812 -4.375 13.812 C-4.045 11.832 -3.715 9.852 -3.375 7.812 C-7.396 7.534 -7.396 7.534 -11.375 7.812 C-12.401 9.126 -13.402 10.459 -14.375 11.812 C-18.062 12.062 -18.062 12.062 -21.375 11.812 C-21.045 9.173 -20.715 6.533 -20.375 3.812 C-19.715 3.812 -19.055 3.812 -18.375 3.812 C-18.375 3.153 -18.375 2.492 -18.375 1.812 C-12.231 -1.968 -6.727 -2.367 0 0 Z " fill="#989897" transform="translate(541.375,644.1875)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 3.3 4 6.6 4 10 C6.688 9.539 8.951 9.023 11.438 7.875 C14.757 6.742 16.682 6.894 20 8 C21.375 10.562 21.375 10.562 22 13 C22.66 12.67 23.32 12.34 24 12 C23.67 10.68 23.34 9.36 23 8 C25 7 25 7 27 7.5 C30.001 9.751 30.21 12.446 31 16 C31.66 16 32.32 16 33 16 C33.99 13.36 34.98 10.72 36 8 C36.66 8 37.32 8 38 8 C38.33 7.34 38.66 6.68 39 6 C40.32 6.99 41.64 7.98 43 9 C42.34 9 41.68 9 41 9 C40.83 9.701 40.66 10.403 40.484 11.125 C38.927 17.221 37.02 22.481 34 28 C31.75 28.062 31.75 28.062 29 27 C27.307 24.133 26.13 21.126 25 18 C24.01 18.33 23.02 18.66 22 19 C22.33 19.66 22.66 20.32 23 21 C23.04 23.333 23.043 25.667 23 28 C20 29 20 29 17 28 C15.237 28.031 15.237 28.031 13.438 28.062 C10 28 10 28 8 26 C7.67 26.66 7.34 27.32 7 28 C4.125 28.188 4.125 28.188 1 28 C-1.255 24.618 -1.247 23.451 -1.23 19.492 C-1.229 18.424 -1.227 17.355 -1.225 16.254 C-1.206 14.581 -1.206 14.581 -1.188 12.875 C-1.187 11.752 -1.186 10.629 -1.186 9.473 C-1.14 1.14 -1.14 1.14 0 0 Z M4 11 C3.67 12.65 3.34 14.3 3 16 C5.354 17.429 6.48 18.087 9.25 17.625 C9.827 17.419 10.405 17.212 11 17 C9.848 14.532 8.952 12.952 7 11 C6.01 11 5.02 11 4 11 Z M14 12 C14.33 12.99 14.66 13.98 15 15 C15.99 14.67 16.98 14.34 18 14 C17.67 13.34 17.34 12.68 17 12 C16.01 12 15.02 12 14 12 Z M5 19 C5.33 19.99 5.66 20.98 6 22 C6 21.01 6 20.02 6 19 C5.67 19 5.34 19 5 19 Z M12 21 C12.33 21.99 12.66 22.98 13 24 C14.32 23.67 15.64 23.34 17 23 C17.33 21.68 17.66 20.36 18 19 C15.537 19 14.148 19.855 12 21 Z M4 21 C5 23 5 23 5 23 Z " fill="#A7A6A5" transform="translate(329,733)"/>
<path d="M0 0 C4.509 2.255 8.447 5.447 12 9 C12.536 9.454 13.072 9.908 13.625 10.375 C15 12 15 12 15 16 C14.34 16 13.68 16 13 16 C12.963 16.592 12.925 17.183 12.887 17.793 C12.169 26.422 12.169 26.422 9 30 C4.505 33.216 -0.268 34.881 -5.816 34.66 C-10.141 33.353 -12.636 29.68 -15 26 C-16.926 20.221 -16.875 14.163 -14.375 8.562 C-12.26 4.876 -10.111 3.315 -6 2 C-2.625 1.812 -2.625 1.812 0 2 C0 1.34 0 0.68 0 0 Z M-7 12 C-8.512 15.024 -8.173 17.662 -8 21 C-7.119 23.354 -7.119 23.354 -6 25 C-4.68 24.67 -3.36 24.34 -2 24 C-2 24.66 -2 25.32 -2 26 C0.867 25.427 1.861 25.139 4 23 C4.243 20.56 4.243 20.56 4.125 17.875 C4.098 16.51 4.098 16.51 4.07 15.117 C4.047 14.419 4.024 13.72 4 13 C2.68 13 1.36 13 0 13 C0 12.34 0 11.68 0 11 C0.66 10.67 1.32 10.34 2 10 C-1.908 8.408 -3.71 9.599 -7 12 Z M12 11 C13 13 13 13 13 13 Z M13 13 C12.67 13.66 12.34 14.32 12 15 C12.66 15 13.32 15 14 15 C13.67 14.34 13.34 13.68 13 13 Z " fill="#C4C3C3" transform="translate(488,650)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.598 1.794 2.196 1.587 2.812 1.375 C5 1 5 1 8 3 C9.188 5.625 9.188 5.625 10 8 C10.66 8 11.32 8 12 8 C12.33 6.02 12.66 4.04 13 2 C14.875 1.375 14.875 1.375 17 1 C17.66 1.66 18.32 2.32 19 3 C18.67 3.66 18.34 4.32 18 5 C18.66 5.33 19.32 5.66 20 6 C20.625 8.062 20.625 8.062 21 10 C21.289 8.721 21.577 7.442 21.875 6.125 C22.541 3.175 22.802 2.198 25 0 C26.33 0.526 26.33 0.526 27.688 1.062 C31.18 2.051 32.561 1.705 36 1 C38.75 1.688 38.75 1.688 41 3 C41.625 5.062 41.625 5.062 42 7 C42.99 7.33 43.98 7.66 45 8 C45 8.66 45 9.32 45 10 C44.34 10 43.68 10 43 10 C43.206 11.217 43.413 12.434 43.625 13.688 C44.1 17.348 43.766 19.676 42 23 C41.258 22.814 40.515 22.629 39.75 22.438 C37.105 21.82 37.105 21.82 35 22.562 C32.059 23.206 30.591 22.462 28 21 C26.188 18.375 26.188 18.375 25 16 C24.34 17.98 23.68 19.96 23 22 C21.35 22 19.7 22 18 22 C17.711 21.237 17.423 20.474 17.125 19.688 C16.037 16.85 16.037 16.85 14 14 C13.876 15.093 13.753 16.186 13.625 17.312 C13 21 13 21 11 24 C11 23.34 11 22.68 11 22 C10.01 22 9.02 22 8 22 C7.743 21.19 7.743 21.19 7.481 20.364 C6.703 17.927 5.914 15.495 5.125 13.062 C4.856 12.212 4.586 11.362 4.309 10.486 C4.044 9.677 3.78 8.869 3.508 8.035 C3.267 7.286 3.026 6.537 2.778 5.766 C2.137 3.803 2.137 3.803 0 3 C0 2.01 0 1.02 0 0 Z M27 5 C28 7 28 7 28 7 Z M35 6 C35.99 6.99 36.98 7.98 38 9 C37.67 8.01 37.34 7.02 37 6 C36.34 6 35.68 6 35 6 Z M28 7 C28 8.65 28 10.3 28 12 C28.99 11.34 29.98 10.68 31 10 C30.34 10 29.68 10 29 10 C28.67 9.01 28.34 8.02 28 7 Z M32 7 C31.67 7.99 31.34 8.98 31 10 C32.65 9.67 34.3 9.34 36 9 C34.68 8.34 33.36 7.68 32 7 Z M26 9 C25.67 10.65 25.34 12.3 25 14 C25.99 14.495 25.99 14.495 27 15 C27.33 14.01 27.66 13.02 28 12 C27.34 11.01 26.68 10.02 26 9 Z M32 15 C32 15.99 32 16.98 32 18 C33.65 17.67 35.3 17.34 37 17 C37.33 15.68 37.66 14.36 38 13 C35.537 13 34.148 13.855 32 15 Z " fill="#939391" transform="translate(620,739)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C8.266 3.867 8.112 7.732 8.062 11.75 C8.058 12.447 8.053 13.145 8.049 13.863 C8.037 15.576 8.019 17.288 8 19 C8.66 19 9.32 19 10 19 C9.814 19.639 9.629 20.279 9.438 20.938 C9.221 21.958 9.221 21.958 9 23 C9.33 23.33 9.66 23.66 10 24 C11.675 23.714 13.344 23.382 15 23 C15 21.763 15 20.525 15 19.25 C15 10.5 15 10.5 16 6.25 C16.186 5.451 16.371 4.652 16.562 3.828 C16.707 3.225 16.851 2.622 17 2 C19.31 2 21.62 2 24 2 C24.168 6.271 24.334 10.542 24.5 14.812 C24.548 16.027 24.595 17.242 24.645 18.494 C24.69 19.658 24.735 20.821 24.781 22.02 C24.823 23.093 24.865 24.167 24.908 25.273 C25.042 29.25 24.925 31.512 23 35 C21.02 34.01 19.04 33.02 17 32 C16.34 32.66 15.68 33.32 15 34 C13.312 33.688 13.312 33.688 11 33 C9.845 32.918 8.69 32.835 7.5 32.75 C4 32 4 32 1.75 29.938 C-1.111 25.136 -3.504 19.622 -4 14 C-3.34 13.34 -2.68 12.68 -2 12 C-2.33 11.01 -2.66 10.02 -3 9 C-2.34 9 -1.68 9 -1 9 C-1.021 7.886 -1.041 6.773 -1.062 5.625 C-1 2 -1 2 0 0 Z " fill="#C7C7C7" transform="translate(413,651)"/>
<path d="M0 0 C1.586 0.23 3.169 0.485 4.75 0.75 C5.632 0.889 6.513 1.028 7.422 1.172 C10.664 2.213 11.895 3.324 14 6 C14.454 9.116 14.454 9.116 14.391 12.734 C14.371 14.678 14.371 14.678 14.352 16.66 C14.318 18.023 14.284 19.387 14.25 20.75 C14.23 22.129 14.212 23.508 14.195 24.887 C14.148 28.259 14.082 31.629 14 35 C12.68 35 11.36 35 10 35 C10 34.34 10 33.68 10 33 C8.68 32.34 7.36 31.68 6 31 C6 27.04 6 23.08 6 19 C5.34 19 4.68 19 4 19 C4.33 16.03 4.66 13.06 5 10 C2.108 8.667 2.108 8.667 -0.188 9.938 C-0.786 10.288 -1.384 10.639 -2 11 C-1.988 12.013 -1.977 13.026 -1.965 14.07 C-1.955 15.422 -1.946 16.773 -1.938 18.125 C-1.929 18.79 -1.921 19.455 -1.912 20.141 C-1.892 24.207 -2.211 28.012 -3 32 C-3.99 32 -4.98 32 -6 32 C-6.33 33.65 -6.66 35.3 -7 37 C-7.99 36.67 -8.98 36.34 -10 36 C-10 35.34 -10 34.68 -10 34 C-10.33 33.34 -10.66 32.68 -11 32 C-13.025 31.348 -13.025 31.348 -15 31 C-14.273 30.449 -14.273 30.449 -13.532 29.887 C-11.36 27.212 -11.568 25.217 -11.488 21.801 C-11.453 20.592 -11.417 19.384 -11.381 18.139 C-11.358 16.876 -11.336 15.613 -11.312 14.312 C-11.261 12.401 -11.261 12.401 -11.209 10.451 C-11.126 7.301 -11.057 4.151 -11 1 C-7.37 1.33 -3.74 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BCBCBB" transform="translate(454,651)"/>
<path d="M0 0 C3.932 1.929 5.605 3.058 7.312 7.125 C7.653 8.548 7.653 8.548 8 10 C8.33 10.66 8.66 11.32 9 12 C9.66 12 10.32 12 11 12 C10.502 15.688 9.899 17.605 7 20 C8.32 20.33 9.64 20.66 11 21 C10.34 21 9.68 21 9 21 C8.66 22.145 8.66 22.145 8.312 23.312 C6.641 26.736 5.501 27.51 2 29 C-2.399 30.038 -5.745 30.561 -9.938 28.688 C-13.942 25.411 -14.836 23.071 -16 18 C-16.488 12.534 -15.713 8.793 -13 4 C-8.75 0.233 -5.633 -0.805 0 0 Z M-6 5 C-4.515 5.99 -4.515 5.99 -3 7 C-3 6.34 -3 5.68 -3 5 C-3.99 5 -4.98 5 -6 5 Z M-7 6 C-7.33 6.66 -7.66 7.32 -8 8 C-7.34 8 -6.68 8 -6 8 C-6.33 7.34 -6.66 6.68 -7 6 Z M-2 6 C-1.34 6.66 -0.68 7.32 0 8 C0 7.34 0 6.68 0 6 C-0.66 6 -1.32 6 -2 6 Z M0 8 C1 10 1 10 1 10 Z M-5 18 C-5 18.66 -5 19.32 -5 20 C-2.69 20.33 -0.38 20.66 2 21 C1.67 19.68 1.34 18.36 1 17 C-1.025 17 -3.024 17.595 -5 18 Z " fill="#B3B3B2" transform="translate(499,162)"/>
<path d="M0 0 C6.029 1.464 11.027 3.839 16.012 7.512 C18.901 10.054 18.901 10.054 22.137 9.762 C22.673 10.731 23.209 11.7 23.762 12.699 C25.423 15.638 27.255 18.378 29.199 21.137 C35.932 31.117 37.462 42.892 35.488 54.613 C32.786 67.599 26.946 76.797 16.137 84.762 C5.997 91.166 -4.99 93.393 -16.863 92.762 C-17.193 93.422 -17.523 94.082 -17.863 94.762 C-18.193 94.102 -18.523 93.442 -18.863 92.762 C-20.656 92.251 -22.475 91.828 -24.301 91.449 C-30.373 90.031 -35.424 87.747 -40.863 84.762 C-43.215 83.537 -45.333 82.605 -47.863 81.762 C-47.905 81.102 -47.946 80.442 -47.988 79.762 C-48.871 76.735 -49.725 75.525 -51.676 73.137 C-58.998 63.86 -60.624 52.244 -59.863 40.762 C-59.125 36.028 -58.287 31.341 -56.863 26.762 C-56.203 26.432 -55.543 26.102 -54.863 25.762 C-54.43 24.679 -54.43 24.679 -53.988 23.574 C-49.685 12.815 -41.113 6.411 -30.863 1.762 C-24.977 -0.492 -19.078 -0.468 -12.863 -0.238 C-12.863 0.092 -12.863 0.422 -12.863 0.762 C-14.047 0.981 -15.23 1.2 -16.449 1.426 C-25.556 3.212 -33.353 5.007 -40.863 10.762 C-41.516 11.236 -42.168 11.71 -42.84 12.199 C-51.233 18.771 -55.462 28.376 -56.863 38.762 C-57.668 53.558 -55.579 64.771 -46.34 76.734 C-38.099 85.88 -26.592 89.168 -14.707 89.977 C-0.964 90.386 10.971 86.817 21.492 77.727 C31.365 67.521 33.482 55.218 33.348 41.516 C32.68 29.638 26.837 19.602 18.277 11.516 C9.346 4.198 -0.729 2.48 -11.863 0.762 C-7.592 -2.086 -4.88 -0.924 0 0 Z " fill="#472E2C" transform="translate(568.86328125,429.23828125)"/>
<path d="M0 0 C1.742 1.168 1.742 1.168 3.742 4.168 C-1.912 4.514 -5.288 3.602 -10.258 1.168 C-10.258 0.508 -10.258 -0.152 -10.258 -0.832 C-16.604 -0.577 -22.357 -0.221 -27.258 4.168 C-33.984 12.833 -34.484 22.541 -33.258 33.168 C-32.043 36.877 -30.5 39.98 -28.258 43.168 C-26.608 43.168 -24.958 43.168 -23.258 43.168 C-23.258 44.158 -23.258 45.148 -23.258 46.168 C-14.648 49.793 -6.684 48.587 1.742 45.168 C1.412 44.508 1.082 43.848 0.742 43.168 C0.511 37.63 0.511 37.63 1.742 35.168 C-4.693 35.663 -4.693 35.663 -11.258 36.168 C-10.928 35.508 -10.598 34.848 -10.258 34.168 C-10.135 31.837 -10.082 29.502 -10.07 27.168 C-10.056 25.91 -10.042 24.652 -10.027 23.355 C-9.915 20.074 -9.915 20.074 -12.258 18.168 C-11.883 16.043 -11.883 16.043 -11.258 14.168 C-10.598 14.168 -9.938 14.168 -9.258 14.168 C-9.258 13.508 -9.258 12.848 -9.258 12.168 C-8.268 12.498 -7.278 12.828 -6.258 13.168 C-4.832 13.238 -3.404 13.252 -1.977 13.23 C-1.134 13.223 -0.29 13.215 0.578 13.207 C2.323 13.181 4.068 13.155 5.812 13.129 C12.179 13.071 18.413 13.488 24.742 14.168 C24.866 20.2 24.957 26.232 25.017 32.265 C25.042 34.316 25.076 36.368 25.119 38.419 C25.18 41.371 25.208 44.321 25.23 47.273 C25.256 48.188 25.282 49.103 25.309 50.045 C25.311 54.587 25.26 56.512 22.361 60.18 C9.517 69.927 -3.146 73.468 -19.258 72.168 C-20.743 71.178 -20.743 71.178 -22.258 70.168 C-20.988 70.154 -19.718 70.14 -18.41 70.125 C-4.715 69.811 8.542 68.754 19.324 59.305 C20.742 58.168 20.742 58.168 22.742 58.168 C22.247 37.378 22.247 37.378 21.742 16.168 C11.842 16.168 1.942 16.168 -8.258 16.168 C-8.258 21.778 -8.258 27.388 -8.258 33.168 C-4.298 33.168 -0.338 33.168 3.742 33.168 C4.231 43.426 4.231 43.426 2.055 46.73 C-4.973 51.099 -12.253 50.848 -20.258 50.168 C-25.251 48.839 -29.119 46.18 -32.227 41.98 C-37.263 33.128 -37.609 22.568 -35.133 12.855 C-32.962 6.566 -29.35 2.142 -23.633 -1.27 C-15.815 -4.723 -7.514 -3.592 0 0 Z M-3.258 1.168 C-2.598 1.828 -1.938 2.488 -1.258 3.168 C-1.258 2.508 -1.258 1.848 -1.258 1.168 C-1.918 1.168 -2.578 1.168 -3.258 1.168 Z " fill="#4D312F" transform="translate(743.2578125,323.83203125)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-0.66 2 -1.32 2 -2 2 C-2 3.98 -2 5.96 -2 8 C-0.02 8 1.96 8 4 8 C4 8.99 4 9.98 4 11 C4.99 11.33 5.98 11.66 7 12 C4.833 14.275 2.544 16.158 0 18 C0 17.01 0 16.02 0 15 C-0.66 15 -1.32 15 -2 15 C-2.027 16.604 -2.046 18.208 -2.062 19.812 C-2.074 20.706 -2.086 21.599 -2.098 22.52 C-2.11 25.134 -2.11 25.134 -1 28 C-1.33 28.66 -1.66 29.32 -2 30 C-0.35 30 1.3 30 3 30 C3.124 30.639 3.248 31.279 3.375 31.938 C3.581 32.618 3.788 33.299 4 34 C4.66 34.33 5.32 34.66 6 35 C6 36.32 6 37.64 6 39 C-5.815 38.4 -5.815 38.4 -10 35 C-10.619 34.546 -11.238 34.093 -11.875 33.625 C-13 32 -13 32 -12.688 29.312 C-12.461 28.549 -12.234 27.786 -12 27 C-12.33 27 -12.66 27 -13 27 C-13.195 25.376 -13.381 23.751 -13.562 22.125 C-13.667 21.22 -13.771 20.315 -13.879 19.383 C-14 17 -14 17 -13 15 C-13.99 14.67 -14.98 14.34 -16 14 C-15.67 11.69 -15.34 9.38 -15 7 C-14.34 7 -13.68 7 -13 7 C-12.67 6.01 -12.34 5.02 -12 4 C-9.24 -2.255 -6.125 -0.889 0 0 Z " fill="#9F9D9C" transform="translate(477,153)"/>
<path d="M0 0 C18.009 0.404 30.955 2.751 44 16 C43.67 16.66 43.34 17.32 43 18 C42.443 17.469 41.886 16.938 41.312 16.391 C36.534 11.967 32.089 8.379 26 6 C25.175 5.644 24.35 5.288 23.5 4.922 C18.395 3.577 13.445 3.631 8.188 3.625 C6.624 3.588 6.624 3.588 5.029 3.551 C-1.908 3.525 -6.947 4.608 -13 8 C-13.847 8.458 -14.694 8.915 -15.566 9.387 C-26.584 16.566 -33.203 26.07 -36 39 C-37.218 55.113 -35.556 67.921 -25.477 80.973 C-18.611 88.591 -8.088 93.207 2.069 94.203 C4.176 94.302 6.266 94.329 8.375 94.312 C9.491 94.304 9.491 94.304 10.629 94.295 C21.562 94.055 31.117 90.468 39.219 82.938 C41.834 80.137 41.834 80.137 44 77 C42.642 72.926 39.869 71.58 36.438 69.312 C35.822 68.897 35.206 68.481 34.572 68.053 C33.053 67.028 31.527 66.013 30 65 C29.564 65.398 29.129 65.797 28.68 66.207 C19.92 74 19.92 74 14 74 C14 73.34 14 72.68 14 72 C15.114 71.546 16.228 71.092 17.375 70.625 C20.74 69.116 22.088 67.988 24.5 65.375 C26 64 26 64 28 64 C28.33 63.01 28.66 62.02 29 61 C29.474 61.364 29.948 61.727 30.437 62.102 C32.579 63.739 34.727 65.37 36.875 67 C37.995 67.859 37.995 67.859 39.137 68.734 C39.852 69.276 40.568 69.817 41.305 70.375 C42.294 71.129 42.294 71.129 43.304 71.898 C44.943 73.182 44.943 73.182 47 73 C46.663 78.389 43.599 82.209 40 86 C37.904 87.497 35.808 88.777 33.539 89.992 C32.777 90.491 32.777 90.491 32 91 C32 91.66 32 92.32 32 93 C31.237 93.103 30.474 93.206 29.688 93.312 C26.605 93.887 23.803 94.654 20.812 95.562 C12.486 97.875 4.481 97.221 -4.004 96.121 C-6.074 95.828 -6.074 95.828 -8 97 C-8.99 96.505 -8.99 96.505 -10 96 C-10 95.01 -10 94.02 -10 93 C-10.846 92.773 -11.691 92.546 -12.562 92.312 C-16.05 90.981 -18.467 89.383 -21.387 87.121 C-23.122 85.803 -23.122 85.803 -26 85 C-26.33 84.01 -26.66 83.02 -27 82 C-27.897 81.134 -27.897 81.134 -28.812 80.25 C-31.696 77.284 -35 73.29 -35 69 C-35.99 69 -36.98 69 -38 69 C-37.67 68.01 -37.34 67.02 -37 66 C-37.606 64.311 -38.278 62.644 -39 61 C-39.165 60.257 -39.33 59.515 -39.5 58.75 C-39.665 58.173 -39.83 57.595 -40 57 C-40.66 56.67 -41.32 56.34 -42 56 C-41.01 55.505 -41.01 55.505 -40 55 C-39.642 52.094 -39.642 52.094 -39.562 48.625 C-38.802 35.729 -35.011 25.12 -27 15 C-26.34 15 -25.68 15 -25 15 C-25 14.34 -25 13.68 -25 13 C-19.294 7.425 -10.716 2.188 -2.664 1.867 C-1.345 1.933 -1.345 1.933 0 2 C0 1.34 0 0.68 0 0 Z " fill="#503432" transform="translate(290,299)"/>
<path d="M0 0 C6.338 3.639 6.338 3.639 8.016 7.09 C9.113 11.205 9.097 13.652 7.938 17.812 C5.94 21.098 4.223 22.934 1 25 C0.34 25 -0.32 25 -1 25 C-1 24.34 -1 23.68 -1 23 C-2.114 22.907 -2.114 22.907 -3.25 22.812 C-6.819 21.758 -7.922 20.001 -10 17 C-10.619 17.804 -11.237 18.609 -11.875 19.438 C-14 22 -14 22 -16 23 C-24.958 23.433 -24.958 23.433 -28 21 C-29.669 17.383 -29.436 12.912 -29 9 C-27.748 6.121 -26.703 4.553 -24.25 2.625 C-20.936 1.704 -18.327 2.274 -15 3 C-14.01 3.66 -13.02 4.32 -12 5 C-7.898 4.544 -3.952 3.162 0 2 C0 1.34 0 0.68 0 0 Z M-22 7 C-24.364 8.937 -24.364 8.937 -24.312 12.438 C-24.282 15.886 -24.282 15.886 -22.75 18 C-21.045 19.145 -21.045 19.145 -19 19.125 C-15.777 17.312 -15.219 14.353 -14 11 C-14 12.32 -14 13.64 -14 15 C-13.34 14.67 -12.68 14.34 -12 14 C-12.04 12.077 -12.04 12.077 -13 10 C-16.209 8.047 -18.221 7 -22 7 Z M-11 7 C-11.33 8.98 -11.66 10.96 -12 13 C-10.438 11.688 -10.438 11.688 -9 10 C-9 9.01 -9 8.02 -9 7 C-9.66 7 -10.32 7 -11 7 Z M-3 7 C-5.313 10.469 -5.425 11.922 -5 16 C-3.894 18.025 -3.894 18.025 -2 19 C0.111 19.076 0.111 19.076 2 18 C4.057 15.356 4.014 14.117 3.625 10.75 C3.419 9.842 3.212 8.935 3 8 C0.03 7.505 0.03 7.505 -3 7 Z " fill="#9E9E9C" transform="translate(720,738)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C5.32 2 6.64 2 8 2 C9.208 4.416 9.129 5.959 9.133 8.656 C9.134 9.597 9.135 10.538 9.137 11.508 C9.133 12.495 9.129 13.483 9.125 14.5 C9.131 15.935 9.131 15.935 9.137 17.398 C9.114 34.255 9.114 34.255 6.125 38.938 C5.424 39.618 4.723 40.299 4 41 C3.553 41.541 3.105 42.083 2.645 42.641 C1 44 1 44 -1.121 44.145 C-1.886 44.035 -2.65 43.925 -3.438 43.812 C-4.199 43.716 -4.961 43.619 -5.746 43.52 C-8.115 42.973 -9.933 42.28 -12 41 C-13.265 37.399 -12.939 34.653 -12 31 C-11.67 31.66 -11.34 32.32 -11 33 C-8.644 33.468 -8.644 33.468 -5.938 33.625 C-5.018 33.7 -4.099 33.775 -3.152 33.852 C-2.442 33.901 -1.732 33.95 -1 34 C-1 33.34 -1 32.68 -1 32 C-1.66 31.67 -2.32 31.34 -3 31 C-2 23.571 -2 23.571 -1 20 C-1.33 20 -1.66 20 -2 20 C-2 18.35 -2 16.7 -2 15 C-1.34 15 -0.68 15 0 15 C0 10.05 0 5.1 0 0 Z M-1 24 C-1 26.31 -1 28.62 -1 31 C-0.67 31 -0.34 31 0 31 C0 28.69 0 26.38 0 24 C-0.33 24 -0.66 24 -1 24 Z " fill="#9A9A99" transform="translate(399,641)"/>
<path d="M0 0 C1.875 0.25 1.875 0.25 4 1 C5.25 3.062 5.25 3.062 6 5 C6.66 4.34 7.32 3.68 8 3 C10 6 10 6 9.625 8.188 C9.316 9.085 9.316 9.085 9 10 C8.34 10 7.68 10 7 10 C7 10.66 7 11.32 7 12 C6.34 12 5.68 12 5 12 C4.67 13.65 4.34 15.3 4 17 C4.66 17.33 5.32 17.66 6 18 C5.34 18 4.68 18 4 18 C5.635 21.125 5.635 21.125 8.125 21.688 C8.744 21.791 9.363 21.894 10 22 C9.67 23.98 9.34 25.96 9 28 C8.67 27.34 8.34 26.68 8 26 C7.051 26.021 6.103 26.041 5.125 26.062 C2 26 2 26 0 25 C-0.625 22.938 -0.625 22.938 -1 21 C-1.66 21.66 -2.32 22.32 -3 23 C-3 21.35 -3 19.7 -3 18 C-2.34 18 -1.68 18 -1 18 C-0.833 15.083 -0.833 15.083 -1 12 C-1.66 11.34 -2.32 10.68 -3 10 C-3.33 15.28 -3.66 20.56 -4 26 C-15.625 26.25 -15.625 26.25 -19 24 C-19 23.01 -19 22.02 -19 21 C-19.99 20.67 -20.98 20.34 -22 20 C-21.34 20 -20.68 20 -20 20 C-20.084 19.408 -20.168 18.817 -20.254 18.207 C-20.809 13.901 -21.232 10.244 -20 6 C-19.34 6 -18.68 6 -18 6 C-17.67 5.01 -17.34 4.02 -17 3 C-14.096 7.292 -12.475 11.147 -13.438 16.375 C-13.623 16.911 -13.809 17.447 -14 18 C-12.35 17.67 -10.7 17.34 -9 17 C-9 13.37 -9 9.74 -9 6 C-7.68 5.34 -6.36 4.68 -5 4 C-4.34 4.66 -3.68 5.32 -3 6 C-2.01 4.02 -1.02 2.04 0 0 Z M-11 18 C-10.34 18.66 -9.68 19.32 -9 20 C-9 19.34 -9 18.68 -9 18 C-9.66 18 -10.32 18 -11 18 Z " fill="#9F9E9E" transform="translate(310,735)"/>
<path d="M0 0 C2.058 2.011 2.058 2.011 4.281 1.805 C5.031 4.555 5.031 4.555 5.281 7.805 C3.344 10.18 3.344 10.18 1.281 11.805 C0.621 11.475 -0.039 11.145 -0.719 10.805 C0.271 9.815 1.261 8.825 2.281 7.805 C-0.526 1.291 -6.563 -1.9 -12.906 -4.508 C-23.704 -8.642 -35.892 -8.446 -46.578 -4.008 C-53.873 -0.38 -58.326 3.821 -61.848 11.156 C-63.898 17.39 -63.862 25.184 -61.219 31.242 C-55.325 40.85 -44.892 42.976 -34.719 45.805 C-32.32 46.482 -29.924 47.168 -27.531 47.867 C-26.527 48.147 -25.523 48.427 -24.488 48.715 C-21.312 49.965 -19.186 51.453 -16.719 53.805 C-16.156 56.867 -16.156 56.867 -16.719 59.805 C-20.688 63.774 -24.945 64.005 -30.344 64.117 C-39.47 64.051 -45.462 61.015 -52.719 55.805 C-56.197 57.32 -58.675 59.382 -61.469 61.93 C-62.668 63.016 -62.668 63.016 -63.891 64.125 C-64.494 64.679 -65.097 65.234 -65.719 65.805 C-59.426 74.085 -51.982 78.904 -41.719 80.805 C-41.719 81.135 -41.719 81.465 -41.719 81.805 C-51.559 81.954 -58.724 77.704 -65.844 71.117 C-67.705 68.822 -68.14 67.611 -68.719 64.805 C-68.199 64.43 -67.68 64.054 -67.145 63.668 C-63.848 61.249 -60.672 58.894 -57.844 55.93 C-57.142 55.228 -56.441 54.527 -55.719 53.805 C-55.059 53.805 -54.399 53.805 -53.719 53.805 C-53.389 53.145 -53.059 52.485 -52.719 51.805 C-52.121 52.465 -51.522 53.125 -50.906 53.805 C-48.844 56.142 -48.844 56.142 -45.719 55.805 C-45.274 56.266 -44.829 56.728 -44.371 57.203 C-42.177 59.33 -40.229 59.702 -37.281 60.43 C-36.334 60.669 -35.386 60.909 -34.41 61.156 C-33.522 61.37 -32.634 61.584 -31.719 61.805 C-30.385 62.137 -29.052 62.471 -27.719 62.805 C-27.389 62.145 -27.059 61.485 -26.719 60.805 C-25.399 60.805 -24.079 60.805 -22.719 60.805 C-21.377 60.507 -20.04 60.182 -18.719 59.805 C-19.049 57.825 -19.379 55.845 -19.719 53.805 C-21.039 53.475 -22.359 53.145 -23.719 52.805 C-23.719 52.145 -23.719 51.485 -23.719 50.805 C-26.007 50.249 -28.3 49.711 -30.594 49.18 C-31.87 48.878 -33.146 48.576 -34.461 48.266 C-35.536 48.114 -36.611 47.961 -37.719 47.805 C-38.379 48.465 -39.039 49.125 -39.719 49.805 C-41.039 49.805 -42.359 49.805 -43.719 49.805 C-43.783 49.189 -43.848 48.572 -43.914 47.938 C-44.532 45.6 -44.532 45.6 -46.898 44.609 C-47.788 44.303 -48.677 43.996 -49.594 43.68 C-57.592 40.858 -60.988 36.323 -64.719 28.805 C-66.648 21.332 -66.023 14.42 -62.598 7.559 C-57.988 -0.185 -51.273 -5.273 -42.719 -8.07 C-27.507 -11.879 -12.513 -10.104 0 0 Z " fill="#4A302E" transform="translate(395.71875,438.1953125)"/>
<path d="M0 0 C0.958 0.047 1.917 0.094 2.904 0.143 C3.939 0.191 4.973 0.24 6.039 0.289 C7.127 0.345 8.216 0.401 9.338 0.459 C10.43 0.511 11.523 0.564 12.648 0.617 C15.357 0.748 18.066 0.883 20.775 1.022 C20.775 1.352 20.775 1.682 20.775 2.022 C13.515 2.022 6.255 2.022 -1.225 2.022 C-0.895 19.182 -0.565 36.342 -0.225 54.022 C-4.722 49.525 -4.722 49.525 -6.557 46.299 C-6.959 45.602 -7.361 44.904 -7.775 44.186 C-8.192 43.451 -8.608 42.716 -9.037 41.959 C-9.929 40.414 -10.822 38.87 -11.717 37.326 C-12.149 36.581 -12.58 35.836 -13.025 35.068 C-14.511 32.534 -16.07 30.053 -17.662 27.584 C-20.887 22.577 -24.037 17.528 -27.162 12.459 C-27.66 11.66 -28.157 10.861 -28.67 10.038 C-29.13 9.289 -29.59 8.54 -30.065 7.768 C-30.481 7.094 -30.897 6.421 -31.326 5.727 C-32.225 4.022 -32.225 4.022 -32.225 2.022 C-39.485 2.022 -46.745 2.022 -54.225 2.022 C-54.225 32.052 -54.225 62.082 -54.225 93.022 C-47.955 93.022 -41.685 93.022 -35.225 93.022 C-35.225 93.352 -35.225 93.682 -35.225 94.022 C-45.62 94.517 -45.62 94.517 -56.225 95.022 C-56.225 64.002 -56.225 32.982 -56.225 1.022 C-31.225 1.022 -31.225 1.022 -25.451 6.549 C-24.23 8.454 -24.23 8.454 -23.522 10.145 C-23.105 11.088 -23.105 11.088 -22.68 12.051 C-22.406 12.681 -22.132 13.31 -21.85 13.959 C-21.247 15.169 -20.635 16.374 -20.014 17.574 C-19.071 19.405 -18.139 21.237 -17.245 23.092 C-13.323 31.185 -8.224 38.566 -3.225 46.022 C-3.227 45.46 -3.229 44.899 -3.232 44.32 C-3.254 38.497 -3.269 32.674 -3.28 26.851 C-3.285 24.675 -3.292 22.499 -3.3 20.323 C-3.312 17.203 -3.318 14.083 -3.323 10.963 C-3.328 9.984 -3.333 9.005 -3.338 7.996 C-3.338 7.095 -3.338 6.194 -3.338 5.266 C-3.341 4.469 -3.343 3.672 -3.345 2.85 C-3.165 0.104 -2.75 0.027 0 0 Z M-3.225 47.022 C-2.225 49.022 -2.225 49.022 -2.225 49.022 Z " fill="#442A2A" transform="translate(654.224853515625,299.978271484375)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.66 2.98 3.32 4 4 C4 10.365 3.612 16.026 -0.5 21.062 C-5.82 25.185 -11.394 24.547 -18 25 C-18 16.42 -18 7.84 -18 -1 C-18.66 -1.33 -19.32 -1.66 -20 -2 C-12.543 -4.725 -7.004 -3.652 0 0 Z M-11 2 C-10.34 2.66 -9.68 3.32 -9 4 C-9.99 4 -10.98 4 -12 4 C-13.001 7.311 -13.104 10.301 -13.062 13.75 C-13.053 14.735 -13.044 15.72 -13.035 16.734 C-13.024 17.482 -13.012 18.23 -13 19 C-10.072 19 -7.796 18.86 -5 18 C-3.5 16.25 -3.5 16.25 -3 14 C-3.298 11.803 -3.298 11.803 -4 10 C-3.34 10 -2.68 10 -2 10 C-2.184 7.059 -2.184 7.059 -3 4 C-5.859 1.831 -7.284 2 -11 2 Z " fill="#8E8E8D" transform="translate(492,737)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3 2.66 3 3.32 3 4 C3.315 5.338 3.646 6.672 4 8 C4 6.68 4 5.36 4 4 C4.66 4 5.32 4 6 4 C6.33 3.34 6.66 2.68 7 2 C9.603 4.712 9.995 5.891 10.188 9.75 C10.126 10.822 10.064 11.895 10 13 C10.516 12.319 11.031 11.639 11.562 10.938 C14 9 14 9 17.625 9.062 C21 10 21 10 23 13 C21.35 13.66 19.7 14.32 18 15 C19.32 15.33 20.64 15.66 22 16 C22.33 17.65 22.66 19.3 23 21 C20.165 23.752 18.846 25 14.812 25 C12 24 12 24 10.625 21.938 C10.419 21.298 10.212 20.659 10 20 C10.33 19.67 10.66 19.34 11 19 C10.34 19 9.68 19 9 19 C9 20.65 9 22.3 9 24 C5.25 25.125 5.25 25.125 3 24 C0.902 24.399 0.902 24.399 -1 25 C-1 24.34 -1 23.68 -1 23 C-1.66 22.67 -2.32 22.34 -3 22 C-2.34 22 -1.68 22 -1 22 C-1.115 20.416 -1.242 18.833 -1.375 17.25 C-1.445 16.368 -1.514 15.487 -1.586 14.578 C-1.999 12.009 -2.712 10.242 -4 8 C-3.34 7.34 -2.68 6.68 -2 6 C-1.286 4.016 -0.614 2.017 0 0 Z M3 14 C4 17 4 17 4 17 Z M10 16 C11 18 11 18 11 18 Z M3 19 C4 22 4 22 4 22 Z M16 19 C15.67 19.66 15.34 20.32 15 21 C15.99 21 16.98 21 18 21 C18 20.34 18 19.68 18 19 C17.34 19 16.68 19 16 19 Z " fill="#8A8B89" transform="translate(433,880)"/>
<path d="M0 0 C1.101 7.018 1.097 13.906 1 21 C-2.606 22.202 -5.21 22.327 -9 22 C-11.938 20 -11.938 20 -14 18 C-14.66 18.99 -15.32 19.98 -16 21 C-19.312 22 -19.312 22 -23 22 C-25.938 19.75 -25.938 19.75 -28 17 C-28 16.01 -28 15.02 -28 14 C-28.66 13.67 -29.32 13.34 -30 13 C-29.34 12.67 -28.68 12.34 -28 12 C-27.526 11.196 -27.051 10.391 -26.562 9.562 C-25 7 -25 7 -22 5 C-18.651 5.324 -16.481 6.785 -14 9 C-14 9.66 -14 10.32 -14 11 C-13.464 10.175 -12.928 9.35 -12.375 8.5 C-10 6 -10 6 -6.75 5.688 C-5.842 5.791 -4.935 5.894 -4 6 C-4 5.01 -4 4.02 -4 3 C-4.66 2.67 -5.32 2.34 -6 2 C-3 0 -3 0 0 0 Z M-22 10 C-21.67 10.66 -21.34 11.32 -21 12 C-20.34 11.34 -19.68 10.68 -19 10 C-19.99 10 -20.98 10 -22 10 Z M-9 10 C-9.33 11.98 -9.66 13.96 -10 16 C-8.02 16.99 -8.02 16.99 -6 18 C-3.713 16.161 -3.713 16.161 -3.875 13.375 C-3.916 12.591 -3.957 11.808 -4 11 C-5.65 10.67 -7.3 10.34 -9 10 Z M-15 14 C-17.64 14.33 -20.28 14.66 -23 15 C-19.865 16.858 -17.625 17.201 -14 17 C-14.33 16.01 -14.66 15.02 -15 14 Z " fill="#8A8B8A" transform="translate(415,883)"/>
<path d="M0 0 C2.324 0.185 2.324 0.185 5 -2 C10.345 -2.327 10.345 -2.327 13 -1 C13 -0.34 13 0.32 13 1 C13.66 1.33 14.32 1.66 15 2 C14.01 2.66 13.02 3.32 12 4 C12.99 4.66 13.98 5.32 15 6 C15.66 5.67 16.32 5.34 17 5 C16 8 16 8 15 9 C16.32 9 17.64 9 19 9 C19.495 10.98 19.495 10.98 20 13 C16.25 14.125 16.25 14.125 14 13 C14 12.34 14 11.68 14 11 C13.34 11 12.68 11 12 11 C12 11.66 12 12.32 12 13 C5.6 14.354 5.6 14.354 2.562 12.562 C2.047 12.047 1.531 11.531 1 11 C0.484 11.516 -0.031 12.031 -0.562 12.562 C-4.016 14.599 -6.19 13.917 -10 13 C-12.661 10.339 -12.785 8.729 -13 5 C-11.82 1.769 -10.799 -0.311 -8.188 -2.562 C-4.84 -3.232 -2.885 -1.631 0 0 Z M-8 2 C-7.67 2.66 -7.34 3.32 -7 4 C-5.68 4 -4.36 4 -3 4 C-3 3.34 -3 2.68 -3 2 C-4.65 2 -6.3 2 -8 2 Z M8 2 C7.67 2.66 7.34 3.32 7 4 C8.32 3.67 9.64 3.34 11 3 C10.01 2.67 9.02 2.34 8 2 Z M3 6 C2.34 6.66 1.68 7.32 1 8 C1.99 8 2.98 8 4 8 C4.33 7.34 4.66 6.68 5 6 C4.34 6 3.68 6 3 6 Z M-8 7 C-7.67 7.66 -7.34 8.32 -7 9 C-6.67 8.34 -6.34 7.68 -6 7 C-6.66 7 -7.32 7 -8 7 Z M5 7 C5.33 7.99 5.66 8.98 6 10 C6 9.01 6 8.02 6 7 C5.67 7 5.34 7 5 7 Z " fill="#898988" transform="translate(589,891)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C7.98 5.94 9.96 11.88 12 18 C15.768 9.101 15.768 9.101 19 0 C20.98 0 22.96 0 25 0 C22.989 5.819 20.959 11.63 18.906 17.435 C18.348 19.014 17.793 20.593 17.242 22.175 C16.612 23.975 15.963 25.769 15.312 27.562 C14.958 28.554 14.604 29.545 14.238 30.566 C12.834 33.326 11.689 34.492 9 36 C5.25 36.188 5.25 36.188 2 36 C3 34 3 34 5.625 31.875 C8.414 29.154 8.414 29.154 7.836 25.367 C7.103 20.791 5.628 16.574 4 12.25 C3.457 10.777 2.915 9.303 2.375 7.828 C2.015 6.868 2.015 6.868 1.648 5.888 C0.981 3.944 0.47 1.999 0 0 Z " fill="#B6B5B4" transform="translate(539,163)"/>
<path d="M0 0 C3.72 2.916 6.002 4.742 6.734 9.629 C6.959 14.719 7.091 17.76 4 21.938 C-1.27 25.614 -4.302 26.733 -10.688 25.812 C-14.299 23.661 -16.119 21.662 -17.688 17.812 C-18.379 12.491 -18.319 8.098 -15.938 3.25 C-11.407 -1.659 -6.34 -2.801 0 0 Z M-11.625 5.625 C-12.906 7.857 -12.906 7.857 -12.688 10.812 C-12.027 10.812 -11.368 10.812 -10.688 10.812 C-10.688 11.472 -10.688 12.133 -10.688 12.812 C-11.348 12.812 -12.007 12.812 -12.688 12.812 C-12.438 16.552 -12.297 18.271 -9.438 20.812 C-6.73 22.08 -6.73 22.08 -4 21.188 C-1.42 19.878 -1.42 19.878 0.312 16.812 C0.886 12.771 0.819 9.033 -0.75 5.25 C-4.336 2.59 -8.349 2.56 -11.625 5.625 Z " fill="#B5B4B4" transform="translate(282.6875,735.1875)"/>
<path d="M0 0 C1.305 3.958 1.133 7.957 1.114 12.073 C1.114 12.883 1.114 13.694 1.114 14.528 C1.113 17.201 1.105 19.874 1.098 22.547 C1.096 24.402 1.094 26.257 1.093 28.112 C1.09 32.992 1.08 37.871 1.069 42.75 C1.058 47.731 1.054 52.711 1.049 57.691 C1.038 67.461 1.021 77.23 1 87 C0.67 87 0.34 87 0 87 C-1.056 58.997 -1.104 31.02 -1 3 C-7.93 3 -14.86 3 -22 3 C-22 19.83 -22 36.66 -22 54 C-25.751 50.249 -28.133 46.911 -30.812 42.438 C-31.232 41.765 -31.652 41.093 -32.084 40.4 C-33.14 38.648 -34.079 36.827 -35 35 C-34.67 34.01 -34.34 33.02 -34 32 C-33.434 32.938 -32.868 33.877 -32.285 34.844 C-31.544 36.063 -30.804 37.281 -30.062 38.5 C-29.689 39.12 -29.316 39.74 -28.932 40.379 C-27.982 41.936 -26.994 43.47 -26 45 C-25.67 45 -25.34 45 -25 45 C-25.008 43.358 -25.008 43.358 -25.016 41.683 C-25.034 37.611 -25.045 33.539 -25.055 29.467 C-25.06 27.706 -25.067 25.946 -25.075 24.185 C-25.088 21.651 -25.093 19.116 -25.098 16.582 C-25.103 15.799 -25.108 15.015 -25.113 14.208 C-25.114 10.344 -24.944 6.755 -24 3 C-24.99 2.67 -25.98 2.34 -27 2 C-23.952 0.984 -21.653 0.802 -18.457 0.684 C-17.384 0.642 -16.311 0.6 -15.205 0.557 C-14.086 0.517 -12.966 0.478 -11.812 0.438 C-10.681 0.394 -9.55 0.351 -8.385 0.307 C-5.59 0.201 -2.795 0.098 0 0 Z " fill="#6F4440" transform="translate(693,428)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C28.475 4.455 28.475 4.455 31 9 C30.67 9.99 30.34 10.98 30 12 C27.525 7.545 27.525 7.545 25 3 C13.615 2.505 13.615 2.505 2 2 C2 31.04 2 60.08 2 90 C7.94 90 13.88 90 20 90 C20 71.19 20 52.38 20 33 C20.33 33 20.66 33 21 33 C21 52.14 21 71.28 21 91 C14.07 91 7.14 91 0 91 C0 60.97 0 30.94 0 0 Z " fill="#EA8577" transform="translate(447,302)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 7.59 7 15.18 7 23 C6.01 22.34 5.02 21.68 4 21 C3.67 25.62 3.34 30.24 3 35 C3.99 35 4.98 35 6 35 C6 33.02 6 31.04 6 29 C6.33 29 6.66 29 7 29 C7 31.64 7 34.28 7 37 C5.02 37 3.04 37 1 37 C1 34.03 1 31.06 1 28 C-4.28 28 -9.56 28 -15 28 C-15 19.856 -10.988 15.181 -6 9 C-4.058 12.516 -3.817 14.972 -4 19 C-3.01 19.33 -2.02 19.66 -1 20 C-1 20.33 -1 20.66 -1 21 C-2.98 21.33 -4.96 21.66 -7 22 C-4.36 22.33 -1.72 22.66 1 23 C0.858 20.646 0.711 18.292 0.562 15.938 C0.481 14.627 0.4 13.316 0.316 11.965 C0.106 9.323 -0.2 6.687 -0.598 4.066 C-0.73 3.054 -0.863 2.043 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BCBBBB" transform="translate(630,645)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C3.972 0.977 5.945 0.948 7.916 0.883 C21.141 0.494 32.951 2.668 43 12 C44.805 14.594 44.805 14.594 46 17 C45.058 20.887 42.111 22.708 39 25 C38.34 24.67 37.68 24.34 37 24 C38.98 22.35 40.96 20.7 43 19 C42.451 15.264 41.112 13.819 38.188 11.5 C29.421 5.14 21.005 3.659 10.375 3.562 C9.505 3.552 8.634 3.542 7.737 3.531 C-2.617 3.671 -10.882 6.623 -19 13 C-19.944 13.737 -19.944 13.737 -20.906 14.488 C-27.198 20.048 -32.59 29.925 -33.161 38.385 C-33.207 41.444 -33.235 44.503 -33.25 47.562 C-33.271 48.61 -33.291 49.657 -33.312 50.736 C-33.372 62.619 -30.283 73.055 -22.184 82.008 C-15.884 87.888 -8.245 91.613 0 94 C0 94.33 0 94.66 0 95 C-10.555 94.602 -18.873 89.124 -25.992 81.559 C-31.315 74.776 -34.119 67.309 -36 59 C-36.66 58.67 -37.32 58.34 -38 58 C-37.67 57.34 -37.34 56.68 -37 56 C-36.906 54.147 -36.817 52.294 -36.761 50.44 C-36.297 36.266 -33.146 22.893 -22.605 12.68 C-15.654 6.892 -9.096 2.758 0 2 C0 1.34 0 0.68 0 0 Z " fill="#533330" transform="translate(720,299)"/>
<path d="M0 0 C6.331 4.11 14.148 9.238 17.895 15.879 C17.582 18.129 17.582 18.129 16.895 19.879 C16.554 19.383 16.214 18.886 15.863 18.375 C7.28 6.345 -3.992 -0.72 -18.551 -3.652 C-33.004 -5.558 -46.206 -4.207 -59.145 2.891 C-61.105 3.879 -61.105 3.879 -63.105 3.879 C-63.105 4.539 -63.105 5.199 -63.105 5.879 C-64.609 7.324 -64.609 7.324 -66.668 9.004 C-77.272 18.11 -83.791 28.557 -85.59 42.684 C-85.691 44.039 -85.779 45.396 -85.855 46.754 C-85.899 47.477 -85.942 48.2 -85.986 48.945 C-86.342 57.031 -85.314 64.899 -84.105 72.879 C-88.895 68.09 -89.273 58.081 -89.543 51.504 C-88.98 34.894 -82.225 19.214 -70.105 7.879 C-50.56 -8.82 -22.809 -12.45 0 0 Z " fill="#424140" transform="translate(542.10546875,122.12109375)"/>
<path d="M0 0 C1.668 0.952 3.277 2.005 4.875 3.07 C8.281 2.896 9.62 2.298 12.188 0.008 C12.744 -0.632 13.301 -1.271 13.875 -1.93 C14.535 -1.6 15.195 -1.27 15.875 -0.93 C10.23 4.872 10.23 4.872 6.875 7.07 C6.215 7.07 5.555 7.07 4.875 7.07 C4.875 6.41 4.875 5.75 4.875 5.07 C4.215 5.07 3.555 5.07 2.875 5.07 C2.875 4.41 2.875 3.75 2.875 3.07 C-4.657 -0.046 -11.988 -0.996 -20.125 0.07 C-22.07 1.335 -22.07 1.335 -23.125 3.07 C-23.438 5.161 -23.438 5.161 -22.125 7.07 C-16.847 9.678 -11.515 11.076 -5.812 12.445 C3.871 14.891 15.525 17.951 21.09 27.059 C24.61 33.91 26.259 40.062 24.156 47.672 C20.585 57.204 15.075 62.804 5.875 67.07 C-3.129 70.189 -12.66 71.013 -22.125 70.07 C-22.785 69.41 -23.445 68.75 -24.125 68.07 C-22.881 68.033 -21.637 67.996 -20.355 67.957 C-7.16 67.475 5.151 66.708 15.875 58.07 C21.112 51.822 22.255 45.894 22.148 37.887 C21.573 31.96 18.992 27.834 14.688 23.82 C6.754 17.737 -2.764 15.674 -12.305 13.359 C-21.59 10.98 -21.59 10.98 -25.125 7.07 C-25.312 3.508 -25.312 3.508 -24.125 0.07 C-17.204 -5.575 -7.4 -3.673 0 0 Z " fill="#523634" transform="translate(379.125,451.9296875)"/>
<path d="M0 0 C1.652 1.25 1.652 1.25 3 3 C3.293 5.48 3.293 5.48 3.188 8.188 C3.16 9.089 3.133 9.99 3.105 10.918 C3.071 11.605 3.036 12.292 3 13 C-1.072 15.036 -3.602 15.147 -8 14 C-8.99 13.34 -9.98 12.68 -11 12 C-11.66 12 -12.32 12 -13 12 C-13 12.66 -13 13.32 -13 14 C-19.24 15.32 -19.24 15.32 -22.375 14 C-24.775 11.046 -24.935 8.764 -25 5 C-24 2.188 -24 2.188 -22 0 C-14.974 -2.951 -6.982 -3.159 0 0 Z M-19 3 C-19.66 4.98 -20.32 6.96 -21 9 C-20.34 9.66 -19.68 10.32 -19 11 C-18.01 10.67 -17.02 10.34 -16 10 C-16 9.34 -16 8.68 -16 8 C-15.01 8 -14.02 8 -13 8 C-13.66 6.35 -14.32 4.7 -15 3 C-16.32 3 -17.64 3 -19 3 Z M-11 3 C-11 6 -11 6 -11 6 Z M-5.75 4.188 C-7.298 6.007 -7.298 6.007 -6.688 8.688 C-6.461 9.451 -6.234 10.214 -6 11 C-4.35 10.67 -2.7 10.34 -1 10 C-1 9.01 -1 8.02 -1 7 C-1 5.68 -1 4.36 -1 3 C-3.939 2.719 -3.939 2.719 -5.75 4.188 Z " fill="#878685" transform="translate(634,890)"/>
<path d="M0 0 C6.236 3.747 10.83 9.169 13.094 16.141 C14.926 25.938 13.76 35.175 8.438 43.562 C3.359 48.973 -3.169 51.598 -10.5 51.938 C-17.548 51.593 -23.328 49.458 -28.383 44.445 C-35.083 36.127 -35.358 26.631 -34.5 16.312 C-32.873 9.123 -28.539 4.372 -22.5 0.312 C-15.772 -3.018 -6.828 -3.212 0 0 Z M-27.875 6.438 C-32.747 12.757 -34.243 20.506 -33.5 28.312 C-32.664 31.382 -31.653 34.348 -30.5 37.312 C-29.51 37.312 -28.52 37.312 -27.5 37.312 C-27.83 38.632 -28.16 39.952 -28.5 41.312 C-27.51 41.972 -26.52 42.632 -25.5 43.312 C-24.819 44.137 -24.139 44.962 -23.438 45.812 C-19.396 49.252 -15.742 50.017 -10.5 50.312 C-2.476 49.577 3.222 46.424 8.5 40.312 C10.563 36.257 11.5 33.884 11.5 29.312 C10.84 29.312 10.18 29.312 9.5 29.312 C9.875 27.375 9.875 27.375 10.5 25.312 C11.16 24.983 11.82 24.652 12.5 24.312 C12.005 22.827 12.005 22.827 11.5 21.312 C11.582 20.426 11.665 19.539 11.75 18.625 C11.399 13.975 9.153 11.046 6.5 7.312 C5.84 8.303 5.18 9.293 4.5 10.312 C4.5 9.652 4.5 8.993 4.5 8.312 C3.84 8.312 3.18 8.312 2.5 8.312 C2.232 7.343 1.964 6.374 1.688 5.375 C1.1 3.859 1.1 3.859 0.5 2.312 C-0.49 1.983 -1.48 1.652 -2.5 1.312 C-2.5 2.303 -2.5 3.293 -2.5 4.312 C-3.16 3.983 -3.82 3.652 -4.5 3.312 C-4.17 2.322 -3.84 1.332 -3.5 0.312 C-12.409 -2.657 -21.436 -0.304 -27.875 6.438 Z M3.5 5.312 C3.5 5.973 3.5 6.632 3.5 7.312 C4.16 6.983 4.82 6.652 5.5 6.312 C4.84 5.983 4.18 5.652 3.5 5.312 Z " fill="#5C3936" transform="translate(398.5,322.6875)"/>
<path d="M0 0 C4.152 0.691 5.726 2.284 8.512 5.418 C11.199 10.802 10.719 15.643 9.512 21.418 C7.441 24.917 5.437 27.219 1.512 28.418 C-3.369 28.89 -5.515 28.941 -9.863 26.605 C-12.488 24.418 -12.488 24.418 -14.488 21.418 C-14.613 18.73 -14.613 18.73 -14.488 16.418 C-13.828 17.738 -13.168 19.058 -12.488 20.418 C-11.828 20.418 -11.168 20.418 -10.488 20.418 C-11.148 19.758 -11.808 19.098 -12.488 18.418 C-12.246 16.035 -12.246 16.035 -11.613 13.293 C-11.41 12.383 -11.206 11.473 -10.996 10.535 C-10.829 9.836 -10.661 9.138 -10.488 8.418 C-10.158 8.418 -9.828 8.418 -9.488 8.418 C-9.451 9.177 -9.414 9.936 -9.375 10.719 C-9.276 12.21 -9.276 12.21 -9.176 13.73 C-9.089 15.21 -9.089 15.21 -9 16.719 C-8.434 19.704 -7.975 20.714 -5.488 22.418 C-1.863 22.668 -1.863 22.668 1.512 22.418 C3.958 18.112 4.226 14.281 3.512 9.418 C2.072 6.727 2.072 6.727 -0.488 5.418 C-3.656 5.083 -3.656 5.083 -6.488 5.418 C-5.828 5.088 -5.168 4.758 -4.488 4.418 C-5.148 3.758 -5.808 3.098 -6.488 2.418 C-8.596 2.736 -8.596 2.736 -10.488 3.418 C-7.404 -0.36 -4.643 -0.142 0 0 Z " fill="#BDBCBB" transform="translate(526.48828125,161.58203125)"/>
<path d="M0 0 C0.495 1.485 0.495 1.485 1 3 C0.67 3.66 0.34 4.32 0 5 C0 5.99 0 6.98 0 8 C0 8.66 0 9.32 0 10 C-0.99 10 -1.98 10 -3 10 C-3.33 11.98 -3.66 13.96 -4 16 C-4.66 16 -5.32 16 -6 16 C-6.124 16.71 -6.247 17.421 -6.375 18.152 C-7.019 21.088 -7.937 23.753 -9 26.562 C-10.422 30.296 -10.422 30.296 -11.473 34.141 C-12.414 37.46 -14.701 39.477 -17 42 C-20.215 43.607 -23.436 43.057 -27 43 C-27.8 39.713 -28.097 38.29 -27 35 C-23.938 33.812 -23.938 33.812 -21 33 C-21.325 31.753 -21.325 31.753 -21.656 30.48 C-21.935 29.394 -22.213 28.307 -22.5 27.188 C-22.778 26.109 -23.057 25.03 -23.344 23.918 C-24 21 -24 21 -24 18 C-24.99 17.67 -25.98 17.34 -27 17 C-26.34 17 -25.68 17 -25 17 C-25.784 15.351 -26.578 13.706 -27.375 12.062 C-27.816 11.146 -28.257 10.229 -28.711 9.285 C-29.792 6.871 -29.792 6.871 -32 6 C-30.438 4 -30.438 4 -28 2 C-25.668 1.92 -23.332 1.913 -21 2 C-20.662 3.071 -20.325 4.142 -19.977 5.246 C-19.526 6.643 -19.076 8.04 -18.625 9.438 C-18.403 10.145 -18.182 10.852 -17.953 11.58 C-17.359 13.406 -16.685 15.206 -16 17 C-15.34 17.33 -14.68 17.66 -14 18 C-12.859 13.017 -11.778 8.055 -11 3 C-10.374 2.951 -9.747 2.902 -9.102 2.852 C-7.876 2.739 -7.876 2.739 -6.625 2.625 C-5.813 2.555 -5.001 2.486 -4.164 2.414 C-1.797 2.171 -1.797 2.171 0 0 Z M-28 5 C-27.608 6.176 -27.216 7.351 -26.812 8.562 C-26.544 9.373 -26.276 10.184 -26 11.02 C-25.244 13.272 -24.47 15.518 -23.688 17.762 C-23.229 19.091 -22.771 20.421 -22.312 21.75 C-22.086 22.388 -21.86 23.025 -21.626 23.682 C-20.419 27.211 -19.5 30.268 -20 34 C-22.438 36.688 -22.438 36.688 -25 39 C-25.33 39.66 -25.66 40.32 -26 41 C-20.798 41.514 -20.798 41.514 -16.443 38.919 C-13.734 35.682 -12.515 32.953 -11.105 28.984 C-10.868 28.328 -10.631 27.672 -10.387 26.995 C-9.641 24.918 -8.914 22.834 -8.188 20.75 C-7.682 19.335 -7.174 17.921 -6.666 16.508 C-4.196 10.854 -4.196 10.854 -3 5 C-4.98 5 -6.96 5 -9 5 C-9.413 6.114 -9.825 7.227 -10.25 8.375 C-12.088 13.282 -14.042 18.14 -16 23 C-17.98 17.06 -19.96 11.12 -22 5 C-23.98 5 -25.96 5 -28 5 Z " fill="#7C7B78" transform="translate(567,158)"/>
<path d="M0 0 C7.743 -0.298 7.743 -0.298 11 2 C11.828 4.277 11.828 4.277 12.25 6.938 C12.4 7.813 12.549 8.688 12.703 9.59 C13.05 12.409 13 15.161 13 18 C10.43 20.775 9.088 20.996 5.25 21.188 C4.178 21.126 3.105 21.064 2 21 C1.67 21.66 1.34 22.32 1 23 C1 22.34 1 21.68 1 21 C-0.65 20.67 -2.3 20.34 -4 20 C-3.979 19.237 -3.959 18.474 -3.938 17.688 C-3.871 14.898 -3.871 14.898 -5 12 C-4.722 9.659 -4.395 7.324 -4 5 C-3.01 6.485 -3.01 6.485 -2 8 C-1.34 5.36 -0.68 2.72 0 0 Z M1 7 C2 9 2 9 2 9 Z M2 13 C2.66 14.32 3.32 15.64 4 17 C4.66 16.67 5.32 16.34 6 16 C5.67 15.01 5.34 14.02 5 13 C4.01 13 3.02 13 2 13 Z " fill="#959593" transform="translate(422,740)"/>
<path d="M0 0 C8.014 0.342 8.014 0.342 11.438 2.625 C13.409 5.621 14.198 8.523 15 12 C10.003 12.754 5.899 13.352 1 12 C1 12.66 1 13.32 1 14 C3.791 15.209 3.791 15.209 7 16 C8.349 15.365 9.685 14.702 11 14 C13.25 14.875 13.25 14.875 15 16 C12.901 18.709 11.253 20.73 7.805 21.473 C3.862 21.622 1.714 21.535 -1.5 19.125 C-4.504 14.87 -4.659 11.111 -4 6 C-2.903 3.638 -1.67 2.035 0 0 Z M3 5 C2.01 6.485 2.01 6.485 1 8 C3.64 8 6.28 8 9 8 C8.34 7.01 7.68 6.02 7 5 C5.68 5 4.36 5 3 5 Z " fill="#999998" transform="translate(501,740)"/>
<path d="M0 0 C1.658 6.066 -0.556 10.427 -2.688 16.125 C-3.216 17.627 -3.216 17.627 -3.756 19.16 C-4.108 20.121 -4.461 21.081 -4.824 22.07 C-5.145 22.944 -5.465 23.819 -5.796 24.719 C-7.514 27.972 -9.68 29.48 -13.062 30.812 C-15 31 -15 31 -18 29 C-18 28.34 -18 27.68 -18 27 C-16.02 26.01 -14.04 25.02 -12 24 C-12.144 23.301 -12.289 22.603 -12.438 21.883 C-12.623 20.973 -12.809 20.063 -13 19.125 C-13.186 18.22 -13.371 17.315 -13.562 16.383 C-14 14 -14 14 -14 12 C-14.66 12 -15.32 12 -16 12 C-16.33 12.66 -16.66 13.32 -17 14 C-16.967 12.014 -16.935 10.029 -16.902 8.043 C-16.917 5.797 -16.917 5.797 -18 3 C-16.35 2.67 -14.7 2.34 -13 2 C-12.023 3.79 -11.047 5.581 -10.07 7.371 C-9.086 9.166 -9.086 9.166 -7 10 C-6.807 9.362 -6.613 8.724 -6.414 8.066 C-6.154 7.24 -5.893 6.414 -5.625 5.562 C-5.37 4.739 -5.115 3.915 -4.852 3.066 C-3.739 0.366 -2.885 0 0 0 Z M-16 9 C-15 11 -15 11 -15 11 Z M-9 12 C-8 14 -8 14 -8 14 Z M-12 20 C-11 22 -11 22 -11 22 Z " fill="#898988" transform="translate(461,738)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C8.266 3.867 8.112 7.732 8.062 11.75 C8.058 12.447 8.053 13.145 8.049 13.863 C8.037 15.576 8.019 17.288 8 19 C8.66 19 9.32 19 10 19 C9.814 19.639 9.629 20.279 9.438 20.938 C9.221 21.958 9.221 21.958 9 23 C9.33 23.33 9.66 23.66 10 24 C11.675 23.714 13.344 23.382 15 23 C15 21.763 15 20.525 15 19.25 C15 10.5 15 10.5 16 6.25 C16.186 5.451 16.371 4.652 16.562 3.828 C16.707 3.225 16.851 2.622 17 2 C19.31 2 21.62 2 24 2 C24.168 6.271 24.334 10.542 24.5 14.812 C24.548 16.027 24.595 17.242 24.645 18.494 C24.69 19.658 24.735 20.821 24.781 22.02 C24.823 23.093 24.865 24.167 24.908 25.273 C25.042 29.25 24.925 31.512 23 35 C21.02 34.01 19.04 33.02 17 32 C16.34 32.66 15.68 33.32 15 34 C13.312 33.688 13.312 33.688 11 33 C9.783 32.856 8.566 32.711 7.312 32.562 C5.673 32.284 5.673 32.284 4 32 C3.67 31.34 3.34 30.68 3 30 C4.031 30.052 4.031 30.052 5.082 30.105 C6.434 30.146 6.434 30.146 7.812 30.188 C8.706 30.222 9.599 30.257 10.52 30.293 C13.46 29.946 14.105 29.168 16 27 C16.66 27 17.32 27 18 27 C18 27.99 18 28.98 18 30 C19.65 30 21.3 30 23 30 C23 21.09 23 12.18 23 3 C21.35 3 19.7 3 18 3 C18.058 4.314 18.116 5.627 18.176 6.98 C18.223 8.716 18.268 10.452 18.312 12.188 C18.354 13.052 18.396 13.916 18.439 14.807 C18.561 21.146 18.561 21.146 16.865 23.729 C15.48 25.113 15.48 25.113 13 27 C10.98 26.398 8.979 25.727 7 25 C5.8 22.599 5.885 21.106 5.902 18.43 C5.906 17.534 5.909 16.638 5.912 15.715 C5.92 14.778 5.929 13.841 5.938 12.875 C5.942 11.93 5.947 10.985 5.951 10.012 C5.963 7.674 5.979 5.337 6 3 C4.35 3 2.7 3 1 3 C1.139 6.771 1.287 10.542 1.438 14.312 C1.477 15.384 1.516 16.456 1.557 17.561 C1.619 19.103 1.619 19.103 1.684 20.676 C1.72 21.624 1.757 22.572 1.795 23.548 C1.914 26.136 1.914 26.136 3 29 C2.34 29 1.68 29 1 29 C0.349 27.44 -0.296 25.877 -0.938 24.312 C-1.297 23.442 -1.657 22.572 -2.027 21.676 C-2.97 19.081 -3.705 16.746 -4 14 C-3.34 13.34 -2.68 12.68 -2 12 C-2.33 11.01 -2.66 10.02 -3 9 C-2.34 9 -1.68 9 -1 9 C-1.021 7.886 -1.041 6.773 -1.062 5.625 C-1 2 -1 2 0 0 Z " fill="#767574" transform="translate(413,651)"/>
<path d="M0 0 C3.639 0.457 6.556 0.633 10.125 -0.438 C13.296 3.836 14.332 8.291 14.125 13.562 C13.135 15.048 13.135 15.048 12.125 16.562 C10.475 16.232 8.825 15.903 7.125 15.562 C6.5 13.688 6.5 13.688 6.125 11.562 C6.785 10.903 7.445 10.242 8.125 9.562 C6.905 6.872 6.905 6.872 5.125 4.562 C4.795 7.533 4.465 10.503 4.125 13.562 C4.785 13.893 5.445 14.222 6.125 14.562 C3.737 16.066 2.585 16.635 -0.25 16.188 C-1.549 15.878 -1.549 15.878 -2.875 15.562 C-4.875 15.538 -6.875 15.535 -8.875 15.562 C-8.788 13.27 -8.68 10.979 -8.562 8.688 C-8.504 7.411 -8.446 6.135 -8.387 4.82 C-8.218 3.745 -8.049 2.67 -7.875 1.562 C-4.45 -0.721 -3.876 -0.571 0 0 Z M-2.875 4.562 C-2.875 7.202 -2.875 9.842 -2.875 12.562 C-1.555 12.893 -0.235 13.222 1.125 13.562 C0.795 12.82 0.465 12.077 0.125 11.312 C-0.875 8.562 -0.875 8.562 -0.875 4.562 C-1.535 4.562 -2.195 4.562 -2.875 4.562 Z " fill="#8B8B8A" transform="translate(645.875,888.4375)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 3.3 4 6.6 4 10 C7.465 9.505 7.465 9.505 11 9 C11 9.66 11 10.32 11 11 C13.64 11 16.28 11 19 11 C20.26 13.521 19.919 14.854 19.562 17.625 C19.461 18.442 19.359 19.26 19.254 20.102 C19.17 20.728 19.086 21.355 19 22 C18.67 21.01 18.34 20.02 18 19 C14.455 20.398 14.455 20.398 11 22 C11 21.01 11 20.02 11 19 C12.32 18.34 13.64 17.68 15 17 C13.02 17.99 11.04 18.98 9 20 C9.33 21.65 9.66 23.3 10 25 C12.31 25.66 14.62 26.32 17 27 C17 27.33 17 27.66 17 28 C13.625 28.125 13.625 28.125 10 28 C9.34 27.34 8.68 26.68 8 26 C7.67 26.66 7.34 27.32 7 28 C4.125 28.188 4.125 28.188 1 28 C-1.255 24.618 -1.247 23.451 -1.23 19.492 C-1.229 18.424 -1.227 17.355 -1.225 16.254 C-1.206 14.581 -1.206 14.581 -1.188 12.875 C-1.187 11.752 -1.186 10.629 -1.186 9.473 C-1.14 1.14 -1.14 1.14 0 0 Z M4 11 C3.67 12.65 3.34 14.3 3 16 C5.354 17.429 6.48 18.087 9.25 17.625 C9.827 17.419 10.405 17.212 11 17 C9.848 14.532 8.952 12.952 7 11 C6.01 11 5.02 11 4 11 Z M14 12 C14.33 12.99 14.66 13.98 15 15 C15.99 14.67 16.98 14.34 18 14 C17.67 13.34 17.34 12.68 17 12 C16.01 12 15.02 12 14 12 Z M5 19 C5.33 19.99 5.66 20.98 6 22 C6 21.01 6 20.02 6 19 C5.67 19 5.34 19 5 19 Z M4 21 C5 23 5 23 5 23 Z " fill="#898987" transform="translate(329,733)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.784 1.814 4.567 1.629 5.375 1.438 C8 1 8 1 10.75 2.312 C13 4 13 4 14 6 C14.072 8.718 14.093 11.409 14.062 14.125 C14.058 14.879 14.053 15.633 14.049 16.41 C14.037 18.273 14.019 20.137 14 22 C11.525 22.495 11.525 22.495 9 23 C8.67 19.7 8.34 16.4 8 13 C6.35 13 4.7 13 3 13 C3 15.97 3 18.94 3 22 C0.36 21.67 -2.28 21.34 -5 21 C-4.835 20.361 -4.67 19.721 -4.5 19.062 C-3.961 17.192 -3.961 17.192 -4 16 C-4 15.01 -4 14.02 -4 13 C-3.34 13 -2.68 13 -2 13 C-2.107 12.313 -2.214 11.626 -2.324 10.918 C-2.444 10.017 -2.564 9.116 -2.688 8.188 C-2.815 7.294 -2.943 6.401 -3.074 5.48 C-3 3 -3 3 -1.52 1.176 C-1.018 0.788 -0.517 0.4 0 0 Z M7 6 C8 8 8 8 8 8 Z M4 7 C3.505 8.98 3.505 8.98 3 11 C4.32 11 5.64 11 7 11 C6.67 10.01 6.34 9.02 6 8 C5.34 7.67 4.68 7.34 4 7 Z " fill="#B3B3B2" transform="translate(733,739)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C3.176 2.186 3.176 2.186 4.375 2.375 C7 3 7 3 9 5 C9.125 8.625 9.125 8.625 9 12 C8.01 12.33 7.02 12.66 6 13 C6.99 14.64 6.99 14.64 8 16.312 C9.585 18.937 10 19.826 10 23 C9.241 22.916 8.481 22.832 7.699 22.746 C6.705 22.644 5.711 22.542 4.688 22.438 C3.701 22.333 2.715 22.229 1.699 22.121 C-1.099 21.834 -1.099 21.834 -4 23 C-4.66 22.67 -5.32 22.34 -6 22 C-6 15.4 -6 8.8 -6 2 C-4.02 1.67 -2.04 1.34 0 1 C0 0.67 0 0.34 0 0 Z M-1 6 C-1 7.65 -1 9.3 -1 11 C0.65 11 2.3 11 4 11 C4.33 10.01 4.66 9.02 5 8 C4.34 7.34 3.68 6.68 3 6 C1.68 6 0.36 6 -1 6 Z M-1 15 C0.485 15.99 0.485 15.99 2 17 C2 16.34 2 15.68 2 15 C1.01 15 0.02 15 -1 15 Z M2 17 C2.33 17.99 2.66 18.98 3 20 C3 19.01 3 18.02 3 17 C2.67 17 2.34 17 2 17 Z " fill="#898888" transform="translate(377,882)"/>
<path d="M0 0 C5.144 3.888 7.627 8.571 9.012 14.816 C9.917 23.569 9.751 31.237 5.012 38.816 C4.455 39.745 3.898 40.673 3.324 41.629 C-1.727 46.407 -7.161 48.062 -14.051 48.254 C-20.035 48.011 -25.101 47.362 -29.703 43.305 C-35.405 37.101 -37.561 30.794 -37.488 22.504 C-37.483 21.759 -37.478 21.014 -37.473 20.247 C-37.247 13.074 -35.333 7.688 -30.602 2.211 C-21.841 -5.973 -9.949 -5.922 0 0 Z M-31.363 5.504 C-35.635 11.761 -36.88 19.341 -35.988 26.816 C-34.631 33.827 -31.508 39.24 -25.988 43.816 C-19.905 47.282 -13.705 47.351 -6.988 45.816 C-3.501 44.411 -3.501 44.411 -0.988 42.816 C-0.988 42.156 -0.988 41.496 -0.988 40.816 C-0.373 40.559 0.241 40.301 0.875 40.035 C4.52 37.956 5.663 34.671 7.012 30.816 C8.472 23.715 9.035 15.169 5.012 8.816 C4.145 7.331 4.145 7.331 3.262 5.816 C-0.004 1.462 -3.641 -1.149 -8.988 -2.184 C-18.317 -3.067 -25.215 -1.919 -31.363 5.504 Z " fill="#4B2F2E" transform="translate(570.98828125,453.18359375)"/>
<path d="M0 0 C0.799 0.009 1.598 0.018 2.422 0.027 C3.025 0.039 3.628 0.051 4.25 0.062 C4.58 0.722 4.91 1.383 5.25 2.062 C7.23 1.403 9.21 0.742 11.25 0.062 C13.826 1.976 14.234 2.964 14.762 6.199 C14.82 7.371 14.878 8.543 14.938 9.75 C15.003 10.929 15.069 12.109 15.137 13.324 C15.174 14.228 15.211 15.131 15.25 16.062 C12.24 16.997 11.383 17.107 8.25 16.062 C8.01 11.975 8.827 8.886 10.25 5.062 C9.26 5.393 8.27 5.722 7.25 6.062 C6.92 9.362 6.59 12.663 6.25 16.062 C4.93 16.062 3.61 16.062 2.25 16.062 C1.92 15.403 1.59 14.742 1.25 14.062 C0.59 14.062 -0.07 14.062 -0.75 14.062 C-0.75 13.072 -0.75 12.082 -0.75 11.062 C-1.41 13.043 -2.07 15.023 -2.75 17.062 C-3.74 16.732 -4.73 16.403 -5.75 16.062 C-5.823 14.815 -5.823 14.815 -5.898 13.543 C-5.973 12.456 -6.048 11.37 -6.125 10.25 C-6.195 9.171 -6.264 8.092 -6.336 6.98 C-6.562 3.876 -6.562 3.876 -8.75 1.062 C-5.755 0.19 -3.114 -0.046 0 0 Z M-1.75 6.062 C-1.75 6.722 -1.75 7.383 -1.75 8.062 C-0.43 8.062 0.89 8.062 2.25 8.062 C2.25 7.072 2.25 6.082 2.25 5.062 C0.175 4.913 0.175 4.913 -1.75 6.062 Z " fill="#7C7C7B" transform="translate(482.75,887.9375)"/>
<path d="M0 0 C3.465 2.475 3.465 2.475 7 5 C6.67 5.99 6.34 6.98 6 8 C5.34 7.67 4.68 7.34 4 7 C1.667 6.96 -0.667 6.957 -3 7 C-3 8.32 -3 9.64 -3 11 C-1.721 11.247 -0.442 11.495 0.875 11.75 C4.766 12.73 4.766 12.73 6.625 14.875 C7 17 7 17 6.562 19.188 C5 21 5 21 1.562 21.875 C-2 22 -2 22 -5 20 C-5 19.34 -5 18.68 -5 18 C-4.34 18 -3.68 18 -3 18 C-3 18.66 -3 19.32 -3 20 C0.488 19.23 0.488 19.23 4 18 C4.33 17.01 4.66 16.02 5 15 C1.37 14.01 -2.26 13.02 -6 12 C-6 11.34 -6 10.68 -6 10 C-6.99 9.67 -7.98 9.34 -9 9 C-9.99 8.67 -10.98 8.34 -12 8 C-13.941 9.941 -13.399 13.473 -13.562 16.062 C-13.646 17.353 -13.73 18.643 -13.816 19.973 C-13.877 20.972 -13.938 21.971 -14 23 C-15.65 23 -17.3 23 -19 23 C-19.959 18.38 -19.961 14.319 -19.562 9.625 C-19.461 8.38 -19.359 7.135 -19.254 5.852 C-19.17 4.911 -19.086 3.97 -19 3 C-15.446 1.815 -12.73 1.734 -9 2 C-7.062 3.5 -7.062 3.5 -6 5 C-2.902 2.611 -2.902 2.611 0 0 Z " fill="#92918F" transform="translate(536,738)"/>
<path d="M0 0 C2.625 0.375 2.625 0.375 5 1 C5.193 1.638 5.387 2.276 5.586 2.934 C5.846 3.76 6.107 4.586 6.375 5.438 C6.63 6.261 6.885 7.085 7.148 7.934 C7.809 10.178 7.809 10.178 10 11 C10.66 7.7 11.32 4.4 12 1 C13.65 0.67 15.3 0.34 17 0 C16.481 6.378 14.864 11.6 12.438 17.5 C11.943 18.734 11.943 18.734 11.439 19.992 C10.635 21.998 9.819 24 9 26 C8.34 26 7.68 26 7 26 C7 26.66 7 27.32 7 28 C4.69 28 2.38 28 0 28 C-0.33 26.02 -0.66 24.04 -1 22 C-0.34 21.67 0.32 21.34 1 21 C1 21.99 1 22.98 1 24 C4.047 23.373 4.047 23.373 4.832 21.379 C5.081 17.859 3.915 15.225 2.688 11.938 C2.238 10.709 1.788 9.481 1.324 8.215 C0.093 4.927 0.093 4.927 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#888886" transform="translate(664,740)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5 1.66 5 2.32 5 3 C5.771 2.988 6.542 2.977 7.336 2.965 C8.339 2.956 9.342 2.947 10.375 2.938 C11.373 2.926 12.37 2.914 13.398 2.902 C16 3 16 3 18 4 C19.254 7.763 19.107 11.272 19.062 15.188 C19.058 15.937 19.053 16.687 19.049 17.459 C19.037 19.306 19.019 21.153 19 23 C18.67 23 18.34 23 18 23 C17.939 22.001 17.879 21.002 17.816 19.973 C17.733 18.682 17.649 17.392 17.562 16.062 C17.481 14.775 17.4 13.487 17.316 12.16 C17.351 9.14 17.351 9.14 16 8 C14.334 7.959 12.666 7.957 11 8 C9.584 10.833 9.654 13.475 9.438 16.625 C9.354 17.814 9.27 19.002 9.184 20.227 C9.123 21.142 9.062 22.057 9 23 C8.34 23 7.68 23 7 23 C6.67 17.72 6.34 12.44 6 7 C4.35 7.33 2.7 7.66 1 8 C-0.656 16.137 -0.656 16.137 1 24 C-1 24.043 -3 24.041 -5 24 C-6 23 -6 23 -6.114 21.142 C-6.108 20.352 -6.103 19.561 -6.098 18.746 C-6.094 17.892 -6.091 17.038 -6.088 16.158 C-6.08 15.26 -6.071 14.363 -6.062 13.438 C-6.058 12.536 -6.053 11.634 -6.049 10.705 C-6.037 8.47 -6.021 6.235 -6 4 C-4 3 -4 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#BABAB9" transform="translate(582,737)"/>
<path d="M0 0 C0 9.57 0 19.14 0 29 C2.31 29.66 4.62 30.32 7 31 C7.99 31.66 8.98 32.32 10 33 C10 34.32 10 35.64 10 37 C-1.724 35.065 -1.724 35.065 -5 32 C-6.806 25.61 -6.089 18.59 -6 12 C-6.99 12 -7.98 12 -9 12 C-9.33 10.68 -9.66 9.36 -10 8 C-8.68 7.67 -7.36 7.34 -6 7 C-5.918 6.216 -5.835 5.433 -5.75 4.625 C-5 2 -5 2 -3 0.688 C-1 0 -1 0 0 0 Z " fill="#AFAFAE" transform="translate(473,155)"/>
<path d="M0 0 C3.801 1.98 5.93 4.562 7.508 8.586 C8.664 14.255 8.2 20.241 8 26 C6.02 26.99 4.04 27.98 2 29 C1 26 1 26 1.438 24.312 C3.027 17.777 2.125 12.787 -1 7 C-3.737 4.759 -3.737 4.759 -6.438 5.062 C-9.775 6.284 -11.008 8.123 -13 11 C-13.66 10.34 -14.32 9.68 -15 9 C-15.99 10.32 -16.98 11.64 -18 13 C-17.586 7.726 -16.71 4.823 -13 1 C-9.053 -0.974 -4.303 -0.436 0 0 Z " fill="#BFBEBE" transform="translate(572,645)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.598 1.794 2.196 1.587 2.812 1.375 C5 1 5 1 8 3 C9.188 5.625 9.188 5.625 10 8 C10.66 8 11.32 8 12 8 C12.33 6.02 12.66 4.04 13 2 C14.875 1.375 14.875 1.375 17 1 C17.66 1.66 18.32 2.32 19 3 C18.67 3.66 18.34 4.32 18 5 C18.66 5.33 19.32 5.66 20 6 C20 8.31 20 10.62 20 13 C20.66 12.67 21.32 12.34 22 12 C22.687 9.882 22.687 9.882 23.125 7.438 C23.293 6.611 23.46 5.785 23.633 4.934 C23.754 4.296 23.875 3.657 24 3 C24.66 3 25.32 3 26 3 C25.357 8.913 23.81 14.342 22 20 C21.01 20 20.02 20 19 20 C17.807 16.123 17 13.084 17 9 C16.34 9 15.68 9 15 9 C14.01 13.95 13.02 18.9 12 24 C11.67 23.34 11.34 22.68 11 22 C10.01 22 9.02 22 8 22 C7.743 21.19 7.743 21.19 7.481 20.364 C6.703 17.927 5.914 15.495 5.125 13.062 C4.856 12.212 4.586 11.362 4.309 10.486 C4.044 9.677 3.78 8.869 3.508 8.035 C3.267 7.286 3.026 6.537 2.778 5.766 C2.137 3.803 2.137 3.803 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B4B4B3" transform="translate(620,739)"/>
<path d="M0 0 C3.932 1.929 5.605 3.058 7.312 7.125 C7.653 8.548 7.653 8.548 8 10 C8.33 10.66 8.66 11.32 9 12 C9.66 12 10.32 12 11 12 C10.669 15.062 10.399 16.601 8.188 18.812 C6 20 6 20 2 20 C1.34 19.34 0.68 18.68 0 18 C-2.31 18.66 -4.62 19.32 -7 20 C-7.66 18.68 -8.32 17.36 -9 16 C-4.38 16 0.24 16 5 16 C-1.93 15.505 -1.93 15.505 -9 15 C-9.33 14.01 -9.66 13.02 -10 12 C-4.06 11.505 -4.06 11.505 2 11 C1.372 7.029 1.372 7.029 -1 5 C-3.915 4.416 -3.915 4.416 -7 5 C-9.404 7.378 -9.404 7.378 -11 10 C-11.33 9.34 -11.66 8.68 -12 8 C-12.99 9.98 -13.98 11.96 -15 14 C-16.168 10.497 -15.611 9.001 -14.074 5.719 C-10.739 0.382 -5.924 -0.846 0 0 Z " fill="#C3C2C1" transform="translate(499,162)"/>
<path d="M0 0 C12.87 0.495 12.87 0.495 26 1 C26 1.33 26 1.66 26 2 C17.75 2 9.5 2 1 2 C1 32.03 1 62.06 1 93 C8.26 93 15.52 93 23 93 C23 74.52 23 56.04 23 37 C23.66 37.33 24.32 37.66 25 38 C24.67 56.81 24.34 75.62 24 95 C16.74 95 9.48 95 2 95 C1.01 95.495 1.01 95.495 0 96 C0 95.34 0 94.68 0 94 C-0.66 93.67 -1.32 93.34 -2 93 C-1.505 92.505 -1.505 92.505 -1 92 C-0.875 89.14 -0.814 86.303 -0.795 83.441 C-0.785 82.544 -0.775 81.646 -0.765 80.722 C-0.733 77.74 -0.708 74.759 -0.684 71.777 C-0.663 69.716 -0.642 67.655 -0.621 65.594 C-0.565 60.157 -0.516 54.72 -0.468 49.284 C-0.418 43.74 -0.362 38.197 -0.307 32.654 C-0.199 21.77 -0.098 10.885 0 0 Z " fill="#130F11" transform="translate(446,300)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C2.99 3 3.98 3 5 3 C1.37 6.3 -2.26 9.6 -6 13 C-0.39 13.33 5.22 13.66 11 14 C11 15.65 11 17.3 11 19 C2.75 19 -5.5 19 -14 19 C-11.75 11.125 -11.75 11.125 -9.18 8.691 C-8.645 8.173 -8.11 7.654 -7.559 7.119 C-7.003 6.605 -6.447 6.092 -5.875 5.562 C-5.03 4.751 -5.03 4.751 -4.168 3.924 C-2.79 2.604 -1.396 1.3 0 0 Z " fill="#B0B0B0" transform="translate(599,663)"/>
<path d="M0 0 C2.59 2.391 2.634 2.78 2.773 6.121 C2.74 7.68 2.681 9.239 2.598 10.797 C2.575 11.594 2.553 12.391 2.529 13.213 C2.47 15.179 2.381 17.145 2.285 19.109 C1.955 19.109 1.625 19.109 1.285 19.109 C1.236 18.339 1.187 17.568 1.137 16.773 C1.062 15.771 0.987 14.768 0.91 13.734 C0.841 12.737 0.771 11.739 0.699 10.711 C0.522 7.954 0.522 7.954 -1.715 6.109 C-2.045 6.769 -2.375 7.429 -2.715 8.109 C-2.055 8.109 -1.395 8.109 -0.715 8.109 C-0.715 9.429 -0.715 10.749 -0.715 12.109 C-2.695 12.439 -4.675 12.769 -6.715 13.109 C-6.715 14.429 -6.715 15.749 -6.715 17.109 C-5.725 17.109 -4.735 17.109 -3.715 17.109 C-3.385 16.119 -3.055 15.129 -2.715 14.109 C-1.725 14.439 -0.735 14.769 0.285 15.109 C-0.045 17.089 -0.375 19.069 -0.715 21.109 C-5.048 21.109 -9.382 21.109 -13.715 21.109 C-13.825 17.945 -13.692 15.136 -12.715 12.109 C-10.302 9.875 -7.699 8.462 -4.715 7.109 C-7.685 6.614 -7.685 6.614 -10.715 6.109 C-10.652 4.297 -10.652 4.297 -9.715 2.109 C-3.984 -1.307 -3.984 -1.307 0 0 Z " fill="#91918F" transform="translate(418.71484375,739.890625)"/>
<path d="M0 0 C6.152 -0.098 6.152 -0.098 8 0 C9 1 9 1 9.114 3.133 C9.108 4.049 9.103 4.966 9.098 5.91 C9.094 6.9 9.091 7.889 9.088 8.908 C9.08 9.949 9.071 10.99 9.062 12.062 C9.058 13.107 9.053 14.152 9.049 15.229 C9.037 17.819 9.021 20.41 9 23 C10.65 22.67 12.3 22.34 14 22 C14 25.3 14 28.6 14 32 C13.01 32 12.02 32 11 32 C10.67 32.99 10.34 33.98 10 35 C9.505 32.525 9.505 32.525 9 30 C8.34 30.33 7.68 30.66 7 31 C6.342 34.029 6.342 34.029 6 37 C5.01 37 4.02 37 3 37 C2.913 33.042 2.86 29.084 2.812 25.125 C2.775 23.433 2.775 23.433 2.736 21.707 C2.727 20.631 2.717 19.554 2.707 18.445 C2.691 17.45 2.676 16.455 2.659 15.43 C2.772 14.628 2.884 13.826 3 13 C3.99 12.34 4.98 11.68 6 11 C6.58 8.805 6.58 8.805 6.688 6.375 C6.753 5.558 6.819 4.74 6.887 3.898 C6.924 3.272 6.961 2.645 7 2 C4.36 2.33 1.72 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#B0B0AF" transform="translate(630,643)"/>
<path d="M0 0 C3.918 3.588 6.336 6.995 8.688 11.75 C9.56 13.475 9.56 13.475 10.449 15.234 C16.55 28.976 15.826 46.367 10.617 60.242 C5.444 72.749 -1.497 83.057 -12.625 90.938 C-13.176 91.328 -13.726 91.718 -14.294 92.12 C-17.49 94.279 -20.158 95.55 -24 96 C-21.021 93.501 -17.966 91.326 -14.688 89.25 C-1.284 80.337 5.245 67.935 10 53 C10.33 52.01 10.66 51.02 11 50 C11.259 46.338 11.231 42.67 11.25 39 C11.271 38.009 11.291 37.017 11.312 35.996 C11.36 26.967 9.735 18.612 5.5 10.562 C5.098 9.78 4.696 8.998 4.281 8.191 C3.028 5.944 3.028 5.944 1.344 3.84 C0 2 0 2 0 0 Z " fill="#3A3938" transform="translate(561,134)"/>
<path d="M0 0 C-0.66 1.65 -1.32 3.3 -2 5 C-1.01 5.33 -0.02 5.66 1 6 C-1.475 7.485 -1.475 7.485 -4 9 C1.61 9.33 7.22 9.66 13 10 C13 11.65 13 13.3 13 15 C4.75 15 -3.5 15 -12 15 C-9.74 7.089 -9.74 7.089 -6 3.562 C-5.299 2.883 -4.597 2.204 -3.875 1.504 C-2 0 -2 0 0 0 Z " fill="#B4B3B3" transform="translate(533,667)"/>
<path d="M0 0 C0.958 0.047 1.917 0.094 2.904 0.143 C3.939 0.191 4.973 0.24 6.039 0.289 C7.127 0.345 8.216 0.401 9.338 0.459 C10.43 0.511 11.523 0.564 12.648 0.617 C15.357 0.748 18.066 0.883 20.775 1.022 C20.775 1.352 20.775 1.682 20.775 2.022 C13.515 2.022 6.255 2.022 -1.225 2.022 C-0.895 19.182 -0.565 36.342 -0.225 54.022 C-4.722 49.525 -4.722 49.525 -6.557 46.299 C-6.959 45.602 -7.361 44.904 -7.775 44.186 C-8.192 43.451 -8.608 42.716 -9.037 41.959 C-9.929 40.414 -10.822 38.87 -11.717 37.326 C-12.149 36.581 -12.58 35.836 -13.025 35.068 C-15.017 31.672 -17.152 28.375 -19.287 25.069 C-23.706 18.12 -27.946 11.057 -32.225 4.022 C-31.565 3.362 -30.905 2.702 -30.225 2.022 C-29.62 3.12 -29.62 3.12 -29.002 4.24 C-24.376 12.491 -19.477 20.556 -14.475 28.584 C-13.5 30.154 -13.5 30.154 -12.506 31.756 C-11.882 32.753 -11.258 33.749 -10.615 34.776 C-10.059 35.666 -9.502 36.556 -8.929 37.473 C-7.162 40.116 -5.245 42.568 -3.225 45.022 C-3.227 44.473 -3.229 43.924 -3.232 43.358 C-3.254 37.665 -3.269 31.971 -3.28 26.278 C-3.285 24.151 -3.292 22.023 -3.3 19.896 C-3.312 16.845 -3.318 13.795 -3.323 10.744 C-3.328 9.787 -3.333 8.83 -3.338 7.844 C-3.338 6.522 -3.338 6.522 -3.338 5.174 C-3.341 4.395 -3.343 3.615 -3.345 2.812 C-3.162 0.091 -2.722 0.027 0 0 Z M-3.225 47.022 C-2.225 49.022 -2.225 49.022 -2.225 49.022 Z " fill="#56312E" transform="translate(654.224853515625,299.978271484375)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3 2.66 3 3.32 3 4 C3.318 5.67 3.65 7.337 4 9 C4.99 9 5.98 9 7 9 C8 10 8 10 8.098 13.379 C8.091 14.773 8.079 16.168 8.062 17.562 C8.058 18.273 8.053 18.984 8.049 19.717 C8.037 21.478 8.019 23.239 8 25 C7.223 24.826 6.445 24.652 5.645 24.473 C2.983 23.997 1.527 24.158 -1 25 C-1 24.34 -1 23.68 -1 23 C-1.66 22.67 -2.32 22.34 -3 22 C-2.34 22 -1.68 22 -1 22 C-1.115 20.416 -1.242 18.833 -1.375 17.25 C-1.445 16.368 -1.514 15.487 -1.586 14.578 C-1.999 12.009 -2.712 10.242 -4 8 C-3.34 7.34 -2.68 6.68 -2 6 C-1.286 4.016 -0.614 2.017 0 0 Z M3 14 C4 17 4 17 4 17 Z M3 19 C4 22 4 22 4 22 Z " fill="#939393" transform="translate(433,880)"/>
<path d="M0 0 C1.586 0.23 3.169 0.485 4.75 0.75 C5.632 0.889 6.513 1.028 7.422 1.172 C10.664 2.213 11.895 3.324 14 6 C14.454 9.116 14.454 9.116 14.391 12.734 C14.371 14.678 14.371 14.678 14.352 16.66 C14.318 18.023 14.284 19.387 14.25 20.75 C14.23 22.129 14.212 23.508 14.195 24.887 C14.148 28.259 14.082 31.629 14 35 C12.68 35 11.36 35 10 35 C10 34.34 10 33.68 10 33 C8.515 32.505 8.515 32.505 7 32 C7 25.4 7 18.8 7 12 C7.33 12 7.66 12 8 12 C8 18.27 8 24.54 8 31 C9.65 31 11.3 31 13 31 C12.89 27.604 12.759 24.208 12.625 20.812 C12.594 19.853 12.563 18.893 12.531 17.904 C12.493 16.973 12.454 16.042 12.414 15.082 C12.367 13.802 12.367 13.802 12.319 12.495 C11.962 9.703 11.237 7.526 10 5 C6.826 3.413 3.464 3.715 0 4 C-2.882 4.882 -2.882 4.882 -5 6 C-5 5.01 -5 4.02 -5 3 C-6.65 3 -8.3 3 -10 3 C-10 12.24 -10 21.48 -10 31 C-8.02 31 -6.04 31 -4 31 C-4.66 31.33 -5.32 31.66 -6 32 C-6.656 34.527 -6.656 34.527 -7 37 C-7.99 36.67 -8.98 36.34 -10 36 C-10 35.34 -10 34.68 -10 34 C-10.33 33.34 -10.66 32.68 -11 32 C-13.025 31.348 -13.025 31.348 -15 31 C-14.273 30.449 -14.273 30.449 -13.532 29.887 C-11.36 27.212 -11.568 25.217 -11.488 21.801 C-11.453 20.592 -11.417 19.384 -11.381 18.139 C-11.358 16.876 -11.336 15.613 -11.312 14.312 C-11.261 12.401 -11.261 12.401 -11.209 10.451 C-11.126 7.301 -11.057 4.151 -11 1 C-7.37 1.33 -3.74 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#6C6C6B" transform="translate(454,651)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.176 2.186 2.176 2.186 3.375 2.375 C6 3 6 3 7.812 4.5 C10.085 9.284 9.644 14.144 7.938 19.062 C6 21 6 21 2.562 21.562 C-1 21 -1 21 -3.375 19.438 C-5.452 16.321 -5.716 13.695 -6 10 C-6.931 12.606 -7.149 13.642 -6.062 16.25 C-5.537 17.116 -5.537 17.116 -5 18 C-5.99 18.66 -6.98 19.32 -8 20 C-8.66 18.35 -9.32 16.7 -10 15 C-10.33 15 -10.66 15 -11 15 C-11.322 10.328 -11.431 7.861 -9.312 3.625 C-6.644 0.596 -4.014 -2.007 0 0 Z M-2.875 5.562 C-4.459 8.994 -4.525 11.26 -4 15 C-1.978 18.106 -1.978 18.106 1 20 C3.76 19.95 3.76 19.95 6 19 C7.967 14.255 8.156 11.01 7 6 C5.119 3.711 5.119 3.711 2.062 3.625 C-1.009 3.72 -1.009 3.72 -2.875 5.562 Z " fill="#B6B5B4" transform="translate(522,164)"/>
<path d="M0 0 C6.445 1.338 6.445 1.338 8.938 4.312 C10.086 7.217 10.474 8.917 10 12 C8.938 14.25 8.938 14.25 7 16 C2.929 17.206 0.885 17.406 -3 15.625 C-5.631 12.172 -5.277 10.197 -4.785 6.023 C-4 4 -4 4 -1.934 2.727 C-0.976 2.367 -0.976 2.367 0 2 C0 1.34 0 0.68 0 0 Z M0 5 C0.66 6.98 1.32 8.96 2 11 C3.32 10.34 4.64 9.68 6 9 C5.34 7.68 4.68 6.36 4 5 C2.68 5 1.36 5 0 5 Z M0 10 C1 12 1 12 1 12 Z M4 10 C5 12 5 12 5 12 Z " fill="#8D8D8C" transform="translate(421,888)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-1.093 2.062 -2.186 2.124 -3.312 2.188 C-7.03 2.643 -7.03 2.643 -8.938 5.062 C-10.547 9.512 -10.625 13.687 -9.375 18.312 C-9.251 18.869 -9.127 19.426 -9 20 C-9.66 20.99 -10.32 21.98 -11 23 C-7.29 25.473 -4.302 25.559 0 25 C3.402 23.635 3.402 23.635 6 22 C5 25 5 25 3.242 26.16 C-0.804 27.676 -4.758 27.656 -9 27 C-12.417 25.312 -14.463 22.928 -16.375 19.625 C-17.661 14.224 -17.636 8.943 -15 4 C-10.256 0.411 -5.822 -0.485 0 0 Z " fill="#BBBABA" transform="translate(490,655)"/>
<path d="M0 0 C3.218 2.671 5.418 5.117 7 9 C6.113 8.196 5.226 7.391 4.312 6.562 C1.617 4.296 -0.421 3.316 -4 3 C-4 2.34 -4 1.68 -4 1 C-9.981 -0.994 -16.674 -0.999 -22.438 1.688 C-27.653 4.742 -30.495 8.832 -32.457 14.566 C-34.245 22.582 -33.75 30.041 -29.875 37.293 C-29 39 -29 39 -29 41 C-28.34 41 -27.68 41 -27 41 C-26.608 41.433 -26.216 41.866 -25.812 42.312 C-23.187 44.757 -20.327 45.682 -17 47 C-18.562 47.75 -18.562 47.75 -21 48 C-28.026 44.738 -31.358 40.974 -34.25 33.75 C-37.163 25.149 -36.78 17.325 -33 9 C-25.124 -3.106 -12.963 -6.343 0 0 Z " fill="#412A29" transform="translate(468,452)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.368 0.796 3.735 1.592 4.114 2.412 C5.501 5.413 6.89 8.413 8.28 11.414 C8.876 12.703 9.473 13.993 10.069 15.283 C13.594 22.916 17.176 30.51 21 38 C21.454 37.526 21.908 37.051 22.375 36.562 C24 35 24 35 26 34 C26.751 32.212 26.751 32.212 27.344 30.043 C28.964 24.899 31.146 20.156 33.5 15.312 C33.891 14.497 34.281 13.682 34.684 12.842 C36.749 8.541 38.853 4.261 41 0 C41.99 0.33 42.98 0.66 44 1 C43.234 2.388 43.234 2.388 42.453 3.805 C35.329 16.907 29.15 30.42 23 44 C22.34 44 21.68 44 21 44 C20.312 42.534 20.312 42.534 19.611 41.038 C17.888 37.365 16.164 33.694 14.439 30.023 C13.696 28.442 12.955 26.862 12.213 25.281 C8.238 16.805 4.262 8.337 0 0 Z " fill="#4E2E2C" transform="translate(472,301)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.644 2.722 4.311 3.394 6 4 C5.94 5.605 5.851 7.209 5.75 8.812 C5.68 10.152 5.68 10.152 5.609 11.52 C5 14 5 14 3.176 15.824 C-0.199 17.648 -2.398 17.048 -6 16 C-8.558 13.202 -9.015 11.722 -8.938 7.938 C-7.86 4.561 -6.791 3.153 -4 1 C-1.75 0.25 -1.75 0.25 0 0 Z M-4 5 C-3.34 5.66 -2.68 6.32 -2 7 C-2.99 7.99 -3.98 8.98 -5 10 C-4.01 11.485 -4.01 11.485 -3 13 C-1.68 12.67 -0.36 12.34 1 12 C1.68 9.603 1.68 9.603 2 7 C1.34 6.34 0.68 5.68 0 5 C-1.32 5 -2.64 5 -4 5 Z " fill="#898989" transform="translate(470,888)"/>
<path d="M0 0 C4.413 2.292 7.073 5.028 10 9 C10.66 9.66 11.32 10.32 12 11 C11.977 13.277 11.977 13.277 11.625 15.938 C11.278 18.679 11 21.232 11 24 C10.689 25.671 10.359 27.339 10 29 C9.67 29 9.34 29 9 29 C8.974 27.938 8.948 26.876 8.922 25.781 C8.573 14.816 8.573 14.816 4 5 C-0.458 2.028 -4.774 2.258 -10 3 C-14.364 6.028 -15.599 9.002 -17 14 C-17.823 21.711 -17.71 28.599 -13 35 C-11.039 37.324 -9.896 37.914 -6.887 38.203 C-3.07 38.278 0.316 38.038 4 37 C2.017 39.851 0.662 40.078 -2.812 40.875 C-6.984 41.242 -9.491 40.392 -13 38 C-17.747 33.253 -20.092 27.352 -20.438 20.688 C-20.181 13.96 -18.173 7.226 -13.438 2.375 C-9.076 -0.085 -4.934 -0.674 0 0 Z " fill="#6D6D6B" transform="translate(571,643)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.875 2.5 2.875 2.5 3 6 C-0.202 12.503 -6.383 17.419 -11.609 22.289 C-14.507 25.219 -14.507 25.219 -16 31 C-7.75 31 0.5 31 9 31 C9 29.35 9 27.7 9 26 C3.06 26 -2.88 26 -9 26 C-6.218 22.523 -4.039 19.983 -0.75 17.188 C-0.044 16.583 0.663 15.978 1.391 15.355 C1.922 14.908 2.453 14.461 3 14 C3 17.952 1.628 19.146 -1 22 C-1.99 22.33 -2.98 22.66 -4 23 C2.93 23.495 2.93 23.495 10 24 C10 26.64 10 29.28 10 32 C1.09 32 -7.82 32 -17 32 C-17 25 -17 25 -15.623 22.86 C-14.995 22.281 -14.366 21.702 -13.719 21.105 C-13.042 20.46 -12.365 19.815 -11.668 19.15 C-10.953 18.503 -10.237 17.855 -9.5 17.188 C-8.09 15.873 -6.683 14.554 -5.281 13.23 C-4.653 12.657 -4.024 12.084 -3.377 11.493 C-1.774 10.093 -1.774 10.093 -2 8 C-1.34 7.67 -0.68 7.34 0 7 C0 6.01 0 5.02 0 4 C0 2.68 0 1.36 0 0 Z " fill="#70706E" transform="translate(601,651)"/>
<path d="M0 0 C2.522 5.502 2.394 11.133 1 17 C0.34 17.33 -0.32 17.66 -1 18 C-1.039 16.645 -1.039 16.645 -1.078 15.262 C-1.135 14.082 -1.192 12.903 -1.25 11.688 C-1.296 10.516 -1.343 9.344 -1.391 8.137 C-1.743 5.027 -1.743 5.027 -3.484 3.145 C-6.916 1.426 -6.916 1.426 -15 1 C-15 7.27 -15 13.54 -15 20 C-10.234 20 -6.546 19.887 -2 19 C-2 19.66 -2 20.32 -2 21 C-7.335 23.846 -10.106 23 -17 23 C-17 15.08 -17 7.16 -17 -1 C-4.329 -2.392 -4.329 -2.392 0 0 Z " fill="#B6B6B5" transform="translate(492,737)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C5.65 1.34 7.3 0.68 9 0 C9.99 0.99 10.98 1.98 12 3 C11.34 4.32 10.68 5.64 10 7 C8.68 6.67 7.36 6.34 6 6 C5.67 9.63 5.34 13.26 5 17 C1 16 1 16 -1 14 C-0.34 14 0.32 14 1 14 C0.67 12.68 0.34 11.36 0 10 C-0.65 11.392 -0.65 11.392 -1.312 12.812 C-2.719 15.468 -3.67 17.177 -6 19 C-6.66 19 -7.32 19 -8 19 C-8.33 20.32 -8.66 21.64 -9 23 C-9.66 22.67 -10.32 22.34 -11 22 C-11 19.69 -11 17.38 -11 15 C-9.866 14.876 -8.731 14.753 -7.562 14.625 C-6.387 14.419 -5.211 14.212 -4 14 C-2.181 10.362 -2.747 6.758 -4 3 C-3.34 3 -2.68 3 -2 3 C-2 3.66 -2 4.32 -2 5 C-1.34 4.67 -0.68 4.34 0 4 C0 3.01 0 2.02 0 1 C0 0.67 0 0.34 0 0 Z M0 5 C-0.33 5.99 -0.66 6.98 -1 8 C-0.34 8 0.32 8 1 8 C0.67 7.01 0.34 6.02 0 5 Z " fill="#7D7E7D" transform="translate(566,888)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C0.66 2 1.32 2 2 2 C3.513 6.539 3.605 10.363 1.938 14.875 C0 17 0 17 -3.375 18.062 C-7 18 -7 18 -9.938 16.688 C-12.645 13.16 -12.661 10.71 -12.41 6.352 C-12 4 -12 4 -10.5 1.375 C-6.854 -0.63 -4.128 -0.275 0 0 Z M-7 3 C-9.313 6.469 -9.425 7.922 -9 12 C-7.894 14.025 -7.894 14.025 -6 15 C-3.889 15.076 -3.889 15.076 -2 14 C0.057 11.356 0.014 10.117 -0.375 6.75 C-0.581 5.842 -0.788 4.935 -1 4 C-3.97 3.505 -3.97 3.505 -7 3 Z " fill="#9A9998" transform="translate(724,742)"/>
<path d="M0 0 C1.031 0.722 2.062 1.444 3.125 2.188 C-0.103 4.339 -1.159 4.388 -4.875 4.188 C-2.895 5.178 -0.915 6.168 1.125 7.188 C1.346 9.915 1.429 11.652 0.062 14.062 C-3.018 15.851 -5.509 14.922 -8.875 14.188 C-9.535 13.857 -10.195 13.528 -10.875 13.188 C-10.875 4.759 -10.875 4.759 -9.875 1.188 C-6.134 -1.841 -4.099 -2.341 0 0 Z M-7.875 8.188 C-7.875 8.847 -7.875 9.508 -7.875 10.188 C-5.4 10.683 -5.4 10.683 -2.875 11.188 C-2.875 10.528 -2.875 9.867 -2.875 9.188 C-4.525 8.857 -6.175 8.528 -7.875 8.188 Z " fill="#868684" transform="translate(508.875,889.8125)"/>
<path d="M0 0 C2.125 0.375 2.125 0.375 4 1 C3.01 1.33 2.02 1.66 1 2 C1 2.66 1 3.32 1 4 C2.288 4.53 3.58 5.049 4.875 5.562 C5.594 5.853 6.314 6.143 7.055 6.441 C9.056 7.254 9.056 7.254 11 6 C10 9 10 9 9 10 C10.32 10 11.64 10 13 10 C13.33 11.32 13.66 12.64 14 14 C10.25 15.125 10.25 15.125 8 14 C8 13.34 8 12.68 8 12 C7.34 12 6.68 12 6 12 C6 12.66 6 13.32 6 14 C2.75 14.688 2.75 14.688 -1 15 C-2.362 14.039 -3.699 13.041 -5 12 C-7.698 12.222 -7.698 12.222 -10 13 C-7.312 10.748 -5.354 9.118 -2 8 C-2.041 7.051 -2.082 6.102 -2.125 5.125 C-2 2 -2 2 0 0 Z M-1 8 C-0.67 8.99 -0.34 9.98 0 11 C0 10.01 0 9.02 0 8 C-0.33 8 -0.66 8 -1 8 Z " fill="#9C9C9B" transform="translate(595,890)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C8 13 14 26 20 39 C17 38 17 38 15.973 36.342 C15.522 35.291 15.522 35.291 15.062 34.219 C14.725 33.459 14.387 32.7 14.039 31.918 C13.346 30.306 12.654 28.694 11.961 27.082 C11.623 26.323 11.285 25.564 10.938 24.781 C10.637 24.081 10.337 23.38 10.027 22.658 C9.031 20.655 9.031 20.655 6 20 C4.603 18.044 3.262 16.046 2 14 C2.01 15.141 2.021 16.281 2.032 17.457 C2.068 21.679 2.091 25.902 2.11 30.125 C2.12 31.955 2.134 33.784 2.151 35.614 C2.175 38.24 2.186 40.866 2.195 43.492 C2.206 44.313 2.216 45.134 2.227 45.98 C2.227 47.988 2.122 49.996 2 52 C1.34 52.66 0.68 53.32 0 54 C0 36.18 0 18.36 0 0 Z M1 4 C1 6.31 1 8.62 1 11 C2.32 11.33 3.64 11.66 5 12 C4.01 9.36 3.02 6.72 2 4 C1.67 4 1.34 4 1 4 Z M5 13 C6 15 6 15 6 15 Z M6 15 C7 17 7 17 7 17 Z M7 17 C8 19 8 19 8 19 Z " fill="#88524D" transform="translate(470,340)"/>
<path d="M0 0 C3 1 3 1 5 4 C5.99 4 6.98 4 8 4 C8.445 4.463 8.889 4.926 9.348 5.402 C11.545 7.527 13.483 7.861 16.438 8.562 C19.89 9.394 22.883 10.283 26 12 C15.902 12.759 9.09 9.808 1 4 C-2.478 5.516 -4.956 7.577 -7.75 10.125 C-8.549 10.849 -9.348 11.574 -10.172 12.32 C-10.775 12.875 -11.378 13.429 -12 14 C-5.707 22.28 1.737 27.099 12 29 C12 29.33 12 29.66 12 30 C2.16 30.149 -5.005 25.899 -12.125 19.312 C-13.986 17.017 -14.421 15.806 -15 13 C-14.481 12.625 -13.961 12.25 -13.426 11.863 C-10.129 9.444 -6.953 7.089 -4.125 4.125 C-3.424 3.424 -2.723 2.723 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#422F2E" transform="translate(342,490)"/>
<path d="M0 0 C1.742 1.317 3.471 2.65 5.188 4 C6.082 4.686 6.977 5.372 7.898 6.078 C10.372 8.155 11.689 10.16 13.188 13 C12.528 13.33 11.868 13.66 11.188 14 C10.604 13.256 10.604 13.256 10.008 12.496 C0.283 0.687 -13.531 -6.47 -28.812 -8 C-35.624 -8.278 -42.361 -8.281 -49.125 -7.375 C-50.95 -7.189 -50.95 -7.189 -52.812 -7 C-53.473 -7.66 -54.132 -8.32 -54.812 -9 C-36.095 -13.127 -15.896 -11.394 0 0 Z " fill="#545353" transform="translate(548.8125,121)"/>
<path d="M0 0 C2.208 0.231 4.413 0.471 6.617 0.73 C8.491 0.942 10.37 1.105 12.25 1.25 C15.992 2.271 17.391 4.142 20 7 C21.306 8.091 22.619 9.174 23.938 10.25 C31.538 16.892 37.368 26.189 38.195 36.412 C38.753 49.104 38.753 49.104 38 54 C37.34 54.66 36.68 55.32 36 56 C35.986 55.087 35.986 55.087 35.972 54.155 C35.667 38.382 34.941 25.332 24 13 C17.015 6.59 8.887 3.779 0 1 C0 0.67 0 0.34 0 0 Z " fill="#523230" transform="translate(465,430)"/>
<path d="M0 0 C1.65 0.66 3.3 1.32 5 2 C4.01 2.495 4.01 2.495 3 3 C2.23 5.307 2.23 5.307 1.625 8.062 C0.326 13.166 -1.477 17.389 -4 22 C-6.312 22 -6.312 22 -9 21 C-11.399 16.989 -12.437 12.661 -12 8 C-9.32 10.68 -8.835 13.424 -7.781 17.004 C-7.184 19.187 -7.184 19.187 -5 20 C-3.02 14.39 -1.04 8.78 1 3 C0.34 3 -0.32 3 -1 3 C-1.155 4.454 -1.155 4.454 -1.312 5.938 C-1.946 10.486 -3.448 14.694 -5 19 C-7.802 16.614 -8.699 14.516 -9.688 11 C-9.939 10.134 -10.19 9.268 -10.449 8.375 C-11 6 -11 6 -11 3 C-11.66 3 -12.32 3 -13 3 C-12.67 3.99 -12.34 4.98 -12 6 C-12.66 6 -13.32 6 -14 6 C-14.33 4.68 -14.66 3.36 -15 2 C-13 1 -13 1 -11 1.5 C-7.999 3.751 -7.79 6.446 -7 10 C-6.34 10 -5.68 10 -5 10 C-4.01 7.36 -3.02 4.72 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8C8C8A" transform="translate(367,739)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C1.339 5.867 -2.968 9.106 -7.301 13.096 C-7.872 13.64 -8.443 14.185 -9.031 14.746 C-9.813 15.468 -9.813 15.468 -10.611 16.205 C-12.525 18.475 -12.525 18.475 -14 24 C-5.75 24 2.5 24 11 24 C11 22.35 11 20.7 11 19 C5.06 19 -0.88 19 -7 19 C-6.34 18.01 -5.68 17.02 -5 16 C-1.375 15.312 -1.375 15.312 2 15 C2 15.33 2 15.66 2 16 C5.3 16 8.6 16 12 16 C12 18.97 12 21.94 12 25 C3.09 25 -5.82 25 -15 25 C-15 22.36 -15 19.72 -15 17 C-13.125 15.25 -13.125 15.25 -11 14 C-9.748 11.917 -9.748 11.917 -9 10 C-8.34 10 -7.68 10 -7 10 C-5.194 8.553 -5.194 8.553 -3.375 6.688 C-2.743 6.073 -2.112 5.458 -1.461 4.824 C0.313 2.999 0.313 2.999 0 0 Z " fill="#6D6D6B" transform="translate(535,658)"/>
<path d="M0 0 C1.052 0.351 2.104 0.701 3.188 1.062 C6.955 1.989 8.37 1.764 12 1 C14.812 1.938 14.812 1.938 17 3 C16.67 3.66 16.34 4.32 16 5 C15.67 4.67 15.34 4.34 15 4 C10.835 3.84 7.053 4.018 3 5 C3.33 7.31 3.66 9.62 4 12 C3.34 11.67 2.68 11.34 2 11 C1.01 14.63 0.02 18.26 -1 22 C-2.65 22 -4.3 22 -6 22 C-6.289 21.237 -6.577 20.474 -6.875 19.688 C-7.963 16.85 -7.963 16.85 -10 14 C-10.33 16.31 -10.66 18.62 -11 21 C-12.073 17.728 -11.893 15.571 -11.062 12.25 C-10.771 11.051 -10.771 11.051 -10.473 9.828 C-10.317 9.225 -10.161 8.622 -10 8 C-9.01 8.33 -8.02 8.66 -7 9 C-6.051 11.504 -6.051 11.504 -5.312 14.562 C-5.061 15.574 -4.81 16.586 -4.551 17.629 C-4.369 18.411 -4.187 19.194 -4 20 C-2.105 16.551 -1.004 13.012 0.125 9.25 C0.655 7.494 0.655 7.494 1.195 5.703 C1.461 4.811 1.726 3.919 2 3 C1.34 3 0.68 3 0 3 C-0.049 3.638 -0.098 4.276 -0.148 4.934 C-0.223 5.76 -0.298 6.586 -0.375 7.438 C-0.445 8.261 -0.514 9.085 -0.586 9.934 C-0.723 10.616 -0.859 11.297 -1 12 C-1.99 12.495 -1.99 12.495 -3 13 C-2.885 11.582 -2.757 10.166 -2.625 8.75 C-2.555 7.961 -2.486 7.172 -2.414 6.359 C-1.985 3.914 -1.214 2.152 0 0 Z " fill="#858482" transform="translate(644,739)"/>
<path d="M0 0 C3.72 2.916 6.002 4.742 6.734 9.629 C6.959 14.719 7.091 17.76 4 21.938 C-1.27 25.614 -4.302 26.733 -10.688 25.812 C-14.299 23.661 -16.119 21.662 -17.688 17.812 C-18.379 12.491 -18.319 8.098 -15.938 3.25 C-11.407 -1.659 -6.34 -2.801 0 0 Z M-13.688 2.812 C-16.703 7.284 -17.673 11.538 -16.688 16.812 C-15.19 19.469 -13.841 21.659 -11.688 23.812 C-6.802 24.446 -2.909 24.496 1.438 22.062 C4.254 17.181 4.192 12.322 3.312 6.812 C1.759 3.666 0.98 2.172 -2.125 0.5 C-7.012 -0.811 -9.764 -0.206 -13.688 2.812 Z " fill="#7F7F7D" transform="translate(282.6875,735.1875)"/>
<path d="M0 0 C2.375 1.688 2.375 1.688 4 4 C4 5.65 4 7.3 4 9 C0.04 8.67 -3.92 8.34 -8 8 C-7.34 10.31 -6.68 12.62 -6 15 C-3.03 15 -0.06 15 3 15 C3 15.66 3 16.32 3 17 C-4.098 18.564 -4.098 18.564 -8 16.125 C-10.648 11.988 -10.956 8.789 -10 4 C-7.355 -0.171 -4.796 -0.799 0 0 Z M-2 3 C-1.34 3.66 -0.68 4.32 0 5 C0 4.34 0 3.68 0 3 C-0.66 3 -1.32 3 -2 3 Z M-6 4 C-6 4.66 -6 5.32 -6 6 C-4.68 5.67 -3.36 5.34 -2 5 C-3.32 4.67 -4.64 4.34 -6 4 Z " fill="#ABAAA9" transform="translate(617,742)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C0.568 5.189 -0.679 8.775 -3.258 13.301 C-3.503 13.862 -3.748 14.422 -4 15 C-3.505 15.99 -3.505 15.99 -3 17 C-3.763 17.413 -4.526 17.825 -5.312 18.25 C-7.982 19.988 -9.137 21.489 -11 24 C-12.322 24.689 -13.657 25.353 -15 26 C-16.236 26.802 -17.465 27.615 -18.688 28.438 C-26.674 33.479 -35.321 35.133 -44.688 35.188 C-45.805 35.202 -46.923 35.216 -48.074 35.23 C-51 35 -51 35 -54 33 C-52.9 32.963 -51.801 32.925 -50.668 32.887 C-36.188 32.289 -21.828 30.119 -10.664 19.934 C-5.683 14.145 -2.217 7.267 0 0 Z " fill="#4D3534" transform="translate(500,487)"/>
<path d="M0 0 C2.765 1.543 4.587 2.84 6.812 5.125 C7.369 5.661 7.926 6.197 8.5 6.75 C11.54 6.676 12.951 6.259 15.188 4.188 C15.621 3.713 16.054 3.239 16.5 2.75 C17.16 2.75 17.82 2.75 18.5 2.75 C18.5 3.41 18.5 4.07 18.5 4.75 C16.531 6.258 16.531 6.258 14 7.875 C13.165 8.414 12.329 8.953 11.469 9.508 C10.819 9.918 10.169 10.328 9.5 10.75 C8.51 10.09 7.52 9.43 6.5 8.75 C6.5 8.09 6.5 7.43 6.5 6.75 C5.919 6.463 5.337 6.175 4.738 5.879 C3.35 5.179 1.97 4.46 0.605 3.715 C-6.173 0.084 -11.859 -0.932 -19.363 1.043 C-24.793 2.84 -27.557 5.372 -30.25 10.312 C-32.714 15.628 -33.763 20.965 -34.5 26.75 C-34.83 26.75 -35.16 26.75 -35.5 26.75 C-36.046 18.293 -34.21 12.048 -29.688 4.875 C-21.616 -2.966 -10.161 -4.922 0 0 Z " fill="#4E312F" transform="translate(309.5,323.25)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.949 2.926 3.949 2.926 4.688 5.312 C4.939 6.092 5.19 6.872 5.449 7.676 C6 10 6 10 6 14 C6.99 14 7.98 14 9 14 C9.103 13.175 9.206 12.35 9.312 11.5 C10.116 7.407 11.464 3.875 13 0 C13.66 0 14.32 0 15 0 C15.525 5.797 13.667 10.13 11.5 15.375 C11.166 16.206 10.832 17.038 10.488 17.895 C9.668 19.934 8.838 21.968 8 24 C7.34 24 6.68 24 6 24 C5.67 24.66 5.34 25.32 5 26 C3.68 25.67 2.36 25.34 1 25 C1 24.34 1 23.68 1 23 C2.32 22.34 3.64 21.68 5 21 C4.424 13.65 2.29 6.986 0 0 Z " fill="#A7A6A5" transform="translate(665,742)"/>
<path d="M0 0 C1.207 0.031 1.207 0.031 2.438 0.062 C3.939 3.066 3.53 5.853 3.5 9.188 C3.491 10.469 3.482 11.75 3.473 13.07 C3.461 14.058 3.449 15.045 3.438 16.062 C4.097 16.062 4.758 16.062 5.438 16.062 C5.414 15.159 5.391 14.255 5.367 13.324 C5.34 11.555 5.34 11.555 5.312 9.75 C5.289 8.578 5.266 7.406 5.242 6.199 C5.438 3.062 5.438 3.062 7.438 0.062 C9.938 -0.25 9.938 -0.25 12.438 0.062 C15.284 2.909 14.893 5.866 15.062 9.688 C15.101 10.394 15.14 11.1 15.18 11.828 C15.274 13.572 15.357 15.317 15.438 17.062 C14.447 17.393 13.457 17.722 12.438 18.062 C11.942 10.638 11.942 10.638 11.438 3.062 C11.438 3.393 11.438 3.722 11.438 4.062 C11.438 5.383 11.438 6.702 11.438 8.062 C10.117 8.393 8.798 8.722 7.438 9.062 C7.107 11.702 6.778 14.342 6.438 17.062 C4.788 17.062 3.138 17.062 1.438 17.062 C0.942 9.638 0.942 9.638 0.438 2.062 C-0.883 1.732 -2.202 1.403 -3.562 1.062 C-2.562 0.062 -2.562 0.062 0 0 Z M7.438 2.062 C8.438 4.062 8.438 4.062 8.438 4.062 Z " fill="#8A8A88" transform="translate(585.5625,743.9375)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.66 1.32 4.32 2.64 5 4 C5.66 3.34 6.32 2.68 7 2 C9 5 9 5 8.625 7.188 C8.419 7.786 8.212 8.384 8 9 C7.34 9 6.68 9 6 9 C6 9.66 6 10.32 6 11 C5.34 11 4.68 11 4 11 C3.67 12.65 3.34 14.3 3 16 C3.66 16.33 4.32 16.66 5 17 C4.34 17 3.68 17 3 17 C3 18.65 3 20.3 3 22 C4.32 22 5.64 22 7 22 C7 22.66 7 23.32 7 24 C4.625 24.125 4.625 24.125 2 24 C0 22 0 22 -0.195 18.742 C-0.172 17.466 -0.149 16.19 -0.125 14.875 C-0.107 13.594 -0.089 12.312 -0.07 10.992 C-0.047 10.005 -0.024 9.017 0 8 C-0.99 8 -1.98 8 -3 8 C-3 7.34 -3 6.68 -3 6 C-2.01 6 -1.02 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#9D9D9B" transform="translate(311,736)"/>
<path d="M0 0 C3.32 0.473 6.137 1.394 9.188 2.75 C14.834 5.101 20.651 6.614 26.579 8.073 C31.774 9.42 36.117 11.117 40 15 C40.312 17.875 40.312 17.875 40 21 C37.006 24.473 33.392 24.578 29 25 C29.33 24.01 29.66 23.02 30 22 C31.32 22 32.64 22 34 22 C35.342 21.702 36.679 21.378 38 21 C37.505 18.03 37.505 18.03 37 15 C35.68 14.67 34.36 14.34 33 14 C33 13.34 33 12.68 33 12 C30.712 11.444 28.419 10.906 26.125 10.375 C24.849 10.073 23.573 9.772 22.258 9.461 C21.183 9.309 20.108 9.157 19 9 C18.34 9.66 17.68 10.32 17 11 C15.68 11 14.36 11 13 11 C12.936 10.384 12.871 9.768 12.805 9.133 C12.187 6.795 12.187 6.795 9.82 5.805 C8.931 5.498 8.041 5.191 7.125 4.875 C4.018 3.779 1.77 2.847 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#694340" transform="translate(339,477)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.598 1.794 2.196 1.587 2.812 1.375 C5 1 5 1 8 3 C9.188 5.625 9.188 5.625 10 8 C10.66 8 11.32 8 12 8 C12.33 6.02 12.66 4.04 13 2 C14.875 1.375 14.875 1.375 17 1 C17.66 1.66 18.32 2.32 19 3 C18.67 3.66 18.34 4.32 18 5 C18.66 5.33 19.32 5.66 20 6 C20.414 8.066 20.414 8.066 20.625 10.562 C20.7 11.389 20.775 12.215 20.852 13.066 C20.901 13.704 20.95 14.343 21 15 C20.34 15 19.68 15 19 15 C18.01 11.37 17.02 7.74 16 4 C12.759 10.762 12.759 10.762 11 18 C8.89 14.834 8.244 12.488 7.375 8.812 C7.115 7.726 6.854 6.639 6.586 5.52 C6.296 4.272 6.296 4.272 6 3 C5.34 3 4.68 3 4 3 C4.454 4.114 4.908 5.227 5.375 6.375 C7.178 11.144 8.127 15.989 9 21 C9.99 21 10.98 21 12 21 C11.67 21.99 11.34 22.98 11 24 C11 23.34 11 22.68 11 22 C10.01 22 9.02 22 8 22 C7.743 21.19 7.743 21.19 7.481 20.364 C6.703 17.927 5.914 15.495 5.125 13.062 C4.856 12.212 4.586 11.362 4.309 10.486 C4.044 9.677 3.78 8.869 3.508 8.035 C3.267 7.286 3.026 6.537 2.778 5.766 C2.137 3.803 2.137 3.803 0 3 C0 2.01 0 1.02 0 0 Z " fill="#888886" transform="translate(620,739)"/>
<path d="M0 0 C4.578 4.705 5.299 8.366 5.227 14.836 C4.75 19.388 3.269 23.731 0 27 C-2.875 27.375 -2.875 27.375 -6 27 C-8.848 24.768 -9.85 23.45 -11 20 C-12.017 6.574 -12.017 6.574 -9.125 2.188 C-5.904 -1.128 -4.372 -1.457 0 0 Z M-7.617 2.723 C-10.801 7.965 -10.348 14.312 -9.062 20.125 C-8.724 21.421 -8.376 22.714 -8 24 C-7.01 24 -6.02 24 -5 24 C-2.812 25 -2.812 25 -1 26 C2.6 21.461 3.155 18.192 3.188 12.438 C3.209 10.703 3.209 10.703 3.23 8.934 C3.31 5.877 3.31 5.877 1 4 C0 3 0 3 -1 1 C-4.753 0.533 -4.753 0.533 -7.617 2.723 Z " fill="#7B7B79" transform="translate(570,650)"/>
<path d="M0 0 C0.763 0.206 1.526 0.412 2.312 0.625 C-0.479 2.821 -3.272 4.58 -6.438 6.188 C-7.237 6.601 -8.036 7.015 -8.859 7.441 C-16.854 10.787 -23.641 10.761 -32.188 10.25 C-33.85 10.165 -33.85 10.165 -35.547 10.078 C-38.261 9.938 -40.974 9.786 -43.688 9.625 C-40.364 7.409 -39.627 7.444 -35.812 7.625 C-24.382 7.806 -9.445 -1.318 0 0 Z " fill="#B7B4B3" transform="translate(543.6875,223.375)"/>
<path d="M0 0 C4.149 0.556 6.611 1.9 10 4.312 C15.183 7.744 20.172 9.57 26.152 11.176 C29 12 29 12 30.863 13.066 C34.196 14.522 37.666 14.473 41.25 14.625 C41.995 14.664 42.74 14.702 43.508 14.742 C45.338 14.836 47.169 14.919 49 15 C49 15.33 49 15.66 49 16 C32.097 16.607 18.153 15.394 4 5 C1.66 2.277 1.66 2.277 0 0 Z " fill="#C2C0BF" transform="translate(470,218)"/>
<path d="M0 0 C1.506 1.964 2.891 3.783 4 6 C4.66 6 5.32 6 6 6 C6 7.32 6 8.64 6 10 C13.309 22.357 25.441 30.039 39 34 C41.332 34.075 43.669 34.093 46 34 C46 34.66 46 35.32 46 36 C38.635 38.065 31.277 34.807 24.812 31.242 C22.159 29.555 19.567 27.816 17 26 C16.01 25.34 15.02 24.68 14 24 C14 23.34 14 22.68 14 22 C13.412 21.764 12.824 21.528 12.219 21.285 C5.435 17.356 1.738 8.042 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#494747" transform="translate(458,194)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.01 7 -0.98 7 -2 7 C-2 7.66 -2 8.32 -2 9 C-1.01 9 -0.02 9 1 9 C1.33 13.62 1.66 18.24 2 23 C3.98 23.66 5.96 24.32 8 25 C8 24.34 8 23.68 8 23 C6.68 23 5.36 23 4 23 C4.33 22.34 4.66 21.68 5 21 C6.65 21 8.3 21 10 21 C9.67 23.31 9.34 25.62 9 28 C8.67 27.34 8.34 26.68 8 26 C7.051 26.021 6.103 26.041 5.125 26.062 C2 26 2 26 0 25 C-0.625 22.938 -0.625 22.938 -1 21 C-1.66 21.66 -2.32 22.32 -3 23 C-3 21.35 -3 19.7 -3 18 C-2.34 18 -1.68 18 -1 18 C-0.833 15.083 -0.833 15.083 -1 12 C-1.66 11.34 -2.32 10.68 -3 10 C-3.33 15.28 -3.66 20.56 -4 26 C-4.99 25.67 -5.98 25.34 -7 25 C-6.34 25 -5.68 25 -5 25 C-5 19.06 -5 13.12 -5 7 C-5.99 7 -6.98 7 -8 7 C-8 10.3 -8 13.6 -8 17 C-8.33 17 -8.66 17 -9 17 C-9 13.37 -9 9.74 -9 6 C-7.68 5.34 -6.36 4.68 -5 4 C-4.34 4.66 -3.68 5.32 -3 6 C-2.01 4.02 -1.02 2.04 0 0 Z " fill="#848483" transform="translate(310,735)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 9.57 1 19.14 1 29 C3.31 29.66 5.62 30.32 8 31 C8.99 31.66 9.98 32.32 11 33 C11 34.32 11 35.64 11 37 C9.435 36.742 7.873 36.468 6.312 36.188 C5.442 36.037 4.572 35.886 3.676 35.73 C-1.559 34.301 -1.559 34.301 -4 32 C-4.631 28.591 -4.468 25.264 -4.312 21.812 C-4.29 20.872 -4.267 19.932 -4.244 18.963 C-4.185 16.64 -4.103 14.321 -4 12 C-3.67 12 -3.34 12 -3 12 C-2.67 17.61 -2.34 23.22 -2 29 C-1.34 29.33 -0.68 29.66 0 30 C0 20.1 0 10.2 0 0 Z " fill="#C0BFBE" transform="translate(472,155)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 5.94 2 11.88 2 18 C-5 19 -5 19 -7.375 17.938 C-9.635 15.243 -10 13.507 -10 10 C-8.375 7.125 -8.375 7.125 -6 5 C-3.25 4.688 -3.25 4.688 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z M-6 8 C-6.33 9.98 -6.66 11.96 -7 14 C-5.02 14.99 -5.02 14.99 -3 16 C-0.713 14.161 -0.713 14.161 -0.875 11.375 C-0.916 10.591 -0.957 9.808 -1 9 C-2.65 8.67 -4.3 8.34 -6 8 Z " fill="#848483" transform="translate(412,885)"/>
<path d="M0 0 C6.338 3.639 6.338 3.639 8.016 7.09 C9.113 11.205 9.097 13.652 7.938 17.812 C5.94 21.098 4.223 22.934 1 25 C0.34 25 -0.32 25 -1 25 C-1 24.34 -1 23.68 -1 23 C-1.746 22.914 -2.493 22.827 -3.262 22.738 C-6.537 21.855 -7.634 20.746 -9.812 18.188 C-10.417 17.5 -11.022 16.813 -11.645 16.105 C-13 14 -13 14 -13 10 C-13.66 9.67 -14.32 9.34 -15 9 C-14.01 9 -13.02 9 -12 9 C-12.33 7.68 -12.66 6.36 -13 5 C-11.948 5.093 -11.948 5.093 -10.875 5.188 C-7.347 4.957 -5.094 3.65 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z M-6.5 5.375 C-8.843 9.475 -8.869 13.403 -8 18 C-6.127 20.883 -6.127 20.883 -3 22 C0.638 22.254 0.638 22.254 4 21 C6.656 18.087 6.978 16.313 7.25 12.375 C7.139 9.009 7.139 9.009 6 6 C5.34 6 4.68 6 4 6 C4 5.34 4 4.68 4 4 C-1.491 3.547 -1.491 3.547 -6.5 5.375 Z M-11 7 C-11.33 8.98 -11.66 10.96 -12 13 C-10.438 11.688 -10.438 11.688 -9 10 C-9 9.01 -9 8.02 -9 7 C-9.66 7 -10.32 7 -11 7 Z " fill="#898887" transform="translate(720,738)"/>
<path d="M0 0 C1.65 0.33 3.3 0.66 5 1 C5 1.66 5 2.32 5 3 C6.156 2.965 6.156 2.965 7.336 2.93 C8.339 2.912 9.342 2.894 10.375 2.875 C11.373 2.852 12.37 2.829 13.398 2.805 C16 3 16 3 18 5 C14.37 5.66 10.74 6.32 7 7 C7 6.34 7 5.68 7 5 C2.354 5.485 2.354 5.485 -2 7 C-2 6.34 -2 5.68 -2 5 C-2.66 5 -3.32 5 -4 5 C-4 10.94 -4 16.88 -4 23 C-3.34 23 -2.68 23 -2 23 C-2 18.71 -2 14.42 -2 10 C-1.34 9.67 -0.68 9.34 0 9 C-0.012 9.915 -0.023 10.83 -0.035 11.773 C-0.044 12.962 -0.053 14.15 -0.062 15.375 C-0.074 16.558 -0.086 17.742 -0.098 18.961 C-0.215 21.919 -0.215 21.919 1 24 C-1 24.043 -3 24.041 -5 24 C-6 23 -6 23 -6.114 21.142 C-6.108 20.352 -6.103 19.561 -6.098 18.746 C-6.094 17.892 -6.091 17.038 -6.088 16.158 C-6.08 15.26 -6.071 14.363 -6.062 13.438 C-6.058 12.536 -6.053 11.634 -6.049 10.705 C-6.037 8.47 -6.021 6.235 -6 4 C-4 3 -4 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#898887" transform="translate(582,737)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.67 0.99 4.34 1.98 4 3 C4 2.34 4 1.68 4 1 C3.01 1 2.02 1 1 1 C1.33 6.61 1.66 12.22 2 18 C2.66 14.7 3.32 11.4 4 8 C4.99 8.66 5.98 9.32 7 10 C7.547 12.828 7.547 12.828 7.75 16.25 C7.827 17.369 7.905 18.488 7.984 19.641 C8 22.894 7.637 25.816 7 29 C4.69 28.67 2.38 28.34 0 28 C0 18.76 0 9.52 0 0 Z " fill="#C3C3C3" transform="translate(444,654)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C3.99 1.67 4.98 1.34 6 1 C8.262 1.559 8.262 1.559 10.688 2.438 C11.496 2.725 12.304 3.012 13.137 3.309 C14.059 3.651 14.059 3.651 15 4 C15 4.99 15 5.98 15 7 C14.423 6.629 13.845 6.258 13.25 5.875 C11.021 4.73 11.021 4.73 8.812 5.75 C6.857 6.883 6.857 6.883 6 9 C5.768 11.713 5.581 14.408 5.438 17.125 C5.373 18.256 5.373 18.256 5.307 19.41 C5.201 21.273 5.1 23.137 5 25 C4.67 25 4.34 25 4 25 C3.67 21.04 3.34 17.08 3 13 C2.34 14.65 1.68 16.3 1 18 C0.67 18 0.34 18 0 18 C0 12.06 0 6.12 0 0 Z " fill="#9B9B9A" transform="translate(445,655)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.34 1 1.68 1 1 1 C2.353 3.706 3.81 3.962 6.562 5.125 C7.389 5.478 8.215 5.831 9.066 6.195 C10.024 6.594 10.024 6.594 11 7 C11.314 10.457 11.38 12.399 9.5 15.375 C6.011 17.643 3.054 17.847 -1 17 C-3.5 15.125 -3.5 15.125 -5 13 C-5 12.34 -5 11.68 -5 11 C-3.02 10.67 -1.04 10.34 1 10 C1 9.34 1 8.68 1 8 C0.01 7.67 -0.98 7.34 -2 7 C0 6 0 6 1.945 6.559 C2.665 6.849 3.384 7.139 4.125 7.438 C4.849 7.725 5.574 8.012 6.32 8.309 C6.875 8.537 7.429 8.765 8 9 C7.125 12.875 7.125 12.875 6 14 C4 14.041 2 14.043 0 14 C0 13.34 0 12.68 0 12 C-0.66 12 -1.32 12 -2 12 C-0.964 13.707 -0.964 13.707 1 15 C4.645 14.765 4.645 14.765 8 14 C8.33 12.35 8.66 10.7 9 9 C8.362 8.758 7.724 8.515 7.066 8.266 C6.24 7.93 5.414 7.595 4.562 7.25 C3.327 6.763 3.327 6.763 2.066 6.266 C0 5 0 5 -0.848 2.922 C-0.898 2.288 -0.948 1.653 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z M2 9 C2.99 10.485 2.99 10.485 4 12 C4.66 12 5.32 12 6 12 C6 11.34 6 10.68 6 10 C4.68 9.67 3.36 9.34 2 9 Z " fill="#888786" transform="translate(533,744)"/>
<path d="M0 0 C1.742 1.168 1.742 1.168 3.742 4.168 C-1.912 4.514 -5.288 3.602 -10.258 1.168 C-10.258 0.508 -10.258 -0.152 -10.258 -0.832 C-16.604 -0.577 -22.357 -0.221 -27.258 4.168 C-31.646 9.822 -33.326 14.716 -33.82 21.855 C-33.904 23.035 -33.988 24.214 -34.074 25.43 C-34.135 26.333 -34.195 27.237 -34.258 28.168 C-34.588 28.168 -34.918 28.168 -35.258 28.168 C-36.681 20.111 -36.382 13.242 -32 6.125 C-23.88 -4.28 -11.598 -5.544 0 0 Z M-3.258 1.168 C-2.598 1.828 -1.938 2.488 -1.258 3.168 C-1.258 2.508 -1.258 1.848 -1.258 1.168 C-1.918 1.168 -2.578 1.168 -3.258 1.168 Z " fill="#5D3835" transform="translate(743.2578125,323.83203125)"/>
<path d="M0 0 C-1.32 0.33 -2.64 0.66 -4 1 C-4 2.65 -4 4.3 -4 6 C-5.65 6.33 -7.3 6.66 -9 7 C-8.67 8.32 -8.34 9.64 -8 11 C-7.01 11 -6.02 11 -5 11 C-4.96 11.595 -4.921 12.191 -4.88 12.804 C-4.69 15.496 -4.47 18.185 -4.25 20.875 C-4.188 21.812 -4.126 22.749 -4.062 23.715 C-3.985 24.611 -3.908 25.507 -3.828 26.43 C-3.765 27.257 -3.702 28.085 -3.638 28.937 C-3.189 31.154 -3.189 31.154 -1.151 32.247 C1.535 33.187 4.185 33.616 7 34 C7 34.33 7 34.66 7 35 C1.97 35.296 -0.934 35.05 -5 32 C-5.619 31.546 -6.238 31.093 -6.875 30.625 C-8 29 -8 29 -7.688 26.312 C-7.461 25.549 -7.234 24.786 -7 24 C-7.33 24 -7.66 24 -8 24 C-8.195 22.376 -8.381 20.751 -8.562 19.125 C-8.667 18.22 -8.771 17.315 -8.879 16.383 C-9 14 -9 14 -8 12 C-8.99 11.67 -9.98 11.34 -11 11 C-10.67 8.69 -10.34 6.38 -10 4 C-9.34 4 -8.68 4 -8 4 C-7.67 3.01 -7.34 2.02 -7 1 C-4.057 -0.962 -3.48 -1.16 0 0 Z " fill="#888684" transform="translate(472,156)"/>
<path d="M0 0 C1.386 2.771 1.188 4.933 1 8 C0.01 9.485 0.01 9.485 -1 11 C-4.625 11.188 -4.625 11.188 -8 11 C-8 13.31 -8 15.62 -8 18 C-8.66 17.67 -9.32 17.34 -10 17 C-10 11.06 -10 5.12 -10 -1 C-5.128 -1.974 -4.109 -2.169 0 0 Z M-6 1 C-7.356 2.125 -7.356 2.125 -7.062 5.062 C-7.042 6.032 -7.021 7.001 -7 8 C-5.68 8.33 -4.36 8.66 -3 9 C-2.019 5.947 -2.019 4.053 -3 1 C-3.99 1 -4.98 1 -6 1 Z " fill="#A7A7A6" transform="translate(563,892)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.65 4.29 5.3 8.58 7 13 C7.144 11.886 7.289 10.773 7.438 9.625 C8 6 8 6 9 4 C9.66 4 10.32 4 11 4 C11.33 2.68 11.66 1.36 12 0 C12.66 0 13.32 0 14 0 C12.551 5.983 10.829 11.812 8.625 17.562 C8.246 18.554 7.867 19.545 7.477 20.566 C5.683 23.523 4.27 24.076 1 25 C2 22 2 22 5 20 C4.132 15.05 2.498 10.474 0.84 5.742 C0 3 0 3 0 0 Z " fill="#9D9C9B" transform="translate(446,742)"/>
<path d="M0 0 C0.969 0.866 1.939 1.733 2.938 2.625 C13.303 11.447 22.514 15.16 36.062 16.188 C37.023 16.269 37.984 16.351 38.975 16.436 C41.316 16.634 43.657 16.822 46 17 C44 19 44 19 41.398 19.195 C40.401 19.172 39.403 19.149 38.375 19.125 C36.871 19.098 36.871 19.098 35.336 19.07 C34.565 19.047 33.794 19.024 33 19 C32.67 19.66 32.34 20.32 32 21 C31.67 20.34 31.34 19.68 31 19 C29.207 18.489 27.388 18.067 25.562 17.688 C19.491 16.269 14.439 13.985 9 11 C6.648 9.776 4.53 8.843 2 8 C1.856 7.237 1.711 6.474 1.562 5.688 C1.037 2.918 1.037 2.918 0 0 Z " fill="#553A38" transform="translate(519,503)"/>
<path d="M0 0 C1.668 0.952 3.277 2.005 4.875 3.07 C8.281 2.896 9.62 2.298 12.188 0.008 C12.744 -0.632 13.301 -1.271 13.875 -1.93 C14.535 -1.6 15.195 -1.27 15.875 -0.93 C10.23 4.872 10.23 4.872 6.875 7.07 C6.215 7.07 5.555 7.07 4.875 7.07 C4.875 6.41 4.875 5.75 4.875 5.07 C4.215 5.07 3.555 5.07 2.875 5.07 C2.875 4.41 2.875 3.75 2.875 3.07 C-4.657 -0.046 -11.988 -0.996 -20.125 0.07 C-22.082 1.331 -22.082 1.331 -23.125 3.07 C-23.193 5.136 -23.193 5.136 -22.125 7.07 C-20.148 8.825 -20.148 8.825 -18.125 10.07 C-19.115 10.565 -19.115 10.565 -20.125 11.07 C-22.688 9.508 -22.688 9.508 -25.125 7.07 C-25.312 3.508 -25.312 3.508 -24.125 0.07 C-17.204 -5.575 -7.4 -3.673 0 0 Z " fill="#5B3936" transform="translate(379.125,451.9296875)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.67 3.66 0.34 4.32 0 5 C0 5.99 0 6.98 0 8 C0 8.66 0 9.32 0 10 C-0.99 10 -1.98 10 -3 10 C-3.33 11.98 -3.66 13.96 -4 16 C-4.66 16 -5.32 16 -6 16 C-6.122 16.715 -6.245 17.431 -6.371 18.168 C-7.011 21.051 -7.936 23.586 -9.062 26.312 C-9.425 27.196 -9.787 28.079 -10.16 28.988 C-10.576 29.984 -10.576 29.984 -11 31 C-12.212 27.364 -11.669 26.411 -10.348 22.895 C-9.983 21.912 -9.619 20.929 -9.244 19.916 C-8.854 18.892 -8.464 17.868 -8.062 16.812 C-7.676 15.777 -7.29 14.741 -6.893 13.674 C-5.937 11.113 -4.972 8.555 -4 6 C-5.65 6 -7.3 6 -9 6 C-9.266 6.915 -9.531 7.83 -9.805 8.773 C-10.334 10.556 -10.334 10.556 -10.875 12.375 C-11.223 13.558 -11.571 14.742 -11.93 15.961 C-13 19 -13 19 -15 21 C-13.667 15 -12.333 9 -11 3 C-10.374 2.951 -9.747 2.902 -9.102 2.852 C-8.284 2.777 -7.467 2.702 -6.625 2.625 C-5.813 2.555 -5.001 2.486 -4.164 2.414 C-1.797 2.171 -1.797 2.171 0 0 Z " fill="#7A7977" transform="translate(567,158)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-0.835 1.147 -0.835 1.147 -1.688 1.297 C-7.381 2.413 -11.908 4.222 -17 7 C-17 7.66 -17 8.32 -17 9 C-17.99 9 -18.98 9 -20 9 C-24.329 18.587 -25.469 28.032 -21.867 37.945 C-20.806 40.46 -19.53 42.741 -18 45 C-18.99 45 -19.98 45 -21 45 C-26.749 36.644 -27.459 27.825 -26 18 C-24.053 11.367 -20.776 6.738 -15 3 C-9.917 0.448 -5.656 -0.353 0 0 Z " fill="#4B2E2D" transform="translate(560,449)"/>
<path d="M0 0 C4.152 0.691 5.726 2.284 8.512 5.418 C10.08 8.213 10.649 9.569 10.137 12.73 C9.827 13.566 9.827 13.566 9.512 14.418 C9.223 13.449 8.934 12.479 8.637 11.48 C7.87 8.311 7.87 8.311 5.512 7.418 C5.512 11.048 5.512 14.678 5.512 18.418 C5.182 18.418 4.852 18.418 4.512 18.418 C4.473 17.513 4.473 17.513 4.434 16.59 C4.162 12.76 3.966 10.108 1.824 6.855 C-1.111 5.031 -3.094 5.109 -6.488 5.418 C-5.828 5.088 -5.168 4.758 -4.488 4.418 C-5.148 3.758 -5.808 3.098 -6.488 2.418 C-8.596 2.736 -8.596 2.736 -10.488 3.418 C-7.404 -0.36 -4.643 -0.142 0 0 Z " fill="#C4C2C2" transform="translate(526.48828125,161.58203125)"/>
<path d="M0 0 C2.165 2.221 2.94 3.563 3.363 6.656 C3.347 7.595 3.33 8.533 3.312 9.5 C3.309 10.438 3.305 11.377 3.301 12.344 C2.989 15.095 2.615 16.752 1 19 C-0.84 19.645 -0.84 19.645 -2.938 19.812 C-3.972 19.915 -3.972 19.915 -5.027 20.02 C-7 20 -7 20 -10 19 C-10 18.34 -10 17.68 -10 17 C-10.66 17 -11.32 17 -12 17 C-13.676 11.973 -13.809 6.901 -11.75 2.062 C-10 0 -10 0 -8.188 -0.875 C-5.228 -1.044 -2.865 -0.793 0 0 Z M-9.938 2.812 C-11.218 5.044 -11.218 5.044 -11 8 C-10.34 8 -9.68 8 -9 8 C-9 8.66 -9 9.32 -9 10 C-9.66 10 -10.32 10 -11 10 C-10.751 13.74 -10.609 15.458 -7.75 18 C-5.043 19.268 -5.043 19.268 -2.312 18.375 C0.268 17.066 0.268 17.066 2 14 C2.573 9.959 2.506 6.22 0.938 2.438 C-2.648 -0.223 -6.661 -0.252 -9.938 2.812 Z " fill="#7D7D7B" transform="translate(281,738)"/>
<path d="M0 0 C2.094 3.402 2.179 6.053 2 10 C2.99 10.33 3.98 10.66 5 11 C5 11.33 5 11.66 5 12 C3.02 12.33 1.04 12.66 -1 13 C-0.67 14.32 -0.34 15.64 0 17 C-3.96 17.495 -3.96 17.495 -8 18 C-7.171 11.577 -4.255 4.894 0 0 Z " fill="#ADADAC" transform="translate(624,654)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 2.97 2 5.94 2 9 C1.34 8.67 0.68 8.34 0 8 C-1.611 9.611 -2.322 11.656 -3.293 13.707 C-3.756 14.681 -4.219 15.656 -4.696 16.66 C-5.188 17.7 -5.68 18.741 -6.188 19.812 C-10.041 27.919 -13.924 36.002 -18 44 C-20.97 44 -23.94 44 -27 44 C-27 43.67 -27 43.34 -27 43 C-25.761 42.979 -24.522 42.959 -23.245 42.938 C-21.03 42.721 -21.03 42.721 -19 42 C-17.162 39.518 -16.19 37.79 -15.078 35 C-14.61 33.935 -14.61 33.935 -14.132 32.849 C-13.141 30.578 -12.195 28.291 -11.25 26 C-10.601 24.49 -9.948 22.98 -9.293 21.473 C-8.048 18.595 -6.819 15.712 -5.607 12.82 C-3.804 8.517 -1.915 4.255 0 0 Z " fill="#4C2E2C" transform="translate(519,335)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.061 0.999 2.121 1.998 2.184 3.027 C2.267 4.318 2.351 5.608 2.438 6.938 C2.519 8.225 2.6 9.513 2.684 10.84 C2.649 13.86 2.649 13.86 4 15 C5.666 15.041 7.334 15.043 9 15 C10.416 12.167 10.346 9.525 10.562 6.375 C10.646 5.186 10.73 3.998 10.816 2.773 C10.877 1.858 10.938 0.943 11 0 C11.66 0 12.32 0 13 0 C13 5.94 13 11.88 13 18 C8.001 19 6.448 19.076 2 17 C-0.419 13.562 -0.207 10.351 -0.125 6.25 C-0.098 4.494 -0.098 4.494 -0.07 2.703 C-0.047 1.811 -0.024 0.919 0 0 Z " fill="#AAAAAA" transform="translate(390,742)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.825 1.835 4.65 1.67 5.5 1.5 C8 1 8 1 11 3 C9.376 3.195 7.751 3.381 6.125 3.562 C5.22 3.667 4.315 3.771 3.383 3.879 C1 4 1 4 -1 3 C-1 8.94 -1 14.88 -1 21 C-0.34 21 0.32 21 1 21 C0.977 20.229 0.954 19.458 0.93 18.664 C0.912 17.661 0.894 16.658 0.875 15.625 C0.852 14.627 0.829 13.63 0.805 12.602 C1 10 1 10 3 8 C3.33 8.99 3.66 9.98 4 11 C4.66 11 5.32 11 6 11 C6.66 10.01 7.32 9.02 8 8 C8 9.65 8 11.3 8 13 C6.35 13 4.7 13 3 13 C3 15.97 3 18.94 3 22 C0.36 21.67 -2.28 21.34 -5 21 C-4.835 20.361 -4.67 19.721 -4.5 19.062 C-3.961 17.192 -3.961 17.192 -4 16 C-4 15.01 -4 14.02 -4 13 C-3.34 13 -2.68 13 -2 13 C-2.107 12.313 -2.214 11.626 -2.324 10.918 C-2.444 10.017 -2.564 9.116 -2.688 8.188 C-2.815 7.294 -2.943 6.401 -3.074 5.48 C-3 3 -3 3 -1.52 1.176 C-1.018 0.788 -0.517 0.4 0 0 Z " fill="#828280" transform="translate(733,739)"/>
<path d="M0 0 C1.429 2.354 2.087 3.48 1.625 6.25 C1.419 6.827 1.212 7.405 1 8 C1.99 8 2.98 8 4 8 C3.67 9.32 3.34 10.64 3 12 C1.35 12 -0.3 12 -2 12 C-2 11.34 -2 10.68 -2 10 C-2.99 10.495 -2.99 10.495 -4 11 C-7.5 11.25 -7.5 11.25 -11 11 C-13.152 7.772 -13.201 6.716 -13 3 C-8.839 2.918 -5.053 3.018 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z M-12 5 C-11.67 6.32 -11.34 7.64 -11 9 C-8.03 9.495 -8.03 9.495 -5 10 C-5.66 9.67 -6.32 9.34 -7 9 C-6.67 8.01 -6.34 7.02 -6 6 C-8.97 5.505 -8.97 5.505 -12 5 Z M-4 6 C-3 8 -3 8 -3 8 Z M-2 6 C-1 8 -1 8 -1 8 Z " fill="#8F8F8D" transform="translate(622,747)"/>
<path d="M0 0 C0.91 0.009 1.82 0.018 2.758 0.027 C3.456 0.039 4.155 0.051 4.875 0.062 C4.875 0.393 4.875 0.722 4.875 1.062 C3.225 1.393 1.575 1.722 -0.125 2.062 C1.267 2.681 1.267 2.681 2.688 3.312 C5.777 5.009 7.277 5.961 8.875 9.062 C8.562 11.312 8.562 11.312 7.875 13.062 C7.442 12.114 7.009 11.165 6.562 10.188 C5.082 6.862 5.082 6.862 1.875 5.062 C-0.635 4.997 -0.635 4.997 -3.125 6.062 C-5.441 8.519 -5.441 8.519 -7.125 11.062 C-7.785 10.403 -8.445 9.742 -9.125 9.062 C-10.115 10.383 -11.105 11.702 -12.125 13.062 C-11.711 7.788 -10.835 4.885 -7.125 1.062 C-4.6 -0.2 -2.813 -0.036 0 0 Z " fill="#C1C1C0" transform="translate(566.125,644.9375)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.168 4.271 5.334 8.542 5.5 12.812 C5.548 14.027 5.595 15.242 5.645 16.494 C5.69 17.658 5.735 18.821 5.781 20.02 C5.823 21.093 5.865 22.167 5.908 23.273 C6.042 27.25 5.925 29.512 4 33 C2.02 32.01 0.04 31.02 -2 30 C-2.66 30.66 -3.32 31.32 -4 32 C-5.688 31.688 -5.688 31.688 -8 31 C-9.217 30.856 -10.434 30.711 -11.688 30.562 C-12.781 30.377 -13.874 30.191 -15 30 C-15.33 29.34 -15.66 28.68 -16 28 C-14.969 28.052 -14.969 28.052 -13.918 28.105 C-12.566 28.146 -12.566 28.146 -11.188 28.188 C-10.294 28.222 -9.401 28.257 -8.48 28.293 C-5.54 27.946 -4.895 27.168 -3 25 C-2.34 25 -1.68 25 -1 25 C-1 25.99 -1 26.98 -1 28 C0.65 28 2.3 28 4 28 C4 19.09 4 10.18 4 1 C2.68 0.67 1.36 0.34 0 0 Z " fill="#676665" transform="translate(432,653)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.139 4.797 0.71 8.911 -0.438 13.562 C-1.88 19.845 -2.456 25.898 -2.691 32.332 C-2.793 33.212 -2.895 34.093 -3 35 C-3.99 35.495 -3.99 35.495 -5 36 C-5.47 23.259 -5.845 11.691 0 0 Z " fill="#C8C7C6" transform="translate(456,147)"/>
<path d="M0 0 C1.236 0.017 1.236 0.017 2.496 0.035 C3.322 0.044 4.149 0.053 5 0.062 C5.638 0.074 6.276 0.086 6.934 0.098 C7.264 0.758 7.594 1.418 7.934 2.098 C5.624 2.758 3.314 3.418 0.934 4.098 C0.934 3.438 0.934 2.778 0.934 2.098 C0.274 2.098 -0.386 2.098 -1.066 2.098 C-1.066 8.038 -1.066 13.978 -1.066 20.098 C-0.406 20.098 0.254 20.098 0.934 20.098 C0.91 19.11 0.887 18.123 0.863 17.105 C0.836 15.183 0.836 15.183 0.809 13.223 C0.774 11.308 0.774 11.308 0.738 9.355 C0.934 6.098 0.934 6.098 2.934 4.098 C5.559 3.973 5.559 3.973 7.934 4.098 C7.934 4.758 7.934 5.418 7.934 6.098 C6.284 6.428 4.634 6.758 2.934 7.098 C2.604 11.718 2.274 16.338 1.934 21.098 C0.284 21.098 -1.366 21.098 -3.066 21.098 C-4.01 16.512 -4.056 12.447 -3.691 7.785 C-3.601 6.552 -3.511 5.318 -3.418 4.047 C-2.952 0.14 -2.952 0.14 0 0 Z " fill="#888887" transform="translate(520.06640625,739.90234375)"/>
<path d="M0 0 C3.375 1.062 3.375 1.062 5.312 2.562 C6.945 6.404 7.033 9.958 6.375 14.062 C4.544 17.317 3.381 18.65 -0.062 20.062 C-3.347 20.062 -4.99 18.945 -7.625 17.062 C-7.295 16.403 -6.965 15.742 -6.625 15.062 C-2.858 15.062 -1.713 15.298 1.375 17.062 C3.772 15.129 3.772 15.129 3.438 11.625 C3.417 10.449 3.396 9.274 3.375 8.062 C3.705 7.072 4.035 6.082 4.375 5.062 C3.055 5.062 1.735 5.062 0.375 5.062 C0.045 4.072 -0.285 3.082 -0.625 2.062 C-3.168 2.307 -3.168 2.307 -5.625 4.062 C-6.705 7.341 -7.202 10.643 -7.625 14.062 C-7.955 14.062 -8.285 14.062 -8.625 14.062 C-9.099 9.228 -9.305 6.092 -6.562 1.938 C-3.625 0.062 -3.625 0.062 0 0 Z " fill="#797A79" transform="translate(487.625,657.9375)"/>
<path d="M0 0 C4.509 2.255 8.447 5.447 12 9 C12.536 9.454 13.072 9.908 13.625 10.375 C15 12 15 12 15 16 C14.34 16 13.68 16 13 16 C12.963 16.592 12.925 17.183 12.887 17.793 C12.486 22.607 11.961 26.008 9 30 C8.34 29.67 7.68 29.34 7 29 C7.486 28.297 7.972 27.595 8.473 26.871 C11.014 22.094 10.663 17.279 10 12 C8.721 8.805 7.154 6.577 4 5 C2.232 4.878 0.459 4.824 -1.312 4.812 C-2.257 4.798 -3.202 4.784 -4.176 4.77 C-7.144 5.012 -9.309 5.76 -12 7 C-11 4 -11 4 -8.812 2.812 C-5.746 1.927 -3.176 1.852 0 2 C0 1.34 0 0.68 0 0 Z M12 11 C13 13 13 13 13 13 Z M13 13 C12.67 13.66 12.34 14.32 12 15 C12.66 15 13.32 15 14 15 C13.67 14.34 13.34 13.68 13 13 Z " fill="#828280" transform="translate(488,650)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 1.34 2.98 0.68 4 0 C6.961 0.359 7.847 0.847 10 3 C9.977 5.383 9.977 5.383 9.625 8.125 C9.459 9.49 9.459 9.49 9.289 10.883 C9.194 11.581 9.098 12.28 9 13 C8.67 13 8.34 13 8 13 C8 9.7 8 6.4 8 3 C5.525 4.485 5.525 4.485 3 6 C4.32 6 5.64 6 7 6 C7 7.65 7 9.3 7 11 C6.01 11.33 5.02 11.66 4 12 C4 11.01 4 10.02 4 9 C3.34 10.98 2.68 12.96 2 15 C1.34 15 0.68 15 0 15 C0 10.05 0 5.1 0 0 Z " fill="#9E9E9E" transform="translate(478,890)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C3.392 1.845 3.392 1.845 4.812 1.688 C8 2 8 2 9.938 4.25 C11 7 11 7 10 11 C5.545 11.495 5.545 11.495 1 12 C1.33 10.35 1.66 8.7 2 7 C1.34 7 0.68 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#9A9A99" transform="translate(422,740)"/>
<path d="M0 0 C2.56 1.707 2.92 2.687 3.688 5.688 C3.791 6.451 3.894 7.214 4 8 C4.33 8 4.66 8 5 8 C5 11.96 5 15.92 5 20 C3.35 20 1.7 20 0 20 C0.66 19.67 1.32 19.34 2 19 C1.886 16.729 1.758 14.458 1.625 12.188 C1.555 10.923 1.486 9.658 1.414 8.355 C1.239 4.872 1.239 4.872 -1 2 C-2.977 1.637 -2.977 1.637 -5.125 1.812 C-6.404 1.874 -7.683 1.936 -9 2 C-7.875 3.356 -7.875 3.356 -4.938 3.062 C-3.968 3.042 -2.999 3.021 -2 3 C-2 3.33 -2 3.66 -2 4 C-4.64 4.66 -7.28 5.32 -10 6 C-9.67 6.66 -9.34 7.32 -9 8 C-8.01 8.33 -7.02 8.66 -6 9 C-7.65 9.33 -9.3 9.66 -11 10 C-11.33 7.36 -11.66 4.72 -12 2 C-7.745 -1.223 -5.131 -1.33 0 0 Z " fill="#878786" transform="translate(570,741)"/>
<path d="M0 0 C1.087 0.027 2.173 0.054 3.293 0.082 C4.54 0.134 4.54 0.134 5.812 0.188 C5.812 0.518 5.812 0.847 5.812 1.188 C2.513 1.518 -0.788 1.847 -4.188 2.188 C-1.548 2.518 1.092 2.847 3.812 3.188 C3.812 3.518 3.812 3.847 3.812 4.188 C2.905 4.229 1.997 4.27 1.062 4.312 C-2.471 4.87 -2.471 4.87 -4.125 8.25 C-4.806 9.219 -5.486 10.189 -6.188 11.188 C-9.938 11.562 -9.938 11.562 -13.188 11.188 C-12.857 8.548 -12.528 5.908 -12.188 3.188 C-11.857 3.188 -11.528 3.188 -11.188 3.188 C-10.857 4.837 -10.528 6.487 -10.188 8.188 C-10.084 7.424 -9.981 6.661 -9.875 5.875 C-8.41 0.15 -5.552 -0.179 0 0 Z " fill="#BFBFBE" transform="translate(533.1875,644.8125)"/>
<path d="M0 0 C6.152 -0.098 6.152 -0.098 8 0 C9 1 9 1 9.114 3.133 C9.108 4.049 9.103 4.966 9.098 5.91 C9.094 6.9 9.091 7.889 9.088 8.908 C9.08 9.949 9.071 10.99 9.062 12.062 C9.058 13.107 9.053 14.152 9.049 15.229 C9.037 17.819 9.021 20.41 9 23 C10.65 22.67 12.3 22.34 14 22 C14 24.97 14 27.94 14 31 C13.67 31 13.34 31 13 31 C13 29.02 13 27.04 13 25 C11.02 25 9.04 25 7 25 C7 17.41 7 9.82 7 2 C4.36 2.33 1.72 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#7F7F7D" transform="translate(630,643)"/>
<path d="M0 0 C1.486 2.837 2.615 5.673 3.688 8.688 C7.364 17.865 13.208 24.466 22 29 C26.282 30.825 30.607 32.465 35 34 C35 34.33 35 34.66 35 35 C24.897 34.832 17.042 30.055 9.625 23.375 C4.467 17.85 0.194 10.229 -0.156 2.602 C-0.105 1.743 -0.053 0.885 0 0 Z " fill="#452E2D" transform="translate(410,485)"/>
<path d="M0 0 C1.488 0.988 1.488 0.988 3 3 C3.217 5.811 3.283 8.388 3.188 11.188 C3.174 11.937 3.16 12.687 3.146 13.459 C3.111 15.306 3.057 17.153 3 19 C1.35 19.33 -0.3 19.66 -2 20 C-2.495 12.575 -2.495 12.575 -3 5 C-3.99 5.33 -4.98 5.66 -6 6 C-6.66 5.67 -7.32 5.34 -8 5 C-7.67 4.01 -7.34 3.02 -7 2 C-5.35 2 -3.7 2 -2 2 C-1.34 3.98 -0.68 5.96 0 8 C0 5.36 0 2.72 0 0 Z " fill="#818180" transform="translate(744,742)"/>
<path d="M0 0 C1.894 2.841 3.245 5.6 4.688 8.688 C10.362 20.022 18.044 26.791 30.125 30.875 C31.413 31.263 32.703 31.643 34 32 C34 32.33 34 32.66 34 33 C23.449 32.602 15.137 27.128 8.016 19.57 C3.565 13.895 -0.527 7.406 0 0 Z " fill="#3E2928" transform="translate(686,361)"/>
<path d="M0 0 C1.923 2.885 2.885 5.325 4.062 8.562 C8.893 20.189 17.008 26.383 28 32 C26.332 32.684 26.332 32.684 24 33 C12.511 27.81 5.984 19.209 1 8 C0.34 8 -0.32 8 -1 8 C-1.33 7.01 -1.66 6.02 -2 5 C-1.34 5 -0.68 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#4A302F" transform="translate(342,359)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-3.96 1 -7.92 1 -12 1 C-12 8.92 -12 16.84 -12 25 C-9.938 24.711 -7.875 24.423 -5.75 24.125 C-4.59 23.963 -3.43 23.8 -2.234 23.633 C0.582 23.082 2.552 22.442 5 21 C4 24 4 24 2.359 25.148 C-2.513 26.907 -7.879 26.693 -13 27 C-13 18.42 -13 9.84 -13 1 C-13.66 0.67 -14.32 0.34 -15 0 C-9.39 -1.621 -5.538 -2.556 0 0 Z " fill="#71726F" transform="translate(487,735)"/>
<path d="M0 0 C2.281 3.421 2.219 4.682 2.125 8.688 C2.107 9.681 2.089 10.675 2.07 11.699 C2.047 12.458 2.024 13.218 2 14 C2.619 14.124 3.238 14.247 3.875 14.375 C6 15 6 15 8 17 C6.35 17 4.7 17 3 17 C2.67 17.66 2.34 18.32 2 19 C2 18.34 2 17.68 2 17 C0.35 16.67 -1.3 16.34 -3 16 C-2.979 15.237 -2.959 14.474 -2.938 13.688 C-2.871 10.898 -2.871 10.898 -4 8 C-3.722 5.659 -3.395 3.324 -3 1 C-2.34 1.99 -1.68 2.98 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#8F8F8E" transform="translate(421,744)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 3.3 4 6.6 4 10 C4.99 10.33 5.98 10.66 7 11 C6.01 11 5.02 11 4 11 C2.716 13.569 2.67 15.766 2.438 18.625 C2.312 20.129 2.312 20.129 2.184 21.664 C2.123 22.435 2.062 23.206 2 24 C2.99 24 3.98 24 5 24 C5 24.99 5 25.98 5 27 C3.68 26.67 2.36 26.34 1 26 C-0.016 20.739 -0.35 15.526 -0.625 10.188 C-0.677 9.317 -0.728 8.446 -0.781 7.549 C-0.825 6.722 -0.869 5.895 -0.914 5.043 C-0.955 4.29 -0.996 3.538 -1.038 2.762 C-1 1 -1 1 0 0 Z " fill="#989898" transform="translate(329,733)"/>
<path d="M0 0 C-0.498 3.735 -0.875 5.812 -3 9 C-6.039 9.512 -6.039 9.512 -9.625 9.688 C-10.814 9.753 -12.002 9.819 -13.227 9.887 C-14.599 9.943 -14.599 9.943 -16 10 C-15.67 9.34 -15.34 8.68 -15 8 C-14.01 8 -13.02 8 -12 8 C-12 7.34 -12 6.68 -12 6 C-10.041 4.607 -8.036 3.278 -6 2 C-3 0 -3 0 0 0 Z " fill="#ABAAA9" transform="translate(580,671)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.98 2 3.96 2 6 C3.32 6.33 4.64 6.66 6 7 C5.67 7.66 5.34 8.32 5 9 C3.68 9 2.36 9 1 9 C1 13.62 1 18.24 1 23 C2.65 23 4.3 23 6 23 C6 23.66 6 24.32 6 25 C3.625 25.188 3.625 25.188 1 25 C-1.421 21.368 -1.21 19.617 -1.125 15.312 C-1.098 13.543 -1.098 13.543 -1.07 11.738 C-1.047 10.835 -1.024 9.931 -1 9 C-1.99 9 -2.98 9 -4 9 C-4 8.34 -4 7.68 -4 7 C-3.01 7 -2.02 7 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#B3B3B3" transform="translate(549,735)"/>
<path d="M0 0 C6.029 1.464 11.027 3.839 16.012 7.512 C18.901 10.054 18.901 10.054 22.137 9.762 C23.887 12.074 23.887 12.074 25.137 14.762 C24.642 16.247 24.642 16.247 24.137 17.762 C23.664 17.242 23.191 16.723 22.703 16.188 C12.676 5.613 2.235 2.938 -11.863 0.762 C-7.592 -2.086 -4.88 -0.924 0 0 Z " fill="#4C2E2D" transform="translate(568.86328125,429.23828125)"/>
<path d="M0 0 C1.905 4.941 -0.532 8.912 -2.418 13.547 C-6.488 22.432 -11.775 29.451 -19 36 C-19.66 35.34 -20.32 34.68 -21 34 C-20.381 33.588 -19.763 33.175 -19.125 32.75 C-16.935 31.188 -16.935 31.188 -16.062 29.125 C-15 27 -15 27 -12.688 24.812 C-6.094 17.913 -3.207 8.829 0 0 Z " fill="#424141" transform="translate(568,183)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 1.66 0.68 2.32 0 3 C-0.242 4.981 -0.242 4.981 -0.125 7.125 C-0.084 8.404 -0.043 9.683 0 11 C1.98 11.99 3.96 12.98 6 14 C4.312 14.688 4.312 14.688 2 15 C0.315 14.031 -1.352 13.03 -3 12 C-4.32 12 -5.64 12 -7 12 C-8.667 12 -10.333 12 -12 12 C-12 11.01 -12 10.02 -12 9 C-11.01 9.33 -10.02 9.66 -9 10 C-9.33 9.01 -9.66 8.02 -10 7 C-8.35 6.67 -6.7 6.34 -5 6 C-5.66 5.67 -6.32 5.34 -7 5 C-7 4.34 -7 3.68 -7 3 C-4.696 1.933 -2.36 0.936 0 0 Z M-3 3 C-3 6 -3 6 -3 6 Z " fill="#828382" transform="translate(626,890)"/>
<path d="M0 0 C-0.33 5.28 -0.66 10.56 -1 16 C-2.65 16.33 -4.3 16.66 -6 17 C-6.081 14.917 -6.139 12.834 -6.188 10.75 C-6.222 9.59 -6.257 8.43 -6.293 7.234 C-5.875 2.615 -4.908 0 0 0 Z " fill="#B4B4B3" transform="translate(559,653)"/>
<path d="M0 0 C3.188 0.312 3.188 0.312 5.625 2.062 C6.141 2.805 6.656 3.548 7.188 4.312 C6.858 5.632 6.527 6.952 6.188 8.312 C1.897 8.312 -2.392 8.312 -6.812 8.312 C-3.499 0.389 -3.499 0.389 0 0 Z M-1.812 1.312 C-0.327 2.303 -0.327 2.303 1.188 3.312 C1.188 2.653 1.188 1.993 1.188 1.312 C0.197 1.312 -0.793 1.312 -1.812 1.312 Z M-2.812 2.312 C-3.142 2.972 -3.473 3.632 -3.812 4.312 C-3.152 4.312 -2.493 4.312 -1.812 4.312 C-2.142 3.653 -2.473 2.993 -2.812 2.312 Z M2.188 2.312 C2.848 2.972 3.507 3.632 4.188 4.312 C4.188 3.653 4.188 2.993 4.188 2.312 C3.527 2.312 2.868 2.312 2.188 2.312 Z M4.188 4.312 C5.188 6.312 5.188 6.312 5.188 6.312 Z " fill="#969593" transform="translate(494.8125,165.6875)"/>
<path d="M0 0 C2.067 3.101 2.245 3.729 2.195 7.23 C2.189 8.033 2.182 8.835 2.176 9.662 C2.159 10.495 2.142 11.329 2.125 12.188 C2.116 13.032 2.107 13.877 2.098 14.748 C2.074 16.832 2.038 18.916 2 21 C6.477 21.043 10.599 20.859 15 20 C15 20.66 15 21.32 15 22 C9.665 24.846 6.894 24 0 24 C0 16.08 0 8.16 0 0 Z " fill="#BEBEBE" transform="translate(475,736)"/>
<path d="M0 0 C0 3 0 3 -1.496 4.719 C-2.137 5.307 -2.777 5.894 -3.438 6.5 C-5.921 8.788 -6.909 9.726 -8 13 C-0.41 13 7.18 13 15 13 C15 13.66 15 14.32 15 15 C6.75 15 -1.5 15 -10 15 C-7.75 7.125 -7.75 7.125 -3.875 3.562 C-2.788 2.543 -2.788 2.543 -1.68 1.504 C-0.848 0.759 -0.848 0.759 0 0 Z " fill="#BBBABA" transform="translate(531,667)"/>
<path d="M0 0 C1.441 0.595 2.878 1.202 4.312 1.812 C5.513 2.317 5.513 2.317 6.738 2.832 C8.965 3.982 10.35 5.136 12 7 C11.01 8.485 11.01 8.485 10 10 C9.649 9.175 9.299 8.35 8.938 7.5 C7.159 4.617 7.159 4.617 3.5 4.438 C0.064 4.703 0.064 4.703 -1.938 6.5 C-3.452 10.063 -3.564 13.187 -3 17 C-2.048 19.52 -2.048 19.52 0 21 C3.102 21.809 3.102 21.809 6 22 C6 22.33 6 22.66 6 23 C-0.179 23.475 -0.179 23.475 -3.375 21.625 C-5.849 17.628 -6.211 13.657 -6 9 C-4.653 5.921 -3.353 4.353 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#848482" transform="translate(375,738)"/>
<path d="M0 0 C2.829 2.551 4.679 5.466 6.688 8.688 C7.31 9.681 7.933 10.675 8.574 11.699 C9.045 12.458 9.515 13.218 10 14 C9.977 13.397 9.954 12.793 9.93 12.172 C9.903 10.973 9.903 10.973 9.875 9.75 C9.852 8.961 9.829 8.172 9.805 7.359 C10.017 4.793 10.713 3.208 12 1 C13.327 7.968 13.088 14.93 13 22 C9.213 18.604 6.814 14.768 4.25 10.438 C3.839 9.765 3.428 9.093 3.004 8.4 C0 3.376 0 3.376 0 0 Z " fill="#52312E" transform="translate(658,460)"/>
<path d="M0 0 C1.671 0.062 1.671 0.062 3.375 0.125 C3.705 0.785 4.035 1.445 4.375 2.125 C1.735 2.455 -0.905 2.785 -3.625 3.125 C-3.625 4.445 -3.625 5.765 -3.625 7.125 C-2.346 7.372 -1.067 7.62 0.25 7.875 C4.141 8.855 4.141 8.855 6 11 C6.375 13.125 6.375 13.125 5.938 15.312 C4.375 17.125 4.375 17.125 0.938 18 C-2.625 18.125 -2.625 18.125 -5.625 16.125 C-5.625 15.465 -5.625 14.805 -5.625 14.125 C-4.965 14.125 -4.305 14.125 -3.625 14.125 C-3.625 14.785 -3.625 15.445 -3.625 16.125 C-0.137 15.355 -0.137 15.355 3.375 14.125 C3.705 13.135 4.035 12.145 4.375 11.125 C1.075 10.135 -2.225 9.145 -5.625 8.125 C-5.75 5.25 -5.75 5.25 -5.625 2.125 C-3.625 0.125 -3.625 0.125 0 0 Z " fill="#B8B7B6" transform="translate(536.625,741.875)"/>
<path d="M0 0 C0.958 0.047 1.917 0.094 2.904 0.143 C3.939 0.191 4.973 0.24 6.039 0.289 C7.127 0.345 8.216 0.401 9.338 0.459 C10.43 0.511 11.523 0.564 12.648 0.617 C15.357 0.748 18.066 0.883 20.775 1.022 C20.775 1.352 20.775 1.682 20.775 2.022 C13.515 2.022 6.255 2.022 -1.225 2.022 C-1.225 15.882 -1.225 29.742 -1.225 44.022 C-1.555 44.022 -1.885 44.022 -2.225 44.022 C-2.392 37.916 -2.553 31.811 -2.707 25.705 C-2.761 23.626 -2.816 21.547 -2.873 19.469 C-2.955 16.488 -3.03 13.507 -3.104 10.526 C-3.131 9.59 -3.158 8.655 -3.186 7.691 C-3.206 6.83 -3.226 5.969 -3.247 5.083 C-3.267 4.321 -3.287 3.559 -3.307 2.774 C-3.18 0.072 -2.688 0.026 0 0 Z " fill="#180F12" transform="translate(654.224853515625,299.978271484375)"/>
<path d="M0 0 C8.58 0 17.16 0 26 0 C28.475 4.455 28.475 4.455 31 9 C30.67 9.99 30.34 10.98 30 12 C29.788 11.363 29.575 10.725 29.357 10.069 C28.17 7.257 27.078 5.249 25 3 C20.736 1.706 16.615 1.831 12.188 1.938 C10.433 1.927 10.433 1.927 8.643 1.916 C5.759 1.904 2.882 1.934 0 2 C0 1.34 0 0.68 0 0 Z " fill="#EA8473" transform="translate(447,302)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.493 5.397 -1.119 7.838 -4 11 C-7.215 12.607 -10.436 12.057 -14 12 C-14.8 8.713 -15.097 7.29 -14 4 C-12.152 3.27 -12.152 3.27 -9.938 2.812 C-9.204 2.654 -8.471 2.495 -7.715 2.332 C-6.866 2.168 -6.866 2.168 -6 2 C-6.786 3.149 -7.579 4.294 -8.375 5.438 C-8.816 6.076 -9.257 6.714 -9.711 7.371 C-11 9 -11 9 -13 10 C-9.809 9.727 -6.952 9.27 -4 8 C-2.075 5.525 -0.985 2.963 0 0 Z " fill="#807E7D" transform="translate(554,189)"/>
<path d="M0 0 C8.024 0.365 15.538 4.223 21.742 9.199 C23.779 11.208 23.779 11.208 26 11 C26.75 13.75 26.75 13.75 27 17 C25.062 19.375 25.062 19.375 23 21 C22.34 20.67 21.68 20.34 21 20 C21.99 19.01 22.98 18.02 24 17 C21.193 10.487 15.156 7.295 8.812 4.688 C5.568 3.445 2.419 2.613 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#593635" transform="translate(374,429)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 17.16 1 34.32 1 52 C1.66 52.33 2.32 52.66 3 53 C2.01 53.495 2.01 53.495 1 54 C-0.134 56.017 -0.134 56.017 -1 58 C-1.837 55.581 -2.016 54.085 -1.547 51.544 C-0.794 46.668 -0.764 41.842 -0.684 36.914 C-0.652 35.329 -0.652 35.329 -0.621 33.711 C-0.555 30.349 -0.496 26.987 -0.438 23.625 C-0.394 21.34 -0.351 19.055 -0.307 16.77 C-0.2 11.18 -0.098 5.59 0 0 Z " fill="#975952" transform="translate(621,340)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C4.554 1.081 6.112 1.108 7.668 1.098 C8.561 1.094 9.453 1.091 10.373 1.088 C11.302 1.08 12.231 1.071 13.188 1.062 C14.128 1.058 15.068 1.053 16.037 1.049 C18.358 1.037 20.679 1.021 23 1 C23 1.66 23 2.32 23 3 C25.64 3 28.28 3 31 3 C31 3.33 31 3.66 31 4 C30.071 4.015 29.143 4.029 28.186 4.044 C24.745 4.105 21.305 4.179 17.865 4.262 C16.375 4.296 14.886 4.324 13.396 4.346 C11.255 4.38 9.116 4.432 6.977 4.488 C5.688 4.514 4.4 4.541 3.073 4.568 C1.552 4.782 1.552 4.782 0 5 C-0.66 5.99 -1.32 6.98 -2 8 C-3.245 5.509 -2.777 4.589 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#73433E" transform="translate(734,336)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C4.884 3.295 3.756 6.585 2.625 9.875 C2.149 11.281 2.149 11.281 1.664 12.715 C1.355 13.611 1.045 14.507 0.727 15.43 C0.444 16.257 0.161 17.085 -0.13 17.937 C-1 20 -1 20 -3 22 C-2.446 18.03 -1.501 14.701 0 11 C0 10.01 0 9.02 0 8 C-0.99 9.32 -1.98 10.64 -3 12 C-2.401 7.805 -1.425 3.99 0 0 Z " fill="#C6C5C4" transform="translate(558,163)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.98 3.33 -3.96 3.66 -6 4 C-6 5.32 -6 6.64 -6 8 C-5.01 8 -4.02 8 -3 8 C-2.67 7.01 -2.34 6.02 -2 5 C-1.01 5.33 -0.02 5.66 1 6 C0.67 7.98 0.34 9.96 0 12 C-1.314 11.692 -2.626 11.378 -3.938 11.062 C-4.668 10.888 -5.399 10.714 -6.152 10.535 C-8 10 -8 10 -9 9 C-9.041 7 -9.043 5 -9 3 C-2.25 0 -2.25 0 0 0 Z " fill="#AEADAC" transform="translate(418,749)"/>
<path d="M0 0 C1.52 1.145 1.52 1.145 3 3 C3.074 5.699 3.074 5.699 2.688 8.688 C2.568 9.681 2.448 10.675 2.324 11.699 C2.217 12.458 2.11 13.218 2 14 C1.67 14 1.34 14 1 14 C1 16.31 1 18.62 1 21 C2.98 21.33 4.96 21.66 7 22 C7 22.33 7 22.66 7 23 C4.69 22.67 2.38 22.34 0 22 C-0.155 21.041 -0.155 21.041 -0.312 20.062 C-0.539 19.382 -0.766 18.701 -1 18 C-1.99 17.67 -2.98 17.34 -4 17 C-3.34 17 -2.68 17 -2 17 C-2.126 16.112 -2.126 16.112 -2.254 15.207 C-2.809 10.901 -3.232 7.244 -2 3 C-1.34 3 -0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#838483" transform="translate(292,738)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C3.221 4.627 3.282 7.009 3.188 9.625 C3.174 10.331 3.16 11.038 3.146 11.766 C3.111 13.511 3.057 15.255 3 17 C1.68 16.67 0.36 16.34 -1 16 C-2.376 10.152 -2.595 5.514 0 0 Z " fill="#959595" transform="translate(438,888)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 8.25 2 16.5 2 25 C-0.475 24.01 -0.475 24.01 -3 23 C-2.125 18.25 -2.125 18.25 -1 16 C-0.768 13.287 -0.581 10.592 -0.438 7.875 C-0.394 7.121 -0.351 6.367 -0.307 5.59 C-0.201 3.727 -0.1 1.863 0 0 Z " fill="#ADAEAE" transform="translate(432,655)"/>
<path d="M0 0 C2.946 0.151 4.531 0.612 6.812 2.5 C9.085 7.284 8.644 12.144 6.938 17.062 C5 19 5 19 1.562 19.562 C-2 19 -2 19 -4.438 17.562 C-6.738 13.79 -6.331 10.309 -6 6 C-5.67 6 -5.34 6 -5 6 C-4.914 6.626 -4.827 7.253 -4.738 7.898 C-4.314 10.364 -3.844 12.644 -3 15 C-0.064 16.882 1.53 17 5 17 C5.223 15.419 5.428 13.835 5.625 12.25 C5.741 11.368 5.857 10.487 5.977 9.578 C6.003 6.721 5.647 5.323 4 3 C0.941 2.184 0.941 2.184 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#858481" transform="translate(523,166)"/>
<path d="M0 0 C3.438 0.562 3.438 0.562 5.375 2.25 C6.586 4.885 6.676 6.695 6.438 9.562 C6.035 8.665 6.035 8.665 5.625 7.75 C4.396 5.487 3.547 4.047 1.438 2.562 C-1.217 2.793 -1.217 2.793 -3.562 3.562 C-5.587 9.057 -5.587 9.057 -4.562 14.562 C-2.275 15.706 -0.97 15.666 1.562 15.625 C2.841 15.604 4.12 15.584 5.438 15.562 C3.575 18.046 2.619 18.532 -0.5 19.062 C-3.562 18.562 -3.562 18.562 -5.938 16.562 C-8.021 12.716 -8.4 9.845 -7.562 5.562 C-5.471 1.547 -4.504 0.711 0 0 Z " fill="#B9B9B8" transform="translate(378.5625,741.4375)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C2.99 3 3.98 3 5 3 C1.37 6.3 -2.26 9.6 -6 13 C-6.66 12.34 -7.32 11.68 -8 11 C-8.99 11 -9.98 11 -11 11 C-7.37 7.37 -3.74 3.74 0 0 Z " fill="#BEBEBE" transform="translate(599,663)"/>
<path d="M0 0 C1.875 0.125 1.875 0.125 4 1 C5.812 3.125 5.812 3.125 7 6 C6.688 9.812 6.688 9.812 6 13 C3.03 12.505 3.03 12.505 0 12 C-0.194 10.188 -0.38 8.376 -0.562 6.562 C-0.667 5.553 -0.771 4.544 -0.879 3.504 C-0.919 2.678 -0.959 1.851 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BABAB9" transform="translate(539,646)"/>
<path d="M0 0 C5.94 0 11.88 0 18 0 C18 0.33 18 0.66 18 1 C12.39 1 6.78 1 1 1 C1.66 2.32 2.32 3.64 3 5 C8.94 5 14.88 5 21 5 C20.01 5.495 20.01 5.495 19 6 C18.278 7.644 17.606 9.311 17 11 C16.34 10.67 15.68 10.34 15 10 C15.66 9.01 16.32 8.02 17 7 C16.024 7.119 15.048 7.237 14.043 7.359 C12.135 7.553 12.135 7.553 10.188 7.75 C8.291 7.959 8.291 7.959 6.355 8.172 C3 8 3 8 1.051 6.266 C0 4 0 4 0 0 Z " fill="#898784" transform="translate(489,177)"/>
<path d="M0 0 C6.773 -0.56 11.749 -0.515 17.352 3.59 C22.477 7.975 24.183 12.68 26 19 C25.021 20.021 24.02 21.021 23 22 C22.67 22 22.34 22 22 22 C22.082 20.824 22.165 19.649 22.25 18.438 C22.233 13.739 20.941 10.635 18 7 C12.85 2.236 6.713 1.826 0 1 C0 0.67 0 0.34 0 0 Z " fill="#6A403C" transform="translate(554,450)"/>
<path d="M0 0 C1.682 2.523 2.699 4.6 3.812 7.375 C5.676 11.52 7.203 14.311 11 17 C14.947 18.305 18.925 19.198 23 20 C24.423 20.286 24.423 20.286 25.875 20.578 C26.576 20.717 27.277 20.857 28 21 C28 21.33 28 21.66 28 22 C20.602 22.498 14.247 22.363 8 18 C3.69 13.57 0.155 8.494 -0.125 2.188 C-0.063 1.105 -0.063 1.105 0 0 Z " fill="#452D2C" transform="translate(275,352)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.691 2.887 0.381 3.774 0.062 4.688 C-1.53 9.653 -1.63 13.363 0.25 18.25 C2.026 21.041 3.209 22.297 6 24 C8.451 24.398 8.451 24.398 11 24.25 C11.846 24.235 12.691 24.219 13.562 24.203 C15.819 24.015 17.829 23.623 20 23 C17.679 25.759 16.133 26.437 12.562 26.75 C11.801 26.827 11.039 26.905 10.254 26.984 C6.908 27.008 4.851 26.777 2.332 24.477 C1.831 23.866 1.329 23.255 0.812 22.625 C0.303 22.019 -0.206 21.413 -0.73 20.789 C-3.845 16.401 -3.479 11.151 -3 6 C-1.5 2.375 -1.5 2.375 0 0 Z " fill="#6D6D6B" transform="translate(475,657)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C4 3.66 4 4.32 4 5 C3.34 5 2.68 5 2 5 C2.206 6.217 2.413 7.434 2.625 8.688 C3.1 12.348 2.766 14.676 1 18 C0.258 17.814 -0.485 17.629 -1.25 17.438 C-3.895 16.82 -3.895 16.82 -6 17.562 C-8.799 18.175 -10.467 17.196 -13 16 C-12.67 15.34 -12.34 14.68 -12 14 C-10.391 14.278 -10.391 14.278 -8.75 14.562 C-4.928 15.232 -4.928 15.232 -1 14 C-0.67 9.38 -0.34 4.76 0 0 Z " fill="#8A8A88" transform="translate(661,744)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-1 4 -1 4 -4.062 4.938 C-7.276 5.771 -7.276 5.771 -9 9 C-9 8.01 -9 7.02 -9 6 C-9.55 6.162 -10.101 6.325 -10.668 6.492 C-17.26 7.928 -24.41 7.172 -31 6 C-31 5.67 -31 5.34 -31 5 C-29.744 4.963 -28.489 4.925 -27.195 4.887 C-13.185 4.436 -13.185 4.436 0 0 Z " fill="#53504E" transform="translate(536,229)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.088 3.166 1.141 6.333 1.188 9.5 C1.213 10.387 1.238 11.274 1.264 12.188 C1.33 18.183 0.441 22.515 -2 28 C-2.33 28 -2.66 28 -3 28 C-3.159 21.453 -2.61 15.384 -1.5 8.938 C-1.357 8.072 -1.214 7.206 -1.066 6.314 C-0.717 4.209 -0.36 2.104 0 0 Z " fill="#CBCAC9" transform="translate(572,164)"/>
<path d="M0 0 C9.681 0.215 17.723 7.31 24.688 13.5 C26.182 15.208 27.075 16.936 28 19 C27.34 19.33 26.68 19.66 26 20 C25.416 19.256 25.416 19.256 24.82 18.496 C18.01 10.226 9.949 4.869 0 1 C0 0.67 0 0.34 0 0 Z " fill="#464544" transform="translate(534,115)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.535 1.244 7.052 2.496 7.562 3.75 C7.853 4.446 8.143 5.142 8.441 5.859 C9.078 8.3 8.802 9.64 8 12 C7.67 11.34 7.34 10.68 7 10 C6.01 10.33 5.02 10.66 4 11 C3.329 9.544 2.663 8.085 2 6.625 C1.629 5.813 1.258 5.001 0.875 4.164 C0 2 0 2 0 0 Z " fill="#BABAB9" transform="translate(539,163)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C5.65 1.34 7.3 0.68 9 0 C9.99 0.99 10.98 1.98 12 3 C11.34 4.32 10.68 5.64 10 7 C9.01 7 8.02 7 7 7 C7 6.34 7 5.68 7 5 C6.01 5 5.02 5 4 5 C4 8.3 4 11.6 4 15 C3.67 15 3.34 15 3 15 C2.927 13.935 2.927 13.935 2.852 12.848 C2.739 11.469 2.739 11.469 2.625 10.062 C2.555 9.146 2.486 8.229 2.414 7.285 C2.277 6.531 2.141 5.777 2 5 C1.34 4.67 0.68 4.34 0 4 C0 2.667 0 1.333 0 0 Z " fill="#979796" transform="translate(566,888)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.183 1.219 -2.367 1.438 -3.586 1.664 C-11.914 3.302 -19.109 4.81 -26.152 9.828 C-28 11 -28 11 -31 11 C-22.672 1.631 -12.096 -0.446 0 0 Z " fill="#573431" transform="translate(556,429)"/>
<path d="M0 0 C9.247 1.267 17.75 9.484 24 16.125 C25.28 18.526 24.756 19.471 24 22 C23.49 21.256 23.49 21.256 22.969 20.496 C16.704 11.716 9.844 6.377 0 2 C0 1.34 0 0.68 0 0 Z " fill="#3C3C3C" transform="translate(535,120)"/>
<path d="M0 0 C2.622 2.792 2.46 4.893 2.609 8.668 C2.666 9.847 2.723 11.027 2.781 12.242 C2.82 13.598 2.82 13.598 2.859 14.98 C1.539 15.31 0.219 15.64 -1.141 15.98 C-1.141 11.69 -1.141 7.4 -1.141 2.98 C-2.791 2.98 -4.441 2.98 -6.141 2.98 C-5.811 1.99 -5.481 1 -5.141 -0.02 C-2.141 -1.02 -2.141 -1.02 0 0 Z " fill="#979796" transform="translate(495.140625,889.01953125)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.091 3.057 0.325 4.723 -2.125 6.812 C-6.148 8.474 -8.646 8.484 -13 8 C-15.5 6 -15.5 6 -17 4 C-12.25 2.875 -12.25 2.875 -10 4 C-10 4.66 -10 5.32 -10 6 C-4.449 3.831 -4.449 3.831 0 0 Z " fill="#ADADAD" transform="translate(495,673)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 7.27 2 13.54 2 20 C1.01 20 0.02 20 -1 20 C-1.029 16.854 -1.047 13.708 -1.062 10.562 C-1.071 9.665 -1.079 8.767 -1.088 7.842 C-1.091 6.988 -1.094 6.134 -1.098 5.254 C-1.106 4.068 -1.106 4.068 -1.114 2.858 C-1 1 -1 1 0 0 Z " fill="#AEAFAF" transform="translate(416,654)"/>
<path d="M0 0 C3 1 3 1 5 4 C5.99 4 6.98 4 8 4 C8.667 4.694 8.667 4.694 9.348 5.402 C11.545 7.527 13.483 7.861 16.438 8.562 C19.89 9.394 22.883 10.283 26 12 C15.047 12.823 7.93 8.937 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#613E3B" transform="translate(342,490)"/>
<path d="M0 0 C1.266 3.867 1.112 7.732 1.062 11.75 C1.058 12.447 1.053 13.145 1.049 13.863 C1.037 15.576 1.019 17.288 1 19 C0.34 18.67 -0.32 18.34 -1 18 C-1 13.05 -1 8.1 -1 3 C-4.3 3.33 -7.6 3.66 -11 4 C-8.073 -1.855 -5.9 -1.18 0 0 Z " fill="#AAAAAA" transform="translate(349,741)"/>
<path d="M0 0 C5.549 -0.617 5.549 -0.617 8 0 C10.676 2.418 10.986 3.832 11.293 7.453 C11.258 8.706 11.223 9.959 11.188 11.25 C11.16 12.513 11.133 13.777 11.105 15.078 C11.071 16.042 11.036 17.007 11 18 C10.34 18 9.68 18 9 18 C8.939 17.001 8.879 16.002 8.816 14.973 C8.691 13.037 8.691 13.037 8.562 11.062 C8.481 9.775 8.4 8.487 8.316 7.16 C8.351 4.14 8.351 4.14 7 3 C5.481 2.928 3.958 2.916 2.438 2.938 C1.198 2.951 1.198 2.951 -0.066 2.965 C-1.024 2.982 -1.024 2.982 -2 3 C-1.34 2.01 -0.68 1.02 0 0 Z " fill="#BABAB9" transform="translate(561,742)"/>
<path d="M0 0 C1.108 3.325 0.845 5.622 0 9 C0.66 9.33 1.32 9.66 2 10 C-0.354 11.429 -1.48 12.087 -4.25 11.625 C-4.827 11.419 -5.405 11.212 -6 11 C-5.67 10.01 -5.34 9.02 -5 8 C-5 5.379 -5.313 3.491 -6 1 C-5.01 1 -4.02 1 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#828280" transform="translate(650,893)"/>
<path d="M0 0 C7.969 -0.379 14.622 0.197 20.938 5.438 C22.676 7.598 23.959 9.444 25 12 C24.113 11.196 23.226 10.391 22.312 9.562 C19.617 7.296 17.579 6.316 14 6 C14 5.34 14 4.68 14 4 C13.157 3.879 12.314 3.758 11.445 3.633 C10.35 3.465 9.254 3.298 8.125 3.125 C6.489 2.881 6.489 2.881 4.82 2.633 C2 2 2 2 0 0 Z " fill="#4E312F" transform="translate(450,449)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.669 3.062 1.399 4.601 -0.812 6.812 C-3 8 -3 8 -7 8 C-7.66 7.34 -8.32 6.68 -9 6 C-11.31 6.66 -13.62 7.32 -16 8 C-16.66 6.68 -17.32 5.36 -18 4 C-12.39 4 -6.78 4 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#AEADAB" transform="translate(508,174)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-1.32 2.67 -2.64 2.34 -4 2 C-4.33 2.99 -4.66 3.98 -5 5 C-5.33 5.66 -5.66 6.32 -6 7 C-6 8.32 -6 9.64 -6 11 C-5.34 11.33 -4.68 11.66 -4 12 C-7.875 11.125 -7.875 11.125 -9 10 C-9.462 2.846 -9.462 2.846 -7.875 -0.062 C-5.065 -1.468 -2.968 -0.725 0 0 Z " fill="#979896" transform="translate(634,891)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.078 1.965 0.078 1.965 -0.863 1.93 C-1.672 1.912 -2.48 1.894 -3.312 1.875 C-4.513 1.84 -4.513 1.84 -5.738 1.805 C-8.284 1.842 -8.284 1.842 -11 4 C-11.365 6.531 -11.365 6.531 -11.188 9.375 C-11.147 10.81 -11.147 10.81 -11.105 12.273 C-11.053 13.623 -11.053 13.623 -11 15 C-11 16.32 -11 17.64 -11 19 C-13 18 -13 18 -13.625 16.438 C-14.368 11.607 -14.699 7.608 -12.75 3.125 C-8.897 -1.553 -5.801 -1.313 0 0 Z " fill="#888886" transform="translate(705,741)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.11 0.566 1.219 1.132 1.332 1.715 C1.491 2.448 1.649 3.182 1.812 3.938 C1.963 4.668 2.114 5.399 2.27 6.152 C2.511 6.762 2.752 7.372 3 8 C3.99 8.33 4.98 8.66 6 9 C7 12 7 12 7 13 C7.66 13.33 8.32 13.66 9 14 C9.33 14.99 9.66 15.98 10 17 C10.66 17.33 11.32 17.66 12 18 C12 18.66 12 19.32 12 20 C7.179 18.866 5.039 16.107 2.297 12.234 C0.327 8.841 -0.399 5.876 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#6B423F" transform="translate(365,351)"/>
<path d="M0 0 C-0.931 2.328 -1.654 3.744 -3.688 5.25 C-6.835 6.271 -9.715 6.126 -13 6 C-13 5.67 -13 5.34 -13 5 C-11.35 5 -9.7 5 -8 5 C-8 4.34 -8 3.68 -8 3 C-9.65 3.33 -11.3 3.66 -13 4 C-13.33 3.01 -13.66 2.02 -14 1 C-9.277 0.229 -4.785 -0.098 0 0 Z " fill="#BFBDBC" transform="translate(506,184)"/>
<path d="M0 0 C2 1.562 2 1.562 4 4 C4.188 7.375 4.188 7.375 4 11 C4.292 13.006 4.612 15.01 5 17 C4.34 17 3.68 17 3 17 C3 18.65 3 20.3 3 22 C2.67 22 2.34 22 2 22 C1.963 21.013 1.925 20.025 1.887 19.008 C1.821 17.726 1.755 16.445 1.688 15.125 C1.629 13.849 1.571 12.573 1.512 11.258 C1.343 10.183 1.174 9.108 1 8 C0.01 7.34 -0.98 6.68 -2 6 C-2 4.68 -2 3.36 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#80807F" transform="translate(439,882)"/>
<path d="M0 0 C1.437 -0.081 2.874 -0.139 4.312 -0.188 C5.513 -0.24 5.513 -0.24 6.738 -0.293 C9.479 0.062 10.292 0.894 12 3 C9 4 9 4 8 4 C8 5.32 8 6.64 8 8 C7.34 8 6.68 8 6 8 C6 7.01 6 6.02 6 5 C5.67 5.66 5.34 6.32 5 7 C4.01 7 3.02 7 2 7 C1.67 6.01 1.34 5.02 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#A1A1A0" transform="translate(593,646)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C4.33 1.34 4.66 0.68 5 0 C5.99 0.33 6.98 0.66 8 1 C8 1.66 8 2.32 8 3 C9.65 2.67 11.3 2.34 13 2 C13 2.66 13 3.32 13 4 C14.98 3.67 16.96 3.34 19 3 C16.706 5.703 15.136 6.48 11.625 6.875 C8.466 7.127 7.068 7.027 4.062 5.812 C1.971 3.975 0.981 2.585 0 0 Z " fill="#908F8C" transform="translate(485,185)"/>
<path d="M0 0 C7.75 1.875 7.75 1.875 10 3 C9.125 6.875 9.125 6.875 8 8 C6 8.041 4 8.043 2 8 C2 7.34 2 6.68 2 6 C0.02 6.99 0.02 6.99 -2 8 C-2.33 7.01 -2.66 6.02 -3 5 C-1.02 4.67 0.96 4.34 3 4 C3 3.34 3 2.68 3 2 C2.01 1.67 1.02 1.34 0 1 C0 0.67 0 0.34 0 0 Z M4 3 C4.99 4.485 4.99 4.485 6 6 C6.66 6 7.32 6 8 6 C8 5.34 8 4.68 8 4 C6.68 3.67 5.36 3.34 4 3 Z " fill="#8F8E8C" transform="translate(531,750)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.497 4.668 -1.637 6.689 -4.625 9.25 C-5.442 9.956 -6.26 10.663 -7.102 11.391 C-8.041 12.187 -8.041 12.187 -9 13 C-10.32 12.34 -11.64 11.68 -13 11 C-12.481 10.625 -11.961 10.25 -11.426 9.863 C-7.277 6.819 -3.527 3.747 0 0 Z " fill="#C3C1C0" transform="translate(556,211)"/>
<path d="M0 0 C-1.593 0.654 -1.593 0.654 -3.219 1.32 C-13.508 5.594 -13.508 5.594 -22.617 11.934 C-24 13 -24 13 -26 13 C-26 12.34 -26 11.68 -26 11 C-23.863 9.418 -21.746 7.975 -19.5 6.562 C-18.86 6.157 -18.22 5.752 -17.561 5.334 C-5.986 -1.856 -5.986 -1.856 0 0 Z " fill="#3D3E3D" transform="translate(496,114)"/>
<path d="M0 0 C0.699 0.3 1.397 0.601 2.117 0.91 C3.482 1.481 3.482 1.481 4.875 2.062 C5.78 2.445 6.685 2.828 7.617 3.223 C9.981 4.302 9.981 4.302 12 3 C11 6 11 6 10 7 C11.32 7 12.64 7 14 7 C14.33 8.32 14.66 9.64 15 11 C11.25 12.125 11.25 12.125 9 11 C9 10.34 9 9.68 9 9 C8.34 9 7.68 9 7 9 C7 7.35 7 5.7 7 4 C4.69 3.34 2.38 2.68 0 2 C0 1.34 0 0.68 0 0 Z " fill="#888886" transform="translate(594,893)"/>
<path d="M0 0 C1.114 0.155 1.114 0.155 2.25 0.312 C2.25 0.972 2.25 1.633 2.25 2.312 C0.6 2.312 -1.05 2.312 -2.75 2.312 C-2.42 3.633 -2.09 4.952 -1.75 6.312 C-2.41 6.312 -3.07 6.312 -3.75 6.312 C-3.42 7.962 -3.09 9.612 -2.75 11.312 C-0.77 11.312 1.21 11.312 3.25 11.312 C2.26 12.798 2.26 12.798 1.25 14.312 C-3.5 13.562 -3.5 13.562 -5.75 11.312 C-6.625 8.5 -6.625 8.5 -6.75 5.312 C-4.834 1.923 -3.951 0.449 0 0 Z " fill="#A0A1A0" transform="translate(408.75,889.6875)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-2.488 8.373 -7.108 12.843 -13.312 14.688 C-16.901 15.105 -20.388 15.061 -24 15 C-24 14.67 -24 14.34 -24 14 C-23.143 13.841 -22.286 13.683 -21.402 13.52 C-12.906 11.786 -6.654 10.131 -1.562 2.688 C-1.047 1.801 -0.531 0.914 0 0 Z " fill="#452D2B" transform="translate(476,486)"/>
<path d="M0 0 C7.095 -0.533 12.906 0.073 19 4 C19.99 5.485 19.99 5.485 21 7 C15.346 7.346 11.97 6.434 7 4 C7 3.34 7 2.68 7 2 C4.69 1.67 2.38 1.34 0 1 C0 0.67 0 0.34 0 0 Z M14 4 C14.66 4.66 15.32 5.32 16 6 C16 5.34 16 4.68 16 4 C15.34 4 14.68 4 14 4 Z " fill="#744640" transform="translate(726,321)"/>
<path d="M0 0 C2.562 0.188 2.562 0.188 4.562 2.188 C4.812 5.188 4.812 5.188 4.562 8.188 C2.562 10.188 2.562 10.188 0.062 10.5 C-1.175 10.345 -1.175 10.345 -2.438 10.188 C-4.438 7.188 -4.438 7.188 -4.125 4.062 C-3.213 0.247 -3.213 0.247 0 0 Z M-2.438 1.188 C-1.777 3.168 -1.118 5.148 -0.438 7.188 C0.882 6.528 2.202 5.867 3.562 5.188 C2.902 3.867 2.243 2.548 1.562 1.188 C0.243 1.188 -1.077 1.188 -2.438 1.188 Z M-2.438 6.188 C-1.438 8.188 -1.438 8.188 -1.438 8.188 Z M1.562 6.188 C2.562 8.188 2.562 8.188 2.562 8.188 Z " fill="#838381" transform="translate(423.4375,891.8125)"/>
<path d="M0 0 C1.728 0.068 3.458 0.085 5.188 0.062 C6.089 0.053 6.99 0.044 7.918 0.035 C8.949 0.018 8.949 0.018 10 0 C9.67 1.32 9.34 2.64 9 4 C3.06 3.67 -2.88 3.34 -9 3 C-7 0 -7 0 -5.062 -0.75 C-3 -1 -3 -1 0 0 Z " fill="#888786" transform="translate(537,674)"/>
<path d="M0 0 C2.825 4.238 2.533 8.07 2.75 13.062 C2.805 13.986 2.861 14.91 2.918 15.861 C2.961 16.744 3.003 17.626 3.047 18.535 C3.087 19.343 3.126 20.152 3.167 20.985 C3 23 3 23 1 25 C0.876 24.196 0.753 23.391 0.625 22.562 C0.419 21.717 0.212 20.871 0 20 C-0.66 19.67 -1.32 19.34 -2 19 C-1.67 18.34 -1.34 17.68 -1 17 C-0.767 14.121 -0.58 11.259 -0.438 8.375 C-0.394 7.573 -0.351 6.771 -0.307 5.945 C-0.2 3.964 -0.1 1.982 0 0 Z " fill="#693D39" transform="translate(684,338)"/>
<path d="M0 0 C2.68 2.68 3.165 5.424 4.219 9.004 C4.816 11.187 4.816 11.187 7 12 C8.98 6.555 8.98 6.555 11 1 C11.33 1 11.66 1 12 1 C12.639 6.115 10.405 9.673 8 14 C5.688 14 5.688 14 3 13 C0.601 8.989 -0.437 4.661 0 0 Z " fill="#828380" transform="translate(355,747)"/>
<path d="M0 0 C0.754 0.1 1.508 0.2 2.285 0.303 C4.149 0.55 6.012 0.806 7.875 1.062 C7.215 2.712 6.555 4.362 5.875 6.062 C4.555 5.732 3.235 5.403 1.875 5.062 C1.875 4.072 1.875 3.082 1.875 2.062 C-3.294 2.468 -3.294 2.468 -8.125 4.062 C-8.125 3.072 -8.125 2.082 -8.125 1.062 C-8.785 0.732 -9.445 0.403 -10.125 0.062 C-6.879 -1.56 -3.487 -0.485 0 0 Z " fill="#8A8988" transform="translate(622.125,672.9375)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3 2.32 3 3 3 C3 9.93 3 16.86 3 24 C2.34 24 1.68 24 1 24 C1.023 23.203 1.046 22.407 1.07 21.586 C1.167 15.908 1.088 11.35 -1 6 C-1.188 3.188 -1.188 3.188 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BFBFBE" transform="translate(464,658)"/>
<path d="M0 0 C-2.058 1.677 -3.841 2.938 -6.324 3.906 C-10.059 5.433 -13.297 7.599 -16.695 9.758 C-19 11 -19 11 -22 11 C-16.066 5.244 -8.882 -1.146 0 0 Z " fill="#E98675" transform="translate(720,303)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.061 0.748 1.121 1.495 1.184 2.266 C1.267 3.25 1.351 4.235 1.438 5.25 C1.519 6.225 1.6 7.199 1.684 8.203 C1.918 10.921 1.918 10.921 2.598 13.172 C3 15 3 15 2 18 C-1.161 16.63 -1.993 16.011 -4 13 C-4.125 10.312 -4.125 10.312 -4 8 C-3.34 9.32 -2.68 10.64 -2 12 C-1.34 12 -0.68 12 0 12 C-0.66 11.34 -1.32 10.68 -2 10 C-1.758 7.617 -1.758 7.617 -1.125 4.875 C-0.921 3.965 -0.718 3.055 -0.508 2.117 C-0.34 1.419 -0.173 0.72 0 0 Z " fill="#C2C1C0" transform="translate(516,170)"/>
<path d="M0 0 C-1.114 0.392 -2.228 0.784 -3.375 1.188 C-6.32 2.425 -6.859 2.736 -8.438 5.688 C-8.623 6.451 -8.809 7.214 -9 8 C-8.34 8 -7.68 8 -7 8 C-7 8.66 -7 9.32 -7 10 C-7.66 10 -8.32 10 -9 10 C-8.794 10.784 -8.587 11.567 -8.375 12.375 C-8.251 13.241 -8.127 14.107 -8 15 C-8.66 15.66 -9.32 16.32 -10 17 C-11.676 11.973 -11.809 6.901 -9.75 2.062 C-8 0 -8 0 -6.062 -0.875 C-3.821 -1.011 -2.13 -0.685 0 0 Z " fill="#898887" transform="translate(279,738)"/>
<path d="M0 0 C3.113 0 5.108 0.174 8 1 C6.788 1.52 6.788 1.52 5.551 2.051 C2.656 3.823 2.656 3.823 2.293 7.355 C2.258 8.62 2.223 9.885 2.188 11.188 C2.14 12.46 2.092 13.732 2.043 15.043 C2.029 16.019 2.015 16.995 2 18 C1.34 18 0.68 18 0 18 C0 12.06 0 6.12 0 0 Z " fill="#B6B6B5" transform="translate(519,742)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.634 4.542 0.091 7.926 -1.625 11.312 C-2.071 12.196 -2.517 13.079 -2.977 13.988 C-3.314 14.652 -3.652 15.316 -4 16 C-4.33 16 -4.66 16 -5 16 C-5.875 13.625 -5.875 13.625 -6 10 C-4.292 6.467 -2.208 3.239 0 0 Z " fill="#B9B8B7" transform="translate(462,136)"/>
<path d="M0 0 C2.125 0.938 2.125 0.938 4 2 C3.67 2.66 3.34 3.32 3 4 C3.66 4.33 4.32 4.66 5 5 C5.414 7.066 5.414 7.066 5.625 9.562 C5.7 10.389 5.775 11.215 5.852 12.066 C5.901 12.704 5.95 13.343 6 14 C5.34 14 4.68 14 4 14 C2.515 8.555 2.515 8.555 1 3 C-0.98 7.455 -0.98 7.455 -3 12 C-3.66 10.68 -4.32 9.36 -5 8 C-4.34 7.67 -3.68 7.34 -3 7 C-2.835 6.01 -2.67 5.02 -2.5 4 C-2.335 3.01 -2.17 2.02 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#91918E" transform="translate(635,740)"/>
<path d="M0 0 C3.465 1.485 3.465 1.485 7 3 C6.01 3.495 6.01 3.495 5 4 C5.33 4.99 5.66 5.98 6 7 C5.67 7.66 5.34 8.32 5 9 C3.35 9 1.7 9 0 9 C-0.845 5.622 -1.108 3.325 0 0 Z " fill="#898887" transform="translate(684,752)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 5.94 2 11.88 2 18 C0.68 17.34 -0.64 16.68 -2 16 C-1.34 10.72 -0.68 5.44 0 0 Z " fill="#ACACAC" transform="translate(303,742)"/>
<path d="M0 0 C4.75 -0.125 4.75 -0.125 7 1 C7.99 0.67 8.98 0.34 10 0 C10.66 1.32 11.32 2.64 12 4 C11.34 4.66 10.68 5.32 10 6 C9.112 7.965 9.112 7.965 8.375 10.125 C8.115 10.849 7.854 11.574 7.586 12.32 C7.393 12.875 7.199 13.429 7 14 C6.01 14 5.02 14 4 14 C4.495 13.299 4.99 12.597 5.5 11.875 C7.194 8.629 7.252 6.607 7 3 C4.69 2.34 2.38 1.68 0 1 C0 0.67 0 0.34 0 0 Z " fill="#BFBEBD" transform="translate(596,648)"/>
<path d="M0 0 C7.538 -0.466 13.291 2.979 19.043 7.664 C20.688 9.25 20.688 9.25 23 12 C23 12.66 23 13.32 23 14 C21 14 21 14 18.688 11.75 C17.801 10.842 16.914 9.935 16 9 C11.419 4.554 5.947 2.958 0 1 C0 0.67 0 0.34 0 0 Z " fill="#E78576" transform="translate(376,432)"/>
<path d="M0 0 C5.94 0 11.88 0 18 0 C18 0.33 18 0.66 18 1 C16.929 1.061 15.858 1.121 14.754 1.184 C13.357 1.268 11.96 1.353 10.562 1.438 C9.502 1.496 9.502 1.496 8.42 1.557 C4.913 1.476 4.913 1.476 2 3 C1.843 4.486 1.749 5.979 1.684 7.473 C1.642 8.372 1.6 9.271 1.557 10.197 C1.517 11.143 1.478 12.088 1.438 13.062 C1.394 14.012 1.351 14.961 1.307 15.939 C1.2 18.293 1.098 20.646 1 23 C0.67 23 0.34 23 0 23 C0 15.41 0 7.82 0 0 Z " fill="#D7786A" transform="translate(672,432)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.73 2.848 3.73 2.848 4.188 5.062 C4.346 5.796 4.505 6.529 4.668 7.285 C4.778 7.851 4.887 8.417 5 9 C4.34 9 3.68 9 3 9 C2.67 10.32 2.34 11.64 2 13 C1.34 13 0.68 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#B0AFAE" transform="translate(532,169)"/>
<path d="M0 0 C3.453 1.151 3.984 2.107 6 5 C6.66 5.66 7.32 6.32 8 7 C7.977 9.277 7.977 9.277 7.625 11.938 C7.278 14.679 7 17.232 7 20 C6.689 21.671 6.359 23.339 6 25 C5.67 25 5.34 25 5 25 C4.974 23.949 4.948 22.899 4.922 21.816 C4.631 13.818 4.211 7.051 0 0 Z " fill="#848482" transform="translate(575,647)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C3.356 2.468 3.356 2.468 6.062 2.625 C7.441 2.737 7.441 2.737 8.848 2.852 C9.558 2.901 10.268 2.95 11 3 C11 3.33 11 3.66 11 4 C9.733 4.061 8.466 4.121 7.16 4.184 C5.461 4.268 3.762 4.353 2.062 4.438 C0.816 4.496 0.816 4.496 -0.455 4.557 C-6.004 4.837 -11.484 5.338 -17 6 C-12.025 2.683 -5.964 1.615 0 2 C0 1.34 0 0.68 0 0 Z " fill="#E58372" transform="translate(556,428)"/>
<path d="M0 0 C5.439 1.768 5.439 1.768 7.934 2.801 C12.542 4.696 16.017 5.311 21 5 C21 5.66 21 6.32 21 7 C16.607 8.281 13.754 7.63 9.438 6.25 C7.765 5.732 7.765 5.732 6.059 5.203 C3.244 4.096 1.288 2.946 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#595756" transform="translate(483,223)"/>
<path d="M0 0 C2.97 0 5.94 0 9 0 C9.043 2 9.041 4 9 6 C8 7 8 7 6.152 7.098 C4.102 7.065 2.051 7.033 0 7 C0 4.69 0 2.38 0 0 Z M2 1 C2 2.65 2 4.3 2 6 C3.65 6 5.3 6 7 6 C7.33 5.01 7.66 4.02 8 3 C7.34 2.34 6.68 1.68 6 1 C4.68 1 3.36 1 2 1 Z " fill="#7C7C7B" transform="translate(374,887)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.33 4.62 2.66 9.24 3 14 C4.32 14 5.64 14 7 14 C7 14.66 7 15.32 7 16 C4.625 16.125 4.625 16.125 2 16 C0 14 0 14 -0.195 10.742 C-0.172 9.466 -0.149 8.19 -0.125 6.875 C-0.107 5.594 -0.089 4.312 -0.07 2.992 C-0.047 2.005 -0.024 1.017 0 0 Z " fill="#AEADAD" transform="translate(311,744)"/>
<path d="M0 0 C2 2 2 2 2.195 5.695 C2.182 7.172 2.158 8.649 2.125 10.125 C2.116 10.879 2.107 11.633 2.098 12.41 C2.074 14.274 2.038 16.137 2 18 C1.67 18 1.34 18 1 18 C0.939 17.001 0.879 16.002 0.816 14.973 C0.691 13.037 0.691 13.037 0.562 11.062 C0.481 9.775 0.4 8.487 0.316 7.16 C0.351 4.14 0.351 4.14 -1 3 C-2.691 3.09 -4.38 3.246 -6.062 3.438 C-6.982 3.539 -7.901 3.641 -8.848 3.746 C-9.913 3.872 -9.913 3.872 -11 4 C-11 3.34 -11 2.68 -11 2 C-7.365 -0.326 -4.24 -0.314 0 0 Z " fill="#BBBBBB" transform="translate(659,742)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.061 0.999 2.121 1.998 2.184 3.027 C2.267 4.318 2.351 5.608 2.438 6.938 C2.519 8.225 2.6 9.513 2.684 10.84 C2.649 13.86 2.649 13.86 4 15 C6 15.041 8 15.043 10 15 C10 15.66 10 16.32 10 17 C7.36 17 4.72 17 2 17 C-0.26 13.405 -0.209 10.434 -0.125 6.25 C-0.098 4.494 -0.098 4.494 -0.07 2.703 C-0.047 1.811 -0.024 0.919 0 0 Z " fill="#B5B4B4" transform="translate(390,742)"/>
<path d="M0 0 C2 2 2 2 2.195 5.477 C2.182 6.86 2.158 8.242 2.125 9.625 C2.116 10.331 2.107 11.038 2.098 11.766 C2.074 13.511 2.038 15.255 2 17 C1.67 17 1.34 17 1 17 C0.963 16.241 0.925 15.481 0.887 14.699 C0.821 13.705 0.755 12.711 0.688 11.688 C0.629 10.701 0.571 9.715 0.512 8.699 C-0.053 5.723 -0.538 4.719 -3 3 C-6.125 2.75 -6.125 2.75 -9 3 C-9 2.34 -9 1.68 -9 1 C-5.987 -0.507 -3.317 -0.178 0 0 Z " fill="#BEBEBD" transform="translate(419,742)"/>
<path d="M0 0 C5.61 0 11.22 0 17 0 C17 1.65 17 3.3 17 5 C11.06 4.67 5.12 4.34 -1 4 C-1 3.67 -1 3.34 -1 3 C4.28 3 9.56 3 15 3 C15 2.34 15 1.68 15 1 C10.05 1 5.1 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C4C3C3" transform="translate(593,677)"/>
<path d="M0 0 C3.63 0 7.26 0 11 0 C9.515 1.485 9.515 1.485 8 3 C6.68 3 5.36 3 4 3 C4 3.66 4 4.32 4 5 C5.98 5 7.96 5 10 5 C10 5.33 10 5.66 10 6 C8.521 6.027 7.042 6.046 5.562 6.062 C4.327 6.08 4.327 6.08 3.066 6.098 C1 6 1 6 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BDBCBC" transform="translate(388,676)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C3.237 5.313 3.439 10.32 3 16 C2.67 16.66 2.34 17.32 2 18 C1.34 18 0.68 18 0 18 C0 12.06 0 6.12 0 0 Z " fill="#ACACAC" transform="translate(463,664)"/>
<path d="M0 0 C3.312 0.938 3.312 0.938 5.312 2.938 C4.983 3.928 4.652 4.918 4.312 5.938 C2.375 5.625 2.375 5.625 0.312 4.938 C-0.017 3.947 -0.348 2.957 -0.688 1.938 C-3.23 2.182 -3.23 2.182 -5.688 3.938 C-6.768 7.216 -7.265 10.518 -7.688 13.938 C-8.017 13.938 -8.348 13.938 -8.688 13.938 C-9.161 9.103 -9.367 5.967 -6.625 1.812 C-3.688 -0.062 -3.688 -0.062 0 0 Z " fill="#7B7B79" transform="translate(487.6875,658.0625)"/>
<path d="M0 0 C-2.857 1.905 -5.65 3.286 -8.75 4.75 C-13.916 7.295 -17.846 10.035 -22 14 C-22 13.01 -22 12.02 -22 11 C-16.532 5.544 -8.15 -1.019 0 0 Z " fill="#52312E" transform="translate(718,302)"/>
<path d="M0 0 C0.976 0.195 1.952 0.389 2.957 0.59 C4.061 0.824 4.061 0.824 5.188 1.062 C4.137 2.613 4.137 2.613 2.188 4.062 C-1.168 3.918 -1.168 3.918 -5 3.25 C-6.272 3.037 -7.545 2.825 -8.855 2.605 C-10.319 2.337 -10.319 2.337 -11.812 2.062 C-11.482 1.403 -11.153 0.743 -10.812 0.062 C-6.938 -1.142 -3.932 -0.805 0 0 Z " fill="#B7B6B5" transform="translate(528.8125,113.9375)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 6.93 1 13.86 1 21 C-3 16 -3 16 -2.75 13.438 C-2.502 12.633 -2.255 11.829 -2 11 C-2 9.68 -2 8.36 -2 7 C-1.34 7 -0.68 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#A6A5A5" transform="translate(412,653)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C4.143 7.429 4.143 7.429 5 11 C4.01 10.67 3.02 10.34 2 10 C2 10.99 2 11.98 2 13 C1.34 13 0.68 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#9B9B9A" transform="translate(537,890)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-2.65 1.67 -4.3 1.34 -6 1 C-6 1.99 -6 2.98 -6 4 C-3.69 4.33 -1.38 4.66 1 5 C0.67 6.98 0.34 8.96 0 11 C-0.33 9.68 -0.66 8.36 -1 7 C-2.176 6.783 -2.176 6.783 -3.375 6.562 C-6 6 -6 6 -8 5 C-7.625 2.562 -7.625 2.562 -7 0 C-4.333 -1.333 -2.833 -0.671 0 0 Z " fill="#A3A3A2" transform="translate(508,891)"/>
<path d="M0 0 C2.64 0.33 5.28 0.66 8 1 C9.165 4.494 9.136 7.337 9 11 C8 12 8 12 5.934 12.098 C5.11 12.086 4.286 12.074 3.438 12.062 C2.611 12.053 1.785 12.044 0.934 12.035 C0.296 12.024 -0.343 12.012 -1 12 C-1 11.67 -1 11.34 -1 11 C1.64 11 4.28 11 7 11 C6.433 6.87 6.433 6.87 5 3 C1.971 2.342 1.971 2.342 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#848483" transform="translate(425,740)"/>
<path d="M0 0 C0.371 0.516 0.742 1.031 1.125 1.562 C3.032 3.363 3.032 3.363 6.188 2.688 C7.116 2.461 8.044 2.234 9 2 C9.33 2.99 9.66 3.98 10 5 C6.911 7.059 6.291 7.239 2.812 7.125 C0.925 7.063 0.925 7.063 -1 7 C-0.67 4.69 -0.34 2.38 0 0 Z " fill="#ACAAA9" transform="translate(585,649)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.098 1.074 1.196 2.148 1.297 3.254 C1.447 4.69 1.598 6.126 1.75 7.562 C1.812 8.267 1.874 8.971 1.938 9.697 C2.53 15.053 4.21 18.412 7 23 C7 23.99 7 24.98 7 26 C3.374 22.874 1.023 19.684 0 15 C-0.243 9.985 -0.174 5.016 0 0 Z " fill="#372322" transform="translate(534,469)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.061 0.687 1.121 1.374 1.184 2.082 C1.267 2.983 1.351 3.884 1.438 4.812 C1.519 5.706 1.6 6.599 1.684 7.52 C1.935 10.127 1.935 10.127 3 13 C2.67 13.66 2.34 14.32 2 15 C2.804 14.979 3.609 14.959 4.438 14.938 C7 15 7 15 8 16 C8.041 17.666 8.043 19.334 8 21 C7.34 20.01 6.68 19.02 6 18 C4.02 17.34 2.04 16.68 0 16 C0 10.72 0 5.44 0 0 Z " fill="#797775" transform="translate(473,168)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C-4.61 7 -10.22 7 -16 7 C-14 5 -14 5 -10.961 4.805 C-9.186 4.839 -9.186 4.839 -7.375 4.875 C-5.592 4.902 -5.592 4.902 -3.773 4.93 C-2.858 4.953 -1.943 4.976 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#C07169" transform="translate(617,385)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C2.645 6.441 1.055 10.218 -2 16 C-2.33 15.34 -2.66 14.68 -3 14 C-3.66 13.67 -4.32 13.34 -5 13 C-3.35 8.71 -1.7 4.42 0 0 Z " fill="#B2B1B0" transform="translate(568,187)"/>
<path d="M0 0 C2.325 2.138 3.822 4.186 5.25 7 C5.606 7.681 5.962 8.361 6.328 9.062 C7 11 7 11 6 14 C4 13 2 12 0 11 C0 9.68 0 8.36 0 7 C0.66 6.67 1.32 6.34 2 6 C1.34 5.01 0.68 4.02 0 3 C0 2.01 0 1.02 0 0 Z M3 8 C4 10 4 10 4 10 Z " fill="#5C5B5A" transform="translate(560,140)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.087 6.739 1.087 6.739 0.312 9.062 C-0.128 11.796 0.841 13.545 2 16 C1.34 16 0.68 16 0 16 C-0.588 14.577 -0.588 14.577 -1.188 13.125 C-3.198 9.659 -4.227 9.02 -8 8 C-8 7.67 -8 7.34 -8 7 C-4.535 6.505 -4.535 6.505 -1 6 C-0.67 4.02 -0.34 2.04 0 0 Z " fill="#ABABAA" transform="translate(383,887)"/>
<path d="M0 0 C0.897 0.309 0.897 0.309 1.812 0.625 C-0.188 3.188 -0.188 3.188 -3.188 5.625 C-6.235 5.945 -8.135 5.426 -11.188 4.625 C-11.848 3.635 -12.507 2.645 -13.188 1.625 C-11.202 1.69 -9.216 1.755 -7.23 1.82 C-4.161 1.527 -3.152 -0.54 0 0 Z " fill="#A7A6A5" transform="translate(514.1875,755.375)"/>
<path d="M0 0 C2.664 0.962 4.754 1.794 6.938 3.625 C9.771 5.514 11.656 5.348 15 5 C17.302 4.102 17.302 4.102 19 3 C17.115 5.722 15.659 7.661 12.688 9.188 C7.289 8.811 3.646 3.668 0 0 Z " fill="#E78475" transform="translate(308,322)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.34 3.64 0.68 6.28 0 9 C-0.99 9 -1.98 9 -3 9 C-4.688 7 -4.688 7 -6 5 C-2.25 1.125 -2.25 1.125 0 0 Z " fill="#A8A8A6" transform="translate(520,163)"/>
<path d="M0 0 C1.145 2.29 1.074 3.531 1 6.062 C1 7.362 1 8.661 1 10 C1.983 11.206 1.983 11.206 3.848 11.098 C5.898 11.065 7.949 11.033 10 11 C10 11.66 10 12.32 10 13 C6.812 13.812 6.812 13.812 3 14 C0.062 11.75 0.062 11.75 -2 9 C-2 8.01 -2 7.02 -2 6 C-2.66 5.67 -3.32 5.34 -4 5 C-3.34 4.67 -2.68 4.34 -2 4 C-0.866 1.983 -0.866 1.983 0 0 Z " fill="#7B7B7A" transform="translate(389,891)"/>
<path d="M0 0 C1.914 0.629 1.914 0.629 4 2 C4.898 4.715 4.898 4.715 5.375 7.938 C5.548 8.998 5.72 10.059 5.898 11.152 C6 14 6 14 4 17 C3.34 16.67 2.68 16.34 2 16 C2 11.38 2 6.76 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#727371" transform="translate(654,888)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.99 0.34 3.98 0 5 C6.93 5.33 13.86 5.66 21 6 C21 6.33 21 6.66 21 7 C13.41 7 5.82 7 -2 7 C-1.34 4.69 -0.68 2.38 0 0 Z " fill="#B8B7B7" transform="translate(587,675)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C1.66 3.33 2.32 3.66 3 4 C3.66 4.33 4.32 4.66 5 5 C5.33 5.99 5.66 6.98 6 8 C5.01 8.33 4.02 8.66 3 9 C2.278 8.196 1.556 7.391 0.812 6.562 C-2.025 3.977 -3.272 3.32 -7 3 C-4.64 0.64 -3.221 0.509 0 0 Z " fill="#AFAFAE" transform="translate(571,645)"/>
<path d="M0 0 C4.192 0.558 6.816 1.938 10.25 4.375 C11.121 4.981 11.993 5.587 12.891 6.211 C15 8 15 8 16 11 C11.873 9.559 8.698 7.527 5.188 4.938 C4.212 4.225 3.236 3.512 2.23 2.777 C1.494 2.191 0.758 1.604 0 1 C0 0.67 0 0.34 0 0 Z " fill="#514E4D" transform="translate(467,219)"/>
<path d="M0 0 C2.339 2.127 3.807 4.062 5 7 C5 7.99 5 8.98 5 10 C2.688 9.812 2.688 9.812 0 9 C-3 4.966 -3 4.966 -3 2 C-3.99 1.67 -4.98 1.34 -6 1 C-3.525 1.495 -3.525 1.495 -1 2 C-0.34 3.98 0.32 5.96 1 8 C1 5.16 0.597 2.763 0 0 Z " fill="#7E7E7B" transform="translate(382,895)"/>
<path d="M0 0 C1.268 0.124 1.268 0.124 2.562 0.25 C4.562 3.25 4.562 3.25 4.25 6.375 C3.562 9.25 3.562 9.25 2.562 10.25 C0.062 10.375 0.062 10.375 -2.438 10.25 C-3.438 9.25 -3.438 9.25 -3.57 7.438 C-3.568 6.716 -3.565 5.994 -3.562 5.25 C-3.566 4.167 -3.566 4.167 -3.57 3.062 C-3.363 0.229 -2.833 0.291 0 0 Z M-1.438 1.25 C-2.794 2.375 -2.794 2.375 -2.5 5.312 C-2.479 6.282 -2.459 7.251 -2.438 8.25 C-1.117 8.58 0.202 8.91 1.562 9.25 C2.544 6.197 2.544 4.303 1.562 1.25 C0.572 1.25 -0.418 1.25 -1.438 1.25 Z " fill="#7D7D7C" transform="translate(558.4375,891.75)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C7.271 5.395 7.204 10.262 7 16 C6.67 16 6.34 16 6 16 C5.34 11.71 4.68 7.42 4 3 C3.01 3.33 2.02 3.66 1 4 C0.34 3.67 -0.32 3.34 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#888887" transform="translate(737,744)"/>
<path d="M0 0 C0.99 1.98 1.98 3.96 3 6 C-0.63 6 -4.26 6 -8 6 C-7.67 4.35 -7.34 2.7 -7 1 C-4.537 -0.231 -2.72 -0.072 0 0 Z M-5 2 C-5.99 3.485 -5.99 3.485 -7 5 C-4.36 5 -1.72 5 1 5 C0.34 4.01 -0.32 3.02 -1 2 C-2.32 2 -3.64 2 -5 2 Z " fill="#8D8D8B" transform="translate(509,743)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 0.99 1.66 1.98 2 3 C4.31 3 6.62 3 9 3 C7.938 5.5 7.938 5.5 6 8 C2.312 8.312 2.312 8.312 -1 8 C-0.67 5.36 -0.34 2.72 0 0 Z " fill="#A4A2A1" transform="translate(521,648)"/>
<path d="M0 0 C2.97 0.33 5.94 0.66 9 1 C9 1.33 9 1.66 9 2 C14.28 2.33 19.56 2.66 25 3 C25 3.33 25 3.66 25 4 C21.771 4.088 18.542 4.141 15.312 4.188 C13.944 4.225 13.944 4.225 12.549 4.264 C7.57 4.318 4.008 4.35 0 1 C0 0.67 0 0.34 0 0 Z " fill="#D4D1D0" transform="translate(494,230)"/>
<path d="M0 0 C1.188 2.562 1.188 2.562 2 5 C1.01 5.33 0.02 5.66 -1 6 C-1.66 5.34 -2.32 4.68 -3 4 C-5.318 3.281 -7.651 2.609 -10 2 C-6.17 -0.553 -4.417 -1.472 0 0 Z " fill="#B5B5B3" transform="translate(501,163)"/>
<path d="M0 0 C2 1 2 1 2.668 2.961 C2.839 3.758 3.011 4.554 3.188 5.375 C3.367 6.166 3.546 6.958 3.73 7.773 C4 10 4 10 3 13 C2.01 13 1.02 13 0 13 C0 8.71 0 4.42 0 0 Z " fill="#969694" transform="translate(640,891)"/>
<path d="M0 0 C5.75 -0.125 5.75 -0.125 8 1 C8 1.66 8 2.32 8 3 C8.66 3.33 9.32 3.66 10 4 C8.312 5.125 8.312 5.125 6 6 C3.25 5.125 3.25 5.125 1 4 C1 3.34 1 2.68 1 2 C0.01 2.66 -0.98 3.32 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#858684" transform="translate(594,889)"/>
<path d="M0 0 C2.312 0 2.312 0 5 1 C7.084 3.665 9 6.531 9 10 C8.34 10 7.68 10 7 10 C5.515 7.03 5.515 7.03 4 4 C4.33 5.65 4.66 7.3 5 9 C2.443 6.776 1.758 5.25 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#7D7D7C" transform="translate(518,884)"/>
<path d="M0 0 C1 3 1 3 0.223 4.824 C-0.852 6.549 -1.926 8.275 -3 10 C-3.99 9.67 -4.98 9.34 -6 9 C-5.34 8.01 -4.68 7.02 -4 6 C-4.99 6.33 -5.98 6.66 -7 7 C-7 6.01 -7 5.02 -7 4 C-7.99 3.67 -8.98 3.34 -10 3 C-9.362 2.951 -8.724 2.902 -8.066 2.852 C-7.24 2.777 -6.414 2.702 -5.562 2.625 C-4.739 2.555 -3.915 2.486 -3.066 2.414 C-2.384 2.277 -1.703 2.141 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#A09F9E" transform="translate(434,749)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 7.59 1 15.18 1 23 C1.99 23 2.98 23 4 23 C4 23.99 4 24.98 4 26 C2.68 25.67 1.36 25.34 0 25 C0 16.75 0 8.5 0 0 Z " fill="#BBBBBB" transform="translate(330,734)"/>
<path d="M0 0 C-3 2 -3 2 -7 2 C-9.043 6.524 -10.01 11.164 -11 16 C-11.33 16 -11.66 16 -12 16 C-12.451 9.576 -11.79 6.263 -8 1 C-5.075 -1.194 -3.405 -1.216 0 0 Z " fill="#ADACAC" transform="translate(278,736)"/>
<path d="M0 0 C1.939 2.909 3.492 5.816 5.062 8.938 C5.59 9.978 6.117 11.018 6.66 12.09 C7.743 14.442 8.507 16.475 9 19 C3.748 14.477 0.324 8.968 -0.25 1.938 C-0.168 1.298 -0.085 0.659 0 0 Z " fill="#4B2D2C" transform="translate(686,361)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C1.36 3.64 -1.28 6.28 -4 9 C-4.99 8.67 -5.98 8.34 -7 8 C-6.021 6.854 -5.042 5.708 -4.062 4.562 C-3.517 3.924 -2.972 3.286 -2.41 2.629 C-1.632 1.73 -0.841 0.841 0 0 Z " fill="#C4C4C2" transform="translate(470,128)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C-1.26 4.764 -5.518 8.499 -10 12 C-10 9 -10 9 -8.32 6.992 C-7.596 6.294 -6.871 5.595 -6.125 4.875 C-5.406 4.171 -4.686 3.467 -3.945 2.742 C-2 1 -2 1 0 0 Z " fill="#4E4D4D" transform="translate(476,127)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C2 3.64 2 6.28 2 9 C5.629 9.151 5.629 9.151 9 8 C8.67 8.99 8.34 9.98 8 11 C5.056 11.899 3.556 12.241 0.688 11 C-1 9 -1 9 -1.312 5.438 C-1 2 -1 2 0 0 Z " fill="#A4A4A5" transform="translate(612,892)"/>
<path d="M0 0 C0.784 0.186 1.567 0.371 2.375 0.562 C4.971 1.26 4.971 1.26 7 0 C7.562 1.938 7.562 1.938 8 4 C7 5 7 5 3.938 5.062 C2.968 5.042 1.999 5.021 1 5 C0.67 5.66 0.34 6.32 0 7 C-0.934 3.99 -1.044 3.133 0 0 Z " fill="#7E7F7F" transform="translate(580,893)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C2.644 2.722 4.311 3.394 6 4 C5.01 4.495 5.01 4.495 4 5 C4 7.31 4 9.62 4 12 C3.67 12 3.34 12 3 12 C2.34 9.69 1.68 7.38 1 5 C-2.629 4.849 -2.629 4.849 -6 6 C-5.67 5.01 -5.34 4.02 -5 3 C-3.35 2.67 -1.7 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A3A3A3" transform="translate(470,888)"/>
<path d="M0 0 C0.598 0.206 1.196 0.413 1.812 0.625 C0.163 1.285 -1.487 1.945 -3.188 2.625 C-2.857 3.945 -2.528 5.265 -2.188 6.625 C0.783 7.12 0.783 7.12 3.812 7.625 C3.153 8.285 2.492 8.945 1.812 9.625 C-1.812 9.25 -1.812 9.25 -5.188 8.625 C-5.375 5.25 -5.375 5.25 -5.188 1.625 C-2.188 -0.375 -2.188 -0.375 0 0 Z " fill="#B3B3B2" transform="translate(654.1875,750.375)"/>
<path d="M0 0 C1.891 2.837 2.25 3.881 2.75 7.062 C3.155 9.177 3.155 9.177 4 11 C6.994 12.611 6.994 12.611 10 13 C10 13.33 10 13.66 10 14 C3.552 13.392 3.552 13.392 1.281 11.781 C-0.457 9.364 -0.302 7.756 -0.188 4.812 C-0.16 3.911 -0.133 3.01 -0.105 2.082 C-0.071 1.395 -0.036 0.708 0 0 Z " fill="#B0B0AF" transform="translate(712,746)"/>
<path d="M0 0 C2.312 -0.312 2.312 -0.312 5 0 C6.875 2.25 6.875 2.25 8 5 C7.688 7.312 7.688 7.312 7 9 C4.36 9 1.72 9 -1 9 C0.952 7.048 2.532 6.152 5 5 C3.167 1.833 3.167 1.833 0 0 Z " fill="#B1B1B1" transform="translate(425,742)"/>
<path d="M0 0 C2.281 3.421 2.219 4.682 2.125 8.688 C2.107 9.681 2.089 10.675 2.07 11.699 C2.047 12.458 2.024 13.218 2 14 C2.619 14.124 3.238 14.247 3.875 14.375 C6 15 6 15 8 17 C6.35 17 4.7 17 3 17 C2.67 17.66 2.34 18.32 2 19 C2 18.34 2 17.68 2 17 C1.34 16.67 0.68 16.34 0 16 C0.33 15.67 0.66 15.34 1 15 C0.909 12.465 0.763 9.964 0.562 7.438 C0.51 6.727 0.458 6.016 0.404 5.283 C0.274 3.522 0.138 1.761 0 0 Z " fill="#81817F" transform="translate(421,744)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.66 0.34 3.32 0 4 C-0.225 5.537 -0.408 7.08 -0.562 8.625 C-0.646 9.442 -0.73 10.26 -0.816 11.102 C-0.877 11.728 -0.938 12.355 -1 13 C-2.32 13.33 -3.64 13.66 -5 14 C-5.33 12.68 -5.66 11.36 -6 10 C-5.01 10.495 -5.01 10.495 -4 11 C-3.567 9.113 -3.567 9.113 -3.125 7.188 C-2.427 4.146 -1.796 2.693 0 0 Z " fill="#828281" transform="translate(530,891)"/>
<path d="M0 0 C0.99 0.66 1.98 1.32 3 2 C2.67 2.99 2.34 3.98 2 5 C1.34 5 0.68 5 0 5 C0 8.63 0 12.26 0 16 C-0.66 15.67 -1.32 15.34 -2 15 C-2.562 13.125 -2.562 13.125 -3 11 C-3.33 10.01 -3.66 9.02 -4 8 C-3.34 7.34 -2.68 6.68 -2 6 C-1.286 4.016 -0.614 2.017 0 0 Z " fill="#868686" transform="translate(433,880)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.32 2 2.64 2 4 C6.141 4.21 6.141 4.21 10 3 C9.34 4.65 8.68 6.3 8 8 C1.125 6.125 1.125 6.125 0 5 C-0.041 3.334 -0.043 1.666 0 0 Z " fill="#B2B1B0" transform="translate(559,753)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.688 3.312 3.688 3.312 4 6 C2.562 7.812 2.562 7.812 1 9 C0.67 8.01 0.34 7.02 0 6 C-0.33 8.31 -0.66 10.62 -1 13 C-2.073 9.728 -1.893 7.571 -1.062 4.25 C-0.771 3.051 -0.771 3.051 -0.473 1.828 C-0.317 1.225 -0.161 0.622 0 0 Z " fill="#919290" transform="translate(634,747)"/>
<path d="M0 0 C6.36 0.36 6.36 0.36 8.938 2.938 C10.453 7.307 10.24 11.421 10 16 C9.67 16 9.34 16 9 16 C8.902 15.325 8.804 14.649 8.703 13.953 C7.631 7.024 7.631 7.024 3 2 C2.01 1.34 1.02 0.68 0 0 Z " fill="#BFBDBD" transform="translate(276,736)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C3.67 4.64 3.34 7.28 3 10 C2.01 10 1.02 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#B0B0B0" transform="translate(495,661)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.384 3.041 -1.286 5.041 -3 7 C-3.66 7 -4.32 7 -5 7 C-5.66 7.66 -6.32 8.32 -7 9 C-7 7.02 -7 5.04 -7 3 C-4.696 1.933 -2.36 0.936 0 0 Z " fill="#9B9998" transform="translate(546,191)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.355 4.485 0.899 7.028 -1 10 C-2 8 -2 8 -2 6 C-2.66 5.34 -3.32 4.68 -4 4 C-3.67 3.01 -3.34 2.02 -3 1 C-2.01 1 -1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#828281" transform="translate(533,881)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3.812 2.188 3.812 2.188 4 5 C1.75 7.812 1.75 7.812 -1 10 C-1.99 10 -2.98 10 -4 10 C-4.33 9.34 -4.66 8.68 -5 8 C-3.02 7.01 -1.04 6.02 1 5 C0.505 2.525 0.505 2.525 0 0 Z M1 1 C2 3 2 3 2 3 Z " fill="#8F908E" transform="translate(448,757)"/>
<path d="M0 0 C3 3 3 3 4 6 C7.512 7.23 7.512 7.23 11 8 C11 8.33 11 8.66 11 9 C7 9 3 9 -1 9 C-0.67 6.03 -0.34 3.06 0 0 Z " fill="#979694" transform="translate(406,752)"/>
<path d="M0 0 C3.675 0.509 6.895 1.65 10.312 3.062 C11.196 3.425 12.079 3.787 12.988 4.16 C13.652 4.437 14.316 4.714 15 5 C15 5.66 15 6.32 15 7 C8.994 6.268 4.034 4.356 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#5E3D3A" transform="translate(339,477)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.755 2.13 0.504 3.254 -0.75 4.375 C-1.446 5.001 -2.142 5.628 -2.859 6.273 C-5.979 8.789 -7.956 10 -12 10 C-12 9.34 -12 8.68 -12 8 C-10.886 7.546 -9.772 7.092 -8.625 6.625 C-5.038 5.275 -5.038 5.275 -3 3 C-2 2 -1 1 0 0 Z " fill="#4A2F2E" transform="translate(316,363)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C2.66 3.66 3.32 4.32 4 5 C4 3.35 4 1.7 4 0 C5.32 1.65 6.64 3.3 8 5 C7.01 6.485 7.01 6.485 6 8 C6.66 8.66 7.32 9.32 8 10 C4.905 9.668 3.972 8.971 1.75 6.688 C0 4 0 4 0 0 Z " fill="#C6C6C4" transform="translate(484,179)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.086 0.626 1.173 1.253 1.262 1.898 C1.686 4.364 2.156 6.644 3 9 C5.936 10.882 7.53 11 11 11 C11 11.66 11 12.32 11 13 C4.808 13.476 4.808 13.476 1.562 11.562 C-0.738 7.79 -0.331 4.309 0 0 Z " fill="#7B7A77" transform="translate(517,172)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C4 0.66 4 1.32 4 2 C3.01 2.495 3.01 2.495 2 3 C1.261 5.122 0.607 7.274 0 9.438 C-1.783 15.783 -1.783 15.783 -4 18 C-2.667 12 -1.333 6 0 0 Z " fill="#7B7A76" transform="translate(556,161)"/>
<path d="M0 0 C0.901 0.027 1.802 0.054 2.73 0.082 C3.761 0.134 3.761 0.134 4.812 0.188 C5.143 1.178 5.472 2.167 5.812 3.188 C-1.938 3.312 -1.938 3.312 -4.188 2.188 C-6.286 2.587 -6.286 2.587 -8.188 3.188 C-5.678 0.01 -3.961 -0.154 0 0 Z " fill="#BEBDBB" transform="translate(524.1875,161.8125)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.673 1.769 3.338 3.54 4 5.312 C4.371 6.299 4.743 7.285 5.125 8.301 C6 11 6 11 6 14 C5.34 14 4.68 14 4 14 C3.327 12.231 2.662 10.46 2 8.688 C1.629 7.701 1.257 6.715 0.875 5.699 C0 3 0 3 0 0 Z " fill="#B8B9B9" transform="translate(446,742)"/>
<path d="M0 0 C-0.33 0.66 -0.66 1.32 -1 2 C-1.99 1.814 -2.98 1.629 -4 1.438 C-8.171 0.823 -11.85 1.345 -16 2 C-16 1.01 -16 0.02 -16 -1 C-14.273 -1.224 -12.543 -1.428 -10.812 -1.625 C-9.368 -1.799 -9.368 -1.799 -7.895 -1.977 C-4.759 -2.002 -2.805 -1.344 0 0 Z " fill="#A4A4A3" transform="translate(492,737)"/>
<path d="M0 0 C1.5 1.125 1.5 1.125 3 3 C3.188 6.188 3.188 6.188 3 9 C0.525 8.01 0.525 8.01 -2 7 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#B6B6B6" transform="translate(431,671)"/>
<path d="M0 0 C2.009 0.018 4.017 0.168 6.02 0.332 C6.35 1.982 6.68 3.632 7.02 5.332 C6.36 5.332 5.7 5.332 5.02 5.332 C5.02 4.672 5.02 4.012 5.02 3.332 C1.072 3.153 -1.579 3.239 -4.98 5.332 C-3.052 0.512 -3.052 0.512 0 0 Z " fill="#908E8D" transform="translate(539.98046875,159.66796875)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.97 1 5.94 1 9 C-3 11 -3 11 -7 11 C-7 10.34 -7 9.68 -7 9 C-5.546 8.443 -5.546 8.443 -4.062 7.875 C-0.689 6.289 -0.689 6.289 -0.125 2.812 C-0.084 1.884 -0.043 0.956 0 0 Z " fill="#9C9C9B" transform="translate(428,894)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3.66 4.96 4.32 8.92 5 13 C4.01 12.67 3.02 12.34 2 12 C0.511 7.925 -0.263 4.335 0 0 Z M1 2 C2 4 2 4 2 4 Z " fill="#828281" transform="translate(541,891)"/>
<path d="M0 0 C2.335 0.991 4.107 2.269 6.062 3.875 C5.402 5.525 4.743 7.175 4.062 8.875 C2.856 6.543 1.897 4.379 1.062 1.875 C-0.918 2.205 -2.897 2.535 -4.938 2.875 C-2.508 -0.162 -2.508 -0.162 0 0 Z " fill="#8E8E8E" transform="translate(394.9375,888.125)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.99 2.33 2.98 2.66 4 3 C2.25 4.562 2.25 4.562 0 6 C-2.25 5.688 -2.25 5.688 -4 5 C-3.688 3.062 -3.688 3.062 -3 1 C-2.01 0.67 -1.02 0.34 0 0 Z " fill="#B8B7B7" transform="translate(591,647)"/>
<path d="M0 0 C0.715 1.578 0.715 1.578 1 4 C-0.057 6.385 -1.132 8.512 -2.438 10.75 C-2.776 11.352 -3.114 11.954 -3.463 12.574 C-4.298 14.056 -5.147 15.529 -6 17 C-7 14 -7 14 -5.566 10.645 C-4.909 9.362 -4.24 8.085 -3.562 6.812 C-3.224 6.158 -2.886 5.504 -2.537 4.83 C-1.7 3.215 -0.851 1.607 0 0 Z " fill="#E88475" transform="translate(505,322)"/>
<path d="M0 0 C2.64 0.33 5.28 0.66 8 1 C8 1.99 8 2.98 8 4 C8.99 4.33 9.98 4.66 11 5 C10.67 5.66 10.34 6.32 10 7 C9.01 7 8.02 7 7 7 C7 5.68 7 4.36 7 3 C4.69 3 2.38 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8B8A87" transform="translate(473,160)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.66 0.34 3.32 0 4 C-1.145 3.969 -1.145 3.969 -2.312 3.938 C-5.102 3.871 -5.102 3.871 -8 5 C-8 4.34 -8 3.68 -8 3 C-9.32 2.34 -10.64 1.68 -12 1 C-6.06 0.505 -6.06 0.505 0 0 Z " fill="#A2A09F" transform="translate(623,751)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-2.124 1.221 -4.249 1.427 -6.375 1.625 C-7.558 1.741 -8.742 1.857 -9.961 1.977 C-10.964 1.984 -11.967 1.992 -13 2 C-13.66 1.34 -14.32 0.68 -15 0 C-9.39 -1.621 -5.538 -2.556 0 0 Z " fill="#898988" transform="translate(487,735)"/>
<path d="M0 0 C0.994 0.009 1.988 0.018 3.012 0.027 C4.151 0.045 4.151 0.045 5.312 0.062 C5.642 0.722 5.973 1.383 6.312 2.062 C5.672 2.028 5.031 1.993 4.371 1.957 C-1.521 1.773 -5.454 2.332 -10.688 5.062 C-9.875 3.125 -9.875 3.125 -8.688 1.062 C-5.538 0.013 -3.299 -0.039 0 0 Z " fill="#C2C2C1" transform="translate(486.6875,653.9375)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-3.645 6.802 -3.645 6.802 -7 9 C-7.66 9 -8.32 9 -9 9 C-9 8.34 -9 7.68 -9 7 C-9.66 6.67 -10.32 6.34 -11 6 C-9.928 5.752 -8.855 5.505 -7.75 5.25 C-4.004 4.001 -2.479 2.975 0 0 Z " fill="#69443F" transform="translate(393,450)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0 4 0 4 -2.504 4.098 C-4.018 4.08 -4.018 4.08 -5.562 4.062 C-6.574 4.053 -7.586 4.044 -8.629 4.035 C-9.411 4.024 -10.194 4.012 -11 4 C-10.67 3.01 -10.34 2.02 -10 1 C-9.362 1.012 -8.724 1.023 -8.066 1.035 C-7.24 1.044 -6.414 1.053 -5.562 1.062 C-4.327 1.08 -4.327 1.08 -3.066 1.098 C-1.008 1.233 -1.008 1.233 0 0 Z " fill="#DB8076" transform="translate(500,374)"/>
<path d="M0 0 C-1.382 3.21 -2.924 5.049 -5.625 7.25 C-6.572 8.031 -6.572 8.031 -7.539 8.828 C-8.021 9.215 -8.503 9.602 -9 10 C-9 9.01 -9 8.02 -9 7 C-8.01 6.34 -7.02 5.68 -6 5 C-5.317 3.675 -4.649 2.342 -4 1 C-2 0 -2 0 0 0 Z " fill="#555453" transform="translate(557,209)"/>
<path d="M0 0 C2.762 2.762 2.579 5.207 3 9 C2.34 8.34 1.68 7.68 1 7 C0.01 7.33 -0.98 7.66 -2 8 C-2.66 5.69 -3.32 3.38 -4 1 C-3.01 1.495 -3.01 1.495 -2 2 C-2 2.66 -2 3.32 -2 4 C-1.34 3.67 -0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C1C1C0" transform="translate(545,166)"/>
<path d="M0 0 C3.99 0.56 6.526 1.757 9.812 4.062 C10.998 4.884 10.998 4.884 12.207 5.723 C12.799 6.144 13.39 6.566 14 7 C13.34 7.66 12.68 8.32 12 9 C11.385 8.578 10.77 8.157 10.137 7.723 C8.924 6.901 8.924 6.901 7.688 6.062 C6.887 5.517 6.086 4.972 5.262 4.41 C2.974 2.862 2.974 2.862 0 2 C0 1.34 0 0.68 0 0 Z " fill="#434240" transform="translate(535,120)"/>
<path d="M0 0 C3.273 0.209 5.324 0.437 7.875 2.562 C8.432 3.274 8.432 3.274 9 4 C6.83 4.955 5.437 5.144 3.176 4.398 C1.432 3.641 -0.286 2.824 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#C1C0C0" transform="translate(533,116)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C0.426 6.861 -1.61 6.943 -6 8 C-6 6 -6 6 -4.125 3.875 C-2 2 -2 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#999998" transform="translate(387,753)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.67 3.32 3.34 4.64 3 6 C0.36 6.33 -2.28 6.66 -5 7 C-4.041 6.66 -4.041 6.66 -3.062 6.312 C-0.728 5.147 -0.728 5.147 -0.25 2.375 C-0.168 1.591 -0.085 0.808 0 0 Z " fill="#A9A9A7" transform="translate(565,745)"/>
<path d="M0 0 C0.33 1.65 0.66 3.3 1 5 C0.587 4.484 0.175 3.969 -0.25 3.438 C-1.899 1.741 -1.899 1.741 -4.125 2.188 C-6.19 2.844 -6.19 2.844 -7 5 C-7.66 5 -8.32 5 -9 5 C-9 3.68 -9 2.36 -9 1 C-5.804 -0.065 -3.343 -0.074 0 0 Z " fill="#8D8D8B" transform="translate(658,744)"/>
<path d="M0 0 C2.375 0.375 2.375 0.375 5 2 C6.08 5.278 6.577 8.581 7 12 C4.745 9.745 4.023 7.995 3 5 C3 4.01 3 3.02 3 2 C2.34 2 1.68 2 1 2 C1.33 2.99 1.66 3.98 2 5 C1.34 5 0.68 5 0 5 C-0.562 3.062 -0.562 3.062 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#868684" transform="translate(353,740)"/>
<path d="M0 0 C1.461 2.647 2 3.894 2 7 C0.02 7.66 -1.96 8.32 -4 9 C-4 5 -4 5 -2.562 3.375 C-2.047 2.921 -1.531 2.467 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#AEADAC" transform="translate(604,658)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-4.29 2 -8.58 2 -13 2 C-8.244 -1.171 -5.588 -1.143 0 0 Z " fill="#BB726B" transform="translate(564,518)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.3 1 6.6 1 10 C1.66 10.33 2.32 10.66 3 11 C2.01 11.495 2.01 11.495 1 12 C-0.134 14.017 -0.134 14.017 -1 16 C-2.268 12.346 -1.757 9.509 -1.062 5.75 C-0.771 4.134 -0.771 4.134 -0.473 2.484 C-0.317 1.665 -0.161 0.845 0 0 Z " fill="#784B46" transform="translate(621,382)"/>
<path d="M0 0 C3.315 0.617 5.438 1.795 8.125 3.812 C8.808 4.314 9.491 4.815 10.195 5.332 C12.103 7.095 13 8.623 14 11 C13.492 10.613 12.984 10.227 12.461 9.828 C9.194 7.357 5.938 4.917 2.5 2.688 C1.675 2.131 0.85 1.574 0 1 C0 0.67 0 0.34 0 0 Z " fill="#EF8A78" transform="translate(318,306)"/>
<path d="M0 0 C-2.853 2.325 -5.541 3.751 -9 5 C-6.965 -0.67 -5.879 -1.392 0 0 Z " fill="#B5B2B1" transform="translate(546,224)"/>
<path d="M0 0 C1.698 0.252 3.385 0.576 5.062 0.938 C5.982 1.132 6.901 1.327 7.848 1.527 C8.913 1.761 8.913 1.761 10 2 C8 4 8 4 4.5 4.312 C3.345 4.209 2.19 4.106 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#878786" transform="translate(391,898)"/>
<path d="M0 0 C1.454 0.031 1.454 0.031 2.938 0.062 C2.938 0.722 2.938 1.383 2.938 2.062 C3.598 2.393 4.257 2.722 4.938 3.062 C3.938 4.062 3.938 4.062 1 4.312 C-0.011 4.23 -1.021 4.148 -2.062 4.062 C-2.723 3.072 -3.382 2.082 -4.062 1.062 C-3.062 0.062 -3.062 0.062 0 0 Z " fill="#AEAEAE" transform="translate(421.0625,676.9375)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C0.36 3.97 -2.28 6.94 -5 10 C-5.66 9.34 -6.32 8.68 -7 8 C-4.69 5.36 -2.38 2.72 0 0 Z " fill="#514F4F" transform="translate(560,210)"/>
<path d="M0 0 C4.455 0.99 4.455 0.99 9 2 C8.67 2.99 8.34 3.98 8 5 C5.125 4.75 5.125 4.75 2 4 C1.34 2.68 0.68 1.36 0 0 Z " fill="#989797" transform="translate(443,900)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.09 5.152 1.847 9.919 1 15 C0.67 15 0.34 15 0 15 C-0.849 9.624 -1.002 5.346 0 0 Z " fill="#A9A9A9" transform="translate(412,885)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C1.66 3.33 2.32 3.66 3 4 C2.477 6.763 1.891 9.326 1 12 C0.34 12 -0.32 12 -1 12 C-0.67 8.04 -0.34 4.08 0 0 Z " fill="#9A9A98" transform="translate(551,746)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C1.99 6 2.98 6 4 6 C4 4.02 4 2.04 4 0 C4.33 0 4.66 0 5 0 C5 2.64 5 5.28 5 8 C3.02 8 1.04 8 -1 8 C-0.67 5.36 -0.34 2.72 0 0 Z " fill="#C2C2C2" transform="translate(632,674)"/>
<path d="M0 0 C2.625 0.375 2.625 0.375 5 1 C5 2.32 5 3.64 5 5 C2.625 5.188 2.625 5.188 0 5 C-0.66 4.01 -1.32 3.02 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z M0 1 C0.33 1.99 0.66 2.98 1 4 C1 3.01 1 2.02 1 1 C0.67 1 0.34 1 0 1 Z " fill="#868684" transform="translate(594,897)"/>
<path d="M0 0 C0.433 0.495 0.866 0.99 1.312 1.5 C2.997 3.332 2.997 3.332 6 3 C6.66 2.34 7.32 1.68 8 1 C8.66 1.66 9.32 2.32 10 3 C9.67 3.99 9.34 4.98 9 6 C8.34 5.67 7.68 5.34 7 5 C5 4.96 3 4.957 1 5 C0.505 2.525 0.505 2.525 0 0 Z " fill="#929190" transform="translate(339,753)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.34 5.31 1.68 7.62 1 10 C-1.475 9.01 -1.475 9.01 -4 8 C-4 7.34 -4 6.68 -4 6 C-2.68 6 -1.36 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#787877" transform="translate(436,676)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6.33 2.31 6.66 4.62 7 7 C6.01 7 5.02 7 4 7 C3.67 7.99 3.34 8.98 3 10 C2.505 7.525 2.505 7.525 2 5 C2.99 5 3.98 5 5 5 C5 3.68 5 2.36 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#9C9C9A" transform="translate(637,668)"/>
<path d="M0 0 C1.772 0.114 3.542 0.242 5.312 0.375 C6.792 0.479 6.792 0.479 8.301 0.586 C11 1 11 1 14 3 C12.417 3.054 10.834 3.093 9.25 3.125 C8.368 3.148 7.487 3.171 6.578 3.195 C3.886 2.991 2.301 2.366 0 1 C0 0.67 0 0.34 0 0 Z " fill="#432C2C" transform="translate(354,499)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.715 2.339 -0.579 3.671 -1.875 5 C-2.594 5.743 -3.314 6.485 -4.055 7.25 C-6 9 -6 9 -8 9 C-6.417 4.726 -3.494 2.735 0 0 Z " fill="#E78473" transform="translate(529,438)"/>
<path d="M0 0 C2.375 0.312 2.375 0.312 5 1 C5.66 1.99 6.32 2.98 7 4 C6.67 4.99 6.34 5.98 6 7 C5.67 5.68 5.34 4.36 5 3 C3.02 3 1.04 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#979798" transform="translate(582,890)"/>
<path d="M0 0 C0.557 0.186 1.114 0.371 1.688 0.562 C4.399 1.076 6.344 0.664 9 0 C9.33 0.99 9.66 1.98 10 3 C6.7 3 3.4 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#848483" transform="translate(552,888)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 2.32 4 3.64 4 5 C2.35 5 0.7 5 -1 5 C-1.33 4.01 -1.66 3.02 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#888786" transform="translate(543,885)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.502 3.735 1.125 5.812 -1 9 C-3.125 9.688 -3.125 9.688 -5 10 C-4 7 -4 7 -1 5 C-0.27 2.437 -0.27 2.437 0 0 Z " fill="#B9B8B7" transform="translate(452,757)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C2.66 3 3.32 3 4 3 C4.66 2.01 5.32 1.02 6 0 C6 1.65 6 3.3 6 5 C4.35 5 2.7 5 1 5 C0.67 6.32 0.34 7.64 0 9 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#8D8D8C" transform="translate(735,747)"/>
<path d="M0 0 C2.007 0.287 4.009 0.619 6 1 C6 1.99 6 2.98 6 4 C3.625 4.188 3.625 4.188 1 4 C0.34 3.01 -0.32 2.02 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B3B4B3" transform="translate(479,676)"/>
<path d="M0 0 C1.125 1.688 1.125 1.688 2 4 C1.125 6.75 1.125 6.75 0 9 C-0.99 8.01 -1.98 7.02 -3 6 C-1.125 1.125 -1.125 1.125 0 0 Z " fill="#C0C0C0" transform="translate(624,654)"/>
<path d="M0 0 C4.355 1.161 6.646 2.077 9 6 C8.67 6.66 8.34 7.32 8 8 C7.278 7.196 6.556 6.391 5.812 5.562 C2.975 2.977 1.728 2.32 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#7B4C46" transform="translate(466,453)"/>
<path d="M0 0 C2.312 -0.312 2.312 -0.312 5 0 C6.875 2.312 6.875 2.312 8 5 C7.67 5.99 7.34 6.98 7 8 C5.83 6.857 4.664 5.711 3.5 4.562 C2.85 3.924 2.201 3.286 1.531 2.629 C0 1 0 1 0 0 Z " fill="#6C3F3A" transform="translate(586,439)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 3.98 0.02 5.96 -1 8 C-1.66 8 -2.32 8 -3 8 C-3.33 7.01 -3.66 6.02 -4 5 C-2.062 2.312 -2.062 2.312 0 0 Z " fill="#864C47" transform="translate(496,337)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.96 1.66 7.92 2 12 C1.67 11.01 1.34 10.02 1 9 C0.34 9 -0.32 9 -1 9 C-1.81 6.085 -2.218 4.539 -1.062 1.688 C-0.712 1.131 -0.361 0.574 0 0 Z " fill="#C0BFBD" transform="translate(453,179)"/>
<path d="M0 0 C-0.33 1.98 -0.66 3.96 -1 6 C-1.99 6.33 -2.98 6.66 -4 7 C-4 5.02 -4 3.04 -4 1 C-2 0 -2 0 0 0 Z " fill="#B9B7B7" transform="translate(472,156)"/>
<path d="M0 0 C2.438 0.812 2.438 0.812 5 2 C5.33 2.99 5.66 3.98 6 5 C5.01 5.99 4.02 6.98 3 8 C0 2.25 0 2.25 0 0 Z " fill="#B4B4B3" transform="translate(563,143)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.01 3.3 0.02 6.6 -1 10 C-1.66 10 -2.32 10 -3 10 C-1.125 2.25 -1.125 2.25 0 0 Z " fill="#B6B5B5" transform="translate(458,742)"/>
<path d="M0 0 C1.32 1.32 2.64 2.64 4 4 C3.301 4.133 2.603 4.266 1.883 4.402 C0.973 4.579 0.063 4.756 -0.875 4.938 C-1.78 5.112 -2.685 5.286 -3.617 5.465 C-6 5.9 -6 5.9 -8 7 C-7.67 6.01 -7.34 5.02 -7 4 C-3.438 2.812 -3.438 2.812 0 2 C0 1.34 0 0.68 0 0 Z " fill="#828280" transform="translate(720,738)"/>
<path d="M0 0 C0.681 0.557 1.361 1.114 2.062 1.688 C1.733 2.347 1.402 3.008 1.062 3.688 C0.733 3.357 0.402 3.028 0.062 2.688 C-3.601 2.552 -6.444 2.523 -9.938 3.688 C-9.938 3.028 -9.938 2.367 -9.938 1.688 C-3.661 -1.636 -3.661 -1.636 0 0 Z " fill="#888988" transform="translate(418.9375,740.3125)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 3.31 1.34 5.62 1 8 C-0.32 7.34 -1.64 6.68 -3 6 C-1.125 1.125 -1.125 1.125 0 0 Z M0 2 C1 4 1 4 1 4 Z " fill="#999796" transform="translate(628,656)"/>
<path d="M0 0 C2.724 2.353 3.814 4.625 5 8 C3.333 8 1.667 8 0 8 C0 5.36 0 2.72 0 0 Z " fill="#683B37" transform="translate(636,464)"/>
<path d="M0 0 C0 3 0 3 -2 6 C-2.167 8.625 -2.167 8.625 -2 11 C-2.99 11.33 -3.98 11.66 -5 12 C-4.585 6.712 -3.648 3.883 0 0 Z " fill="#E78779" transform="translate(338,442)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C3.75 2.25 3.75 2.25 5 5 C4.188 7.312 4.188 7.312 3 9 C0.875 5.812 0.498 3.735 0 0 Z " fill="#B1B0AF" transform="translate(452,188)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.96 1.66 7.92 2 12 C1.34 12 0.68 12 0 12 C-0.762 7.684 -1.161 4.256 0 0 Z " fill="#848584" transform="translate(552,900)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.33 3.96 1.66 7.92 2 12 C1.01 12.33 0.02 12.66 -1 13 C-1.99 12.34 -2.98 11.68 -4 11 C-2.68 11 -1.36 11 0 11 C0 7.37 0 3.74 0 0 Z " fill="#787977" transform="translate(414,892)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C-1.64 5 -4.28 5 -7 5 C-6.01 4.67 -5.02 4.34 -4 4 C-3.67 3.01 -3.34 2.02 -3 1 C-2.01 1.33 -1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#999997" transform="translate(659,753)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-2.709 4.354 -5.009 4.065 -8 4 C-8 3.01 -8 2.02 -8 1 C-6.855 1.309 -6.855 1.309 -5.688 1.625 C-2.781 2.334 -2.781 2.334 0 0 Z " fill="#8A8A88" transform="translate(511,754)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.32 2 2.64 2 4 C2.66 4 3.32 4 4 4 C3.67 4.99 3.34 5.98 3 7 C2.01 7.33 1.02 7.66 0 8 C-1.097 4.71 -0.8 3.287 0 0 Z " fill="#9C9C9A" transform="translate(704,749)"/>
<path d="M0 0 C-0.33 1.32 -0.66 2.64 -1 4 C-3.343 3.744 -5.678 3.407 -8 3 C-8.33 2.34 -8.66 1.68 -9 1 C-6.043 -0.478 -3.258 -0.06 0 0 Z " fill="#A8A9A8" transform="translate(419,742)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.744 5.673 2.221 10.352 1 16 C0.67 16 0.34 16 0 16 C0 10.72 0 5.44 0 0 Z " fill="#595A58" transform="translate(580,656)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.75 2.75 2.75 2.75 3 6 C1.062 8.375 1.062 8.375 -1 10 C-1.66 9.67 -2.32 9.34 -3 9 C-2.505 8.629 -2.01 8.257 -1.5 7.875 C0.598 5.253 0.217 3.262 0 0 Z " fill="#7F4E48" transform="translate(398,440)"/>
<path d="M0 0 C2.964 2.808 5.445 5.183 7 9 C6.01 9.495 6.01 9.495 5 10 C4.381 9.051 3.763 8.103 3.125 7.125 C1.18 4.056 1.18 4.056 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#553431" transform="translate(647,381)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C-1.465 4.97 -1.465 4.97 -5 8 C-5 5 -5 5 -2.5 2.312 C-1.675 1.549 -0.85 0.786 0 0 Z " fill="#5F5E5D" transform="translate(469,126)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C2.65 6 4.3 6 6 6 C5.01 7.485 5.01 7.485 4 9 C3.01 9 2.02 9 1 9 C-0.478 6.043 -0.06 3.258 0 0 Z " fill="#A0A0A0" transform="translate(545,896)"/>
<path d="M0 0 C2.64 0.66 5.28 1.32 8 2 C6.68 2.33 5.36 2.66 4 3 C3.67 4.32 3.34 5.64 3 7 C3 5.68 3 4.36 3 3 C1.68 3 0.36 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#A1A0A0" transform="translate(541,889)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C3.97 5.495 3.97 5.495 7 6 C7 6.66 7 7.32 7 8 C4.625 8.125 4.625 8.125 2 8 C0 6 0 6 -0.125 2.875 C-0.084 1.926 -0.043 0.977 0 0 Z " fill="#B6B6B5" transform="translate(311,752)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C1.99 4 2.98 4 4 4 C4.33 3.01 4.66 2.02 5 1 C5.66 1.33 6.32 1.66 7 2 C6.34 3.32 5.68 4.64 5 6 C3.35 5.67 1.7 5.34 0 5 C0 3.35 0 1.7 0 0 Z " fill="#918F8D" transform="translate(411,753)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-3.83 3.802 -6.781 4.196 -11 4 C-11 3.67 -11 3.34 -11 3 C-9.35 3 -7.7 3 -6 3 C-6 2.34 -6 1.68 -6 1 C-3 0 -3 0 0 0 Z " fill="#B5B5B5" transform="translate(490,756)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.125 5.75 2.125 5.75 1 8 C0.34 8 -0.32 8 -1 8 C-1.042 5.667 -1.041 3.333 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A5A5A4" transform="translate(678,742)"/>
<path d="M0 0 C-0.598 0.268 -1.196 0.536 -1.812 0.812 C-3.997 1.998 -5.332 3.181 -7 5 C-7.66 5.66 -8.32 6.32 -9 7 C-9.188 4.625 -9.188 4.625 -9 2 C-5.565 -0.29 -4.015 -0.178 0 0 Z " fill="#BABAB9" transform="translate(722,742)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.34 1.65 4.68 3.3 4 5 C2.68 4.67 1.36 4.34 0 4 C0 2.68 0 1.36 0 0 Z " fill="#A6A5A5" transform="translate(624,674)"/>
<path d="M0 0 C0.598 0.165 1.196 0.33 1.812 0.5 C4.113 1.111 4.113 1.111 7 1 C7.33 2.32 7.66 3.64 8 5 C4.625 4.453 2.918 3.945 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8B8A89" transform="translate(480,673)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C-0.688 3.188 -0.688 3.188 -3 4 C-5.75 2.625 -5.75 2.625 -8 1 C-3 -1.5 -3 -1.5 0 0 Z " fill="#969593" transform="translate(594,652)"/>
<path d="M0 0 C3.3 1.65 6.6 3.3 10 5 C6 6 6 6 4.078 5.223 C2.385 4.148 0.693 3.074 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#543735" transform="translate(360,386)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C4 1.66 4 2.32 4 3 C4.99 3.33 5.98 3.66 7 4 C3.799 5.6 1.277 4.032 -2 3 C-1.34 2.67 -0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#AEABA9" transform="translate(490,228)"/>
<path d="M0 0 C3.631 2.65 5.899 5.018 8 9 C4.206 7.51 2.317 5.309 0 2 C0 1.34 0 0.68 0 0 Z " fill="#353434" transform="translate(461,210)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-3.473 3.158 -6.361 3.069 -10 3 C-6.682 -0.063 -4.448 -0.303 0 0 Z " fill="#949391" transform="translate(653,889)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 3 -0.32 3 -1 3 C-1.33 3.66 -1.66 4.32 -2 5 C-3.32 4.67 -4.64 4.34 -6 4 C-6 3.34 -6 2.68 -6 2 C-2.25 0 -2.25 0 0 0 Z " fill="#A8A7A7" transform="translate(672,763)"/>
<path d="M0 0 C1.32 0.66 2.64 1.32 4 2 C3.01 2 2.02 2 1 2 C1 2.66 1 3.32 1 4 C1.66 4 2.32 4 3 4 C3 5.32 3 6.64 3 8 C2.34 8 1.68 8 1 8 C-0.429 5.646 -1.087 4.52 -0.625 1.75 C-0.419 1.173 -0.212 0.595 0 0 Z " fill="#A6A5A5" transform="translate(684,752)"/>
<path d="M0 0 C2.164 0.023 2.164 0.023 4.625 0.375 C5.851 0.541 5.851 0.541 7.102 0.711 C7.728 0.806 8.355 0.902 9 1 C9 1.33 9 1.66 9 2 C6.69 2 4.38 2 2 2 C2 2.66 2 3.32 2 4 C0.68 3.34 -0.64 2.68 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#A3A3A2" transform="translate(374,749)"/>
<path d="M0 0 C-0.66 0.66 -1.32 1.32 -2 2 C-4.164 1.977 -4.164 1.977 -6.625 1.625 C-7.442 1.514 -8.26 1.403 -9.102 1.289 C-9.728 1.194 -10.355 1.098 -11 1 C-6.82 -1.786 -4.853 -0.796 0 0 Z " fill="#6B403B" transform="translate(568,429)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 5.94 1 11.88 1 18 C0.67 18 0.34 18 0 18 C0 12.06 0 6.12 0 0 Z " fill="#C9796E" transform="translate(596,326)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.25 5.75 1.25 5.75 -1 8 C-1.931 5.394 -2.149 4.358 -1.062 1.75 C-0.712 1.173 -0.361 0.595 0 0 Z " fill="#AEADAC" transform="translate(555,176)"/>
<path d="M0 0 C1.619 2.572 2.044 3.677 1.625 6.75 C1.419 7.493 1.212 8.235 1 9 C0.34 9 -0.32 9 -1 9 C-1.33 7.02 -1.66 5.04 -2 3 C-1.34 3 -0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B6B5B4" transform="translate(453,159)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 0.99 6 1.98 6 3 C4.02 3 2.04 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C6C5C4" transform="translate(474,163)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 3.31 2.02 5.62 1 8 C0.174 5.108 0 3.113 0 0 Z " fill="#A6A5A4" transform="translate(603,744)"/>
<path d="M0 0 C0.928 0.206 1.856 0.413 2.812 0.625 C2.812 0.955 2.812 1.285 2.812 1.625 C-2.812 3.625 -2.812 3.625 -6.188 3.625 C-6.188 2.965 -6.188 2.305 -6.188 1.625 C-3.188 -0.375 -3.188 -0.375 0 0 Z " fill="#ADADAC" transform="translate(585.1875,742.375)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-5.415 3.231 -5.415 3.231 -8.375 2.062 C-8.911 1.712 -9.447 1.361 -10 1 C-6.62 -0.04 -3.522 -0.08 0 0 Z " fill="#BCBBBB" transform="translate(630,668)"/>
<path d="M0 0 C1.32 1.32 2.64 2.64 4 4 C3.67 4.66 3.34 5.32 3 6 C1.68 5.34 0.36 4.68 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#B4B1B0" transform="translate(463,207)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C-0.31 4 -2.62 4 -5 4 C-3.745 1.489 -2.499 1.129 0 0 Z " fill="#BCBBBA" transform="translate(546,195)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.34 1.98 3.68 3.96 3 6 C2.34 5.67 1.68 5.34 1 5 C0.375 2.438 0.375 2.438 0 0 Z " fill="#AEAFAD" transform="translate(557,164)"/>
<path d="M0 0 C3.166 2.111 4.196 3.735 6 7 C5.01 7.495 5.01 7.495 4 8 C2.35 5.69 0.7 3.38 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#5A5858" transform="translate(556,127)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.96 1 7.92 1 12 C0.67 12 0.34 12 0 12 C0 8.04 0 4.08 0 0 Z " fill="#C1786D" transform="translate(612,468)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 3.3 2 6.6 2 10 C1.67 10 1.34 10 1 10 C0.67 6.7 0.34 3.4 0 0 Z " fill="#ACABAB" transform="translate(440,664)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.65 2 3.3 2 5 C1.34 5 0.68 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#9A9B99" transform="translate(307,747)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 1.98 2 3.96 2 6 C1.67 5.01 1.34 4.02 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#A5A3A2" transform="translate(584,746)"/>
<path d="M0 0 C1.98 0.66 3.96 1.32 6 2 C3.924 2.553 2.156 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C5776E" transform="translate(561,452)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 3.31 2 5.62 2 8 C1.67 8 1.34 8 1 8 C0.67 5.36 0.34 2.72 0 0 Z " fill="#9A9A98" transform="translate(728,743)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 1.32 2.34 2.64 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#ADACAC" transform="translate(607,667)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 1.65 3 3.3 3 5 C2.01 5.495 2.01 5.495 1 6 C1.33 4.35 1.66 2.7 2 1 C1.34 0.67 0.68 0.34 0 0 Z " fill="#ACABAB" transform="translate(569,664)"/>
<path d="M0 0 C2.475 0.99 2.475 0.99 5 2 C5 2.33 5 2.66 5 3 C3.35 3 1.7 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#B07770" transform="translate(530,516)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.99 3 2.98 3 4 C2.34 3.67 1.68 3.34 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#9D9D9B" transform="translate(716,751)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C-0.062 4.125 -0.062 4.125 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#9D9D9B" transform="translate(689,748)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.99 2 1.98 2 3 C1.01 2.67 0.02 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#A19F9E" transform="translate(672,745)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C4.67 1.66 4.34 2.32 4 3 C2.68 2.01 1.36 1.02 0 0 Z " fill="#999898" transform="translate(276,740)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C0.68 2.67 -0.64 2.34 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#9E9E9D" transform="translate(382,737)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C2 1.99 2 2.98 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#AEADAE" transform="translate(469,668)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.68 1.99 -0.64 2.98 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#ABAAAA" transform="translate(529,664)"/>
<path d="M0 0 C1.32 0 2.64 0 4 0 C3.67 0.99 3.34 1.98 3 3 C2.01 2.01 1.02 1.02 0 0 Z " fill="#AEAEAD" transform="translate(455,661)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 1.33 5 1.66 5 2 C3.35 2 1.7 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B1736C" transform="translate(712,394)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C0.68 2 -0.64 2 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#C0766C" transform="translate(644,381)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.34 -0.32 4.68 -1 4 C-0.625 1.875 -0.625 1.875 0 0 Z " fill="#C47B71" transform="translate(444,377)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.34 5.01 -0.32 4.02 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#D08074" transform="translate(444,354)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#C7796E" transform="translate(596,348)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#D27D70" transform="translate(682,344)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 2.31 1 4.62 1 7 C0.67 7 0.34 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#AD746B" transform="translate(249,346)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.35 1.66 -0.3 2.32 -2 3 C-2 2.34 -2 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#C1776E" transform="translate(291,324)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.33 3 1.66 3 2 C1.35 2 -0.3 2 -2 2 C-1.34 1.34 -0.68 0.68 0 0 Z " fill="#C47C70" transform="translate(578,298)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.67 0.66 4.34 1.32 4 2 C2.68 1.34 1.36 0.68 0 0 Z " fill="#9E9C9B" transform="translate(587,762)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#ABAAAA" transform="translate(571,657)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C-0.66 3 -1.32 3 -2 3 C-2 2.34 -2 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#ABABAA" transform="translate(550,655)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 1.66 2.34 2.32 2 3 C1.01 2.34 0.02 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A7716B" transform="translate(337,516)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C3 1.66 3 2.32 3 3 C2.34 3 1.68 3 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#AD726C" transform="translate(659,509)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.99 1.34 2.98 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#BC746C" transform="translate(438,487)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C3 0.66 3 1.32 3 2 C2.01 2.33 1.02 2.66 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C4786C" transform="translate(357,454)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.34 4.34 -0.32 3.68 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#C47D72" transform="translate(444,386)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#C3776E" transform="translate(596,372)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C0.67 6 0.34 6 0 6 C0 4.02 0 2.04 0 0 Z " fill="#CF8175" transform="translate(444,362)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 2.32 0.68 3.64 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C4776D" transform="translate(437,342)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 1.33 5 1.66 5 2 C3.35 2 1.7 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A09997" transform="translate(490,234)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C0.01 4.495 0.01 4.495 -1 5 C-0.67 3.35 -0.34 1.7 0 0 Z " fill="#B0AEAD" transform="translate(552,169)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A2A29F" transform="translate(634,758)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#979595" transform="translate(740,755)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 4 0.68 4 0 4 C0 2.68 0 1.36 0 0 Z " fill="#A09F9E" transform="translate(496,755)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.99 1.34 1.98 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#A5A3A3" transform="translate(453,744)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#A1A09F" transform="translate(629,741)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 2.33 -0.98 2.66 -2 3 C-2 2.34 -2 1.68 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#959594" transform="translate(737,738)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2.66 0.68 3.32 0 4 C0 2.68 0 1.36 0 0 Z " fill="#ACABAB" transform="translate(548,675)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 3 -0.32 3 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#ADADAA" transform="translate(612,667)"/>
<path d="M0 0 C0.66 1.32 1.32 2.64 2 4 C1.34 3.67 0.68 3.34 0 3 C0 2.01 0 1.02 0 0 Z " fill="#ADACAD" transform="translate(469,660)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2 0.02 2 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#ACABA9" transform="translate(453,661)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.33 0.02 2.66 -1 3 C-0.67 2.01 -0.34 1.02 0 0 Z " fill="#B17770" transform="translate(419,508)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.67 1.66 1.34 2.32 1 3 C0.34 2.34 -0.32 1.68 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B77770" transform="translate(514,500)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.99 1.34 2.98 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#C0756B" transform="translate(643,481)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#C97B72" transform="translate(577,474)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 0.33 5 0.66 5 1 C3.35 1 1.7 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C27A6F" transform="translate(658,395)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.67 1.66 2.34 2.32 2 3 C1.34 2.67 0.68 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B7776F" transform="translate(368,393)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.99 1.34 2.98 1 4 C0.67 2.68 0.34 1.36 0 0 Z " fill="#BE756C" transform="translate(482,370)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 2.66 -0.32 3.32 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#BD746C" transform="translate(481,368)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BE786E" transform="translate(338,346)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C0.67 5 0.34 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#BA746B" transform="translate(277,342)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.01 1.33 2.02 1.66 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C4786D" transform="translate(303,325)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.01 2.67 0.02 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#ABA9A7" transform="translate(484,189)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#929190" transform="translate(532,891)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A09F9E" transform="translate(365,758)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#8F908F" transform="translate(748,753)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9E9C9B" transform="translate(719,746)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#A7A5A4" transform="translate(574,744)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A2A1A0" transform="translate(485,741)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9F9F9D" transform="translate(386,741)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#9F9FA0" transform="translate(476,732)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A8A6A6" transform="translate(545,684)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A7A6A6" transform="translate(481,684)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#A1A1A1" transform="translate(388,684)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#ACACAC" transform="translate(520,672)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#ACABAA" transform="translate(490,667)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#ABAAA9" transform="translate(592,664)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#ADACAB" transform="translate(441,659)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#ADADAC" transform="translate(617,658)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1.33 1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#ADACAA" transform="translate(438,655)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#AF736D" transform="translate(665,516)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#AB736D" transform="translate(612,516)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C0.34 2.67 -0.32 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#B8756D" transform="translate(471,489)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B7736A" transform="translate(358,487)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#C77A6E" transform="translate(501,455)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BE786E" transform="translate(359,427)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C1.01 1 0.02 1 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#AD746D" transform="translate(309,398)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#B6756E" transform="translate(383,397)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#AD756F" transform="translate(297,397)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#AD756F" transform="translate(288,397)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#BE786E" transform="translate(602,395)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#B9786F" transform="translate(376,396)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2 0.66 2 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BF786F" transform="translate(537,395)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B7756D" transform="translate(423,382)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#C7776C" transform="translate(640,374)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.67 1.66 1.34 2.32 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#B1756D" transform="translate(256,374)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C5786E" transform="translate(596,364)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#C7776B" transform="translate(634,364)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C77A71" transform="translate(436,356)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#B8746B" transform="translate(277,350)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#C87A6D" transform="translate(732,348)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#C1756C" transform="translate(367,339)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CE7D6F" transform="translate(682,339)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CF7A6E" transform="translate(493,336)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#C4776A" transform="translate(287,326)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C2.01 1 1.02 1 0 1 C0 0.67 0 0.34 0 0 Z " fill="#C87B6F" transform="translate(388,323)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#CB7A6E" transform="translate(596,318)"/>
<path d="M0 0 C0 3 0 3 0 3 Z " fill="#CC7D70" transform="translate(505,314)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 2.495 1.01 2.495 0 3 C0 2.01 0 1.02 0 0 Z " fill="#CA7D71" transform="translate(482,312)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.67 0.66 2.34 1.32 2 2 C1.34 1.34 0.68 0.68 0 0 Z " fill="#CB7B6F" transform="translate(504,312)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#CD8374" transform="translate(538,298)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#BF796D" transform="translate(300,298)"/>
<path d="M0 0 C4 1 4 1 4 1 Z " fill="#9F9794" transform="translate(503,237)"/>
<path d="M0 0 C0.99 1.485 0.99 1.485 2 3 C1.34 3 0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#A09D9E" transform="translate(479,217)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#B0AEAE" transform="translate(480,172)"/>
<path d="M0 0 C1 4 1 4 1 4 Z " fill="#B0AEAC" transform="translate(539,171)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.01 1.67 -0.98 1.34 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#AFADAC" transform="translate(478,174)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.34 1.66 0.68 2.32 0 3 C0 2.01 0 1.02 0 0 Z " fill="#AAA9A9" transform="translate(534,160)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.01 1.67 0.02 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9E9D9D" transform="translate(485,126)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#8D8E8D" transform="translate(407,898)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#939291" transform="translate(469,895)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#90908F" transform="translate(616,894)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#8F9090" transform="translate(589,888)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#8A8C8B" transform="translate(382,882)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A6A4A2" transform="translate(528,758)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A1A0A0" transform="translate(493,757)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9E9E9E" transform="translate(680,755)"/>
<path d="" fill="#9F9F9E" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#989796" transform="translate(343,754)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#979695" transform="translate(277,754)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#9B9B9A" transform="translate(721,753)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9C9C9A" transform="translate(316,753)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9D9D9C" transform="translate(353,752)"/>
<path d="" fill="#999998" transform="translate(0,0)"/>
<path d="" fill="#A09F9D" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#A0A0A0" transform="translate(514,744)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A1A1A1" transform="translate(462,743)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9B9C9A" transform="translate(298,744)"/>
<path d="" fill="#9A9B99" transform="translate(0,0)"/>
<path d="" fill="#A2A1A0" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9E9F9E" transform="translate(360,741)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A1A2A0" transform="translate(616,739)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9C9A99" transform="translate(287,738)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A0A0A0" transform="translate(658,738)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A2A2A2" transform="translate(589,736)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A2A3A2" transform="translate(509,737)"/>
<path d="" fill="#9C9C9C" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9A9B9A" transform="translate(304,736)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#999999" transform="translate(295,737)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A0A09F" transform="translate(656,736)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A4A4A5" transform="translate(422,686)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#ABAAAA" transform="translate(540,684)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A4A3A3" transform="translate(417,684)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9F9D9D" transform="translate(384,680)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#A8A7A6" transform="translate(616,676)"/>
<path d="" fill="#ADACAC" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A9A9A8" transform="translate(609,672)"/>
<path d="" fill="#AAA9A8" transform="translate(0,0)"/>
<path d="" fill="#AEAEAE" transform="translate(0,0)"/>
<path d="" fill="#AFAEAD" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A9A8A7" transform="translate(396,662)"/>
<path d="" fill="#AFAEAE" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#A5A4A5" transform="translate(433,650)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#AAA9A9" transform="translate(560,642)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#AE766F" transform="translate(421,510)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#B47770" transform="translate(416,505)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#B1736C" transform="translate(412,501)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#BA766D" transform="translate(373,496)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B5756A" transform="translate(353,496)"/>
<path d="" fill="#BA756D" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B6756C" transform="translate(373,491)"/>
<path d="" fill="#BC756E" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C57B72" transform="translate(604,486)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BB746C" transform="translate(473,485)"/>
<path d="" fill="#C0776E" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CB7B6F" transform="translate(640,479)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C3786E" transform="translate(476,476)"/>
<path d="" fill="#C4786F" transform="translate(0,0)"/>
<path d="" fill="#C4786E" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#B9776D" transform="translate(329,462)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C57B70" transform="translate(364,460)"/>
<path d="" fill="#CB7B6E" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C6786D" transform="translate(481,433)"/>
<path d="" fill="#C1796D" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C3776D" transform="translate(468,428)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BA746B" transform="translate(688,427)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BE756C" transform="translate(680,427)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#C0776C" transform="translate(634,427)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BD796F" transform="translate(373,427)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#AE726A" transform="translate(729,397)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B6766F" transform="translate(466,396)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#AD756E" transform="translate(285,395)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#B5756B" transform="translate(702,391)"/>
<path d="" fill="#B8736B" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#C0756C" transform="translate(650,389)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C1766C" transform="translate(640,379)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C3786C" transform="translate(642,379)"/>
<path d="" fill="#BE786E" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C0786D" transform="translate(688,374)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BB7369" transform="translate(728,371)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C4786D" transform="translate(684,366)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C4776D" transform="translate(683,364)"/>
<path d="" fill="#AA736B" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#BA776E" transform="translate(339,361)"/>
<path d="" fill="#C67B70" transform="translate(0,0)"/>
<path d="" fill="#BA756C" transform="translate(0,0)"/>
<path d="" fill="#C3766B" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C8776B" transform="translate(681,352)"/>
<path d="" fill="#C8766D" transform="translate(0,0)"/>
<path d="" fill="#C1766C" transform="translate(0,0)"/>
<path d="" fill="#C97B70" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#BC7164" transform="translate(765,336)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#D47B6F" transform="translate(645,336)"/>
<path d="" fill="#B0746B" transform="translate(0,0)"/>
<path d="" fill="#CA7A6C" transform="translate(0,0)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CB7B6D" transform="translate(717,327)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#CB796C" transform="translate(730,323)"/>
<path d="" fill="#CE7D71" transform="translate(0,0)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C4796C" transform="translate(693,313)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#C5796D" transform="translate(333,312)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9E9696" transform="translate(520,237)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9B9592" transform="translate(541,230)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9F9C9A" transform="translate(507,228)"/>
<path d="M0 0 C0.66 0.66 1.32 1.32 2 2 C1.34 2 0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#A09E9D" transform="translate(470,210)"/>
<path d="M0 0 C0.99 0.495 0.99 0.495 2 1 C1.01 1.495 1.01 1.495 0 2 C0 1.34 0 0.68 0 0 Z " fill="#9A9694" transform="translate(456,208)"/>
<path d="" fill="#9A9897" transform="translate(0,0)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#AAA8A5" transform="translate(541,180)"/>
<path d="" fill="#ADACAB" transform="translate(0,0)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#979797" transform="translate(449,154)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.34 1.67 -0.32 1.34 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z " fill="#A09C9D" transform="translate(538,124)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.67 0.66 1.34 1.32 1 2 C0.67 1.34 0.34 0.68 0 0 Z " fill="#9E9C9A" transform="translate(516,118)"/>
<path d="M0 0 C3 1 3 1 3 1 Z " fill="#9E9B9B" transform="translate(508,118)"/>
<path d="" fill="#8C8B8B" transform="translate(0,0)"/>
<path d="" fill="#8D8D8D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#909290" transform="translate(468,899)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8E8D8C" transform="translate(607,897)"/>
<path d="" fill="#8E8D8D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#91908F" transform="translate(532,895)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#929191" transform="translate(490,894)"/>
<path d="" fill="#8D8B8D" transform="translate(0,0)"/>
<path d="" fill="#8E8F90" transform="translate(0,0)"/>
<path d="" fill="#929290" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#919090" transform="translate(454,889)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8C8C8B" transform="translate(649,888)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8F8E8D" transform="translate(464,888)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8F8F8F" transform="translate(418,888)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#908E8F" transform="translate(402,888)"/>
<path d="" fill="#8E8E8E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8D8D8D" transform="translate(413,881)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#8E8D8E" transform="translate(438,880)"/>
<path d="" fill="#8F8F8E" transform="translate(0,0)"/>
<path d="" fill="#9E9C9B" transform="translate(0,0)"/>
<path d="" fill="#989898" transform="translate(0,0)"/>
<path d="" fill="#959594" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9B9A99" transform="translate(700,762)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9D9C9B" transform="translate(628,762)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9B9A99" transform="translate(419,762)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#959594" transform="translate(315,762)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#949594" transform="translate(299,762)"/>
<path d="" fill="#A2A2A2" transform="translate(0,0)"/>
<path d="" fill="#A0A09E" transform="translate(0,0)"/>
<path d="" fill="#9D9E9B" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9A9997" transform="translate(307,759)"/>
<path d="" fill="#8F8F8F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9FA09E" transform="translate(666,758)"/>
<path d="" fill="#9F9D9D" transform="translate(0,0)"/>
<path d="" fill="#9D9C9B" transform="translate(0,0)"/>
<path d="" fill="#9D9C9B" transform="translate(0,0)"/>
<path d="" fill="#A2A1A2" transform="translate(0,0)"/>
<path d="" fill="#9C9999" transform="translate(0,0)"/>
<path d="" fill="#A1A09E" transform="translate(0,0)"/>
<path d="" fill="#9E9F9D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9D9D9C" transform="translate(380,754)"/>
<path d="" fill="#A2A2A1" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9E9D9C" transform="translate(701,751)"/>
<path d="" fill="#A0A09E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1A1A0" transform="translate(435,747)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9F9E9D" transform="translate(407,747)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9D9D9B" transform="translate(318,747)"/>
<path d="" fill="#A2A09F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1A1A1" transform="translate(379,746)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9F9D9C" transform="translate(688,744)"/>
<path d="" fill="#A2A0A1" transform="translate(0,0)"/>
<path d="" fill="#A09FA0" transform="translate(0,0)"/>
<path d="" fill="#9E9F9D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9E9F9D" transform="translate(660,740)"/>
<path d="" fill="#9C9B9A" transform="translate(0,0)"/>
<path d="" fill="#A1A29F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A09F9F" transform="translate(638,739)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1A2A1" transform="translate(608,739)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A2A1A1" transform="translate(590,739)"/>
<path d="" fill="#909090" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9D9E9D" transform="translate(665,738)"/>
<path d="" fill="#A2A1A2" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A3A3A3" transform="translate(557,738)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A1A0A0" transform="translate(462,738)"/>
<path d="" fill="#9F9F9D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9D9C9E" transform="translate(377,738)"/>
<path d="" fill="#9E9D9C" transform="translate(0,0)"/>
<path d="" fill="#969595" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#959595" transform="translate(734,737)"/>
<path d="" fill="#9D9D9D" transform="translate(0,0)"/>
<path d="" fill="#A7A5A6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A3A4A4" transform="translate(423,685)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A3A3A2" transform="translate(393,685)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A6A6A4" transform="translate(606,684)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8A7A6" transform="translate(603,684)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A5A3A4" transform="translate(425,684)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9A8A8" transform="translate(494,683)"/>
<path d="" fill="#9E9E9C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8A6A7" transform="translate(548,682)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9A7A6" transform="translate(452,681)"/>
<path d="" fill="#A5A6A4" transform="translate(0,0)"/>
<path d="" fill="#A7A6A6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEACAA" transform="translate(549,679)"/>
<path d="" fill="#A9A7A6" transform="translate(0,0)"/>
<path d="" fill="#AEABAC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AAA9A9" transform="translate(410,676)"/>
<path d="" fill="#ABA9A9" transform="translate(0,0)"/>
<path d="" fill="#B0AEAE" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9A9AA" transform="translate(606,673)"/>
<path d="" fill="#B1B0B1" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEACAC" transform="translate(538,673)"/>
<path d="" fill="#ABAAAA" transform="translate(0,0)"/>
<path d="" fill="#AAABAA" transform="translate(0,0)"/>
<path d="" fill="#AAA8A8" transform="translate(0,0)"/>
<path d="" fill="#AAA9AB" transform="translate(0,0)"/>
<path d="" fill="#ABA9A9" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AAAAA9" transform="translate(455,663)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AEADAD" transform="translate(421,663)"/>
<path d="" fill="#ADABAA" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACACAC" transform="translate(592,661)"/>
<path d="" fill="#AEAEAF" transform="translate(0,0)"/>
<path d="" fill="#A9A8A9" transform="translate(0,0)"/>
<path d="" fill="#ABA9AA" transform="translate(0,0)"/>
<path d="" fill="#AEAFAD" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACA9A9" transform="translate(421,655)"/>
<path d="" fill="#AAAAA8" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ABAAAB" transform="translate(565,652)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A8A7A6" transform="translate(462,651)"/>
<path d="" fill="#A8A7A6" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A6A4A5" transform="translate(446,651)"/>
<path d="" fill="#ACABAC" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A7A7A8" transform="translate(484,650)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A6A6A6" transform="translate(457,650)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9B9A9B" transform="translate(405,641)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9C9B9C" transform="translate(402,641)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AD7A72" transform="translate(558,523)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A87570" transform="translate(440,521)"/>
<path d="" fill="#B0766F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B67871" transform="translate(424,513)"/>
<path d="" fill="#AE746E" transform="translate(0,0)"/>
<path d="" fill="#B7766E" transform="translate(0,0)"/>
<path d="" fill="#AC7169" transform="translate(0,0)"/>
<path d="" fill="#B8766E" transform="translate(0,0)"/>
<path d="" fill="#B7756C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0776F" transform="translate(463,496)"/>
<path d="" fill="#BD7970" transform="translate(0,0)"/>
<path d="" fill="#B7766D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BB796F" transform="translate(347,493)"/>
<path d="" fill="#B7756C" transform="translate(0,0)"/>
<path d="" fill="#BD766E" transform="translate(0,0)"/>
<path d="" fill="#C1776E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BE766B" transform="translate(640,485)"/>
<path d="" fill="#BC756D" transform="translate(0,0)"/>
<path d="" fill="#BE766D" transform="translate(0,0)"/>
<path d="" fill="#B7756C" transform="translate(0,0)"/>
<path d="" fill="#C1786D" transform="translate(0,0)"/>
<path d="" fill="#C4796E" transform="translate(0,0)"/>
<path d="" fill="#BE736A" transform="translate(0,0)"/>
<path d="" fill="#CF7A6F" transform="translate(0,0)"/>
<path d="" fill="#BF766D" transform="translate(0,0)"/>
<path d="" fill="#C4776D" transform="translate(0,0)"/>
<path d="" fill="#C27B6F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C6796C" transform="translate(567,455)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CB7B70" transform="translate(443,455)"/>
<path d="" fill="#C1776C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8776D" transform="translate(546,454)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BE766C" transform="translate(446,454)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C77B71" transform="translate(553,452)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C3786E" transform="translate(454,452)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4796F" transform="translate(362,452)"/>
<path d="" fill="#C5786E" transform="translate(0,0)"/>
<path d="" fill="#BC786F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0776C" transform="translate(379,428)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C1796F" transform="translate(622,427)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C97E72" transform="translate(550,427)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8766D" transform="translate(620,399)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B1746D" transform="translate(721,397)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3756C" transform="translate(391,397)"/>
<path d="" fill="#AE746E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AF726B" transform="translate(719,396)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B8746C" transform="translate(622,396)"/>
<path d="" fill="#C07870" transform="translate(0,0)"/>
<path d="" fill="#BD7973" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B7756E" transform="translate(396,396)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#AD726C" transform="translate(308,396)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BF786F" transform="translate(581,395)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C0786E" transform="translate(578,395)"/>
<path d="" fill="#C0776C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B2726A" transform="translate(361,390)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A9726C" transform="translate(267,387)"/>
<path d="" fill="#C0756B" transform="translate(0,0)"/>
<path d="" fill="#A8726C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BD776D" transform="translate(692,380)"/>
<path d="" fill="#C1776D" transform="translate(0,0)"/>
<path d="" fill="#BF7A70" transform="translate(0,0)"/>
<path d="" fill="#BA766E" transform="translate(0,0)"/>
<path d="" fill="#CC7E74" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BC766D" transform="translate(481,374)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B5756D" transform="translate(344,374)"/>
<path d="" fill="#BE7771" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B3746C" transform="translate(294,370)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BF776D" transform="translate(395,369)"/>
<path d="" fill="#BE746B" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C4756A" transform="translate(633,367)"/>
<path d="" fill="#C2776E" transform="translate(0,0)"/>
<path d="" fill="#C5776E" transform="translate(0,0)"/>
<path d="" fill="#C57A6F" transform="translate(0,0)"/>
<path d="" fill="#C2746A" transform="translate(0,0)"/>
<path d="" fill="#C5786D" transform="translate(0,0)"/>
<path d="" fill="#C5776C" transform="translate(0,0)"/>
<path d="" fill="#CE7D71" transform="translate(0,0)"/>
<path d="" fill="#D07D71" transform="translate(0,0)"/>
<path d="" fill="#CD7C6F" transform="translate(0,0)"/>
<path d="" fill="#B8736D" transform="translate(0,0)"/>
<path d="" fill="#CC776E" transform="translate(0,0)"/>
<path d="" fill="#C7786B" transform="translate(0,0)"/>
<path d="" fill="#D78175" transform="translate(0,0)"/>
<path d="" fill="#C67A6D" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CF7D6F" transform="translate(754,336)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CF796B" transform="translate(742,336)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#D17C6D" transform="translate(682,336)"/>
<path d="" fill="#C3776D" transform="translate(0,0)"/>
<path d="" fill="#C9796B" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CB7A6E" transform="translate(495,333)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BC746B" transform="translate(314,332)"/>
<path d="" fill="#CA7D70" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CB7C70" transform="translate(496,331)"/>
<path d="" fill="#CA7A6D" transform="translate(0,0)"/>
<path d="" fill="#CF7C6F" transform="translate(0,0)"/>
<path d="" fill="#CB7C6F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C57A6D" transform="translate(374,327)"/>
<path d="" fill="#C6786C" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C8786C" transform="translate(721,325)"/>
<path d="" fill="#CC7B6F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C9786B" transform="translate(724,324)"/>
<path d="" fill="#C87B6F" transform="translate(0,0)"/>
<path d="" fill="#C77A6F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C97B71" transform="translate(483,316)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#B56F65" transform="translate(765,312)"/>
<path d="" fill="#C47C6F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C17A6C" transform="translate(699,305)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C57B70" transform="translate(369,301)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BB776C" transform="translate(742,299)"/>
<path d="" fill="#C77D6F" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C07A6E" transform="translate(582,299)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C37E70" transform="translate(542,299)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#CD8175" transform="translate(517,299)"/>
<path d="" fill="#BE7E72" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C07B6E" transform="translate(400,299)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BA766B" transform="translate(737,298)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BF786B" transform="translate(722,298)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#C17C6D" transform="translate(653,298)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#BA766A" transform="translate(305,298)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A39C9A" transform="translate(513,237)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#9D9694" transform="translate(524,236)"/>
<path d="" fill="#A19A98" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A29D9B" transform="translate(553,221)"/>
<path d="" fill="#9D9897" transform="translate(0,0)"/>
<path d="" fill="#9B9795" transform="translate(0,0)"/>
<path d="" fill="#9B9796" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#A3A19F" transform="translate(469,191)"/>
<path d="" fill="#A4A2A1" transform="translate(0,0)"/>
<path d="" fill="#A2A09F" transform="translate(0,0)"/>
<path d="" fill="#ADACAA" transform="translate(0,0)"/>
<path d="" fill="#A5A4A3" transform="translate(0,0)"/>
<path d="" fill="#A3A1A0" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#ACA9A9" transform="translate(476,158)"/>
<path d="" fill="#A2A19F" transform="translate(0,0)"/>
<path d="" fill="#999797" transform="translate(0,0)"/>
<path d="" fill="#989796" transform="translate(0,0)"/>
<path d="" fill="#989695" transform="translate(0,0)"/>
<path d="" fill="#A09E9E" transform="translate(0,0)"/>
<path d="M0 0 C2 1 2 1 2 1 Z " fill="#999797" transform="translate(520,109)"/>
<path d="" fill="#999797" transform="translate(0,0)"/>
<path d="" fill="#545555" transform="translate(0,0)"/>
<path d="" fill="#535554" transform="translate(0,0)"/>
<path d="" fill="#6F6E6D" transform="translate(0,0)"/>
<path d="" fill="#676867" transform="translate(0,0)"/>
<path d="" fill="#595A5A" transform="translate(0,0)"/>
<path d="" fill="#585758" transform="translate(0,0)"/>
<path d="" fill="#8B8C8C" transform="translate(0,0)"/>
<path d="" fill="#8C8988" transform="translate(0,0)"/>
<path d="" fill="#8A8988" transform="translate(0,0)"/>
<path d="" fill="#8A8B8A" transform="translate(0,0)"/>
<path d="" fill="#8A898A" transform="translate(0,0)"/>
<path d="" fill="#8A8A8A" transform="translate(0,0)"/>
<path d="" fill="#8E8E8C" transform="translate(0,0)"/>
<path d="" fill="#808381" transform="translate(0,0)"/>
<path d="" fill="#8E8D8B" transform="translate(0,0)"/>
<path d="" fill="#8F908F" transform="translate(0,0)"/>
<path d="" fill="#969492" transform="translate(0,0)"/>
<path d="" fill="#908E8D" transform="translate(0,0)"/>
<path d="" fill="#8C8D8A" transform="translate(0,0)"/>
<path d="" fill="#90918E" transform="translate(0,0)"/>
<path d="" fill="#8D8D8B" transform="translate(0,0)"/>
<path d="" fill="#8E8D8C" transform="translate(0,0)"/>
<path d="" fill="#908E8F" transform="translate(0,0)"/>
<path d="" fill="#8C8B8C" transform="translate(0,0)"/>
<path d="" fill="#918E8D" transform="translate(0,0)"/>
<path d="" fill="#8E8E8C" transform="translate(0,0)"/>
<path d="" fill="#909290" transform="translate(0,0)"/>
<path d="" fill="#8C8D8C" transform="translate(0,0)"/>
<path d="" fill="#908F8E" transform="translate(0,0)"/>
<path d="" fill="#8E8E8D" transform="translate(0,0)"/>
<path d="" fill="#8E8E8C" transform="translate(0,0)"/>
<path d="" fill="#939190" transform="translate(0,0)"/>
<path d="" fill="#878788" transform="translate(0,0)"/>
<path d="" fill="#8C8D8C" transform="translate(0,0)"/>
<path d="" fill="#8B8C8A" transform="translate(0,0)"/>
<path d="" fill="#8F9090" transform="translate(0,0)"/>
<path d="" fill="#929191" transform="translate(0,0)"/>
<path d="" fill="#929291" transform="translate(0,0)"/>
<path d="" fill="#909091" transform="translate(0,0)"/>
<path d="" fill="#939393" transform="translate(0,0)"/>
<path d="" fill="#908E90" transform="translate(0,0)"/>
<path d="" fill="#91908F" transform="translate(0,0)"/>
<path d="" fill="#959193" transform="translate(0,0)"/>
<path d="" fill="#8E8E8C" transform="translate(0,0)"/>
<path d="" fill="#8E8E8D" transform="translate(0,0)"/>
<path d="" fill="#8C8E8F" transform="translate(0,0)"/>
<path d="" fill="#919190" transform="translate(0,0)"/>
<path d="" fill="#8E8C8D" transform="translate(0,0)"/>
<path d="" fill="#949595" transform="translate(0,0)"/>
<path d="" fill="#919191" transform="translate(0,0)"/>
<path d="" fill="#918F8E" transform="translate(0,0)"/>
<path d="" fill="#8E908E" transform="translate(0,0)"/>
<path d="" fill="#929191" transform="translate(0,0)"/>
<path d="" fill="#929191" transform="translate(0,0)"/>
<path d="" fill="#8F928F" transform="translate(0,0)"/>
<path d="" fill="#8F908F" transform="translate(0,0)"/>
<path d="" fill="#8F8D8B" transform="translate(0,0)"/>
<path d="" fill="#918E8F" transform="translate(0,0)"/>
<path d="" fill="#8B8A8B" transform="translate(0,0)"/>
<path d="" fill="#928F91" transform="translate(0,0)"/>
<path d="" fill="#8D8E8D" transform="translate(0,0)"/>
<path d="" fill="#8B8B89" transform="translate(0,0)"/>
<path d="" fill="#8E8E8D" transform="translate(0,0)"/>
<path d="" fill="#8E8E8E" transform="translate(0,0)"/>
<path d="" fill="#908F8D" transform="translate(0,0)"/>
<path d="" fill="#90908F" transform="translate(0,0)"/>
<path d="" fill="#8E8E90" transform="translate(0,0)"/>
<path d="" fill="#8B8B8D" transform="translate(0,0)"/>
<path d="" fill="#8E8E8E" transform="translate(0,0)"/>
<path d="" fill="#908F8F" transform="translate(0,0)"/>
<path d="" fill="#91908E" transform="translate(0,0)"/>
<path d="" fill="#8A8A88" transform="translate(0,0)"/>
<path d="" fill="#7A7B78" transform="translate(0,0)"/>
<path d="" fill="#9B9B99" transform="translate(0,0)"/>
<path d="" fill="#9B9C9A" transform="translate(0,0)"/>
<path d="" fill="#979895" transform="translate(0,0)"/>
<path d="" fill="#9E9C9D" transform="translate(0,0)"/>
<path d="" fill="#9E9B9B" transform="translate(0,0)"/>
<path d="" fill="#9B9A9A" transform="translate(0,0)"/>
<path d="" fill="#9E9E9D" transform="translate(0,0)"/>
<path d="" fill="#A1A0A0" transform="translate(0,0)"/>
<path d="" fill="#999996" transform="translate(0,0)"/>
<path d="" fill="#959391" transform="translate(0,0)"/>
<path d="" fill="#969793" transform="translate(0,0)"/>
<path d="" fill="#999898" transform="translate(0,0)"/>
<path d="" fill="#9D9C9B" transform="translate(0,0)"/>
<path d="" fill="#9B9D9A" transform="translate(0,0)"/>
<path d="" fill="#9E9D9C" transform="translate(0,0)"/>
<path d="" fill="#9E9E9D" transform="translate(0,0)"/>
<path d="" fill="#A0A09E" transform="translate(0,0)"/>
<path d="" fill="#9FA09F" transform="translate(0,0)"/>
<path d="" fill="#A09F9D" transform="translate(0,0)"/>
<path d="" fill="#9D9D9D" transform="translate(0,0)"/>
<path d="" fill="#9D9E9C" transform="translate(0,0)"/>
<path d="" fill="#9E9E9D" transform="translate(0,0)"/>
<path d="" fill="#9E9B9D" transform="translate(0,0)"/>
<path d="" fill="#9B9C9A" transform="translate(0,0)"/>
<path d="" fill="#9B9A9A" transform="translate(0,0)"/>
<path d="" fill="#9F9E9D" transform="translate(0,0)"/>
<path d="" fill="#9E9E9D" transform="translate(0,0)"/>
<path d="" fill="#9B9B9B" transform="translate(0,0)"/>
<path d="" fill="#9B9A99" transform="translate(0,0)"/>
<path d="" fill="#9A9999" transform="translate(0,0)"/>
<path d="" fill="#979695" transform="translate(0,0)"/>
<path d="" fill="#929191" transform="translate(0,0)"/>
<path d="" fill="#9D9E9B" transform="translate(0,0)"/>
<path d="" fill="#9E9C9C" transform="translate(0,0)"/>
<path d="" fill="#9F9F9D" transform="translate(0,0)"/>
<path d="" fill="#9D9E9C" transform="translate(0,0)"/>
<path d="" fill="#9D9D9C" transform="translate(0,0)"/>
<path d="" fill="#9D9D9D" transform="translate(0,0)"/>
<path d="" fill="#9F9F9D" transform="translate(0,0)"/>
<path d="" fill="#A1A09D" transform="translate(0,0)"/>
<path d="" fill="#9F9D9D" transform="translate(0,0)"/>
<path d="" fill="#9C9C9A" transform="translate(0,0)"/>
<path d="" fill="#9E9E9B" transform="translate(0,0)"/>
<path d="" fill="#9D9C9C" transform="translate(0,0)"/>
<path d="" fill="#9F9F9D" transform="translate(0,0)"/>
<path d="" fill="#9F9D9D" transform="translate(0,0)"/>
<path d="" fill="#A19E9E" transform="translate(0,0)"/>
<path d="" fill="#9C9A98" transform="translate(0,0)"/>
<path d="" fill="#A2A2A1" transform="translate(0,0)"/>
<path d="" fill="#A0A0A0" transform="translate(0,0)"/>
<path d="" fill="#A5A4A4" transform="translate(0,0)"/>
<path d="" fill="#9E9D9D" transform="translate(0,0)"/>
<path d="" fill="#939192" transform="translate(0,0)"/>
<path d="" fill="#8F8F90" transform="translate(0,0)"/>
<path d="" fill="#A09F9F" transform="translate(0,0)"/>
<path d="" fill="#969695" transform="translate(0,0)"/>
<path d="" fill="#989997" transform="translate(0,0)"/>
<path d="" fill="#989695" transform="translate(0,0)"/>
<path d="" fill="#999895" transform="translate(0,0)"/>
<path d="" fill="#9E9D9D" transform="translate(0,0)"/>
<path d="" fill="#9B9A97" transform="translate(0,0)"/>
<path d="" fill="#A3A4A1" transform="translate(0,0)"/>
<path d="" fill="#A1A19E" transform="translate(0,0)"/>
<path d="" fill="#A09E9E" transform="translate(0,0)"/>
<path d="" fill="#939293" transform="translate(0,0)"/>
<path d="" fill="#A0A09F" transform="translate(0,0)"/>
<path d="" fill="#A19D9B" transform="translate(0,0)"/>
<path d="" fill="#9E9E9D" transform="translate(0,0)"/>
<path d="" fill="#A19F9F" transform="translate(0,0)"/>
<path d="" fill="#A7A7A5" transform="translate(0,0)"/>
<path d="" fill="#A1A0A0" transform="translate(0,0)"/>
<path d="" fill="#9F9E9D" transform="translate(0,0)"/>
<path d="" fill="#939292" transform="translate(0,0)"/>
<path d="" fill="#9D9D9E" transform="translate(0,0)"/>
<path d="" fill="#9E9B9A" transform="translate(0,0)"/>
<path d="" fill="#A3A1A0" transform="translate(0,0)"/>
<path d="" fill="#A3A0A0" transform="translate(0,0)"/>
<path d="" fill="#A0A0A0" transform="translate(0,0)"/>
<path d="" fill="#A2A1A2" transform="translate(0,0)"/>
<path d="" fill="#A1A19F" transform="translate(0,0)"/>
<path d="" fill="#9A9A99" transform="translate(0,0)"/>
<path d="" fill="#918F90" transform="translate(0,0)"/>
<path d="" fill="#9E9D9E" transform="translate(0,0)"/>
<path d="" fill="#A4A4A2" transform="translate(0,0)"/>
<path d="" fill="#969696" transform="translate(0,0)"/>
<path d="" fill="#A1A1A1" transform="translate(0,0)"/>
<path d="" fill="#A3A1A3" transform="translate(0,0)"/>
<path d="" fill="#A2A1A0" transform="translate(0,0)"/>
<path d="" fill="#9C9B9A" transform="translate(0,0)"/>
<path d="" fill="#9C9B99" transform="translate(0,0)"/>
<path d="" fill="#A1A09D" transform="translate(0,0)"/>
<path d="" fill="#A2A09F" transform="translate(0,0)"/>
<path d="" fill="#A1A1A0" transform="translate(0,0)"/>
<path d="" fill="#9F9D9E" transform="translate(0,0)"/>
<path d="" fill="#A1A0A0" transform="translate(0,0)"/>
<path d="" fill="#A3A4A1" transform="translate(0,0)"/>
<path d="" fill="#A1A1A1" transform="translate(0,0)"/>
<path d="" fill="#9E9D9D" transform="translate(0,0)"/>
<path d="" fill="#9E9D9D" transform="translate(0,0)"/>
<path d="" fill="#A3A3A1" transform="translate(0,0)"/>
<path d="" fill="#A3A3A3" transform="translate(0,0)"/>
<path d="" fill="#A1A1A1" transform="translate(0,0)"/>
<path d="" fill="#989997" transform="translate(0,0)"/>
<path d="" fill="#9D9E9D" transform="translate(0,0)"/>
<path d="" fill="#9E9E9C" transform="translate(0,0)"/>
<path d="" fill="#999B9A" transform="translate(0,0)"/>
<path d="" fill="#999998" transform="translate(0,0)"/>
<path d="" fill="#A5A3A2" transform="translate(0,0)"/>
<path d="" fill="#989998" transform="translate(0,0)"/>
<path d="" fill="#919191" transform="translate(0,0)"/>
<path d="" fill="#A09F9D" transform="translate(0,0)"/>
<path d="" fill="#9E9B99" transform="translate(0,0)"/>
<path d="" fill="#AAA8A8" transform="translate(0,0)"/>
<path d="" fill="#A1A29F" transform="translate(0,0)"/>
<path d="" fill="#A5A6A4" transform="translate(0,0)"/>
<path d="" fill="#A1A2A1" transform="translate(0,0)"/>
<path d="" fill="#9D9C9B" transform="translate(0,0)"/>
<path d="" fill="#9E9B9A" transform="translate(0,0)"/>
<path d="" fill="#9F9E9D" transform="translate(0,0)"/>
<path d="" fill="#A3A4A4" transform="translate(0,0)"/>
<path d="" fill="#A1A1A0" transform="translate(0,0)"/>
<path d="" fill="#9FA19F" transform="translate(0,0)"/>
<path d="" fill="#A1A0A0" transform="translate(0,0)"/>
<path d="" fill="#9E9E9D" transform="translate(0,0)"/>
<path d="" fill="#A8A6A4" transform="translate(0,0)"/>
<path d="" fill="#A3A2A3" transform="translate(0,0)"/>
<path d="" fill="#9E9C9C" transform="translate(0,0)"/>
<path d="" fill="#9D9D9D" transform="translate(0,0)"/>
<path d="" fill="#9D9D9C" transform="translate(0,0)"/>
<path d="" fill="#A19F9F" transform="translate(0,0)"/>
<path d="" fill="#959493" transform="translate(0,0)"/>
<path d="" fill="#9B989A" transform="translate(0,0)"/>
<path d="" fill="#A19FA0" transform="translate(0,0)"/>
<path d="" fill="#A1A09F" transform="translate(0,0)"/>
<path d="" fill="#9F9F9F" transform="translate(0,0)"/>
<path d="" fill="#9D9B9D" transform="translate(0,0)"/>
<path d="" fill="#A0A3A2" transform="translate(0,0)"/>
<path d="" fill="#A2A2A2" transform="translate(0,0)"/>
<path d="" fill="#9D9D9F" transform="translate(0,0)"/>
<path d="" fill="#9B9D9D" transform="translate(0,0)"/>
<path d="" fill="#98999A" transform="translate(0,0)"/>
<path d="" fill="#9B9B99" transform="translate(0,0)"/>
<path d="" fill="#939593" transform="translate(0,0)"/>
<path d="" fill="#999796" transform="translate(0,0)"/>
<path d="" fill="#9D9C9B" transform="translate(0,0)"/>
<path d="" fill="#9C9C9B" transform="translate(0,0)"/>
<path d="" fill="#9B9B98" transform="translate(0,0)"/>
<path d="" fill="#9F9F9E" transform="translate(0,0)"/>
<path d="" fill="#A2A2A1" transform="translate(0,0)"/>
<path d="" fill="#A0A1A0" transform="translate(0,0)"/>
<path d="" fill="#A4A09D" transform="translate(0,0)"/>
<path d="" fill="#A3A1A1" transform="translate(0,0)"/>
<path d="" fill="#9FA09F" transform="translate(0,0)"/>
<path d="" fill="#A4A5A3" transform="translate(0,0)"/>
<path d="" fill="#A4A3A3" transform="translate(0,0)"/>
<path d="" fill="#A6A6A7" transform="translate(0,0)"/>
<path d="" fill="#A3A4A3" transform="translate(0,0)"/>
<path d="" fill="#A4A4A3" transform="translate(0,0)"/>
<path d="" fill="#A3A1A1" transform="translate(0,0)"/>
<path d="" fill="#A3A3A2" transform="translate(0,0)"/>
<path d="" fill="#A4A3A3" transform="translate(0,0)"/>
<path d="" fill="#A3A3A2" transform="translate(0,0)"/>
<path d="" fill="#A4A3A2" transform="translate(0,0)"/>
<path d="" fill="#A1A3A2" transform="translate(0,0)"/>
<path d="" fill="#A1A0A1" transform="translate(0,0)"/>
<path d="" fill="#9E9D9D" transform="translate(0,0)"/>
<path d="" fill="#9B9B99" transform="translate(0,0)"/>
<path d="" fill="#979696" transform="translate(0,0)"/>
<path d="" fill="#9B9B9A" transform="translate(0,0)"/>
<path d="" fill="#9C9B9B" transform="translate(0,0)"/>
<path d="" fill="#9D9D9C" transform="translate(0,0)"/>
<path d="" fill="#9E9E9D" transform="translate(0,0)"/>
<path d="" fill="#A2A2A2" transform="translate(0,0)"/>
<path d="" fill="#A4A2A3" transform="translate(0,0)"/>
<path d="" fill="#A2A3A2" transform="translate(0,0)"/>
<path d="" fill="#A3A5A3" transform="translate(0,0)"/>
<path d="" fill="#A0A19F" transform="translate(0,0)"/>
<path d="" fill="#A3A1A3" transform="translate(0,0)"/>
<path d="" fill="#A3A4A3" transform="translate(0,0)"/>
<path d="" fill="#A0A1A1" transform="translate(0,0)"/>
<path d="" fill="#A09E9F" transform="translate(0,0)"/>
<path d="" fill="#9F9E9F" transform="translate(0,0)"/>
<path d="" fill="#9E9D9E" transform="translate(0,0)"/>
<path d="" fill="#9D9D9E" transform="translate(0,0)"/>
<path d="" fill="#9C9B9C" transform="translate(0,0)"/>
<path d="" fill="#999898" transform="translate(0,0)"/>
<path d="" fill="#9C9B9B" transform="translate(0,0)"/>
<path d="" fill="#9F9E9B" transform="translate(0,0)"/>
<path d="" fill="#A2A0A2" transform="translate(0,0)"/>
<path d="" fill="#A1A1A2" transform="translate(0,0)"/>
<path d="" fill="#A4A3A5" transform="translate(0,0)"/>
<path d="" fill="#A0A2A3" transform="translate(0,0)"/>
<path d="" fill="#A2A3A3" transform="translate(0,0)"/>
<path d="" fill="#A1A3A1" transform="translate(0,0)"/>
<path d="" fill="#A1A1A1" transform="translate(0,0)"/>
<path d="" fill="#A7A6A6" transform="translate(0,0)"/>
<path d="" fill="#A1A2A1" transform="translate(0,0)"/>
<path d="" fill="#A0A0A0" transform="translate(0,0)"/>
<path d="" fill="#9E9E9D" transform="translate(0,0)"/>
<path d="" fill="#9C9D9D" transform="translate(0,0)"/>
<path d="" fill="#999898" transform="translate(0,0)"/>
<path d="" fill="#9A9B9A" transform="translate(0,0)"/>
<path d="" fill="#9B9B9B" transform="translate(0,0)"/>
<path d="" fill="#9B989B" transform="translate(0,0)"/>
<path d="" fill="#A09F9E" transform="translate(0,0)"/>
<path d="" fill="#A0A0A2" transform="translate(0,0)"/>
<path d="" fill="#A5A3A4" transform="translate(0,0)"/>
<path d="" fill="#A5A3A3" transform="translate(0,0)"/>
<path d="" fill="#A3A2A2" transform="translate(0,0)"/>
<path d="" fill="#A3A3A2" transform="translate(0,0)"/>
<path d="" fill="#9FA0A0" transform="translate(0,0)"/>
<path d="" fill="#9E9F9E" transform="translate(0,0)"/>
<path d="" fill="#9B9E9B" transform="translate(0,0)"/>
<path d="" fill="#9B9A9A" transform="translate(0,0)"/>
<path d="" fill="#9A9C9A" transform="translate(0,0)"/>
<path d="" fill="#A1A1A1" transform="translate(0,0)"/>
<path d="" fill="#949392" transform="translate(0,0)"/>
<path d="" fill="#929193" transform="translate(0,0)"/>
<path d="" fill="#949393" transform="translate(0,0)"/>
<path d="" fill="#939393" transform="translate(0,0)"/>
<path d="" fill="#A8A8A7" transform="translate(0,0)"/>
<path d="" fill="#A7A6A7" transform="translate(0,0)"/>
<path d="" fill="#A5A7A5" transform="translate(0,0)"/>
<path d="" fill="#A7A6A7" transform="translate(0,0)"/>
<path d="" fill="#A5A5A4" transform="translate(0,0)"/>
<path d="" fill="#A1A1A1" transform="translate(0,0)"/>
<path d="" fill="#A7A5A7" transform="translate(0,0)"/>
<path d="" fill="#A7A6A6" transform="translate(0,0)"/>
<path d="" fill="#A8A7A7" transform="translate(0,0)"/>
<path d="" fill="#A7A6A7" transform="translate(0,0)"/>
<path d="" fill="#A6A6A5" transform="translate(0,0)"/>
<path d="" fill="#A7A6A5" transform="translate(0,0)"/>
<path d="" fill="#A5A6A8" transform="translate(0,0)"/>
<path d="" fill="#A7A7A7" transform="translate(0,0)"/>
<path d="" fill="#A3A2A2" transform="translate(0,0)"/>
<path d="" fill="#A5A6A6" transform="translate(0,0)"/>
<path d="" fill="#A09F9E" transform="translate(0,0)"/>
<path d="" fill="#A5A3A5" transform="translate(0,0)"/>
<path d="" fill="#A5A6A4" transform="translate(0,0)"/>
<path d="" fill="#A8A6A6" transform="translate(0,0)"/>
<path d="" fill="#A7A6A7" transform="translate(0,0)"/>
<path d="" fill="#A8A7A7" transform="translate(0,0)"/>
<path d="" fill="#A6A6A8" transform="translate(0,0)"/>
<path d="" fill="#A7A6A8" transform="translate(0,0)"/>
<path d="" fill="#A9A6A7" transform="translate(0,0)"/>
<path d="" fill="#A8A6A7" transform="translate(0,0)"/>
<path d="" fill="#A7A9AA" transform="translate(0,0)"/>
<path d="" fill="#ABA9A9" transform="translate(0,0)"/>
<path d="" fill="#A6A7A6" transform="translate(0,0)"/>
<path d="" fill="#A9A7A7" transform="translate(0,0)"/>
<path d="" fill="#AFAEAB" transform="translate(0,0)"/>
<path d="" fill="#A7A6A6" transform="translate(0,0)"/>
<path d="" fill="#AAABAA" transform="translate(0,0)"/>
<path d="" fill="#AAAAAA" transform="translate(0,0)"/>
<path d="" fill="#AAAAA9" transform="translate(0,0)"/>
<path d="" fill="#ACABAA" transform="translate(0,0)"/>
<path d="" fill="#ABAAA9" transform="translate(0,0)"/>
<path d="" fill="#ABAAAA" transform="translate(0,0)"/>
<path d="" fill="#A7A7A7" transform="translate(0,0)"/>
<path d="" fill="#ABA9A8" transform="translate(0,0)"/>
<path d="" fill="#AAACAA" transform="translate(0,0)"/>
<path d="" fill="#ABAAA9" transform="translate(0,0)"/>
<path d="" fill="#A7A6A6" transform="translate(0,0)"/>
<path d="" fill="#ABABAB" transform="translate(0,0)"/>
<path d="" fill="#B0B0AF" transform="translate(0,0)"/>
<path d="" fill="#A9AAAA" transform="translate(0,0)"/>
<path d="" fill="#AFACAD" transform="translate(0,0)"/>
<path d="" fill="#ACAAAC" transform="translate(0,0)"/>
<path d="" fill="#AAA8AA" transform="translate(0,0)"/>
<path d="" fill="#AEAFAF" transform="translate(0,0)"/>
<path d="" fill="#A7A7A7" transform="translate(0,0)"/>
<path d="" fill="#ADADAC" transform="translate(0,0)"/>
<path d="" fill="#AFAEAF" transform="translate(0,0)"/>
<path d="" fill="#AAA9A9" transform="translate(0,0)"/>
<path d="" fill="#ACAAAC" transform="translate(0,0)"/>
<path d="" fill="#AFAFAF" transform="translate(0,0)"/>
<path d="" fill="#AAABA8" transform="translate(0,0)"/>
<path d="" fill="#AEACAD" transform="translate(0,0)"/>
<path d="" fill="#ABAAAA" transform="translate(0,0)"/>
<path d="" fill="#AFADAE" transform="translate(0,0)"/>
<path d="" fill="#ACACA9" transform="translate(0,0)"/>
<path d="" fill="#ACA9A9" transform="translate(0,0)"/>
<path d="" fill="#AEADAC" transform="translate(0,0)"/>
<path d="" fill="#ACABAC" transform="translate(0,0)"/>
<path d="" fill="#AFAEAE" transform="translate(0,0)"/>
<path d="" fill="#AEAEAE" transform="translate(0,0)"/>
<path d="" fill="#B3B1B2" transform="translate(0,0)"/>
<path d="" fill="#ADABAB" transform="translate(0,0)"/>
<path d="" fill="#ADACAB" transform="translate(0,0)"/>
<path d="" fill="#B9B7B5" transform="translate(0,0)"/>
<path d="" fill="#ADADAC" transform="translate(0,0)"/>
<path d="" fill="#A8A7A6" transform="translate(0,0)"/>
<path d="" fill="#ACAAA9" transform="translate(0,0)"/>
<path d="" fill="#ACA9A9" transform="translate(0,0)"/>
<path d="" fill="#B1B0AF" transform="translate(0,0)"/>
<path d="" fill="#B1B1B1" transform="translate(0,0)"/>
<path d="" fill="#AFAFAD" transform="translate(0,0)"/>
<path d="" fill="#A9AAA9" transform="translate(0,0)"/>
<path d="" fill="#ACAAA9" transform="translate(0,0)"/>
<path d="" fill="#ACAAA8" transform="translate(0,0)"/>
<path d="" fill="#B2B0AF" transform="translate(0,0)"/>
<path d="" fill="#ADADAB" transform="translate(0,0)"/>
<path d="" fill="#ABABAB" transform="translate(0,0)"/>
<path d="" fill="#AFADAD" transform="translate(0,0)"/>
<path d="" fill="#B1B0B1" transform="translate(0,0)"/>
<path d="" fill="#ADABAA" transform="translate(0,0)"/>
<path d="" fill="#B1B1B1" transform="translate(0,0)"/>
<path d="" fill="#B2B0AF" transform="translate(0,0)"/>
<path d="" fill="#ABAAAB" transform="translate(0,0)"/>
<path d="" fill="#AEADAD" transform="translate(0,0)"/>
<path d="" fill="#ACABAA" transform="translate(0,0)"/>
<path d="" fill="#ADAAAB" transform="translate(0,0)"/>
<path d="" fill="#B3B2B0" transform="translate(0,0)"/>
<path d="" fill="#AAA9A9" transform="translate(0,0)"/>
<path d="" fill="#AFADAD" transform="translate(0,0)"/>
<path d="" fill="#AAA9A8" transform="translate(0,0)"/>
<path d="" fill="#ACA9AB" transform="translate(0,0)"/>
<path d="" fill="#ABAAAA" transform="translate(0,0)"/>
<path d="" fill="#ADACAC" transform="translate(0,0)"/>
<path d="" fill="#A9AAA8" transform="translate(0,0)"/>
<path d="" fill="#A9A8A7" transform="translate(0,0)"/>
<path d="" fill="#ACAAAA" transform="translate(0,0)"/>
<path d="" fill="#AFADAE" transform="translate(0,0)"/>
<path d="" fill="#ABA8A9" transform="translate(0,0)"/>
<path d="" fill="#B2AFB0" transform="translate(0,0)"/>
<path d="" fill="#AEADAC" transform="translate(0,0)"/>
<path d="" fill="#ADACAB" transform="translate(0,0)"/>
<path d="" fill="#AAAAAA" transform="translate(0,0)"/>
<path d="" fill="#ABABAA" transform="translate(0,0)"/>
<path d="" fill="#A9A8A8" transform="translate(0,0)"/>
<path d="" fill="#A8A5A6" transform="translate(0,0)"/>
<path d="" fill="#ADADAE" transform="translate(0,0)"/>
<path d="" fill="#ABA9A9" transform="translate(0,0)"/>
<path d="" fill="#A9A9A6" transform="translate(0,0)"/>
<path d="" fill="#A5A4A3" transform="translate(0,0)"/>
<path d="" fill="#A9A6A7" transform="translate(0,0)"/>
<path d="" fill="#A8A8A7" transform="translate(0,0)"/>
<path d="" fill="#A3A4A2" transform="translate(0,0)"/>
<path d="" fill="#A6A7A6" transform="translate(0,0)"/>
<path d="" fill="#A6A6A7" transform="translate(0,0)"/>
<path d="" fill="#A9A8A7" transform="translate(0,0)"/>
<path d="" fill="#A7A6A6" transform="translate(0,0)"/>
<path d="" fill="#A6A4A6" transform="translate(0,0)"/>
<path d="" fill="#AEACAA" transform="translate(0,0)"/>
<path d="" fill="#A9A7A6" transform="translate(0,0)"/>
<path d="" fill="#ACACAA" transform="translate(0,0)"/>
<path d="" fill="#A7A7A7" transform="translate(0,0)"/>
<path d="" fill="#A7A7A6" transform="translate(0,0)"/>
<path d="" fill="#A5A3A5" transform="translate(0,0)"/>
<path d="" fill="#A7A5A5" transform="translate(0,0)"/>
<path d="" fill="#AAA8A8" transform="translate(0,0)"/>
<path d="" fill="#A7A5A4" transform="translate(0,0)"/>
<path d="" fill="#A6A6A5" transform="translate(0,0)"/>
<path d="" fill="#5C5C5B" transform="translate(0,0)"/>
<path d="" fill="#636364" transform="translate(0,0)"/>
<path d="" fill="#A57570" transform="translate(0,0)"/>
<path d="" fill="#AF7B72" transform="translate(0,0)"/>
<path d="" fill="#AE7A73" transform="translate(0,0)"/>
<path d="" fill="#AD7972" transform="translate(0,0)"/>
<path d="" fill="#A9766F" transform="translate(0,0)"/>
<path d="" fill="#AA766F" transform="translate(0,0)"/>
<path d="" fill="#A97670" transform="translate(0,0)"/>
<path d="" fill="#AE7972" transform="translate(0,0)"/>
<path d="" fill="#AD786F" transform="translate(0,0)"/>
<path d="" fill="#B07A74" transform="translate(0,0)"/>
<path d="" fill="#AC7871" transform="translate(0,0)"/>
<path d="" fill="#A5736C" transform="translate(0,0)"/>
<path d="" fill="#9E716C" transform="translate(0,0)"/>
<path d="" fill="#AB7873" transform="translate(0,0)"/>
<path d="" fill="#AA746E" transform="translate(0,0)"/>
<path d="" fill="#B27972" transform="translate(0,0)"/>
<path d="" fill="#AC736D" transform="translate(0,0)"/>
<path d="" fill="#A5726B" transform="translate(0,0)"/>
<path d="" fill="#A3716A" transform="translate(0,0)"/>
<path d="" fill="#B27971" transform="translate(0,0)"/>
<path d="" fill="#B0786E" transform="translate(0,0)"/>
<path d="" fill="#B27872" transform="translate(0,0)"/>
<path d="" fill="#AD736E" transform="translate(0,0)"/>
<path d="" fill="#AD716B" transform="translate(0,0)"/>
<path d="" fill="#AE746D" transform="translate(0,0)"/>
<path d="" fill="#AC7670" transform="translate(0,0)"/>
<path d="" fill="#B2746A" transform="translate(0,0)"/>
<path d="" fill="#AC7269" transform="translate(0,0)"/>
<path d="" fill="#AB736C" transform="translate(0,0)"/>
<path d="" fill="#B17670" transform="translate(0,0)"/>
<path d="" fill="#AD706B" transform="translate(0,0)"/>
<path d="" fill="#AF756E" transform="translate(0,0)"/>
<path d="" fill="#B77571" transform="translate(0,0)"/>
<path d="" fill="#B1756F" transform="translate(0,0)"/>
<path d="" fill="#B1766E" transform="translate(0,0)"/>
<path d="" fill="#B5776E" transform="translate(0,0)"/>
<path d="" fill="#B0756E" transform="translate(0,0)"/>
<path d="" fill="#BC7971" transform="translate(0,0)"/>
<path d="" fill="#B87970" transform="translate(0,0)"/>
<path d="" fill="#B1746D" transform="translate(0,0)"/>
<path d="" fill="#B7766F" transform="translate(0,0)"/>
<path d="" fill="#B2726B" transform="translate(0,0)"/>
<path d="" fill="#B0736D" transform="translate(0,0)"/>
<path d="" fill="#B6776F" transform="translate(0,0)"/>
<path d="" fill="#B5766D" transform="translate(0,0)"/>
<path d="" fill="#AE736C" transform="translate(0,0)"/>
<path d="" fill="#B0756E" transform="translate(0,0)"/>
<path d="" fill="#B5766E" transform="translate(0,0)"/>
<path d="" fill="#B5776D" transform="translate(0,0)"/>
<path d="" fill="#C17A6F" transform="translate(0,0)"/>
<path d="" fill="#B6746C" transform="translate(0,0)"/>
<path d="" fill="#BD776E" transform="translate(0,0)"/>
<path d="" fill="#B2756B" transform="translate(0,0)"/>
<path d="" fill="#B6766C" transform="translate(0,0)"/>
<path d="" fill="#B0726A" transform="translate(0,0)"/>
<path d="" fill="#B97770" transform="translate(0,0)"/>
<path d="" fill="#B8776D" transform="translate(0,0)"/>
<path d="" fill="#BE766E" transform="translate(0,0)"/>
<path d="" fill="#C77F75" transform="translate(0,0)"/>
<path d="" fill="#B1756B" transform="translate(0,0)"/>
<path d="" fill="#BA796F" transform="translate(0,0)"/>
<path d="" fill="#B7776E" transform="translate(0,0)"/>
<path d="" fill="#B6746B" transform="translate(0,0)"/>
<path d="" fill="#C97971" transform="translate(0,0)"/>
<path d="" fill="#B3756C" transform="translate(0,0)"/>
<path d="" fill="#B5756D" transform="translate(0,0)"/>
<path d="" fill="#BB746B" transform="translate(0,0)"/>
<path d="" fill="#B6766D" transform="translate(0,0)"/>
<path d="" fill="#B4756C" transform="translate(0,0)"/>
<path d="" fill="#BB736B" transform="translate(0,0)"/>
<path d="" fill="#B4736B" transform="translate(0,0)"/>
<path d="" fill="#B5756D" transform="translate(0,0)"/>
<path d="" fill="#BA746B" transform="translate(0,0)"/>
<path d="" fill="#B9756E" transform="translate(0,0)"/>
<path d="" fill="#B2776C" transform="translate(0,0)"/>
<path d="" fill="#B4746A" transform="translate(0,0)"/>
<path d="" fill="#BF746D" transform="translate(0,0)"/>
<path d="" fill="#B9776E" transform="translate(0,0)"/>
<path d="" fill="#C1786E" transform="translate(0,0)"/>
<path d="" fill="#B3726A" transform="translate(0,0)"/>
<path d="" fill="#B7746E" transform="translate(0,0)"/>
<path d="" fill="#CA7B72" transform="translate(0,0)"/>
<path d="" fill="#C47A6F" transform="translate(0,0)"/>
<path d="" fill="#BD766C" transform="translate(0,0)"/>
<path d="" fill="#BB756B" transform="translate(0,0)"/>
<path d="" fill="#BC776D" transform="translate(0,0)"/>
<path d="" fill="#B7726B" transform="translate(0,0)"/>
<path d="" fill="#BC786E" transform="translate(0,0)"/>
<path d="" fill="#B8776C" transform="translate(0,0)"/>
<path d="" fill="#B8726A" transform="translate(0,0)"/>
<path d="" fill="#BE766E" transform="translate(0,0)"/>
<path d="" fill="#BB756C" transform="translate(0,0)"/>
<path d="" fill="#BF756B" transform="translate(0,0)"/>
<path d="" fill="#C37971" transform="translate(0,0)"/>
<path d="" fill="#BD776D" transform="translate(0,0)"/>
<path d="" fill="#BF796E" transform="translate(0,0)"/>
<path d="" fill="#B8756F" transform="translate(0,0)"/>
<path d="" fill="#C0786D" transform="translate(0,0)"/>
<path d="" fill="#C77970" transform="translate(0,0)"/>
<path d="" fill="#B7766C" transform="translate(0,0)"/>
<path d="" fill="#BF776E" transform="translate(0,0)"/>
<path d="" fill="#C1786D" transform="translate(0,0)"/>
<path d="" fill="#BD746A" transform="translate(0,0)"/>
<path d="" fill="#B8746B" transform="translate(0,0)"/>
<path d="" fill="#BE766D" transform="translate(0,0)"/>
<path d="" fill="#BD756B" transform="translate(0,0)"/>
<path d="" fill="#BD756A" transform="translate(0,0)"/>
<path d="" fill="#C2786F" transform="translate(0,0)"/>
<path d="" fill="#BB746A" transform="translate(0,0)"/>
<path d="" fill="#BD766D" transform="translate(0,0)"/>
<path d="" fill="#D27F75" transform="translate(0,0)"/>
<path d="" fill="#BA746B" transform="translate(0,0)"/>
<path d="" fill="#BC746B" transform="translate(0,0)"/>
<path d="" fill="#C47A70" transform="translate(0,0)"/>
<path d="" fill="#C1796E" transform="translate(0,0)"/>
<path d="" fill="#BE766D" transform="translate(0,0)"/>
<path d="" fill="#BD756B" transform="translate(0,0)"/>
<path d="" fill="#BF786D" transform="translate(0,0)"/>
<path d="" fill="#B5766C" transform="translate(0,0)"/>
<path d="" fill="#C77970" transform="translate(0,0)"/>
<path d="" fill="#BB7569" transform="translate(0,0)"/>
<path d="" fill="#C4786E" transform="translate(0,0)"/>
<path d="" fill="#BE7870" transform="translate(0,0)"/>
<path d="" fill="#655C5B" transform="translate(0,0)"/>
<path d="" fill="#CD7D71" transform="translate(0,0)"/>
<path d="" fill="#B9766E" transform="translate(0,0)"/>
<path d="" fill="#BF776C" transform="translate(0,0)"/>
<path d="" fill="#C7766B" transform="translate(0,0)"/>
<path d="" fill="#C97C6F" transform="translate(0,0)"/>
<path d="" fill="#CF7A6F" transform="translate(0,0)"/>
<path d="" fill="#BD7669" transform="translate(0,0)"/>
<path d="" fill="#D17D70" transform="translate(0,0)"/>
<path d="" fill="#C4786E" transform="translate(0,0)"/>
<path d="" fill="#C3776B" transform="translate(0,0)"/>
<path d="" fill="#CA7A6E" transform="translate(0,0)"/>
<path d="" fill="#D27A6F" transform="translate(0,0)"/>
<path d="" fill="#C47870" transform="translate(0,0)"/>
<path d="" fill="#BF786E" transform="translate(0,0)"/>
<path d="" fill="#CF7D71" transform="translate(0,0)"/>
<path d="" fill="#C67A6F" transform="translate(0,0)"/>
<path d="" fill="#C8786E" transform="translate(0,0)"/>
<path d="" fill="#D87F74" transform="translate(0,0)"/>
<path d="" fill="#C4786E" transform="translate(0,0)"/>
<path d="" fill="#C77A6E" transform="translate(0,0)"/>
<path d="" fill="#C7796F" transform="translate(0,0)"/>
<path d="" fill="#C07872" transform="translate(0,0)"/>
<path d="" fill="#C27A6E" transform="translate(0,0)"/>
<path d="" fill="#C67A6E" transform="translate(0,0)"/>
<path d="" fill="#C7786E" transform="translate(0,0)"/>
<path d="" fill="#C27871" transform="translate(0,0)"/>
<path d="" fill="#CB7C70" transform="translate(0,0)"/>
<path d="" fill="#CD7C70" transform="translate(0,0)"/>
<path d="" fill="#C2786E" transform="translate(0,0)"/>
<path d="" fill="#BF796F" transform="translate(0,0)"/>
<path d="" fill="#C6776C" transform="translate(0,0)"/>
<path d="" fill="#CB7A6D" transform="translate(0,0)"/>
<path d="" fill="#C3796F" transform="translate(0,0)"/>
<path d="" fill="#CA7B6F" transform="translate(0,0)"/>
<path d="" fill="#BF786F" transform="translate(0,0)"/>
<path d="" fill="#C6786E" transform="translate(0,0)"/>
<path d="" fill="#C6786D" transform="translate(0,0)"/>
<path d="" fill="#C77B71" transform="translate(0,0)"/>
<path d="" fill="#C4786F" transform="translate(0,0)"/>
<path d="" fill="#C3786E" transform="translate(0,0)"/>
<path d="" fill="#C47A72" transform="translate(0,0)"/>
<path d="" fill="#C47A6F" transform="translate(0,0)"/>
<path d="" fill="#BD776E" transform="translate(0,0)"/>
<path d="" fill="#C47A6D" transform="translate(0,0)"/>
<path d="" fill="#C67A6E" transform="translate(0,0)"/>
<path d="" fill="#C2796F" transform="translate(0,0)"/>
<path d="" fill="#C67870" transform="translate(0,0)"/>
<path d="" fill="#C2796F" transform="translate(0,0)"/>
<path d="" fill="#C3796E" transform="translate(0,0)"/>
<path d="" fill="#BF786F" transform="translate(0,0)"/>
<path d="" fill="#C3786E" transform="translate(0,0)"/>
<path d="" fill="#BE786E" transform="translate(0,0)"/>
<path d="" fill="#CF7C70" transform="translate(0,0)"/>
<path d="" fill="#615F5F" transform="translate(0,0)"/>
<path d="" fill="#CA7B70" transform="translate(0,0)"/>
<path d="" fill="#BB7A6D" transform="translate(0,0)"/>
<path d="" fill="#BD776D" transform="translate(0,0)"/>
<path d="" fill="#CB7A6F" transform="translate(0,0)"/>
<path d="" fill="#C6796D" transform="translate(0,0)"/>
<path d="" fill="#C77A70" transform="translate(0,0)"/>
<path d="" fill="#C2766C" transform="translate(0,0)"/>
<path d="" fill="#CB7C71" transform="translate(0,0)"/>
<path d="" fill="#CB7B6F" transform="translate(0,0)"/>
<path d="" fill="#C67D71" transform="translate(0,0)"/>
<path d="" fill="#C5786C" transform="translate(0,0)"/>
<path d="" fill="#CC7C72" transform="translate(0,0)"/>
<path d="" fill="#C5796D" transform="translate(0,0)"/>
<path d="" fill="#C7786C" transform="translate(0,0)"/>
<path d="" fill="#CB7B6E" transform="translate(0,0)"/>
<path d="" fill="#C0786D" transform="translate(0,0)"/>
<path d="" fill="#CC786D" transform="translate(0,0)"/>
<path d="" fill="#CA7B72" transform="translate(0,0)"/>
<path d="" fill="#C87A70" transform="translate(0,0)"/>
<path d="" fill="#B87369" transform="translate(0,0)"/>
<path d="" fill="#C87A6F" transform="translate(0,0)"/>
<path d="" fill="#CF7D71" transform="translate(0,0)"/>
<path d="" fill="#CA7A71" transform="translate(0,0)"/>
<path d="" fill="#CC8173" transform="translate(0,0)"/>
<path d="" fill="#C97A6D" transform="translate(0,0)"/>
<path d="" fill="#CA7D72" transform="translate(0,0)"/>
<path d="" fill="#C77D6F" transform="translate(0,0)"/>
<path d="" fill="#B9776D" transform="translate(0,0)"/>
<path d="" fill="#D37F73" transform="translate(0,0)"/>
<path d="" fill="#C17A6E" transform="translate(0,0)"/>
<path d="" fill="#CB796F" transform="translate(0,0)"/>
<path d="" fill="#C9796F" transform="translate(0,0)"/>
<path d="" fill="#C17A70" transform="translate(0,0)"/>
<path d="" fill="#BB796E" transform="translate(0,0)"/>
<path d="" fill="#CF7E72" transform="translate(0,0)"/>
<path d="" fill="#C77B6D" transform="translate(0,0)"/>
<path d="" fill="#C57B6E" transform="translate(0,0)"/>
<path d="" fill="#C17B6D" transform="translate(0,0)"/>
<path d="" fill="#CC7D70" transform="translate(0,0)"/>
<path d="" fill="#BD7970" transform="translate(0,0)"/>
<path d="" fill="#BD796F" transform="translate(0,0)"/>
<path d="" fill="#C17A6D" transform="translate(0,0)"/>
<path d="" fill="#C47B70" transform="translate(0,0)"/>
<path d="" fill="#BE796E" transform="translate(0,0)"/>
<path d="" fill="#C97C6F" transform="translate(0,0)"/>
<path d="" fill="#C47A6E" transform="translate(0,0)"/>
<path d="" fill="#C87C72" transform="translate(0,0)"/>
<path d="" fill="#C47C6F" transform="translate(0,0)"/>
<path d="" fill="#B77168" transform="translate(0,0)"/>
<path d="" fill="#BD776E" transform="translate(0,0)"/>
<path d="" fill="#BF786D" transform="translate(0,0)"/>
<path d="" fill="#BC756B" transform="translate(0,0)"/>
<path d="" fill="#BC766C" transform="translate(0,0)"/>
<path d="" fill="#C2776D" transform="translate(0,0)"/>
<path d="" fill="#C1796F" transform="translate(0,0)"/>
<path d="" fill="#C3796E" transform="translate(0,0)"/>
<path d="" fill="#C0796D" transform="translate(0,0)"/>
<path d="" fill="#C1786E" transform="translate(0,0)"/>
<path d="" fill="#C3796D" transform="translate(0,0)"/>
<path d="" fill="#CB7B70" transform="translate(0,0)"/>
<path d="" fill="#C37A70" transform="translate(0,0)"/>
<path d="" fill="#C57B70" transform="translate(0,0)"/>
<path d="" fill="#C37A6E" transform="translate(0,0)"/>
<path d="" fill="#C67B6F" transform="translate(0,0)"/>
<path d="" fill="#CB7E72" transform="translate(0,0)"/>
<path d="" fill="#CB7D71" transform="translate(0,0)"/>
<path d="" fill="#C97E73" transform="translate(0,0)"/>
<path d="" fill="#BA756C" transform="translate(0,0)"/>
<path d="" fill="#AF746C" transform="translate(0,0)"/>
<path d="" fill="#BB796F" transform="translate(0,0)"/>
<path d="" fill="#BC756F" transform="translate(0,0)"/>
<path d="" fill="#BE786F" transform="translate(0,0)"/>
<path d="" fill="#BA776E" transform="translate(0,0)"/>
<path d="" fill="#BB786F" transform="translate(0,0)"/>
<path d="" fill="#B67871" transform="translate(0,0)"/>
<path d="" fill="#B6776F" transform="translate(0,0)"/>
<path d="" fill="#B4786F" transform="translate(0,0)"/>
<path d="" fill="#AD746E" transform="translate(0,0)"/>
<path d="" fill="#AA756E" transform="translate(0,0)"/>
<path d="" fill="#B9786E" transform="translate(0,0)"/>
<path d="" fill="#B87770" transform="translate(0,0)"/>
<path d="" fill="#B7786F" transform="translate(0,0)"/>
<path d="" fill="#AE736F" transform="translate(0,0)"/>
<path d="" fill="#AA716B" transform="translate(0,0)"/>
<path d="" fill="#AB7067" transform="translate(0,0)"/>
<path d="" fill="#AB7167" transform="translate(0,0)"/>
<path d="" fill="#BB786F" transform="translate(0,0)"/>
<path d="" fill="#BE7870" transform="translate(0,0)"/>
<path d="" fill="#B3756F" transform="translate(0,0)"/>
<path d="" fill="#AC746C" transform="translate(0,0)"/>
<path d="" fill="#AB716A" transform="translate(0,0)"/>
<path d="" fill="#B0726C" transform="translate(0,0)"/>
<path d="" fill="#B8776F" transform="translate(0,0)"/>
<path d="" fill="#AA736D" transform="translate(0,0)"/>
<path d="" fill="#C87B74" transform="translate(0,0)"/>
<path d="" fill="#CB7B74" transform="translate(0,0)"/>
<path d="" fill="#C57B70" transform="translate(0,0)"/>
<path d="" fill="#C97F75" transform="translate(0,0)"/>
<path d="" fill="#C77F74" transform="translate(0,0)"/>
<path d="" fill="#C78175" transform="translate(0,0)"/>
<path d="" fill="#C57F7B" transform="translate(0,0)"/>
<path d="" fill="#B7766D" transform="translate(0,0)"/>
<path d="" fill="#C47D73" transform="translate(0,0)"/>
<path d="" fill="#C07D73" transform="translate(0,0)"/>
<path d="" fill="#C57B72" transform="translate(0,0)"/>
<path d="" fill="#B4786D" transform="translate(0,0)"/>
<path d="" fill="#BB7870" transform="translate(0,0)"/>
<path d="" fill="#AD736C" transform="translate(0,0)"/>
<path d="" fill="#A9726B" transform="translate(0,0)"/>
<path d="" fill="#AB706B" transform="translate(0,0)"/>
<path d="" fill="#BB746C" transform="translate(0,0)"/>
<path d="" fill="#AD746C" transform="translate(0,0)"/>
<path d="" fill="#BB786D" transform="translate(0,0)"/>
<path d="" fill="#B0726B" transform="translate(0,0)"/>
<path d="" fill="#B37670" transform="translate(0,0)"/>
<path d="" fill="#A8736B" transform="translate(0,0)"/>
<path d="" fill="#B77770" transform="translate(0,0)"/>
<path d="" fill="#AF726B" transform="translate(0,0)"/>
<path d="" fill="#B9756F" transform="translate(0,0)"/>
<path d="" fill="#B4766D" transform="translate(0,0)"/>
<path d="" fill="#B5736E" transform="translate(0,0)"/>
<path d="" fill="#A56F6A" transform="translate(0,0)"/>
<path d="" fill="#B3726B" transform="translate(0,0)"/>
<path d="" fill="#C2766C" transform="translate(0,0)"/>
<path d="" fill="#C0756D" transform="translate(0,0)"/>
<path d="" fill="#B4766E" transform="translate(0,0)"/>
<path d="" fill="#A56F6A" transform="translate(0,0)"/>
<path d="" fill="#B5726B" transform="translate(0,0)"/>
<path d="" fill="#B4756D" transform="translate(0,0)"/>
<path d="" fill="#BB746C" transform="translate(0,0)"/>
<path d="" fill="#B4766D" transform="translate(0,0)"/>
<path d="" fill="#B8736B" transform="translate(0,0)"/>
<path d="" fill="#BB786F" transform="translate(0,0)"/>
<path d="" fill="#B7776E" transform="translate(0,0)"/>
<path d="" fill="#BB776D" transform="translate(0,0)"/>
<path d="" fill="#C07971" transform="translate(0,0)"/>
<path d="" fill="#B77369" transform="translate(0,0)"/>
<path d="" fill="#B3756D" transform="translate(0,0)"/>
<path d="" fill="#B0756C" transform="translate(0,0)"/>
<path d="" fill="#A6726E" transform="translate(0,0)"/>
<path d="" fill="#C0766D" transform="translate(0,0)"/>
<path d="" fill="#BC776E" transform="translate(0,0)"/>
<path d="" fill="#B8776E" transform="translate(0,0)"/>
<path d="" fill="#BA746C" transform="translate(0,0)"/>
<path d="" fill="#B7776E" transform="translate(0,0)"/>
<path d="" fill="#B8776E" transform="translate(0,0)"/>
<path d="" fill="#A8736C" transform="translate(0,0)"/>
<path d="" fill="#BA7269" transform="translate(0,0)"/>
<path d="" fill="#B6756C" transform="translate(0,0)"/>
<path d="" fill="#A9746C" transform="translate(0,0)"/>
<path d="" fill="#B5776E" transform="translate(0,0)"/>
<path d="" fill="#A9736F" transform="translate(0,0)"/>
<path d="" fill="#BF776C" transform="translate(0,0)"/>
<path d="" fill="#B7756D" transform="translate(0,0)"/>
<path d="" fill="#BD756F" transform="translate(0,0)"/>
<path d="" fill="#CB766E" transform="translate(0,0)"/>
<path d="" fill="#B5746B" transform="translate(0,0)"/>
<path d="" fill="#B5766C" transform="translate(0,0)"/>
<path d="" fill="#AE726C" transform="translate(0,0)"/>
<path d="" fill="#A8746C" transform="translate(0,0)"/>
<path d="" fill="#BC756B" transform="translate(0,0)"/>
<path d="" fill="#BD756C" transform="translate(0,0)"/>
<path d="" fill="#C17A71" transform="translate(0,0)"/>
<path d="" fill="#BE726A" transform="translate(0,0)"/>
<path d="" fill="#BC746B" transform="translate(0,0)"/>
<path d="" fill="#C17A70" transform="translate(0,0)"/>
<path d="" fill="#C2786D" transform="translate(0,0)"/>
<path d="" fill="#BC766E" transform="translate(0,0)"/>
<path d="" fill="#BC786D" transform="translate(0,0)"/>
<path d="" fill="#BD716B" transform="translate(0,0)"/>
<path d="" fill="#BD756D" transform="translate(0,0)"/>
<path d="" fill="#CE8076" transform="translate(0,0)"/>
<path d="" fill="#BA766E" transform="translate(0,0)"/>
<path d="" fill="#BE756A" transform="translate(0,0)"/>
<path d="" fill="#CA7D73" transform="translate(0,0)"/>
<path d="" fill="#B9776E" transform="translate(0,0)"/>
<path d="" fill="#C0756D" transform="translate(0,0)"/>
<path d="" fill="#C3786E" transform="translate(0,0)"/>
<path d="" fill="#BF766E" transform="translate(0,0)"/>
<path d="" fill="#B4756D" transform="translate(0,0)"/>
<path d="" fill="#BE746A" transform="translate(0,0)"/>
<path d="" fill="#C7786D" transform="translate(0,0)"/>
<path d="" fill="#C5796E" transform="translate(0,0)"/>
<path d="" fill="#BF766E" transform="translate(0,0)"/>
<path d="" fill="#B47069" transform="translate(0,0)"/>
<path d="" fill="#A5726B" transform="translate(0,0)"/>
<path d="" fill="#C8756E" transform="translate(0,0)"/>
<path d="" fill="#BB756B" transform="translate(0,0)"/>
<path d="" fill="#BF766D" transform="translate(0,0)"/>
<path d="" fill="#B5756B" transform="translate(0,0)"/>
<path d="" fill="#B6746C" transform="translate(0,0)"/>
<path d="" fill="#D77A70" transform="translate(0,0)"/>
<path d="" fill="#C37A6E" transform="translate(0,0)"/>
<path d="" fill="#C37A70" transform="translate(0,0)"/>
<path d="" fill="#C7796F" transform="translate(0,0)"/>
<path d="" fill="#BA766B" transform="translate(0,0)"/>
<path d="" fill="#C17B71" transform="translate(0,0)"/>
<path d="" fill="#AE756A" transform="translate(0,0)"/>
<path d="" fill="#BC756A" transform="translate(0,0)"/>
<path d="" fill="#C5756A" transform="translate(0,0)"/>
<path d="" fill="#B9756B" transform="translate(0,0)"/>
<path d="" fill="#B0736A" transform="translate(0,0)"/>
<path d="" fill="#C17A70" transform="translate(0,0)"/>
<path d="" fill="#B7716A" transform="translate(0,0)"/>
<path d="" fill="#C37B73" transform="translate(0,0)"/>
<path d="" fill="#BE7469" transform="translate(0,0)"/>
<path d="" fill="#B1716A" transform="translate(0,0)"/>
<path d="" fill="#A8716B" transform="translate(0,0)"/>
<path d="" fill="#C3786C" transform="translate(0,0)"/>
<path d="" fill="#C4766B" transform="translate(0,0)"/>
<path d="" fill="#C0786D" transform="translate(0,0)"/>
<path d="" fill="#B57169" transform="translate(0,0)"/>
<path d="" fill="#B8786D" transform="translate(0,0)"/>
<path d="" fill="#C77D71" transform="translate(0,0)"/>
<path d="" fill="#CE796F" transform="translate(0,0)"/>
<path d="" fill="#B87870" transform="translate(0,0)"/>
<path d="" fill="#C17970" transform="translate(0,0)"/>
<path d="" fill="#C8796C" transform="translate(0,0)"/>
<path d="" fill="#C67A6F" transform="translate(0,0)"/>
<path d="" fill="#C0746C" transform="translate(0,0)"/>
<path d="" fill="#C6796D" transform="translate(0,0)"/>
<path d="" fill="#C6776B" transform="translate(0,0)"/>
<path d="" fill="#C2776D" transform="translate(0,0)"/>
<path d="" fill="#B8736B" transform="translate(0,0)"/>
<path d="" fill="#BE756B" transform="translate(0,0)"/>
<path d="" fill="#C27367" transform="translate(0,0)"/>
<path d="" fill="#C7756E" transform="translate(0,0)"/>
<path d="" fill="#B7756E" transform="translate(0,0)"/>
<path d="" fill="#C07970" transform="translate(0,0)"/>
<path d="" fill="#C6796F" transform="translate(0,0)"/>
<path d="" fill="#C6776C" transform="translate(0,0)"/>
<path d="" fill="#C7796E" transform="translate(0,0)"/>
<path d="" fill="#C57A6D" transform="translate(0,0)"/>
<path d="" fill="#C6796E" transform="translate(0,0)"/>
<path d="" fill="#BA766F" transform="translate(0,0)"/>
<path d="" fill="#C3786D" transform="translate(0,0)"/>
<path d="" fill="#AB726B" transform="translate(0,0)"/>
<path d="" fill="#D57D70" transform="translate(0,0)"/>
<path d="" fill="#C57B6E" transform="translate(0,0)"/>
<path d="" fill="#CF7F72" transform="translate(0,0)"/>
<path d="" fill="#CE7B71" transform="translate(0,0)"/>
<path d="" fill="#C4786D" transform="translate(0,0)"/>
<path d="" fill="#626362" transform="translate(0,0)"/>
<path d="" fill="#6A6161" transform="translate(0,0)"/>
<path d="" fill="#D48175" transform="translate(0,0)"/>
<path d="" fill="#D48175" transform="translate(0,0)"/>
<path d="" fill="#C5776D" transform="translate(0,0)"/>
<path d="" fill="#C57669" transform="translate(0,0)"/>
<path d="" fill="#C87A6F" transform="translate(0,0)"/>
<path d="" fill="#BF756D" transform="translate(0,0)"/>
<path d="" fill="#D27A6F" transform="translate(0,0)"/>
<path d="" fill="#D47E73" transform="translate(0,0)"/>
<path d="" fill="#C6796F" transform="translate(0,0)"/>
<path d="" fill="#C87A6E" transform="translate(0,0)"/>
<path d="" fill="#CB7D70" transform="translate(0,0)"/>
<path d="" fill="#AA706A" transform="translate(0,0)"/>
<path d="" fill="#CB7C6E" transform="translate(0,0)"/>
<path d="" fill="#AF746E" transform="translate(0,0)"/>
<path d="" fill="#D78377" transform="translate(0,0)"/>
<path d="" fill="#BF786B" transform="translate(0,0)"/>
<path d="" fill="#D77F73" transform="translate(0,0)"/>
<path d="" fill="#DA7E6E" transform="translate(0,0)"/>
<path d="" fill="#BC746A" transform="translate(0,0)"/>
<path d="" fill="#C8756C" transform="translate(0,0)"/>
<path d="" fill="#CD7A6D" transform="translate(0,0)"/>
<path d="" fill="#D98276" transform="translate(0,0)"/>
<path d="" fill="#BE786D" transform="translate(0,0)"/>
<path d="" fill="#AE776E" transform="translate(0,0)"/>
<path d="" fill="#C3746B" transform="translate(0,0)"/>
<path d="" fill="#CA776B" transform="translate(0,0)"/>
<path d="" fill="#D07D6E" transform="translate(0,0)"/>
<path d="" fill="#CE7B6E" transform="translate(0,0)"/>
<path d="" fill="#BF7468" transform="translate(0,0)"/>
<path d="" fill="#BF786D" transform="translate(0,0)"/>
<path d="" fill="#CA7A6D" transform="translate(0,0)"/>
<path d="" fill="#CA7C70" transform="translate(0,0)"/>
<path d="" fill="#C2776D" transform="translate(0,0)"/>
<path d="" fill="#B9746A" transform="translate(0,0)"/>
<path d="" fill="#C97A70" transform="translate(0,0)"/>
<path d="" fill="#CB7A6F" transform="translate(0,0)"/>
<path d="" fill="#C1776C" transform="translate(0,0)"/>
<path d="" fill="#BD766D" transform="translate(0,0)"/>
<path d="" fill="#C97C70" transform="translate(0,0)"/>
<path d="" fill="#CA7B6E" transform="translate(0,0)"/>
<path d="" fill="#CA7A6E" transform="translate(0,0)"/>
<path d="" fill="#C3766B" transform="translate(0,0)"/>
<path d="" fill="#C4766C" transform="translate(0,0)"/>
<path d="" fill="#BD786D" transform="translate(0,0)"/>
<path d="" fill="#D37B6F" transform="translate(0,0)"/>
<path d="" fill="#DA8579" transform="translate(0,0)"/>
<path d="" fill="#C0796D" transform="translate(0,0)"/>
<path d="" fill="#BF7A6E" transform="translate(0,0)"/>
<path d="" fill="#C77A6F" transform="translate(0,0)"/>
<path d="" fill="#C37A70" transform="translate(0,0)"/>
<path d="" fill="#BE776D" transform="translate(0,0)"/>
<path d="" fill="#C27569" transform="translate(0,0)"/>
<path d="" fill="#D07C6F" transform="translate(0,0)"/>
<path d="" fill="#CA7B71" transform="translate(0,0)"/>
<path d="" fill="#C57A6F" transform="translate(0,0)"/>
<path d="" fill="#C3796D" transform="translate(0,0)"/>
<path d="" fill="#C3786E" transform="translate(0,0)"/>
<path d="" fill="#C8796B" transform="translate(0,0)"/>
<path d="" fill="#C2756A" transform="translate(0,0)"/>
<path d="" fill="#C5776D" transform="translate(0,0)"/>
<path d="" fill="#C3766B" transform="translate(0,0)"/>
<path d="" fill="#C3786E" transform="translate(0,0)"/>
<path d="" fill="#C4786D" transform="translate(0,0)"/>
<path d="" fill="#C77A6E" transform="translate(0,0)"/>
<path d="" fill="#C2786D" transform="translate(0,0)"/>
<path d="" fill="#C17368" transform="translate(0,0)"/>
<path d="" fill="#D57C6F" transform="translate(0,0)"/>
<path d="" fill="#C97C6D" transform="translate(0,0)"/>
<path d="" fill="#D58273" transform="translate(0,0)"/>
<path d="" fill="#C4776B" transform="translate(0,0)"/>
<path d="" fill="#C67A6E" transform="translate(0,0)"/>
<path d="" fill="#C3766B" transform="translate(0,0)"/>
<path d="" fill="#BF766D" transform="translate(0,0)"/>
<path d="" fill="#DC8372" transform="translate(0,0)"/>
<path d="" fill="#CC7D71" transform="translate(0,0)"/>
<path d="" fill="#C77A6D" transform="translate(0,0)"/>
<path d="" fill="#C2776E" transform="translate(0,0)"/>
<path d="" fill="#C5796E" transform="translate(0,0)"/>
<path d="" fill="#C5786E" transform="translate(0,0)"/>
<path d="" fill="#CC7C71" transform="translate(0,0)"/>
<path d="" fill="#D78276" transform="translate(0,0)"/>
<path d="" fill="#C5786E" transform="translate(0,0)"/>
<path d="" fill="#C5796D" transform="translate(0,0)"/>
<path d="" fill="#C1786D" transform="translate(0,0)"/>
<path d="" fill="#DC8271" transform="translate(0,0)"/>
<path d="" fill="#C97D6F" transform="translate(0,0)"/>
<path d="" fill="#D07B70" transform="translate(0,0)"/>
<path d="" fill="#CA7C6F" transform="translate(0,0)"/>
<path d="" fill="#D07A70" transform="translate(0,0)"/>
<path d="" fill="#D17C6E" transform="translate(0,0)"/>
<path d="" fill="#E38373" transform="translate(0,0)"/>
<path d="" fill="#CB7C70" transform="translate(0,0)"/>
<path d="" fill="#CA7C6E" transform="translate(0,0)"/>
<path d="" fill="#CA7D72" transform="translate(0,0)"/>
<path d="" fill="#AB746C" transform="translate(0,0)"/>
<path d="" fill="#CB7C6F" transform="translate(0,0)"/>
<path d="" fill="#C77C6F" transform="translate(0,0)"/>
<path d="" fill="#B1776E" transform="translate(0,0)"/>
<path d="" fill="#B2746C" transform="translate(0,0)"/>
<path d="" fill="#CA7B6F" transform="translate(0,0)"/>
<path d="" fill="#D88578" transform="translate(0,0)"/>
<path d="" fill="#C77A6D" transform="translate(0,0)"/>
<path d="" fill="#D98070" transform="translate(0,0)"/>
<path d="" fill="#C57B6E" transform="translate(0,0)"/>
<path d="" fill="#B1766E" transform="translate(0,0)"/>
<path d="" fill="#D68377" transform="translate(0,0)"/>
<path d="" fill="#C97E71" transform="translate(0,0)"/>
<path d="" fill="#DE8376" transform="translate(0,0)"/>
<path d="" fill="#C87B6E" transform="translate(0,0)"/>
<path d="" fill="#D58174" transform="translate(0,0)"/>
<path d="" fill="#B2756C" transform="translate(0,0)"/>
<path d="" fill="#C3796D" transform="translate(0,0)"/>
<path d="" fill="#C97B6D" transform="translate(0,0)"/>
<path d="" fill="#C47A6C" transform="translate(0,0)"/>
<path d="" fill="#CA7E71" transform="translate(0,0)"/>
<path d="" fill="#CC7D71" transform="translate(0,0)"/>
<path d="" fill="#D48275" transform="translate(0,0)"/>
<path d="" fill="#D98174" transform="translate(0,0)"/>
<path d="" fill="#C27A6E" transform="translate(0,0)"/>
<path d="" fill="#BE7C6E" transform="translate(0,0)"/>
<path d="" fill="#D18176" transform="translate(0,0)"/>
<path d="" fill="#B3776B" transform="translate(0,0)"/>
<path d="" fill="#C1796B" transform="translate(0,0)"/>
<path d="" fill="#C27A6E" transform="translate(0,0)"/>
<path d="" fill="#CC7D72" transform="translate(0,0)"/>
<path d="" fill="#CA7F72" transform="translate(0,0)"/>
<path d="" fill="#C7786E" transform="translate(0,0)"/>
<path d="" fill="#C97D71" transform="translate(0,0)"/>
<path d="" fill="#B0746C" transform="translate(0,0)"/>
<path d="" fill="#B77468" transform="translate(0,0)"/>
<path d="" fill="#C57B6E" transform="translate(0,0)"/>
<path d="" fill="#C17A6D" transform="translate(0,0)"/>
<path d="" fill="#CB7F73" transform="translate(0,0)"/>
<path d="" fill="#D08073" transform="translate(0,0)"/>
<path d="" fill="#BC756A" transform="translate(0,0)"/>
<path d="" fill="#BC7A6D" transform="translate(0,0)"/>
<path d="" fill="#B2766D" transform="translate(0,0)"/>
<path d="" fill="#C1766B" transform="translate(0,0)"/>
<path d="" fill="#C2776B" transform="translate(0,0)"/>
<path d="" fill="#C97E70" transform="translate(0,0)"/>
<path d="" fill="#B7766B" transform="translate(0,0)"/>
<path d="" fill="#C07C6C" transform="translate(0,0)"/>
<path d="" fill="#C37A6E" transform="translate(0,0)"/>
<path d="" fill="#C27A6E" transform="translate(0,0)"/>
<path d="" fill="#C47C6F" transform="translate(0,0)"/>
<path d="" fill="#D18172" transform="translate(0,0)"/>
<path d="" fill="#D18374" transform="translate(0,0)"/>
<path d="" fill="#CF8074" transform="translate(0,0)"/>
<path d="" fill="#CD8174" transform="translate(0,0)"/>
<path d="" fill="#C97E72" transform="translate(0,0)"/>
<path d="" fill="#CC8174" transform="translate(0,0)"/>
<path d="" fill="#CE8375" transform="translate(0,0)"/>
<path d="" fill="#C98074" transform="translate(0,0)"/>
<path d="" fill="#CC7F70" transform="translate(0,0)"/>
<path d="" fill="#C57D6F" transform="translate(0,0)"/>
<path d="" fill="#C0796C" transform="translate(0,0)"/>
<path d="" fill="#BE776C" transform="translate(0,0)"/>
<path d="" fill="#BA7368" transform="translate(0,0)"/>
<path d="" fill="#C2796D" transform="translate(0,0)"/>
<path d="" fill="#C27B6F" transform="translate(0,0)"/>
<path d="" fill="#BE7A6E" transform="translate(0,0)"/>
<path d="" fill="#C17F72" transform="translate(0,0)"/>
<path d="" fill="#BE7D73" transform="translate(0,0)"/>
<path d="" fill="#C17B6E" transform="translate(0,0)"/>
<path d="" fill="#C98072" transform="translate(0,0)"/>
<path d="" fill="#C37A6E" transform="translate(0,0)"/>
<path d="" fill="#C67E6F" transform="translate(0,0)"/>
<path d="" fill="#BD786C" transform="translate(0,0)"/>
<path d="" fill="#B8776C" transform="translate(0,0)"/>
<path d="" fill="#BD7C6F" transform="translate(0,0)"/>
<path d="" fill="#9F9795" transform="translate(0,0)"/>
<path d="" fill="#A29996" transform="translate(0,0)"/>
<path d="" fill="#9F9897" transform="translate(0,0)"/>
<path d="" fill="#9C9896" transform="translate(0,0)"/>
<path d="" fill="#9E9996" transform="translate(0,0)"/>
<path d="" fill="#9F9797" transform="translate(0,0)"/>
<path d="" fill="#9D9795" transform="translate(0,0)"/>
<path d="" fill="#9D9795" transform="translate(0,0)"/>
<path d="" fill="#9B9594" transform="translate(0,0)"/>
<path d="" fill="#A3A09F" transform="translate(0,0)"/>
<path d="" fill="#A29E9E" transform="translate(0,0)"/>
<path d="" fill="#A29F9E" transform="translate(0,0)"/>
<path d="" fill="#9D9897" transform="translate(0,0)"/>
<path d="" fill="#A09998" transform="translate(0,0)"/>
<path d="" fill="#9E9B9A" transform="translate(0,0)"/>
<path d="" fill="#9B9895" transform="translate(0,0)"/>
<path d="" fill="#A09D9A" transform="translate(0,0)"/>
<path d="" fill="#A3A09F" transform="translate(0,0)"/>
<path d="" fill="#9A9795" transform="translate(0,0)"/>
<path d="" fill="#A6A5A3" transform="translate(0,0)"/>
<path d="" fill="#A2A09D" transform="translate(0,0)"/>
<path d="" fill="#9D9997" transform="translate(0,0)"/>
<path d="" fill="#9B9695" transform="translate(0,0)"/>
<path d="" fill="#9C9896" transform="translate(0,0)"/>
<path d="" fill="#9F9A9C" transform="translate(0,0)"/>
<path d="" fill="#A7A3A3" transform="translate(0,0)"/>
<path d="" fill="#A49FA0" transform="translate(0,0)"/>
<path d="" fill="#A5A1A0" transform="translate(0,0)"/>
<path d="" fill="#A2A29E" transform="translate(0,0)"/>
<path d="" fill="#5B5B5B" transform="translate(0,0)"/>
<path d="" fill="#9A9595" transform="translate(0,0)"/>
<path d="" fill="#A39F9F" transform="translate(0,0)"/>
<path d="" fill="#A4A19F" transform="translate(0,0)"/>
<path d="" fill="#AEA9A8" transform="translate(0,0)"/>
<path d="" fill="#A3A2A1" transform="translate(0,0)"/>
<path d="" fill="#A09D9D" transform="translate(0,0)"/>
<path d="" fill="#ACAAA8" transform="translate(0,0)"/>
<path d="" fill="#9B9798" transform="translate(0,0)"/>
<path d="" fill="#9A9795" transform="translate(0,0)"/>
<path d="" fill="#9C9898" transform="translate(0,0)"/>
<path d="" fill="#AFAEAB" transform="translate(0,0)"/>
<path d="" fill="#ADAAA9" transform="translate(0,0)"/>
<path d="" fill="#B3B0AE" transform="translate(0,0)"/>
<path d="" fill="#A5A3A2" transform="translate(0,0)"/>
<path d="" fill="#B0ADAB" transform="translate(0,0)"/>
<path d="" fill="#A6A4A4" transform="translate(0,0)"/>
<path d="" fill="#AFABAD" transform="translate(0,0)"/>
<path d="" fill="#A9A4A3" transform="translate(0,0)"/>
<path d="" fill="#B2B1B1" transform="translate(0,0)"/>
<path d="" fill="#AAA6A6" transform="translate(0,0)"/>
<path d="" fill="#979495" transform="translate(0,0)"/>
<path d="" fill="#AAA6A6" transform="translate(0,0)"/>
<path d="" fill="#A4A3A1" transform="translate(0,0)"/>
<path d="" fill="#A09F9F" transform="translate(0,0)"/>
<path d="" fill="#A5A4A3" transform="translate(0,0)"/>
<path d="" fill="#AAA9A7" transform="translate(0,0)"/>
<path d="" fill="#ACAAA7" transform="translate(0,0)"/>
<path d="" fill="#5A5857" transform="translate(0,0)"/>
<path d="" fill="#ADA8A8" transform="translate(0,0)"/>
<path d="" fill="#AFADAB" transform="translate(0,0)"/>
<path d="" fill="#ABAAA8" transform="translate(0,0)"/>
<path d="" fill="#B1B1AE" transform="translate(0,0)"/>
<path d="" fill="#AEACAB" transform="translate(0,0)"/>
<path d="" fill="#B5B4B4" transform="translate(0,0)"/>
<path d="" fill="#AEACAA" transform="translate(0,0)"/>
<path d="" fill="#ADABAA" transform="translate(0,0)"/>
<path d="" fill="#AFACAC" transform="translate(0,0)"/>
<path d="" fill="#ACABA7" transform="translate(0,0)"/>
<path d="" fill="#AFADAC" transform="translate(0,0)"/>
<path d="" fill="#AEACAC" transform="translate(0,0)"/>
<path d="" fill="#A7A5A4" transform="translate(0,0)"/>
<path d="" fill="#A5A4A1" transform="translate(0,0)"/>
<path d="" fill="#A5A4A3" transform="translate(0,0)"/>
<path d="" fill="#B2AFAE" transform="translate(0,0)"/>
<path d="" fill="#AEABA8" transform="translate(0,0)"/>
<path d="" fill="#AEAAAA" transform="translate(0,0)"/>
<path d="" fill="#979697" transform="translate(0,0)"/>
<path d="" fill="#A9A7A5" transform="translate(0,0)"/>
<path d="" fill="#ABA9A7" transform="translate(0,0)"/>
<path d="" fill="#ABAAA8" transform="translate(0,0)"/>
<path d="" fill="#A8A5A5" transform="translate(0,0)"/>
<path d="" fill="#ACA9AB" transform="translate(0,0)"/>
<path d="" fill="#A7A5A6" transform="translate(0,0)"/>
<path d="" fill="#A5A5A3" transform="translate(0,0)"/>
<path d="" fill="#949494" transform="translate(0,0)"/>
<path d="" fill="#979796" transform="translate(0,0)"/>
<path d="" fill="#AAA8A7" transform="translate(0,0)"/>
<path d="" fill="#A5A3A2" transform="translate(0,0)"/>
<path d="" fill="#A3A19F" transform="translate(0,0)"/>
<path d="" fill="#979695" transform="translate(0,0)"/>
<path d="" fill="#A7A7A6" transform="translate(0,0)"/>
<path d="" fill="#9A9A9B" transform="translate(0,0)"/>
<path d="" fill="#A5A2A0" transform="translate(0,0)"/>
<path d="" fill="#969494" transform="translate(0,0)"/>
<path d="" fill="#A0A09F" transform="translate(0,0)"/>
<path d="" fill="#969494" transform="translate(0,0)"/>
<path d="" fill="#9E9E9C" transform="translate(0,0)"/>
<path d="" fill="#9F9F9C" transform="translate(0,0)"/>
<path d="" fill="#5D5A5A" transform="translate(0,0)"/>
<path d="" fill="#929191" transform="translate(0,0)"/>
<path d="" fill="#A2A19F" transform="translate(0,0)"/>
<path d="" fill="#939393" transform="translate(0,0)"/>
<path d="" fill="#9F9D9D" transform="translate(0,0)"/>
<path d="" fill="#979794" transform="translate(0,0)"/>
<path d="" fill="#959594" transform="translate(0,0)"/>
<path d="" fill="#A0A09D" transform="translate(0,0)"/>
<path d="" fill="#A1A1A1" transform="translate(0,0)"/>
<path d="" fill="#989896" transform="translate(0,0)"/>
<path d="" fill="#A09E9B" transform="translate(0,0)"/>
<path d="" fill="#A29F9E" transform="translate(0,0)"/>
<path d="" fill="#9E9E9C" transform="translate(0,0)"/>
<path d="" fill="#A2A0A0" transform="translate(0,0)"/>
<path d="" fill="#989997" transform="translate(0,0)"/>
<path d="" fill="#A29E9D" transform="translate(0,0)"/>
<path d="" fill="#9E9C9D" transform="translate(0,0)"/>
<path d="" fill="#9E9C9C" transform="translate(0,0)"/>
<path d="" fill="#949394" transform="translate(0,0)"/>
<path d="" fill="#9D9C9C" transform="translate(0,0)"/>
<path d="" fill="#9F9E9C" transform="translate(0,0)"/>
<path d="" fill="#9C9999" transform="translate(0,0)"/>
<path d="" fill="#9A9A9B" transform="translate(0,0)"/>
<path d="" fill="#9C999E" transform="translate(0,0)"/>
<path d="" fill="#9F9F9E" transform="translate(0,0)"/>
<path d="" fill="#A19F9E" transform="translate(0,0)"/>
<path d="" fill="#9B9B99" transform="translate(0,0)"/>
<path d="" fill="#9B9B9B" transform="translate(0,0)"/>
<path d="" fill="#989695" transform="translate(0,0)"/>
<path d="" fill="#9B9A98" transform="translate(0,0)"/>
<path d="" fill="#969596" transform="translate(0,0)"/>
<path d="" fill="#5E605F" transform="translate(0,0)"/>
<path d="" fill="#555755" transform="translate(0,0)"/>
<path d="" fill="#565755" transform="translate(0,0)"/>
<path d="" fill="#5C5C5C" transform="translate(0,0)"/>
<path d="" fill="#666665" transform="translate(0,0)"/>
</svg>
