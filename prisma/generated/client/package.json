{"name": "prisma-client-ac02763ac5bd7904a8103cb47ec6d3d865cccc70e26264dd396eacd29efaef91", "main": "index.js", "types": "index.d.ts", "browser": "index-browser.js", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./default.d.ts", "node": "./default.js", "edge-light": "./wasm.js", "workerd": "./wasm.js", "worker": "./wasm.js", "browser": "./index-browser.js"}, "import": {"types": "./default.d.ts", "node": "./default.js", "edge-light": "./wasm.js", "workerd": "./wasm.js", "worker": "./wasm.js", "browser": "./index-browser.js"}, "default": "./default.js"}, "./edge": {"types": "./edge.d.ts", "require": "./edge.js", "import": "./edge.js", "default": "./edge.js"}, "./extension": {"types": "./extension.d.ts", "require": "./extension.js", "import": "./extension.js", "default": "./extension.js"}, "./index-browser": {"types": "./default.d.ts", "require": "./index-browser.js", "import": "./index-browser.js", "default": "./index-browser.js"}, "./index": {"types": "./default.d.ts", "require": "./default.js", "import": "./default.js", "default": "./default.js"}, "./wasm": {"types": "./wasm.d.ts", "require": "./wasm.js", "import": "./wasm.js", "default": "./wasm.js"}, "./runtime/library": {"types": "./runtime/library.d.ts", "require": "./runtime/library.js", "import": "./runtime/library.js", "default": "./runtime/library.js"}, "./runtime/binary": {"types": "./runtime/binary.d.ts", "require": "./runtime/binary.js", "import": "./runtime/binary.js", "default": "./runtime/binary.js"}, "./generator-build": {"require": "./generator-build/index.js", "import": "./generator-build/index.js", "default": "./generator-build/index.js"}, "./*": "./*"}, "version": "5.10.2", "sideEffects": false}