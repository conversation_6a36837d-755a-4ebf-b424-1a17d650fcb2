"use strict";var Ol=Object.create;var At=Object.defineProperty;var Fl=Object.getOwnPropertyDescriptor;var Ml=Object.getOwnPropertyNames;var $l=Object.getPrototypeOf,ql=Object.prototype.hasOwnProperty;var X=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),Or=(e,r)=>{for(var t in r)At(e,t,{get:r[t],enumerable:!0})},to=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of Ml(r))!ql.call(e,i)&&i!==t&&At(e,i,{get:()=>r[i],enumerable:!(n=Fl(r,i))||n.enumerable});return e};var _=(e,r,t)=>(t=e!=null?Ol($l(e)):{},to(r||!e||!e.__esModule?At(t,"default",{value:e,enumerable:!0}):t,e)),Bl=e=>to(At({},"__esModule",{value:!0}),e);var bo=X((Rd,Vn)=>{"use strict";var v=Vn.exports;Vn.exports.default=v;var D="\x1B[",Br="\x1B]",dr="\x07",Nt=";",Eo=process.env.TERM_PROGRAM==="Apple_Terminal";v.cursorTo=(e,r)=>{if(typeof e!="number")throw new TypeError("The `x` argument is required");return typeof r!="number"?D+(e+1)+"G":D+(r+1)+";"+(e+1)+"H"};v.cursorMove=(e,r)=>{if(typeof e!="number")throw new TypeError("The `x` argument is required");let t="";return e<0?t+=D+-e+"D":e>0&&(t+=D+e+"C"),r<0?t+=D+-r+"A":r>0&&(t+=D+r+"B"),t};v.cursorUp=(e=1)=>D+e+"A";v.cursorDown=(e=1)=>D+e+"B";v.cursorForward=(e=1)=>D+e+"C";v.cursorBackward=(e=1)=>D+e+"D";v.cursorLeft=D+"G";v.cursorSavePosition=Eo?"\x1B7":D+"s";v.cursorRestorePosition=Eo?"\x1B8":D+"u";v.cursorGetPosition=D+"6n";v.cursorNextLine=D+"E";v.cursorPrevLine=D+"F";v.cursorHide=D+"?25l";v.cursorShow=D+"?25h";v.eraseLines=e=>{let r="";for(let t=0;t<e;t++)r+=v.eraseLine+(t<e-1?v.cursorUp():"");return e&&(r+=v.cursorLeft),r};v.eraseEndLine=D+"K";v.eraseStartLine=D+"1K";v.eraseLine=D+"2K";v.eraseDown=D+"J";v.eraseUp=D+"1J";v.eraseScreen=D+"2J";v.scrollUp=D+"S";v.scrollDown=D+"T";v.clearScreen="\x1Bc";v.clearTerminal=process.platform==="win32"?`${v.eraseScreen}${D}0f`:`${v.eraseScreen}${D}3J${D}H`;v.beep=dr;v.link=(e,r)=>[Br,"8",Nt,Nt,r,dr,e,Br,"8",Nt,Nt,dr].join("");v.image=(e,r={})=>{let t=`${Br}1337;File=inline=1`;return r.width&&(t+=`;width=${r.width}`),r.height&&(t+=`;height=${r.height}`),r.preserveAspectRatio===!1&&(t+=";preserveAspectRatio=0"),t+":"+e.toString("base64")+dr};v.iTerm={setCwd:(e=process.cwd())=>`${Br}50;CurrentDir=${e}${dr}`,annotation:(e,r={})=>{let t=`${Br}1337;`,n=typeof r.x<"u",i=typeof r.y<"u";if((n||i)&&!(n&&i&&typeof r.length<"u"))throw new Error("`x`, `y` and `length` must be defined when `x` or `y` is defined");return e=e.replace(/\|/g,""),t+=r.isHidden?"AddHiddenAnnotation=":"AddAnnotation=",r.length>0?t+=(n?[e,r.length,r.x,r.y]:[r.length,e]).join("|"):t+=e,t+dr}}});var jn=X((Ad,wo)=>{"use strict";wo.exports=(e,r=process.argv)=>{let t=e.startsWith("-")?"":e.length===1?"-":"--",n=r.indexOf(t+e),i=r.indexOf("--");return n!==-1&&(i===-1||n<i)}});var vo=X((Id,Po)=>{"use strict";var pu=require("os"),xo=require("tty"),pe=jn(),{env:G}=process,je;pe("no-color")||pe("no-colors")||pe("color=false")||pe("color=never")?je=0:(pe("color")||pe("colors")||pe("color=true")||pe("color=always"))&&(je=1);"FORCE_COLOR"in G&&(G.FORCE_COLOR==="true"?je=1:G.FORCE_COLOR==="false"?je=0:je=G.FORCE_COLOR.length===0?1:Math.min(parseInt(G.FORCE_COLOR,10),3));function Un(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Qn(e,r){if(je===0)return 0;if(pe("color=16m")||pe("color=full")||pe("color=truecolor"))return 3;if(pe("color=256"))return 2;if(e&&!r&&je===void 0)return 0;let t=je||0;if(G.TERM==="dumb")return t;if(process.platform==="win32"){let n=pu.release().split(".");return Number(n[0])>=10&&Number(n[2])>=10586?Number(n[2])>=14931?3:2:1}if("CI"in G)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(n=>n in G)||G.CI_NAME==="codeship"?1:t;if("TEAMCITY_VERSION"in G)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(G.TEAMCITY_VERSION)?1:0;if(G.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in G){let n=parseInt((G.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(G.TERM_PROGRAM){case"iTerm.app":return n>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(G.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(G.TERM)||"COLORTERM"in G?1:t}function mu(e){let r=Qn(e,e&&e.isTTY);return Un(r)}Po.exports={supportsColor:mu,stdout:Un(Qn(!0,xo.isatty(1))),stderr:Un(Qn(!0,xo.isatty(2)))}});var So=X((_d,Co)=>{"use strict";var du=vo(),fr=jn();function To(e){if(/^\d{3,4}$/.test(e)){let t=/(\d{1,2})(\d{2})/.exec(e);return{major:0,minor:parseInt(t[1],10),patch:parseInt(t[2],10)}}let r=(e||"").split(".").map(t=>parseInt(t,10));return{major:r[0],minor:r[1],patch:r[2]}}function Gn(e){let{env:r}=process;if("FORCE_HYPERLINK"in r)return!(r.FORCE_HYPERLINK.length>0&&parseInt(r.FORCE_HYPERLINK,10)===0);if(fr("no-hyperlink")||fr("no-hyperlinks")||fr("hyperlink=false")||fr("hyperlink=never"))return!1;if(fr("hyperlink=true")||fr("hyperlink=always")||"NETLIFY"in r)return!0;if(!du.supportsColor(e)||e&&!e.isTTY||process.platform==="win32"||"CI"in r||"TEAMCITY_VERSION"in r)return!1;if("TERM_PROGRAM"in r){let t=To(r.TERM_PROGRAM_VERSION);switch(r.TERM_PROGRAM){case"iTerm.app":return t.major===3?t.minor>=1:t.major>3;case"WezTerm":return t.major>=20200620;case"vscode":return t.major>1||t.major===1&&t.minor>=72}}if("VTE_VERSION"in r){if(r.VTE_VERSION==="0.50.0")return!1;let t=To(r.VTE_VERSION);return t.major>0||t.minor>=50}return!1}Co.exports={supportsHyperlink:Gn,stdout:Gn(process.stdout),stderr:Gn(process.stderr)}});var Ao=X((kd,Vr)=>{"use strict";var fu=bo(),Jn=So(),Ro=(e,r,{target:t="stdout",...n}={})=>Jn[t]?fu.link(e,r):n.fallback===!1?e:typeof n.fallback=="function"?n.fallback(e,r):`${e} (\u200B${r}\u200B)`;Vr.exports=(e,r,t={})=>Ro(e,r,t);Vr.exports.stderr=(e,r,t={})=>Ro(e,r,{target:"stderr",...t});Vr.exports.isSupported=Jn.stdout;Vr.exports.stderr.isSupported=Jn.stderr});var $o=X((Wd,Iu)=>{Iu.exports={name:"dotenv",version:"16.0.3",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{require:"./lib/main.js",types:"./lib/main.d.ts",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard","lint-readme":"standard-markdown",pretest:"npm run lint && npm run dts-check",test:"tap tests/*.js --100 -Rspec",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^17.0.9",decache:"^4.6.1",dtslint:"^3.7.0",sinon:"^12.0.1",standard:"^16.0.4","standard-markdown":"^7.1.0","standard-version":"^9.3.2",tap:"^15.1.6",tar:"^6.1.11",typescript:"^4.5.4"},engines:{node:">=12"}}});var Bo=X((Kd,qt)=>{"use strict";var _u=require("fs"),qo=require("path"),ku=require("os"),Du=$o(),Lu=Du.version,Nu=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function Ou(e){let r={},t=e.toString();t=t.replace(/\r\n?/mg,`
`);let n;for(;(n=Nu.exec(t))!=null;){let i=n[1],o=n[2]||"";o=o.trim();let s=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),s==='"'&&(o=o.replace(/\\n/g,`
`),o=o.replace(/\\r/g,"\r")),r[i]=o}return r}function Kn(e){console.log(`[dotenv@${Lu}][DEBUG] ${e}`)}function Fu(e){return e[0]==="~"?qo.join(ku.homedir(),e.slice(1)):e}function Mu(e){let r=qo.resolve(process.cwd(),".env"),t="utf8",n=!!(e&&e.debug),i=!!(e&&e.override);e&&(e.path!=null&&(r=Fu(e.path)),e.encoding!=null&&(t=e.encoding));try{let o=$t.parse(_u.readFileSync(r,{encoding:t}));return Object.keys(o).forEach(function(s){Object.prototype.hasOwnProperty.call(process.env,s)?(i===!0&&(process.env[s]=o[s]),n&&Kn(i===!0?`"${s}" is already defined in \`process.env\` and WAS overwritten`:`"${s}" is already defined in \`process.env\` and was NOT overwritten`)):process.env[s]=o[s]}),{parsed:o}}catch(o){return n&&Kn(`Failed to load ${r} ${o.message}`),{error:o}}}var $t={config:Mu,parse:Ou};qt.exports.config=$t.config;qt.exports.parse=$t.parse;qt.exports=$t});var Jo=X((tf,Go)=>{"use strict";Go.exports=e=>{let r=e.match(/^[ \t]*(?=\S)/gm);return r?r.reduce((t,n)=>Math.min(t,n.length),1/0):0}});var Wo=X((nf,Ho)=>{"use strict";var Vu=Jo();Ho.exports=e=>{let r=Vu(e);if(r===0)return e;let t=new RegExp(`^[ \\t]{${r}}`,"gm");return e.replace(t,"")}});var Zn=X((of,ju)=>{ju.exports={name:"@prisma/engines-version",version:"5.10.0-34.5a9203d0590c951969e85a7d07215503f4672eb9",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"5a9203d0590c951969e85a7d07215503f4672eb9"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.15",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var Xn=X(Vt=>{"use strict";Object.defineProperty(Vt,"__esModule",{value:!0});Vt.enginesVersion=void 0;Vt.enginesVersion=Zn().prisma.enginesVersion});var ii=X((If,Yo)=>{"use strict";Yo.exports=(e,r=1,t)=>{if(t={indent:" ",includeEmptyLines:!1,...t},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof r!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof r}\``);if(typeof t.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof t.indent}\``);if(r===0)return e;let n=t.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,t.indent.repeat(r))}});var rs=X((Df,es)=>{"use strict";es.exports=({onlyFirst:e=!1}={})=>{let r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}});var li=X((Lf,ts)=>{"use strict";var Zu=rs();ts.exports=e=>typeof e=="string"?e.replace(Zu(),""):e});var ns=X((Ff,Ut)=>{"use strict";Ut.exports=(e={})=>{let r;if(e.repoUrl)r=e.repoUrl;else if(e.user&&e.repo)r=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let t=new URL(`${r}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}t.searchParams.set(i,o)}}return t.toString()};Ut.exports.default=Ut.exports});var Hi=X((C0,Ba)=>{"use strict";Ba.exports=function(){function e(r,t,n,i,o){return r<t||n<t?r>n?n+1:r+1:i===o?t:t+1}return function(r,t){if(r===t)return 0;if(r.length>t.length){var n=r;r=t,t=n}for(var i=r.length,o=t.length;i>0&&r.charCodeAt(i-1)===t.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&r.charCodeAt(s)===t.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,l,u,c,p,m,f,g,h,A,T,C,E,I=[];for(l=0;l<i;l++)I.push(l+1),I.push(r.charCodeAt(s+l));for(var me=I.length-1;a<o-3;)for(A=t.charCodeAt(s+(u=a)),T=t.charCodeAt(s+(c=a+1)),C=t.charCodeAt(s+(p=a+2)),E=t.charCodeAt(s+(m=a+3)),f=a+=4,l=0;l<me;l+=2)g=I[l],h=I[l+1],u=e(g,u,c,A,h),c=e(u,c,p,T,h),p=e(c,p,m,C,h),f=e(p,m,f,E,h),I[l]=f,m=p,p=c,c=u,u=g;for(;a<o;)for(A=t.charCodeAt(s+(u=a)),f=++a,l=0;l<me;l+=2)g=I[l],I[l]=f=e(g,u,f,A,I[l+1]),u=g;return f}}()});var sd={};Or(sd,{Debug:()=>On,Decimal:()=>Te,Extensions:()=>Dn,MetricsClient:()=>yr,NotFoundError:()=>Le,PrismaClientInitializationError:()=>S,PrismaClientKnownRequestError:()=>V,PrismaClientRustPanicError:()=>ue,PrismaClientUnknownRequestError:()=>j,PrismaClientValidationError:()=>K,Public:()=>Ln,Sql:()=>oe,defineDmmfProperty:()=>os,detectRuntime:()=>gn,empty:()=>as,getPrismaClient:()=>Dl,join:()=>ss,makeStrictEnum:()=>Ll,objectEnumValues:()=>Jt,raw:()=>yi,sqltag:()=>Ei,warnEnvConflicts:()=>Nl,warnOnce:()=>Kr});module.exports=Bl(sd);var Dn={};Or(Dn,{defineExtension:()=>no,getExtensionContext:()=>io});function no(e){return typeof e=="function"?e:r=>r.$extends(e)}function io(e){return e}var Ln={};Or(Ln,{validator:()=>oo});function oo(...e){return r=>r}var It={};Or(It,{$:()=>co,bgBlack:()=>zl,bgBlue:()=>eu,bgCyan:()=>tu,bgGreen:()=>Zl,bgMagenta:()=>ru,bgRed:()=>Yl,bgWhite:()=>nu,bgYellow:()=>Xl,black:()=>Jl,blue:()=>Ze,bold:()=>W,cyan:()=>_e,dim:()=>Ie,gray:()=>Fr,green:()=>$e,grey:()=>Kl,hidden:()=>Ql,inverse:()=>Ul,italic:()=>jl,magenta:()=>Hl,red:()=>ce,reset:()=>Vl,strikethrough:()=>Gl,underline:()=>ee,white:()=>Wl,yellow:()=>de});var Nn,so,ao,lo,uo=!0;typeof process<"u"&&({FORCE_COLOR:Nn,NODE_DISABLE_COLORS:so,NO_COLOR:ao,TERM:lo}=process.env||{},uo=process.stdout&&process.stdout.isTTY);var co={enabled:!so&&ao==null&&lo!=="dumb"&&(Nn!=null&&Nn!=="0"||uo)};function F(e,r){let t=new RegExp(`\\x1b\\[${r}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${r}m`;return function(o){return!co.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(t,i+n):o)+i}}var Vl=F(0,0),W=F(1,22),Ie=F(2,22),jl=F(3,23),ee=F(4,24),Ul=F(7,27),Ql=F(8,28),Gl=F(9,29),Jl=F(30,39),ce=F(31,39),$e=F(32,39),de=F(33,39),Ze=F(34,39),Hl=F(35,39),_e=F(36,39),Wl=F(37,39),Fr=F(90,39),Kl=F(90,39),zl=F(40,49),Yl=F(41,49),Zl=F(42,49),Xl=F(43,49),eu=F(44,49),ru=F(45,49),tu=F(46,49),nu=F(47,49);var iu=100,po=["green","yellow","blue","magenta","cyan","red"],Mr=[],mo=Date.now(),ou=0;globalThis.DEBUG??(globalThis.DEBUG=process.env.DEBUG??"");globalThis.DEBUG_COLORS??(globalThis.DEBUG_COLORS=process.env.DEBUG_COLORS?process.env.DEBUG_COLORS==="true":!0);var $r={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let r=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),t=r.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=r.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return t&&!n},log:(...e)=>{let[r,t,...n]=e,i;typeof require=="function"&&typeof process<"u"&&typeof process.stderr<"u"&&typeof process.stderr.write=="function"?i=(...o)=>{let s=require("util");process.stderr.write(s.format(...o)+`
`)}:i=console.warn??console.log,i(`${r} ${t}`,...n)},formatters:{}};function su(e){let r={color:po[ou++%po.length],enabled:$r.enabled(e),namespace:e,log:$r.log,extend:()=>{}},t=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=r;if(n.length!==0&&Mr.push([o,...n]),Mr.length>iu&&Mr.shift(),$r.enabled(o)||i){let l=n.map(c=>typeof c=="string"?c:au(c)),u=`+${Date.now()-mo}ms`;mo=Date.now(),globalThis.DEBUG_COLORS?a(It[s](W(o)),...l,It[s](u)):a(o,...l,u)}};return new Proxy(t,{get:(n,i)=>r[i],set:(n,i,o)=>r[i]=o})}var On=new Proxy(su,{get:(e,r)=>$r[r],set:(e,r,t)=>$r[r]=t});function au(e,r=2){let t=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(t.has(i))return"[Circular *]";t.add(i)}else if(typeof i=="bigint")return i.toString();return i},r)}function fo(e=7500){let r=Mr.map(([t,...n])=>`${t} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return r.length<e?r:r.slice(-e)}function go(){Mr.length=0}var N=On;var ho=_(require("fs"));function Fn(){let e=process.env.PRISMA_QUERY_ENGINE_LIBRARY;if(!(e&&ho.default.existsSync(e))&&process.arch==="ia32")throw new Error('The default query engine type (Node-API, "library") is currently not supported for 32bit Node. Please set `engineType = "binary"` in the "generator" block of your "schema.prisma" file (or use the environment variables "PRISMA_CLIENT_ENGINE_TYPE=binary" and/or "PRISMA_CLI_QUERY_ENGINE_TYPE=binary".)')}var Mn=["darwin","darwin-arm64","debian-openssl-1.0.x","debian-openssl-1.1.x","debian-openssl-3.0.x","rhel-openssl-1.0.x","rhel-openssl-1.1.x","rhel-openssl-3.0.x","linux-arm64-openssl-1.1.x","linux-arm64-openssl-1.0.x","linux-arm64-openssl-3.0.x","linux-arm-openssl-1.1.x","linux-arm-openssl-1.0.x","linux-arm-openssl-3.0.x","linux-musl","linux-musl-openssl-3.0.x","linux-musl-arm64-openssl-1.1.x","linux-musl-arm64-openssl-3.0.x","linux-nixos","linux-static-x64","linux-static-arm64","windows","freebsd11","freebsd12","freebsd13","freebsd14","freebsd15","openbsd","netbsd","arm"];var _t="libquery_engine";function kt(e,r){let t=r==="url";return e.includes("windows")?t?"query_engine.dll.node":`query_engine-${e}.dll.node`:e.includes("darwin")?t?`${_t}.dylib.node`:`${_t}-${e}.dylib.node`:t?`${_t}.so.node`:`${_t}-${e}.so.node`}var Do=_(require("child_process")),Hn=_(require("fs/promises")),Ft=_(require("os"));var ke=Symbol.for("@ts-pattern/matcher"),lu=Symbol.for("@ts-pattern/isVariadic"),Lt="@ts-pattern/anonymous-select-key",$n=e=>!!(e&&typeof e=="object"),Dt=e=>e&&!!e[ke],we=(e,r,t)=>{if(Dt(e)){let n=e[ke](),{matched:i,selections:o}=n.match(r);return i&&o&&Object.keys(o).forEach(s=>t(s,o[s])),i}if($n(e)){if(!$n(r))return!1;if(Array.isArray(e)){if(!Array.isArray(r))return!1;let n=[],i=[],o=[];for(let s of e.keys()){let a=e[s];Dt(a)&&a[lu]?o.push(a):o.length?i.push(a):n.push(a)}if(o.length){if(o.length>1)throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(r.length<n.length+i.length)return!1;let s=r.slice(0,n.length),a=i.length===0?[]:r.slice(-i.length),l=r.slice(n.length,i.length===0?1/0:-i.length);return n.every((u,c)=>we(u,s[c],t))&&i.every((u,c)=>we(u,a[c],t))&&(o.length===0||we(o[0],l,t))}return e.length===r.length&&e.every((s,a)=>we(s,r[a],t))}return Object.keys(e).every(n=>{let i=e[n];return(n in r||Dt(o=i)&&o[ke]().matcherType==="optional")&&we(i,r[n],t);var o})}return Object.is(r,e)},Ve=e=>{var r,t,n;return $n(e)?Dt(e)?(r=(t=(n=e[ke]()).getSelectionKeys)==null?void 0:t.call(n))!=null?r:[]:Array.isArray(e)?qr(e,Ve):qr(Object.values(e),Ve):[]},qr=(e,r)=>e.reduce((t,n)=>t.concat(r(n)),[]);function fe(e){return Object.assign(e,{optional:()=>uu(e),and:r=>B(e,r),or:r=>cu(e,r),select:r=>r===void 0?yo(e):yo(r,e)})}function uu(e){return fe({[ke]:()=>({match:r=>{let t={},n=(i,o)=>{t[i]=o};return r===void 0?(Ve(e).forEach(i=>n(i,void 0)),{matched:!0,selections:t}):{matched:we(e,r,n),selections:t}},getSelectionKeys:()=>Ve(e),matcherType:"optional"})})}function B(...e){return fe({[ke]:()=>({match:r=>{let t={},n=(i,o)=>{t[i]=o};return{matched:e.every(i=>we(i,r,n)),selections:t}},getSelectionKeys:()=>qr(e,Ve),matcherType:"and"})})}function cu(...e){return fe({[ke]:()=>({match:r=>{let t={},n=(i,o)=>{t[i]=o};return qr(e,Ve).forEach(i=>n(i,void 0)),{matched:e.some(i=>we(i,r,n)),selections:t}},getSelectionKeys:()=>qr(e,Ve),matcherType:"or"})})}function k(e){return{[ke]:()=>({match:r=>({matched:!!e(r)})})}}function yo(...e){let r=typeof e[0]=="string"?e[0]:void 0,t=e.length===2?e[1]:typeof e[0]=="string"?void 0:e[0];return fe({[ke]:()=>({match:n=>{let i={[r??Lt]:n};return{matched:t===void 0||we(t,n,(o,s)=>{i[o]=s}),selections:i}},getSelectionKeys:()=>[r??Lt].concat(t===void 0?[]:Ve(t))})})}function Ee(e){return typeof e=="number"}function Xe(e){return typeof e=="string"}function qe(e){return typeof e=="bigint"}var bd=fe(k(function(e){return!0}));var er=e=>Object.assign(fe(e),{startsWith:r=>{return er(B(e,(t=r,k(n=>Xe(n)&&n.startsWith(t)))));var t},endsWith:r=>{return er(B(e,(t=r,k(n=>Xe(n)&&n.endsWith(t)))));var t},minLength:r=>er(B(e,(t=>k(n=>Xe(n)&&n.length>=t))(r))),maxLength:r=>er(B(e,(t=>k(n=>Xe(n)&&n.length<=t))(r))),includes:r=>{return er(B(e,(t=r,k(n=>Xe(n)&&n.includes(t)))));var t},regex:r=>{return er(B(e,(t=r,k(n=>Xe(n)&&!!n.match(t)))));var t}}),wd=er(k(Xe)),be=e=>Object.assign(fe(e),{between:(r,t)=>be(B(e,((n,i)=>k(o=>Ee(o)&&n<=o&&i>=o))(r,t))),lt:r=>be(B(e,(t=>k(n=>Ee(n)&&n<t))(r))),gt:r=>be(B(e,(t=>k(n=>Ee(n)&&n>t))(r))),lte:r=>be(B(e,(t=>k(n=>Ee(n)&&n<=t))(r))),gte:r=>be(B(e,(t=>k(n=>Ee(n)&&n>=t))(r))),int:()=>be(B(e,k(r=>Ee(r)&&Number.isInteger(r)))),finite:()=>be(B(e,k(r=>Ee(r)&&Number.isFinite(r)))),positive:()=>be(B(e,k(r=>Ee(r)&&r>0))),negative:()=>be(B(e,k(r=>Ee(r)&&r<0)))}),xd=be(k(Ee)),Be=e=>Object.assign(fe(e),{between:(r,t)=>Be(B(e,((n,i)=>k(o=>qe(o)&&n<=o&&i>=o))(r,t))),lt:r=>Be(B(e,(t=>k(n=>qe(n)&&n<t))(r))),gt:r=>Be(B(e,(t=>k(n=>qe(n)&&n>t))(r))),lte:r=>Be(B(e,(t=>k(n=>qe(n)&&n<=t))(r))),gte:r=>Be(B(e,(t=>k(n=>qe(n)&&n>=t))(r))),positive:()=>Be(B(e,k(r=>qe(r)&&r>0))),negative:()=>Be(B(e,k(r=>qe(r)&&r<0)))}),Pd=Be(k(qe)),vd=fe(k(function(e){return typeof e=="boolean"})),Td=fe(k(function(e){return typeof e=="symbol"})),Cd=fe(k(function(e){return e==null}));var qn={matched:!1,value:void 0};function mr(e){return new Bn(e,qn)}var Bn=class e{constructor(r,t){this.input=void 0,this.state=void 0,this.input=r,this.state=t}with(...r){if(this.state.matched)return this;let t=r[r.length-1],n=[r[0]],i;r.length===3&&typeof r[1]=="function"?i=r[1]:r.length>2&&n.push(...r.slice(1,r.length-1));let o=!1,s={},a=(u,c)=>{o=!0,s[u]=c},l=!n.some(u=>we(u,this.input,a))||i&&!i(this.input)?qn:{matched:!0,value:t(o?Lt in s?s[Lt]:s:this.input,this.input)};return new e(this.input,l)}when(r,t){if(this.state.matched)return this;let n=!!r(this.input);return new e(this.input,n?{matched:!0,value:t(this.input,this.input)}:qn)}otherwise(r){return this.state.matched?this.state.value:r(this.input)}exhaustive(){if(this.state.matched)return this.state.value;let r;try{r=JSON.stringify(this.input)}catch{r=this.input}throw new Error(`Pattern matching error: no pattern matches value ${r}`)}run(){return this.exhaustive()}returnType(){return this}};var Lo=require("util");var Io=_(Ao());function jr(e){return(0,Io.default)(e,e,{fallback:ee})}var gu={warn:de("prisma:warn")},hu={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function Ur(e,...r){hu.warn()&&console.warn(`${gu.warn} ${e}`,...r)}var yu=(0,Lo.promisify)(Do.default.exec),ie=N("prisma:get-platform"),Eu=["1.0.x","1.1.x","3.0.x"];async function No(){let e=Ft.default.platform(),r=process.arch;if(e==="freebsd"){let s=await Mt("freebsd-version");if(s&&s.trim().length>0){let l=/^(\d+)\.?/.exec(s);if(l)return{platform:"freebsd",targetDistro:`freebsd${l[1]}`,arch:r}}}if(e!=="linux")return{platform:e,arch:r};let t=await wu(),n=await Au(),i=Pu({arch:r,archFromUname:n,familyDistro:t.familyDistro}),{libssl:o}=await vu(i);return{platform:"linux",libssl:o,arch:r,archFromUname:n,...t}}function bu(e){let r=/^ID="?([^"\n]*)"?$/im,t=/^ID_LIKE="?([^"\n]*)"?$/im,n=r.exec(e),i=n&&n[1]&&n[1].toLowerCase()||"",o=t.exec(e),s=o&&o[1]&&o[1].toLowerCase()||"",a=mr({id:i,idLike:s}).with({id:"alpine"},({id:l})=>({targetDistro:"musl",familyDistro:l,originalDistro:l})).with({id:"raspbian"},({id:l})=>({targetDistro:"arm",familyDistro:"debian",originalDistro:l})).with({id:"nixos"},({id:l})=>({targetDistro:"nixos",originalDistro:l,familyDistro:"nixos"})).with({id:"debian"},{id:"ubuntu"},({id:l})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:l})).with({id:"rhel"},{id:"centos"},{id:"fedora"},({id:l})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:l})).when(({idLike:l})=>l.includes("debian")||l.includes("ubuntu"),({id:l})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:l})).when(({idLike:l})=>i==="arch"||l.includes("arch"),({id:l})=>({targetDistro:"debian",familyDistro:"arch",originalDistro:l})).when(({idLike:l})=>l.includes("centos")||l.includes("fedora")||l.includes("rhel")||l.includes("suse"),({id:l})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:l})).otherwise(({id:l})=>({targetDistro:void 0,familyDistro:void 0,originalDistro:l}));return ie(`Found distro info:
${JSON.stringify(a,null,2)}`),a}async function wu(){let e="/etc/os-release";try{let r=await Hn.default.readFile(e,{encoding:"utf-8"});return bu(r)}catch{return{targetDistro:void 0,familyDistro:void 0,originalDistro:void 0}}}function xu(e){let r=/^OpenSSL\s(\d+\.\d+)\.\d+/.exec(e);if(r){let t=`${r[1]}.x`;return Oo(t)}}function _o(e){let r=/libssl\.so\.(\d)(\.\d)?/.exec(e);if(r){let t=`${r[1]}${r[2]??".0"}.x`;return Oo(t)}}function Oo(e){let r=(()=>{if(Mo(e))return e;let t=e.split(".");return t[1]="0",t.join(".")})();if(Eu.includes(r))return r}function Pu(e){return mr(e).with({familyDistro:"musl"},()=>(ie('Trying platform-specific paths for "alpine"'),["/lib"])).with({familyDistro:"debian"},({archFromUname:r})=>(ie('Trying platform-specific paths for "debian" (and "ubuntu")'),[`/usr/lib/${r}-linux-gnu`,`/lib/${r}-linux-gnu`])).with({familyDistro:"rhel"},()=>(ie('Trying platform-specific paths for "rhel"'),["/lib64","/usr/lib64"])).otherwise(({familyDistro:r,arch:t,archFromUname:n})=>(ie(`Don't know any platform-specific paths for "${r}" on ${t} (${n})`),[]))}async function vu(e){let r='grep -v "libssl.so.0"',t=await ko(e);if(t){ie(`Found libssl.so file using platform-specific paths: ${t}`);let o=_o(t);if(ie(`The parsed libssl version is: ${o}`),o)return{libssl:o,strategy:"libssl-specific-path"}}ie('Falling back to "ldconfig" and other generic paths');let n=await Mt(`ldconfig -p | sed "s/.*=>s*//" | sed "s|.*/||" | grep libssl | sort | ${r}`);if(n||(n=await ko(["/lib64","/usr/lib64","/lib"])),n){ie(`Found libssl.so file using "ldconfig" or other generic paths: ${n}`);let o=_o(n);if(ie(`The parsed libssl version is: ${o}`),o)return{libssl:o,strategy:"ldconfig"}}let i=await Mt("openssl version -v");if(i){ie(`Found openssl binary with version: ${i}`);let o=xu(i);if(ie(`The parsed openssl version is: ${o}`),o)return{libssl:o,strategy:"openssl-binary"}}return ie("Couldn't find any version of libssl or OpenSSL in the system"),{}}async function ko(e){for(let r of e){let t=await Tu(r);if(t)return t}}async function Tu(e){try{return(await Hn.default.readdir(e)).find(t=>t.startsWith("libssl.so.")&&!t.startsWith("libssl.so.0"))}catch(r){if(r.code==="ENOENT")return;throw r}}async function rr(){let{binaryTarget:e}=await Fo();return e}function Cu(e){return e.binaryTarget!==void 0}async function Wn(){let{memoized:e,...r}=await Fo();return r}var Ot={};async function Fo(){if(Cu(Ot))return Promise.resolve({...Ot,memoized:!0});let e=await No(),r=Su(e);return Ot={...e,binaryTarget:r},{...Ot,memoized:!1}}function Su(e){let{platform:r,arch:t,archFromUname:n,libssl:i,targetDistro:o,familyDistro:s,originalDistro:a}=e;r==="linux"&&!["x64","arm64"].includes(t)&&Ur(`Prisma only officially supports Linux on amd64 (x86_64) and arm64 (aarch64) system architectures. If you are using your own custom Prisma engines, you can ignore this warning, as long as you've compiled the engines for your system architecture "${n}".`);let l="1.1.x";if(r==="linux"&&i===void 0){let c=mr({familyDistro:s}).with({familyDistro:"debian"},()=>"Please manually install OpenSSL via `apt-get update -y && apt-get install -y openssl` and try installing Prisma again. If you're running Prisma on Docker, add this command to your Dockerfile, or switch to an image that already has OpenSSL installed.").otherwise(()=>"Please manually install OpenSSL and try installing Prisma again.");Ur(`Prisma failed to detect the libssl/openssl version to use, and may not work as expected. Defaulting to "openssl-${l}".
${c}`)}let u="debian";if(r==="linux"&&o===void 0&&Ur(`Prisma doesn't know which engines to download for the Linux distro "${a}". Falling back to Prisma engines built "${u}".
Please report your experience by creating an issue at ${jr("https://github.com/prisma/prisma/issues")} so we can add your distro to the list of known supported distros.`),r==="darwin"&&t==="arm64")return"darwin-arm64";if(r==="darwin")return"darwin";if(r==="win32")return"windows";if(r==="freebsd")return o;if(r==="openbsd")return"openbsd";if(r==="netbsd")return"netbsd";if(r==="linux"&&o==="nixos")return"linux-nixos";if(r==="linux"&&t==="arm64")return`${o==="musl"?"linux-musl-arm64":"linux-arm64"}-openssl-${i||l}`;if(r==="linux"&&t==="arm")return`linux-arm-openssl-${i||l}`;if(r==="linux"&&o==="musl"){let c="linux-musl";return!i||Mo(i)?c:`${c}-openssl-${i}`}return r==="linux"&&o&&i?`${o}-openssl-${i}`:(r!=="linux"&&Ur(`Prisma detected unknown OS "${r}" and may not work as expected. Defaulting to "linux".`),i?`${u}-openssl-${i}`:o?`${o}-openssl-${l}`:`${u}-openssl-${l}`)}async function Ru(e){try{return await e()}catch{return}}function Mt(e){return Ru(async()=>{let r=await yu(e);return ie(`Command "${e}" successfully returned "${r.stdout}"`),r.stdout})}async function Au(){return typeof Ft.default.machine=="function"?Ft.default.machine():(await Mt("uname -m"))?.trim()}function Mo(e){return e.startsWith("1.")}var Yn=_(Bo()),Bt=_(require("fs"));var gr=_(require("path"));function Vo(e){let r=e.ignoreProcessEnv?{}:process.env,t=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o,s){let a=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);if(!a)return o;let l=a[1],u,c;if(l==="\\")c=a[0],u=c.replace("\\$","$");else{let p=a[2];c=a[0].substring(l.length),u=Object.hasOwnProperty.call(r,p)?r[p]:e.parsed[p]||"",u=t(u)}return o.replace(c,u)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(r,n)?r[n]:e.parsed[n];e.parsed[n]=t(i)}for(let n in e.parsed)r[n]=e.parsed[n];return e}var zn=N("prisma:tryLoadEnv");function Qr({rootEnvPath:e,schemaEnvPath:r},t={conflictCheck:"none"}){let n=jo(e);t.conflictCheck!=="none"&&$u(n,r,t.conflictCheck);let i=null;return Uo(n?.path,r)||(i=jo(r)),!n&&!i&&zn("No Environment variables loaded"),i?.dotenvResult.error?console.error(ce(W("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function $u(e,r,t){let n=e?.dotenvResult.parsed,i=!Uo(e?.path,r);if(n&&r&&i&&Bt.default.existsSync(r)){let o=Yn.default.parse(Bt.default.readFileSync(r)),s=[];for(let a in o)n[a]===o[a]&&s.push(a);if(s.length>0){let a=gr.default.relative(process.cwd(),e.path),l=gr.default.relative(process.cwd(),r);if(t==="error"){let u=`There is a conflict between env var${s.length>1?"s":""} in ${ee(a)} and ${ee(l)}
Conflicting env vars:
${s.map(c=>`  ${W(c)}`).join(`
`)}

We suggest to move the contents of ${ee(l)} to ${ee(a)} to consolidate your env vars.
`;throw new Error(u)}else if(t==="warn"){let u=`Conflict for env var${s.length>1?"s":""} ${s.map(c=>W(c)).join(", ")} in ${ee(a)} and ${ee(l)}
Env vars from ${ee(l)} overwrite the ones from ${ee(a)}
      `;console.warn(`${de("warn(prisma)")} ${u}`)}}}}function jo(e){if(qu(e)){zn(`Environment variables loaded from ${e}`);let r=Yn.default.config({path:e,debug:process.env.DOTENV_CONFIG_DEBUG?!0:void 0});return{dotenvResult:Vo(r),message:Ie(`Environment variables loaded from ${gr.default.relative(process.cwd(),e)}`),path:e}}else zn(`Environment variables not found at ${e}`);return null}function Uo(e,r){return e&&r&&gr.default.resolve(e)===gr.default.resolve(r)}function qu(e){return!!(e&&Bt.default.existsSync(e))}var Qo="library";function Gr(e){let r=Bu();return r||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":Qo)}function Bu(){let e=process.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":void 0}var Uu=_(Xn());var M=_(require("path")),Qu=_(Xn()),gf=N("prisma:engines");function Ko(){return M.default.join(__dirname,"../")}var hf="libquery-engine";M.default.join(__dirname,"../query-engine-darwin");M.default.join(__dirname,"../query-engine-darwin-arm64");M.default.join(__dirname,"../query-engine-debian-openssl-1.0.x");M.default.join(__dirname,"../query-engine-debian-openssl-1.1.x");M.default.join(__dirname,"../query-engine-debian-openssl-3.0.x");M.default.join(__dirname,"../query-engine-linux-static-x64");M.default.join(__dirname,"../query-engine-linux-static-arm64");M.default.join(__dirname,"../query-engine-rhel-openssl-1.0.x");M.default.join(__dirname,"../query-engine-rhel-openssl-1.1.x");M.default.join(__dirname,"../query-engine-rhel-openssl-3.0.x");M.default.join(__dirname,"../libquery_engine-darwin.dylib.node");M.default.join(__dirname,"../libquery_engine-darwin-arm64.dylib.node");M.default.join(__dirname,"../libquery_engine-debian-openssl-1.0.x.so.node");M.default.join(__dirname,"../libquery_engine-debian-openssl-1.1.x.so.node");M.default.join(__dirname,"../libquery_engine-debian-openssl-3.0.x.so.node");M.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.0.x.so.node");M.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.1.x.so.node");M.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-3.0.x.so.node");M.default.join(__dirname,"../libquery_engine-linux-musl.so.node");M.default.join(__dirname,"../libquery_engine-linux-musl-openssl-3.0.x.so.node");M.default.join(__dirname,"../libquery_engine-rhel-openssl-1.0.x.so.node");M.default.join(__dirname,"../libquery_engine-rhel-openssl-1.1.x.so.node");M.default.join(__dirname,"../libquery_engine-rhel-openssl-3.0.x.so.node");M.default.join(__dirname,"../query_engine-windows.dll.node");var ei=_(require("fs")),zo=N("chmodPlusX");function ri(e){if(process.platform==="win32")return;let r=ei.default.statSync(e),t=r.mode|64|8|1;if(r.mode===t){zo(`Execution permissions of ${e} are fine`);return}let n=t.toString(8).slice(-3);zo(`Have to call chmodPlusX on ${e}`),ei.default.chmodSync(e,n)}function ti(e){let r=e.e,t=a=>`Prisma cannot find the required \`${a}\` system library in your system`,n=r.message.includes("cannot open shared object file"),i=`Please refer to the documentation about Prisma's system requirements: ${jr("https://pris.ly/d/system-requirements")}`,o=`Unable to require(\`${Ie(e.id)}\`).`,s=mr({message:r.message,code:r.code}).with({code:"ENOENT"},()=>"File does not exist.").when(({message:a})=>n&&a.includes("libz"),()=>`${t("libz")}. Please install it and try again.`).when(({message:a})=>n&&a.includes("libgcc_s"),()=>`${t("libgcc_s")}. Please install it and try again.`).when(({message:a})=>n&&a.includes("libssl"),()=>{let a=e.platformInfo.libssl?`openssl-${e.platformInfo.libssl}`:"openssl";return`${t("libssl")}. Please install ${a} and try again.`}).when(({message:a})=>a.includes("GLIBC"),()=>`Prisma has detected an incompatible version of the \`glibc\` C standard library installed in your system. This probably means your system may be too old to run Prisma. ${i}`).when(({message:a})=>e.platformInfo.platform==="linux"&&a.includes("symbol not found"),()=>`The Prisma engines are not compatible with your system ${e.platformInfo.originalDistro} on (${e.platformInfo.archFromUname}) which uses the \`${e.platformInfo.binaryTarget}\` binaryTarget by default. ${i}`).otherwise(()=>`The Prisma engines do not seem to be compatible with your system. ${i}`);return`${o}
${s}

Details: ${r.message}`}var De;(r=>{let e;(E=>(E.findUnique="findUnique",E.findUniqueOrThrow="findUniqueOrThrow",E.findFirst="findFirst",E.findFirstOrThrow="findFirstOrThrow",E.findMany="findMany",E.create="create",E.createMany="createMany",E.update="update",E.updateMany="updateMany",E.upsert="upsert",E.delete="delete",E.deleteMany="deleteMany",E.groupBy="groupBy",E.count="count",E.aggregate="aggregate",E.findRaw="findRaw",E.aggregateRaw="aggregateRaw"))(e=r.ModelAction||(r.ModelAction={}))})(De||(De={}));var Jr=_(require("path"));function ni(e){return Jr.default.sep===Jr.default.posix.sep?e:e.split(Jr.default.sep).join(Jr.default.posix.sep)}var Zo=_(ii());function si(e){return String(new oi(e))}var oi=class{constructor(r){this.config=r}toString(){let{config:r}=this,t=r.provider.fromEnvVar?`env("${r.provider.fromEnvVar}")`:r.provider.value,n=JSON.parse(JSON.stringify({provider:t,binaryTargets:Gu(r.binaryTargets)}));return`generator ${r.name} {
${(0,Zo.default)(Ju(n),2)}
}`}};function Gu(e){let r;if(e.length>0){let t=e.find(n=>n.fromEnvVar!==null);t?r=`env("${t.fromEnvVar}")`:r=e.map(n=>n.native?"native":n.value)}else r=void 0;return r}function Ju(e){let r=Object.keys(e).reduce((t,n)=>Math.max(t,n.length),0);return Object.entries(e).map(([t,n])=>`${t.padEnd(r)} = ${Hu(n)}`).join(`
`)}function Hu(e){return JSON.parse(JSON.stringify(e,(r,t)=>Array.isArray(t)?`[${t.map(n=>JSON.stringify(n)).join(", ")}]`:JSON.stringify(t)))}var Wr={};Or(Wr,{error:()=>zu,info:()=>Ku,log:()=>Wu,query:()=>Yu,should:()=>Xo,tags:()=>Hr,warn:()=>ai});var Hr={error:ce("prisma:error"),warn:de("prisma:warn"),info:_e("prisma:info"),query:Ze("prisma:query")},Xo={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function Wu(...e){console.log(...e)}function ai(e,...r){Xo.warn()&&console.warn(`${Hr.warn} ${e}`,...r)}function Ku(e,...r){console.info(`${Hr.info} ${e}`,...r)}function zu(e,...r){console.error(`${Hr.error} ${e}`,...r)}function Yu(e,...r){console.log(`${Hr.query} ${e}`,...r)}function jt(e,r){if(!e)throw new Error(`${r}. This should never happen. If you see this error, please, open an issue at https://pris.ly/prisma-prisma-bug-report`)}function tr(e,r){throw new Error(r)}function ui(e,r){return Object.prototype.hasOwnProperty.call(e,r)}var ci=(e,r)=>e.reduce((t,n)=>(t[r(n)]=n,t),{});function hr(e,r){let t={};for(let n of Object.keys(e))t[n]=r(e[n],n);return t}function pi(e,r){if(e.length===0)return;let t=e[0];for(let n=1;n<e.length;n++)r(t,e[n])<0&&(t=e[n]);return t}function w(e,r){Object.defineProperty(e,"name",{value:r,configurable:!0})}var is=new Set,Kr=(e,r,...t)=>{is.has(e)||(is.add(e),ai(r,...t))};var V=class extends Error{constructor(r,{code:t,clientVersion:n,meta:i,batchRequestIdx:o}){super(r),this.name="PrismaClientKnownRequestError",this.code=t,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};w(V,"PrismaClientKnownRequestError");var Le=class extends V{constructor(r,t){super(r,{code:"P2025",clientVersion:t}),this.name="NotFoundError"}};w(Le,"NotFoundError");var S=class e extends Error{constructor(r,t,n){super(r),this.name="PrismaClientInitializationError",this.clientVersion=t,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};w(S,"PrismaClientInitializationError");var ue=class extends Error{constructor(r,t){super(r),this.name="PrismaClientRustPanicError",this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};w(ue,"PrismaClientRustPanicError");var j=class extends Error{constructor(r,{clientVersion:t,batchRequestIdx:n}){super(r),this.name="PrismaClientUnknownRequestError",this.clientVersion=t,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};w(j,"PrismaClientUnknownRequestError");var K=class extends Error{constructor(t,{clientVersion:n}){super(t);this.name="PrismaClientValidationError";this.clientVersion=n}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};w(K,"PrismaClientValidationError");var yr=class{constructor(r){this._engine=r}prometheus(r){return this._engine.metrics({format:"prometheus",...r})}json(r){return this._engine.metrics({format:"json",...r})}};function zr(e){let r;return{get(){return r||(r={value:e()}),r.value}}}function os(e,r){let t=zr(()=>Xu(r));Object.defineProperty(e,"dmmf",{get:()=>t.get()})}function Xu(e){return{datamodel:{models:mi(e.models),enums:mi(e.enums),types:mi(e.types)}}}function mi(e){return Object.entries(e).map(([r,t])=>({name:r,...t}))}var Gt=Symbol(),di=new WeakMap,Ne=class{constructor(r){r===Gt?di.set(this,`Prisma.${this._getName()}`):di.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return di.get(this)}},Yr=class extends Ne{_getNamespace(){return"NullTypes"}},Zr=class extends Yr{};fi(Zr,"DbNull");var Xr=class extends Yr{};fi(Xr,"JsonNull");var et=class extends Yr{};fi(et,"AnyNull");var Jt={classes:{DbNull:Zr,JsonNull:Xr,AnyNull:et},instances:{DbNull:new Zr(Gt),JsonNull:new Xr(Gt),AnyNull:new et(Gt)}};function fi(e,r){Object.defineProperty(e,"name",{value:r,configurable:!0})}function rt(e){return{ok:!1,error:e,map(){return rt(e)},flatMap(){return rt(e)}}}var gi=class{constructor(){this.registeredErrors=[]}consumeError(r){return this.registeredErrors[r]}registerNewError(r){let t=0;for(;this.registeredErrors[t]!==void 0;)t++;return this.registeredErrors[t]={error:r},t}},hi=e=>{let r=new gi,t=nr(r,e.startTransaction.bind(e)),n={errorRegistry:r,queryRaw:nr(r,e.queryRaw.bind(e)),executeRaw:nr(r,e.executeRaw.bind(e)),provider:e.provider,startTransaction:async(...i)=>(await t(...i)).map(s=>ec(r,s))};return e.getConnectionInfo&&(n.getConnectionInfo=rc(r,e.getConnectionInfo.bind(e))),n},ec=(e,r)=>({provider:r.provider,options:r.options,queryRaw:nr(e,r.queryRaw.bind(r)),executeRaw:nr(e,r.executeRaw.bind(r)),commit:nr(e,r.commit.bind(r)),rollback:nr(e,r.rollback.bind(r))});function nr(e,r){return async(...t)=>{try{return await r(...t)}catch(n){let i=e.registerNewError(n);return rt({kind:"GenericJs",id:i})}}}function rc(e,r){return(...t)=>{try{return r(...t)}catch(n){let i=e.registerNewError(n);return rt({kind:"GenericJs",id:i})}}}var Al=_(Zn());var Il=require("async_hooks"),_l=require("events"),kl=_(require("fs")),St=_(require("path"));var oe=class e{constructor(r,t){if(r.length-1!==t.length)throw r.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${r.length} strings to have ${r.length-1} values`);let n=t.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=r[0];let i=0,o=0;for(;i<t.length;){let s=t[i++],a=r[i];if(s instanceof e){this.strings[o]+=s.strings[0];let l=0;for(;l<s.values.length;)this.values[o++]=s.values[l++],this.strings[o]=s.strings[l];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get text(){let r=this.strings.length,t=1,n=this.strings[0];for(;t<r;)n+=`$${t}${this.strings[t++]}`;return n}get sql(){let r=this.strings.length,t=1,n=this.strings[0];for(;t<r;)n+=`?${this.strings[t++]}`;return n}get statement(){let r=this.strings.length,t=1,n=this.strings[0];for(;t<r;)n+=`:${t}${this.strings[t++]}`;return n}inspect(){return{text:this.text,sql:this.sql,values:this.values}}};function ss(e,r=",",t="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new oe([t,...Array(e.length-1).fill(r),n],e)}function yi(e){return new oe([e],[])}var as=yi("");function Ei(e,...r){return new oe(e,r)}function tt(e){return{getKeys(){return Object.keys(e)},getPropertyValue(r){return e[r]}}}function te(e,r){return{getKeys(){return[e]},getPropertyValue(){return r()}}}var xe=class{constructor(){this._map=new Map}get(r){return this._map.get(r)?.value}set(r,t){this._map.set(r,{value:t})}getOrCreate(r,t){let n=this._map.get(r);if(n)return n.value;let i=t();return this.set(r,i),i}};function ir(e){let r=new xe;return{getKeys(){return e.getKeys()},getPropertyValue(t){return r.getOrCreate(t,()=>e.getPropertyValue(t))},getPropertyDescriptor(t){return e.getPropertyDescriptor?.(t)}}}var cs=require("util");var Ht={enumerable:!0,configurable:!0,writable:!0};function Wt(e){let r=new Set(e);return{getOwnPropertyDescriptor:()=>Ht,has:(t,n)=>r.has(n),set:(t,n,i)=>r.add(n)&&Reflect.set(t,n,i),ownKeys:()=>[...r]}}var ls=Symbol.for("nodejs.util.inspect.custom");function Pe(e,r){let t=tc(r),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=t.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=t.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=us(Reflect.ownKeys(o),t),a=us(Array.from(t.keys()),t);return[...new Set([...s,...a,...n])]},set(o,s,a){return t.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let l=t.get(s);return l?l.getPropertyDescriptor?{...Ht,...l?.getPropertyDescriptor(s)}:Ht:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)}});return i[ls]=function(o,s,a=cs.inspect){let l={...this};return delete l[ls],a(l,s)},i}function tc(e){let r=new Map;for(let t of e){let n=t.getKeys();for(let i of n)r.set(i,t)}return r}function us(e,r){return e.filter(t=>r.get(t)?.has?.(t)??!0)}function nt(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}function Er(e,r){return{batch:e,transaction:r?.kind==="batch"?{isolationLevel:r.options.isolationLevel}:void 0}}var br=class{constructor(r=0,t){this.context=t;this.lines=[];this.currentLine="";this.currentIndent=0;this.currentIndent=r}write(r){return typeof r=="string"?this.currentLine+=r:r.write(this),this}writeJoined(r,t){let n=t.length-1;for(let i=0;i<t.length;i++)this.write(t[i]),i!==n&&this.write(r);return this}writeLine(r){return this.write(r).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let r=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,r?.(),this}withIndent(r){return this.indent(),r(this),this.unindent(),this}afterNextNewline(r){return this.afterNextNewLineCallback=r,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(r){return this.marginSymbol=r,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let r=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+r.slice(1):r}};function ps(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function wr(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function Kt(e){return e.toString()!=="Invalid Date"}var xr=9e15,Je=1e9,bi="0123456789abcdef",Yt="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",Zt="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",wi={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-xr,maxE:xr,crypto:!1},gs,Oe,x=!0,en="[DecimalError] ",Ge=en+"Invalid argument: ",hs=en+"Precision limit exceeded",ys=en+"crypto unavailable",Es="[object Decimal]",re=Math.floor,Q=Math.pow,nc=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,ic=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,oc=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,bs=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,he=1e7,b=7,sc=9007199254740991,ac=Yt.length-1,xi=Zt.length-1,d={toStringTag:Es};d.absoluteValue=d.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),y(e)};d.ceil=function(){return y(new this.constructor(this),this.e+1,2)};d.clampedTo=d.clamp=function(e,r){var t,n=this,i=n.constructor;if(e=new i(e),r=new i(r),!e.s||!r.s)return new i(NaN);if(e.gt(r))throw Error(Ge+r);return t=n.cmp(e),t<0?e:n.cmp(r)>0?r:new i(n)};d.comparedTo=d.cmp=function(e){var r,t,n,i,o=this,s=o.d,a=(e=new o.constructor(e)).d,l=o.s,u=e.s;if(!s||!a)return!l||!u?NaN:l!==u?l:s===a?0:!s^l<0?1:-1;if(!s[0]||!a[0])return s[0]?l:a[0]?-u:0;if(l!==u)return l;if(o.e!==e.e)return o.e>e.e^l<0?1:-1;for(n=s.length,i=a.length,r=0,t=n<i?n:i;r<t;++r)if(s[r]!==a[r])return s[r]>a[r]^l<0?1:-1;return n===i?0:n>i^l<0?1:-1};d.cosine=d.cos=function(){var e,r,t=this,n=t.constructor;return t.d?t.d[0]?(e=n.precision,r=n.rounding,n.precision=e+Math.max(t.e,t.sd())+b,n.rounding=1,t=lc(n,Ts(n,t)),n.precision=e,n.rounding=r,y(Oe==2||Oe==3?t.neg():t,e,r,!0)):new n(1):new n(NaN)};d.cubeRoot=d.cbrt=function(){var e,r,t,n,i,o,s,a,l,u,c=this,p=c.constructor;if(!c.isFinite()||c.isZero())return new p(c);for(x=!1,o=c.s*Q(c.s*c,1/3),!o||Math.abs(o)==1/0?(t=z(c.d),e=c.e,(o=(e-t.length+1)%3)&&(t+=o==1||o==-2?"0":"00"),o=Q(t,1/3),e=re((e+1)/3)-(e%3==(e<0?-1:2)),o==1/0?t="5e"+e:(t=o.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new p(t),n.s=c.s):n=new p(o.toString()),s=(e=p.precision)+3;;)if(a=n,l=a.times(a).times(a),u=l.plus(c),n=O(u.plus(c).times(a),u.plus(l),s+2,1),z(a.d).slice(0,s)===(t=z(n.d)).slice(0,s))if(t=t.slice(s-3,s+1),t=="9999"||!i&&t=="4999"){if(!i&&(y(a,e+1,0),a.times(a).times(a).eq(c))){n=a;break}s+=4,i=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(y(n,e+1,1),r=!n.times(n).times(n).eq(c));break}return x=!0,y(n,e,p.rounding,r)};d.decimalPlaces=d.dp=function(){var e,r=this.d,t=NaN;if(r){if(e=r.length-1,t=(e-re(this.e/b))*b,e=r[e],e)for(;e%10==0;e/=10)t--;t<0&&(t=0)}return t};d.dividedBy=d.div=function(e){return O(this,new this.constructor(e))};d.dividedToIntegerBy=d.divToInt=function(e){var r=this,t=r.constructor;return y(O(r,new t(e),0,1,1),t.precision,t.rounding)};d.equals=d.eq=function(e){return this.cmp(e)===0};d.floor=function(){return y(new this.constructor(this),this.e+1,3)};d.greaterThan=d.gt=function(e){return this.cmp(e)>0};d.greaterThanOrEqualTo=d.gte=function(e){var r=this.cmp(e);return r==1||r===0};d.hyperbolicCosine=d.cosh=function(){var e,r,t,n,i,o=this,s=o.constructor,a=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return a;t=s.precision,n=s.rounding,s.precision=t+Math.max(o.e,o.sd())+4,s.rounding=1,i=o.d.length,i<32?(e=Math.ceil(i/3),r=(1/tn(4,e)).toString()):(e=16,r="2.3283064365386962890625e-10"),o=Pr(s,1,o.times(r),new s(1),!0);for(var l,u=e,c=new s(8);u--;)l=o.times(o),o=a.minus(l.times(c.minus(l.times(c))));return y(o,s.precision=t,s.rounding=n,!0)};d.hyperbolicSine=d.sinh=function(){var e,r,t,n,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(r=o.precision,t=o.rounding,o.precision=r+Math.max(i.e,i.sd())+4,o.rounding=1,n=i.d.length,n<3)i=Pr(o,2,i,i,!0);else{e=1.4*Math.sqrt(n),e=e>16?16:e|0,i=i.times(1/tn(5,e)),i=Pr(o,2,i,i,!0);for(var s,a=new o(5),l=new o(16),u=new o(20);e--;)s=i.times(i),i=i.times(a.plus(s.times(l.times(s).plus(u))))}return o.precision=r,o.rounding=t,y(i,r,t,!0)};d.hyperbolicTangent=d.tanh=function(){var e,r,t=this,n=t.constructor;return t.isFinite()?t.isZero()?new n(t):(e=n.precision,r=n.rounding,n.precision=e+7,n.rounding=1,O(t.sinh(),t.cosh(),n.precision=e,n.rounding=r)):new n(t.s)};d.inverseCosine=d.acos=function(){var e,r=this,t=r.constructor,n=r.abs().cmp(1),i=t.precision,o=t.rounding;return n!==-1?n===0?r.isNeg()?ge(t,i,o):new t(0):new t(NaN):r.isZero()?ge(t,i+4,o).times(.5):(t.precision=i+6,t.rounding=1,r=r.asin(),e=ge(t,i+4,o).times(.5),t.precision=i,t.rounding=o,e.minus(r))};d.inverseHyperbolicCosine=d.acosh=function(){var e,r,t=this,n=t.constructor;return t.lte(1)?new n(t.eq(1)?0:NaN):t.isFinite()?(e=n.precision,r=n.rounding,n.precision=e+Math.max(Math.abs(t.e),t.sd())+4,n.rounding=1,x=!1,t=t.times(t).minus(1).sqrt().plus(t),x=!0,n.precision=e,n.rounding=r,t.ln()):new n(t)};d.inverseHyperbolicSine=d.asinh=function(){var e,r,t=this,n=t.constructor;return!t.isFinite()||t.isZero()?new n(t):(e=n.precision,r=n.rounding,n.precision=e+2*Math.max(Math.abs(t.e),t.sd())+6,n.rounding=1,x=!1,t=t.times(t).plus(1).sqrt().plus(t),x=!0,n.precision=e,n.rounding=r,t.ln())};d.inverseHyperbolicTangent=d.atanh=function(){var e,r,t,n,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,r=o.rounding,n=i.sd(),Math.max(n,e)<2*-i.e-1?y(new o(i),e,r,!0):(o.precision=t=n-i.e,i=O(i.plus(1),new o(1).minus(i),t+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=r,i.times(.5))):new o(NaN)};d.inverseSine=d.asin=function(){var e,r,t,n,i=this,o=i.constructor;return i.isZero()?new o(i):(r=i.abs().cmp(1),t=o.precision,n=o.rounding,r!==-1?r===0?(e=ge(o,t+4,n).times(.5),e.s=i.s,e):new o(NaN):(o.precision=t+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=t,o.rounding=n,i.times(2)))};d.inverseTangent=d.atan=function(){var e,r,t,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,m=c.rounding;if(u.isFinite()){if(u.isZero())return new c(u);if(u.abs().eq(1)&&p+4<=xi)return s=ge(c,p+4,m).times(.25),s.s=u.s,s}else{if(!u.s)return new c(NaN);if(p+4<=xi)return s=ge(c,p+4,m).times(.5),s.s=u.s,s}for(c.precision=a=p+10,c.rounding=1,t=Math.min(28,a/b+2|0),e=t;e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(x=!1,r=Math.ceil(a/b),n=1,l=u.times(u),s=new c(u),i=u;e!==-1;)if(i=i.times(l),o=s.minus(i.div(n+=2)),i=i.times(l),s=o.plus(i.div(n+=2)),s.d[r]!==void 0)for(e=r;s.d[e]===o.d[e]&&e--;);return t&&(s=s.times(2<<t-1)),x=!0,y(s,c.precision=p,c.rounding=m,!0)};d.isFinite=function(){return!!this.d};d.isInteger=d.isInt=function(){return!!this.d&&re(this.e/b)>this.d.length-2};d.isNaN=function(){return!this.s};d.isNegative=d.isNeg=function(){return this.s<0};d.isPositive=d.isPos=function(){return this.s>0};d.isZero=function(){return!!this.d&&this.d[0]===0};d.lessThan=d.lt=function(e){return this.cmp(e)<0};d.lessThanOrEqualTo=d.lte=function(e){return this.cmp(e)<1};d.logarithm=d.log=function(e){var r,t,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,m=c.rounding,f=5;if(e==null)e=new c(10),r=!0;else{if(e=new c(e),t=e.d,e.s<0||!t||!t[0]||e.eq(1))return new c(NaN);r=e.eq(10)}if(t=u.d,u.s<0||!t||!t[0]||u.eq(1))return new c(t&&!t[0]?-1/0:u.s!=1?NaN:t?0:1/0);if(r)if(t.length>1)o=!0;else{for(i=t[0];i%10===0;)i/=10;o=i!==1}if(x=!1,a=p+f,s=Qe(u,a),n=r?Xt(c,a+10):Qe(e,a),l=O(s,n,a,1),it(l.d,i=p,m))do if(a+=10,s=Qe(u,a),n=r?Xt(c,a+10):Qe(e,a),l=O(s,n,a,1),!o){+z(l.d).slice(i+1,i+15)+1==1e14&&(l=y(l,p+1,0));break}while(it(l.d,i+=10,m));return x=!0,y(l,p,m)};d.minus=d.sub=function(e){var r,t,n,i,o,s,a,l,u,c,p,m,f=this,g=f.constructor;if(e=new g(e),!f.d||!e.d)return!f.s||!e.s?e=new g(NaN):f.d?e.s=-e.s:e=new g(e.d||f.s!==e.s?f:NaN),e;if(f.s!=e.s)return e.s=-e.s,f.plus(e);if(u=f.d,m=e.d,a=g.precision,l=g.rounding,!u[0]||!m[0]){if(m[0])e.s=-e.s;else if(u[0])e=new g(f);else return new g(l===3?-0:0);return x?y(e,a,l):e}if(t=re(e.e/b),c=re(f.e/b),u=u.slice(),o=c-t,o){for(p=o<0,p?(r=u,o=-o,s=m.length):(r=m,t=c,s=u.length),n=Math.max(Math.ceil(a/b),s)+2,o>n&&(o=n,r.length=1),r.reverse(),n=o;n--;)r.push(0);r.reverse()}else{for(n=u.length,s=m.length,p=n<s,p&&(s=n),n=0;n<s;n++)if(u[n]!=m[n]){p=u[n]<m[n];break}o=0}for(p&&(r=u,u=m,m=r,e.s=-e.s),s=u.length,n=m.length-s;n>0;--n)u[s++]=0;for(n=m.length;n>o;){if(u[--n]<m[n]){for(i=n;i&&u[--i]===0;)u[i]=he-1;--u[i],u[n]+=he}u[n]-=m[n]}for(;u[--s]===0;)u.pop();for(;u[0]===0;u.shift())--t;return u[0]?(e.d=u,e.e=rn(u,t),x?y(e,a,l):e):new g(l===3?-0:0)};d.modulo=d.mod=function(e){var r,t=this,n=t.constructor;return e=new n(e),!t.d||!e.s||e.d&&!e.d[0]?new n(NaN):!e.d||t.d&&!t.d[0]?y(new n(t),n.precision,n.rounding):(x=!1,n.modulo==9?(r=O(t,e.abs(),0,3,1),r.s*=e.s):r=O(t,e,0,n.modulo,1),r=r.times(e),x=!0,t.minus(r))};d.naturalExponential=d.exp=function(){return Pi(this)};d.naturalLogarithm=d.ln=function(){return Qe(this)};d.negated=d.neg=function(){var e=new this.constructor(this);return e.s=-e.s,y(e)};d.plus=d.add=function(e){var r,t,n,i,o,s,a,l,u,c,p=this,m=p.constructor;if(e=new m(e),!p.d||!e.d)return!p.s||!e.s?e=new m(NaN):p.d||(e=new m(e.d||p.s===e.s?p:NaN)),e;if(p.s!=e.s)return e.s=-e.s,p.minus(e);if(u=p.d,c=e.d,a=m.precision,l=m.rounding,!u[0]||!c[0])return c[0]||(e=new m(p)),x?y(e,a,l):e;if(o=re(p.e/b),n=re(e.e/b),u=u.slice(),i=o-n,i){for(i<0?(t=u,i=-i,s=c.length):(t=c,n=o,s=u.length),o=Math.ceil(a/b),s=o>s?o+1:s+1,i>s&&(i=s,t.length=1),t.reverse();i--;)t.push(0);t.reverse()}for(s=u.length,i=c.length,s-i<0&&(i=s,t=c,c=u,u=t),r=0;i;)r=(u[--i]=u[i]+c[i]+r)/he|0,u[i]%=he;for(r&&(u.unshift(r),++n),s=u.length;u[--s]==0;)u.pop();return e.d=u,e.e=rn(u,n),x?y(e,a,l):e};d.precision=d.sd=function(e){var r,t=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Ge+e);return t.d?(r=ws(t.d),e&&t.e+1>r&&(r=t.e+1)):r=NaN,r};d.round=function(){var e=this,r=e.constructor;return y(new r(e),e.e+1,r.rounding)};d.sine=d.sin=function(){var e,r,t=this,n=t.constructor;return t.isFinite()?t.isZero()?new n(t):(e=n.precision,r=n.rounding,n.precision=e+Math.max(t.e,t.sd())+b,n.rounding=1,t=cc(n,Ts(n,t)),n.precision=e,n.rounding=r,y(Oe>2?t.neg():t,e,r,!0)):new n(NaN)};d.squareRoot=d.sqrt=function(){var e,r,t,n,i,o,s=this,a=s.d,l=s.e,u=s.s,c=s.constructor;if(u!==1||!a||!a[0])return new c(!u||u<0&&(!a||a[0])?NaN:a?s:1/0);for(x=!1,u=Math.sqrt(+s),u==0||u==1/0?(r=z(a),(r.length+l)%2==0&&(r+="0"),u=Math.sqrt(r),l=re((l+1)/2)-(l<0||l%2),u==1/0?r="5e"+l:(r=u.toExponential(),r=r.slice(0,r.indexOf("e")+1)+l),n=new c(r)):n=new c(u.toString()),t=(l=c.precision)+3;;)if(o=n,n=o.plus(O(s,o,t+2,1)).times(.5),z(o.d).slice(0,t)===(r=z(n.d)).slice(0,t))if(r=r.slice(t-3,t+1),r=="9999"||!i&&r=="4999"){if(!i&&(y(o,l+1,0),o.times(o).eq(s))){n=o;break}t+=4,i=1}else{(!+r||!+r.slice(1)&&r.charAt(0)=="5")&&(y(n,l+1,1),e=!n.times(n).eq(s));break}return x=!0,y(n,l,c.rounding,e)};d.tangent=d.tan=function(){var e,r,t=this,n=t.constructor;return t.isFinite()?t.isZero()?new n(t):(e=n.precision,r=n.rounding,n.precision=e+10,n.rounding=1,t=t.sin(),t.s=1,t=O(t,new n(1).minus(t.times(t)).sqrt(),e+10,0),n.precision=e,n.rounding=r,y(Oe==2||Oe==4?t.neg():t,e,r,!0)):new n(NaN)};d.times=d.mul=function(e){var r,t,n,i,o,s,a,l,u,c=this,p=c.constructor,m=c.d,f=(e=new p(e)).d;if(e.s*=c.s,!m||!m[0]||!f||!f[0])return new p(!e.s||m&&!m[0]&&!f||f&&!f[0]&&!m?NaN:!m||!f?e.s/0:e.s*0);for(t=re(c.e/b)+re(e.e/b),l=m.length,u=f.length,l<u&&(o=m,m=f,f=o,s=l,l=u,u=s),o=[],s=l+u,n=s;n--;)o.push(0);for(n=u;--n>=0;){for(r=0,i=l+n;i>n;)a=o[i]+f[n]*m[i-n-1]+r,o[i--]=a%he|0,r=a/he|0;o[i]=(o[i]+r)%he|0}for(;!o[--s];)o.pop();return r?++t:o.shift(),e.d=o,e.e=rn(o,t),x?y(e,p.precision,p.rounding):e};d.toBinary=function(e,r){return Ti(this,2,e,r)};d.toDecimalPlaces=d.toDP=function(e,r){var t=this,n=t.constructor;return t=new n(t),e===void 0?t:(se(e,0,Je),r===void 0?r=n.rounding:se(r,0,8),y(t,e+t.e+1,r))};d.toExponential=function(e,r){var t,n=this,i=n.constructor;return e===void 0?t=ve(n,!0):(se(e,0,Je),r===void 0?r=i.rounding:se(r,0,8),n=y(new i(n),e+1,r),t=ve(n,!0,e+1)),n.isNeg()&&!n.isZero()?"-"+t:t};d.toFixed=function(e,r){var t,n,i=this,o=i.constructor;return e===void 0?t=ve(i):(se(e,0,Je),r===void 0?r=o.rounding:se(r,0,8),n=y(new o(i),e+i.e+1,r),t=ve(n,!1,e+n.e+1)),i.isNeg()&&!i.isZero()?"-"+t:t};d.toFraction=function(e){var r,t,n,i,o,s,a,l,u,c,p,m,f=this,g=f.d,h=f.constructor;if(!g)return new h(f);if(u=t=new h(1),n=l=new h(0),r=new h(n),o=r.e=ws(g)-f.e-1,s=o%b,r.d[0]=Q(10,s<0?b+s:s),e==null)e=o>0?r:u;else{if(a=new h(e),!a.isInt()||a.lt(u))throw Error(Ge+a);e=a.gt(r)?o>0?r:u:a}for(x=!1,a=new h(z(g)),c=h.precision,h.precision=o=g.length*b*2;p=O(a,r,0,1,1),i=t.plus(p.times(n)),i.cmp(e)!=1;)t=n,n=i,i=u,u=l.plus(p.times(i)),l=i,i=r,r=a.minus(p.times(i)),a=i;return i=O(e.minus(t),n,0,1,1),l=l.plus(i.times(u)),t=t.plus(i.times(n)),l.s=u.s=f.s,m=O(u,n,o,1).minus(f).abs().cmp(O(l,t,o,1).minus(f).abs())<1?[u,n]:[l,t],h.precision=c,x=!0,m};d.toHexadecimal=d.toHex=function(e,r){return Ti(this,16,e,r)};d.toNearest=function(e,r){var t=this,n=t.constructor;if(t=new n(t),e==null){if(!t.d)return t;e=new n(1),r=n.rounding}else{if(e=new n(e),r===void 0?r=n.rounding:se(r,0,8),!t.d)return e.s?t:e;if(!e.d)return e.s&&(e.s=t.s),e}return e.d[0]?(x=!1,t=O(t,e,0,r,1).times(e),x=!0,y(t)):(e.s=t.s,t=e),t};d.toNumber=function(){return+this};d.toOctal=function(e,r){return Ti(this,8,e,r)};d.toPower=d.pow=function(e){var r,t,n,i,o,s,a=this,l=a.constructor,u=+(e=new l(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new l(Q(+a,u));if(a=new l(a),a.eq(1))return a;if(n=l.precision,o=l.rounding,e.eq(1))return y(a,n,o);if(r=re(e.e/b),r>=e.d.length-1&&(t=u<0?-u:u)<=sc)return i=xs(l,a,t,n),e.s<0?new l(1).div(i):y(i,n,o);if(s=a.s,s<0){if(r<e.d.length-1)return new l(NaN);if(e.d[r]&1||(s=1),a.e==0&&a.d[0]==1&&a.d.length==1)return a.s=s,a}return t=Q(+a,u),r=t==0||!isFinite(t)?re(u*(Math.log("0."+z(a.d))/Math.LN10+a.e+1)):new l(t+"").e,r>l.maxE+1||r<l.minE-1?new l(r>0?s/0:0):(x=!1,l.rounding=a.s=1,t=Math.min(12,(r+"").length),i=Pi(e.times(Qe(a,n+t)),n),i.d&&(i=y(i,n+5,1),it(i.d,n,o)&&(r=n+10,i=y(Pi(e.times(Qe(a,r+t)),r),r+5,1),+z(i.d).slice(n+1,n+15)+1==1e14&&(i=y(i,n+1,0)))),i.s=s,x=!0,l.rounding=o,y(i,n,o))};d.toPrecision=function(e,r){var t,n=this,i=n.constructor;return e===void 0?t=ve(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(se(e,1,Je),r===void 0?r=i.rounding:se(r,0,8),n=y(new i(n),e,r),t=ve(n,e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+t:t};d.toSignificantDigits=d.toSD=function(e,r){var t=this,n=t.constructor;return e===void 0?(e=n.precision,r=n.rounding):(se(e,1,Je),r===void 0?r=n.rounding:se(r,0,8)),y(new n(t),e,r)};d.toString=function(){var e=this,r=e.constructor,t=ve(e,e.e<=r.toExpNeg||e.e>=r.toExpPos);return e.isNeg()&&!e.isZero()?"-"+t:t};d.truncated=d.trunc=function(){return y(new this.constructor(this),this.e+1,1)};d.valueOf=d.toJSON=function(){var e=this,r=e.constructor,t=ve(e,e.e<=r.toExpNeg||e.e>=r.toExpPos);return e.isNeg()?"-"+t:t};function z(e){var r,t,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,r=1;r<i;r++)n=e[r]+"",t=b-n.length,t&&(o+=Ue(t)),o+=n;s=e[r],n=s+"",t=b-n.length,t&&(o+=Ue(t))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function se(e,r,t){if(e!==~~e||e<r||e>t)throw Error(Ge+e)}function it(e,r,t,n){var i,o,s,a;for(o=e[0];o>=10;o/=10)--r;return--r<0?(r+=b,i=0):(i=Math.ceil((r+1)/b),r%=b),o=Q(10,b-r),a=e[i]%o|0,n==null?r<3?(r==0?a=a/100|0:r==1&&(a=a/10|0),s=t<4&&a==99999||t>3&&a==49999||a==5e4||a==0):s=(t<4&&a+1==o||t>3&&a+1==o/2)&&(e[i+1]/o/100|0)==Q(10,r-2)-1||(a==o/2||a==0)&&(e[i+1]/o/100|0)==0:r<4?(r==0?a=a/1e3|0:r==1?a=a/100|0:r==2&&(a=a/10|0),s=(n||t<4)&&a==9999||!n&&t>3&&a==4999):s=((n||t<4)&&a+1==o||!n&&t>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==Q(10,r-3)-1,s}function zt(e,r,t){for(var n,i=[0],o,s=0,a=e.length;s<a;){for(o=i.length;o--;)i[o]*=r;for(i[0]+=bi.indexOf(e.charAt(s++)),n=0;n<i.length;n++)i[n]>t-1&&(i[n+1]===void 0&&(i[n+1]=0),i[n+1]+=i[n]/t|0,i[n]%=t)}return i.reverse()}function lc(e,r){var t,n,i;if(r.isZero())return r;n=r.d.length,n<32?(t=Math.ceil(n/3),i=(1/tn(4,t)).toString()):(t=16,i="2.3283064365386962890625e-10"),e.precision+=t,r=Pr(e,1,r.times(i),new e(1));for(var o=t;o--;){var s=r.times(r);r=s.times(s).minus(s).times(8).plus(1)}return e.precision-=t,r}var O=function(){function e(n,i,o){var s,a=0,l=n.length;for(n=n.slice();l--;)s=n[l]*i+a,n[l]=s%o|0,a=s/o|0;return a&&n.unshift(a),n}function r(n,i,o,s){var a,l;if(o!=s)l=o>s?1:-1;else for(a=l=0;a<o;a++)if(n[a]!=i[a]){l=n[a]>i[a]?1:-1;break}return l}function t(n,i,o,s){for(var a=0;o--;)n[o]-=a,a=n[o]<i[o]?1:0,n[o]=a*s+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s,a,l){var u,c,p,m,f,g,h,A,T,C,E,I,me,le,Nr,U,ne,Ae,Y,pr,Rt=n.constructor,kn=n.s==i.s?1:-1,Z=n.d,L=i.d;if(!Z||!Z[0]||!L||!L[0])return new Rt(!n.s||!i.s||(Z?L&&Z[0]==L[0]:!L)?NaN:Z&&Z[0]==0||!L?kn*0:kn/0);for(l?(f=1,c=n.e-i.e):(l=he,f=b,c=re(n.e/f)-re(i.e/f)),Y=L.length,ne=Z.length,T=new Rt(kn),C=T.d=[],p=0;L[p]==(Z[p]||0);p++);if(L[p]>(Z[p]||0)&&c--,o==null?(le=o=Rt.precision,s=Rt.rounding):a?le=o+(n.e-i.e)+1:le=o,le<0)C.push(1),g=!0;else{if(le=le/f+2|0,p=0,Y==1){for(m=0,L=L[0],le++;(p<ne||m)&&le--;p++)Nr=m*l+(Z[p]||0),C[p]=Nr/L|0,m=Nr%L|0;g=m||p<ne}else{for(m=l/(L[0]+1)|0,m>1&&(L=e(L,m,l),Z=e(Z,m,l),Y=L.length,ne=Z.length),U=Y,E=Z.slice(0,Y),I=E.length;I<Y;)E[I++]=0;pr=L.slice(),pr.unshift(0),Ae=L[0],L[1]>=l/2&&++Ae;do m=0,u=r(L,E,Y,I),u<0?(me=E[0],Y!=I&&(me=me*l+(E[1]||0)),m=me/Ae|0,m>1?(m>=l&&(m=l-1),h=e(L,m,l),A=h.length,I=E.length,u=r(h,E,A,I),u==1&&(m--,t(h,Y<A?pr:L,A,l))):(m==0&&(u=m=1),h=L.slice()),A=h.length,A<I&&h.unshift(0),t(E,h,I,l),u==-1&&(I=E.length,u=r(L,E,Y,I),u<1&&(m++,t(E,Y<I?pr:L,I,l))),I=E.length):u===0&&(m++,E=[0]),C[p++]=m,u&&E[0]?E[I++]=Z[U]||0:(E=[Z[U]],I=1);while((U++<ne||E[0]!==void 0)&&le--);g=E[0]!==void 0}C[0]||C.shift()}if(f==1)T.e=c,gs=g;else{for(p=1,m=C[0];m>=10;m/=10)p++;T.e=p+c*f-1,y(T,a?o+T.e+1:o,s,g)}return T}}();function y(e,r,t,n){var i,o,s,a,l,u,c,p,m,f=e.constructor;e:if(r!=null){if(p=e.d,!p)return e;for(i=1,a=p[0];a>=10;a/=10)i++;if(o=r-i,o<0)o+=b,s=r,c=p[m=0],l=c/Q(10,i-s-1)%10|0;else if(m=Math.ceil((o+1)/b),a=p.length,m>=a)if(n){for(;a++<=m;)p.push(0);c=l=0,i=1,o%=b,s=o-b+1}else break e;else{for(c=a=p[m],i=1;a>=10;a/=10)i++;o%=b,s=o-b+i,l=s<0?0:c/Q(10,i-s-1)%10|0}if(n=n||r<0||p[m+1]!==void 0||(s<0?c:c%Q(10,i-s-1)),u=t<4?(l||n)&&(t==0||t==(e.s<0?3:2)):l>5||l==5&&(t==4||n||t==6&&(o>0?s>0?c/Q(10,i-s):0:p[m-1])%10&1||t==(e.s<0?8:7)),r<1||!p[0])return p.length=0,u?(r-=e.e+1,p[0]=Q(10,(b-r%b)%b),e.e=-r||0):p[0]=e.e=0,e;if(o==0?(p.length=m,a=1,m--):(p.length=m+1,a=Q(10,b-o),p[m]=s>0?(c/Q(10,i-s)%Q(10,s)|0)*a:0),u)for(;;)if(m==0){for(o=1,s=p[0];s>=10;s/=10)o++;for(s=p[0]+=a,a=1;s>=10;s/=10)a++;o!=a&&(e.e++,p[0]==he&&(p[0]=1));break}else{if(p[m]+=a,p[m]!=he)break;p[m--]=0,a=1}for(o=p.length;p[--o]===0;)p.pop()}return x&&(e.e>f.maxE?(e.d=null,e.e=NaN):e.e<f.minE&&(e.e=0,e.d=[0])),e}function ve(e,r,t){if(!e.isFinite())return vs(e);var n,i=e.e,o=z(e.d),s=o.length;return r?(t&&(n=t-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ue(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+Ue(-i-1)+o,t&&(n=t-s)>0&&(o+=Ue(n))):i>=s?(o+=Ue(i+1-s),t&&(n=t-i-1)>0&&(o=o+"."+Ue(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),t&&(n=t-s)>0&&(i+1===s&&(o+="."),o+=Ue(n))),o}function rn(e,r){var t=e[0];for(r*=b;t>=10;t/=10)r++;return r}function Xt(e,r,t){if(r>ac)throw x=!0,t&&(e.precision=t),Error(hs);return y(new e(Yt),r,1,!0)}function ge(e,r,t){if(r>xi)throw Error(hs);return y(new e(Zt),r,t,!0)}function ws(e){var r=e.length-1,t=r*b+1;if(r=e[r],r){for(;r%10==0;r/=10)t--;for(r=e[0];r>=10;r/=10)t++}return t}function Ue(e){for(var r="";e--;)r+="0";return r}function xs(e,r,t,n){var i,o=new e(1),s=Math.ceil(n/b+4);for(x=!1;;){if(t%2&&(o=o.times(r),ds(o.d,s)&&(i=!0)),t=re(t/2),t===0){t=o.d.length-1,i&&o.d[t]===0&&++o.d[t];break}r=r.times(r),ds(r.d,s)}return x=!0,o}function ms(e){return e.d[e.d.length-1]&1}function Ps(e,r,t){for(var n,i=new e(r[0]),o=0;++o<r.length;)if(n=new e(r[o]),n.s)i[t](n)&&(i=n);else{i=n;break}return i}function Pi(e,r){var t,n,i,o,s,a,l,u=0,c=0,p=0,m=e.constructor,f=m.rounding,g=m.precision;if(!e.d||!e.d[0]||e.e>17)return new m(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(r==null?(x=!1,l=g):l=r,a=new m(.03125);e.e>-2;)e=e.times(a),p+=5;for(n=Math.log(Q(2,p))/Math.LN10*2+5|0,l+=n,t=o=s=new m(1),m.precision=l;;){if(o=y(o.times(e),l,1),t=t.times(++c),a=s.plus(O(o,t,l,1)),z(a.d).slice(0,l)===z(s.d).slice(0,l)){for(i=p;i--;)s=y(s.times(s),l,1);if(r==null)if(u<3&&it(s.d,l-n,f,u))m.precision=l+=10,t=o=a=new m(1),c=0,u++;else return y(s,m.precision=g,f,x=!0);else return m.precision=g,s}s=a}}function Qe(e,r){var t,n,i,o,s,a,l,u,c,p,m,f=1,g=10,h=e,A=h.d,T=h.constructor,C=T.rounding,E=T.precision;if(h.s<0||!A||!A[0]||!h.e&&A[0]==1&&A.length==1)return new T(A&&!A[0]?-1/0:h.s!=1?NaN:A?0:h);if(r==null?(x=!1,c=E):c=r,T.precision=c+=g,t=z(A),n=t.charAt(0),Math.abs(o=h.e)<15e14){for(;n<7&&n!=1||n==1&&t.charAt(1)>3;)h=h.times(e),t=z(h.d),n=t.charAt(0),f++;o=h.e,n>1?(h=new T("0."+t),o++):h=new T(n+"."+t.slice(1))}else return u=Xt(T,c+2,E).times(o+""),h=Qe(new T(n+"."+t.slice(1)),c-g).plus(u),T.precision=E,r==null?y(h,E,C,x=!0):h;for(p=h,l=s=h=O(h.minus(1),h.plus(1),c,1),m=y(h.times(h),c,1),i=3;;){if(s=y(s.times(m),c,1),u=l.plus(O(s,new T(i),c,1)),z(u.d).slice(0,c)===z(l.d).slice(0,c))if(l=l.times(2),o!==0&&(l=l.plus(Xt(T,c+2,E).times(o+""))),l=O(l,new T(f),c,1),r==null)if(it(l.d,c-g,C,a))T.precision=c+=g,u=s=h=O(p.minus(1),p.plus(1),c,1),m=y(h.times(h),c,1),i=a=1;else return y(l,T.precision=E,C,x=!0);else return T.precision=E,l;l=u,i+=2}}function vs(e){return String(e.s*e.s/0)}function vi(e,r){var t,n,i;for((t=r.indexOf("."))>-1&&(r=r.replace(".","")),(n=r.search(/e/i))>0?(t<0&&(t=n),t+=+r.slice(n+1),r=r.substring(0,n)):t<0&&(t=r.length),n=0;r.charCodeAt(n)===48;n++);for(i=r.length;r.charCodeAt(i-1)===48;--i);if(r=r.slice(n,i),r){if(i-=n,e.e=t=t-n-1,e.d=[],n=(t+1)%b,t<0&&(n+=b),n<i){for(n&&e.d.push(+r.slice(0,n)),i-=b;n<i;)e.d.push(+r.slice(n,n+=b));r=r.slice(n),n=b-r.length}else n-=i;for(;n--;)r+="0";e.d.push(+r),x&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function uc(e,r){var t,n,i,o,s,a,l,u,c;if(r.indexOf("_")>-1){if(r=r.replace(/(\d)_(?=\d)/g,"$1"),bs.test(r))return vi(e,r)}else if(r==="Infinity"||r==="NaN")return+r||(e.s=NaN),e.e=NaN,e.d=null,e;if(ic.test(r))t=16,r=r.toLowerCase();else if(nc.test(r))t=2;else if(oc.test(r))t=8;else throw Error(Ge+r);for(o=r.search(/p/i),o>0?(l=+r.slice(o+1),r=r.substring(2,o)):r=r.slice(2),o=r.indexOf("."),s=o>=0,n=e.constructor,s&&(r=r.replace(".",""),a=r.length,o=a-o,i=xs(n,new n(t),o,o*2)),u=zt(r,t,he),c=u.length-1,o=c;u[o]===0;--o)u.pop();return o<0?new n(e.s*0):(e.e=rn(u,c),e.d=u,x=!1,s&&(e=O(e,i,a*4)),l&&(e=e.times(Math.abs(l)<54?Q(2,l):or.pow(2,l))),x=!0,e)}function cc(e,r){var t,n=r.d.length;if(n<3)return r.isZero()?r:Pr(e,2,r,r);t=1.4*Math.sqrt(n),t=t>16?16:t|0,r=r.times(1/tn(5,t)),r=Pr(e,2,r,r);for(var i,o=new e(5),s=new e(16),a=new e(20);t--;)i=r.times(r),r=r.times(o.plus(i.times(s.times(i).minus(a))));return r}function Pr(e,r,t,n,i){var o,s,a,l,u=1,c=e.precision,p=Math.ceil(c/b);for(x=!1,l=t.times(t),a=new e(n);;){if(s=O(a.times(l),new e(r++*r++),c,1),a=i?n.plus(s):n.minus(s),n=O(s.times(l),new e(r++*r++),c,1),s=a.plus(n),s.d[p]!==void 0){for(o=p;s.d[o]===a.d[o]&&o--;);if(o==-1)break}o=a,a=n,n=s,s=o,u++}return x=!0,s.d.length=p+1,s}function tn(e,r){for(var t=e;--r;)t*=e;return t}function Ts(e,r){var t,n=r.s<0,i=ge(e,e.precision,1),o=i.times(.5);if(r=r.abs(),r.lte(o))return Oe=n?4:1,r;if(t=r.divToInt(i),t.isZero())Oe=n?3:2;else{if(r=r.minus(t.times(i)),r.lte(o))return Oe=ms(t)?n?2:3:n?4:1,r;Oe=ms(t)?n?1:4:n?3:2}return r.minus(i).abs()}function Ti(e,r,t,n){var i,o,s,a,l,u,c,p,m,f=e.constructor,g=t!==void 0;if(g?(se(t,1,Je),n===void 0?n=f.rounding:se(n,0,8)):(t=f.precision,n=f.rounding),!e.isFinite())c=vs(e);else{for(c=ve(e),s=c.indexOf("."),g?(i=2,r==16?t=t*4-3:r==8&&(t=t*3-2)):i=r,s>=0&&(c=c.replace(".",""),m=new f(1),m.e=c.length-s,m.d=zt(ve(m),10,i),m.e=m.d.length),p=zt(c,10,i),o=l=p.length;p[--l]==0;)p.pop();if(!p[0])c=g?"0p+0":"0";else{if(s<0?o--:(e=new f(e),e.d=p,e.e=o,e=O(e,m,t,n,0,i),p=e.d,o=e.e,u=gs),s=p[t],a=i/2,u=u||p[t+1]!==void 0,u=n<4?(s!==void 0||u)&&(n===0||n===(e.s<0?3:2)):s>a||s===a&&(n===4||u||n===6&&p[t-1]&1||n===(e.s<0?8:7)),p.length=t,u)for(;++p[--t]>i-1;)p[t]=0,t||(++o,p.unshift(1));for(l=p.length;!p[l-1];--l);for(s=0,c="";s<l;s++)c+=bi.charAt(p[s]);if(g){if(l>1)if(r==16||r==8){for(s=r==16?4:3,--l;l%s;l++)c+="0";for(p=zt(c,i,r),l=p.length;!p[l-1];--l);for(s=1,c="1.";s<l;s++)c+=bi.charAt(p[s])}else c=c.charAt(0)+"."+c.slice(1);c=c+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)c="0"+c;c="0."+c}else if(++o>l)for(o-=l;o--;)c+="0";else o<l&&(c=c.slice(0,o)+"."+c.slice(o))}c=(r==16?"0x":r==2?"0b":r==8?"0o":"")+c}return e.s<0?"-"+c:c}function ds(e,r){if(e.length>r)return e.length=r,!0}function pc(e){return new this(e).abs()}function mc(e){return new this(e).acos()}function dc(e){return new this(e).acosh()}function fc(e,r){return new this(e).plus(r)}function gc(e){return new this(e).asin()}function hc(e){return new this(e).asinh()}function yc(e){return new this(e).atan()}function Ec(e){return new this(e).atanh()}function bc(e,r){e=new this(e),r=new this(r);var t,n=this.precision,i=this.rounding,o=n+4;return!e.s||!r.s?t=new this(NaN):!e.d&&!r.d?(t=ge(this,o,1).times(r.s>0?.25:.75),t.s=e.s):!r.d||e.isZero()?(t=r.s<0?ge(this,n,i):new this(0),t.s=e.s):!e.d||r.isZero()?(t=ge(this,o,1).times(.5),t.s=e.s):r.s<0?(this.precision=o,this.rounding=1,t=this.atan(O(e,r,o,1)),r=ge(this,o,1),this.precision=n,this.rounding=i,t=e.s<0?t.minus(r):t.plus(r)):t=this.atan(O(e,r,o,1)),t}function wc(e){return new this(e).cbrt()}function xc(e){return y(e=new this(e),e.e+1,2)}function Pc(e,r,t){return new this(e).clamp(r,t)}function vc(e){if(!e||typeof e!="object")throw Error(en+"Object expected");var r,t,n,i=e.defaults===!0,o=["precision",1,Je,"rounding",0,8,"toExpNeg",-xr,0,"toExpPos",0,xr,"maxE",0,xr,"minE",-xr,0,"modulo",0,9];for(r=0;r<o.length;r+=3)if(t=o[r],i&&(this[t]=wi[t]),(n=e[t])!==void 0)if(re(n)===n&&n>=o[r+1]&&n<=o[r+2])this[t]=n;else throw Error(Ge+t+": "+n);if(t="crypto",i&&(this[t]=wi[t]),(n=e[t])!==void 0)if(n===!0||n===!1||n===0||n===1)if(n)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[t]=!0;else throw Error(ys);else this[t]=!1;else throw Error(Ge+t+": "+n);return this}function Tc(e){return new this(e).cos()}function Cc(e){return new this(e).cosh()}function Cs(e){var r,t,n;function i(o){var s,a,l,u=this;if(!(u instanceof i))return new i(o);if(u.constructor=i,fs(o)){u.s=o.s,x?!o.d||o.e>i.maxE?(u.e=NaN,u.d=null):o.e<i.minE?(u.e=0,u.d=[0]):(u.e=o.e,u.d=o.d.slice()):(u.e=o.e,u.d=o.d?o.d.slice():o.d);return}if(l=typeof o,l==="number"){if(o===0){u.s=1/o<0?-1:1,u.e=0,u.d=[0];return}if(o<0?(o=-o,u.s=-1):u.s=1,o===~~o&&o<1e7){for(s=0,a=o;a>=10;a/=10)s++;x?s>i.maxE?(u.e=NaN,u.d=null):s<i.minE?(u.e=0,u.d=[0]):(u.e=s,u.d=[o]):(u.e=s,u.d=[o]);return}else if(o*0!==0){o||(u.s=NaN),u.e=NaN,u.d=null;return}return vi(u,o.toString())}else if(l!=="string")throw Error(Ge+o);return(a=o.charCodeAt(0))===45?(o=o.slice(1),u.s=-1):(a===43&&(o=o.slice(1)),u.s=1),bs.test(o)?vi(u,o):uc(u,o)}if(i.prototype=d,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.EUCLID=9,i.config=i.set=vc,i.clone=Cs,i.isDecimal=fs,i.abs=pc,i.acos=mc,i.acosh=dc,i.add=fc,i.asin=gc,i.asinh=hc,i.atan=yc,i.atanh=Ec,i.atan2=bc,i.cbrt=wc,i.ceil=xc,i.clamp=Pc,i.cos=Tc,i.cosh=Cc,i.div=Sc,i.exp=Rc,i.floor=Ac,i.hypot=Ic,i.ln=_c,i.log=kc,i.log10=Lc,i.log2=Dc,i.max=Nc,i.min=Oc,i.mod=Fc,i.mul=Mc,i.pow=$c,i.random=qc,i.round=Bc,i.sign=Vc,i.sin=jc,i.sinh=Uc,i.sqrt=Qc,i.sub=Gc,i.sum=Jc,i.tan=Hc,i.tanh=Wc,i.trunc=Kc,e===void 0&&(e={}),e&&e.defaults!==!0)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],r=0;r<n.length;)e.hasOwnProperty(t=n[r++])||(e[t]=this[t]);return i.config(e),i}function Sc(e,r){return new this(e).div(r)}function Rc(e){return new this(e).exp()}function Ac(e){return y(e=new this(e),e.e+1,3)}function Ic(){var e,r,t=new this(0);for(x=!1,e=0;e<arguments.length;)if(r=new this(arguments[e++]),r.d)t.d&&(t=t.plus(r.times(r)));else{if(r.s)return x=!0,new this(1/0);t=r}return x=!0,t.sqrt()}function fs(e){return e instanceof or||e&&e.toStringTag===Es||!1}function _c(e){return new this(e).ln()}function kc(e,r){return new this(e).log(r)}function Dc(e){return new this(e).log(2)}function Lc(e){return new this(e).log(10)}function Nc(){return Ps(this,arguments,"lt")}function Oc(){return Ps(this,arguments,"gt")}function Fc(e,r){return new this(e).mod(r)}function Mc(e,r){return new this(e).mul(r)}function $c(e,r){return new this(e).pow(r)}function qc(e){var r,t,n,i,o=0,s=new this(1),a=[];if(e===void 0?e=this.precision:se(e,1,Je),n=Math.ceil(e/b),this.crypto)if(crypto.getRandomValues)for(r=crypto.getRandomValues(new Uint32Array(n));o<n;)i=r[o],i>=429e7?r[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else if(crypto.randomBytes){for(r=crypto.randomBytes(n*=4);o<n;)i=r[o]+(r[o+1]<<8)+(r[o+2]<<16)+((r[o+3]&127)<<24),i>=214e7?crypto.randomBytes(4).copy(r,o):(a.push(i%1e7),o+=4);o=n/4}else throw Error(ys);else for(;o<n;)a[o++]=Math.random()*1e7|0;for(n=a[--o],e%=b,n&&e&&(i=Q(10,b-e),a[o]=(n/i|0)*i);a[o]===0;o--)a.pop();if(o<0)t=0,a=[0];else{for(t=-1;a[0]===0;t-=b)a.shift();for(n=1,i=a[0];i>=10;i/=10)n++;n<b&&(t-=b-n)}return s.e=t,s.d=a,s}function Bc(e){return y(e=new this(e),e.e+1,this.rounding)}function Vc(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function jc(e){return new this(e).sin()}function Uc(e){return new this(e).sinh()}function Qc(e){return new this(e).sqrt()}function Gc(e,r){return new this(e).sub(r)}function Jc(){var e=0,r=arguments,t=new this(r[e]);for(x=!1;t.s&&++e<r.length;)t=t.plus(r[e]);return x=!0,y(t,this.precision,this.rounding)}function Hc(e){return new this(e).tan()}function Wc(e){return new this(e).tanh()}function Kc(e){return y(e=new this(e),e.e+1,1)}d[Symbol.for("nodejs.util.inspect.custom")]=d.toString;d[Symbol.toStringTag]="Decimal";var or=d.constructor=Cs(wi);Yt=new or(Yt);Zt=new or(Zt);var Te=or;function vr(e){return or.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}var ot=class{constructor(r,t,n,i,o){this.modelName=r,this.name=t,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let r=this.isList?"List":"",t=this.isEnum?"Enum":"";return`${r}${t}${this.typeName}FieldRefInput<${this.modelName}>`}};function Tr(e){return e instanceof ot}var nn=class{constructor(r){this.value=r}write(r){r.write(this.value)}markAsError(){this.value.markAsError()}};var on=e=>e,sn={bold:on,red:on,green:on,dim:on,enabled:!1},Ss={bold:W,red:ce,green:$e,dim:Ie,enabled:!0},Cr={write(e){e.writeLine(",")}};var Ce=class{constructor(r){this.contents=r;this.isUnderlined=!1;this.color=r=>r}underline(){return this.isUnderlined=!0,this}setColor(r){return this.color=r,this}write(r){let t=r.getCurrentLineLength();r.write(this.color(this.contents)),this.isUnderlined&&r.afterNextNewline(()=>{r.write(" ".repeat(t)).writeLine(this.color("~".repeat(this.contents.length)))})}};var He=class{constructor(){this.hasError=!1}markAsError(){return this.hasError=!0,this}};var Sr=class extends He{constructor(){super(...arguments);this.items=[]}addItem(t){return this.items.push(new nn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(n=>n.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let n=new Ce("[]");this.hasError&&n.setColor(t.context.colors.red).underline(),t.write(n)}writeWithItems(t){let{colors:n}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(Cr,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(n.red("~".repeat(this.getPrintWidth())))})}};var Rs=": ",an=class{constructor(r,t){this.name=r;this.value=t;this.hasError=!1}markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Rs.length}write(r){let t=new Ce(this.name);this.hasError&&t.underline().setColor(r.context.colors.red),r.write(t).write(Rs).write(this.value)}};var J=class e extends He{constructor(){super(...arguments);this.fields={};this.suggestions=[]}addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[n,...i]=t,o=this.getField(n);if(!o)return;let s=o;for(let a of i){let l;if(s.value instanceof e?l=s.value.getField(a):s.value instanceof Sr&&(l=s.value.getField(Number(a))),!l)return;s=l}return s}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let n=this;for(let i of t){if(!(n instanceof e))return;let o=n.getSubSelectionValue(i);if(!o)return;n=o}return n}getDeepSelectionParent(t){let n=this.getSelectionParent();if(!n)return;let i=n;for(let o of t){let s=i.value.getFieldValue(o);if(!s||!(s instanceof e))return;let a=s.getSelectionParent();if(!a)return;i=a}return i}getSelectionParent(){let t=this.getField("select");if(t?.value instanceof e)return{kind:"select",value:t.value};let n=this.getField("include");if(n?.value instanceof e)return{kind:"include",value:n.value}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(i=>i.getPrintWidth()))+2}write(t){let n=Object.values(this.fields);if(n.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,n)}writeEmpty(t){let n=new Ce("{}");this.hasError&&n.setColor(t.context.colors.red).underline(),t.write(n)}writeWithContents(t,n){t.writeLine("{").withIndent(()=>{t.writeJoined(Cr,[...n,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};var H=class extends He{constructor(t){super();this.text=t}getPrintWidth(){return this.text.length}write(t){let n=new Ce(this.text);this.hasError&&n.underline().setColor(t.context.colors.red),t.write(n)}};var Ci=class{constructor(r){this.errorMessages=[];this.arguments=r}write(r){r.write(this.arguments)}addErrorMessage(r){this.errorMessages.push(r)}renderAllMessages(r){return this.errorMessages.map(t=>t(r)).join(`
`)}};function ln(e){return new Ci(As(e))}function As(e){let r=new J;for(let[t,n]of Object.entries(e)){let i=new an(t,Is(n));r.addField(i)}return r}function Is(e){if(typeof e=="string")return new H(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new H(String(e));if(typeof e=="bigint")return new H(`${e}n`);if(e===null)return new H("null");if(e===void 0)return new H("undefined");if(vr(e))return new H(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return Buffer.isBuffer(e)?new H(`Buffer.alloc(${e.byteLength})`):new H(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let r=Kt(e)?e.toISOString():"Invalid Date";return new H(`new Date("${r}")`)}return e instanceof Ne?new H(`Prisma.${e._getName()}`):Tr(e)?new H(`prisma.${ps(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?Yc(e):typeof e=="object"?As(e):new H(Object.prototype.toString.call(e))}function Yc(e){let r=new Sr;for(let t of e)r.addItem(Is(t));return r}function _s(e){if(e===void 0)return"";let r=ln(e);return new br(0,{colors:sn}).write(r).toString()}var Zc="P2037";function sr({error:e,user_facing_error:r},t,n){return r.error_code?new V(Xc(r,n),{code:r.error_code,clientVersion:t,meta:r.meta,batchRequestIdx:r.batch_request_idx}):new j(e,{clientVersion:t,batchRequestIdx:r.batch_request_idx})}function Xc(e,r){let t=e.message;return(r==="postgresql"||r==="postgres"||r==="mysql")&&e.error_code===Zc&&(t+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),t}var st="<unknown>";function ks(e){var r=e.split(`
`);return r.reduce(function(t,n){var i=tp(n)||ip(n)||ap(n)||pp(n)||up(n);return i&&t.push(i),t},[])}var ep=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,rp=/\((\S*)(?::(\d+))(?::(\d+))\)/;function tp(e){var r=ep.exec(e);if(!r)return null;var t=r[2]&&r[2].indexOf("native")===0,n=r[2]&&r[2].indexOf("eval")===0,i=rp.exec(r[2]);return n&&i!=null&&(r[2]=i[1],r[3]=i[2],r[4]=i[3]),{file:t?null:r[2],methodName:r[1]||st,arguments:t?[r[2]]:[],lineNumber:r[3]?+r[3]:null,column:r[4]?+r[4]:null}}var np=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function ip(e){var r=np.exec(e);return r?{file:r[2],methodName:r[1]||st,arguments:[],lineNumber:+r[3],column:r[4]?+r[4]:null}:null}var op=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,sp=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function ap(e){var r=op.exec(e);if(!r)return null;var t=r[3]&&r[3].indexOf(" > eval")>-1,n=sp.exec(r[3]);return t&&n!=null&&(r[3]=n[1],r[4]=n[2],r[5]=null),{file:r[3],methodName:r[1]||st,arguments:r[2]?r[2].split(","):[],lineNumber:r[4]?+r[4]:null,column:r[5]?+r[5]:null}}var lp=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;function up(e){var r=lp.exec(e);return r?{file:r[3],methodName:r[1]||st,arguments:[],lineNumber:+r[4],column:r[5]?+r[5]:null}:null}var cp=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;function pp(e){var r=cp.exec(e);return r?{file:r[2],methodName:r[1]||st,arguments:[],lineNumber:+r[3],column:r[4]?+r[4]:null}:null}var Si=class{getLocation(){return null}},Ri=class{constructor(){this._error=new Error}getLocation(){let r=this._error.stack;if(!r)return null;let n=ks(r).find(i=>{if(!i.file)return!1;let o=ni(i.file);return o!=="<anonymous>"&&!o.includes("@prisma")&&!o.includes("/packages/client/src/runtime/")&&!o.endsWith("/runtime/binary.js")&&!o.endsWith("/runtime/library.js")&&!o.endsWith("/runtime/edge.js")&&!o.endsWith("/runtime/edge-esm.js")&&!o.startsWith("internal/")&&!i.methodName.includes("new ")&&!i.methodName.includes("getCallSite")&&!i.methodName.includes("Proxy.")&&i.methodName.split(".").length<4});return!n||!n.file?null:{fileName:n.file,lineNumber:n.lineNumber,columnNumber:n.column}}};function We(e){return e==="minimal"?typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new Si:new Ri}var Ds={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function Rr(e={}){let r=dp(e);return Object.entries(r).reduce((n,[i,o])=>(Ds[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function dp(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function un(e={}){return r=>(typeof e._count=="boolean"&&(r._count=r._count._all),r)}function Ls(e,r){let t=un(e);return r({action:"aggregate",unpacker:t,argsMapper:Rr})(e)}function fp(e={}){let{select:r,...t}=e;return typeof r=="object"?Rr({...t,_count:r}):Rr({...t,_count:{_all:!0}})}function gp(e={}){return typeof e.select=="object"?r=>un(e)(r)._count:r=>un(e)(r)._count._all}function Ns(e,r){return r({action:"count",unpacker:gp(e),argsMapper:fp})(e)}function hp(e={}){let r=Rr(e);if(Array.isArray(r.by))for(let t of r.by)typeof t=="string"&&(r.select[t]=!0);else typeof r.by=="string"&&(r.select[r.by]=!0);return r}function yp(e={}){return r=>(typeof e?._count=="boolean"&&r.forEach(t=>{t._count=t._count._all}),r)}function Os(e,r){return r({action:"groupBy",unpacker:yp(e),argsMapper:hp})(e)}function Fs(e,r,t){if(r==="aggregate")return n=>Ls(n,t);if(r==="count")return n=>Ns(n,t);if(r==="groupBy")return n=>Os(n,t)}function Ms(e,r){let t=r.fields.filter(i=>!i.relationName),n=ci(t,i=>i.name);return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new ot(e,o,s.type,s.isList,s.kind==="enum")},...Wt(Object.keys(n))})}var $s=e=>Array.isArray(e)?e:e.split("."),Ai=(e,r)=>$s(r).reduce((t,n)=>t&&t[n],e),qs=(e,r,t)=>$s(r).reduceRight((n,i,o,s)=>Object.assign({},Ai(e,s.slice(0,o)),{[i]:n}),t);function Ep(e,r){return e===void 0||r===void 0?[]:[...r,"select",e]}function bp(e,r,t){return r===void 0?e??{}:qs(r,t,e||!0)}function Ii(e,r,t,n,i,o){let a=e._runtimeDataModel.models[r].fields.reduce((l,u)=>({...l,[u.name]:u}),{});return l=>{let u=We(e._errorFormat),c=Ep(n,i),p=bp(l,o,c),m=t({dataPath:c,callsite:u})(p),f=wp(e,r);return new Proxy(m,{get(g,h){if(!f.includes(h))return g[h];let T=[a[h].type,t,h],C=[c,p];return Ii(e,...T,...C)},...Wt([...f,...Object.getOwnPropertyNames(m)])})}}function wp(e,r){return e._runtimeDataModel.models[r].fields.filter(t=>t.kind==="object").map(t=>t.name)}var Gs=_(ii());var Qs=_(require("fs"));var Bs={keyword:_e,entity:_e,value:e=>W(Ze(e)),punctuation:Ze,directive:_e,function:_e,variable:e=>W(Ze(e)),string:e=>W($e(e)),boolean:de,number:_e,comment:Fr};var xp=e=>e,cn={},Pp=0,P={manual:cn.Prism&&cn.Prism.manual,disableWorkerMessageHandler:cn.Prism&&cn.Prism.disableWorkerMessageHandler,util:{encode:function(e){if(e instanceof ye){let r=e;return new ye(r.type,P.util.encode(r.content),r.alias)}else return Array.isArray(e)?e.map(P.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++Pp}),e.__id},clone:function e(r,t){let n,i,o=P.util.type(r);switch(t=t||{},o){case"Object":if(i=P.util.objId(r),t[i])return t[i];n={},t[i]=n;for(let s in r)r.hasOwnProperty(s)&&(n[s]=e(r[s],t));return n;case"Array":return i=P.util.objId(r),t[i]?t[i]:(n=[],t[i]=n,r.forEach(function(s,a){n[a]=e(s,t)}),n);default:return r}}},languages:{extend:function(e,r){let t=P.util.clone(P.languages[e]);for(let n in r)t[n]=r[n];return t},insertBefore:function(e,r,t,n){n=n||P.languages;let i=n[e],o={};for(let a in i)if(i.hasOwnProperty(a)){if(a==r)for(let l in t)t.hasOwnProperty(l)&&(o[l]=t[l]);t.hasOwnProperty(a)||(o[a]=i[a])}let s=n[e];return n[e]=o,P.languages.DFS(P.languages,function(a,l){l===s&&a!=e&&(this[a]=o)}),o},DFS:function e(r,t,n,i){i=i||{};let o=P.util.objId;for(let s in r)if(r.hasOwnProperty(s)){t.call(r,s,r[s],n||s);let a=r[s],l=P.util.type(a);l==="Object"&&!i[o(a)]?(i[o(a)]=!0,e(a,t,null,i)):l==="Array"&&!i[o(a)]&&(i[o(a)]=!0,e(a,t,s,i))}}},plugins:{},highlight:function(e,r,t){let n={code:e,grammar:r,language:t};return P.hooks.run("before-tokenize",n),n.tokens=P.tokenize(n.code,n.grammar),P.hooks.run("after-tokenize",n),ye.stringify(P.util.encode(n.tokens),n.language)},matchGrammar:function(e,r,t,n,i,o,s){for(let h in t){if(!t.hasOwnProperty(h)||!t[h])continue;if(h==s)return;let A=t[h];A=P.util.type(A)==="Array"?A:[A];for(let T=0;T<A.length;++T){let C=A[T],E=C.inside,I=!!C.lookbehind,me=!!C.greedy,le=0,Nr=C.alias;if(me&&!C.pattern.global){let U=C.pattern.toString().match(/[imuy]*$/)[0];C.pattern=RegExp(C.pattern.source,U+"g")}C=C.pattern||C;for(let U=n,ne=i;U<r.length;ne+=r[U].length,++U){let Ae=r[U];if(r.length>e.length)return;if(Ae instanceof ye)continue;if(me&&U!=r.length-1){C.lastIndex=ne;var p=C.exec(e);if(!p)break;var c=p.index+(I?p[1].length:0),m=p.index+p[0].length,a=U,l=ne;for(let L=r.length;a<L&&(l<m||!r[a].type&&!r[a-1].greedy);++a)l+=r[a].length,c>=l&&(++U,ne=l);if(r[U]instanceof ye)continue;u=a-U,Ae=e.slice(ne,l),p.index-=ne}else{C.lastIndex=0;var p=C.exec(Ae),u=1}if(!p){if(o)break;continue}I&&(le=p[1]?p[1].length:0);var c=p.index+le,p=p[0].slice(le),m=c+p.length,f=Ae.slice(0,c),g=Ae.slice(m);let Y=[U,u];f&&(++U,ne+=f.length,Y.push(f));let pr=new ye(h,E?P.tokenize(p,E):p,Nr,p,me);if(Y.push(pr),g&&Y.push(g),Array.prototype.splice.apply(r,Y),u!=1&&P.matchGrammar(e,r,t,U,ne,!0,h),o)break}}}},tokenize:function(e,r){let t=[e],n=r.rest;if(n){for(let i in n)r[i]=n[i];delete r.rest}return P.matchGrammar(e,t,r,0,0,!1),t},hooks:{all:{},add:function(e,r){let t=P.hooks.all;t[e]=t[e]||[],t[e].push(r)},run:function(e,r){let t=P.hooks.all[e];if(!(!t||!t.length))for(var n=0,i;i=t[n++];)i(r)}},Token:ye};P.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/};P.languages.javascript=P.languages.extend("clike",{"class-name":[P.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/});P.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;P.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:P.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:P.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:P.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:P.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});P.languages.markup&&P.languages.markup.tag.addInlined("script","javascript");P.languages.js=P.languages.javascript;P.languages.typescript=P.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/});P.languages.ts=P.languages.typescript;function ye(e,r,t,n,i){this.type=e,this.content=r,this.alias=t,this.length=(n||"").length|0,this.greedy=!!i}ye.stringify=function(e,r){return typeof e=="string"?e:Array.isArray(e)?e.map(function(t){return ye.stringify(t,r)}).join(""):vp(e.type)(e.content)};function vp(e){return Bs[e]||xp}function Vs(e){return Tp(e,P.languages.javascript)}function Tp(e,r){return P.tokenize(e,r).map(n=>ye.stringify(n)).join("")}var js=_(Wo());function Us(e){return(0,js.default)(e)}var pn=class e{static read(r){let t;try{t=Qs.default.readFileSync(r,"utf-8")}catch{return null}return e.fromContent(t)}static fromContent(r){let t=r.split(/\r?\n/);return new e(1,t)}constructor(r,t){this.firstLineNumber=r,this.lines=t}get lastLineNumber(){return this.firstLineNumber+this.lines.length-1}mapLineAt(r,t){if(r<this.firstLineNumber||r>this.lines.length+this.firstLineNumber)return this;let n=r-this.firstLineNumber,i=[...this.lines];return i[n]=t(i[n]),new e(this.firstLineNumber,i)}mapLines(r){return new e(this.firstLineNumber,this.lines.map((t,n)=>r(t,this.firstLineNumber+n)))}lineAt(r){return this.lines[r-this.firstLineNumber]}prependSymbolAt(r,t){return this.mapLines((n,i)=>i===r?`${t} ${n}`:`  ${n}`)}slice(r,t){let n=this.lines.slice(r-1,t).join(`
`);return new e(r,Us(n).split(`
`))}highlight(){let r=Vs(this.toString());return new e(this.firstLineNumber,r.split(`
`))}toString(){return this.lines.join(`
`)}};var Cp={red:ce,gray:Fr,dim:Ie,bold:W,underline:ee,highlightSource:e=>e.highlight()},Sp={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function Rp({message:e,originalMethod:r,isPanic:t,callArguments:n}){return{functionName:`prisma.${r}()`,message:e,isPanic:t??!1,callArguments:n}}function Ap({callsite:e,message:r,originalMethod:t,isPanic:n,callArguments:i},o){let s=Rp({message:r,originalMethod:t,isPanic:n,callArguments:i});if(!e||typeof window<"u"||process.env.NODE_ENV==="production")return s;let a=e.getLocation();if(!a||!a.lineNumber||!a.columnNumber)return s;let l=Math.max(1,a.lineNumber-3),u=pn.read(a.fileName)?.slice(l,a.lineNumber),c=u?.lineAt(a.lineNumber);if(u&&c){let p=_p(c),m=Ip(c);if(!m)return s;s.functionName=`${m.code})`,s.location=a,n||(u=u.mapLineAt(a.lineNumber,g=>g.slice(0,m.openingBraceIndex))),u=o.highlightSource(u);let f=String(u.lastLineNumber).length;if(s.contextLines=u.mapLines((g,h)=>o.gray(String(h).padStart(f))+" "+g).mapLines(g=>o.dim(g)).prependSymbolAt(a.lineNumber,o.bold(o.red("\u2192"))),i){let g=p+f+1;g+=2,s.callArguments=(0,Gs.default)(i,g).slice(g)}}return s}function Ip(e){let r=Object.keys(De.ModelAction).join("|"),n=new RegExp(String.raw`\.(${r})\(`).exec(e);if(n){let i=n.index+n[0].length,o=e.lastIndexOf(" ",n.index)+1;return{code:e.slice(o,i),openingBraceIndex:i}}return null}function _p(e){let r=0;for(let t=0;t<e.length;t++){if(e.charAt(t)!==" ")return r;r++}return r}function kp({functionName:e,location:r,message:t,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],l=r?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${l}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${l}`)),r&&a.push(s.underline(Dp(r))),i){a.push("");let u=[i.toString()];o&&(u.push(o),u.push(s.dim(")"))),a.push(u.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(t),a.join(`
`)}function Dp(e){let r=[e.fileName];return e.lineNumber&&r.push(String(e.lineNumber)),e.columnNumber&&r.push(String(e.columnNumber)),r.join(":")}function Ar(e){let r=e.showColors?Cp:Sp,t;return t=Ap(e,r),kp(t,r)}function Js(e,r,t,n){return e===De.ModelAction.findFirstOrThrow||e===De.ModelAction.findUniqueOrThrow?Lp(r,t,n):n}function Lp(e,r,t){return async n=>{if("rejectOnNotFound"in n.args){let o=Ar({originalMethod:n.clientMethod,callsite:n.callsite,message:"'rejectOnNotFound' option is not supported"});throw new K(o,{clientVersion:r})}return await t(n).catch(o=>{throw o instanceof V&&o.code==="P2025"?new Le(`No ${e} found`,r):o})}}function Se(e){return e.replace(/^./,r=>r.toLowerCase())}var Np=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],Op=["aggregate","count","groupBy"];function _i(e,r){let t=e._extensions.getAllModelExtensions(r)??{},n=[Fp(e,r),$p(e,r),tt(t),te("name",()=>r),te("$name",()=>r),te("$parent",()=>e._appliedParent)];return Pe({},n)}function Fp(e,r){let t=Se(r),n=Object.keys(De.ModelAction).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=l=>e._request(l);s=Js(o,r,e._clientVersion,s);let a=l=>u=>{let c=We(e._errorFormat);return e._createPrismaPromise(p=>{let m={args:u,dataPath:[],action:o,model:r,clientMethod:`${t}.${i}`,jsModelName:t,transaction:p,callsite:c};return s({...m,...l})})};return Np.includes(o)?Ii(e,r,a):Mp(i)?Fs(e,i,a):a({})}}}function Mp(e){return Op.includes(e)}function $p(e,r){return ir(te("fields",()=>{let t=e._runtimeDataModel.models[r];return Ms(r,t)}))}function Hs(e){return e.replace(/^./,r=>r.toUpperCase())}var ki=Symbol();function at(e){let r=[qp(e),te(ki,()=>e),te("$parent",()=>e._appliedParent)],t=e._extensions.getAllClientExtensions();return t&&r.push(tt(t)),Pe(e,r)}function qp(e){let r=Object.keys(e._runtimeDataModel.models),t=r.map(Se),n=[...new Set(r.concat(t))];return ir({getKeys(){return n},getPropertyValue(i){let o=Hs(i);if(e._runtimeDataModel.models[o]!==void 0)return _i(e,o);if(e._runtimeDataModel.models[i]!==void 0)return _i(e,i)},getPropertyDescriptor(i){if(!t.includes(i))return{enumerable:!1}}})}function Ws(e){return e[ki]?e[ki]:e}function Ks(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let t=e.client.__AccelerateEngine;this._originalClient._engine=new t(this._originalClient._accelerateEngineConfig)}let r=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return at(r)}function zs({result:e,modelName:r,select:t,extensions:n}){let i=n.getAllComputedFields(r);if(!i)return e;let o=[],s=[];for(let a of Object.values(i)){if(t){if(!t[a.name])continue;let l=a.needs.filter(u=>!t[u]);l.length>0&&s.push(nt(l))}Bp(e,a.needs)&&o.push(Vp(a,Pe(e,o)))}return o.length>0||s.length>0?Pe(e,[...o,...s]):e}function Bp(e,r){return r.every(t=>ui(e,t))}function Vp(e,r){return ir(te(e.name,()=>e.compute(r)))}function mn({visitor:e,result:r,args:t,runtimeDataModel:n,modelName:i}){if(Array.isArray(r)){for(let s=0;s<r.length;s++)r[s]=mn({result:r[s],args:t,modelName:i,runtimeDataModel:n,visitor:e});return r}let o=e(r,i,t)??r;return t.include&&Ys({includeOrSelect:t.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),t.select&&Ys({includeOrSelect:t.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Ys({includeOrSelect:e,result:r,parentModelName:t,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||r[o]==null)continue;let l=n.models[t].fields.find(c=>c.name===o);if(!l||l.kind!=="object"||!l.relationName)continue;let u=typeof s=="object"?s:{};r[o]=mn({visitor:i,result:r[o],args:u,modelName:l.type,runtimeDataModel:n})}}function Zs({result:e,modelName:r,args:t,extensions:n,runtimeDataModel:i}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[r]?e:mn({result:e,args:t??{},modelName:r,runtimeDataModel:i,visitor:(s,a,l)=>zs({result:s,modelName:Se(a),select:l.select,extensions:n})})}function Xs(e){if(e instanceof oe)return jp(e);if(Array.isArray(e)){let t=[e[0]];for(let n=1;n<e.length;n++)t[n]=lt(e[n]);return t}let r={};for(let t in e)r[t]=lt(e[t]);return r}function jp(e){return new oe(e.strings,e.values)}function lt(e){if(typeof e!="object"||e==null||e instanceof Ne||Tr(e))return e;if(vr(e))return new Te(e.toFixed());if(wr(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let r=e.length,t;for(t=Array(r);r--;)t[r]=lt(e[r]);return t}if(typeof e=="object"){let r={};for(let t in e)t==="__proto__"?Object.defineProperty(r,t,{value:lt(e[t]),configurable:!0,enumerable:!0,writable:!0}):r[t]=lt(e[t]);return r}tr(e,"Unknown value")}function ra(e,r,t,n=0){return e._createPrismaPromise(i=>{let o=r.customDataProxyFetch;return"transaction"in r&&i!==void 0&&(r.transaction?.kind==="batch"&&r.transaction.lock.then(),r.transaction=i),n===t.length?e._executeRequest(r):t[n]({model:r.model,operation:r.model?r.action:r.clientMethod,args:Xs(r.args??{}),__internalParams:r,query:(s,a=r)=>{let l=a.customDataProxyFetch;return a.customDataProxyFetch=oa(o,l),a.args=s,ra(e,a,t,n+1)}})})}function ta(e,r){let{jsModelName:t,action:n,clientMethod:i}=r,o=t?n:i;if(e._extensions.isEmpty())return e._executeRequest(r);let s=e._extensions.getAllQueryCallbacks(t??"$none",o);return ra(e,r,s)}function na(e){return r=>{let t={requests:r},n=r[0].extensions.getAllBatchQueryCallbacks();return n.length?ia(t,n,0,e):e(t)}}function ia(e,r,t,n){if(t===r.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return r[t]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let l=a.customDataProxyFetch;return a.customDataProxyFetch=oa(i,l),ia(a,r,t+1,n)}})}var ea=e=>e;function oa(e=ea,r=ea){return t=>e(r(t))}function aa(e,r,t){let n=Se(t);return!r.result||!(r.result.$allModels||r.result[n])?e:Up({...e,...sa(r.name,e,r.result.$allModels),...sa(r.name,e,r.result[n])})}function Up(e){let r=new xe,t=(n,i)=>r.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>t(o,i)):[n]));return hr(e,n=>({...n,needs:t(n.name,new Set)}))}function sa(e,r,t){return t?hr(t,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:Qp(r,o,i)})):{}}function Qp(e,r,t){let n=e?.[r]?.compute;return n?i=>t({...i,[r]:n(i)}):t}function la(e,r){if(!r)return e;let t={...e};for(let n of Object.values(r))if(e[n.name])for(let i of n.needs)t[i]=!0;return t}var dn=class{constructor(r,t){this.extension=r;this.previous=t;this.computedFieldsCache=new xe;this.modelExtensionsCache=new xe;this.queryCallbacksCache=new xe;this.clientExtensions=zr(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());this.batchCallbacks=zr(()=>{let r=this.previous?.getAllBatchQueryCallbacks()??[],t=this.extension.query?.$__internalBatch;return t?r.concat(t):r})}getAllComputedFields(r){return this.computedFieldsCache.getOrCreate(r,()=>aa(this.previous?.getAllComputedFields(r),this.extension,r))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(r){return this.modelExtensionsCache.getOrCreate(r,()=>{let t=Se(r);return!this.extension.model||!(this.extension.model[t]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(r):{...this.previous?.getAllModelExtensions(r),...this.extension.model.$allModels,...this.extension.model[t]}})}getAllQueryCallbacks(r,t){return this.queryCallbacksCache.getOrCreate(`${r}:${t}`,()=>{let n=this.previous?.getAllQueryCallbacks(r,t)??[],i=[],o=this.extension.query;return!o||!(o[r]||o.$allModels||o[t]||o.$allOperations)?n:(o[r]!==void 0&&(o[r][t]!==void 0&&i.push(o[r][t]),o[r].$allOperations!==void 0&&i.push(o[r].$allOperations)),r!=="$none"&&o.$allModels!==void 0&&(o.$allModels[t]!==void 0&&i.push(o.$allModels[t]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[t]!==void 0&&i.push(o[t]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},fn=class e{constructor(r){this.head=r}static empty(){return new e}static single(r){return new e(new dn(r))}isEmpty(){return this.head===void 0}append(r){return new e(new dn(r,this.head))}getAllComputedFields(r){return this.head?.getAllComputedFields(r)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(r){return this.head?.getAllModelExtensions(r)}getAllQueryCallbacks(r,t){return this.head?.getAllQueryCallbacks(r,t)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};var ua=N("prisma:client"),ca={Vercel:"vercel","Netlify CI":"netlify"};function pa({postinstall:e,ciName:r,clientVersion:t}){if(ua("checkPlatformCaching:postinstall",e),ua("checkPlatformCaching:ciName",r),e===!0&&r&&r in ca){let n=`Prisma has detected that this project was built on ${r}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${ca[r]}-build`;throw console.error(n),new S(n,t)}}function ma(e,r){return e?e.datasources?e.datasources:e.datasourceUrl?{[r[0]]:{url:e.datasourceUrl}}:{}:{}}var Gp="Cloudflare-Workers",Jp="node";function gn(){return typeof Netlify=="object"?"netlify":typeof EdgeRuntime=="string"?"edge-light":globalThis.navigator?.userAgent===Gp?"workerd":globalThis.Deno?"deno":globalThis.__lagon__?"lagon":globalThis.process?.release?.name===Jp?"node":globalThis.Bun?"bun":globalThis.fastly?"fastly":"unknown"}var ya=_(require("fs")),ut=_(require("path"));function hn(e){let{runtimeBinaryTarget:r}=e;return`Add "${r}" to \`binaryTargets\` in the "schema.prisma" file and run \`prisma generate\` after saving it:

${Hp(e)}`}function Hp(e){let{generator:r,generatorBinaryTargets:t,runtimeBinaryTarget:n}=e,i={fromEnvVar:null,value:n},o=[...t,i];return si({...r,binaryTargets:o})}function Ke(e){let{runtimeBinaryTarget:r}=e;return`Prisma Client could not locate the Query Engine for runtime "${r}".`}function ze(e){let{searchedLocations:r}=e;return`The following locations have been searched:
${[...new Set(r)].map(i=>`  ${i}`).join(`
`)}`}function da(e){let{runtimeBinaryTarget:r}=e;return`${Ke(e)}

This happened because \`binaryTargets\` have been pinned, but the actual deployment also required "${r}".
${hn(e)}

${ze(e)}`}function yn(e){return`We would appreciate if you could take the time to share some information with us.
Please help us by answering a few questions: https://pris.ly/${e}`}function En(e){let{errorStack:r}=e;return r?.match(/\/\.next|\/next@|\/next\//)?`

We detected that you are using Next.js, learn how to fix this: https://pris.ly/d/engine-not-found-nextjs.`:""}function fa(e){let{queryEngineName:r}=e;return`${Ke(e)}${En(e)}

This is likely caused by a bundler that has not copied "${r}" next to the resulting bundle.
Ensure that "${r}" has been copied next to the bundle or in "${e.expectedLocation}".

${yn("engine-not-found-bundler-investigation")}

${ze(e)}`}function ga(e){let{runtimeBinaryTarget:r,generatorBinaryTargets:t}=e,n=t.find(i=>i.native);return`${Ke(e)}

This happened because Prisma Client was generated for "${n?.value??"unknown"}", but the actual deployment required "${r}".
${hn(e)}

${ze(e)}`}function ha(e){let{queryEngineName:r}=e;return`${Ke(e)}${En(e)}

This is likely caused by tooling that has not copied "${r}" to the deployment folder.
Ensure that you ran \`prisma generate\` and that "${r}" has been copied to "${e.expectedLocation}".

${yn("engine-not-found-tooling-investigation")}

${ze(e)}`}var Wp=N("prisma:client:engines:resolveEnginePath"),Kp=()=>new RegExp("runtime[\\\\/]library\\.m?js$");async function Ea(e,r){let t={binary:process.env.PRISMA_QUERY_ENGINE_BINARY,library:process.env.PRISMA_QUERY_ENGINE_LIBRARY}[e]??r.prismaPath;if(t!==void 0)return t;let{enginePath:n,searchedLocations:i}=await zp(e,r);if(Wp("enginePath",n),n!==void 0&&e==="binary"&&ri(n),n!==void 0)return r.prismaPath=n;let o=await rr(),s=r.generator?.binaryTargets??[],a=s.some(m=>m.native),l=!s.some(m=>m.value===o),u=__filename.match(Kp())===null,c={searchedLocations:i,generatorBinaryTargets:s,generator:r.generator,runtimeBinaryTarget:o,queryEngineName:ba(e,o),expectedLocation:ut.default.relative(process.cwd(),r.dirname),errorStack:new Error().stack},p;throw a&&l?p=ga(c):l?p=da(c):u?p=fa(c):p=ha(c),new S(p,r.clientVersion)}async function zp(engineType,config){let binaryTarget=await rr(),searchedLocations=[],dirname=eval("__dirname"),searchLocations=[config.dirname,ut.default.resolve(dirname,".."),config.generator?.output?.value??dirname,ut.default.resolve(dirname,"../../../.prisma/client"),"/tmp/prisma-engines",config.cwd];__filename.includes("resolveEnginePath")&&searchLocations.push(Ko());for(let e of searchLocations){let r=ba(engineType,binaryTarget),t=ut.default.join(e,r);if(searchedLocations.push(e),ya.default.existsSync(t))return{enginePath:t,searchedLocations}}return{enginePath:void 0,searchedLocations}}function ba(e,r){return e==="library"?kt(r,"fs"):`query-engine-${r}${r==="windows"?".exe":""}`}var Di=_(li());function wa(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,r=>`${r[0]}5`):""}function xa(e){return e.split(`
`).map(r=>r.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}var Pa=_(ns());function va({title:e,user:r="prisma",repo:t="prisma",template:n="bug_report.yml",body:i}){return(0,Pa.default)({user:r,repo:t,template:n,title:e,body:i})}function Ta({version:e,binaryTarget:r,title:t,description:n,engineVersion:i,database:o,query:s}){let a=fo(6e3-(s?.length??0)),l=xa((0,Di.default)(a)),u=n?`# Description
\`\`\`
${n}
\`\`\``:"",c=(0,Di.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${r?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${u}

## Logs
\`\`\`
${l}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?wa(s):""}
\`\`\`
`),p=va({title:t,body:c});return`${t}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${ee(p)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}function Ir({inlineDatasources:e,overrideDatasources:r,env:t,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=r[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=t[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw new S(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new S("error: Missing URL environment variable, value, or override.",n);return i}var bn=class extends Error{constructor(r,t){super(r),this.clientVersion=t.clientVersion,this.cause=t.cause}get[Symbol.toStringTag](){return this.name}};var ae=class extends bn{constructor(r,t){super(r,t),this.isRetryable=t.isRetryable??!0}};function R(e,r){return{...e,isRetryable:r}}var _r=class extends ae{constructor(t){super("This request must be retried",R(t,!0));this.name="ForcedRetryError";this.code="P5001"}};w(_r,"ForcedRetryError");var ar=class extends ae{constructor(t,n){super(t,R(n,!1));this.name="InvalidDatasourceError";this.code="P6001"}};w(ar,"InvalidDatasourceError");var lr=class extends ae{constructor(t,n){super(t,R(n,!1));this.name="NotImplementedYetError";this.code="P5004"}};w(lr,"NotImplementedYetError");var $=class extends ae{constructor(r,t){super(r,t),this.response=t.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var ur=class extends ${constructor(t){super("Schema needs to be uploaded",R(t,!0));this.name="SchemaMissingError";this.code="P5005"}};w(ur,"SchemaMissingError");var Li="This request could not be understood by the server",ct=class extends ${constructor(t,n,i){super(n||Li,R(t,!1));this.name="BadRequestError";this.code="P5000";i&&(this.code=i)}};w(ct,"BadRequestError");var pt=class extends ${constructor(t,n){super("Engine not started: healthcheck timeout",R(t,!0));this.name="HealthcheckTimeoutError";this.code="P5013";this.logs=n}};w(pt,"HealthcheckTimeoutError");var mt=class extends ${constructor(t,n,i){super(n,R(t,!0));this.name="EngineStartupError";this.code="P5014";this.logs=i}};w(mt,"EngineStartupError");var dt=class extends ${constructor(t){super("Engine version is not supported",R(t,!1));this.name="EngineVersionNotSupportedError";this.code="P5012"}};w(dt,"EngineVersionNotSupportedError");var Ni="Request timed out",ft=class extends ${constructor(t,n=Ni){super(n,R(t,!1));this.name="GatewayTimeoutError";this.code="P5009"}};w(ft,"GatewayTimeoutError");var Yp="Interactive transaction error",gt=class extends ${constructor(t,n=Yp){super(n,R(t,!1));this.name="InteractiveTransactionError";this.code="P5015"}};w(gt,"InteractiveTransactionError");var Zp="Request parameters are invalid",ht=class extends ${constructor(t,n=Zp){super(n,R(t,!1));this.name="InvalidRequestError";this.code="P5011"}};w(ht,"InvalidRequestError");var Oi="Requested resource does not exist",yt=class extends ${constructor(t,n=Oi){super(n,R(t,!1));this.name="NotFoundError";this.code="P5003"}};w(yt,"NotFoundError");var Fi="Unknown server error",kr=class extends ${constructor(t,n,i){super(n||Fi,R(t,!0));this.name="ServerError";this.code="P5006";this.logs=i}};w(kr,"ServerError");var Mi="Unauthorized, check your connection string",Et=class extends ${constructor(t,n=Mi){super(n,R(t,!1));this.name="UnauthorizedError";this.code="P5007"}};w(Et,"UnauthorizedError");var $i="Usage exceeded, retry again later",bt=class extends ${constructor(t,n=$i){super(n,R(t,!0));this.name="UsageExceededError";this.code="P5008"}};w(bt,"UsageExceededError");async function Xp(e){let r;try{r=await e.text()}catch{return{type:"EmptyError"}}try{let t=JSON.parse(r);if(typeof t=="string")switch(t){case"InternalDataProxyError":return{type:"DataProxyError",body:t};default:return{type:"UnknownTextError",body:t}}if(typeof t=="object"&&t!==null){if("is_panic"in t&&"message"in t&&"error_code"in t)return{type:"QueryEngineError",body:t};if("EngineNotStarted"in t||"InteractiveTransactionMisrouted"in t||"InvalidRequestError"in t){let n=Object.values(t)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:t}:{type:"DataProxyError",body:t}}}return{type:"UnknownJsonError",body:t}}catch{return r===""?{type:"EmptyError"}:{type:"UnknownTextError",body:r}}}async function wt(e,r){if(e.ok)return;let t={clientVersion:r,response:e},n=await Xp(e);if(n.type==="QueryEngineError")throw new V(n.body.message,{code:n.body.error_code,clientVersion:r});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new kr(t,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new ur(t);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new dt(t);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new mt(t,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new S(i,r,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new pt(t,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new gt(t,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new ht(t,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Et(t,Dr(Mi,n));if(e.status===404)return new yt(t,Dr(Oi,n));if(e.status===429)throw new bt(t,Dr($i,n));if(e.status===504)throw new ft(t,Dr(Ni,n));if(e.status>=500)throw new kr(t,Dr(Fi,n));if(e.status>=400)throw new ct(t,Dr(Li,n))}function Dr(e,r){return r.type==="EmptyError"?e:`${e}: ${JSON.stringify(r)}`}function Ca(e){let r=Math.pow(2,e)*50,t=Math.ceil(Math.random()*r)-Math.ceil(r/2),n=r+t;return new Promise(i=>setTimeout(()=>i(n),n))}var Fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Sa(e){let r=new TextEncoder().encode(e),t="",n=r.byteLength,i=n%3,o=n-i,s,a,l,u,c;for(let p=0;p<o;p=p+3)c=r[p]<<16|r[p+1]<<8|r[p+2],s=(c&16515072)>>18,a=(c&258048)>>12,l=(c&4032)>>6,u=c&63,t+=Fe[s]+Fe[a]+Fe[l]+Fe[u];return i==1?(c=r[o],s=(c&252)>>2,a=(c&3)<<4,t+=Fe[s]+Fe[a]+"=="):i==2&&(c=r[o]<<8|r[o+1],s=(c&64512)>>10,a=(c&1008)>>4,l=(c&15)<<2,t+=Fe[s]+Fe[a]+Fe[l]+"="),t}function Ra(e){if(!!e.generator?.previewFeatures.some(t=>t.toLowerCase().includes("metrics")))throw new S("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}function em(e){return e[0]*1e3+e[1]/1e6}function Aa(e){return new Date(em(e))}var Ia={"@prisma/debug":"workspace:*","@prisma/engines-version":"5.10.0-34.5a9203d0590c951969e85a7d07215503f4672eb9","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};var xt=class extends ae{constructor(t,n){super(`Cannot fetch data from service:
${t}`,R(n,!0));this.name="RequestError";this.code="P5010"}};w(xt,"RequestError");async function cr(e,r,t=n=>n){let n=r.clientVersion;try{return typeof fetch=="function"?await t(fetch)(e,r):await t(qi)(e,r)}catch(i){let o=i.message??"Unknown error";throw new xt(o,{clientVersion:n})}}function tm(e){return{...e.headers,"Content-Type":"application/json"}}function nm(e){return{method:e.method,headers:tm(e)}}function im(e,r){return{text:()=>Promise.resolve(Buffer.concat(e).toString()),json:()=>Promise.resolve().then(()=>JSON.parse(Buffer.concat(e).toString())),ok:r.statusCode>=200&&r.statusCode<=299,status:r.statusCode,url:r.url,headers:new Bi(r.headers)}}async function qi(e,r={}){let t=om("https"),n=nm(r),i=[],{origin:o}=new URL(e);return new Promise((s,a)=>{let l=t.request(e,n,u=>{let{statusCode:c,headers:{location:p}}=u;c>=301&&c<=399&&p&&(p.startsWith("http")===!1?s(qi(`${o}${p}`,r)):s(qi(p,r))),u.on("data",m=>i.push(m)),u.on("end",()=>s(im(i,u))),u.on("error",a)});l.on("error",a),l.end(r.body??"")})}var om=typeof require<"u"?require:()=>{},Bi=class{constructor(r={}){this.headers=new Map;for(let[t,n]of Object.entries(r))if(typeof n=="string")this.headers.set(t,n);else if(Array.isArray(n))for(let i of n)this.headers.set(t,i)}append(r,t){this.headers.set(r,t)}delete(r){this.headers.delete(r)}get(r){return this.headers.get(r)??null}has(r){return this.headers.has(r)}set(r,t){this.headers.set(r,t)}forEach(r,t){for(let[n,i]of this.headers)r.call(t,i,n,this)}};var sm=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,_a=N("prisma:client:dataproxyEngine");async function am(e,r){let t=Ia["@prisma/engines-version"],n=r.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&sm.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){if(e.startsWith("localhost")||e.startsWith("127.0.0.1"))return"0.0.0";let[s]=t.split("-")??[],[a,l,u]=s.split("."),c=lm(`<=${a}.${l}.${u}`),p=await cr(c,{clientVersion:n});if(!p.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${p.status} ${p.statusText}, response body: ${await p.text()||"<empty body>"}`);let m=await p.text();_a("length of body fetched from unpkg.com",m.length);let f;try{f=JSON.parse(m)}catch(g){throw console.error("JSON.parse error: body fetched from unpkg.com: ",m),g}return f.version}throw new lr("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function ka(e,r){let t=await am(e,r);return _a("version",t),t}function lm(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var Da=3,Vi=N("prisma:client:dataproxyEngine"),ji=class{constructor({apiKey:r,tracingHelper:t,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=r,this.tracingHelper=t,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:r,interactiveTransaction:t}={}){let n={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(n.traceparent=r??this.tracingHelper.getTraceParent()),t&&(n["X-transaction-id"]=t.id);let i=this.buildCaptureSettings();return i.length>0&&(n["X-capture-telemetry"]=i.join(", ")),n}buildCaptureSettings(){let r=[];return this.tracingHelper.isEnabled()&&r.push("tracing"),this.logLevel&&r.push(this.logLevel),this.logQueries&&r.push("query"),r}},Pt=class{constructor(r){this.name="DataProxyEngine";Ra(r),this.config=r,this.env={...r.env,...typeof process<"u"?process.env:{}},this.inlineSchema=Sa(r.inlineSchema),this.inlineDatasources=r.inlineDatasources,this.inlineSchemaHash=r.inlineSchemaHash,this.clientVersion=r.clientVersion,this.engineHash=r.engineVersion,this.logEmitter=r.logEmitter,this.tracingHelper=r.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let[r,t]=this.extractHostAndApiKey();this.host=r,this.headerBuilder=new ji({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await ka(r,this.config),Vi("host",this.host)})(),await this.startPromise}async stop(){}propagateResponseExtensions(r){r?.logs?.length&&r.logs.forEach(t=>{switch(t.level){case"debug":case"error":case"trace":case"warn":case"info":break;case"query":{let n=typeof t.attributes.query=="string"?t.attributes.query:"";if(!this.tracingHelper.isEnabled()){let[i]=n.split("/* traceparent");n=i}this.logEmitter.emit("query",{query:n,timestamp:Aa(t.timestamp),duration:Number(t.attributes.duration_ms),params:t.attributes.params,target:t.attributes.target})}}}),r?.traces?.length&&this.tracingHelper.createEngineSpan({span:!0,spans:r.traces})}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(r){return await this.start(),`https://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${r}`}async uploadSchema(){let r={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(r,async()=>{let t=await cr(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});t.ok||Vi("schema response status",t.status);let n=await wt(t,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(r,{traceparent:t,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:r,traceparent:t,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(r,{traceparent:t,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=Er(r,n),{batchResult:a,elapsed:l}=await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:t});return a.map(u=>"errors"in u&&u.errors.length>0?sr(u.errors[0],this.clientVersion,this.config.activeProvider):{data:u,elapsed:l})}requestInternal({body:r,traceparent:t,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await cr(s,{method:"POST",headers:this.headerBuilder.build({traceparent:t,interactiveTransaction:i}),body:JSON.stringify(r),clientVersion:this.clientVersion},n);a.ok||Vi("graphql response status",a.status),await this.handleError(await wt(a,this.clientVersion));let l=await a.json(),u=l.extensions;if(u&&this.propagateResponseExtensions(u),l.errors)throw l.errors.length===1?sr(l.errors[0],this.config.clientVersion,this.config.activeProvider):new j(l.errors,{clientVersion:this.config.clientVersion});return l}})}async transaction(r,t,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[r]} transaction`,callback:async({logHttpCall:o})=>{if(r==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let l=await cr(a,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await wt(l,this.clientVersion));let u=await l.json(),c=u.extensions;c&&this.propagateResponseExtensions(c);let p=u.id,m=u["data-proxy"].endpoint;return{id:p,payload:{endpoint:m}}}else{let s=`${n.payload.endpoint}/${r}`;o(s);let a=await cr(s,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),clientVersion:this.clientVersion});await this.handleError(await wt(a,this.clientVersion));let u=(await a.json()).extensions;u&&this.propagateResponseExtensions(u);return}}})}extractHostAndApiKey(){let r={clientVersion:this.clientVersion},t=Object.keys(this.inlineDatasources)[0],n=Ir({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),i;try{i=new URL(n)}catch{throw new ar(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\``,r)}let{protocol:o,host:s,searchParams:a}=i;if(o!=="prisma:")throw new ar(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\``,r);let l=a.get("api_key");if(l===null||l.length<1)throw new ar(`Error validating datasource \`${t}\`: the URL must contain a valid API key`,r);return[s,l]}metrics(){throw new lr("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(r){for(let t=0;;t++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${t})`,timestamp:new Date,target:""})};try{return await r.callback({logHttpCall:n})}catch(i){if(!(i instanceof ae)||!i.isRetryable)throw i;if(t>=Da)throw i instanceof _r?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${t+1}/${Da} failed for ${r.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await Ca(t);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(r){if(r instanceof ur)throw await this.uploadSchema(),new _r({clientVersion:this.clientVersion,cause:r});if(r)throw r}};function La(e){if(e?.kind==="itx")return e.options.id}var Qi=_(require("os")),Na=_(require("path"));var Ui=Symbol("PrismaLibraryEngineCache");function um(){let e=globalThis;return e[Ui]===void 0&&(e[Ui]={}),e[Ui]}function cm(e){let r=um();if(r[e]!==void 0)return r[e];let t=Na.default.toNamespacedPath(e),n={exports:{}},i=0;return process.platform!=="win32"&&(i=Qi.default.constants.dlopen.RTLD_LAZY|Qi.default.constants.dlopen.RTLD_DEEPBIND),process.dlopen(n,t,i),r[e]=n.exports,n.exports}var Oa={async loadLibrary(e){let r=await Wn(),t=await Ea("library",e);try{return e.tracingHelper.runInChildSpan({name:"loadLibrary",internal:!0},()=>cm(t))}catch(n){let i=ti({e:n,platformInfo:r,id:t});throw new S(i,e.clientVersion)}}};var Gi,Fa={async loadLibrary(e){let{clientVersion:r,adapter:t,engineWasm:n}=e;if(t===void 0)throw new S(`The \`adapter\` option for \`PrismaClient\` is required in this context (${gn()})`,r);if(n===void 0)throw new S("WASM engine was unexpectedly `undefined`",r);Gi===void 0&&(Gi=(async()=>{let o=n.getRuntime(),s=await n.getQueryEngineWasmModule();if(s==null)throw new S("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",r);let a={"./query_engine_bg.js":o},l=new WebAssembly.Instance(s,a);return o.__wbg_set_wasm(l.exports),o.QueryEngine})());let i=await Gi;return{debugPanic(){return Promise.reject("{}")},dmmf(){return Promise.resolve("{}")},version(){return{commit:"unknown",version:"unknown"}},QueryEngine:i}}};var pm="P2036",Re=N("prisma:client:libraryEngine");function mm(e){return e.item_type==="query"&&"query"in e}function dm(e){return"level"in e?e.level==="error"&&e.message==="PANIC":!1}var Ma=[...Mn,"native"],$a=0,vt=class{constructor(r,t){this.name="LibraryEngine";this.libraryLoader=t??Oa,r.engineWasm!==void 0&&(this.libraryLoader=t??Fa),this.config=r,this.libraryStarted=!1,this.logQueries=r.logQueries??!1,this.logLevel=r.logLevel??"error",this.logEmitter=r.logEmitter,this.datamodel=r.inlineSchema,r.enableDebugLogs&&(this.logLevel="debug");let n=Object.keys(r.overrideDatasources)[0],i=r.overrideDatasources[n]?.url;n!==void 0&&i!==void 0&&(this.datasourceOverrides={[n]:i}),this.libraryInstantiationPromise=this.instantiateLibrary(),this.checkForTooManyEngines()}checkForTooManyEngines(){$a===10&&console.warn(`${de("warn(prisma-client)")} This is the 10th instance of Prisma Client being started. Make sure this is intentional.`)}async transaction(r,t,n){await this.start();let i=JSON.stringify(t),o;if(r==="start"){let a=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel});o=await this.engine?.startTransaction(a,i)}else r==="commit"?o=await this.engine?.commitTransaction(n.id,i):r==="rollback"&&(o=await this.engine?.rollbackTransaction(n.id,i));let s=this.parseEngineResponse(o);if(fm(s)){let a=this.getExternalAdapterError(s);throw a?a.error:new V(s.message,{code:s.error_code,clientVersion:this.config.clientVersion,meta:s.meta})}return s}async instantiateLibrary(){if(Re("internalSetup"),this.libraryInstantiationPromise)return this.libraryInstantiationPromise;Fn(),this.binaryTarget=await this.getCurrentBinaryTarget(),await this.loadEngine(),this.version()}async getCurrentBinaryTarget(){{if(this.binaryTarget)return this.binaryTarget;let r=await rr();if(!Ma.includes(r))throw new S(`Unknown ${ce("PRISMA_QUERY_ENGINE_LIBRARY")} ${ce(W(r))}. Possible binaryTargets: ${$e(Ma.join(", "))} or a path to the query engine library.
You may have to run ${$e("prisma generate")} for your changes to take effect.`,this.config.clientVersion);return r}}parseEngineResponse(r){if(!r)throw new j("Response from the Engine was empty",{clientVersion:this.config.clientVersion});try{return JSON.parse(r)}catch{throw new j("Unable to JSON.parse response from engine",{clientVersion:this.config.clientVersion})}}async loadEngine(){if(!this.engine){this.QueryEngineConstructor||(this.library=await this.libraryLoader.loadLibrary(this.config),this.QueryEngineConstructor=this.library.QueryEngine);try{let r=new WeakRef(this),{adapter:t}=this.config;t&&Re("Using driver adapter: %O",t),this.engine=new this.QueryEngineConstructor({datamodel:this.datamodel,env:process.env,logQueries:this.config.logQueries??!1,ignoreEnvVarErrors:!0,datasourceOverrides:this.datasourceOverrides??{},logLevel:this.logLevel,configDir:this.config.cwd,engineProtocol:"json"},n=>{r.deref()?.logger(n)},t),$a++}catch(r){let t=r,n=this.parseInitError(t.message);throw typeof n=="string"?t:new S(n.message,this.config.clientVersion,n.error_code)}}}logger(r){let t=this.parseEngineResponse(r);if(t){if("span"in t){this.config.tracingHelper.createEngineSpan(t);return}t.level=t?.level.toLowerCase()??"unknown",mm(t)?this.logEmitter.emit("query",{timestamp:new Date,query:t.query,params:t.params,duration:Number(t.duration_ms),target:t.module_path}):dm(t)?this.loggerRustPanic=new ue(Ji(this,`${t.message}: ${t.reason} in ${t.file}:${t.line}:${t.column}`),this.config.clientVersion):this.logEmitter.emit(t.level,{timestamp:new Date,message:t.message,target:t.module_path})}}parseInitError(r){try{return JSON.parse(r)}catch{}return r}parseRequestError(r){try{return JSON.parse(r)}catch{}return r}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the library engine since Prisma 5.0.0, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){if(await this.libraryInstantiationPromise,await this.libraryStoppingPromise,this.libraryStartingPromise)return Re(`library already starting, this.libraryStarted: ${this.libraryStarted}`),this.libraryStartingPromise;if(this.libraryStarted)return;let r=async()=>{Re("library starting");try{let t={traceparent:this.config.tracingHelper.getTraceParent()};await this.engine?.connect(JSON.stringify(t)),this.libraryStarted=!0,Re("library started")}catch(t){let n=this.parseInitError(t.message);throw typeof n=="string"?t:new S(n.message,this.config.clientVersion,n.error_code)}finally{this.libraryStartingPromise=void 0}};return this.libraryStartingPromise=this.config.tracingHelper.runInChildSpan("connect",r),this.libraryStartingPromise}async stop(){if(await this.libraryStartingPromise,await this.executingQueryPromise,this.libraryStoppingPromise)return Re("library is already stopping"),this.libraryStoppingPromise;if(!this.libraryStarted)return;let r=async()=>{await new Promise(n=>setTimeout(n,5)),Re("library stopping");let t={traceparent:this.config.tracingHelper.getTraceParent()};await this.engine?.disconnect(JSON.stringify(t)),this.libraryStarted=!1,this.libraryStoppingPromise=void 0,Re("library stopped")};return this.libraryStoppingPromise=this.config.tracingHelper.runInChildSpan("disconnect",r),this.libraryStoppingPromise}version(){return this.versionInfo=this.library?.version(),this.versionInfo?.version??"unknown"}debugPanic(r){return this.library?.debugPanic(r)}async request(r,{traceparent:t,interactiveTransaction:n}){Re(`sending request, this.libraryStarted: ${this.libraryStarted}`);let i=JSON.stringify({traceparent:t}),o=JSON.stringify(r);try{await this.start(),this.executingQueryPromise=this.engine?.query(o,i,n?.id),this.lastQuery=o;let s=this.parseEngineResponse(await this.executingQueryPromise);if(s.errors)throw s.errors.length===1?this.buildQueryError(s.errors[0]):new j(JSON.stringify(s.errors),{clientVersion:this.config.clientVersion});if(this.loggerRustPanic)throw this.loggerRustPanic;return{data:s,elapsed:0}}catch(s){if(s instanceof S)throw s;if(s.code==="GenericFailure"&&s.message?.startsWith("PANIC:"))throw new ue(Ji(this,s.message),this.config.clientVersion);let a=this.parseRequestError(s.message);throw typeof a=="string"?s:new j(`${a.message}
${a.backtrace}`,{clientVersion:this.config.clientVersion})}}async requestBatch(r,{transaction:t,traceparent:n}){Re("requestBatch");let i=Er(r,t);await this.start(),this.lastQuery=JSON.stringify(i),this.executingQueryPromise=this.engine.query(this.lastQuery,JSON.stringify({traceparent:n}),La(t));let o=await this.executingQueryPromise,s=this.parseEngineResponse(o);if(s.errors)throw s.errors.length===1?this.buildQueryError(s.errors[0]):new j(JSON.stringify(s.errors),{clientVersion:this.config.clientVersion});let{batchResult:a,errors:l}=s;if(Array.isArray(a))return a.map(u=>u.errors&&u.errors.length>0?this.loggerRustPanic??this.buildQueryError(u.errors[0]):{data:u,elapsed:0});throw l&&l.length===1?new Error(l[0].error):new Error(JSON.stringify(s))}buildQueryError(r){if(r.user_facing_error.is_panic)return new ue(Ji(this,r.user_facing_error.message),this.config.clientVersion);let t=this.getExternalAdapterError(r.user_facing_error);return t?t.error:sr(r,this.config.clientVersion,this.config.activeProvider)}getExternalAdapterError(r){if(r.error_code===pm&&this.config.adapter){let t=r.meta?.id;jt(typeof t=="number","Malformed external JS error received from the engine");let n=this.config.adapter.errorRegistry.consumeError(t);return jt(n,"External error with reported id was not registered"),n}}async metrics(r){await this.start();let t=await this.engine.metrics(JSON.stringify(r));return r.format==="prometheus"?t:this.parseEngineResponse(t)}};function fm(e){return typeof e=="object"&&e!==null&&e.error_code!==void 0}function Ji(e,r){return Ta({binaryTarget:e.binaryTarget,title:r,version:e.config.clientVersion,engineVersion:e.versionInfo?.commit,database:e.config.activeProvider,query:e.lastQuery})}function qa({copyEngine:e=!0},r){let t;try{t=Ir({inlineDatasources:r.inlineDatasources,overrideDatasources:r.overrideDatasources,env:{...r.env,...process.env},clientVersion:r.clientVersion})}catch{}e&&t?.startsWith("prisma://")&&Kr("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)");let n=Gr(r.generator),i=!!(t?.startsWith("prisma://")||!e),o=!!r.adapter,s=n==="library",a=n==="binary";if(i&&o||o&&!1){let l;throw e?t?.startsWith("prisma://")?l=["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:l=["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:l=["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."],new K(l.join(`
`),{clientVersion:r.clientVersion})}if(i)return new Pt(r);if(s)return new vt(r);throw new K("Invalid client engine type, please use `library` or `binary`",{clientVersion:r.clientVersion})}function wn({generator:e}){return e?.previewFeatures??[]}var Ga=_(Hi());function Ua(e,r){let t=Qa(e),n=gm(t),i=ym(n);i?xn(i,r):r.addErrorMessage(()=>"Unknown error")}function Qa(e){return e.errors.flatMap(r=>r.kind==="Union"?Qa(r):[r])}function gm(e){let r=new Map,t=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){t.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=r.get(i);o?r.set(i,{...n,argument:{...n.argument,typeNames:hm(o.argument.typeNames,n.argument.typeNames)}}):r.set(i,n)}return t.push(...r.values()),t}function hm(e,r){return[...new Set(e.concat(r))]}function ym(e){return pi(e,(r,t)=>{let n=Va(r),i=Va(t);return n!==i?n-i:ja(r)-ja(t)})}function Va(e){let r=0;return Array.isArray(e.selectionPath)&&(r+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(r+=e.argumentPath.length),r}function ja(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}var Me=class{constructor(r,t){this.name=r;this.value=t;this.isRequired=!1}makeRequired(){return this.isRequired=!0,this}write(r){let{colors:{green:t}}=r.context;r.addMarginSymbol(t(this.isRequired?"+":"?")),r.write(t(this.name)),this.isRequired||r.write(t("?")),r.write(t(": ")),typeof this.value=="string"?r.write(t(this.value)):r.write(this.value)}};var Pn=class{constructor(){this.fields=[]}addField(r,t){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${r}: ${t}`))).addMarginSymbol(i(o("+")))}}),this}write(r){let{colors:{green:t}}=r.context;r.writeLine(t("{")).withIndent(()=>{r.writeJoined(Cr,this.fields).newLine()}).write(t("}")).addMarginSymbol(t("+"))}};function xn(e,r){switch(e.kind){case"IncludeAndSelect":Em(e,r);break;case"IncludeOnScalar":bm(e,r);break;case"EmptySelection":wm(e,r);break;case"UnknownSelectionField":xm(e,r);break;case"UnknownArgument":Pm(e,r);break;case"UnknownInputField":vm(e,r);break;case"RequiredArgumentMissing":Tm(e,r);break;case"InvalidArgumentType":Cm(e,r);break;case"InvalidArgumentValue":Sm(e,r);break;case"ValueTooLarge":Rm(e,r);break;case"SomeFieldsMissing":Am(e,r);break;case"TooManyFieldsGiven":Im(e,r);break;case"Union":Ua(e,r);break;default:throw new Error("not implemented: "+e.kind)}}function Em(e,r){let t=r.arguments.getDeepSubSelectionValue(e.selectionPath);t&&t instanceof J&&(t.getField("include")?.markAsError(),t.getField("select")?.markAsError()),r.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green("`include`")} or ${n.green("`select`")}, but ${n.red("not both")} at the same time.`)}function bm(e,r){let[t,n]=vn(e.selectionPath),i=e.outputType,o=r.arguments.getDeepSelectionParent(t)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new Me(s.name,"true"));r.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${Tt(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function wm(e,r){let t=e.outputType,n=r.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Wa(n,t)),r.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(t.name)} must not be empty. ${Tt(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(t.name)} needs ${o.bold("at least one truthy value")}.`)}function xm(e,r){let[t,n]=vn(e.selectionPath),i=r.arguments.getDeepSelectionParent(t);i&&(i.value.getField(n)?.markAsError(),Wa(i.value,e.outputType)),r.addErrorMessage(o=>{let s=[`Unknown field ${o.red(`\`${n}\``)}`];return i&&s.push(`for ${o.bold(i.kind)} statement`),s.push(`on model ${o.bold(`\`${e.outputType.name}\``)}.`),s.push(Tt(o)),s.join(" ")})}function Pm(e,r){let t=e.argumentPath[0],n=r.arguments.getDeepSubSelectionValue(e.selectionPath);n instanceof J&&(n.getField(t)?.markAsError(),_m(n,e.arguments)),r.addErrorMessage(i=>Ja(i,t,e.arguments.map(o=>o.name)))}function vm(e,r){let[t,n]=vn(e.argumentPath),i=r.arguments.getDeepSubSelectionValue(e.selectionPath);if(i instanceof J){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(t);o instanceof J&&Ka(o,e.inputType)}r.addErrorMessage(o=>Ja(o,n,e.inputType.fields.map(s=>s.name)))}function Ja(e,r,t){let n=[`Unknown argument \`${e.red(r)}\`.`],i=Dm(r,t);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),t.length>0&&n.push(Tt(e)),n.join(" ")}function Tm(e,r){let t;r.addErrorMessage(l=>t?.value instanceof H&&t.value.text==="null"?`Argument \`${l.green(o)}\` must not be ${l.red("null")}.`:`Argument \`${l.green(o)}\` is missing.`);let n=r.arguments.getDeepSubSelectionValue(e.selectionPath);if(!(n instanceof J))return;let[i,o]=vn(e.argumentPath),s=new Pn,a=n.getDeepFieldValue(i);if(a instanceof J)if(t=a.getField(o),t&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let l of e.inputTypes[0].fields)s.addField(l.name,l.typeNames.join(" | "));a.addSuggestion(new Me(o,s).makeRequired())}else{let l=e.inputTypes.map(Ha).join(" | ");a.addSuggestion(new Me(o,l).makeRequired())}}function Ha(e){return e.kind==="list"?`${Ha(e.elementType)}[]`:e.name}function Cm(e,r){let t=e.argument.name,n=r.arguments.getDeepSubSelectionValue(e.selectionPath);n instanceof J&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),r.addErrorMessage(i=>{let o=Tn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(t)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function Sm(e,r){let t=e.argument.name,n=r.arguments.getDeepSubSelectionValue(e.selectionPath);n instanceof J&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),r.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(t)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=Tn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function Rm(e,r){let t=e.argument.name,n=r.arguments.getDeepSubSelectionValue(e.selectionPath),i;if(n instanceof J){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof H&&(i=s.text)}r.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(t)}\``),s.join(" ")})}function Am(e,r){let t=e.argumentPath[e.argumentPath.length-1],n=r.arguments.getDeepSubSelectionValue(e.selectionPath);if(n instanceof J){let i=n.getDeepFieldValue(e.argumentPath);i instanceof J&&Ka(i,e.inputType)}r.addErrorMessage(i=>{let o=[`Argument \`${i.bold(t)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${Tn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(Tt(i)),o.join(" ")})}function Im(e,r){let t=e.argumentPath[e.argumentPath.length-1],n=r.arguments.getDeepSubSelectionValue(e.selectionPath),i=[];if(n instanceof J){let o=n.getDeepFieldValue(e.argumentPath);o instanceof J&&(o.markAsError(),i=Object.keys(o.getFields()))}r.addErrorMessage(o=>{let s=[`Argument \`${o.bold(t)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${Tn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Wa(e,r){for(let t of r.fields)e.hasField(t.name)||e.addSuggestion(new Me(t.name,"true"))}function _m(e,r){for(let t of r)e.hasField(t.name)||e.addSuggestion(new Me(t.name,t.typeNames.join(" | ")))}function Ka(e,r){if(r.kind==="object")for(let t of r.fields)e.hasField(t.name)||e.addSuggestion(new Me(t.name,t.typeNames.join(" | ")))}function vn(e){let r=[...e],t=r.pop();if(!t)throw new Error("unexpected empty path");return[r,t]}function Tt({green:e,enabled:r}){return"Available options are "+(r?`listed in ${e("green")}`:"marked with ?")+"."}function Tn(e,r){if(r.length===1)return r[0];let t=[...r],n=t.pop();return`${t.join(", ")} ${e} ${n}`}var km=3;function Dm(e,r){let t=1/0,n;for(let i of r){let o=(0,Ga.default)(e,i);o>km||o<t&&(t=o,n=i)}return n}function Cn({args:e,errors:r,errorFormat:t,callsite:n,originalMethod:i,clientVersion:o}){let s=ln(e);for(let p of r)xn(p,s);let a=t==="pretty"?Ss:sn,l=s.renderAllMessages(a),u=new br(0,{colors:a}).write(s).toString(),c=Ar({message:l,callsite:n,originalMethod:i,showColors:t==="pretty",callArguments:u});throw new K(c,{clientVersion:o})}var Lm={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",update:"updateOne",updateMany:"updateMany",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"};function za({modelName:e,action:r,args:t,runtimeDataModel:n,extensions:i,callsite:o,clientMethod:s,errorFormat:a,clientVersion:l}){let u=new Wi({runtimeDataModel:n,modelName:e,action:r,rootArgs:t,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:l});return{modelName:e,action:Lm[r],query:Ki(t,u)}}function Ki({select:e,include:r,...t}={},n){return{arguments:Za(t,n),selection:Nm(e,r,n)}}function Nm(e,r,t){return e&&r&&t.throwValidationError({kind:"IncludeAndSelect",selectionPath:t.getSelectionPath()}),e?Mm(e,t):Om(t,r)}function Om(e,r){let t={};return e.model&&!e.isRawAction()&&(t.$composites=!0,t.$scalars=!0),r&&Fm(t,r,e),t}function Fm(e,r,t){for(let[n,i]of Object.entries(r)){let o=t.findField(n);o&&o?.kind!=="object"&&t.throwValidationError({kind:"IncludeOnScalar",selectionPath:t.getSelectionPath().concat(n),outputType:t.getOutputTypeDescription()}),i===!0?e[n]=!0:typeof i=="object"&&(e[n]=Ki(i,t.nestSelection(n)))}}function Mm(e,r){let t={},n=r.getComputedFields(),i=la(e,n);for(let[o,s]of Object.entries(i)){let a=r.findField(o);n?.[o]&&!a||(s===!0?t[o]=!0:typeof s=="object"&&(t[o]=Ki(s,r.nestSelection(o))))}return t}function Ya(e,r){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(wr(e)){if(Kt(e))return{$type:"DateTime",value:e.toISOString()};r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:r.getSelectionPath(),argumentPath:r.getArgumentPath(),argument:{name:r.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(Tr(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return $m(e,r);if(ArrayBuffer.isView(e))return{$type:"Bytes",value:Buffer.from(e).toString("base64")};if(qm(e))return e.values;if(vr(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Ne){if(e!==Jt.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(Bm(e))return e.toJSON();if(typeof e=="object")return Za(e,r);r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:r.getSelectionPath(),argumentPath:r.getArgumentPath(),argument:{name:r.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function Za(e,r){if(e.$type)return{$type:"Raw",value:e};let t={};for(let n in e){let i=e[n];i!==void 0&&(t[n]=Ya(i,r.nestArgument(n)))}return t}function $m(e,r){let t=[];for(let n=0;n<e.length;n++){let i=r.nestArgument(String(n)),o=e[n];o===void 0&&r.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${r.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:"Can not use `undefined` value within array. Use `null` or filter out `undefined` values"}),t.push(Ya(o,i))}return t}function qm(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function Bm(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}var Wi=class e{constructor(r){this.params=r;this.params.modelName&&(this.model=this.params.runtimeDataModel.models[this.params.modelName])}throwValidationError(r){Cn({errors:[r],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.model))return{name:this.params.modelName,fields:this.model.fields.map(r=>({name:r.name,typeName:"boolean",isRelation:r.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(r){return this.model?.fields.find(t=>t.name===r)}nestSelection(r){let t=this.findField(r),n=t?.kind==="object"?t.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(r)})}nestArgument(r){return new e({...this.params,argumentPath:this.params.argumentPath.concat(r)})}};var Xa=e=>({command:e});var el=e=>e.strings.reduce((r,t,n)=>`${r}@P${n}${t}`);function Ct(e){try{return rl(e,"fast")}catch{return rl(e,"slow")}}function rl(e,r){return JSON.stringify(e.map(t=>Vm(t,r)))}function Vm(e,r){return typeof e=="bigint"?{prisma__type:"bigint",prisma__value:e.toString()}:wr(e)?{prisma__type:"date",prisma__value:e.toJSON()}:Te.isDecimal(e)?{prisma__type:"decimal",prisma__value:e.toJSON()}:Buffer.isBuffer(e)?{prisma__type:"bytes",prisma__value:e.toString("base64")}:jm(e)||ArrayBuffer.isView(e)?{prisma__type:"bytes",prisma__value:Buffer.from(e).toString("base64")}:typeof e=="object"&&r==="slow"?nl(e):e}function jm(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function nl(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(tl);let r={};for(let t of Object.keys(e))r[t]=tl(e[t]);return r}function tl(e){return typeof e=="bigint"?e.toString():nl(e)}var Um=/^(\s*alter\s)/i,il=N("prisma:client");function zi(e,r,t,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&t.length>0&&Um.exec(r))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var Yi=({clientMethod:e,activeProvider:r})=>t=>{let n="",i;if(Array.isArray(t)){let[o,...s]=t;n=o,i={values:Ct(s||[]),__prismaRawParameters__:!0}}else switch(r){case"sqlite":case"mysql":{n=t.sql,i={values:Ct(t.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=t.text,i={values:Ct(t.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=el(t),i={values:Ct(t.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${r} provider does not support ${e}`)}return i?.values?il(`prisma.${e}(${n}, ${i.values})`):il(`prisma.${e}(${n})`),{query:n,parameters:i}},ol={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[r,...t]=e;return new oe(r,t)}},sl={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};function Zi(e){return function(t){let n,i=(o=e)=>{try{return o===void 0||o?.kind==="itx"?n??(n=al(t(o))):al(t(o))}catch(s){return Promise.reject(s)}};return{then(o,s){return i().then(o,s)},catch(o){return i().catch(o)},finally(o){return i().finally(o)},requestTransaction(o){let s=i(o);return s.requestTransaction?s.requestTransaction(o):s},[Symbol.toStringTag]:"PrismaPromise"}}}function al(e){return typeof e.then=="function"?e:Promise.resolve(e)}var ll={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},async createEngineSpan(){},getActiveContext(){},runInChildSpan(e,r){return r()}},Xi=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(r){return this.getGlobalTracingHelper().getTraceParent(r)}createEngineSpan(r){return this.getGlobalTracingHelper().createEngineSpan(r)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(r,t){return this.getGlobalTracingHelper().runInChildSpan(r,t)}getGlobalTracingHelper(){return globalThis.PRISMA_INSTRUMENTATION?.helper??ll}};function ul(e){return e.includes("tracing")?new Xi:ll}function cl(e,r=()=>{}){let t,n=new Promise(i=>t=i);return{then(i){return--e===0&&t(r()),i?.(n)}}}var Qm=["$connect","$disconnect","$on","$transaction","$use","$extends"],pl=Qm;function ml(e){return typeof e=="string"?e:e.reduce((r,t)=>{let n=typeof t=="string"?t:t.level;return n==="query"?r:r&&(t==="info"||r==="info")?"info":n},void 0)}var Sn=class{constructor(){this._middlewares=[]}use(r){this._middlewares.push(r)}get(r){return this._middlewares[r]}has(r){return!!this._middlewares[r]}length(){return this._middlewares.length}};var fl=_(li());function Rn(e){return typeof e.batchRequestIdx=="number"}function An(e){return e===null?e:Array.isArray(e)?e.map(An):typeof e=="object"?Gm(e)?Jm(e):hr(e,An):e}function Gm(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function Jm({$type:e,value:r}){switch(e){case"BigInt":return BigInt(r);case"Bytes":return Buffer.from(r,"base64");case"DateTime":return new Date(r);case"Decimal":return new Te(r);case"Json":return JSON.parse(r);default:tr(r,"Unknown tagged value")}}function dl(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let r=[];return e.modelName&&r.push(e.modelName),e.query.arguments&&r.push(eo(e.query.arguments)),r.push(eo(e.query.selection)),r.join("")}function eo(e){return`(${Object.keys(e).sort().map(t=>{let n=e[t];return typeof n=="object"&&n!==null?`(${t} ${eo(n)})`:t}).join(" ")})`}var Hm={aggregate:!1,aggregateRaw:!1,createMany:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateOne:!0,upsertOne:!0};function ro(e){return Hm[e]}var In=class{constructor(r){this.options=r;this.tickActive=!1;this.batches={}}request(r){let t=this.options.batchBy(r);return t?(this.batches[t]||(this.batches[t]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[t].push({request:r,resolve:n,reject:i})})):this.options.singleLoader(r)}dispatchBatches(){for(let r in this.batches){let t=this.batches[r];delete this.batches[r],t.length===1?this.options.singleLoader(t[0].request).then(n=>{n instanceof Error?t[0].reject(n):t[0].resolve(n)}).catch(n=>{t[0].reject(n)}):(t.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(t.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<t.length;i++)t[i].reject(n);else for(let i=0;i<t.length;i++){let o=n[i];o instanceof Error?t[i].reject(o):t[i].resolve(o)}}).catch(n=>{for(let i=0;i<t.length;i++)t[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};var Wm=N("prisma:client:request_handler"),_n=class{constructor(r,t){this.logEmitter=t,this.client=r,this.dataloader=new In({batchLoader:na(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(p=>p.protocolQuery),l=this.client._tracingHelper.getTraceParent(s),u=n.some(p=>ro(p.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:l,transaction:Km(o),containsWrite:u,customDataProxyFetch:i})).map((p,m)=>{if(p instanceof Error)return p;try{return this.mapQueryEngineResult(n[m],p)}catch(f){return f}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?gl(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:ro(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:dl(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(r){try{return await this.dataloader.request(r)}catch(t){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=r;this.handleAndLogRequestError({error:t,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a})}}mapQueryEngineResult({dataPath:r,unpacker:t},n){let i=n?.data,o=n?.elapsed,s=this.unpack(i,r,t);return process.env.PRISMA_CLIENT_GET_TIME?{data:s,elapsed:o}:s}handleAndLogRequestError(r){try{this.handleRequestError(r)}catch(t){throw this.logEmitter&&this.logEmitter.emit("error",{message:t.message,target:r.clientMethod,timestamp:new Date}),t}}handleRequestError({error:r,clientMethod:t,callsite:n,transaction:i,args:o,modelName:s}){if(Wm(r),zm(r,i)||r instanceof Le)throw r;if(r instanceof V&&Ym(r)){let l=hl(r.meta);Cn({args:o,errors:[l],callsite:n,errorFormat:this.client._errorFormat,originalMethod:t,clientVersion:this.client._clientVersion})}let a=r.message;if(n&&(a=Ar({callsite:n,originalMethod:t,isPanic:r.isPanic,showColors:this.client._errorFormat==="pretty",message:a})),a=this.sanitizeMessage(a),r.code){let l=s?{modelName:s,...r.meta}:r.meta;throw new V(a,{code:r.code,clientVersion:this.client._clientVersion,meta:l,batchRequestIdx:r.batchRequestIdx})}else{if(r.isPanic)throw new ue(a,this.client._clientVersion);if(r instanceof j)throw new j(a,{clientVersion:this.client._clientVersion,batchRequestIdx:r.batchRequestIdx});if(r instanceof S)throw new S(a,this.client._clientVersion);if(r instanceof ue)throw new ue(a,this.client._clientVersion)}throw r.clientVersion=this.client._clientVersion,r}sanitizeMessage(r){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,fl.default)(r):r}unpack(r,t,n){if(!r||(r.data&&(r=r.data),!r))return r;let i=Object.values(r)[0],o=t.filter(a=>a!=="select"&&a!=="include"),s=An(Ai(i,o));return n?n(s):s}get[Symbol.toStringTag](){return"RequestHandler"}};function Km(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:gl(e)};tr(e,"Unknown transaction kind")}}function gl(e){return{id:e.id,payload:e.payload}}function zm(e,r){return Rn(e)&&r?.kind==="batch"&&e.batchRequestIdx!==r.index}function Ym(e){return e.code==="P2009"||e.code==="P2012"}function hl(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(hl)};if(Array.isArray(e.selectionPath)){let[,...r]=e.selectionPath;return{...e,selectionPath:r}}return e}var yl="5.10.2";var El=yl;function bl(e){return e.map(r=>{let t={};for(let n of Object.keys(r))t[n]=wl(r[n]);return t})}function wl({prisma__type:e,prisma__value:r}){switch(e){case"bigint":return BigInt(r);case"bytes":return Buffer.from(r,"base64");case"decimal":return new Te(r);case"datetime":case"date":return new Date(r);case"time":return new Date(`1970-01-01T${r}Z`);case"array":return r.map(wl);default:return r}}var Tl=_(Hi());var q=class extends Error{constructor(r){super(r+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};w(q,"PrismaClientConstructorValidationError");var xl=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","__internal"],Pl=["pretty","colorless","minimal"],vl=["info","query","warn","error"],Xm={datasources:(e,{datasourceNames:r})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new q(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[t,n]of Object.entries(e)){if(!r.includes(t)){let i=Lr(t,r)||` Available datasources: ${r.join(", ")}`;throw new q(`Unknown datasource ${t} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new q(`Invalid value ${JSON.stringify(e)} for datasource "${t}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new q(`Invalid value ${JSON.stringify(e)} for datasource "${t}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new q(`Invalid value ${JSON.stringify(o)} for datasource "${t}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,r)=>{if(e===null)return;if(e===void 0)throw new q('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!wn(r).includes("driverAdapters"))throw new q('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(Gr()==="binary")throw new q('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new q(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new q(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!Pl.includes(e)){let r=Lr(e,Pl);throw new q(`Invalid errorFormat ${e} provided to PrismaClient constructor.${r}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new q(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function r(t){if(typeof t=="string"&&!vl.includes(t)){let n=Lr(t,vl);throw new q(`Invalid log level "${t}" provided to PrismaClient constructor.${n}`)}}for(let t of e){r(t);let n={level:r,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Lr(i,o);throw new q(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(t&&typeof t=="object")for(let[i,o]of Object.entries(t))if(n[i])n[i](o);else throw new q(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let r=e.maxWait;if(r!=null&&r<=0)throw new q(`Invalid value ${r} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let t=e.timeout;if(t!=null&&t<=0)throw new q(`Invalid value ${t} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},__internal:e=>{if(!e)return;let r=["debug","engine","configOverride"];if(typeof e!="object")throw new q(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[t]of Object.entries(e))if(!r.includes(t)){let n=Lr(t,r);throw new q(`Invalid property ${JSON.stringify(t)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Cl(e,r){for(let[t,n]of Object.entries(e)){if(!xl.includes(t)){let i=Lr(t,xl);throw new q(`Unknown property ${t} provided to PrismaClient constructor.${i}`)}Xm[t](n,r)}if(e.datasourceUrl&&e.datasources)throw new q('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Lr(e,r){if(r.length===0||typeof e!="string")return"";let t=ed(e,r);return t?` Did you mean "${t}"?`:""}function ed(e,r){if(r.length===0)return null;let t=r.map(i=>({value:i,distance:(0,Tl.default)(e,i)}));t.sort((i,o)=>i.distance<o.distance?-1:1);let n=t[0];return n.distance<3?n.value:null}function Sl(e){return e.length===0?Promise.resolve([]):new Promise((r,t)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?t(i):r(n)))},l=u=>{o||(o=!0,t(u))};for(let u=0;u<e.length;u++)e[u].then(c=>{n[u]=c,a()},c=>{if(!Rn(c)){l(c);return}c.batchRequestIdx===u?l(c):(i||(i=c),a())})})}var Ye=N("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var rd={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},td=Symbol.for("prisma.client.transaction.id"),nd={id:0,nextId(){return++this.id}};function Dl(e){class r{constructor(n){this._originalClient=this;this._middlewares=new Sn;this._createPrismaPromise=Zi();this.$extends=Ks;e=n?.__internal?.configOverride?.(e)??e,pa(e),n&&Cl(n,e);let i=n?.adapter?hi(n.adapter):void 0,o=new _l.EventEmitter().on("error",()=>{});this._extensions=fn.empty(),this._previewFeatures=wn(e),this._clientVersion=e.clientVersion??El,this._activeProvider=e.activeProvider,this._tracingHelper=ul(this._previewFeatures);let s={rootEnvPath:e.relativeEnvPaths.rootEnvPath&&St.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&St.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},a=!i&&Qr(s,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{let l=n??{},u=l.__internal??{},c=u.debug===!0;c&&N.enable("prisma:client");let p=St.default.resolve(e.dirname,e.relativePath);kl.default.existsSync(p)||(p=e.dirname),Ye("dirname",e.dirname),Ye("relativePath",e.relativePath),Ye("cwd",p);let m=u.engine||{};if(l.errorFormat?this._errorFormat=l.errorFormat:process.env.NODE_ENV==="production"?this._errorFormat="minimal":process.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:p,dirname:e.dirname,enableDebugLogs:c,allowTriggerPanic:m.allowTriggerPanic,datamodelPath:St.default.join(e.dirname,e.filename??"schema.prisma"),prismaPath:m.binaryPath??void 0,engineEndpoint:m.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:l.log&&ml(l.log),logQueries:l.log&&!!(typeof l.log=="string"?l.log==="query":l.log.find(f=>typeof f=="string"?f==="query":f.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:ma(l,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:l.transactionOptions?.maxWait??2e3,timeout:l.transactionOptions?.timeout??5e3,isolationLevel:l.transactionOptions?.isolationLevel},logEmitter:o,isBundled:e.isBundled,adapter:i},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Ir,getBatchRequestPayload:Er,prismaGraphQLToJSError:sr,PrismaClientUnknownRequestError:j,PrismaClientInitializationError:S,PrismaClientKnownRequestError:V,debug:N("prisma:client:accelerateEngine"),engineVersion:Al.version,clientVersion:e.clientVersion}},Ye("clientVersion",e.clientVersion),this._engine=qa(e,this._engineConfig),this._requestHandler=new _n(this,o),l.log)for(let f of l.log){let g=typeof f=="string"?f:f.emit==="stdout"?f.level:null;g&&this.$on(g,h=>{Wr.log(`${Wr.tags[g]??""}`,h.message||h.query)})}this._metrics=new yr(this._engine)}catch(l){throw l.clientVersion=this._clientVersion,l}return this._appliedParent=at(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i)}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{go()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:Yi({clientMethod:i,activeProvider:a}),callsite:We(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=Rl(n,i);return zi(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new K("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(zi(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new K(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:Xa,callsite:We(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:Yi({clientMethod:i,activeProvider:a}),callsite:We(this._errorFormat),dataPath:[],middlewareArgsMapper:s}).then(bl)}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...Rl(n,i));throw new K("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=nd.nextId(),s=cl(n.length),a=n.map((l,u)=>{if(l?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let c=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,p={kind:"batch",id:o,index:u,isolationLevel:c,lock:s};return l.requestTransaction?.(p)??l});return Sl(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),l;try{let u={kind:"itx",...a};l=await n(this._createItxClient(u)),await this._engine.transaction("commit",o,a)}catch(u){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),u}return l}_createItxClient(n){return at(Pe(Ws(this),[te("_appliedParent",()=>this._appliedParent._createItxClient(n)),te("_createPrismaPromise",()=>Zi(n)),te(td,()=>n.id),nt(pl)]))}$transaction(n,i){let o;typeof n=="function"?o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??rd,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,l=async u=>{let c=this._middlewares.get(++a);if(c)return this._tracingHelper.runInChildSpan(s.middleware,A=>c(u,T=>(A?.end(),l(T))));let{runInTransaction:p,args:m,...f}=u,g={...n,...f};m&&(g.args=i.middlewareArgsToRequestArgs(m)),n.transaction!==void 0&&p===!1&&delete g.transaction;let h=await ta(this,g);return g.model?Zs({result:h,modelName:g.model,args:g.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel}):h};return this._tracingHelper.runInChildSpan(s.operation,()=>new Il.AsyncResource("prisma-client-request").runInAsyncScope(()=>l(o)))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:l,argsMapper:u,transaction:c,unpacker:p,otelParentCtx:m,customDataProxyFetch:f}){try{n=u?u(n):n;let g={name:"serialize"},h=this._tracingHelper.runInChildSpan(g,()=>za({modelName:l,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion}));return N.enabled("prisma:client")&&(Ye("Prisma Client call:"),Ye(`prisma.${i}(${_s(n)})`),Ye("Generated request:"),Ye(JSON.stringify(h,null,2)+`
`)),c?.kind==="batch"&&await c.lock,this._requestHandler.request({protocolQuery:h,modelName:l,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:c,unpacker:p,otelParentCtx:m,otelChildCtx:this._tracingHelper.getActiveContext(),customDataProxyFetch:f})}catch(g){throw g.clientVersion=this._clientVersion,g}}get $metrics(){if(!this._hasPreviewFlag("metrics"))throw new K("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:this._clientVersion});return this._metrics}_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}}return r}function Rl(e,r){return id(e)?[new oe(e,r),ol]:[e,sl]}function id(e){return Array.isArray(e)&&Array.isArray(e.raw)}var od=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Ll(e){return new Proxy(e,{get(r,t){if(t in r)return r[t];if(!od.has(t))throw new TypeError(`Invalid enum value: ${String(t)}`)}})}function Nl(e){Qr(e,{conflictCheck:"warn"})}0&&(module.exports={Debug,Decimal,Extensions,MetricsClient,NotFoundError,PrismaClientInitializationError,PrismaClientKnownRequestError,PrismaClientRustPanicError,PrismaClientUnknownRequestError,PrismaClientValidationError,Public,Sql,defineDmmfProperty,detectRuntime,empty,getPrismaClient,join,makeStrictEnum,objectEnumValues,raw,sqltag,warnEnvConflicts,warnOnce});
/*! Bundled license information:

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.4.3
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2022 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/
//# sourceMappingURL=library.js.map
